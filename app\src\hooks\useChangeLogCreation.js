import { getCurrentDate, getPreviousValueGlobal } from "@helper/helper";
import { useDispatch, useSelector } from "react-redux";
import { setCreateChangeLogDataBK, setCreateChangeLogDataIO } from "@app/changeLogReducer";
import { MODULE_MAP } from "@constant/enum";

export const useChangeLogCreation = () => {
    const dispatch = useDispatch();
    const createPayloadCopyForChangeLog = useSelector((state) => state.changeLog.createPayloadCopyForChangeLog || {});
    const changeLogBK = useSelector((state) => state.changeLog.createChangeLogDataBK || {});
    const changeLogIO = useSelector((state) => state.changeLog.createChangeLogDataIO || {});
    const userData = useSelector((state) => state.userManagement.userData);

    const updateChangeLogGlobal = ({
        uniqueId,
        viewName,
        fieldName,
        jsonName,
        currentValue,
        module=MODULE_MAP?.BK
    }) => {
        const CURRENT_DATE = getCurrentDate().humanReadable;
        const previousValue = getPreviousValueGlobal(uniqueId, jsonName, createPayloadCopyForChangeLog,module);

        const ObjectNo = module === MODULE_MAP?.BK ?
            createPayloadCopyForChangeLog?.[uniqueId]?.BankNo :
            module === MODULE_MAP?.IO ?
            createPayloadCopyForChangeLog?.[uniqueId]?.internalOrder || uniqueId :
            uniqueId;

        const newChangeEntry = {
            ObjectNo,
            ChangedBy: userData?.emailId,
            ChangedOn: CURRENT_DATE,
            FieldName: fieldName,
            PreviousValue: previousValue,
            CurrentValue: currentValue,
            SAPValue: previousValue,
            ViewName: viewName,
            JsonName:jsonName
        };

        // Get existing logs based on module
        const existingLogs = module === MODULE_MAP?.BK ?
            changeLogBK?.[uniqueId]?.changeLog || [] :
            module === MODULE_MAP?.IO ?
            changeLogIO?.[uniqueId]?.changeLog || [] :
            [];

        const existingIndex = existingLogs.findIndex(
            (entry) => entry.ObjectNo === ObjectNo && entry.JsonName === jsonName
        );

        let updatedLogs;

        if (existingIndex !== -1) {
            updatedLogs = [...existingLogs];
            updatedLogs[existingIndex] = {
                ...updatedLogs[existingIndex],
                CurrentValue: currentValue,
                ChangedOn: CURRENT_DATE,
            };
        } else {
            updatedLogs = [...existingLogs, newChangeEntry];
        }

        // Dispatch to appropriate reducer based on module
        if (module === MODULE_MAP?.BK) {
            dispatch(setCreateChangeLogDataBK({
                uniqueId,
                changeLog: updatedLogs
            }));
        } else if (module === MODULE_MAP?.IO) {
            dispatch(setCreateChangeLogDataIO({
                uniqueId,
                changeLog: updatedLogs
            }));
        }
    };

    return { updateChangeLogGlobal };
};
