import{b9 as ce,n as $,a as de,g as ie,u as le,s as ge,aP as pe,r as u,bc as ue,C as l,c as b,j as n,Q as ye,O as y,a6 as me,a_ as he,a$ as be,d as A,Z as C,aZ as v,B as R,b5 as Ce,b6 as fe,ag as xe,ae as Se,aO as Ae,bf as Le,cd as g,aG as ke,aK as we,de as Ne,df as p}from"./index-226a1e75.js";import{d as Te,a as Ge,b as $e}from"./Category-83dc6e58.js";import{d as ve}from"./Description-d98685cc.js";import"./AutoCompleteType-63e88d3d.js";import{u as Ee}from"./useMaterialFieldConfig-a3bf7965.js";import"./SingleSelectDropdown-ee61a6b7.js";import{u as De}from"./useGeneralLedgerFieldConfig-2898dc29.js";import{G as Re}from"./GenericTabsGlobal-6faba7da.js";import"./useChangeLogUpdate-23c3e0f8.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./FilterFieldGlobal-b5a561ef.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";const w=({label:N,value:f,labelWidth:E="25%",centerWidth:D="5%",icon:L})=>b(v,{flexDirection:"row",alignItems:"center",children:[L&&n("div",{style:{marginRight:"10px"},children:L}),n(A,{variant:"body2",color:C.secondary.grey,style:{width:E},children:N}),n(A,{variant:"body2",fontWeight:"bold",sx:{width:D,textAlign:"center"},children:":"}),n(A,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:f||""})]}),to=()=>{Ee();const N=ce(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}}));$(e=>e.payload);const f=$(e=>(e.generalLedger.generalLedgerTabs||[]).filter(s=>s.tab!=="Initial Screen")),{loading:E,error:D,fetchGeneralLedgerFieldConfig:L}=De(),{t:S}=de(),O=ie();N();const _=le(),i=ge(),r=_.state,{customError:m}=pe();u.useState([]),u.useState({});const[B,Oe]=u.useState(!1),[I,_e]=u.useState("");u.useState(0),u.useState(null);const[k,F]=u.useState(0),[M,Be]=u.useState(null),[P,V]=u.useState(""),[Ie,H]=u.useState(!1);let T=$(e=>{var o;return((o=e.generalLedgerDropDownData)==null?void 0:o.dropDown)||{}});u.useEffect(()=>{ue(Le.MODULE,Ae.GL),oe(),L()},[]);const W=e=>{const o=t=>{i(p({keyName:"Accounttype",data:t.body||[],keyName2:e}))},s=t=>{};l(`/${g}/data/getGLAccountType`,"get",o,s)},z=(e,o)=>{const s=a=>{var G;let d=[];(G=a==null?void 0:a.body)==null||G.map(c=>{let h={};h.code=c==null?void 0:c.AccountGroup,h.desc=c==null?void 0:c.Description,d==null||d.push(h)}),i(p({keyName:"AccountGroup",data:d||[],keyName2:o}))},t=a=>{};l(`/${g}/data/getAccountGroup?chartAccount=${e}`,"get",s,t)},K=e=>{const o=t=>{i(p({keyName:"Language",data:t.body||[],keyName2:e}))},s=t=>{};l(`/${g}/data/getLanguageKey`,"get",o,s)},q=(e="")=>{const o=t=>{i(p({keyName:"Sortkey",data:t.body||[],keyName2:e}))},s=t=>{m(t)};l(`/${g}/data/getSortKey`,"get",o,s)},j=(e,o="")=>{const s=a=>{i(p({keyName:"AccountCurrency",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getAccountCurrency?companyCode=${e}`,"get",s,t)},U=(e,o)=>{const s=a=>{i(p({keyName:"FieldStsGrp",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getFieldStatusGroup?fieldStatusVariant=${e}`,"get",s,t)},Y=(e,o="")=>{const s=a=>{i(p({keyName:"Taxcategory",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getTaxCategory?companyCode=${e}`,"get",s,t)},Z=(e,o="")=>{const s=a=>{i(p({keyName:"HouseBank",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getHouseBank?companyCode=${e}`,"get",s,t)},Q=(e,o)=>{const s=a=>{i(p({keyName:"AccountId",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getAccountId?companyCode=${e}`,"get",s,t)},X=(e,o="")=>{const s=a=>{i(p({keyName:"CostEleCategory",data:a.body||[],keyName2:o}))},t=a=>{m(a)};l(`/${g}/data/getCostElementCategory?accountType=${e}`,"get",s,t)},J=(e="")=>{const o=t=>{i(p({keyName:"ReconAcc",data:t.body||[],keyName2:e}))},s=t=>{m(t)};l(`/${g}/data/getReconAccountForAccountType`,"get",o,s)},ee=(e="")=>{const o=t=>{i(p({keyName:"Planninglevel",data:t.body||[],keyName2:e}))},s=t=>{m(t)};l(`/${g}/data/getPlanningLevel`,"get",o,s)},oe=()=>{const e=[];e.push({glAccount:r==null?void 0:r.glAccount,chartOfAccount:r==null?void 0:r.chartOfAccount,companyCode:r==null?void 0:r.companyCode});const o={glAccCOACoCode:e},s=async a=>{const d=we(),c=((a==null?void 0:a.body)||[]).reduce((re,x)=>({...re,...x.typeNDescriptionViewDto,...x.controlDataViewDto,...x.createBankInterestViewDto,...x.keywordNTranslationViewDto,...x.informationViewDto,COA:x.COA,CompanyCode:x.CompanyCode}),{}),h=[];c.COA&&h.push(W(d),z(c.COA,d),K(d),q(d)),c.CompanyCode&&h.push(j(c.CompanyCode,d),Y(c.CompanyCode,d),Z(c.CompanyCode,d),Q(c.CompanyCode,d),J(c.CompanyCode),ee(c.CompanyCode),U(c.CompanyCode,d)),c.Accounttype&&h.push(X(c.Accounttype,d));const ae=await Promise.all(h),ne=Object.assign({},...ae),se={[d]:{...c,...ne}};i(Ne({requestHeaderData:{},rowsHeaderData:{},rowsBodyData:se})),setTimeout(()=>{H(!0)},1e3),V(d)},t=a=>{};l(`/${g}/data/getGeneralLedgersData`,"post",s,t,o)},te=(e,o)=>{F(o)};return b("div",{style:{backgroundColor:"#FAFCFF"},children:[n(y,{container:!0,sx:ye,children:n(y,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:b(y,{md:9,sx:{display:"flex"},children:[n(me,{color:"primary",sx:he,onClick:()=>O(-1),children:n(be,{sx:{fontSize:"25px",color:"#000000"}})}),b(y,{item:!0,md:12,children:[n(A,{variant:"h3",children:n("strong",{children:S("Display General Ledger")})}),n(A,{variant:"body2",color:"#777",children:S("This view displays the details of the General Ledgers")})]})]})})}),b(y,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:C.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[b(v,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[n(y,{item:!0,children:n(w,{label:S("GeneralLedger"),value:(r==null?void 0:r.glAccount)||"",labelWidth:"35%",icon:n(Te,{sx:{color:C.blue.indigo,fontSize:"20px"}})})}),n(y,{item:!0,children:n(w,{label:S("Chat Of Account"),value:(r==null?void 0:r.chartOfAccount)||"",labelWidth:"35%",icon:n(Ge,{sx:{color:C.blue.indigo,fontSize:"20px"}})})})]}),b(v,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[n(y,{item:!0,children:n(w,{label:S("Company Code"),value:(r==null?void 0:r.companyCode)||"",labelWidth:"35%",icon:n($e,{sx:{color:C.blue.indigo,fontSize:"20px"}})})}),n(y,{item:!0,children:n(w,{label:S("General Ledger Description"),value:(r==null?void 0:r.glAcctLongText)||"",labelWidth:"35%",icon:n(ve,{sx:{color:C.blue.indigo,fontSize:"20px"}})})})]})]}),n(y,{children:f?b(R,{sx:{mt:3},children:[n(Ce,{value:k,onChange:te,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:f.map((e,o)=>n(fe,{label:e.tab},o))}),n(xe,{elevation:2,sx:{p:3,borderRadius:4},children:f[k]&&(T!=null&&T.Accounttype)?n(Re,{disabled:!1,basicDataTabDetails:f[k].data,dropDownData:"",activeViewTab:f[k].tab,uniqueId:P,selectedRow:M||{},module:"GeneralLedger"}):""})]}):n(R,{sx:{marginTop:"30px",border:`1px solid ${C.secondary.grey}`,padding:"16px",background:`${C.primary.white}`,textAlign:"center"},children:n("span",{children:Se.NO_DATA_AVAILABLE})})}),n(ke,{blurLoading:B,loaderMessage:I})]})};export{to as default};
