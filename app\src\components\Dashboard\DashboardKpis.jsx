import { Grid, IconButton } from '@mui/material'
import localConfigServer from "../../data/localConfigServer.json";
import dashboardConfigBupa from "./DashboardConfigBuPA";
import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { KPICard } from '@cw/rds';
import moment from 'moment';
import { doAjax } from "../Common/fetchService";
import { destination_Dashboard } from '../../destinationVariables';
import colorLib from '@kurkle/color';
import { END_POINTS } from '../../constant/apiEndPoints';
import useLang from '../../hooks/useLang';
import { arrayToCommaString } from '@helper/helper';
import { MODULE_OPTIONS } from '@constant/enum';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from "react-redux";
import { commonFilterUpdate } from '@app/commonFilterSlice';

const DashboardKpis = () => {
  const dashboardConfigVersion =
    localConfigServer?.system === "CHWSCP"
      ? dashboardConfigBupa.BUPA
      : dashboardConfigBupa.BUPA;
  const TileLoaderNames = dashboardConfigVersion.Tiles.reduce((acc, tile) => {
    acc[tile.count] = true;
    return acc;
  }, {});
  const { t } = useLang();
  const TileNames = dashboardConfigVersion.Tiles.reduce((acc, tile) => {
    acc[tile.count] = 0;
    return acc;
  }, {});
  const dashboardSearchForm = useSelector(
    (state) => state?.commonFilter["Dashboard"]
  );
  const rbSearchForm = useSelector(
    (state) => state.commonFilter["RequestBench"]
  );
  const [TileCounterLoader, setTileCounterLoader] = useState(TileLoaderNames);
  const [TileCounter, setTileCounter] = useState(TileNames);
  const [KpiCards, setKpiCards] = useState([]);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const transparentize = (value, opacity) => {
    var alpha = opacity === undefined ? 0.5 : 1 - opacity;
    return colorLib(value).alpha(alpha).rgbString();
  }
  const handleNavigation = (type) => {
    let tempFilterData = {
          ...rbSearchForm,
          ["requestType"]: type,
        };
        
        dispatch(
          commonFilterUpdate({
            module: "RequestBench",
            filterData: tempFilterData,
          })
        );
    navigate("/requestBench");
  };
  useEffect(() => {
    let onlyTilePayload = new FormData();
      onlyTilePayload.append(
        "fromDate",
        moment(
          dashboardSearchForm?.dashboardDate[0]
            ? dashboardSearchForm?.dashboardDate[0]
            : rbSearchForm?.createdOn[0]
        ).format("YYYY-MM-DD") + " 00:00:00"
      );
      onlyTilePayload.append(
        "toDate",
        moment(
          dashboardSearchForm?.dashboardDate[1]
            ? dashboardSearchForm?.dashboardDate[1]
            : rbSearchForm?.createdOn[1]
        ).format("YYYY-MM-DD") + " 23:59:59"
      );
      onlyTilePayload.append("module", arrayToCommaString(dashboardSearchForm?.dashBoardModuleName) || MODULE_OPTIONS[0]);
      onlyTilePayload.append("userId", "");
      const hSuccessTiles = (data) => {
        setKpiCards(data.body)
      };
      const hErrorTiles = () => {};
        doAjax(
          `/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.KPI_CARDS}`,
          "postformdata",
          hSuccessTiles,
          hErrorTiles,
          onlyTilePayload
        );
      
  },[dashboardSearchForm])
  const KPI_CARD_COLOR = {
    'Create': transparentize('#4dc9f6', 0.7),
    'Change': transparentize('#f6d55c', 0.7),
    'Extend': transparentize('#537bc4', 0.7),
    'Create With Upload': transparentize('#00a950', 0.7),
    'Change With Upload': transparentize('#8549ba', 0.7),
    'Extend With Upload': transparentize('#ff6384', 0.7),
  };
  return (
    <Grid container spacing={2} sx={{ mt: 2 }} className="kpiCard">
      {KpiCards?.map((tile) => {
        return (
          <>
            {/* {TileCounterLoader[tile?.count] ? ( */}
            {/* // <POtileSkeleton header={tile?.name} key={tile?.count} /> */}
            <Grid item xs={12} sm={6} md={4} lg={2}>
              <Grid
                onClick={() => handleNavigation(tile.status)} // your navigation function 
                // sx={{ padding: 0, display: "flex"}} // remove extra padding
              >
                <KPICard
                  events={{
                    allow: true,
                    type: 'option',
                    // options: navButton
                  }}
                  value={tile?.statusCount}
                  graphName={t(tile?.status)}
                  KPIColor={KPI_CARD_COLOR[tile.status]}
                />
              </Grid>
            </Grid>
            {/* ):(
              <DashboardCard
                header={tile?.name}
                date={dashboardSearchForm?.dashboardDate}
                count={TileCounter[tile?.count]}
                icon={(tile?.type && true) || false}
                status={tile?.status && tile?.status}
                type={tile?.type}
                additionalStatus={tile?.additionalStatus}
                isPercentage={tile?.isPercentage && tile?.isPercentage}
                color={tile.color}
                borderColor={tile?.borderColor}
              />
            )} */}

          </>
        );
      })}
    </Grid>
  )
}

export default DashboardKpis
