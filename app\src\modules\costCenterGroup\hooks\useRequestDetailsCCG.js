import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";

import { createPayloadForCCG } from "../../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import {
  FAILURE_DIALOG_MESSAGE,
  MODULE,
  MODULE_MAP,
  REQUEST_TYPE,
  SUCCESS_DIALOG_MESSAGE,
} from "@constant/enum";
import { doAjax, promiseAjax } from "@components/Common/fetchService";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import useChangeListPayloadHierarchy from "@modules/modulesHooks/hierarchyHooks/useChangeListPayloadHierarchy";
import { destination_CostCenter } from "../../../destinationVariables";
import {
  setDeleteNodeList,
  setDescList,
  setDescListForDBDuplicate<PERSON><PERSON><PERSON>,
  setE<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  setNode<PERSON>ist,
  setNodesListForDBDuplica<PERSON><PERSON><PERSON><PERSON>,
  setReplaceNodesList,
  setReplaceTagList,
  setTagList,
  setTagListForDBDuplicateCheck,
} from "@app/hierarchyDataSlice";
import useDynamicWorkflowDTHierarchy from "@modules/modulesHooks/hierarchyHooks/useDynamicWorkflowDTHierarchy";

export const useRequestDetailsCCG = ({
  setSuccessDialogOpen,
  setDialogData,
  selectedLevel,
} = {}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const { getDynamicWorkflowDT } = useDynamicWorkflowDTHierarchy();
  // URL params
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const RequestId = queryParams.get("RequestId");

  // Local state
  const [showTree, setShowTree] = useState(false);
  const [buttonsLoading, setButtonsLoading] = useState(false);
  const [openButtonSnackBar, setOpenButtonSnackBar] = useState(false);
  const [alertButtonMsg, setButtonAlertMsg] = useState("");
  const [alertButtonType, setAlertButtonType] = useState("success");
  const [wfLevels, setWfLevels] = useState([]);
  // Redux selectors
  const taskData = useSelector((state) => state.userManagement.taskData);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const hierarchyRequestHeaderData = useSelector(
    (state) => state.hierarchyData.requestHeaderData
  );
  const initialNodeData = useSelector((state) => state.hierarchyData.treeData);
  const requestorPayload = useSelector(
    (state) => state.payload.requestorPayload
  );
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const reduxPayload = useSelector((state) => state.hierarchyData);
  const treeChanges = useSelector((state) => state.hierarchyData.TreeChanges);
  // Your existing state and selectors...
  const changeWithUploadRecords = useSelector(
    (state) => state.hierarchyData.DisplayRecords
  );

  // Custom hooks
  const { getButtonsDisplayGlobal, showWfLevels } = useButtonDTConfig();
  const { preparePayload } = useChangeListPayloadHierarchy();

  // Effects
  useEffect(() => {
    if (taskData?.ATTRIBUTE_1 || RequestType) {
      getButtonsDisplayGlobal(
        "Hierarchy Node (Cost Center)",
        "MDG_DYN_BTN_DT",
        "v3"
      );
    }
  }, [taskData]);

  useEffect(() => {
    const fetchWorkflowLevels = async () => {
      try {
        let conditions = {
          "MDG_CONDITIONS.MDG_CCG_REQUEST_TYPE": RequestType || "",
          "MDG_CONDITIONS.MDG_HIERARCHY_REGION":
            reduxPayload?.GeneralInformation?.["Hierarchy Region"] || "",
        };
        const workflowLevelsDtData = await getDynamicWorkflowDT(
          taskData?.ATTRIBUTE_3,
          "v4",
          "MDG_DYNAMIC_WF_CCG_DT",
          MODULE_MAP?.CCG,
          conditions
        );
        setWfLevels(workflowLevelsDtData);
      } catch (err) {
        // customError(err);
      }
    };
    if (
      RequestType &&
      reduxPayload?.GeneralInformation?.["Hierarchy Region"] &&
      taskData?.ATTRIBUTE_3
    ) {
      fetchWorkflowLevels();
    }
  }, [RequestType, reduxPayload?.GeneralInformation?.["Hierarchy Region"], taskData?.ATTRIBUTE_3]);

  useEffect(() => {
    if (initialNodeData?.length !== 0) {
      setShowTree(true);
    }
  }, [initialNodeData]);

  // API functions
  const fetchOldDescriptionForNode = async (nodeValue) => {
    try {
      const payload = {
        controllingArea: reduxPayload?.ControllingArea ?? "",
        nodes: [nodeValue?.toUpperCase()],
      };
      const response = await promiseAjax(
        `/${destination_CostCenter}/node/fetchDescriptionForNode`,
        "post",
        payload
      );
      const oldDescData = await response.json();
      return oldDescData?.body?.[0]?.Description || "";
    } catch (error) {
      console.error("Error fetching old description:", error);
      return "";
    }
  };

  const fetchOldParentForNode = async (nodeValue) => {
    try {
      const payload = {
        controllingArea: reduxPayload?.ControllingArea ?? "",
        nodes: [nodeValue?.toUpperCase()],
      };
      const response = await promiseAjax(
        `/${destination_CostCenter}/node/fetchParentNodeForSubNode`,
        "post",
        payload
      );
      const oldParentData = await response.json();
      return oldParentData?.body?.[0]?.ParentNode || "";
    } catch (error) {
      console.error("Error fetching old parent node:", error);
      return "";
    }
  };

  const fetchOldParentForObject = async (costCenter) => {
    try {
      const payload = {
        controllingArea: reduxPayload?.ControllingArea ?? "",
        hierarchy: reduxPayload?.ParentNode ?? "",
        ccList: [costCenter?.toUpperCase()],
      };
      const response = await promiseAjax(
        `/${destination_CostCenter}/node/fetchParentNodeForObject`,
        "post",
        payload
      );
      const oldObject = await response.json();
      return oldObject?.body?.[0]?.Node || "";
    } catch (error) {
      console.error("Error fetching old parent for cost center:", error);
      return "";
    }
  };

  const createAndPopulateLists = () => {
    // Initialize fresh arrays
    const nodeList = [];
    const replaceNodesList = [];
    const tagList = [];
    const replaceTagList = [];
    const descList = [];
    const editDescList = [];
    const deleteNodeList = [];
    // New lists for DB duplicate checks
    const nodesListForDBDuplicateCheck = [];
    const descListForDBDuplicateCheck = [];
    const tagListForDBDuplicateCheck = [];

    // Get data from different tables
    const newNodesData = changeWithUploadRecords?.["NEW NODES"] || [];
    const moveNodeData = changeWithUploadRecords?.["MOVE NODE"] || [];
    const costCentersData = changeWithUploadRecords?.["COST CENTERS"] || [];
    const moveCostCenterData =
      changeWithUploadRecords?.["MOVE COST CENTER"] || [];
    const descriptionsData = changeWithUploadRecords?.["DESCRIPTIONS"] || [];
    const deleteNodeData = changeWithUploadRecords?.["DELETE NODE"] || [];

    // 1. NodeList: ParentNode$NewNode from NEW NODES and MOVE NODE tables
    newNodesData.forEach((row) => {
      if (row["Parent Node"] && row["New Node"]) {
        nodeList.push(`${row["Parent Node"]}$$${row["New Node"]}`);
        nodesListForDBDuplicateCheck.push(`${row["New Node"]}`);
      }
    });

    moveNodeData.forEach((row) => {
      if (row["New Parent Node"] && row["Selected Node"]) {
        nodeList.push(`${row["New Parent Node"]}$$${row["Selected Node"]}`);
      }
    });

    // 2. ReplaceNodesList: ParentNode$OldParentNode from MOVE NODE table
    moveNodeData.forEach((row) => {
      if (row["Selected Node"] && row["Old Parent Node"]) {
        replaceNodesList.push(
          `${row["Selected Node"]}$$${row["Old Parent Node"]}`
        );
      }
    });

    // 3. TagList: ParentNode$CostCenter from COST CENTERS and NewParentNode$CostCenter from MOVE COST CENTER
    costCentersData.forEach((row) => {
      if (row["Node"] && row["Cost Center"]) {
        tagList.push(`${row["Node"]}$$${row["Cost Center"]}`);
        tagListForDBDuplicateCheck.push(`${row["Cost Center"]}`);
      }
    });

    moveCostCenterData.forEach((row) => {
      if (row["New Parent Node"] && row["Selected Cost Center"]) {
        tagList.push(
          `${row["New Parent Node"]}$$${row["Selected Cost Center"]}`
        );
      }
    });

    // 4. ReplaceTagList: OldParentNode$CostCenter from MOVE COST CENTER table
    moveCostCenterData.forEach((row) => {
      if (row["Old Parent Node"] && row["Selected Cost Center"]) {
        replaceTagList.push(
          `${row["Old Parent Node"]}$$${row["Selected Cost Center"]}`
        );
      }
    });

    // 5. DescList: ParentNode$Description from NEW NODES table
    newNodesData.forEach((row) => {
      if (row["New Node"] && row["Description"]) {
        descList.push(`${row["New Node"]}$~$${row["Description"]}`);
        descListForDBDuplicateCheck.push(`${row["Description"]}`);
      }
    });

    // 6. EditDescList: ParentNode$NewDescription from DESCRIPTIONS table
    descriptionsData.forEach((row) => {
      if (row["Parent Node"] && row["New Description"]) {
        editDescList.push(`${row["Parent Node"]}$~$${row["New Description"]}`);
        descListForDBDuplicateCheck.push(`${row["New Description"]}`);
      }
    });

    // 7. DeleteNodeList: ParentNode$DeletedNode from DELETE NODE table
    deleteNodeData.forEach((row) => {
      if (row["Parent Node"] && row["Deleted Node"]) {
        deleteNodeList.push(`${row["Parent Node"]}$$${row["Deleted Node"]}`);
      }
    });

    // Return the lists object
    const result = {
      NodeList: nodeList,
      ReplaceNodesList: replaceNodesList,
      TagList: tagList,
      ReplaceTagList: replaceTagList,
      DescList: descList,
      EditDescList: editDescList,
      DeleteNodeList: deleteNodeList,
      nodesListForDBDuplicateCheck: nodesListForDBDuplicateCheck,
      descListForDBDuplicateCheck: descListForDBDuplicateCheck,
      tagListForDBDuplicateCheck: tagListForDBDuplicateCheck,
      success: true,
    };

    // Optionally dispatch to Redux if you need them in state
    dispatch(setNodeList(nodeList));
    dispatch(setReplaceNodesList(replaceNodesList));
    dispatch(setTagList(tagList));
    dispatch(setReplaceTagList(replaceTagList));
    dispatch(setDescList(descList));
    dispatch(setEditDescList(editDescList));
    dispatch(setDeleteNodeList(deleteNodeList));
    dispatch(setNodesListForDBDuplicateCheck(nodesListForDBDuplicateCheck));
    dispatch(setDescListForDBDuplicateCheck(descListForDBDuplicateCheck));
    dispatch(setTagListForDBDuplicateCheck(tagListForDBDuplicateCheck));

    console.log("Lists populated:", result);

    return result;
  };

  // Event handlers
  const handleButtonClick = async (type, remarks, userInput) => {
    try {
      let apiEndpoint =
        END_POINTS?.MASTER_BUTTON_APIS?.[MODULE?.CCG]?.[
          hierarchyRequestHeaderData?.RequestType
        ]?.[type];
      let finalPayload = {};
      if (RequestType != REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        const result = preparePayload(treeChanges);
        setButtonsLoading(true);

        if (!result?.success) {
          return;
        }

        finalPayload = createPayloadForCCG(
          {
            ...reduxPayload,
            NodesList: result.NodeList,
            ReplaceNodesList: result.ReplaceNodesList,
            TagList: result.TagList,
            ReplaceTagList: result.ReplaceTagList,
            DescList: result.DescList,
            EditDescList: result.EditDescList,
            DeleteNodeList: result.DeleteNodeList,
            nodesListForDBDuplicateCheck: result.nodesListForDBDuplicateCheck,
            descListForDBDuplicateCheck: result.descListForDBDuplicateCheck,
            tagListForDBDuplicateCheck: result.tagListForDBDuplicateCheck,
          },
          requestHeaderSlice,
          RequestId,
          taskData,
          dynamicData,
          requestorPayload,
          type,
          remarks,
          userInput,
          selectedLevel
        );
      }
      if (RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        const result = createAndPopulateLists();
        setButtonsLoading(true);

        if (!result?.success) {
          setButtonsLoading(false);
          return;
        }

        // Create the final payload with the populated lists
        finalPayload = createPayloadForCCG(
          {
            ...reduxPayload,
            NodesList: result.NodeList,
            ReplaceNodesList: result.ReplaceNodesList,
            TagList: result.TagList,
            ReplaceTagList: result.ReplaceTagList,
            DescList: result.DescList,
            EditDescList: result.EditDescList,
            DeleteNodeList: result.DeleteNodeList,
            nodesListForDBDuplicateCheck: result.nodesListForDBDuplicateCheck,
            descListForDBDuplicateCheck: result.descListForDBDuplicateCheck,
            tagListForDBDuplicateCheck: result.tagListForDBDuplicateCheck,
          },
          requestHeaderSlice,
          RequestId,
          taskData,
          dynamicData,
          requestorPayload,
          remarks,
          userInput,
          selectedLevel
        );
      }

      const hSuccess = (data) => {
        if (data.statusCode >= 200 && data.statusCode < 300) {
          setButtonsLoading(false);
          setDialogData({
            title: SUCCESS_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
            buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo:
              END_POINTS?.MASTER_BUTTON_APIS?.[MODULE?.PCG]?.[
                hierarchyRequestHeaderData?.RequestType
              ]?.[type]?.NAVIGATE_TO,
          });
          setSuccessDialogOpen(true);
        } else {
          setButtonsLoading(false);
          setDialogData({
            title: FAILURE_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
            buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        }
      };

      const hError = (error) => {
        setButtonsLoading(false);
        setAlertButtonType("error");
        setButtonAlertMsg(error?.error || "An error occurred");
        setOpenButtonSnackBar(true);
      };

      doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
    } catch (error) {
      console.error("Error in handleButtonClick:", error);
      setButtonsLoading(false);
    }
  };

  const handleSnackBarButtonClose = () => {
    setOpenButtonSnackBar(false);
  };
  return {
    showTree,
    buttonsLoading,
    openButtonSnackBar,
    alertButtonMsg,
    alertButtonType,
    filteredButtons,
    requestorPayload,
    loadForFetching,
    initialNodeData,
    showWfLevels,
    wfLevels,
    fetchOldDescriptionForNode,
    fetchOldParentForNode,
    fetchOldParentForObject,
    handleButtonClick,
    handleSnackBarButtonClose,
  };
};
