import { useCallback } from "react";
import { Modal, message } from "antd";
import { useDispatch } from "react-redux";
import { setTreeData, updateTreeChanges } from "@app/hierarchyDataSlice";

const { confirm } = Modal;

// Reassign IDs while maintaining the hierarchical structure
const reassignIds = (nodes, parentId = "") => {
  return nodes.map((node, index) => {
    const newId = parentId === "" ? `${index}` : `${parentId}_${index}`;
    return {
      ...node,
      id: newId,
      child: node.child ? reassignIds(node.child, newId) : [],
    };
  });
};

const removeNodeForDelete = (nodes, nodeId) => {
  return nodes
    .map((node) => {
      const updatedNode = {
        ...node,
        child: node.child ? removeNodeForDelete(node.child, nodeId) : [],
      };
      return updatedNode;
    })
    .filter((node) => node.id !== nodeId);
};
// Helper function to collect all nodes that will be deleted (including children)
const collectNodesToDelete = (nodes, targetNodeId, parent = null) => {
  const nodesToDelete = [];

  const traverse = (nodeList, parentNode) => {
    for (const node of nodeList) {
      if (node.id === targetNodeId) {
        const collectAll = (n, parentRef) => {
          nodesToDelete.push({
            ...n,
            oldParentNode: parentRef ? parentRef.label : null, // ✅ add parent info
          });
          n.child?.forEach((child) => collectAll(child, n));
        };
        collectAll(node, parentNode);
        return true;
      }
      if (node.child?.length && traverse(node.child, node)) return true;
    }
    return false;
  };

  traverse(nodes, parent);
  return nodesToDelete;
};

const useDeleteOperation = ({ rawTreeData, treeChanges, addToChangeLog }) => {
  const dispatch = useDispatch();

  const handleDeleteNode = useCallback(
    (node) => {
      confirm({
        title: `Delete ${node.label}?`,
        content:
          "Deleting node will remove all Profit Centers attached to the nodes & Sub-Nodes from this Hierarchy",
        okText: "Delete",
        okType: "danger",
        cancelText: "Cancel",
        onOk() {
          const nodesToDelete = collectNodesToDelete(rawTreeData, node.id);
          let updatedTree = removeNodeForDelete([...rawTreeData], node.id);
          updatedTree = reassignIds(updatedTree);

          addToChangeLog("DELETE NODE", `${node?.label} Node deleted`);

          nodesToDelete.forEach((nodeToDelete) => {
          dispatch(
            updateTreeChanges({
              nodeLabel: nodeToDelete.label,
              changes: {
                ...((treeChanges && treeChanges[nodeToDelete.label]) || {}),
                isDeleted: true,
                oldParentNode: nodeToDelete.oldParentNode,
              },
            })
          );
        })

          dispatch(setTreeData(updatedTree));
          const childCount = nodesToDelete.length - 1;
          const successMessage = childCount > 0 
            ? `Deleted ${node.label} and ${childCount} child node(s). Updated Hierarchy`
            : `Deleted ${node.label} and Updated Hierarchy`;
          message.success(successMessage);
        },
      });
    },
    [rawTreeData, treeChanges, dispatch, addToChangeLog]
  );

  return { handleDeleteNode };
};

export default useDeleteOperation;
