import moment from "moment";

// Steps table configuration (same as in other files for consistency)
const stepsTable = [
  { label: "New Nodes", value: "1", key: "NEW NODES" },
  { label: "Description Change", value: "2", key: "DESCRIPTIONS" },
  { label: "Add Profit Centers", value: "3", key: "PROFIT CENTERS" },
  { label: "Move Node", value: "4", key: "MOVE NODE" },
  { label: "Move Profit Center", value: "5", key: "MOVE PROFIT CENTER" },
  { label: "Remove Profit Center", value: "6", key: "REMOVE PROFIT CENTER" },
  { label: "Delete Node", value: "7", key: "DELETE NODE" },
  {
    label: "Change Person Responsible",
    value: "8",
    key: "PERSON RESPONSIBLE",
  },
];

// Change log action types
export const CHANGE_LOG_TYPES = {
  ADD_NODE: "ADD_NODE",
  EDIT_DESCRIPTION: "EDIT_DESCRIPTION", 
  ADD_PROFIT_CENTER: "ADD_PROFIT_CENTER",
  MOVE_NODE: "MOVE_NODE",
  MOVE_PROFIT_CENTER: "MOVE_PROFIT_CENTER",
  REMOVE_PROFIT_CENTER: "REMOVE_PROFIT_CENTER",
  DELETE_NODE: "DELETE_NODE",
  CHANGE_PERSON_RESPONSIBLE: "CHANGE_PERSON_RESPONSIBLE",
  DELETE_ROW: "DELETE_ROW",
  FIELD_UPDATE: "FIELD_UPDATE" // New type for general field updates
};

// Generate unique change log ID
export const generateChangeLogId = () => {
  return Date.now() + Math.random().toString(36).substr(2, 9);
};

// Generate description based on change type and data
export const generateChangeDescription = (type, data, oldData = {}) => {
  switch (type) {
    case CHANGE_LOG_TYPES.ADD_NODE:
      return `New node "${data['New Node']}" added under parent "${data['Parent Node']}"`;
    
    case CHANGE_LOG_TYPES.EDIT_DESCRIPTION:
      return `Description changed for node "${data['Parent Node']}" from "${oldData['Old Description'] || 'N/A'}" to "${data['New Description']}"`;
    
    case CHANGE_LOG_TYPES.ADD_PROFIT_CENTER:
      return `Profit Center "${data['Profit Center']}" added to node "${data['Node']}"`;
    
    case CHANGE_LOG_TYPES.MOVE_NODE:
      return `Node "${data['Selected Node']}" moved from "${data['Old Parent Node']}" to "${data['New Parent Node']}"`;
    
    case CHANGE_LOG_TYPES.MOVE_PROFIT_CENTER:
      return `Profit Center "${data['Selected Profit Center']}" moved from "${data['Old Parent Node']}" to "${data['New Parent Node']}"`;
    
    case CHANGE_LOG_TYPES.REMOVE_PROFIT_CENTER:
      return `Profit Center "${data['Selected Profit Center']}" removed from node "${data['Parent Node']}"`;
    
    case CHANGE_LOG_TYPES.DELETE_NODE:
      return `Node "${data['Deleted Node']}" deleted from parent "${data['Parent Node']}"`;
    
    case CHANGE_LOG_TYPES.CHANGE_PERSON_RESPONSIBLE:
      return `Person responsible changed for node "${data['Parent Node']}" from "${oldData['Old Person Responsible'] || 'N/A'}" to "${data['New Person Responsible']}"`;
    
    case CHANGE_LOG_TYPES.DELETE_ROW:
      return `Row deleted: ${data.description}`;
    
    case CHANGE_LOG_TYPES.FIELD_UPDATE:
      return `Field updated: ${data.description}`;
    
    default:
      return `Unknown change type: ${type}`;
  }
};

// Helper function to detect what fields changed
const getChangedFields = (oldRow, newRow) => {
  const changedFields = [];
  const excludeFields = ['Id', 'Updated By', 'Updated On'];
  
  Object.keys(newRow).forEach(key => {
    if (!excludeFields.includes(key)) {
      const oldValue = oldRow[key] || '';
      const newValue = newRow[key] || '';
      if (oldValue !== newValue) {
        changedFields.push({
          field: key,
          oldValue,
          newValue
        });
      }
    }
  });
  
  return changedFields;
};

// Generate field-specific change description
const generateFieldChangeDescription = (fieldName, oldValue, newValue, tableKey, rowId) => {
  const from = oldValue ? `"${oldValue}"` : 'empty';
  const to = newValue ? `"${newValue}"` : 'empty';
  
  return `${fieldName} changed from ${from} to ${to} in row ${rowId} (${tableKey})`;
};

// Helper function to get duplicate list operations for a field change
const getDuplicateListOperations = (field, oldValue, newValue, stepIndex) => {
  const operations = [];
  
  switch (stepIndex) {
    case 0: // New Nodes
      if (field === 'New Node') {
        if (oldValue && oldValue.trim() !== '') {
          operations.push({ type: 'node', value: oldValue, action: 'remove' });
        }
        if (newValue && newValue.trim() !== '') {
          operations.push({ type: 'node', value: newValue, action: 'add' });
        }
      } else if (field === 'Description') {
        if (oldValue && oldValue.trim() !== '') {
          operations.push({ type: 'desc', value: oldValue, action: 'remove' });
        }
        if (newValue && newValue.trim() !== '') {
          operations.push({ type: 'desc', value: newValue, action: 'add' });
        }
      }
      break;
      
    case 1: // Description Change
      if (field === 'New Description') {
        if (oldValue && oldValue.trim() !== '') {
          operations.push({ type: 'desc', value: oldValue, action: 'remove' });
        }
        if (newValue && newValue.trim() !== '') {
          operations.push({ type: 'desc', value: newValue, action: 'add' });
        }
      }
      break;
      
    case 2: // Add Profit Centers
      // Profit centers don't need duplicate checking typically, but you can add logic here if needed
      break;
      
    case 3: // Move Node
      // Node moves don't create new nodes, so no duplicate checking needed
      break;
      
    case 4: // Move Profit Center
      // Profit center moves don't create new entities, so no duplicate checking needed
      break;
      
    case 5: // Remove Profit Center
      // Removing doesn't create duplicates
      break;
      
    case 6: // Delete Node
      // Deleting doesn't create duplicates
      break;
      
    case 7: // Change Person Responsible
      // Person responsible changes don't typically need duplicate checking
      break;
      
    default:
      break;
  }
  
  return operations;
};

// Enhanced function to determine which fields should be checked for duplicates
export const isDuplicateCheckField = (fieldName, tableKey) => {
  const stepIndex = stepsTable.findIndex(step => step.key === tableKey);
  
  switch (stepIndex) {
    case 0: // New Nodes
      return fieldName === 'New Node' || fieldName === 'Description';
    case 1: // Description Change
      return fieldName === 'New Description';
    // Add more cases if you want to check duplicates for other tables
    default:
      return false;
  }
};

// Enhanced function to get duplicate check type
export const getDuplicateCheckType = (fieldName, tableKey) => {
  const stepIndex = stepsTable.findIndex(step => step.key === tableKey);
  
  switch (stepIndex) {
    case 0: // New Nodes
      if (fieldName === 'New Node') return 'node';
      if (fieldName === 'Description') return 'desc';
      break;
    case 1: // Description Change
      if (fieldName === 'New Description') return 'desc';
      break;
    // Add more cases as needed
    default:
      break;
  }
  return null;
};

// Helper function to log field changes and update duplicate check lists
export const logFieldChanges = (oldRow, newRow, tableKey, stepsTable, addToChangeLog, updateDuplicateCheckLists = null) => {
  const stepIndex = stepsTable.findIndex(step => step.key === tableKey);
  if (stepIndex === -1) return;

  const changedFields = getChangedFields(oldRow, newRow);
  
  // Collect all duplicate list operations for batch processing
  let allOperations = [];
  
  // Log each field change individually
  changedFields.forEach(({ field, oldValue, newValue }) => {
    if (newValue.trim() !== '') { // Only log when user actually enters something
      const description = generateFieldChangeDescription(field, oldValue, newValue, tableKey, newRow.Id);
      
      // Determine change type based on field and table
      let changeType = 'FIELD_UPDATE';
      
      // Use specific change types for key operations
      switch (stepIndex) {
        case 0: // New Nodes
          if (field === 'New Node' && newValue) {
            changeType = CHANGE_LOG_TYPES.ADD_NODE;
          }
          break;
        case 1: // Description Change
          if (field === 'New Description' && newValue) {
            changeType = CHANGE_LOG_TYPES.EDIT_DESCRIPTION;
          }
          break;
        case 2: // Add Profit Centers
          if (field === 'Profit Center' && newValue) {
            changeType = CHANGE_LOG_TYPES.ADD_PROFIT_CENTER;
          }
          break;
        case 3: // Move Node
          if (field === 'Selected Node' && newValue) {
            changeType = CHANGE_LOG_TYPES.MOVE_NODE;
          }
          break;
        case 4: // Move Profit Center
          if (field === 'Selected Profit Center' && newValue) {
            changeType = CHANGE_LOG_TYPES.MOVE_PROFIT_CENTER;
          }
          break;
        case 5: // Remove Profit Center
          if (field === 'Selected Profit Center' && newValue) {
            changeType = CHANGE_LOG_TYPES.REMOVE_PROFIT_CENTER;
          }
          break;
        case 6: // Delete Node
          if (field === 'Deleted Node' && newValue) {
            changeType = CHANGE_LOG_TYPES.DELETE_NODE;
          }
          break;
        case 7: // Change Person Responsible
          if (field === 'New Person Responsible' && newValue) {
            changeType = CHANGE_LOG_TYPES.CHANGE_PERSON_RESPONSIBLE;
          }
          break;
      }

      // Get duplicate list operations for this field change
      const operations = getDuplicateListOperations(field, oldValue, newValue, stepIndex);
      allOperations = [...allOperations, ...operations];

      addToChangeLog(changeType, description, {
        rowId: newRow.Id,
        fieldName: field,
        oldValue,
        newValue,
        oldData: { ...oldRow },
        newData: { ...newRow },
        tableKey
      });
    }
  });

  // Batch update duplicate lists if there are operations and functions are provided
  if (allOperations.length > 0 && updateDuplicateCheckLists) {
    if (updateDuplicateCheckLists.batchUpdateDuplicateLists) {
      updateDuplicateCheckLists.batchUpdateDuplicateLists(allOperations);
    } else {
      // Fallback to individual operations if batch function is not available
      allOperations.forEach(operation => {
        const { type, value, action } = operation;
        if (type === 'node' && updateDuplicateCheckLists.updateNodesListForDuplicateCheck) {
          updateDuplicateCheckLists.updateNodesListForDuplicateCheck(value, action);
        } else if (type === 'desc' && updateDuplicateCheckLists.updateDescListForDuplicateCheck) {
          updateDuplicateCheckLists.updateDescListForDuplicateCheck(value, action);
        }
      });
    }
  }
};

// Utility function to validate duplicate entries
export const validateDuplicateEntry = (value, type, checkForDuplicates) => {
  if (!value || !checkForDuplicates) return { isValid: true, message: '' };
  
  const isDuplicate = checkForDuplicates(value, type);
  
  if (isDuplicate) {
    const typeLabel = type === 'node' ? 'Node' : 'Description';
    return {
      isValid: false,
      message: `${typeLabel} "${value}" already exists. Please choose a different ${typeLabel.toLowerCase()}.`
    };
  }
  
  return { isValid: true, message: '' };
};

// Function to clean up duplicate lists (remove empty entries, duplicates)
export const cleanupDuplicateLists = (nodesList, descsList) => {
  const cleanNodes = [...new Set(nodesList.filter(node => node && node.trim() !== ''))];
  const cleanDescs = [...new Set(descsList.filter(desc => desc && desc.trim() !== ''))];
  
  return { cleanNodes, cleanDescs };
};

// Function to export duplicate lists for debugging
export const exportDuplicateListsForDebug = (nodesList, descsList) => {
  const debugData = {
    timestamp: moment().format('YYYY-MM-DD HH:mm:ss'),
    nodesCount: nodesList.length,
    descriptionsCount: descsList.length,
    nodes: nodesList,
    descriptions: descsList
  };
  
  const dataStr = JSON.stringify(debugData, null, 2);
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
  
  const exportFileDefaultName = `duplicate_lists_debug_${moment().format('YYYY_MM_DD_HH_mm')}.json`;
  
  const linkElement = document.createElement('a');
  linkElement.setAttribute('href', dataUri);
  linkElement.setAttribute('download', exportFileDefaultName);
  linkElement.click();
};