// SettingsScreen.jsx
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import SettingsSidebar from "./SettingsSidebar";
import ThemePreference from './ThemePreference';
import LanguageSettings from './LanguageSettings';
import NotificationSettings from './NotificationSettings';
import ApplicationSettings from '../Common/ApplicationSettings';

const SettingsScreen = () => {
  const [activeMenuItem, setActiveMenuItem] = useState('theme');

  const renderContent = () => {
    switch (activeMenuItem) {
      case 'theme':
        return <ThemePreference />;
        case 'Language':
        return <LanguageSettings/>;
        case 'notifications':
        return <NotificationSettings/>;
        case 'region':
        return <ApplicationSettings/>;
      default:
        return <Typography variant="body1" sx={{ mt: 4 }}>Coming soon...</Typography>;
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100%', backgroundColor: '#f8f9fa' }}>
      <SettingsSidebar activeItem={activeMenuItem} onChange={setActiveMenuItem} />
      <Box sx={{ flex: 1, p: 4 }}>{renderContent()}</Box>
    </Box>
  );
};

export default SettingsScreen;
