import{n as ee,o as ke,p as yl,q as Gl,s as vl,g as Pl,r as i,P as Sl,t as re,a as xl,v as Ll,w as be,x as I,y as $l,z as _,C as v,E as Rl,G as kl,H as bl,I as Bl,J as Be,K as wl,M as Ul,c as d,j as o,N as Yl,S as Nt,O as p,Q as Ol,d as $,U as zl,V as Hl,W as Wl,i as Vl,X as x,Y as N,Z as we,$ as W,a0 as Xl,a1 as ql,a2 as Jl,a3 as Kl,a4 as Ql,a5 as Zl,a6 as Ue,a7 as At,a8 as Ye,a9 as Fl,F as ce,aa as Oe,ab as Tt,ac as jl,ad as ml,ae as es,af as ts,ag as ze,ah as as,ai as ls,aj as ss,ak as ns,al as os,am as is,an as ae,ao as rs,ap as cs,aq as It,ar as yt,as as Gt,at as V,k as ds,A as us,au as g,av as Ms,aw as fs,ax as ps,ay as gs,az as Ds,aA as hs,aB as _s,aC as Cs,aD as Es,aE as Ns,aF as As,aG as Ts,aH as Is,aI as ys,aJ as de,aK as He,aL as vt,aM as Pt,T as We,B as te,aN as Gs,aO as vs}from"./index-226a1e75.js";import{d as Ps}from"./History-09ae589c.js";import{A as Ss}from"./AttachmentUploadDialog-5b2112e0.js";import{R as xs}from"./ReusablePresetFilter-da63464b.js";import{u as Ls}from"./useMaterialFieldConfig-a3bf7965.js";import{L as w}from"./LargeDropdown-b7ffdbd5.js";import{S as $s}from"./SingleSelectDropdown-ee61a6b7.js";import{E as Rs}from"./ExportExcelSearch-5bce0696.js";import"./CloudUpload-17ed0189.js";import"./Delete-3f2fc9ef.js";import"./utilityImages-067c3dc2.js";import"./FeedOutlined-2c089703.js";import"./FilterChangeDropdown-2d228e28.js";const ks=V(ds,{target:"e1t2kitc5"})(({theme:T})=>({marginTop:"0px !important",border:`1px solid ${T.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),bs=V(us,{target:"e1t2kitc4"})(({theme:T})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:T.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:T.palette.primary.light}}),""),Bs=V(p,{target:"e1t2kitc3"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),ws=V(p,{target:"e1t2kitc2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),St=V(ae,{target:"e1t2kitc1"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),A=V($,{target:"e1t2kitc0"})(({theme:T})=>({fontSize:"0.75rem",color:T.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}),""),Dn=()=>{var _t,Ct,Et;const T=ee(t=>t.materialDropDownData.dropDown),U=ee(t=>t.request.salesOrgDTData),{fetchOrgData:Ve}=Ls(),{getDtCall:xt,dtData:X}=ke(),{getDtCall:Lt,dtData:q}=ke(),{getDtCall:$t,dtData:J}=ke(),{fetchDataAndDispatch:Rt}=yl(),ue=Gl();let Me=ee(t=>{var l;return(l=t.userManagement.entitiesAndActivities)==null?void 0:l.Material});const f=vl(),K=Pl(),kt=48,bt=8,Bt={PaperProps:{style:{maxHeight:kt*4.5+bt,width:250}}},h=ee(t=>t.appSettings),[Us,wt]=i.useState(!0),[Ys,Ut]=i.useState(!1),[Xe,P]=i.useState(!1),[Os,fe]=i.useState(!1),[qe,Je]=i.useState(!1),[z,Q]=i.useState({}),[pe,ge]=i.useState([]),[De,Ke]=i.useState([]),[zs,Yt]=i.useState(""),[Qe,Ot]=i.useState(null),[zt,Ht]=i.useState([]),[he,_e]=i.useState([]),[C,Ze]=i.useState(""),[Ce,Fe]=i.useState([]),[y,Ee]=i.useState([]),[Ne,Ae]=i.useState([]),[Wt,Hs]=i.useState(!1),[E,Te]=i.useState([]),[Ie,H]=i.useState([]),[je,S]=i.useState(!1),[Vt,Xt]=i.useState(null),[G,ye]=i.useState([]),[qt,Ge]=i.useState([...G]),[R,Jt]=i.useState((_t=Sl)==null?void 0:_t.TOP_SKIP);re.useState(""),i.useState(!1),i.useState("");const[Ws,Kt]=i.useState(!1),[Vs,Qt]=i.useState(!0),[Zt,Ft]=i.useState([]);i.useState([]);const[jt,me]=i.useState([]),[Xs,Z]=i.useState(!0),[qs,mt]=i.useState([]),[Js,ea]=i.useState([]),[le,ve]=i.useState([]),[Ks,ta]=i.useState([]),[Qs,aa]=i.useState({}),[la,sa]=i.useState([]),[na,oa]=i.useState([]),[F,ia]=i.useState([]),[ra,ca]=i.useState([]),[Pe,da]=i.useState([]),[ua,Ma]=i.useState("success"),[fa,et]=i.useState(!1),[pa,Se]=i.useState(!1),[tt,at]=i.useState(""),[Zs,ga]=i.useState(!1),[Da,lt]=i.useState(!1),se=re.useRef(null),[ha,_a]=i.useState(0),[Ca,st]=i.useState(!1),ne=re.useRef(null),[Ea,Na]=i.useState(0),Aa=["Create Multiple","Upload Template","Download Template"],Ta=["Change Multiple","Upload Template","Download Template"],[Ia,Fs]=i.useState(""),[ya,xe]=i.useState(!1),[Ga,Le]=i.useState(!1),{t:u}=xl(),[$e,va]=i.useState(),Pa=t=>{xe(!1)},Sa=()=>{et(!0)},xa=()=>{et(!1)},e=ee(t=>t.commonFilter.MaterialMaster),La=()=>{gl(!1)},$a=(t,l)=>{l!==0&&(_a(l),lt(!1),l===1?Ka():l===2&&Ha())},Ra=t=>{se.current&&se.current.contains(t.target)||lt(!1)},ka=t=>{const l=new FormData;[...t].forEach(a=>l.append("files",a)),l.append("dtName","MDG_MAT_FIELD_CONFIG"),l.append("version","v1");var n=`/${g}/massAction/getAllMaterialsFromExcel`;v(n,"postformdata",a=>{var M;f(ys((M=a==null?void 0:a.body)==null?void 0:M.tableData)),K("/masterDataCockpit/materialMaster/massMaterialTable",{state:tt}),a.statusCode===200&&(k("Create"),L(`${t.name} has been Uploaded Succesfully`),b("success"),Qt(!1),ga(!0),Kt(!0),K("/masterDataCockpit/materialMaster/massMaterialTable",{state:tt})),La()},a=>{console.log(a)},l)},ba=(t,l)=>{l!==0&&(Na(l),st(!1),l===1?Qa():l===2&&za())},Ba=t=>{ne.current&&ne.current.contains(t.target)||st(!1)},nt=(t="")=>{var r;S(!0);let l={materialNo:t,salesOrg:((r=U==null?void 0:U.uniqueSalesOrgList)==null?void 0:r.map(a=>a.code).join("$^$"))||"",top:200,skip:0};const n=a=>{S(!1),Ht(a.body)},s=a=>{console.log(a)};v(`/${g}/data/getSearchParamsMaterialNo`,"post",n,s,l)},wa=t=>{const l=t.target.value;if(Yt(l),Qe&&clearTimeout(Qe),l.length>=4){const n=setTimeout(()=>{nt(l)},500);Ot(n)}},Ua=t=>{if(t.target.value!==null){var l=t.target.value;let n={...e,description:l};f(I({module:"MaterialMaster",filterData:n}))}},Ya=(t,l)=>{if(t.target.value!==null){var n=t.target.value;let s={...e,createdBy:n};f(I({module:"MaterialMaster",filterData:s}))}},Oa=(t,l)=>{if(t.target.value!==null){var n=t.target.value;let s={...e,oldMaterialNumber:n};f(I({module:"MaterialMaster",filterData:s}))}},za=()=>{var l={materialNos:Ia.map(r=>r.materialNumber??""),dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"};let n=r=>{wt(!1);const a=URL.createObjectURL(r),M=document.createElement("a");M.href=a,M.setAttribute("download","Material_Mass Change.xls"),document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(a),B(),k("Success"),L("Material_Mass Change.xls has been downloaded successfully"),b("success")},s=r=>{r.message&&(B(),k("Error"),L(`${r.message}`),b("danger"))};v(`/${g}/excel/downloadExcelWithData`,"postandgetblob",n,s,l)},Ha=async()=>{const t=new URLSearchParams({dtName:"MDG_MAT_FIELD_CONFIG",version:"v2"});let l=s=>{const r=URL.createObjectURL(s),a=document.createElement("a");a.href=r,a.setAttribute("download",`${name}`),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(r),B(),k("Success"),L("Material_Mass Create.xls has been downloaded successfully"),b("success")},n=s=>{s.message&&(B(),k("Error"),L(`${s.message}`),b("danger"))};v(`/${g}/excel/downloadExcel?${t.toString()}`,"getblobfile",l,n)};let ot={"Basic Material":`/${g}/data/getBasicMatl`,"Product Hierarchy":`/${g}/data/getProdHier`,"Purchasing Group":`/${g}/data/getPurGroup`,"Lab/Office":`/${g}/data/getDsnOffice`,"Transportation Group":`/${g}/data/getTransGrp`,"Material Group 5":`/${g}/data/getMatlGrp5`,"Profit Center":`/${g}/data/getProfitCenterBasedOnPlant`,"MRP Controller":`/${g}/data/getMRPController`,"Warehouse No.":`/${g}/data/getWareHouseNo`,"MRP Profile":`/${g}/data/getMRPProfile`};const it=(t,l)=>{let n={plant:(y==null?void 0:y.map(a=>a.code).join("$^$"))||""};fe(!0);const s=a=>{fe(!1);const M=a.body;ia(D=>({...D,[l]:M}))},r=a=>{fe(!1)};l==="Profit Center"?v(t,"post",s,r,n):v(t,"get",s,r)},Wa=t=>{var l,n;((l=z[t])==null?void 0:l.length)===((n=F[t])==null?void 0:n.length)?Q(s=>({...s,[t]:[]})):Q(s=>({...s,[t]:F[t]??[]}))},Va=t=>{const l=t.target.value;ve(l),ta([]),l.forEach(async n=>{const s=ot[n];it(s,n)})},Xa=()=>{S(!0);const t=n=>{S(!1),sa(n.body)},l=n=>{S(!1)};v(`/${g}/data/getMatlType`,"get",t,l)},qa=()=>{S(!0);const t=n=>{oa(n.body),S(!1)},l=n=>{S(!1),console.log(n)};v(`/${g}/data/getMatlGroup`,"get",t,l)},Ja=t=>{S(!0);let l={salesOrg:t?t.map(r=>r==null?void 0:r.code).join("$^$"):""};const n=r=>{S(!1),me(r.body)},s=r=>{console.log(r)};v(`/${g}/data/getDistrChan`,"post",n,s,l)},Ka=()=>{Se(!0),at("Create")},Qa=()=>{Se(!0),at("Change")};i.useEffect(()=>{qe&&(j(),Je(!1))},[qe]),i.useEffect(()=>{[{url:`/${g}/data/getCSalStatus`,keyName:"CSalStatus"}].forEach(({url:l,keyName:n})=>{Rt(l,n)}),f(Ll())},[]);const Za=()=>{f(Ms()),f(fs()),f(ps()),f(gs()),f(Ds()),f(hs([])),f(_s({})),f(Cs({}))};i.useEffect(()=>(qa(),Xa(),Za(),it([ot]),()=>{f(be({module:"MaterialMaster",days:7}))}),[]),i.useEffect(()=>Ge([...G]),[G]),i.useEffect(()=>{var t=De.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,type:t};f(I({module:"MaterialMaster",filterData:l}))},[De]),i.useEffect(()=>{var t=Ie.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,distributionChannel:t};f(I({module:"MaterialMaster",filterData:l}))},[Ie]),i.useEffect(()=>{var t=pe.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,PurStatus:t};f(I({module:"MaterialMaster",filterData:l}))},[pe]),i.useEffect(()=>{var t=he.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,number:t};f(I({module:"MaterialMaster",filterData:l}))},[he]),i.useEffect(()=>{var t=Ne.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,division:t};f(I({module:"MaterialMaster",filterData:l}))},[Ne]),i.useEffect(()=>{Object.keys(z).forEach(t=>{var s;const l=(s=z[t])==null?void 0:s.map(r=>r==null?void 0:r.code).join("$^$");let n={...e,[t]:l};f(I({module:"MaterialMaster",filterData:n}))})},[z]),i.useEffect(()=>{var t=E.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,salesOrg:t};f(I({module:"MaterialMaster",filterData:l})),Fa(),nt()},[E]);const Fa=()=>{const t=$l(E,U);Ft(t)};i.useEffect(()=>{var t=Ce.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,group:t};f(I({module:"MaterialMaster",filterData:l}))},[Ce]),i.useEffect(()=>{var t=y.map(n=>n==null?void 0:n.code).join("$^$");let l={...e,plant:t};f(I({module:"MaterialMaster",filterData:l}))},[y]);const ja=t=>{if(!t){ie(ct),Y(0),Ge([...G]);return}const l=G.filter(n=>{var a;let s=!1,r=Object.keys(n);for(let M=0;M<r.length&&(s=n[r[M]]?(n==null?void 0:n[r==null?void 0:r[M]])&&((a=n==null?void 0:n[r==null?void 0:r[M]].toString().toLowerCase())==null?void 0:a.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!s);M++);return s});Ge([...l]),ie(l==null?void 0:l.length)},rt=new Date,oe=new Date;oe.setDate(oe.getDate()-7),i.useState([oe,rt]),i.useState([oe,rt]);const ma=t=>{var l=t;f(I({module:"MaterialMaster",filterData:{...e,createdOn:l}}))},j=()=>{Y(0),P(!0),Le(!1);let t={fromDate:_(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:_(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:R,skip:0,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const l=s=>{var D;if((s==null?void 0:s.statusCode)===de.STATUS_200){var r=[];for(let c=0;c<((D=s==null?void 0:s.body)==null?void 0:D.length);c++){var a=s==null?void 0:s.body[c],M={id:He(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:_(a.CreatedOn).format(h==null?void 0:h.dateFormat),changedOn:_(a.LastChange).format(h==null?void 0:h.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,Division:a.Division!==""?`${a.Division} ${a.DivisionDesc?"-"+a.DivisionDesc:""}`:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(M)}r.sort((c,O)=>_(c.createdOn,"DD MMM YYYY HH:mm")-_(O.createdOn,"DD MMM YYYY HH:mm")),ye(r.reverse()),P(!1),ie(s.count),dt(s.count),f(vt({module:"MaterialMgmt"}))}else(s==null?void 0:s.statusCode)===de.STATUS_414&&(Pt(s==null?void 0:s.message,"error"),P(!1))},n=s=>{console.log(s)};v(`/${g}/data/getMaterialBasedOnAdditionalParams`,"post",l,n,t)},el=()=>{P(!0);let t={fromDate:_(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:_(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:R,skip:R*m,fetchCount:!1,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const l=s=>{var D;P(!1);var r=[];for(let c=0;c<((D=s==null?void 0:s.body)==null?void 0:D.length);c++){var a=s==null?void 0:s.body[c],M={id:He(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"Not Available",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:_(a.CreatedOn).format(h==null?void 0:h.dateFormat),changedOn:_(a.LastChange).format(h==null?void 0:h.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",storageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(M)}r.sort((c,O)=>_(c.createdOn,"DD MMM YYYY HH:mm")-_(O.createdOn,"DD MMM YYYY HH:mm")),ye(c=>[...c,...r]),P(!1)},n=s=>{P(!1),console.log(s)};v(`/${g}/data/getMaterialBasedOnAdditionalParams`,"post",l,n,t)};i.useState(null);const[tl,ie]=i.useState(0),[ct,dt]=i.useState(0),[js,al]=i.useState(!1),[ll,sl]=i.useState(!1),[ms,nl]=i.useState(!1),[en,ol]=i.useState(!1),[il,ut]=i.useState(!1),[rl,k]=i.useState(""),[Mt,L]=i.useState(""),[cl,b]=i.useState(""),Re=()=>{sl(!1)},B=()=>{ut(!0)},ft=()=>{ut(!1),ol(!1),al(!1),nl(!1)};i.useState(null);const[pt,dl]=i.useState(!1),gt=()=>{Ee([]),ve([]),_e([]),Ze(""),ge([]),H([]),Fe([]),Te([]),Q({}),Ke([]),Ae([]),f(be({module:"MaterialMaster"})),aa(t=>{const l={...t};return Object.keys(l).forEach(n=>{l[n]={code:"",desc:""}}),l}),Je(!0)},ul=t=>{const l=t.map(c=>G.find(O=>O.id===c));var n=l.map(c=>c.company),s=new Set(n),r=l.map(c=>c.vendor),a=new Set(r),M=l.map(c=>c.paymentTerm),D=new Set(M);l.length>0?s.size===1?a.size===1?D.size!==1?(Z(!0),k("Error"),L("Invoice cannot be generated for vendors with different payment terms"),b("danger"),B()):Z(!1):(Z(!0),k("Error"),L("Invoice cannot be generated for multiple suppliers"),b("danger"),B()):(Z(!0),k("Error"),L("Invoice cannot be generated for multiple companies"),b("danger"),B()):Z(!0),mt(t),ea(l)},[m,Y]=i.useState(0),Ml=t=>{const l=t.target.value;Jt(l),Y(0)},fl=(t,l)=>{Y(isNaN(l)?0:l)};i.useEffect(()=>{Ga||m!=0&&m*R>=(G==null?void 0:G.length)&&el()},[m,R]);function pl(){j()}const[tn,gl]=i.useState(!1),Dl=(t,l)=>({field:t,headerName:u(l),editable:!1,flex:1,renderCell:n=>{const s=n.value?n.value.split(",").map(M=>M.trim()):[],r=s.length-1;if(s.length===0)return"-";const a=M=>{const[D,...c]=M.split("-");return d(ce,{children:[o("strong",{children:D}),c.length?` - ${c.join("-")}`:""]})};return d(te,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[o(We,{title:s[0],placement:"top",arrow:!0,children:o($,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:a(s[0])})}),r>0&&o(te,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:o(We,{arrow:!0,placement:"right",title:d(te,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[d($,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",l,"s (",r,")"]}),s.slice(1).map((M,D)=>o($,{variant:"body2",sx:{mb:.5},children:a(M)},D))]}),children:d(te,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[o(Gs,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),d($,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",r]})]})})})]})}}),hl=()=>({field:"dataValidation",headerName:u("Audit History"),editable:!1,flex:1,renderCell:t=>o(te,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:o(We,{title:"View Audit Log",placement:"top",children:o(Ue,{onClick:n=>{var s;n.stopPropagation(),K("/masterDataCockpit/consolidatedRequestHistory",{state:{materialNumber:t.row.Number,module:(s=vs)==null?void 0:s.MAT}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:o(Ps,{sx:{fontSize:"1.5rem"}})})})})}),_l=(t,l)=>({field:t,headerName:u(l),editable:!1,flex:1,renderCell:n=>{var a;const[s,...r]=((a=n.value)==null?void 0:a.split(" - "))||[];return d("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[o("strong",{children:s})," ",r.length?`- ${r.join(" - ")}`:""]})}}),Dt=t=>{let l={decisionTableId:null,decisionTableName:Be.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(t==null?void 0:t.toUpperCase())||"US","MDG_CONDITIONS.MDG_MODULE":"Material","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};Lt(l)},Cl=()=>{let t={decisionTableId:null,decisionTableName:Be.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Material","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};$t(t)},El=t=>{const l=[];let n=(t==null?void 0:t.sort((s,r)=>s.MDG_MAT_SEQUENCE_NO-r.MDG_MAT_SEQUENCE_NO))||[];return n&&(n==null||n.forEach(s=>{if((s==null?void 0:s.MDG_MAT_VISIBILITY)===Es.DISPLAY&&s!=null&&s.MDG_MAT_UI_FIELD_NAME){const r=s.MDG_MAT_JSON_FIELD_NAME,a=s.MDG_MAT_UI_FIELD_NAME;r==="DataValidation"?l.push(hl()):s.MDG_MAT_FIELD_TYPE==="Multiple"?l.push(Dl(r,a)):s.MDG_MAT_FIELD_TYPE==="Single"&&l.push(_l(r,a))}})),l};i.useEffect(()=>{var t,l,n,s;if(q){const r=El((l=(t=q==null?void 0:q.result)==null?void 0:t[0])==null?void 0:l.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);ca(r)}if(J){const r=(s=(n=J==null?void 0:J.result)==null?void 0:n[0])==null?void 0:s.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,a=r==null?void 0:r.filter(M=>M.MDG_MAT_FILTER_TYPE==="Additional").map(M=>({title:u(M.MDG_MAT_UI_FIELD_NAME)}));da(r),va(a)}},[q,J]),i.useEffect(()=>{j()},[R]),i.useEffect(()=>{var t;if(C){Ve(C),ht(C);const l=(t=C==null?void 0:C.code)==null?void 0:t.toUpperCase();(l==="US"||l==="EUR")&&Dt(l)}},[C]),i.useEffect(()=>(Dt("US"),Cl(),f(be({module:"DuplicateDesc"})),f(Rl()),f(kl()),f(bl({})),()=>{f(Bl())}),[]);let Nl=i.useRef(null);const ht=t=>{let l={decisionTableId:null,decisionTableName:Be.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(t==null?void 0:t.code)||""}]};xt(l)};i.useEffect(()=>{var t,l;if(X){const n=wl((l=(t=X==null?void 0:X.result)==null?void 0:t[0])==null?void 0:l.MDG_MAT_REGION_DIVISION_MAPPING);f(Ul({keyName:"Division",data:n}))}},[X]),i.useEffect(()=>{C&&(Ve(C),ht(C))},[C]);const Al=()=>{Le(!0),Il()},Tl=()=>{Le(!0),Y(0)},Il=()=>{Y(0),P(!0);let t={fromDate:_(e==null?void 0:e.createdOn[0]).format("YYYYMMDD")??"",toDate:_(e==null?void 0:e.createdOn[1]).format("YYYYMMDD")??"",createdBy:(e==null?void 0:e.createdBy)??"",materialDesc:(e==null?void 0:e.description)??"",plant:(e==null?void 0:e.plant)??"",materialGroup:(e==null?void 0:e.group)??"",materialType:(e==null?void 0:e.type)??"",changedBy:(e==null?void 0:e.changedBy)??"",taskId:(e==null?void 0:e.taskId)??"",status:(e==null?void 0:e.status)??"",salesOrg:(e==null?void 0:e.salesOrg)??"",division:(e==null?void 0:e.division)??"",distributionChannel:(e==null?void 0:e.distributionChannel)??"",storageLocation:(e==null?void 0:e.storageLocation)??"",ProdHier:(e==null?void 0:e["Product Hierarchy"])??"",BasicMatl:(e==null?void 0:e["Basic Material"])??"",ProfitCtr:(e==null?void 0:e["Profit Center"])??"",PurGroup:(e==null?void 0:e["Purchasing Group"])??"",MatlGrp5:(e==null?void 0:e["Material Group 5"])??"",MrpCtrler:(e==null?void 0:e["MRP Controller"])??"",warehouseNo:(e==null?void 0:e["Warehouse No"])??"",Mrpprofile:(e==null?void 0:e["MRP Profile"])??"",oldMaterialNo:(e==null?void 0:e.oldMaterialNumber)??"",number:(e==null?void 0:e.number)??"",PurStatus:(e==null?void 0:e.PurStatus)??"",top:ct,skip:0,labOffice:(e==null?void 0:e["Lab/Office"])??"",transportationGroup:(e==null?void 0:e["Transportation Group"])??"",batchManagement:(e==null?void 0:e.batchManagement)??""};const l=s=>{var D;if((s==null?void 0:s.statusCode)===de.STATUS_200){var r=[];for(let c=0;c<((D=s==null?void 0:s.body)==null?void 0:D.length);c++){var a=s==null?void 0:s.body[c],M={id:He(),Number:a.Number,materialType:a.Materialtype!==""?`${a.Materialtype} - ${a.MaterialTypeDesc}`:"Not Available",materialDesc:a.MaterialDescrption!==""?`${a.MaterialDescrption}`:"Not Available",materialGroup:a.MaterialGroup!==""?`${a.MaterialGroup} - ${a.materialGroupDesc}`:"-",XplantMatStatus:a.XplantMatStatus!==""?`${a.XplantMatStatus} ${a.XplantMatStatusDesc?"-"+a.XplantMatStatusDesc:""}`:"-",Plant:a.Plant.length>0?`${a.Plant}`:"-",WarehouseNo:a.WarehouseNo.length>0?`${a.WarehouseNo}`:"-",createdOn:_(a.CreatedOn).format(h==null?void 0:h.dateFormat),changedOn:_(a.LastChange).format(h==null?void 0:h.dateFormat),changedBy:a.ChangedBy,createdBy:a.CreatedBy,division:a.Division!==""?`${a.Division}- ${a.DivisionDesc} `:"Not Available",StorageLocation:a.StorageLocation.length>0?`${a.StorageLocation} `:"-",oldMaterialNumber:a.OldMaterialNumber!==""?`${a.OldMaterialNumber} - ${a.OldMaterialNumberName}`:"Not Available",labOffice:a.LabOffice!==""?`${a.LabOffice} - ${a.LabOfficeName}`:"Not Available",transportationGroup:a.TrnsportGroup!==""?`${a.TrnsportGroup} - ${a.TrnsportGroupName}`:"Not Available",SalesOrg:a.SalesOrg.length>0?`${a.SalesOrg}`:"-",DistChnl:a.DistChnl.length>0?`${a.DistChnl}`:"-",indSector:a.Industrysector!==""?a.Industrysector:"-",PrimaryVendor:a.PryVendor!==""?a.PryVendor:"-"};r.push(M)}r.sort((c,O)=>_(c.createdOn,"DD MMM YYYY HH:mm")-_(O.createdOn,"DD MMM YYYY HH:mm")),ye(r.reverse()),P(!1),Y(Math.floor((r==null?void 0:r.length)/R)),ie(s.count),dt(s.count),f(vt({module:"MaterialMgmt"}))}else(s==null?void 0:s.statusCode)===de.STATUS_414&&(Pt(s==null?void 0:s.message,"error"),P(!1))},n=s=>{console.log(s)};v(`/${g}/data/getMaterialBasedOnAdditionalParams`,"post",l,n,t)};return d("div",{ref:Nl,children:[o(Ns,{dialogState:il,openReusableDialog:B,closeReusableDialog:ft,dialogTitle:rl,dialogMessage:Mt,handleDialogConfirm:ft,dialogOkText:"OK",dialogSeverity:cl}),o(As,{openSnackBar:fa,alertMsg:Mt,alertType:ua,handleSnackBarClose:xa}),o("div",{style:{...Yl,backgroundColor:"#FAFCFF"},children:d(Nt,{spacing:1,children:[o(p,{container:!0,mt:0,sx:Ol,children:d(p,{item:!0,md:5,children:[o($,{variant:"h3",children:o("strong",{children:u("Material")})}),o($,{variant:"body2",color:"#777",children:u("This view displays the list of Materials")})]})}),o(p,{container:!0,sx:zl,children:o(p,{item:!0,md:12,children:d(ks,{defaultExpanded:!1,children:[d(bs,{expandIcon:o(Hl,{sx:{fontSize:"1.25rem",color:ue.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterMaterial",children:[o(Wl,{sx:{fontSize:"1.25rem",marginRight:1,color:ue.palette.primary.dark}}),o($,{sx:{fontSize:"0.875rem",fontWeight:600,color:ue.palette.primary.dark},children:u("Filter Material")})]}),d(Vl,{sx:{padding:"1rem 1rem 0.5rem"},children:[d(Bs,{container:!0,children:[d(p,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[Pe==null?void 0:Pe.filter(t=>t.MDG_MAT_VISIBILITY!=="Hidden").sort((t,l)=>t.MDG_MAT_SEQUENCE_NO-l.MDG_MAT_SEQUENCE_NO).map((t,l)=>{var n,s,r,a,M,D;return d(re.Fragment,{children:[(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.REGION&&d(p,{item:!0,md:2,children:[d(A,{sx:N,children:[u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)," ",o("span",{style:{color:(s=(n=we)==null?void 0:n.error)==null?void 0:s.dark},children:"*"})]}),o(W,{size:"small",fullWidth:!0,children:o($s,{options:[{code:"US",desc:"USA"},{code:"EUR",desc:"Europe"}],value:C,onChange:c=>{Ze(c),Te([]),H([]),Ee([]),_e([])},placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),disabled:!1,minWidth:"90%",listWidth:210})})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.SALESORG&&d(p,{item:!0,md:2,children:[d(A,{sx:N,children:[u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),o("span",{style:{color:(a=(r=we)==null?void 0:r.error)==null?void 0:a.dark},children:"*"})]}),o(w,{matGroup:C?U==null?void 0:U.uniqueSalesOrgList:[],selectedMaterialGroup:E,setSelectedMaterialGroup:c=>{Te(c),H([]),c.length===0?(me([]),H([])):Ja(c)},placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.PLANT&&d(p,{item:!0,md:2,children:[d(A,{sx:N,children:[u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),o("span",{style:{color:(D=(M=we)==null?void 0:M.error)==null?void 0:D.dark},children:"*"})]}),o(w,{matGroup:E!=null&&E.length?Zt:[],selectedMaterialGroup:y,setSelectedMaterialGroup:Ee,placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.NUMBER&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(W,{size:"small",fullWidth:!0,children:o(Xl,{matGroup:zt,selectedMaterialGroup:he,setSelectedMaterialGroup:_e,isDropDownLoading:je,placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME),onInputChange:wa,minCharacters:4})})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.MATERIALTYPE&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(w,{matGroup:la,selectedMaterialGroup:De,setSelectedMaterialGroup:Ke,placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.MATERIALGROUP&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(w,{matGroup:na,selectedMaterialGroup:Ce,setSelectedMaterialGroup:Fe,placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.DISTRIBUTIONCHANNEL&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(w,{matGroup:E!=null&&E.length?jt:[],selectedMaterialGroup:Ie,setSelectedMaterialGroup:c=>{if(!c||c.length===0){H([]);return}H(c)},isDropDownLoading:je,placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.DIVISION&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(w,{matGroup:C?T==null?void 0:T.Division:[],selectedMaterialGroup:Ne,setSelectedMaterialGroup:c=>{if(!c||c.length===0){Ae([]);return}Ae(c)},placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.PURSTATUS&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(w,{matGroup:(T==null?void 0:T.CSalStatus)??[],selectedMaterialGroup:pe,setSelectedMaterialGroup:c=>{if(!c||c.length===0){ge([]);return}ge(c)},placeholder:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)})]}),(t==null?void 0:t.MDG_MAT_JSON_FIELD_NAME)===x.CREATEDON&&d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t==null?void 0:t.MDG_MAT_UI_FIELD_NAME)}),o(W,{fullWidth:!0,sx:{padding:0},children:o(ql,{dateAdapter:Jl,children:o(Kl,{handleDate:ma,date:e==null?void 0:e.createdOn})})})]})]},l)}),d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u("Add New Filters")}),o(W,{sx:{width:"100%"},children:o(Ql,{sx:{font_Small:N,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:le,onChange:Va,renderValue:t=>t.join(", "),MenuProps:{MenuProps:Bt},endAdornment:le.length>0&&o(Zl,{position:"end",sx:{marginRight:"10px"},children:o(Ue,{size:"small",onClick:()=>ve([]),"aria-label":"Clear selections",children:o(At,{})})}),children:$e==null?void 0:$e.map(t=>d(Ye,{value:t.title,children:[o(Fl,{checked:le.indexOf(t.title)>-1}),t.title]},t.title))})})]})]}),o(p,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:le.map((t,l)=>{var n;return t==="Old Material Number"?o(ce,{children:d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t)}),o(W,{size:"small",fullWidth:!0,children:o(Oe,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.oldMaterialNumber,onChange:Oa,placeholder:u("ENTER OLD MATERIAL NUMBER")})})]},l)}):t==="Material Description"?o(ce,{children:d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u(t)}),o(W,{size:"small",fullWidth:!0,children:o(Oe,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.materialDescription,onChange:Ua,placeholder:u("ENTER MATERIAL DESCRIPTION")})})]},l)}):t==="Created By"?o(ce,{children:d(p,{item:!0,md:2,children:[o(A,{sx:N,children:u("Created By")}),o(Oe,{sx:{fontSize:"12px !important"},fullWidth:!0,size:"small",value:e==null?void 0:e.createdBy,onChange:Ya,placeholder:u("ENTER CREATED BY")})]},l)}):d(p,{item:!0,md:2,children:[o(A,{sx:{fontSize:"12px"},children:u(t)}),o(w,{matGroup:(F==null?void 0:F[t])??[],selectedMaterialGroup:((n=z[t])==null?void 0:n.length)>0?z[t]:[],setSelectedMaterialGroup:s=>{var r;if(!s||s.length===0){Q(a=>({...a,[t]:[]}));return}s.length>0&&((r=s[s.length-1])==null?void 0:r.code)==="Select All"?Wa(t):Q(a=>({...a,[t]:s}))}})]},l)})})]}),d(ws,{children:[o(St,{variant:"outlined",size:"small",startIcon:o(At,{sx:{fontSize:"1rem"}}),onClick:()=>{gt()},disabled:pt,children:u("Clear")}),o(p,{sx:{...Tt},children:o(xs,{moduleName:"MaterialMaster",handleSearch:j,disabled:C===""||!(E!=null&&E.length)||!(y!=null&&y.length),onPresetActiveChange:t=>dl(t),onClearPreset:gt})}),o(St,{variant:"contained",size:"small",startIcon:o(jl,{sx:{fontSize:"1rem"}}),sx:{...ml,...Tt},disabled:pt,onClick:()=>{const t=[];if(C==""&&t.push("Region"),E!=null&&E.length||t.push("SalesOrg"),y!=null&&y.length||t.push("Plant"),t.length>0){L(es.MANDATORY_FILTER_MD(t.join(", "))),Ma("error"),Sa();return}j()},children:u("Search")})]})]})]})})}),o(p,{item:!0,sx:{position:"relative"},children:o(Nt,{children:o(ts,{isLoading:Xe,paginationLoading:Xe,module:"MaterialMgmt",width:"100%",title:u("List of Materials"),rows:qt??[],columns:ra??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:t=>ja(t),onRefresh:pl,pageSize:R,page:m,onPageSizeChange:Ml,rowCount:tl??(G==null?void 0:G.length)??0,onPageChange:fl,getRowIdValue:"id",hideFooter:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:ul,callback_onRowSingleClick:t=>{var n,s;const l=t.row.Number;(s=(n=t==null?void 0:t.row)==null?void 0:n.materialType)==null||s.split(" - ")[0],Ut(!0),K(`/masterDataCockpit/materialMaster/DisplayMaterialSAPView/${l}`,{state:t.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:Al,onSelectFirstPageOptions:Tl})})}),(Me==null?void 0:Me.length)>0&&o(ze,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:d(as,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Vt,onChange:t=>{Xt(t)},children:[d(ls,{open:ll,onClose:Re,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[d(ss,{children:[o($,{variant:"h6",children:"Inputs"}),o(Ue,{onClick:Re,children:o(ns,{})})]}),o(os,{dividers:!0,children:o(p,{container:!0,spacing:1})}),d(is,{children:[o(ae,{onClick:Re,children:"Cancel"}),o(ae,{variant:"contained",children:"Proceed"})]})]}),o(ae,{size:"small",variant:"contained",color:"primary",onClick:()=>{K("/requestBench/createRequest"),f(rs({})),f(cs({}))},className:"createRequestButtonMaterial",children:u("Create Request")}),o(ae,{size:"small",variant:"contained",onClick:()=>{xe(!0)},className:"sapdataMaterial",children:u("SAP Data Export")}),o(It,{sx:{zIndex:1},open:Da,anchorEl:se.current,placement:"top-end",children:o(ze,{style:{width:(Ct=se.current)==null?void 0:Ct.clientWidth},children:o(yt,{onClickAway:Ra,children:o(Gt,{id:"split-button-menu",autoFocusItem:!0,children:Aa.slice(1).map((t,l)=>o(Ye,{selected:l===ha-1,onClick:()=>$a(t,l+1),children:t},t))})})})}),o(It,{sx:{zIndex:1},open:Ca,anchorEl:ne.current,placement:"top-end",children:o(ze,{style:{width:(Et=ne.current)==null?void 0:Et.clientWidth},children:o(yt,{onClickAway:Ba,children:o(Gt,{id:"split-button-menu",autoFocusItem:!0,children:Ta.slice(1).map((t,l)=>o(Ye,{selected:l===Ea-1,onClick:()=>ba(t,l+1),children:t},t))})})})}),pa&&o(Ss,{artifactId:"",artifactName:"",setOpen:Se,handleUpload:ka})]})})]})}),o(Rs,{openSearch:ya,setOpenSearch:xe,onSearchComplete:Pa}),o(Ts,{blurLoading:Wt}),o(Is,{})]})};export{Dn as default};
