import { useState, useEffect, lazy, Suspense, memo } from "react";
import { Routes, Route } from "react-router-dom";
import { Box, Button } from "@mui/material";
import localConfigServer from "../data/localConfigServer.json";
import AppHeader from "../components/Common/AppHeader";
let SideNavBar = lazy(() => import("../components/Common/SideNavBar"));
import { useSelector, useDispatch } from "react-redux";
import { DialogActions, DialogContent } from "@mui/material";
import { button_Outlined, button_Primary } from "../components/Common/commonStyles.jsx";
import { WarningOutlined } from "@mui/icons-material";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { useBlockingPrompt } from "@hooks/useBlockingPrompt";
import { DIALOUGE_BOX_MESSAGES } from "@constant/enum";
import { resetPayloadData } from "@app/payloadSlice";
import LoadingComponent from "../components/Common/LoadingComponent";
import { allRoutes } from "./Routes";
import LogoutWarningScreen from "./LogoutWarningScreen.jsx";
const ApplicationRouter = () => {
  const entitesAndActivities = useSelector((state) => state.userManagement.entitiesAndActivities);
  const dispatch = useDispatch();
  let applicationConfig = useSelector((state) => state.applicationConfig);
  // const [sideNavConfig, setSideNavConfig] = useState(null);

  useEffect(() => {
    if ((entitesAndActivities, localConfigServer)) updateSideNavConfig();
  }, [entitesAndActivities]);

  /* Setting Default Dates */
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 8);

  const [sideNavConfig, setSideNavConfig] = useState(localConfigServer);

  const updateSideNavConfig = () => {
    let tempSideConfig = [];
    let index = 1;
    localConfigServer?.accessItems?.map((config) => {
      if (config.isAccessible && config.isSideOption && Object.keys(entitesAndActivities).includes(config.iwaName)) {
        tempSideConfig.push({ ...config, id: index });
        index = index + 1;
      }
    });
    const sideNavItems = Math.floor((window.innerHeight - 130) / 62);
    setSideNavConfig({
      configuration: {
        moreOptions: window.innerHeight >= 800 ? sideNavItems - 2 : sideNavItems - 1,
      },
      data: tempSideConfig,
    });
  };

  const payloadFields = useSelector((state) => state.payload.payloadData);
  const [isCreateRequestFieldFilled, setisCreateRequestFieldFilled] = useState(false);
  const { blockNavigation, isDialogVisible, handleConfirm, handleCancel } = useBlockingPrompt(isCreateRequestFieldFilled);

  useEffect(() => {
    const { RequestType, RequestDesc, RequestPriority } = payloadFields || {};
    setisCreateRequestFieldFilled(!!(RequestType || RequestDesc || RequestPriority));
  }, [payloadFields]);

  function pathRedirection(pathName) {
    if (pathName) {
      blockNavigation(pathName);
    }
  }

  const onClickNavigateNavListItem = (pathName) => {
    pathRedirection(pathName);
  };

  const onClickNavDrawerItem = (pathName) => {
    pathRedirection(pathName);
  };

  const onClickMoreNavDrawerItem = (pathName) => {
    pathRedirection(pathName);
  };

  const handlePayloadData = () => {
    dispatch(resetPayloadData({ data: {} }));
  };

  const handleYes = () => {
    handleConfirm();
    handlePayloadData();
  };

  useEffect(() => {
    if ((entitesAndActivities, localConfigServer)) updateSideNavConfig();
  }, [entitesAndActivities]);

  return (
    <Box sx={{ backgroundColor:  (theme) => theme.palette.background.default }}>
      <AppHeader />
      {sideNavConfig && entitesAndActivities && (
        <SideNavBar
          entitesAndActivities={entitesAndActivities}
          sideNavOptions={sideNavConfig}
          onClickNavigateNavListItem={onClickNavigateNavListItem}
          onClickNavDrawerItem={onClickNavDrawerItem}
          onClickMoreNavDrawerItem={onClickMoreNavDrawerItem}
        />
      )}
      <Box
        id={"app_Container"}
        sx={(theme) => ({
          // display: "flex",
          flexGrow: 1,
          marginTop: "63px",
          marginLeft: "90px",
          height: "calc(100vh - 63px)",
          backgroundColor:(theme) => theme.palette.background.default,
          boxSizing: "border-box",
          paddingBottom: "64px",
          overflowY: "auto",
          border: "1px solidrgb(19, 17, 17)",
        })}
      >
        {applicationConfig?.logoutUserWarning && <LogoutWarningScreen />}
        <Suspense fallback={<LoadingComponent />}>
          <Routes>
             
            {allRoutes}
             

          </Routes>
        </Suspense>
      </Box>

      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: "orange", fontSize: "20px" }} />} Title={"Warning"} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              No
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              Yes
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </Box>
  );
};

export default memo(ApplicationRouter);
