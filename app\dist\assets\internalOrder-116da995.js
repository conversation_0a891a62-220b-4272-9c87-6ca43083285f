import{a as Ee,g as Qe,s as Pe,r as o,j as e,c as u,an as me,ai as tt,aj as nt,d as y,a6 as ve,ak as rt,al as at,O as g,am as lt,aq as we,ag as Me,ar as $e,as as Be,a8 as be,ah as ot,N as Ze,aZ as st,Q as Xe,b as it,aP as Ke,n as je,U as ct,V as dt,W as ut,i as ht,X as d,Y as Ne,$ as ze,a4 as Ct,a5 as pt,a7 as Ue,a9 as ft,ab as Ye,ac as gt,ad as mt,at as q,k as At,A as Et,w as et,c5 as Ae,C as ke,bj as Fe,Z as It,a0 as Dt,x as St,ae as _t,o as ye,P as Ot,S as We,af as Mt,bK as Ve,J as He,aO as Te,aD as Nt,av as yt,ay as Tt,aA as xt,aB as Rt,aC as bt,T as xe,B as j,aN as kt,aJ as qe,aK as Ft,aL as Pt,aM as vt,F as Lt}from"./index-226a1e75.js";import{R as Gt}from"./ReusablePresetFilter-da63464b.js";import{L as wt}from"./LargeDropdown-b7ffdbd5.js";import{d as $t}from"./History-09ae589c.js";const Bt=({buttonConfigs:m=[],showDeleteDialog:z=!0,deleteDialogContent:T=null,onDeleteProceed:J=()=>{},enableExportSearch:R=!1,exportSearchComponent:O=null})=>{var $,W;const{t:A}=Ee();Qe(),Pe();const[P,D]=o.useState(0),[M,S]=o.useState(!1);o.useState(!1),o.useState(!1);const[L,v]=o.useState(!1),[G,U]=o.useState(0),E=o.useRef(null),Q=["Main","Option 1","Option 2"],[ee,w]=o.useState(!1),[b,te]=o.useState(0),Y=o.useRef(null),ne=["Change","Change A","Change B"],k=()=>S(!1);return e(Me,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:u(ot,{showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center",gap:1},value:P,onChange:(p,N)=>D(N),children:[m.filter(p=>p.condition).map(p=>e(me,{id:`btn-${p.key}`,className:`createRequestButtonAll_${p.key}`,size:"small",variant:"contained",onClick:p.onClick,sx:{mr:1},children:p.label},p.key)),u(tt,{open:M,onClose:k,children:[u(nt,{children:[e(y,{variant:"h6",children:A("Inputs")}),e(ve,{onClick:k,children:e(rt,{})})]}),e(at,{dividers:!0,children:e(g,{container:!0,spacing:1})}),u(lt,{children:[e(me,{onClick:k,children:A("Cancel")}),e(me,{variant:"contained",children:A("Proceed")})]})]}),e(we,{open:L,anchorEl:E.current,placement:"top-end",sx:{zIndex:1},children:e(Me,{style:{width:($=E.current)==null?void 0:$.clientWidth},children:e($e,{onClickAway:()=>v(!1),children:e(Be,{autoFocusItem:!0,children:Q.slice(1).map((p,N)=>e(be,{selected:N===G-1,onClick:()=>{U(N+1),v(!1)},children:p},p))})})})}),e(we,{open:ee,anchorEl:Y.current,placement:"top-end",sx:{zIndex:1},children:e(Me,{style:{width:(W=Y.current)==null?void 0:W.clientWidth},children:e($e,{onClickAway:()=>w(!1),children:e(Be,{autoFocusItem:!0,children:ne.slice(1).map((p,N)=>e(be,{selected:N===b-1,onClick:()=>{te(N+1),w(!1)},children:p},p))})})})})]})})},zt=({dialogs:m,snackbar:z,searchParamsSection:T,tableSection:J,bottomNav:R,exportSearch:O,backDrop:A,toastContainer:P,...D})=>{const{t:M}=Ee(),[S,L]=o.useState(!0),v=o.useRef(null);return u("div",{ref:v,children:[e("div",{style:{...Ze,backgroundColor:"#FAFCFF"},children:u(st,{spacing:1,children:[e(g,{container:!0,mt:0,sx:Xe,children:u(g,{item:!0,md:5,children:[e(y,{variant:"h3",children:e("strong",{children:M(D==null?void 0:D.heading)})}),e(y,{variant:"body2",color:"#777",children:M(D==null?void 0:D.description)})]})}),S&&T,e(g,{item:!0,sx:{position:"relative"},children:J}),e(g,{item:!0,children:e(Bt,{buttonConfigs:D==null?void 0:D.buttonConfigs})})]})}),O,A,P]})},Ut=q(At,{target:"e1b149hy5"})(({theme:m})=>({marginTop:"0px !important",border:`1px solid ${m.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),Yt=q(Et,{target:"e1b149hy4"})(({theme:m})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:m.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${m.palette.primary.light}20`}}),""),Wt=q(g,{target:"e1b149hy3"})({name:"seull4",styles:"padding:0.75rem;gap:0.5rem"}),Vt=q(g,{target:"e1b149hy2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),Je=q(me,{target:"e1b149hy1"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),Re=q(y,{target:"e1b149hy0"})(({theme:m})=>({fontSize:"0.75rem",color:m.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}),""),Ht=({searchParameters:m,onSearch:z,onClear:T,filterFieldData:J,setFilterFieldData:R,items:O})=>{var r,ae,le,oe,se,ie,ce,de,ue,B,he,Ce,pe,fe,ge;const A=Pe(),P=it(),{customError:D}=Ke(),{t:M}=Ee(),[S,L]=o.useState([]),[v,G]=o.useState([]),[U,E]=o.useState({}),[Q,ee]=o.useState({}),[w,b]=o.useState(""),[te,Y]=o.useState([]),[ne,k]=o.useState(!1),[$,W]=o.useState(null),p=je(a=>a.commonFilter.internalOrder),N=48,Z=8,Ie={PaperProps:{style:{maxHeight:N*4.5+Z,width:250}}},De=()=>{var a;A(et({module:(a=Ae)==null?void 0:a.IO})),R(h=>{const C={...h};return Object.keys(C).forEach(_=>{C[_]={code:"",desc:""}}),C}),L([]),T()},re=(a,h)=>{var _;E(f=>({...f,[a]:h}));let C={...p,[a]:h==null?void 0:h.map(f=>f==null?void 0:f.code).join("$^$")};A(St({module:(_=Ae)==null?void 0:_.IO,filterData:C}))},X=(a,h,C,_)=>{k(t=>({...t,[a]:!0}));const f=t=>{var l;k(i=>({...i,[a]:!1})),G(i=>({...i,[a]:t==null?void 0:t.body})),Y((l=t==null?void 0:t.body)==null?void 0:l.list)},x=t=>{k(l=>({...l,[a]:!1}))};ke(`/${Fe}/api/v1/lookup/${h}?${C}=${_}`,"get",f,x)},Se=(a,h)=>{const C=a.target.value;b(C);const _=F.find(t=>t.key===h),{endpoint:f,parameter:x="search"}=_;if($&&clearTimeout($),C.length>=4){const t=setTimeout(()=>{X(h,f,x,C)},200);W(t)}},_e=(a,h)=>{const C=f=>{G(x=>({...x,[h]:f==null?void 0:f.body}))},_=()=>{D(_t.ERROR_FETCHING_DATA)};ke(`/${Fe}/api/v1/lookup/${a}`,"get",C,_)},V=(a,h)=>{var t,l;const C=a==null?void 0:a.MDG_MAT_JSON_FIELD_NAME,_=M(a==null?void 0:a.MDG_MAT_UI_FIELD_NAME),f={matGroup:v[C],selectedMaterialGroup:U[C]||[],setSelectedMaterialGroup:i=>re(C,i),placeholder:`SELECT ${_}`,isDropDownLoading:Q[C],minWidth:"90%",onInputChange:i=>Se(i,C)},x=C===d.ORDER||C===d.ORDERNAME;return u(g,{item:!0,md:2,children:[u(Re,{sx:Ne,children:[_,e("span",{style:{color:(l=(t=It)==null?void 0:t.error)==null?void 0:l.dark},children:"*"})]}),e(ze,{size:"small",fullWidth:!0,children:x?e(Dt,{...f}):e(wt,{...f})})]},h)},F=[{key:(r=d)==null?void 0:r.CONTROAREA,endpoint:"controlling-areas"},{key:(ae=d)==null?void 0:ae.ORDERTYPE,endpoint:"order-types"},{key:(le=d)==null?void 0:le.COMPANYCODEIO,endpoint:"company-codes"},{key:(oe=d)==null?void 0:oe.CCIO,endpoint:"cost-centers"},{key:(se=d)==null?void 0:se.CURRENCYIO,endpoint:"currencies"},{key:(ie=d)==null?void 0:ie.PLANTIO,endpoint:"plants"},{key:(ce=d)==null?void 0:ce.FUNAREA,endpoint:"functional-areas"},{key:(de=d)==null?void 0:de.PROFITCEN,endpoint:"profit-centers"},{key:(ue=d)==null?void 0:ue.GLACCIO,endpoint:"gl-accounts"},{key:(B=d)==null?void 0:B.WBSELE,endpoint:"wbs-elements"},{key:(he=d)==null?void 0:he.RESCC,endpoint:"responsible-cost-centers"},{key:(Ce=d)==null?void 0:Ce.REQCC,endpoint:"requesting-cost-centers"},{key:(pe=d)==null?void 0:pe.OBJCLASS,endpoint:"object-classes"},{key:(fe=d)==null?void 0:fe.ORDER,endpoint:"getInternalOrder",parameter:"internalOrder"},{key:(ge=d)==null?void 0:ge.ORDERNAME,endpoint:"description",parameter:"description"}];return o.useEffect(()=>{F.forEach(({key:a,endpoint:h})=>{_e(h,a)})},[]),e(g,{container:!0,sx:ct,children:e(g,{item:!0,md:12,children:u(Ut,{defaultExpanded:!1,children:[u(Yt,{expandIcon:e(dt,{sx:{fontSize:"1.25rem",color:P.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterIO",children:[e(ut,{sx:{fontSize:"1.25rem",marginRight:1,color:P.palette.primary.dark}}),e(y,{sx:{fontSize:"0.875rem",fontWeight:600,color:P.palette.primary.dark},children:M("Filter Internal Order")})]}),u(ht,{sx:{padding:"1rem 1rem 0.5rem"},children:[u(Wt,{container:!0,children:[u(g,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[m==null?void 0:m.filter(a=>a.MDG_MAT_VISIBILITY!=="Hidden").sort((a,h)=>a.MDG_MAT_SEQUENCE_NO-h.MDG_MAT_SEQUENCE_NO).map((a,h)=>{const C=a==null?void 0:a.MDG_MAT_JSON_FIELD_NAME;return[d.OBJCLASS,d.REQCC,d.RESCC,d.WBSELE,d.GLACCIO,d.PROFITCEN,d.FUNAREA,d.PLANTIO,d.CURRENCYIO,d.CCIO,d.COMPANYCODEIO,d.ORDERNAME,d.ORDER,d.ORDERTYPE,d.CONTROAREA].includes(C)?V(a,h):null}),u(g,{item:!0,md:2,children:[e(Re,{sx:Ne,children:M("Add New Filters")}),e(ze,{sx:{width:"100%"},children:e(Ct,{sx:{font_Small:Ne,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:S,onChange:a=>L(a.target.value),renderValue:a=>a.join(", "),MenuProps:Ie,endAdornment:S.length>0&&e(pt,{position:"end",sx:{marginRight:"10px"},children:e(ve,{size:"small",onClick:()=>L([]),"aria-label":"Clear selections",children:e(Ue,{})})}),children:O==null?void 0:O.map(a=>u(be,{value:a.title,children:[e(ft,{checked:S.indexOf(a.title)>-1}),a.title]},a.title))})})]})]}),e(g,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:S.map((a,h)=>e(g,{item:!0,md:2,children:e(Re,{sx:{fontSize:"12px"},children:M(a)})},h))})]}),u(Vt,{children:[e(Je,{variant:"outlined",size:"small",startIcon:e(Ue,{sx:{fontSize:"1rem"}}),onClick:De,children:M("Clear")}),e(g,{sx:{...Ye},children:e(Gt,{moduleName:"InternalOrder",handleSearch:z})}),e(Je,{variant:"contained",size:"small",startIcon:e(gt,{sx:{fontSize:"1rem"}}),sx:{...mt,...Ye},onClick:z,children:M("Search")})]})]})]})})})},Xt=()=>{var x;const m=Qe();Ke(),ye();const{getDtCall:z,dtData:T}=ye(),{getDtCall:J,dtData:R}=ye(),O=Pe(),{t:A}=Ee(),[P,D]=o.useState(!1),[M,S]=o.useState(!1),[L,v]=o.useState(!1),[G,U]=o.useState(!1);o.useState(null);const[E,Q]=o.useState([]),[ee,w]=o.useState([...E]),[b,te]=o.useState((x=Ot)==null?void 0:x.TOP_SKIP),[Y,ne]=o.useState({});o.useState([]);const[k,$]=o.useState([]),[W,p]=o.useState([]),[N,Z]=o.useState(!1),[Ie,De]=o.useState(),[re,X]=o.useState(0),[Se,_e]=o.useState(0),[V,F]=o.useState(0),r=je(t=>{var l;return t.commonFilter[(l=Ae)==null?void 0:l.IO]}),ae=(t,l)=>{v(!0)},le=()=>{var l;let t={decisionTableId:null,decisionTableName:He.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(l=Te)==null?void 0:l.IO,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};z(t)},oe=()=>{var l;let t={decisionTableId:null,decisionTableName:He.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":(l=Te)==null?void 0:l.IO,"MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};J(t)},se=(t,l)=>({field:t,headerName:A(l),editable:!1,flex:1,renderCell:i=>{const n=i.value?i.value.split(",").map(I=>I.trim()):[],c=n.length-1;if(n.length===0)return"-";const s=I=>{const[H,...K]=I.split("-");return u(Lt,{children:[e("strong",{children:H}),K.length?` - ${K.join("-")}`:""]})};return u(j,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[e(xe,{title:n[0],placement:"top",arrow:!0,children:e(y,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:s(n[0])})}),c>0&&e(j,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:e(xe,{arrow:!0,placement:"right",title:u(j,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[u(y,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",l,"s (",c,")"]}),n.slice(1).map((I,H)=>e(y,{variant:"body2",sx:{mb:.5},children:s(I)},H))]}),children:u(j,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[e(kt,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),u(y,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",c]})]})})})]})}}),ie=(t,l)=>({field:t,headerName:A(l),editable:!1,flex:1,renderCell:i=>{var s;const[n,...c]=((s=i.value)==null?void 0:s.split(" - "))||[];return u("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[e("strong",{children:n})," ",c.length?`- ${c.join(" - ")}`:""]})}}),ce=()=>({field:"dataValidation",headerName:A("Audit History"),editable:!1,flex:1,renderCell:t=>e(j,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(xe,{title:"View Audit Log",placement:"top",children:e(ve,{onClick:i=>{var n,c;i.stopPropagation(),m((n=Ve)==null?void 0:n.AUDIT_LOG,{state:{materialNumber:t.row.Order,module:(c=Te)==null?void 0:c.IO}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:e($t,{sx:{fontSize:"1.5rem"}})})})})}),de=t=>{const l=[];let i=(t==null?void 0:t.sort((n,c)=>n.MDG_MAT_SEQUENCE_NO-c.MDG_MAT_SEQUENCE_NO))||[];return i&&(i==null||i.forEach(n=>{if((n==null?void 0:n.MDG_MAT_VISIBILITY)===Nt.DISPLAY&&n!=null&&n.MDG_MAT_UI_FIELD_NAME){const c=n.MDG_MAT_JSON_FIELD_NAME,s=n.MDG_MAT_UI_FIELD_NAME;c==="DataValidation"?l.push(ce()):n.MDG_MAT_FIELD_TYPE==="Multiple"?l.push(se(c,s)):n.MDG_MAT_FIELD_TYPE==="Single"&&l.push(ie(c,s))}})),l},ue=t=>{if(!t){X(Se),F(0),w([...E]);return}const l=E.filter(i=>{var s;let n=!1,c=Object.keys(i);for(let I=0;I<c.length&&(n=i[c[I]]?(i==null?void 0:i[c==null?void 0:c[I]])&&((s=i==null?void 0:i[c==null?void 0:c[I]].toString().toLowerCase())==null?void 0:s.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!n);I++);return n});w([...l]),X(l==null?void 0:l.length)},B=()=>{F(0),S(!0),Z(!1);let t={internalOrders:(r==null?void 0:r.Order)||"",orderType:(r==null?void 0:r.OrderType)||"",wbSelm:(r==null?void 0:r.WbsElement)||"",glAccount:(r==null?void 0:r.GLAccount)||"",profitCenter:(r==null?void 0:r.ProfitCtr)||"",plant:(r==null?void 0:r.Plant)||"",funcArea:(r==null?void 0:r.FuncArea)||"",orderName:(r==null?void 0:r.OrderName)||"",Objectclass:(r==null?void 0:r.Objectclass)||"",controllingArea:(r==null?void 0:r.CoArea)||"TZUS",reqCostCenter:(r==null?void 0:r.RequestCctr)||"",respCostCenter:(r==null?void 0:r.Respcctr)||"",currency:(r==null?void 0:r.Currency)||"",costCenter:(r==null?void 0:r.CostCenter)||"",companyCode:(r==null?void 0:r.CompCode)||"",top:b,skip:0,fetchCount:!1};const l=n=>{var H,K,Le,Ge;if((n==null?void 0:n.statusCode)===qe.STATUS_200){var c=[];for(let Oe=0;Oe<((K=(H=n==null?void 0:n.body)==null?void 0:H.list)==null?void 0:K.length);Oe++){var s=(Le=n==null?void 0:n.body)==null?void 0:Le.list[Oe],I={id:Ft(),OrderType:s.OrderType||"Not Available",Costcenter:s.CostCtr!==""?`${s.CostCtr}`:"Not Available",WbsElement:s.WBSelm!==""?`${s.WBSelm}`:"Not Available",CoArea:s.CoArea!==""?`${s.CoArea}`:"Not Available",GlAccount:s.GLAccount!==""?`${s.GLAccount}`:"Not Available",OrderName:s.OrderName!==""?`${s.OrderName} - ${s.OrderName}`:"-",Objectclass:s.ObjClass!==""?`${s.ObjClass}`:"Not Available",FuncArea:s.FuncArea!==""?`${s.FuncArea}`:"Not Available",Order:s.InternalOrder!==""?`${s.InternalOrder}`:"Not Available",CompCode:s.CompCode!==""?`${s.CompCode}`:"Not Available",Plant:s.Plant!==""?`${s.Plant}`:"Not Available",ProfitCtr:s.ProfitCtr!==""?`${s.ProfitCtr}`:"Not Available",RequestCctr:s.ReqCostCtr!==""?`${s.ReqCostCtr}`:"Not Available",Respcctr:s.RespCostCtr!==""?`${s.RespCostCtr}`:"Not Available",Currency:s.Currency!==""?`${s.Currency}`:"Not Available"};c.push(I)}Q(c.reverse()),S(!1),X((Ge=n.body)==null?void 0:Ge.count),_e(n.count),O(Pt({module:"Internal Order"}))}else(n==null?void 0:n.statusCode)===qe.STATUS_414&&(vt(n==null?void 0:n.message,"error"),S(!1))},i=()=>{S(!1)};ke(`/${Fe}/api/v1/lookup/getInternalOrdersBasedOnAdditionalParams`,"post",l,i,t)},he=()=>{S(!0)},Ce=t=>{t.map(l=>E.find(i=>i.id===l))},pe=t=>{const l=t.target.value;te(l),F(0)},fe=(t,l)=>{F(isNaN(l)?0:l)},ge=()=>{Z(!0),h()},a=()=>{Z(!0),F(0)},h=()=>{F(0),S(!0)},C=()=>{B()},_=()=>{O(yt()),O(Tt()),O(xt([])),O(Rt({})),O(bt({}))},f=[{key:"createRequest",label:A("Create Request"),condition:!0,onClick:()=>{m("/masterDataCockpit/internalOrder/createRequestforInternalOrder")}}];return o.useEffect(()=>{G&&(B(),U(!1))},[G]),o.useEffect(()=>(_(),ae(),()=>{var t;O(et({module:(t=Ae)==null?void 0:t.IO,days:7}))}),[]),o.useEffect(()=>w([...E]),[E]),o.useEffect(()=>{var t,l,i,n;if(T){const c=de((l=(t=T==null?void 0:T.result)==null?void 0:t[0])==null?void 0:l.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);$(c)}if(R){const c=(n=(i=R==null?void 0:R.result)==null?void 0:i[0])==null?void 0:n.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,s=c==null?void 0:c.filter(I=>I.MDG_MAT_FILTER_TYPE==="Additional").map(I=>({title:A(I.MDG_MAT_UI_FIELD_NAME)}));p(c),De(s)}},[T,R]),o.useEffect(()=>{B()},[b]),o.useEffect(()=>{le(),oe()},[]),o.useEffect(()=>{N||V!=0&&V*b>=(E==null?void 0:E.length)&&he()},[V,b]),e("div",{style:{...Ze,backgroundColor:"#FAFCFF"},children:u(We,{spacing:1,children:[u(g,{container:!0,mt:0,sx:Xe,children:[u(g,{item:!0,md:5,children:[e(y,{variant:"h3",children:e("strong",{children:A("Internal Order")})}),e(y,{variant:"body2",color:"#777",children:A("This view displays the list of Internal Orders")})]}),e(Ht,{searchParameters:W,onSearch:B,onClear:()=>U(!0),filterFieldData:Y,setFilterFieldData:ne,items:Ie})]}),e(g,{item:!0,sx:{position:"relative"},children:e(We,{children:e(Mt,{isLoading:M,paginationLoading:M,module:"Internal Order",width:"100%",title:A("List of Internal Order")+" ("+re+")",rows:ee??[],columns:k??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:t=>ue(t),onRefresh:C,pageSize:b,page:V,onPageSizeChange:pe,rowCount:re??(E==null?void 0:E.length)??0,onPageChange:fe,getRowIdValue:"id",hideFooter:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:Ce,callback_onRowSingleClick:t=>{var i,n,c;const l=t.row.Order;(n=(i=t==null?void 0:t.row)==null?void 0:i.materialType)==null||n.split(" - ")[0],D(!0),m(`${(c=Ve)==null?void 0:c.DISPLAY_IO_SAP_VIEW}/${l}`,{state:t.row})},showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0,showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:ge,onSelectFirstPageOptions:a})})}),e(zt,{buttonConfigs:f})]})})};export{Xt as default};
