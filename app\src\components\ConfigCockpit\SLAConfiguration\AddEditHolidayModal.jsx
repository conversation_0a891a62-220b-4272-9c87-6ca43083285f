import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  DatePicker,
  Switch,
  Select,
  Row,
  Col,
  Typography,
  Card,
  Space
} from 'antd';
import {
  CalendarOutlined,
  EditOutlined,
  PlusOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useTheme } from '@mui/material';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const AddEditHolidayModal = ({ open, onClose, onSubmit, editingHoliday, regionOptions }) => {
  const [form] = Form.useForm();
  const isEdit = !!editingHoliday;
  const theme = useTheme()

  const typeOptions = [
    { 
      value: 'National Holiday', 
      label: 'National Holiday',
      description: 'Official public holidays recognized by the government'
    },
    { 
      value: 'Cultural Holiday', 
      label: 'Cultural Holiday',
      description: 'Cultural or religious holidays observed by specific communities'
    }
  ];

  useEffect(() => {
    if (open) {
      if (isEdit && editingHoliday) {
        form.setFieldsValue({
          date: dayjs(editingHoliday?.date),
          holidayName: editingHoliday?.holidayName,
          description: editingHoliday?.description,
          type: editingHoliday?.type,
          region: editingHoliday?.region,
          status: editingHoliday?.status ?? true,
          id: editingHoliday?.id || null
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          type: 'National Holiday',
          status: true
        });
      }
    }
  }, [open, isEdit, editingHoliday, form]);

  const handleSubmit = () => {
    form.validateFields().then((values) => {
      onSubmit(values);
    }).catch((errorInfo) => {
    });
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  // Disable past dates for new holidays - To be uncommented after confirming the logic
//   const disabledDate = (current) => {
//     if (isEdit) return false; // Allow past dates when editing
//     return current && current < dayjs().startOf('day');
//   };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {isEdit ? <EditOutlined /> : <PlusOutlined />}
          <span>{isEdit ? 'Edit Holiday' : 'Add New Holiday'}</span>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      onOk={handleSubmit}
      okText={isEdit ? 'Update Holiday' : 'Add Holiday'}
      cancelText="Cancel"
      width={600}
      okButtonProps={{
        style: {
          backgroundColor: theme?.palette?.primary?.main,
        }
      }}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          {isEdit 
            ? 'Update the holiday details below'
            : 'Create a new holiday that will affect business hours and SLA calculations'
          }
        </Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: 'National Holiday',
          status: true,
        }}
      >
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Holiday Date"
                name="date"
                rules={[
                  { required: true, message: 'Please select a date' }
                ]}
                tooltip="The date when the holiday occurs"
              >
                <DatePicker
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                //   disabledDate={disabledDate}
                  placeholder="Select holiday date"
                  suffixIcon={<CalendarOutlined />}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Region"
                name="region"
                rules={[
                  { required: true, message: 'Please select a region' }
                ]}
                tooltip="The region where this holiday applies"
              >
                <Select 
                    placeholder="Select region"
                    showSearch
                    optionFilterProp="label"
                    filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                    }
                >
                    {regionOptions.map(option => (
                    <Option key={option.value} value={option.value} label={`${option.value}-${option.label}`}>
                        {option.value} - {option.label}
                    </Option>
                    ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <Card size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            label="Holiday Name"
            name="holidayName"
            rules={[
              { required: true, message: 'Please enter holiday name' },
              { min: 2, message: 'Holiday name must be at least 2 characters' },
              { max: 100, message: 'Holiday name cannot exceed 100 characters' }
            ]}
            tooltip="A short, descriptive name for the holiday"
          >
            <Input 
              placeholder="e.g., Christmas Day, Independence Day, Company Anniversary"
              maxLength={100}
              showCount
            />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[
              { max: 500, message: 'Description cannot exceed 500 characters' }
            ]}
            tooltip="Optional detailed description of the holiday"
          >
            <TextArea
              placeholder="Provide additional details about this holiday (optional)"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Card>

        <Card size="small">
          <Title level={5} style={{ marginBottom: 12 }}>
            <InfoCircleOutlined style={{ marginRight: 8 }} />
            Holiday Settings
          </Title>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Holiday Type"
                name="type"
                rules={[
                  { required: true, message: 'Please select a holiday type' }
                ]}
                tooltip="Choose the type of holiday"
              >
                <Select 
                  placeholder="Select holiday type"
                  optionLabelProp="label"
                >
                  {typeOptions.map(option => (
                    <Option key={option.value} value={option.value} label={option.label}>
                      <Space direction="vertical" size={0}>
                        <Text strong>{option.label}</Text>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {option.description}
                        </Text>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Status"
                name="status"
                valuePropName="checked"
                tooltip="Active holidays will be considered in SLA calculations"
                >
                <Switch 
                    checkedChildren="Active" 
                    unCheckedChildren="Inactive"
                />
                </Form.Item>
            </Col>
          </Row>

          <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
            {({ getFieldValue }) => {
              const type = getFieldValue('type');
              return (
                <div style={{ 
                  padding: 12, 
                  background: '#f5f5f5', 
                  borderRadius: 6, 
                  border: '1px solid #d9d9d9' 
                }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    <InfoCircleOutlined style={{ marginRight: 4 }} />
                    {type === 'National Holiday' 
                      ? 'This is an official public holiday that typically affects all business operations in the selected region.'
                      : 'This is a cultural or religious holiday that may affect specific communities within the selected region.'
                    }
                  </Text>
                </div>
              );
            }}
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};

export default AddEditHolidayModal;