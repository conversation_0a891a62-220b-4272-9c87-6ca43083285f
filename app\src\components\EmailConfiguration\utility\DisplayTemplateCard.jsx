import React, { useState } from 'react';
import Card from "@mui/material/Card";
import CardActionArea from "@mui/material/CardActionArea";
import CardContent from "@mui/material/CardContent";
import Typography from "@mui/material/Typography";
import Grid from "@mui/material/Grid";
import TemplatePreview from '../Email Config/TemplatePreview';
import { Box, Divider, Chip, Tooltip } from '@mui/material';
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import { colors } from '../../../constant/colors';

const DisplayTemplateCard = (props) => {
    const [openTemplateDialog, setOpenTemplateDialog] = useState({});
    const handleCardClick = (key) => {
      setOpenTemplateDialog({ [key]: true });
    };
    
    const handleDialogClose = () => {
      setOpenTemplateDialog({});
    };

    return (
      <>
        <Grid item key={props?.index}>
          <Card
            elevation={0}
            sx={{
              backgroundColor: colors.primary.veryLight,
              borderRadius: "8px",
              border: `1px solid ${colors.border.light}`,
              height: "160px",
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: colors.primary.light,
                borderColor: colors.border.medium
              }
            }}
            onClick={() => handleCardClick(props?.cardData?.emailDefinitionId)}
          >
            <CardActionArea sx={{ height: "100%" }}>
              <CardContent 
                sx={{ 
                  p: 2,
                  height: "100%",
                  display: "flex",
                  flexDirection: "column",
                  backgroundColor: colors.primary.veryLight,
                  justifyContent: "space-between",
                  gap: 1
                }}
              >
                <Box sx={{ flex: '1 1 auto' }}>
                  <Tooltip title={props?.cardData?.name} placement="top">
                    <Typography
                      sx={{
                        fontSize: "14px",
                        color: colors.text.primary,
                        mb: 0.5,
                        fontWeight: 700,
                        display: '-webkit-box',
                        WebkitLineClamp: '1',
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        lineHeight: '1.2'
                      }}
                    >
                      {props?.cardData?.name}
                    </Typography>
                  </Tooltip>
                  <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    mb: 0.5
                  }}>
                    <FiberManualRecordIcon 
                      sx={{ 
                        fontSize: 12, 
                        color: props?.cardData?.status === "Active" ? colors.success.vibrant : colors.warning.amber,
                        mr: 0.5
                      }} 
                    />
                    <Typography
                      sx={{
                        fontSize: "12px",
                        color: props?.cardData?.status === "Active" ? colors.success.vibrant : colors.warning.amber,
                      }}
                    >
                      {props?.cardData?.status}
                    </Typography>
                  </Box>

                  <Divider sx={{ my: 0.5 }} />
                  <Typography
                    sx={{
                      fontSize: "12px",
                      color: colors.text.secondary,
                      mb: 0.5,
                      wordBreak: "break-word"
                    }}
                  >
                    {props?.cardData?.subject || "Approval for Request"}
                  </Typography>
                </Box>
                <Box sx={{ 
                  display: 'flex', 
                  gap: 0.5,
                  flexWrap: 'wrap',
                  flexShrink: 0
                }}>
                  {props?.cardData?.entityDesc && (
                    <Tooltip title={props.cardData.entityDesc} placement="top">
                      <Chip
                        label={props.cardData.entityDesc}
                        size="small"
                        sx={{
                          backgroundColor: colors.primary.light,
                          color: colors.primary.main,
                          fontSize: '10px',
                          height: '20px',
                          maxWidth: '120px',
                          border: `1px solid ${colors.primary.border}`,
                          '& .MuiChip-label': {
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            px: 1
                          },
                          '&:hover': {
                            backgroundColor: colors.primary.ultraLight,
                          }
                        }}
                      />
                    </Tooltip>
                  )}
                  {props?.cardData?.identifierDesc && (
                    <Tooltip title={props.cardData.identifierDesc} placement="top">
                      <Chip
                        label={props.cardData.identifierDesc}
                        size="small"
                        sx={{
                          backgroundColor: colors.secondary.light,
                          color: colors.secondary.green,
                          fontSize: '10px',
                          height: '20px',
                          maxWidth: '120px',
                          border: `1px solid ${colors.secondary.lightGreen}`,
                          '& .MuiChip-label': {
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            px: 1
                          },
                          '&:hover': {
                            backgroundColor: colors.primary.chipBackground,
                          }
                        }}
                      />
                    </Tooltip>
                  )}
                </Box>
              </CardContent>
            </CardActionArea>
          </Card>
        </Grid>

        <TemplatePreview
          open={openTemplateDialog[props?.cardData?.emailDefinitionId]}
          onClose={handleDialogClose}
          setCreationType={props?.setCreationType}
          index={props?.index}
          data={props?.cardData}
          setSelectedRow={props?.setSelectedRow}
          setOpenCreateTemplate={props?.setOpenCreateTemplate}
          setOpenTemplateDialog={setOpenTemplateDialog}
          mailmappingData={props?.mailmappingData}
          groupList={props?.groupList}
          userList={props?.userList}
          allGroups={props?.allGroups}
          headers={props?.headers}
          setScenario={props.setScenario}
          {...props}
        />
      </>
    );
}

export default DisplayTemplateCard;
