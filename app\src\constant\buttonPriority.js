export const CreatebuttonPriority = {
    handleSubmitForApproval: 6,
    handleSendBack: 1,
    handleReject: 3,
    handleValidate: 5,
    handleSAPSyndication: 8,
    handleValidate1:4,
    handleSubmitForReview: 7,
    handleCorrection: 2,
  };

export const EXTEND_BUTTON_PRIORITY = {
  "Approve": 6,
  "Send Back": 3,
  "Save As Draft":1,
  "Reject": 3,
  "Validate": 4,
  "Forward": 6,
  "SAP Syndication": 8,
  "Submit": 7,
  "Correction": 2,
}

export const TABS_NAME_VALUE = {
  REQUEST_HEADER:0,
  REQUEST_DETAILS:1,
  ATTACHMENT_AND_COMMENTS:2,
  PREVIEW:3,
}

export const BUTTONS_ACTION_TYPE = {
  HANDLE_SEND_BACK:'handleSendBack',
  HANDLE_VALIDATE1:'handleValidate1',
  HANDLE_SUBMIT_FOR_APPROVAL:'handleSubmitForApproval',
  HANDLE_VALIDATE:'handleValidate',
  HANDLE_SAP_SYNDICATION:'handleSAPSyndication',
  HANDLE_SUBMIT_FOR_REVIEW:'handleSubmitForReview',
  HANDLE_CORRECTION:'handleCorrection',
  HANDLE_SUBMIT:'handleSubmit',
  HANDLE_DRAFT:'handleDraft',
  HANDLE_ACCEPT:"handleReview",
  HANDLE_REJECT:'handleReject'
}
  export const PREVIEW_BUTTON_FILTER = [
    BUTTONS_ACTION_TYPE.HANDLE_SEND_BACK,
      BUTTONS_ACTION_TYPE.HANDLE_CORRECTION,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,
      BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT,
      BUTTONS_ACTION_TYPE.HANDLE_REJECT,
  ];

 export  const REQUEST_DETAILS_BUTTON_FILTER = [
    BUTTONS_ACTION_TYPE.HANDLE_VALIDATE,
    BUTTONS_ACTION_TYPE.HANDLE_DRAFT
  ]

  export const BUTTONS_TAB_MAP = {
    0:[],
    1 : REQUEST_DETAILS_BUTTON_FILTER,
    2 : [],  
    3 : PREVIEW_BUTTON_FILTER
  }

