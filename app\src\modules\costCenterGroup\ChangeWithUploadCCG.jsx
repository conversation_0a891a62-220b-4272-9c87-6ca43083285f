import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Box, Grid, But<PERSON>, Stepper, Step, StepButton } from "@mui/material";
import moment from "moment";
import { updateDisplayRecords, updateToChangeLog, setNodesListForDBDuplicateCheck, setDescListForDBDuplicateCheck } from "@app/hierarchyDataSlice";
import { container_Padding } from "@components/Common/commonStyles";
import ReusableTable from "@components/Common/ReusableTable";
import { useRequestDetailsCCG } from "@costCenterGroup/hooks/useRequestDetailsCCG";
import { getColumnsAddPC, getColumnsChangePR, getColumnsDeleteNode, getColumnsDescChange, getColumnsMoveNode, getColumnsMovePC, getColumnsNewNodes, getColumnsRemovePC } from "@costCenterGroup/TableColumnsCCG";
import { CHANGE_LOG_TYPES, generateChangeLogId, generateChangeDescription } from "@costCenterGroup/hooks/changeLogUtilsCCG";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { ENABLE_STATUSES } from "@constant/enum";
import { useLocation } from "react-router-dom";
import { useSnackbar } from "@hooks/useSnackbar";

const stepsTable = [
  { label: "New Nodes", value: "1", key: "NEW NODES" },
  { label: "Description Change", value: "2", key: "DESCRIPTIONS" },
  { label: "Add Cost Centers", value: "3", key: "COST CENTERS" },
  { label: "Move Node", value: "4", key: "MOVE NODE" },
  { label: "Move Cost Center", value: "5", key: "MOVE COST CENTER" },
  { label: "Remove Cost Center", value: "6", key: "REMOVE COST CENTER" },
  { label: "Delete Node", value: "7", key: "DELETE NODE" },
];

const ChangeWithUploadCCG = () => {
  const dispatch = useDispatch();
  const [activeStepTable, setActiveStepTable] = useState(0);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(100);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const changeWithUploadRecords = useSelector((state) => state.hierarchyData.DisplayRecords);
  const nodesListForDBDuplicateCheck = useSelector((state) => state.hierarchyData.nodesListForDBDuplicateCheck || []);
  const descListForDBDuplicateCheck = useSelector((state) => state.hierarchyData.descListForDBDuplicateCheck || []);
  const userData = useSelector((state) => state.userManagement.userData);
  const { fetchOldDescriptionForNode, fetchOldParentForNode, fetchOldParentForObject } = useRequestDetailsCCG();
 const requestStatus = useSelector(
    (state) => state.hierarchyData.requestHeaderData?.RequestStatus
  );
  const { showSnackbar } = useSnackbar();
  const disableCheck = reqBench && !ENABLE_STATUSES.includes(requestStatus);
  useEffect(() => { initializeDuplicateListsFromExistingData(); }, []);

  const initializeDuplicateListsFromExistingData = () => {
    const existingNodes = new Set();
    const existingDescs = new Set();
    const newNodesData = changeWithUploadRecords?.["NEW NODES"] || [];
    newNodesData.forEach((row) => {
      if (row["New Node"]?.trim()) existingNodes.add(row["New Node"].trim().toUpperCase());
      if (row["Description"]?.trim()) existingDescs.add(row["Description"].trim().toUpperCase());
    });
    const descriptionsData = changeWithUploadRecords?.["DESCRIPTIONS"] || [];
    descriptionsData.forEach((row) => {
      if (row["New Description"]?.trim()) existingDescs.add(row["New Description"].trim().toUpperCase());
    });
    const nodesList = Array.from(existingNodes);
    const descsList = Array.from(existingDescs);
    if (nodesList.length > 0 || nodesListForDBDuplicateCheck.length === 0) dispatch(setNodesListForDBDuplicateCheck(nodesList));
    if (descsList.length > 0 || descListForDBDuplicateCheck.length === 0) dispatch(setDescListForDBDuplicateCheck(descsList));
  };

  const updateNodesListForDuplicateCheck = (nodeValue, action = "add") => {
    if (!nodeValue?.trim()) return;
    const trimmedNode = nodeValue.trim().toUpperCase();
    let updatedNodesList = [...nodesListForDBDuplicateCheck];
    if (action === "add" && !updatedNodesList.some((node) => node.toUpperCase() === trimmedNode)) {
      updatedNodesList.push(trimmedNode);
    } else if (action === "remove") {
      updatedNodesList = updatedNodesList.filter((node) => node.toUpperCase() !== trimmedNode);
    } else if (action === "replace") {
      const oldNode = nodeValue.old?.trim().toUpperCase();
      const newNode = nodeValue.new?.trim().toUpperCase();
      if (oldNode && newNode && oldNode !== newNode) {
        updatedNodesList = updatedNodesList.filter((node) => node.toUpperCase() !== oldNode);
        if (!updatedNodesList.some((node) => node.toUpperCase() === newNode)) updatedNodesList.push(newNode);
      }
    }
    dispatch(setNodesListForDBDuplicateCheck(updatedNodesList));
  };

  const updateDescListForDuplicateCheck = (descValue, action = "add") => {
    if (!descValue?.trim()) return;
    const trimmedDesc = descValue.trim().toUpperCase();
    let updatedDescList = [...descListForDBDuplicateCheck];
    if (action === "add" && !updatedDescList.some((desc) => desc.toUpperCase() === trimmedDesc)) {
      updatedDescList.push(trimmedDesc);
    } else if (action === "remove") {
      updatedDescList = updatedDescList.filter((desc) => desc.toUpperCase() !== trimmedDesc);
    } else if (action === "replace") {
      const oldDesc = descValue.old?.trim().toUpperCase();
      const newDesc = descValue.new?.trim().toUpperCase();
      if (oldDesc && newDesc && oldDesc !== newDesc) {
        updatedDescList = updatedDescList.filter((desc) => desc.toUpperCase() !== oldDesc);
        if (!updatedDescList.some((desc) => desc.toUpperCase() === newDesc)) updatedDescList.push(newDesc);
      }
    }
    dispatch(setDescListForDBDuplicateCheck(updatedDescList));
  };

  const batchUpdateDuplicateLists = (operations) => {
    let updatedNodesList = [...nodesListForDBDuplicateCheck];
    let updatedDescList = [...descListForDBDuplicateCheck];
    operations.forEach((operation) => {
      const { type, value, action } = operation;
      if (type === "node" && value?.trim()) {
        const trimmedValue = value.trim().toUpperCase();
        if (action === "add" && !updatedNodesList.some((node) => node.toUpperCase() === trimmedValue)) {
          updatedNodesList.push(trimmedValue);
        } else if (action === "remove") {
          updatedNodesList = updatedNodesList.filter((node) => node.toUpperCase() !== trimmedValue);
        }
      } else if (type === "desc" && value?.trim()) {
        const trimmedValue = value.trim().toUpperCase();
        if (action === "add" && !updatedDescList.some((desc) => desc.toUpperCase() === trimmedValue)) {
          updatedDescList.push(trimmedValue);
        } else if (action === "remove") {
          updatedDescList = updatedDescList.filter((desc) => desc.toUpperCase() !== trimmedValue);
        }
      }
    });
    dispatch(setNodesListForDBDuplicateCheck(updatedNodesList));
    dispatch(setDescListForDBDuplicateCheck(updatedDescList));
  };

  const checkForDuplicates = (value, type) => {
    if (!value?.trim()) return false;
    const trimmedValue = value.trim().toUpperCase();
    if (type === "node") return nodesListForDBDuplicateCheck.some((node) => node.toUpperCase() === trimmedValue);
    if (type === "desc") return descListForDBDuplicateCheck.some((desc) => desc.toUpperCase() === trimmedValue);
    return false;
  };

  const addToChangeLog = (type, description, additionalData = {}) => {
    const newLogEntry = {
      id: generateChangeLogId(),
      type,
      description,
      updatedBy: userData?.emailId || "<EMAIL>",
      updatedOn: moment().utc().format("YYYY-MM-DDTHH:mm:ss[Z]"),
      ...additionalData,
    };
    dispatch(updateToChangeLog(newLogEntry));
  };

  const updateReduxRecords = (tableName, newData, changeType = null, rowData = null, oldRowData = null) => {
    dispatch(updateDisplayRecords({ ...changeWithUploadRecords, [tableName]: newData }));
    if (changeType && rowData) {
      const description = generateChangeDescription(changeType, rowData, oldRowData);
      addToChangeLog(changeType, description, { tableName, rowData: { ...rowData }, oldRowData: oldRowData ? { ...oldRowData } : null });
    }
  };

  const generateNewId = (rows) => String((rows.length > 0 ? Math.max(...rows.map((row) => parseInt(row.Id))) : 0) + 1);

  const handlePageChange = (event, newPage) => setPage(newPage);
  const handlePageSizeChange = (event) => { setPageSize(event.target.value); setPage(0); };

  const handleAddRow = () => {
    const currentStep = stepsTable[activeStepTable];
    const currentRecords = changeWithUploadRecords?.[currentStep.key] || [];
    let newRow = {
      Id: generateNewId(currentRecords),
      "Updated By": userData?.emailId,
      "Updated On": moment().utc().format("YYYY-MM-DD HH:mm:ss.SSS"),
    };
    switch (activeStepTable) {
      case 0: newRow = { ...newRow, "Parent Node": "", "New Node": "", Description: "", "Person Responsible": "" }; break;
      case 1: newRow = { ...newRow, "Parent Node": "", "Old Description": "", "New Description": "" }; break;
      case 2: newRow = { ...newRow, Node: "", "Cost Center": "" }; break;
      case 3: newRow = { ...newRow, "Old Parent Node": "", "New Parent Node": "", "Selected Node": "" }; break;
      case 4: newRow = { ...newRow, "Old Parent Node": "", "New Parent Node": "", "Selected Cost Center": "" }; break;
      case 5: newRow = { ...newRow, "Parent Node": "", "Selected Cost Center": "" }; break;
      case 6: newRow = { ...newRow, "Parent Node": "", "Deleted Node": "" }; break;
      case 7: newRow = { ...newRow, "Parent Node": "", "Old Person Responsible": "", "New Person Responsible": "" }; break;
      default: break;
    }
    const updatedRecords = [...currentRecords, newRow];
    updateReduxRecords(currentStep.key, updatedRecords);
    addToChangeLog("ROW_CREATED", `New ${currentStep.label.toLowerCase()} row created`, { stepLabel: currentStep.label, rowId: newRow.Id });
  };

  const handleDeleteRow = (rowId, stepIndex) => {
    const currentStep = stepsTable[stepIndex];
    const currentRecords = changeWithUploadRecords?.[currentStep.key] || [];
    const rowToDelete = currentRecords.find((row) => row.Id === rowId);
    const updatedRecords = currentRecords.filter((row) => row.Id !== rowId);
    updateReduxRecords(currentStep.key, updatedRecords);
    if (rowToDelete) {
      const batchOperations = [];
      switch (stepIndex) {
        case 0:
          if (rowToDelete["New Node"]) batchOperations.push({ type: "node", value: rowToDelete["New Node"], action: "remove" });
          if (rowToDelete["Description"]) batchOperations.push({ type: "desc", value: rowToDelete["Description"], action: "remove" });
          break;
        case 1:
          if (rowToDelete["New Description"]) batchOperations.push({ type: "desc", value: rowToDelete["New Description"], action: "remove" });
          break;
      }
      if (batchOperations.length > 0) batchUpdateDuplicateLists(batchOperations);
      let deleteDescription = `Row deleted from ${currentStep.label}`;
      switch (stepIndex) {
        case 0: if (rowToDelete["New Node"] && rowToDelete["Parent Node"]) deleteDescription = `Deleted new node "${rowToDelete["New Node"]}" under "${rowToDelete["Parent Node"]}"`; break;
        case 1: if (rowToDelete["Parent Node"]) deleteDescription = `Cancelled description change for node "${rowToDelete["Parent Node"]}"`; break;
        case 2: if (rowToDelete["Cost Center"] && rowToDelete["Node"]) deleteDescription = `Cancelled adding cost center "${rowToDelete["Cost Center"]}" to node "${rowToDelete["Node"]}"`; break;
        case 3: if (rowToDelete["Selected Node"]) deleteDescription = `Cancelled move operation for node "${rowToDelete["Selected Node"]}"`; break;
        case 4: if (rowToDelete["Selected Cost Center"]) deleteDescription = `Cancelled move operation for cost center "${rowToDelete["Selected Cost Center"]}"`; break;
        case 5: if (rowToDelete["Selected Cost Center"] && rowToDelete["Parent Node"]) deleteDescription = `Cancelled removal of cost center "${rowToDelete["Selected Cost Center"]}" from "${rowToDelete["Parent Node"]}"`; break;
        case 6: if (rowToDelete["Deleted Node"] && rowToDelete["Parent Node"]) deleteDescription = `Cancelled deletion of node "${rowToDelete["Deleted Node"]}" from "${rowToDelete["Parent Node"]}"`; break;
        case 7: if (rowToDelete["Parent Node"]) deleteDescription = `Cancelled person responsible change for node "${rowToDelete["Parent Node"]}"`; break;
      }
      addToChangeLog(CHANGE_LOG_TYPES.DELETE_ROW, deleteDescription, { deletedRow: { ...rowToDelete }, stepLabel: currentStep.label });
    }
  };

  const handleRowSelection = (ids, stepIndex) => console.log("Selected rows:", ids, "for step:", stepIndex);

  const getCurrentTableData = () => {
    const currentStep = stepsTable[activeStepTable];
    const tableData = changeWithUploadRecords?.[currentStep.key] || [];
    let columns = [];
    const columnParams = [
      handleDeleteRow,
      fetchOldDescriptionForNode,
      fetchOldParentForNode,
      fetchOldParentForObject,
      addToChangeLog,
      { updateNodesListForDuplicateCheck, updateDescListForDuplicateCheck, checkForDuplicates, batchUpdateDuplicateLists },
      disableCheck
    ];
    switch (activeStepTable) {
      case 0: columns = getColumnsNewNodes(...columnParams); break;
      case 1: columns = getColumnsDescChange(...columnParams); break;
      case 2: columns = getColumnsAddPC(...columnParams); break;
      case 3: columns = getColumnsMoveNode(...columnParams); break;
      case 4: columns = getColumnsMovePC(...columnParams); break;
      case 5: columns = getColumnsRemovePC(...columnParams); break;
      case 6: columns = getColumnsDeleteNode(...columnParams); break;
      case 7: columns = getColumnsChangePR(...columnParams); break;
      default: columns = [];
    }
    return { rows: tableData, columns: columns, title: currentStep.label, key: currentStep.key };
  };

   const validateCurrentTable = (stepIndex) => {
    const currentStep = stepsTable[stepIndex];
    const tableData = changeWithUploadRecords?.[currentStep.key] || [];

    if (tableData.length === 0) {
      return { isValid: true, message: "" };
    }

    const requiredFieldsByStep = {
      0: ["Parent Node", "New Node", "Description"], // New Nodes
      1: ["Parent Node", "New Description"], // Description Change
      2: ["Node", "Cost Center"], // Add Cost Centers
      3: ["New Parent Node", "Selected Node"], // Move Node
      4: ["New Parent Node", "Selected Cost Center"], // Move Cost Center
      5: ["Selected Cost Center"], // Remove Cost Center (Parent Node auto-filled)
      6: ["Deleted Node"], // Delete Node (Parent Node auto-filled)
      7: ["Parent Node", "New Person Responsible"], // Change Person Responsible
    };

    const requiredFields = requiredFieldsByStep[stepIndex] || [];

    for (const row of tableData) {
      for (const field of requiredFields) {
        if (!row[field] || row[field].toString().trim() === "") {
          return {
            isValid: false,
            message: `Please fill all required fields in ${currentStep.label} table. Missing: ${field}`,
          };
        }
      }
    }

    return { isValid: true, message: "" };
  };

  const currentTableData = getCurrentTableData();

  const handleStepChange = (newStepIndex) => {
    const validation = validateCurrentTable(activeStepTable);
    if (!validation.isValid) {
      showSnackbar(`${validation.message}`, "error")
      return;
    }

    setActiveStepTable(newStepIndex);
  };

  
  return (
    <Box sx={{ display: "flex", flexDirection: "column", height: "100%" }}>
      <Box sx={{ width: "100%", scrollbarWidth: "none", "&::-webkit-scrollbar": { display: "none" } }}>
        <Stepper nonLinear activeStep={activeStepTable} sx={{ mb: 2 }} alternativeLabel>
          {stepsTable.map((step, index) => (
            <Step key={step.value}><StepButton onClick={() => handleStepChange(index)}>{step.label}</StepButton></Step>
          ))}
        </Stepper>
      </Box>
      <Box sx={{ flexGrow: 1, overflowY: "auto", scrollbarWidth: "none", "&::-webkit-scrollbar": { display: "none" } }}>
        <Grid container>
          <Grid item md={12} sx={{
            backgroundColor: "white", maxHeight: "max-content", height: "max-content", borderRadius: "8px",
            border: "1px solid #E0E0E0", mt: 0.25, boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)", ...container_Padding
          }}>
            <Box sx={{ width: "100%", height: "auto", display: "flex", flexDirection: "column", justifyContent: "space-between" }}>
              <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mt: 1, mb: 1 }}>
                <Box><Button variant="contained"   disabled={disableCheck} onClick={handleAddRow} sx={{ ml: 1 }}>Add Row</Button></Box>
              </Box>
              {currentTableData.columns.length > 0 && (
                <ReusableTable
                  width="100%"
                  rowHeight={70}
                  rows={currentTableData.rows}
                  columns={currentTableData.columns}
                  title={`This table shows the ${currentTableData.title}`}
                  rowCount={currentTableData.rows.length}
                  page={page}
                  pageSize={pageSize}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  getRowIdValue="Id"
                  hideFooter={true}
                  checkboxSelection={false}
                  callback_onRowDoubleClick={(params) => console.log("params", params.row)}
                  onRowsSelectionHandler={(ids) => handleRowSelection(ids, activeStepTable)}
                  stopPropagation_Column="action"
                  status_onRowDoubleClick={true}
                  showCustomNavigation={true}
                />
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default ChangeWithUploadCCG;