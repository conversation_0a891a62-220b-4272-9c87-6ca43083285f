import{r as b,c as T,Z as r,b1 as v,j as t,d as V,O as g,B as j,F as G}from"./index-226a1e75.js";import{F as A}from"./FilterFieldGlobal-b5a561ef.js";const k=e=>{var E;let n=(e==null?void 0:e.basicDataTabDetails)&&(Object==null?void 0:Object.entries(e==null?void 0:e.basicDataTabDetails));const[D,F]=b.useState([]),l=b.useRef({});b.useEffect(()=>{let i=N(n,e.fieldErrors);const d=l==null?void 0:l.current[i];d&&(d!=null&&d.scrollIntoView)&&!e.missingFieldsDialogOpen&&setTimeout(()=>d.scrollIntoView({behavior:"smooth",block:"center"}),400)},[e.fieldErrors,e.missingFieldsDialogOpen]);const y=Array.isArray(e.fieldErrors)?e.fieldErrors:[],N=i=>{for(const d of y){const c=i.find(([f,u])=>u.some(m=>m.jsonName===d));if(c)return c[0]}return null};return b.useEffect(()=>{F(n==null?void 0:n.map(i=>{var f,u,m;const d=i[0],c=i[1];return T(g,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(f=e==null?void 0:e.missingValidationCards)!=null&&f.includes(e==null?void 0:e.activeViewTab)&&checkForMandatoryFieldCard(c,e==null?void 0:e.fieldErrors)&&((u=e==null?void 0:e.selectedRow)==null?void 0:u.validated)!==!0?r.error.dark:"#E0E0E0"}`,mt:.5,mb:1,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...v},children:[t(g,{container:!0,children:t(V,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:i[0]})}),t(j,{children:t(g,{container:!0,spacing:1,paddingBottom:1,ref:a=>{l.current[i[0]]=a},children:(m=[...i[1]].filter(a=>a.visibility!="Hidden").sort((a,x)=>a.sequenceNo-x.sequenceNo))==null?void 0:m.map(a=>{var h,w;const x=["Description","CompanyCode","GLname","Accounttype","AccountGroup"].includes(a.jsonName),C=((h=e==null?void 0:e.fieldErrors)==null?void 0:h.includes(a.fieldName))||((w=e==null?void 0:e.fieldErrors)==null?void 0:w.includes(a.jsonName));return t(A,{disabled:(e==null?void 0:e.disabled)||x,field:a,dropDownData:e.dropDownData,uniqueId:e==null?void 0:e.uniqueId,viewName:e==null?void 0:e.activeViewTab,selectedRow:e==null?void 0:e.selectedRow,module:e==null?void 0:e.module,isError:C,missingFields:Array.isArray(e==null?void 0:e.fieldErrors)?e==null?void 0:e.fieldErrors:[]},a.fieldName)})})})]},d)}))},[e==null?void 0:e.basicDataTabDetails,e.activeViewTab,e==null?void 0:e.uniqueId,(E=e==null?void 0:e.selectedRow)==null?void 0:E.id,e==null?void 0:e.fieldErrors,e==null?void 0:e.mandatoryFailedView,e==null?void 0:e.missingValidationCards]),t(G,{children:D})};export{k as G};
