export function validateOrgData(orgData) {
  if (!orgData) {
    return false;
  }
  const requiredFields = ["salesOrg", "dc", "plant", "sloc", "warehouse"];
  for (const field of requiredFields) {
    if (!orgData[field]) {
      return false;
    }
    if (field === "salesOrg") {
      if (!orgData[field].code) {
        return false;
      }
    }
    // For other fields (dc, plant, sloc, warehouse), check if value exists and has code
    else {
      if (!orgData[field].value || !orgData[field].value.code) {
        return false;
      }
    }
  }
  return true;
}
