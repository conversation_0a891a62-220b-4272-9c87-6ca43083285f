import{c9 as Kt,ca as zt,cb as Yt,aP as Jt,u as Qt,s as ve,r as c,n as J,fM as Zt,c as V,j as i,aW as Xt,aG as Ve,B as le,aZ as lt,O as it,gs as es,d as We,T as ts,a6 as dt,a_ as ss,R as os,ak as ns,af as as,ds as rs,F as ye,gv as cs,yo as ls,c2 as is,an as je,C as ae,bI as Oe,aT as xe,dG as ds,xi as us,Z as Ct,bD as Cs,aa as fs,aR as hs,xA as s,g as Rs,Ae as _s,aX as X,c5 as I,ai as gs,al as Ts,bK as Ds,am as Es,aF as As,wP as Q,fa as ne,f9 as ut,fb as ps,y0 as Gs,g3 as Ps,bH as Be,cd as me,bJ as Ls,ae as Ss,bU as Ne,aJ as ft,Af as ys,Ag as Ms,Ah as bs,Ai as Ns,Aj as Os,Ak as xs,Al as vs,Am as Is,An as ks,Ao as $s,aO as Fe}from"./index-226a1e75.js";import{d as ws}from"./FeedOutlined-2c089703.js";import{u as Hs}from"./useChangeMaterialRowsRequestor-9caa254c.js";import{D as qs}from"./PreviewPage-262cf4cb.js";import{F as Us}from"./FilterChangeDropdown-2d228e28.js";import{u as Bs}from"./ReusableHIerarchyTree-e69bb363.js";var Ke={},ms=zt;Object.defineProperty(Ke,"__esModule",{value:!0});var Fs=Ke.default=void 0,Vs=ms(Kt()),Ws=Yt;Fs=Ke.default=(0,Vs.default)((0,Ws.jsx)("path",{d:"M20 2H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m-8.5 7.5c0 .83-.67 1.5-1.5 1.5H9v2H7.5V7H10c.83 0 1.5.67 1.5 1.5zm5 2c0 .83-.67 1.5-1.5 1.5h-2.5V7H15c.83 0 1.5.67 1.5 1.5zm4-3H19v1h1.5V11H19v2h-1.5V7h3zM9 9.5h1v-1H9zM4 6H2v14c0 1.1.9 2 2 2h14v-2H4zm10 5.5h1v-3h-1z"}),"PictureAsPdf");const ro=({open:a,closeModal:B,requestId:f,requestType:G,module:W})=>{Jt();const P=Qt();ve();const[H,r]=c.useState(!1);c.useState(null);const t=J(p=>p.payload.payloadData),N=J(p=>p.hierarchyData.changeLog||[]),E=t==null?void 0:t.TemplateName;new URLSearchParams(P.search).get("RequestId"),Zt(W);const A=[{field:"type",headerName:"Operation Type",flex:1,editable:!1,align:"center",headerAlign:"center"},{field:"description",headerName:"Operation Description",flex:1.7,editable:!1,headerAlign:"center"},{field:"updatedBy",headerName:"Updated By",flex:1.2,editable:!1,headerAlign:"center",align:"center"},{field:"updatedOn",headerName:"Updated On",flex:1.6,editable:!1,headerAlign:"center",align:"center",renderCell:p=>cs(p.value)}];c.useState(()=>{const p=ls[E]||{};return Object.keys(p).map(w=>({label:w,columns:p[w],rows:[]}))});const L={position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"80%",height:"auto",bgcolor:"#fff",boxShadow:4,p:2,borderRadius:"20px"},O=()=>{B(!1)},$=new Date;$.setDate($.getDate()-15);const K={convertJsonToExcel:()=>{let p=[];A==null||A.forEach(w=>{w.headerName.toLowerCase()!=="action"&&!w.hide&&p.push({header:w.headerName,key:w.field})}),is({fileName:"Change Log Sheet",columns:p,rows:N})},button:()=>i(je,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>K.convertJsonToExcel(),children:"Download"})};return V(ye,{children:[H&&i(Ve,{blurLoading:H,loaderMessage:Xt.CHANGELOG_LOADING}),i(rs,{open:a,onClose:O,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:V(le,{sx:L,children:[i(lt,{children:V(it,{item:!0,md:12,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[V(le,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[i(es,{sx:{color:"black",fontSize:"20px","&:hover":{transform:"rotate(360deg)",transition:"0.9s"},textAlign:"center",marginTop:"4px"}}),i(We,{id:"modal-modal-title",variant:"subtitle1",fontSize:"16px",fontWeight:"bold",sx:{color:"black"},children:"Change Log"})]}),V(le,{sx:{display:"flex",alignItems:"center",gap:"8px"},children:[i(ts,{title:"Export Table",children:i(dt,{sx:ss,onClick:K.convertJsonToExcel,children:i(os,{iconName:"IosShare"})})}),i(dt,{sx:{padding:"0 0 0 5px"},onClick:O,children:i(ns,{})})]})]})}),i(it,{item:!0,sx:{position:"relative",mt:1},children:i(lt,{children:i(as,{rows:N,columns:A,getRowIdValue:"id",autoHeight:!0,scrollbarSize:10,sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40"},backgroundColor:"#fff"}})})})]})})]})},co=a=>{const B=J(P=>P.profitCenter.payload.requestHeaderData),f=J(P=>P.applicationConfig),G=ve();return{getRequestHeaderTemplatePCG:()=>{let P={decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(B==null?void 0:B.RequestType)||"Create","MDG_CONDITIONS.MDG_MAT_MODULE_NAME":"Profit Center"}],systemFilters:null,systemOrders:null,filterString:null};const H=t=>{var N,E;if(t.statusCode===200){const L={"Header Data":((E=(N=t==null?void 0:t.data)==null?void 0:N.result[0])==null?void 0:E.MDG_MAT_REQUEST_HEADER_CONFIG).sort((O,$)=>O.MDG_MAT_SEQUENCE_NO-$.MDG_MAT_SEQUENCE_NO).map(O=>({fieldName:O.MDG_MAT_UI_FIELD_NAME,sequenceNo:O.MDG_MAT_SEQUENCE_NO,fieldType:O.MDG_MAT_FIELD_TYPE,maxLength:O.MDG_MAT_MAX_LENGTH,value:O.MDG_MAT_DEFAULT_VALUE,visibility:O.MDG_MAT_VISIBILITY,jsonName:O.MDG_MAT_JSON_FIELD_NAME}))};G(ds({tab:"Request Header",data:L})),G(us(L))}},r=t=>{console.log(t)};f.environment==="localhost"?ae(`/${Oe}${xe.INVOKE_RULES.LOCAL}`,"post",H,r,P):ae(`/${Oe}${xe.INVOKE_RULES.PROD}`,"post",H,r,P)}}},js=({param:a,mandatory:B=!1,selectedValues:f,handleSelectionChange:G,errors:W,isLoading:P=!1,singleSelect:H=!1})=>{var t,N;const r=()=>{const E=f[a.key];return Array.isArray(E)&&E.length>0?E[0]||"":E||""};return i(fs,{fullWidth:!0,label:B?V(ye,{children:[V("strong",{children:["Enter ",a.key]})," ",i("span",{style:{color:(N=(t=Ct)==null?void 0:t.error)==null?void 0:N.dark},children:"*"})]}):`Enter ${a!=null&&a.label?a==null?void 0:a.label:a==null?void 0:a.key}`,variant:"outlined",error:!!W[a.key],helperText:W[a.key],inputProps:{maxLength:a==null?void 0:a.length},value:r(),onChange:E=>{var A;const q=E.target.value;G(a.key,(A=q==null?void 0:q.replace(a==null?void 0:a.characterAllowed,""))==null?void 0:A.toUpperCase())},InputProps:{endAdornment:i(ye,{children:P?i(Cs,{size:20,sx:{mr:1}}):null})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"8px",height:50,boxShadow:"0 2px 4px rgba(0, 0, 0, 0.1)"},"& .MuiInputLabel-root":{fontWeight:500}}},a.key)},Ks=c.forwardRef(function(B,f){return i(hs,{direction:"down",ref:f,...B})}),lo=({open:a,onClose:B,parameters:f,mandatoryFields:G,setShowTable:W,allDropDownData:P,setIsSecondTabEnabled:H,module:r})=>{var Xe,et,tt,st,ot,nt;const[t,N]=c.useState({});c.useState({});const[E,q]=c.useState({}),[A,L]=c.useState(""),[O,$]=c.useState(!1),[K,p]=c.useState("success"),[w,j]=c.useState(!1),[z,Y]=c.useState(""),[ee,te]=c.useState(""),[k,ht]=c.useState(!1),[Ie,ze]=c.useState("systemGenerated");c.useState([]);const[Rt,re]=c.useState(""),[_t,se]=c.useState(!1),u=J(e=>e.hierarchyData.requestHeaderData);J(e=>e.request.requestHeader.requestId);const ke=J(e=>e.hierarchyData.requestHeaderData),gt=J(e=>e.payload.dataLoading);J(e=>e.request.salesOrgDTData);let $e=J(e=>e.userManagement.userData);const{checkForNodeDuplicacy:Ye,checkForDescriptionDuplicacy:Tt,checkForObjectDuplicacy:Ys}=Bs(),[Dt,Z]=c.useState({}),[Et,ce]=c.useState({[s.MATERIAL_NUM]:!1,[s.PLANT]:!1,[s.SALES_ORG]:!1,[s.DIVISION]:!1,[s.DIST_CHNL]:!1,[s.WAREHOUSE]:!1,[s.STORAGE_LOC]:!1,[s.MRP_CTRLER]:!1}),[Js,At]=c.useState({code:"",desc:""});c.useState(null),c.useRef(null);const[Je,pt]=c.useState(null),[Gt,Pt]=c.useState(""),[Lt,Me]=c.useState(!1),St=c.useRef(null),yt=Rs(),U=ve();Hs();const[we,Qs]=c.useState(0);c.useState(null);const[He,Mt]=c.useState([]);c.useState(0);const bt=(Xe=_s[u==null?void 0:u.TemplateName])==null?void 0:Xe.map(e=>({field:e.key,headerName:e.key,editable:!0,flex:2})),qe=c.useCallback(e=>{e.preventDefault();const l=(e.clipboardData||window.clipboardData).getData("Text").trim().split(`
`).map((h,x)=>{const _=h.split("	"),R={id:x+1};return bt.forEach((g,T)=>{R[g.field]=_[T]||""}),R});Mt(l)},[]);c.useEffect(()=>{if(we===1)return document.addEventListener("paste",qe),()=>{document.removeEventListener("paste",qe)}},[we,qe]);const Nt=(e,o)=>{pt(e.currentTarget),Pt(o),Me(!0)},Ot=()=>{Me(!1)},xt=()=>{Me(!0)},vt=()=>{Me(!1)},It=!!Je?"custom-popover":void 0,Qe=(e,o)=>{N(n=>({...n,[e]:o})),o.length>0&&q(n=>({...n,[e]:""}))};c.useEffect(()=>{if(He){let e=$t(He);N(e)}},[He]);const kt=(e,o)=>{var l;const n=((l=t[e])==null?void 0:l.length)===o.length;N(h=>({...h,[e]:n?[]:o})),n||q(h=>({...h,[e]:""}))},$t=e=>{const o={};return e.forEach(n=>{Object.keys(n).forEach(l=>{l!=="id"&&n[l].trim()!==""&&(o[l]||(o[l]=[]),o[l].push({code:n[l].trim()}))})}),o},wt=e=>{const o=e==null?void 0:e.filter(n=>{var l;return!t[n]||Array.isArray(t[n])?!((l=t[n])!=null&&l.length):t[n].trim()===""});return o.length>0?(se(!0),re(Ss.MANDATORY_FILTER_MD(o.join(", "))),!1):!0},Ht=async()=>{var e,o,n,l,h,x,_,R,g,T,S,y,D,d,m,v,M,b,F,ie,de,ue,Ce,fe,he,Re,_e,ge,Te,De,Ee,Ae,pe,Ge,Pe,Le,Se;if(wt(G))try{if(U(Q(!0)),(ke==null?void 0:ke.RequestType)===((e=X)==null?void 0:e.CREATE)){const C=await Ye(t[(n=(o=ne)==null?void 0:o[r])==null?void 0:n.CTR_GRP],r,(_=(x=t[(h=(l=ne)==null?void 0:l[r])==null?void 0:h.CTRL_AREA])==null?void 0:x[0])==null?void 0:_.code,"",(S=(T=t==null?void 0:t[(g=(R=ne)==null?void 0:R[r])==null?void 0:g.COA])==null?void 0:T[0])==null?void 0:S.code,"");if((y=C==null?void 0:C.body)!=null&&y.isDbDuplicate){se(!0),re("This Node already exists in some ongoing request!"),U(Q(!1));return}if(((D=C==null?void 0:C.body)==null?void 0:D.PresentInHier)==="X"||C.body.PresentInCA==="X"||((d=C.body)==null?void 0:d.PresentInCOA)==="X"){se(!0),re("This Node already exists in the hierarchy!"),U(Q(!1));return}const oe=await Tt(t[(v=(m=ne)==null?void 0:m[r])==null?void 0:v.CTR_GRP_DESC],r,(F=t[(b=(M=ne)==null?void 0:M[r])==null?void 0:b.CTRL_AREA][0])==null?void 0:F.code,"","");if(Object.keys(oe.body).length!=0&&((ie=oe==null?void 0:oe.body)==null?void 0:ie.isDbDuplicate)===!0){se(!0),re("Description already present in some ongoing request!"),U(Q(!1));return}}else{const C=await Ye((fe=(Ce=t[(ue=(de=ne)==null?void 0:de[r])==null?void 0:ue.CTR_GRP])==null?void 0:Ce[0])==null?void 0:fe.code,r,(_e=t[(Re=(he=ne)==null?void 0:he[r])==null?void 0:Re.CTRL_AREA][0])==null?void 0:_e.code,"","");if((ge=C==null?void 0:C.body)!=null&&ge.isDbDuplicate){se(!0),re("This Node already exists in some ongoing request!"),U(Q(!1));return}}if(se(!1),B(),(u==null?void 0:u.RequestType)===((Te=X)==null?void 0:Te.CREATE)){let C=[{id:"1",label:t[(De=s)==null?void 0:De.PRCTR_GRP]||t[(Ee=s)==null?void 0:Ee.CCCTR_GRP]||t[(Ae=s)==null?void 0:Ae.CECTR_GRP],description:t[(pe=s)==null?void 0:pe.PRCTR_GRP_DESC]||t[(Ge=s)==null?void 0:Ge.CCCTR_GRP_DESC]||t[(Pe=s)==null?void 0:Pe.CECTR_GRP_DESC],isParent:!0,tags:[],child:[]}];U(ut(C)),U(ps({id:"1",type:"ADD NODE",description:`${(Le=C==null?void 0:C[0])==null?void 0:Le.label} created `,updatedBy:($e==null?void 0:$e.emailId)||"",updatedOn:`/Date(${new Date().getTime()})/`||""}))}else if((u==null?void 0:u.RequestType)===((Se=X)==null?void 0:Se.CHANGE))try{const C=await qt();U(ut(C))}catch{}U(Q(!1)),U(Gs(t)),H(!0),U(Ps(1))}catch{se(!0),re("Error fetching data."),U(Q(!1))}},qt=()=>{var n,l,h,x,_,R,g,T,S,y,D,d,m,v,M,b,F,ie,de,ue,Ce,fe,he,Re,_e,ge,Te,De,Ee,Ae,pe,Ge,Pe,Le,Se,C,oe,at;L(!0),U(Q(!0));var e;let o;return r===((n=I)==null?void 0:n.PCG)?(o=Ne,e={node:((h=t[(l=s)==null?void 0:l.PRCTR_GRP])==null?void 0:h.length)===0?"DSM-1":(R=(_=t[(x=s)==null?void 0:x.PRCTR_GRP])==null?void 0:_[0])==null?void 0:R.code,controllingArea:((T=t[(g=s)==null?void 0:g.CTRL_AREA_PCG])==null?void 0:T.length)===0?"ETCA":(D=(y=t[(S=s)==null?void 0:S.CTRL_AREA_PCG])==null?void 0:y[0])==null?void 0:D.code,classValue:"0106",id:"",screenName:"Display"}):r===((d=I)==null?void 0:d.CCG)?(o=Be,e={node:((v=t[(m=s)==null?void 0:m.CCCTR_GRP])==null?void 0:v.length)===0?"DSM-1":(F=(b=t[(M=s)==null?void 0:M.CCCTR_GRP])==null?void 0:b[0])==null?void 0:F.code,controllingArea:((de=t[(ie=s)==null?void 0:ie.CTRL_AREA_PCG])==null?void 0:de.length)===0?"ETCA":(fe=(Ce=t[(ue=s)==null?void 0:ue.CTRL_AREA_PCG])==null?void 0:Ce[0])==null?void 0:fe.code,classValue:"0101",id:"",screenName:"Display"}):r===((he=I)==null?void 0:he.CEG)&&(o=me,e={coa:((_e=t[(Re=s)==null?void 0:Re.COA])==null?void 0:_e.length)===0?"ETCN":(De=(Te=t[(ge=s)==null?void 0:ge.COA])==null?void 0:Te[0])==null?void 0:De.code,node:((Ae=t[(Ee=s)==null?void 0:Ee.CECTR_GRP])==null?void 0:Ae.length)===0?"DSM-1":(Pe=(Ge=t[(pe=s)==null?void 0:pe.CECTR_GRP])==null?void 0:Ge[0])==null?void 0:Pe.code,controllingArea:((Se=t[(Le=s)==null?void 0:Le.CTRL_AREA_PCG])==null?void 0:Se.length)===0?"ETCA":(at=(oe=t[(C=s)==null?void 0:C.CTRL_AREA_PCG])==null?void 0:oe[0])==null?void 0:at.code,classValue:"0102",id:"",screenName:"Display"}),new Promise((rt,Xs)=>{const Wt=be=>{let ct=[];be.statusCode===ft.STATUS_200?(ct.push(be.body.HierarchyTree),L(!1),U(Q(!1)),rt(ct)):rt([])},jt=be=>{console.log(be)};ae(`/${o}/data/displayHierarchyTreeNodeStructure`,"post",Wt,jt,e)})},Ut=()=>{j(!1)},Ue=()=>{ht(!1),ze("systemGenerated")},Bt=e=>{var o;ze((o=e==null?void 0:e.target)==null?void 0:o.value)},mt=()=>{Ie==="systemGenerated"&&(handleDownload(),Ue()),Ie==="mailGenerated"&&(handleEmailDownload(),Ue())};c.useEffect(()=>{var n;const{[(n=s)==null?void 0:n.MATERIAL_NUM]:e,...o}=t||{};o&&Object.keys(o).length>0&&At({code:"",desc:""})},[JSON.stringify({...t,[s.MATERIAL_NUM]:void 0})]),c.useEffect(()=>{f==null||f.forEach(e=>{var o,n;(e.key===((o=s)==null?void 0:o.CTRL_AREA_PCG)||e.key===((n=s)==null?void 0:n.COA))&&Ft(e.key)})},[f]),c.useEffect(()=>{var e,o,n,l,h,x,_,R,g,T,S,y,D,d,m,v,M;t[(e=s)==null?void 0:e.CTRL_AREA_PCG]&&t[(o=s)==null?void 0:o.CTRL_AREA_PCG].length===0&&(u==null?void 0:u.RequestType)===((n=X)==null?void 0:n.CHANGE)&&(r===((l=I)==null?void 0:l.PCG)?(t[(h=s)==null?void 0:h.PRCTR_GRP]=[],Z(b=>{var F;return{...b,[(F=s)==null?void 0:F.PRCTR_GRP]:[]}})):r===((x=I)==null?void 0:x.CCG)?(t[(_=s)==null?void 0:_.CCCTR_GRP]=[],Z(b=>{var F;return{...b,[(F=s)==null?void 0:F.CCCTR_GRP]:[]}})):r===((R=I)==null?void 0:R.CEG)&&(t[(g=s)==null?void 0:g.CECTR_GRP]=[],Z(b=>{var F;return{...b,[(F=s)==null?void 0:F.CECTR_GRP]:[]}}))),(r===((T=I)==null?void 0:T.PCG)||r===((S=I)==null?void 0:S.CCG))&&t[(y=s)==null?void 0:y.CTRL_AREA_PCG]&&t[(D=s)==null?void 0:D.CTRL_AREA_PCG].length>0&&(u==null?void 0:u.RequestType)===((d=X)==null?void 0:d.CHANGE)&&Ze(((M=(v=t[(m=s)==null?void 0:m.CTRL_AREA_PCG])==null?void 0:v[0])==null?void 0:M.code)||"")},[t[(et=s)==null?void 0:et.CTRL_AREA_PCG]]),c.useEffect(()=>{var e,o,n,l,h,x,_,R,g,T,S,y,D,d,m,v;t[(e=s)==null?void 0:e.CTRL_AREA_PCG]&&t[(o=s)==null?void 0:o.CTRL_AREA_PCG].length===0&&(u==null?void 0:u.RequestType)===((n=X)==null?void 0:n.CHANGE)&&(r===((l=I)==null?void 0:l.PCG)?(t[(h=s)==null?void 0:h.PRCTR_GRP]=[],Z(M=>{var b;return{...M,[(b=s)==null?void 0:b.PRCTR_GRP]:[]}})):r===((x=I)==null?void 0:x.CCG)?(t[(_=s)==null?void 0:_.CCCTR_GRP]=[],Z(M=>{var b;return{...M,[(b=s)==null?void 0:b.CCCTR_GRP]:[]}})):r===((R=I)==null?void 0:R.CEG)&&(t[(g=s)==null?void 0:g.CECTR_GRP]=[],Z(M=>{var b;return{...M,[(b=s)==null?void 0:b.CECTR_GRP]:[]}}))),r===((T=I)==null?void 0:T.CEG)&&t[(S=s)==null?void 0:S.COA]&&t[(y=s)==null?void 0:y.COA].length>0&&(u==null?void 0:u.RequestType)===((D=X)==null?void 0:D.CHANGE)&&Ze(((v=(m=t[(d=s)==null?void 0:d.COA])==null?void 0:m[0])==null?void 0:v.code)||"")},[t[(tt=s)==null?void 0:tt.COA]]);const Ze=e=>{var _,R,g,T,S,y;var o,n,l;r===((_=I)==null?void 0:_.PCG)?(o=(R=s)==null?void 0:R.PRCTR_GRP,n=Ne,l=`/${n}/node/getZeroLevelNodes?controllingArea=${e}`):r===((g=I)==null?void 0:g.CCG)?(o=(T=s)==null?void 0:T.CCCTR_GRP,n=Be,l=`/${n}/node/getZeroLevelNodes?controllingArea=${e}`):r===((S=I)==null?void 0:S.CEG)&&(o=(y=s)==null?void 0:y.CECTR_GRP,n=me,l=`/${n}/node/getZeroLevelNodes?chartOfAccount=${e}`),ce(D=>({...D,[o]:!0})),ae(l,"get",D=>{Z(d=>({...d,[o]:D.body})),ce(d=>({...d,[o]:!1}))},D=>{ce(d=>({...d,[o]:!1}))})},Ft=e=>{var _,R,g,T,S,y,D,d,m;ce(v=>({...v,[e]:!0}));const o={[(_=s)==null?void 0:_.CTRL_AREA_PCG]:"/data/getControllingArea",[(R=s)==null?void 0:R.COA]:"/data/getChartOfAccounts"},l={[(g=I)==null?void 0:g.PCG]:{[(T=s)==null?void 0:T.CTRL_AREA_PCG]:Ne},[(S=I)==null?void 0:S.CCG]:{[(y=s)==null?void 0:y.CTRL_AREA_PCG]:Be},[(D=I)==null?void 0:D.CEG]:{[(d=s)==null?void 0:d.CTRL_AREA_PCG]:Ne,[(m=s)==null?void 0:m.COA]:me}}[r]||{},h=v=>{Z(M=>({...M,[e]:v.body})),U(Ls({keyName:e,data:v==null?void 0:v.body})),ce(M=>({...M,[e]:!1}))},x=v=>{ce(M=>({...M,[e]:!1}))};ae(`/${l[e]}${o[e]}`,"get",h,x)},Vt=e=>{var n,l,h,x,_,R,g,T,S,y,D;const o=d=>d.code&&d.desc?`${d.code} - ${d.desc}`:d.code||"";if(e.key===((n=s)==null?void 0:n.CTRL_AREA_PCG)||(e.key===((l=s)==null?void 0:l.PRCTR_GRP)||e.key===((h=s)==null?void 0:h.CCCTR_GRP)||e.key===((x=s)==null?void 0:x.CECTR_GRP)||e.key===((_=s)==null?void 0:_.COA))&&(e==null?void 0:e.type)==="Dropdown")return i(Us,{param:e,mandatory:G==null?void 0:G.includes(e==null?void 0:e.key),dropDownData:Dt,allDropDownData:P,selectedValues:t,handleSelectAll:kt,handleSelectionChange:Qe,errors:E,formatOptionLabel:o,handlePopoverOpen:Nt,handlePopoverClose:Ot,handleMouseEnterPopover:xt,handleMouseLeavePopover:vt,isPopoverVisible:Lt,popoverId:It,popoverAnchorEl:Je,popoverRef:St,popoverContent:Gt,isMaterialNum:!1,isLoading:Et[e.key],isSelectAll:!0,singleSelect:!0});if(e.key===((R=s)==null?void 0:R.PRCTR_GRP_DESC)||e.key===((g=s)==null?void 0:g.CCCTR_GRP_DESC)||(e.key===((T=s)==null?void 0:T.PRCTR_GRP)||e.key===((S=s)==null?void 0:S.CCCTR_GRP)||e.key===((y=s)==null?void 0:y.CECTR_GRP)||e.key===((D=s)==null?void 0:D.CECTR_GRP_DESC))&&(e==null?void 0:e.type)==="Input")return i(js,{param:e,mandatory:G==null?void 0:G.includes(e==null?void 0:e.key),selectedValues:t,handleSelectionChange:Qe,errors:E})};return V(ye,{children:[V(gs,{open:a,TransitionComponent:Ks,keepMounted:!0,onClose:()=>{},maxWidth:we===1?"md":"sm",fullWidth:!0,children:[V(le,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[i(ws,{color:"primary",sx:{marginRight:"0.5rem"}}),i(We,{variant:"h6",component:"div",color:"primary",children:"Please Select Filter(s)"})]}),V(Ts,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[i(ye,{children:f==null?void 0:f.map(e=>i(le,{sx:{marginBottom:"1rem"},children:Vt(e)},e.key))}),_t&&V(We,{variant:"h6",color:(ot=(st=Ct)==null?void 0:st.error)==null?void 0:ot.dark,children:["* ",Rt]}),i(Ve,{blurLoading:gt})]}),i(Es,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"flex-end",alignItems:"end"},children:V(le,{sx:{display:"flex",gap:1},children:[i(je,{onClick:()=>{var e;yt((e=Ds)==null?void 0:e.REQUEST_BENCH),B()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),(u==null?void 0:u.RequestType)!==((nt=X)==null?void 0:nt.CHANGE_WITH_UPLOAD)&&i(je,{onClick:Ht,variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:"OK"})]})})]}),i(qs,{onDownloadTypeChange:mt,open:k,downloadType:Ie,handleDownloadTypeChange:Bt,onClose:Ue}),i(Ve,{blurLoading:A,loaderMessage:ee}),O&&i(As,{openSnackBar:w,alertMsg:z,alertType:K,handleSnackBarClose:Ut})]})},io=()=>{const a=ve();return{preparePayload:c.useCallback(f=>{const G=[],W=[],P=[],H=[],r=[],t=[],N=[],E=[],q=[],A=[];if(!f||typeof f!="object")return{success:!0};for(const[L,O]of Object.entries(f)){const{isNewNode:$,isMoved:K,oldParentNode:p,newParentNode:w,tags:j=[],replaceTagList:z=[],replacedTags:Y=[],description:ee,isDeleted:te}=O;if(te){$||N.push(`${p}$$${L}`);continue}if($&&E.push(L),ee&&q.push(ee),$?G.push(`${p}$$${L}`):K&&(G.push(`${w}$$${L}`),W.push(`${p}$$${L}`)),(j==null?void 0:j.length)>0&&j.forEach(k=>{P.push(`${L}$$${k}`),A.push(`${k}`)}),(z==null?void 0:z.length)>0&&z.forEach(k=>{H.push(`${L}$$${k}`)}),(Y==null?void 0:Y.length)>0&&Y.forEach(k=>{H.push(`${L}$$${k}`)}),ee){const k=`${L}$~$${ee}`;$?r.push(k):t.push(k)}}return a(ys(G)),a(Ms(W)),a(bs(P)),a(Ns(H)),a(Os(r)),a(xs(t)),a(vs(N)),a(Is(E)),a(ks(q)),a($s(A)),{success:!0,NodeList:G,ReplaceNodesList:W,TagList:P,ReplaceTagList:H,DescList:r,EditDescList:t,DeleteNodeList:N,nodesListForDBDuplicateCheck:E,descListForDBDuplicateCheck:q,tagListForDBDuplicateCheck:A}},[a])}},zs={[Fe.PCG]:"MDG_MAT_DYNAMIC_WF_DT",[Fe.CCG]:"MDG_MAT_DYNAMIC_WF_DT",[Fe.CEG]:"MDG_MAT_DYNAMIC_WF_DT"},uo=()=>{const a=J(f=>f.applicationConfig);return{getDynamicWorkflowDT:(f=1,G,W,P,H)=>new Promise((r,t)=>{let N={decisionTableId:null,decisionTableName:W,version:G,conditions:[H]};const E=A=>{var L,O;if(A.statusCode===ft.STATUS_200){let $;if(P){const p=zs[P]||P;$=((O=(L=A==null?void 0:A.data)==null?void 0:L.result[0])==null?void 0:O[p])||[];const w=new Map;$.forEach(z=>{if(z.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(f)){const Y=z.MDG_MAT_SENDBACK_ALLOWED;typeof Y=="string"&&Y.trim().length>0&&Y.split(",").map(te=>{const k=te.trim().match(/^(-?\d+)-(.*)$/);return k?{key:parseInt(k[1],10),Name:k[2].trim()}:null}).filter(Boolean).forEach(({key:te,Name:k})=>{w.has(te)||w.set(te,k)})}});const j=Array.from(w,([z,Y])=>({key:z,Name:Y}));r(j);return}let K=[];$==null||$.forEach(p=>{p.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(f)&&p.MDG_MAT_SENDBACK_ALLOWED.split(",").map(j=>parseInt(j)).forEach(j=>K.push(j))}),K=[...new Set(K)],r(K)}else t(new Error("Failed to fetch workflow levels"))},q=A=>{t(A)};a.environment==="localhost"?ae(`/${Oe}${xe.INVOKE_RULES.LOCAL}`,"post",E,q,N):ae(`/${Oe}${xe.INVOKE_RULES.PROD}`,"post",E,q,N)})}};export{ro as C,lo as R,uo as a,io as b,Fs as d,co as u};
