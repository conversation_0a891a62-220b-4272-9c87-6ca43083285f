import{s as ce,b9 as te,n as I,g as le,u as ae,a as de,r as d,bc as pe,C as b,c as h,j as o,Q as ge,O as p,a6 as me,a_ as ue,a$ as he,d as _,Z as y,aZ as L,B as $,b5 as ye,b6 as Ce,ag as xe,c5 as be,ae as fe,aO as Te,bf as Ae,bH as S,aT as Se,aG as Ee,c6 as E,aK as we,c7 as ve,c8 as Re,bU as Ne}from"./index-226a1e75.js";import{d as _e,a as ke,b as Oe}from"./Category-83dc6e58.js";import{d as Le}from"./Description-d98685cc.js";import{u as $e}from"./UseCostCenterFieldConfig-3572f1d8.js";import{G as Be}from"./GenericTabsGlobal-6faba7da.js";import"./FilterFieldGlobal-b5a561ef.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";const N=({label:g,value:k,icon:a})=>o(p,{item:!0,xs:6,children:h(L,{flexDirection:"row",alignItems:"center",spacing:1,children:[a&&o($,{children:a}),o(_,{variant:"body2",color:y.secondary.grey,children:g}),h(_,{variant:"body2",fontWeight:"bold",children:[": ",k||""]})]})}),eo=()=>{var M;const g=ce(),k=te(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),a=I(r=>(r.costCenter.costCenterTabs||[]).filter(i=>i.tab!=="Initial Screen")),{loading:Me,error:Pe,fetchCostCenterFieldConfig:F}=$e(),G=le();k();const t=ae().state,{t:f}=de(),[w,V]=d.useState([]),[v,W]=d.useState([]),[z,O]=d.useState(!1),[U,Fe]=d.useState(""),[R,D]=d.useState(0),[K,Ge]=d.useState(null),[H,q]=d.useState(""),C=I(r=>{var e;return(e=r.costCenterDropDownData)==null?void 0:e.dropDown});d.useEffect(()=>{a!=null&&a.length||F()},[]),d.useEffect(()=>{pe(Ae.MODULE,Te.CC),se()},[]);const Y=r=>{const e=s=>{g(E({keyName:"CostcenterType",data:(s==null?void 0:s.body)||[],keyName2:r}))},i=s=>{console.error(s)};b(`/${S}/data/getCostCenterCategory`,"get",e,i)},Z=r=>{const e=s=>{g(E({keyName:"FuncAreaLong",data:s==null?void 0:s.body,keyName2:r}))},i=s=>{console.error(s)};b(`/${S}/data/getFunctionalArea`,"get",e,i)},j=(r,e)=>{const i=l=>{g(E({keyName:"CompCode",data:(l==null?void 0:l.body)||[],keyName2:e}))},s=l=>{console.log(l)};b(`/${S}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${r}&rolePrefix=ETP`,"get",i,s)},Q=(r,e)=>{const i={controllingArea:"ETCA",companyCode:r,top:"100",skip:"0"},s=c=>{var x;g(E({keyName:"ProfitCtr",data:((x=c==null?void 0:c.body)==null?void 0:x.list)||[],keyName2:e}))},l=c=>{console.log(c)};b(`/${Ne}/data/getProfitCentersNo`,"post",s,l,i)},X=()=>{const r=i=>{g(E({keyName:"AddrCountry",data:i==null?void 0:i.body}))},e=i=>{console.log(i)};b(`/${S}/data/getCountry`,"get",r,e)},J=(r,e)=>{const i=l=>{g(E({keyName:"AddrRegion",data:l==null?void 0:l.body,keyName2:e}))},s=l=>{console.log(l)};b(`/${S}/data/getRegionBasedOnCountry?country=${r}`,"get",i,s)},ee=(r,e)=>{D(e)},oe=(r,e)=>{const i={"Basic Data":e==null?void 0:e.basicDataTabDto,"Additional Data":e==null?void 0:e.additionalDataTabDto,Indicators:e==null?void 0:e.indicatorsTabDto,Address:e==null?void 0:e.addressTabDto,Communication:e==null?void 0:e.communicationTabDto,Control:e==null?void 0:e.controlTabDto,Template:e==null?void 0:e.templatesTabDto};return r.map(s=>{const l=s.tab,c=i[l];if(!c)return s;const x={};for(const u in s.data)x[u]=s.data[u].map(n=>{const T=n.jsonName;let A=c==null?void 0:c[T];return typeof A=="boolean"&&(A=A?"TRUE":"FALSE"),{...n,value:A??n.value}});return{...s,data:x}})},B=d.useRef(!1);d.useEffect(()=>{const r=(a==null?void 0:a.length)>0,e=v&&Object.keys(v).length>0;if(r&&e&&!B.current){B.current=!0;const i=oe(a,v);V(i),Y(),Z(),X()}},[a,v]);const se=()=>{var s,l;O(!0);const r={coAreaCCs:[{controllingArea:t==null?void 0:t.controllingArea,costCenter:t==null?void 0:t.costCenter}]},e=async c=>{var P;const x=((P=c==null?void 0:c.body)==null?void 0:P[0])||{};W(x);const u=we(),n=c==null?void 0:c.body.reduce((ie,m)=>({...ie,...m.additionalDataTabDto,...m.addressTabDto,...m.basicDataTabDto,...m.communicationTabDto,...m.controlTabDto,...m.templatesTabDto,controllingArea:m.controllingArea,costCenter:m.costCenter,fromValid:m.fromValid,toValid:m.toValid}),{});q(u);const T=[];n!=null&&n.controllingArea&&T.push(j(n.controllingArea,u)),n!=null&&n.CompCode&&T.push(ve(n==null?void 0:n.CompCode,g,u),Q(n==null?void 0:n.CompCode,u)),n!=null&&n.AddrCountry&&T.push(J(n==null?void 0:n.AddrCountry,u));const A=await Promise.all(T),re=Object.assign({},...A),ne={[u]:{...n,...re}};g(Re({requestHeaderData:{},rowsHeaderData:{},rowsBodyData:ne})),setTimeout(()=>{O(!1)},1e3)},i=c=>{console.error("Error fetching cost center data",c),O(!1)};b(`/${S}${(l=(s=Se)==null?void 0:s.DATA)==null?void 0:l.GET_COSTCENTER_DATA}`,"post",e,i,r)};return h("div",{style:{backgroundColor:"#FAFCFF"},children:[o(p,{container:!0,sx:ge,children:o(p,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:h(p,{md:9,sx:{display:"flex"},children:[o(me,{color:"primary",sx:ue,onClick:()=>G(-1),children:o(he,{sx:{fontSize:"25px",color:"#000000"}})}),h(p,{item:!0,md:12,children:[o(_,{variant:"h3",children:o("strong",{children:f("Display Cost Center")})}),o(_,{variant:"body2",color:"#777",children:f("This view displays the details of the Cost Centers")})]})]})})}),h(p,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:y.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[h(L,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[o(p,{item:!0,children:o(N,{label:f("Controlling Area"),value:(t==null?void 0:t.controllingArea)||"",labelWidth:"35%",icon:o(_e,{sx:{color:y.blue.indigo,fontSize:"20px",marginTop:"10px"}})})}),o(p,{item:!0,children:o(N,{label:f("CostCenter Number"),value:(t==null?void 0:t.costCenter)||"",labelWidth:"35%",icon:o(ke,{sx:{color:y.blue.indigo,fontSize:"20px",marginTop:"10px"}})})})]}),h(L,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[o(p,{item:!0,children:o(N,{label:f("Short Description"),value:(t==null?void 0:t.CostCenterName)||"",labelWidth:"35%",icon:o(Oe,{sx:{color:y.blue.indigo,fontSize:"20px",marginTop:"10px"}})})}),o(p,{item:!0,children:o(N,{label:f("Long Description"),value:(t==null?void 0:t.description)||"",labelWidth:"35%",icon:o(Le,{sx:{color:y.blue.indigo,fontSize:"20px",marginTop:"10px"}})})})]})]}),o(p,{children:w.length>0?h($,{sx:{mt:3},children:[o(ye,{value:R,onChange:ee,indicatorColor:"primary",textColor:"primary",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:w.map((r,e)=>o(Ce,{label:r.tab},e))}),o(xe,{elevation:2,sx:{p:3,borderRadius:8},children:w[R]&&(C!=null&&C.ProfitCtr)&&(C!=null&&C.CompCode)?o(Be,{disabled:!0,basicDataTabDetails:w[R].data,dropDownData:C,activeViewTab:w[R].tab,uniqueId:H,selectedRow:K||{},module:(M=be)==null?void 0:M.CC}):""})]}):o($,{sx:{marginTop:"30px",border:`1px solid ${y.secondary.grey}`,padding:"16px",background:`${y.primary.white}`,textAlign:"center"},children:o("span",{children:fe.NO_DATA_AVAILABLE})})}),o(Ee,{blurLoading:z,loaderMessage:U})]})};export{eo as default};
