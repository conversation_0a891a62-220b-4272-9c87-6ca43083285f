import * as React from 'react';
import { Dialog, DialogTitle, DialogContent } from '@cw/rds';
import { colors } from '../../../constant/colors';
import { useTheme } from '@mui/material';

export default function CustomDialog({isOpen, Title, children, handleClose, titleIcon, width}) {
  const theme = useTheme()
  return (
    <React.Fragment>
      <Dialog
        open={isOpen}
        onClose={handleClose}
        aria-describedby="alert-dialog-slide-description"
        maxWidth={width || "small"}
        fullWidth={true}
      >
        <DialogTitle closeIcon style={{backgroundColor:theme.palette.primary.light}}>
          <div style={{display:"flex",flexDirection:'row',alignItems:"center",gap:4}}>
            {titleIcon}{Title}
          </div>
        </DialogTitle>
        <DialogContent>
          {children}
        </DialogContent>
      </Dialog>
    </React.Fragment>
  );
}