import { requestHeaderTabs } from '@app/tabsDetailsSlice';
import { doAjax } from '@components/Common/fetchService';
import React from 'react'
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import { destination_IDM } from '../../destinationVariables';
import { END_POINTS } from '@constant/apiEndPoints';
import { useLocation } from 'react-router-dom';
import { API_CODE, MODULE_MAP, REQUEST_TYPE, CHANGE_CC_INFO } from '@constant/enum';

const useDynamicWorkflowDTCC = () => {
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const taskData = useSelector((state) => state.userManagement?.taskData);
  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const getDynamicWorkflowDT = (reqType, costCenterType, currentLevel = 1, version, dtName, module) => {
    return new Promise((resolve, reject) => {
      let payload = {
        decisionTableId: null,
        decisionTableName: dtName,
        version: version,
        conditions:
          [
            {
              "MDG_CONDITIONS.MDG_CC_REQUEST_TYPE":  CHANGE_CC_INFO?.reqType,
              "MDG_CONDITIONS.MDG_CC_CCCATEGORY": costCenterType ? costCenterType : "NA",
            },
          ]
      };
      const hSuccess = (data) => {
        if (data.statusCode === API_CODE.STATUS_200) {
          let workFlowData= data?.data?.result[0]?.MDG_MAT_DYNAMIC_WF_DT || [];
          if (module === MODULE_MAP?.BK) {
            workFlowData = data?.data?.result[0]?.MDG_BNKY_DYNAMIC_WORKFLOW_DT_ACTION_TYPE || [];
           const transformedMap = new Map();

            workFlowData.forEach(task => {
              if (task.MDG_DYNAMIC_WF_APPROVAL_LEVEL === parseInt(currentLevel)) {
                const levels = task.MDG_MAT_SENDBACK_ALLOWED;

                if (typeof levels === "string" && levels.trim().length > 0) {
                  const parsedLevels = levels.split(',').map(item => {
                    const match = item.trim().match(/^(-?\d+)-(.*)$/);
                    if (match) {
                      return {
                        key: parseInt(match[1], 10),
                        Name: match[2].trim()
                      };
                    }
                    return null;
                  }).filter(Boolean);

                  // Insert only unique keys
                  parsedLevels.forEach(({ key, Name }) => {
                    if (!transformedMap.has(key)) {
                      transformedMap.set(key, Name);
                    }
                  });
                }
              }
            });

            // Convert map to array
            const transformed = Array.from(transformedMap, ([key, Name]) => ({ key, Name }));
            resolve(transformed);
            return;


          }
          // Step 1: Get sendback levels from tasks at current level
          let sendBackLevels = [];
          workFlowData?.forEach(task => {
            if (task.MDG_DYNAMIC_WF_APPROVAL_LEVEL === parseInt(currentLevel)) {
              const levels = task.MDG_MAT_SENDBACK_ALLOWED.split(',').map(l => parseInt(l));
              levels.forEach(level => sendBackLevels.push(level));
            }
          });
          sendBackLevels = [...new Set(sendBackLevels)]

          // Step 2: Filter tasks where the approval level is in the sendBackLevels set
          // const selectOptions = workFlowData
          //   .filter(task => sendBackLevels.has(task.MDG_DYNAMIC_WF_APPROVAL_LEVEL))
          //   .map(task => ({
          //     label: task.MDG_DYNAMIC_WF_LEVEL_NAME,
          //     value: task.MDG_DYNAMIC_WF_APPROVAL_LEVEL
          //   }));
          resolve(sendBackLevels);
        } else {
          reject(new Error('Failed to fetch workflow levels'));
        }
      };
      const hError = (error) => {
        reject(error)
      };
      if (applicationConfig.environment === "localhost") {
        doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
      } else {
        doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
      }
    });
  };

  return { getDynamicWorkflowDT };
}

export default useDynamicWorkflowDTCC