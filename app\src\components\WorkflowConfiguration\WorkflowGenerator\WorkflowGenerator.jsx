import React, { useState, useEffect } from 'react';
import { Layout, Form, notification } from 'antd';
import { doAjax } from "@components/Common/fetchService";
import { destination_Admin } from "../../../destinationVariables";
import { createPayloadForWF } from '../../../functions';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSnackbar } from '@hooks/useSnackbar';
import { API_CODE, MODULE_MAP, NEW_WF_DATA, WORKFLOW_DATA_CONSTANTS } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';
import WorkflowLayout from './WorkflowLayout';
import WorkflowHeader from './WorkflowHeader';
import WorkflowDisplay from './WorkflowDisplay';
import WorkflowRightPanel from './WorkflowRightPanel';
import WorkflowModals from './WorkflowModals';
const { Content, Sider } = Layout;

const WorkflowGenerator = () => {
  const [workflowData, setWorkflowData] = useState({});
  const [selectedCard, setSelectedCard] = useState(null);
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [isRightPanelCollapsed, setIsRightPanelCollapsed] = useState(false);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isMoveModalVisible, setIsMoveModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isLevelNameChangeModalVisible, setIsLevelNameChangeModalVisible] = useState(false);
  const [isBenchStatusChangeModalVisible, setIsBenchStatusChangeModalVisible] = useState(false);
  const [benchStatusChangeData, setBenchStatusChangeData] = useState(null);
  const [cardToMove, setCardToMove] = useState(null);
  const [cardToEdit, setCardToEdit] = useState(null);
  const [levelNameChangeData, setLevelNameChangeData] = useState(null);
  const [openChangeLog, setOpenChangeLog] = useState(false);
  const [changeLogData, setChangeLogData] = useState([]);
  const [workflowGroups, setWorkflowGroups] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [moveForm] = Form.useForm();
  const [editDialogForm] = Form.useForm();
  const location = useLocation();
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();
  const completeData = location.state || {};
  const mode = completeData?.mode || WORKFLOW_DATA_CONSTANTS.MODES.CREATE;
  const headerData = completeData?.headerData || {};

  useEffect(() => {
    let initialData = (mode === WORKFLOW_DATA_CONSTANTS.MODES.CREATE) ? NEW_WF_DATA : completeData?.workflowData;
    setWorkflowData(initialData);
    setSelectedCard(initialData['-1']?.tasks[0]);
    fetchApproverGroups();
  }, []);

  const toggleRightPanel = () => {
    setIsRightPanelCollapsed(!isRightPanelCollapsed);
  };
  const reAdjustLevels = (workflowData) => {
    const levels = Object.keys(workflowData).map(level => parseInt(level));
    const specialLevels = levels.filter(level => level === -1 || level === 0);
    const regularLevels = levels.filter(level => level > 0).sort((a, b) => a - b);
    const newWorkflowData = {};
    specialLevels.forEach(level => {
        if (workflowData[level]) {
        newWorkflowData[level] = { ...workflowData[level] };
        }
    });
    regularLevels.forEach((oldLevel, index) => {
        const newLevel = index + 1;
        if (workflowData[oldLevel] && workflowData[oldLevel].tasks?.length > 0) {
        newWorkflowData[newLevel] = {
            ...workflowData[oldLevel],
            levelName: workflowData[oldLevel].levelName?.includes(`${WORKFLOW_DATA_CONSTANTS?.LEVEL} ${oldLevel}`)
            ? workflowData[oldLevel].levelName.replace(`${WORKFLOW_DATA_CONSTANTS?.LEVEL} ${oldLevel}`, `${WORKFLOW_DATA_CONSTANTS?.LEVEL} ${newLevel}`)
            : workflowData[oldLevel].levelName,
            requestBenchStatus: workflowData[oldLevel].requestBenchStatus
        };
        newWorkflowData[newLevel].tasks = newWorkflowData[newLevel].tasks.map(task => ({
            ...task,
            sendBackAllowedTo: task.sendBackAllowedTo?.map(sendBackLevel => {
            if (sendBackLevel > 0) {
                const oldLevelIndex = regularLevels.indexOf(sendBackLevel);
                return oldLevelIndex !== -1 ? oldLevelIndex + 1 : sendBackLevel;
            }
            return sendBackLevel;
            }) || []
        }));
        }
    });
    Object.keys(newWorkflowData).forEach(level => {
        if (newWorkflowData[level]?.tasks) {
        newWorkflowData[level].tasks = newWorkflowData[level].tasks.map(task => ({
            ...task,
            sendBackAllowedTo: task.sendBackAllowedTo?.map(sendBackLevel => {
            if (sendBackLevel > 0) {
                const oldLevelIndex = regularLevels.indexOf(sendBackLevel);
                return oldLevelIndex !== -1 ? oldLevelIndex + 1 : sendBackLevel;
            }
            return sendBackLevel;
            }) || []
        }));
        }
    });
    return newWorkflowData;
  };
  const getAvailableLevels = () => {
    const existingLevels = Object.keys(workflowData).map(level => parseInt(level)).sort((a, b) => a - b);
    const availableLevels = [];
    existingLevels.forEach(level => {
      if (level > 0) {
        availableLevels.push(level);
      }
    });
    const maxLevel = Math.max(...existingLevels.filter(l => l > 0), 0);
    availableLevels.push(maxLevel + 1);
    return availableLevels.filter(level => level !== 0);
  };
  const getSendBackAllowedLevels = (currentLevel) => {
    const existingLevels = Object.keys(workflowData)
      .map((level) => parseInt(level))
      .sort((a, b) => a - b);
    if (currentLevel === "0" || currentLevel === 0) {
      return existingLevels.filter(level => level !== 0);
    }
    return existingLevels.filter(level => level < parseInt(currentLevel));
  };
  const getSortedLevels = () => {
    const levels = Object.keys(workflowData).map(level => parseInt(level));
    const finalLevel = levels.filter(level => level === 0);
    const otherLevels = levels.filter(level => level !== 0).sort((a, b) => a - b);
    return [...otherLevels, ...finalLevel];
  };
  const findTaskLevel = (taskId) => {
    for (const level in workflowData) {
      if (workflowData[level]?.tasks?.find(t => t.id === taskId)) {
        return level;
      }
    }
    return null;
  };
  const checkLevelNameChange = (levelNumber, newLevelName, callback) => {
    const currentLevelName = workflowData[levelNumber]?.levelName;
    if (currentLevelName && currentLevelName !== newLevelName && workflowData[levelNumber]?.tasks?.length > 0) {
      setLevelNameChangeData({
        levelNumber,
        currentLevelName,
        newLevelName,
        callback
      });
      setIsLevelNameChangeModalVisible(true);
    } else {
      callback();
    }
  };
  const checkBenchStatusChange = (levelNumber, newBenchStatus, callback) => {
    const currentLevel = workflowData[levelNumber];
    if (currentLevel?.tasks?.length > 0) {
        const existingBenchStatus = currentLevel.requestBenchStatus;
        if (existingBenchStatus && existingBenchStatus !== newBenchStatus) {
        setBenchStatusChangeData({
            levelNumber,
            currentBenchStatus: existingBenchStatus,
            newBenchStatus,
            callback,
            updateAllTasks: true // Flag to indicate we need to update all tasks
        });
        setIsBenchStatusChangeModalVisible(true);
        } else {
        callback();
        }
    } else {
        callback();
    }
  };
  const handleLevelNameChangeConfirm = () => {
    if (levelNameChangeData?.callback) {
      levelNameChangeData.callback();
    }
    setIsLevelNameChangeModalVisible(false);
    setLevelNameChangeData(null);
  };
  const handleBenchStatusChangeConfirm = () => {
    if (benchStatusChangeData?.callback) {
        if (benchStatusChangeData.updateAllTasks) {
        setWorkflowData(prev => {
            const updated = { ...prev };
            const level = benchStatusChangeData.levelNumber;
            if (updated[level]) {
            updated[level].tasks = updated[level].tasks.map(task => ({
                ...task,
                requestBenchStatus: benchStatusChangeData.newBenchStatus
            }));
            if (selectedCard && findTaskLevel(selectedCard.id) === level.toString()) {
                setSelectedCard(prev => ({
                ...prev,
                requestBenchStatus: benchStatusChangeData.newBenchStatus
                }));
            }
            }
            return updated;
        });
        }
        benchStatusChangeData.callback();
    }
    setIsBenchStatusChangeModalVisible(false);
    setBenchStatusChangeData(null);
  };
  const handleAddTask = (values) => {
    const addTaskLogic = () => {
        const newTask = {
        id: `task-${Date.now()}`,
        workflowTaskName: values.workflowTaskName,
        requestBenchStatus: values.requestBenchStatus || WORKFLOW_DATA_CONSTANTS?.TASK_DESC,
        isFixed: false,
        workflowGroup: values.workflowGroup,
        slaHigh: values.slaHigh || 24,
        slaMedium: values.slaMedium || 48,
        slaLow: values.slaLow || 72,
        sendBackAllowedTo: values.sendBackAllowedTo || []
        };
        setWorkflowData(prev => {
        const updated = { ...prev };
        const level = values.levelNumber;

        if (!updated[level]) {
            updated[level] = {
            levelName: values.levelName || `${WORKFLOW_DATA_CONSTANTS?.LEVEL} ${level}`,
            requestBenchStatus: values.requestBenchStatus || WORKFLOW_DATA_CONSTANTS?.TASK_DESC,
            tasks: []
            };
        } else {
            if (values.levelName) {
            updated[level].levelName = values.levelName;
            }
            if (values.requestBenchStatus) {
            updated[level].requestBenchStatus = values.requestBenchStatus;
            updated[level].tasks = updated[level].tasks.map(task => ({
                ...task,
                requestBenchStatus: values.requestBenchStatus
            }));
            }
        }
        updated[level]?.tasks?.push(newTask);
        return updated;
        });
        if (selectedCard && findTaskLevel(selectedCard.id) === values.levelNumber.toString()) {
        setSelectedCard(prev => ({
            ...prev,
            requestBenchStatus: values.requestBenchStatus || WORKFLOW_DATA_CONSTANTS?.TASK_DESC
        }));
        }

        setIsAddModalVisible(false);
        addForm.resetFields();
        notification.success({
        message: WORKFLOW_DATA_CONSTANTS.SUCCESS_MSG,
        description: `"${values.workflowTaskName}" ${WORKFLOW_DATA_CONSTANTS.SUCCESS_DESC} ${values.levelNumber}`,
        duration: 3
        });
    };
    if (values.levelName || values.requestBenchStatus) {
        const checkBenchStatusIfNeeded = () => {
        if (values.requestBenchStatus) {
            checkBenchStatusChange(values.levelNumber, values.requestBenchStatus, addTaskLogic);
        } else {
            addTaskLogic();
        }
        };

        if (values.levelName) {
        checkLevelNameChange(values.levelNumber, values.levelName, checkBenchStatusIfNeeded);
        } else {
        checkBenchStatusIfNeeded();
        }
    } else {
        addTaskLogic();
    }
  };
  const handleDeleteTask = (level, taskId) => {
    const task = workflowData[level]?.tasks.find(t => t.id === taskId);
    const currentLevelTaskCount = workflowData[level]?.tasks?.length || 0;
    setWorkflowData(prev => {
      const updated = { ...prev };

      if (updated[level]) {
        updated[level].tasks = updated[level].tasks.filter(t => t.id !== taskId);
        if (updated[level].tasks.length === 0 && level !== '-1' && level !== 0) {
          delete updated[level];
          if (parseInt(level) > 0) {
            return reAdjustLevels(updated);
          }
        }
      }
      return updated;
    });
    if (selectedCard?.id === taskId) {
      setSelectedCard(workflowData['-1']?.tasks[0]);
    }
    const levelWillBeRemoved = currentLevelTaskCount === 1 && parseInt(level) > 0;
    notification.success({
      message: levelWillBeRemoved ?WORKFLOW_DATA_CONSTANTS.LEVELWILLBEREMOVED_MSG : WORKFLOW_DATA_CONSTANTS.LEVMOV_MSG_TASK_DEL,
      description: levelWillBeRemoved
        ? `"${task?.workflowTaskName}" ${WORKFLOW_DATA_CONSTANTS.LELMOV_DESC}`
        : `"${task?.workflowTaskName}" ${WORKFLOW_DATA_CONSTANTS.LELMOV_DESC_DEL}`
    });
  };
  const handleMoveTask = (values) => {
    const { newLevel } = values;
    const currentLevel = findTaskLevel(cardToMove.id);
    const currentLevelTaskCount = workflowData[currentLevel]?.tasks?.length || 0;
    if (currentLevel === newLevel.toString()) {
      notification.warning({
        message: WORKFLOW_DATA_CONSTANTS.HANDLE_MSG,
        description:  WORKFLOW_DATA_CONSTANTS.HANDLE_DESC
      });
      return;
    }
    setWorkflowData(prev => {
      const updated = { ...prev };
      const levelWillBeEmpty = currentLevelTaskCount === 1 && parseInt(currentLevel) > 0;

      if (updated[currentLevel]) {
        updated[currentLevel].tasks = updated[currentLevel].tasks.filter(t => t.id !== cardToMove.id);
        if (updated[currentLevel].tasks.length === 0 && currentLevel !== '-1' && currentLevel !== 0) {
          delete updated[currentLevel];
        }
      }
      if (!updated[newLevel]) {
        updated[newLevel] = {
          levelName: `${WORKFLOW_DATA_CONSTANTS?.LEVEL} ${newLevel}`,
          tasks: []
        };
      }
      updated[newLevel].tasks.push(cardToMove);
      if (levelWillBeEmpty) {
        return reAdjustLevels(updated);
      }
      return updated;
    });
    setIsMoveModalVisible(false);
    setCardToMove(null);
    moveForm.resetFields();
    const levelWasReAdjusted = currentLevelTaskCount === 1 && parseInt(currentLevel) > 0;
    notification.success({
      message: levelWasReAdjusted ? WORKFLOW_DATA_CONSTANTS.LEVELADJUST_MSG : WORKFLOW_DATA_CONSTANTS.LEVELADJUST_MSG_MSGS,
      description: levelWasReAdjusted
        ? `"${cardToMove.workflowTaskName}" ${ WORKFLOW_DATA_CONSTANTS.LEVELADJUST_DESC_MOV}${newLevel} ${WORKFLOW_DATA_CONSTANTS.LEVELADJUST_DESC_LEV}`
        : `"${cardToMove.workflowTaskName}"${ WORKFLOW_DATA_CONSTANTS.LEVELADJUST_DESC_MOV} ${newLevel}`
    });
  };
  const handleEditTask = (task) => {
    setCardToEdit(task);
    editDialogForm.setFieldsValue({
      workflowTaskName: task.workflowTaskName,
      requestBenchStatus: task.requestBenchStatus,
      workflowGroup: task.workflowGroup,
      slaHigh: task.slaHigh,
      slaMedium: task.slaMedium,
      slaLow: task.slaLow,
      sendBackAllowedTo: task.sendBackAllowedTo
    });
    setIsEditModalVisible(true);
  };
  const handleSaveEditedTask = (values) => {
    if (!cardToEdit) return;
    
    const level = findTaskLevel(cardToEdit.id);
    
    const saveTaskLogic = () => {
        setWorkflowData(prev => {
        const updated = { ...prev };
        if (updated[level]) {
            if (values.requestBenchStatus) {
            updated[level].requestBenchStatus = values.requestBenchStatus;
            updated[level].tasks = updated[level].tasks.map(task => ({
                ...task,
                requestBenchStatus: values.requestBenchStatus,
                ...(task.id === cardToEdit.id ? {
                workflowTaskName: values.workflowTaskName,
                workflowGroup: values.workflowGroup,
                slaHigh: values.slaHigh,
                slaMedium: values.slaMedium,
                slaLow: values.slaLow,
                sendBackAllowedTo: values.sendBackAllowedTo
                } : {})
            }));
            } else {
            const taskIndex = updated[level].tasks.findIndex(t => t.id === cardToEdit.id);
            if (taskIndex !== -1) {
                updated[level].tasks[taskIndex] = {
                ...updated[level].tasks[taskIndex],
                workflowTaskName: values.workflowTaskName,
                workflowGroup: values.workflowGroup,
                slaHigh: values.slaHigh,
                slaMedium: values.slaMedium,
                slaLow: values.slaLow,
                sendBackAllowedTo: values.sendBackAllowedTo
                };
            }
            }
        }
        return updated;
        });
        if (selectedCard?.id === cardToEdit.id) {
        const updatedTask = {
            ...cardToEdit,
            workflowTaskName: values.workflowTaskName,
            requestBenchStatus: values.requestBenchStatus,
            workflowGroup: values.workflowGroup,
            slaHigh: values.slaHigh,
            slaMedium: values.slaMedium,
            slaLow: values.slaLow,
            sendBackAllowedTo: values.sendBackAllowedTo
        };
        setSelectedCard(updatedTask);
        } else if (selectedCard && findTaskLevel(selectedCard.id) === level && values.requestBenchStatus) {
        setSelectedCard(prev => ({
            ...prev,
            requestBenchStatus: values.requestBenchStatus
        }));
        }

        setIsEditModalVisible(false);
        setCardToEdit(null);
        editDialogForm.resetFields();

        const affectedTasksCount = workflowData[level]?.tasks?.length || 0;
        const isUpdatingBenchStatus = values.requestBenchStatus !== workflowData[level]?.requestBenchStatus;
        
        notification.success({
        message: WORKFLOW_DATA_CONSTANTS.HANDLE_TASK_MSG,
        description: isUpdatingBenchStatus 
            ? `"${values.workflowTaskName}" updated and Request Bench Status changed for all ${affectedTasksCount} tasks in Level ${level}`
            : `"${values.workflowTaskName}"${WORKFLOW_DATA_CONSTANTS.HANDLE_TASK_MSG_DESC}`
        });
    };
    const currentBenchStatus = workflowData[level]?.requestBenchStatus;
    if (values.requestBenchStatus !== currentBenchStatus) {
        checkBenchStatusChange(level, values.requestBenchStatus, saveTaskLogic);
    } else {
        saveTaskLogic();
    }
  };
  const handleCardClick = (task) => {
    setSelectedCard(task);
    if (!task.isFixed) {
      editForm.setFieldsValue({
        workflowTaskName: task.workflowTaskName,
        requestBenchStatus: task.requestBenchStatus,
        workflowGroup: task.workflowGroup,
        slaHigh: task.slaHigh,
        slaMedium: task.slaMedium,
        slaLow: task.slaLow,
        sendBackAllowedTo: task.sendBackAllowedTo
      });
    }
  };
  const fetchApproverGroups = () => {
    const hSuccess = (data) => {
      const groupNames = data?.data?.map(item => item.groupName);
      setWorkflowGroups(groupNames);
    };
    const hError = () => {};
    doAjax( `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.GET_GRP_NAMES}`, "get", hSuccess, hError);
  };
  const handleLevelNumberChange = (levelNumber) => {
    const existingLevelName = workflowData[levelNumber]?.levelName;
    const existingBenchStatus = workflowData[levelNumber]?.requestBenchStatus;
    if (existingLevelName) {
        addForm.setFieldsValue({ levelName: existingLevelName });
    } else {
        addForm.setFieldsValue({ levelName: '' });
    }
    if (existingBenchStatus) {
        addForm.setFieldsValue({ requestBenchStatus: existingBenchStatus });
    } else {
        addForm.setFieldsValue({ requestBenchStatus: '' });
    }
    addForm.setFieldsValue({ sendBackAllowedTo: [] });
    setSelectedLevel(existingLevelName || "");
  };
  const handleSubmitWorkflow = () => {
    setBlurLoading(true);
    const payload = createPayloadForWF(headerData, workflowData, completeData?.module);
    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(data?.data, "success");
      navigate(-1);
    };
    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(error?.data, "error");
    };
    const url = mode === WORKFLOW_DATA_CONSTANTS.MODES.EDIT ? `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.CHANGE_SUBMIT_WF}` : `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.CREATE_SUBMIT_WF}`;
    doAjax(url, "post", hSuccess, hError, payload);
  };
  const fetchChangeLogDetails = () => {
    setBlurLoading(true);
    const payload = { workflowId: headerData?.workflowId };
    const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.FETCH_CHANGELOG}`;
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (data?.statusCode === API_CODE.STATUS_200) {
        const dataWithIds = data?.data?.map((item, index) => ({
          ...item,
          id: item.id || index + 1
        }));
        setChangeLogData(dataWithIds);
        setOpenChangeLog(true);
      }
      else {
        setChangeLogData([]);
        setOpenChangeLog(true);
      }
    };
    const hError = () => {
      setBlurLoading(false);
    };
    doAjax(newUrl, "post", hSuccess, hError, payload);
  };
  const handleChangeLogClose = () => {
    setOpenChangeLog(false);
  };

  return (
    <Layout style={{ minHeight: '80vh' }}>
      <Content style={{ 
        padding: '25px', 
        overflow: 'auto', 
        backgroundColor: 'white',
        transition: 'all 0.3s ease'
      }}>
        <WorkflowHeader
          mode={mode}
          navigate={navigate}
          headerData={headerData}
          module={completeData?.module || MODULE_MAP?.MAT}
          fetchChangeLogDetails={fetchChangeLogDetails}
        />
        <WorkflowDisplay
          workflowData={workflowData}
          selectedCard={selectedCard}
          handleCardClick={handleCardClick}
          mode={mode}
          handleEditTask={handleEditTask}
          setCardToMove={setCardToMove}
          setIsMoveModalVisible={setIsMoveModalVisible}
          handleDeleteTask={handleDeleteTask}
          getSortedLevels={getSortedLevels}
        />
      </Content>
      <Sider
        width={isRightPanelCollapsed ? 60 : 380}
        collapsedWidth={60}
        theme="light"
        style={{
          borderLeft: '1px solid #e8e8e8',
          backgroundColor: 'white',
          transition: 'all 0.3s ease',
          overflow: 'hidden'
        }}
      >
        <WorkflowRightPanel
          selectedCard={selectedCard}
          findTaskLevel={findTaskLevel}
          mode={mode}
          setIsAddModalVisible={setIsAddModalVisible}
          isCollapsed={isRightPanelCollapsed}
          onToggleCollapse={toggleRightPanel}
        />
      </Sider>
      <WorkflowModals
        workflowData={workflowData}
        isAddModalVisible={isAddModalVisible}
        setIsAddModalVisible={setIsAddModalVisible}
        addForm={addForm}
        handleAddTask={handleAddTask}
        getAvailableLevels={getAvailableLevels}
        handleLevelNumberChange={handleLevelNumberChange}
        selectedLevel={selectedLevel}
        workflowGroups={workflowGroups}
        getSendBackAllowedLevels={getSendBackAllowedLevels}

        isEditModalVisible={isEditModalVisible}
        setIsEditModalVisible={setIsEditModalVisible}
        cardToEdit={cardToEdit}
        setCardToEdit={setCardToEdit}
        editDialogForm={editDialogForm}
        handleSaveEditedTask={handleSaveEditedTask}
        findTaskLevel={findTaskLevel}
        
        isMoveModalVisible={isMoveModalVisible}
        setIsMoveModalVisible={setIsMoveModalVisible}
        cardToMove={cardToMove}
        setCardToMove={setCardToMove}
        moveForm={moveForm}
        handleMoveTask={handleMoveTask}
        
        isLevelNameChangeModalVisible={isLevelNameChangeModalVisible}
        setIsLevelNameChangeModalVisible={setIsLevelNameChangeModalVisible}
        levelNameChangeData={levelNameChangeData}
        setLevelNameChangeData={setLevelNameChangeData}
        handleLevelNameChangeConfirm={handleLevelNameChangeConfirm}

        isBenchStatusChangeModalVisible={isBenchStatusChangeModalVisible}
        setIsBenchStatusChangeModalVisible={setIsBenchStatusChangeModalVisible}
        benchStatusChangeData={benchStatusChangeData}
        setBenchStatusChangeData={setBenchStatusChangeData}
        handleBenchStatusChangeConfirm={handleBenchStatusChangeConfirm}
      />
      <WorkflowLayout
        handleSubmitWorkflow={handleSubmitWorkflow}
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
        openChangeLog={openChangeLog}
        handleChangeLogClose={handleChangeLogClose}
        changeLogData={changeLogData}
        mode={mode}
      />
    </Layout>
  );
};

export default WorkflowGenerator;