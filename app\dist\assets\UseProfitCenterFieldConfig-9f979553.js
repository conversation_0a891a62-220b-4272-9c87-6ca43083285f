import{s as L,aP as h,n as N,r as G,aX as v,da as F,bI as O,aT as m,C as U,aJ as R,eH as b,eI as q,dI as Q,eJ as j,dc as p,dd as w,aD as V}from"./index-226a1e75.js";const Y=_=>{let E={},D=_==null?void 0:_.sort((a,r)=>a.MDG_PC_SEQUENCE_NO-r.MDG_PC_SEQUENCE_NO);const f=p(D,"MDG_PC_VIEW_NAME");let l=[];Object.entries(f).forEach(([a,r])=>{let C=p(r,"MDG_PC_CARD_NAME"),e=[];Object.entries(C).forEach(([t,o])=>{o.sort((s,i)=>s.MDG_PC_SEQUENCE_NO-i.MDG_PC_SEQUENCE_NO);let P=o.map(s=>({fieldName:s.MDG_PC_UI_FIELD_NAME,sequenceNo:s.MDG_PC_SEQUENCE_NO,fieldType:s.MDG_PC_FIELD_TYPE,maxLength:s.MDG_PC_MAX_LENGTH,dataType:s.MDG_PC_DATA_TYPE,viewName:s.MDG_PC_VIEW_NAME,cardName:s.MDG_PC_CARD_NAME,cardSeq:s.MDG_PC_CARD_SEQUENCE,viewSeq:s.MDG_PC_VIEW_SEQUENCE,value:s.MDG_PC_DEFAULT_VALUE,visibility:s.MDG_PC_VISIBILITY,jsonName:s.MDG_PC_JSON_FIELD_NAME}));e.push({cardName:t,cardSeq:o[0].MDG_PC_CARD_SEQUENCE,cardDetails:P})}),e.sort((t,o)=>t.cardSeq-o.cardSeq),l.push({viewName:a,viewSeq:r[0].MDG_PC_VIEW_SEQUENCE,cards:e})}),l.sort((a,r)=>a.viewSeq-r.viewSeq);let n=w(l),c={};return n.forEach(a=>{let r={};a.cards.forEach(C=>{r[C.cardName]=C.cardDetails,a.viewName!=="Request Header"&&C.cardDetails.forEach(e=>{e.visibility===V.MANDATORY&&(E[e.viewName]||(E[e.viewName]=[]),E[e.viewName].push({jsonName:e==null?void 0:e.jsonName,fieldName:e==null?void 0:e.fieldName}))})}),c[a.viewName]=r}),{transformedData:c,mandatoryFields:E}},x=()=>{const _=L(),{customError:E}=h(),D=N(e=>{var t;return(t=e.payload)==null?void 0:t.payloadData}),f=N(e=>e.applicationConfig);N(e=>e.userManagement.userData),N(e=>e==null?void 0:e.userManagement.taskData);const[l,n]=G.useState(!1),[c,a]=G.useState(null),r=async()=>{n(!0);const e={decisionTableId:null,decisionTableName:"MDG_PC_FIELD_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_PC_SCENARIO":(D==null?void 0:D.RequestType)||v.CREATE,"MDG_CONDITIONS.MDG_PC_ROLE":F.REQ_INITIATE_FIN}],systemFilters:null,systemOrders:null,filterString:null},t=s=>{var i,I,M,S;if(s.statusCode===R.STATUS_200){if(Array.isArray((i=s==null?void 0:s.data)==null?void 0:i.result)&&((I=s==null?void 0:s.data)!=null&&I.result.every(u=>Object.keys(u).length!==0))){let u=(S=(M=s==null?void 0:s.data)==null?void 0:M.result[0])==null?void 0:S.MDG_PC_FIELD_DETAILS_ACTION_TYPE;const{transformedData:T,mandatoryFields:y}=Y(u);let A=Object.keys(T);const g=A.map(d=>({tab:d,data:T[d]}));_(b(g)),_(q({ProfitCenter:{allfields:Q(A),mandatoryFields:y}}))}else _(j({ProfitCenter:{}}));n(!1)}},o=s=>{E(s),a(s),n(!1)},P=f.environment==="localhost"?`/${O}${m.INVOKE_RULES.LOCAL}`:`/${O}${m.INVOKE_RULES.PROD}`;U(P,"post",t,o,e)};return{loading:l,error:c,fetchProfitCenterFieldConfig:()=>{try{r()}catch(e){a(e),n(!1)}}}};export{x as u};
