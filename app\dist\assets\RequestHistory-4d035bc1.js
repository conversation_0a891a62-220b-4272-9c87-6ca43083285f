import{c9 as pn,ca as mn,cb as Ft,r as w,j as f,O as F,c as C,ag as Lt,B as E,dj as j,aZ as $,b as wo,ej as Ao,cq as _t,Z as y,d as M,yw as Eo,yx as <PERSON>,yy as <PERSON>,yz as <PERSON>,yA as me,F as nt,cs as Vo,bE as Wt,yB as Ye,hZ as Ro,yC as Fo,dr as Lo,an as ge,aF as ko,bS as Io,yD as Bo,T as bt,gd as Ee,gk as Oo,yE as Li,yF as No,yG as jo,u as Uo,y3 as zo,cZ as Wo,n as $o,g as _o,bd as Yo,ai as ms,aj as gs,$ as ys,af as De,al as xs,N as Ko,Q as Go,bC as Ho,a6 as At,a_ as qo,a$ as Xo,ds as Zo,fM as Qo,bf as vs,C as Pe,aT as Me,yH as Jo,gA as ta,ak as bs,aE as ea}from"./index-226a1e75.js";import{b as na}from"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";var gn={},sa=mn;Object.defineProperty(gn,"__esModule",{value:!0});var ki=gn.default=void 0,ia=sa(pn()),ra=Ft;ki=gn.default=(0,ia.default)((0,ra.jsx)("path",{d:"M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2m0 4-8 5-8-5V6l8 5 8-5z"}),"Email");var yn={},oa=mn;Object.defineProperty(yn,"__esModule",{value:!0});var Ii=yn.default=void 0,aa=oa(pn()),la=Ft;Ii=yn.default=(0,aa.default)((0,la.jsx)("path",{d:"M16.5 6v11.5c0 2.21-1.79 4-4 4s-4-1.79-4-4V5c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v10.5c0 .55-.45 1-1 1s-1-.45-1-1V6H10v9.5c0 1.38 1.12 2.5 2.5 2.5s2.5-1.12 2.5-2.5V5c0-2.21-1.79-4-4-4S7 2.79 7 5v12.5c0 3.04 2.46 5.5 5.5 5.5s5.5-2.46 5.5-5.5V6z"}),"AttachFileOutlined");const Bi=w.createContext({});function ca(t){const e=w.useRef(null);return e.current===null&&(e.current=t()),e.current}const xn=typeof window<"u",ua=xn?w.useLayoutEffect:w.useEffect,vn=w.createContext(null);function bn(t,e){t.indexOf(e)===-1&&t.push(e)}function Tn(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const lt=(t,e,n)=>n>e?e:n<t?t:n;let Sn=()=>{};const ct={},Oi=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function Ni(t){return typeof t=="object"&&t!==null}const ji=t=>/^0[^.\s]+$/u.test(t);function Cn(t){let e;return()=>(e===void 0&&(e=t()),e)}const Q=t=>t,ha=(t,e)=>n=>e(t(n)),ne=(...t)=>t.reduce(ha),Xt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class wn{constructor(){this.subscriptions=[]}add(e){return bn(this.subscriptions,e),()=>Tn(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const it=t=>t*1e3,rt=t=>t/1e3;function Ui(t,e){return e?t*(1e3/e):0}const zi=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,da=1e-7,fa=12;function pa(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=zi(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>da&&++a<fa);return r}function se(t,e,n,s){if(t===e&&n===s)return Q;const i=o=>pa(o,0,1,t,n);return o=>o===0||o===1?o:zi(i(o),e,s)}const Wi=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,$i=t=>e=>1-t(1-e),_i=se(.33,1.53,.69,.99),An=$i(_i),Yi=Wi(An),Ki=t=>(t*=2)<1?.5*An(t):.5*(2-Math.pow(2,-10*(t-1))),En=t=>1-Math.sin(Math.acos(t)),Gi=$i(En),Hi=Wi(En),ma=se(.42,0,1,1),ga=se(0,0,.58,1),qi=se(.42,0,.58,1),ya=t=>Array.isArray(t)&&typeof t[0]!="number",Xi=t=>Array.isArray(t)&&typeof t[0]=="number",xa={linear:Q,easeIn:ma,easeInOut:qi,easeOut:ga,circIn:En,circInOut:Hi,circOut:Gi,backIn:An,backInOut:Yi,backOut:_i,anticipate:Ki},va=t=>typeof t=="string",Ts=t=>{if(Xi(t)){Sn(t.length===4);const[e,n,s,i]=t;return se(e,n,s,i)}else if(va(t))return xa[t];return t},le=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],Ss={value:null,addProjectionMetrics:null};function ba(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(h){r.has(h)&&(u.schedule(h),t()),l++,h(a)}const u={schedule:(h,d=!1,m=!1)=>{const g=m&&i?n:s;return d&&r.add(h),g.has(h)||g.add(h),h},cancel:h=>{s.delete(h),r.delete(h)},process:h=>{if(a=h,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(c),e&&Ss.value&&Ss.value.frameloop[e].push(l),l=0,n.clear(),i=!1,o&&(o=!1,u.process(h))}};return u}const Ta=40;function Zi(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=le.reduce((v,P)=>(v[P]=ba(o,e?P:void 0),v),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:d,render:m,postRender:x}=r,g=()=>{const v=ct.useManualTiming?i.timestamp:performance.now();n=!1,ct.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(v-i.timestamp,Ta),1)),i.timestamp=v,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),d.process(i),m.process(i),x.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(g))},b=()=>{n=!0,s=!0,i.isProcessing||t(g)};return{schedule:le.reduce((v,P)=>{const A=r[P];return v[P]=(R,B=!1,V=!1)=>(n||b(),A.schedule(R,B,V)),v},{}),cancel:v=>{for(let P=0;P<le.length;P++)r[le[P]].cancel(v)},state:i,steps:r}}const{schedule:L,cancel:pt,state:z,steps:Ve}=Zi(typeof requestAnimationFrame<"u"?requestAnimationFrame:Q,!0);let he;function Sa(){he=void 0}const G={now:()=>(he===void 0&&G.set(z.isProcessing||ct.useManualTiming?z.timestamp:performance.now()),he),set:t=>{he=t,queueMicrotask(Sa)}},Qi=t=>e=>typeof e=="string"&&e.startsWith(t),Dn=Qi("--"),Ca=Qi("var(--"),Pn=t=>Ca(t)?wa.test(t.split("/*")[0].trim()):!1,wa=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Bt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Zt={...Bt,transform:t=>lt(0,1,t)},ce={...Bt,default:1},Yt=t=>Math.round(t*1e5)/1e5,Mn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Aa(t){return t==null}const Ea=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Vn=(t,e)=>n=>!!(typeof n=="string"&&Ea.test(n)&&n.startsWith(t)||e&&!Aa(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ji=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(Mn);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},Da=t=>lt(0,255,t),Re={...Bt,transform:t=>Math.round(Da(t))},Tt={test:Vn("rgb","red"),parse:Ji("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Re.transform(t)+", "+Re.transform(e)+", "+Re.transform(n)+", "+Yt(Zt.transform(s))+")"};function Pa(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const Ke={test:Vn("#"),parse:Pa,transform:Tt.transform},ie=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),ft=ie("deg"),ot=ie("%"),D=ie("px"),Ma=ie("vh"),Va=ie("vw"),Cs=(()=>({...ot,parse:t=>ot.parse(t)/100,transform:t=>ot.transform(t*100)}))(),Et={test:Vn("hsl","hue"),parse:Ji("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+ot.transform(Yt(e))+", "+ot.transform(Yt(n))+", "+Yt(Zt.transform(s))+")"},U={test:t=>Tt.test(t)||Ke.test(t)||Et.test(t),parse:t=>Tt.test(t)?Tt.parse(t):Et.test(t)?Et.parse(t):Ke.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?Tt.transform(t):Et.transform(t),getAnimatableNone:t=>{const e=U.parse(t);return e.alpha=0,U.transform(e)}},Ra=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Fa(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Mn))==null?void 0:e.length)||0)+(((n=t.match(Ra))==null?void 0:n.length)||0)>0}const tr="number",er="color",La="var",ka="var(",ws="${}",Ia=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Qt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(Ia,l=>(U.test(l)?(s.color.push(o),i.push(er),n.push(U.parse(l))):l.startsWith(ka)?(s.var.push(o),i.push(La),n.push(l)):(s.number.push(o),i.push(tr),n.push(parseFloat(l))),++o,ws)).split(ws);return{values:n,split:a,indexes:s,types:i}}function nr(t){return Qt(t).values}function sr(t){const{split:e,types:n}=Qt(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===tr?o+=Yt(i[r]):a===er?o+=U.transform(i[r]):o+=i[r]}return o}}const Ba=t=>typeof t=="number"?0:U.test(t)?U.getAnimatableNone(t):t;function Oa(t){const e=nr(t);return sr(t)(e.map(Ba))}const mt={test:Fa,parse:nr,createTransformer:sr,getAnimatableNone:Oa};function Fe(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Na({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=Fe(l,a,t+1/3),o=Fe(l,a,t),r=Fe(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function ye(t,e){return n=>n>0?e:t}const k=(t,e,n)=>t+(e-t)*n,Le=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},ja=[Ke,Tt,Et],Ua=t=>ja.find(e=>e.test(t));function As(t){const e=Ua(t);if(!e)return!1;let n=e.parse(t);return e===Et&&(n=Na(n)),n}const Es=(t,e)=>{const n=As(t),s=As(e);if(!n||!s)return ye(t,e);const i={...n};return o=>(i.red=Le(n.red,s.red,o),i.green=Le(n.green,s.green,o),i.blue=Le(n.blue,s.blue,o),i.alpha=k(n.alpha,s.alpha,o),Tt.transform(i))},Ge=new Set(["none","hidden"]);function za(t,e){return Ge.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Wa(t,e){return n=>k(t,e,n)}function Rn(t){return typeof t=="number"?Wa:typeof t=="string"?Pn(t)?ye:U.test(t)?Es:Ya:Array.isArray(t)?ir:typeof t=="object"?U.test(t)?Es:$a:ye}function ir(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>Rn(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function $a(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=Rn(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function _a(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const Ya=(t,e)=>{const n=mt.createTransformer(e),s=Qt(t),i=Qt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?Ge.has(t)&&!i.values.length||Ge.has(e)&&!s.values.length?za(t,e):ne(ir(_a(s,i),i.values),n):ye(t,e)};function rr(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?k(t,e,n):Rn(t)(t,e)}const Ka=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>L.update(e,n),stop:()=>pt(e),now:()=>z.isProcessing?z.timestamp:G.now()}},or=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=Math.round(t(o/(i-1))*1e4)/1e4+", ";return`linear(${s.substring(0,s.length-2)})`},xe=2e4;function Fn(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<xe;)e+=n,s=t.next(e);return e>=xe?1/0:e}function Ga(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min(Fn(s),xe);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:rt(i)}}const Ha=5;function ar(t,e,n){const s=Math.max(e-Ha,0);return Ui(n-t(s),e-s)}const I={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},ke=.001;function qa({duration:t=I.duration,bounce:e=I.bounce,velocity:n=I.velocity,mass:s=I.mass}){let i,o,r=1-e;r=lt(I.minDamping,I.maxDamping,r),t=lt(I.minDuration,I.maxDuration,rt(t)),r<1?(i=c=>{const u=c*r,h=u*t,d=u-n,m=He(c,r),x=Math.exp(-h);return ke-d/m*x},o=c=>{const h=c*r*t,d=h*n+n,m=Math.pow(r,2)*Math.pow(c,2)*t,x=Math.exp(-h),g=He(Math.pow(c,2),r);return(-i(c)+ke>0?-1:1)*((d-m)*x)/g}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-ke+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Za(i,o,a);if(t=it(t),isNaN(l))return{stiffness:I.stiffness,damping:I.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const Xa=12;function Za(t,e,n){let s=n;for(let i=1;i<Xa;i++)s=s-t(s)/e(s);return s}function He(t,e){return t*Math.sqrt(1-e*e)}const Qa=["duration","bounce"],Ja=["stiffness","damping","mass"];function Ds(t,e){return e.some(n=>t[n]!==void 0)}function tl(t){let e={velocity:I.velocity,stiffness:I.stiffness,damping:I.damping,mass:I.mass,isResolvedFromDuration:!1,...t};if(!Ds(t,Ja)&&Ds(t,Qa))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*lt(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:I.mass,stiffness:i,damping:o}}else{const n=qa(t);e={...e,...n,mass:I.mass},e.isResolvedFromDuration=!0}return e}function ve(t=I.visualDuration,e=I.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:d,isResolvedFromDuration:m}=tl({...n,velocity:-rt(n.velocity||0)}),x=d||0,g=c/(2*Math.sqrt(l*u)),b=r-o,p=rt(Math.sqrt(l/u)),S=Math.abs(b)<5;s||(s=S?I.restSpeed.granular:I.restSpeed.default),i||(i=S?I.restDelta.granular:I.restDelta.default);let v;if(g<1){const A=He(p,g);v=R=>{const B=Math.exp(-g*p*R);return r-B*((x+g*p*b)/A*Math.sin(A*R)+b*Math.cos(A*R))}}else if(g===1)v=A=>r-Math.exp(-p*A)*(b+(x+p*b)*A);else{const A=p*Math.sqrt(g*g-1);v=R=>{const B=Math.exp(-g*p*R),V=Math.min(A*R,300);return r-B*((x+g*p*b)*Math.sinh(V)+A*b*Math.cosh(V))/A}}const P={calculatedDuration:m&&h||null,next:A=>{const R=v(A);if(m)a.done=A>=h;else{let B=A===0?x:0;g<1&&(B=A===0?it(x):ar(v,A,R));const V=Math.abs(B)<=s,W=Math.abs(r-R)<=i;a.done=V&&W}return a.value=a.done?r:R,a},toString:()=>{const A=Math.min(Fn(P),xe),R=or(B=>P.next(A*B).value,A,30);return A+"ms "+R},toTransition:()=>{}};return P}ve.applyToOptions=t=>{const e=Ga(t,100,ve);return t.ease=e.ease,t.duration=it(e.duration),t.type="keyframes",t};function qe({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],d={done:!1,value:h},m=V=>a!==void 0&&V<a||l!==void 0&&V>l,x=V=>a===void 0?l:l===void 0||Math.abs(a-V)<Math.abs(l-V)?a:l;let g=n*e;const b=h+g,p=r===void 0?b:r(b);p!==b&&(g=p-h);const S=V=>-g*Math.exp(-V/s),v=V=>p+S(V),P=V=>{const W=S(V),H=v(V);d.done=Math.abs(W)<=c,d.value=d.done?p:H};let A,R;const B=V=>{m(d.value)&&(A=V,R=ve({keyframes:[d.value,x(d.value)],velocity:ar(v,V,d.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return B(0),{calculatedDuration:null,next:V=>{let W=!1;return!R&&A===void 0&&(W=!0,P(V),B(V)),A!==void 0&&V>=A?R.next(V-A):(!W&&P(V),d)}}}function el(t,e,n){const s=[],i=n||ct.mix||rr,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||Q:e;a=ne(l,a)}s.push(a)}return s}function nl(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(Sn(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=el(e,s,i),l=a.length,c=u=>{if(r&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const d=Xt(t[h],t[h+1],u);return a[h](d)};return n?u=>c(lt(t[0],t[o-1],u)):c}function sl(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=Xt(0,e,s);t.push(k(n,1,i))}}function il(t){const e=[0];return sl(e,t.length-1),e}function rl(t,e){return t.map(n=>n*e)}function ol(t,e){return t.map(()=>e||qi).splice(0,t.length-1)}function Kt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=ya(s)?s.map(Ts):Ts(s),o={done:!1,value:e[0]},r=rl(n&&n.length===e.length?n:il(e),t),a=nl(r,e,{ease:Array.isArray(i)?i:ol(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const al=t=>t!==null;function Ln(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(al),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const ll={decay:qe,inertia:qe,tween:Kt,keyframes:Kt,spring:ve};function lr(t){typeof t.type=="string"&&(t.type=ll[t.type])}class kn{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const cl=t=>t/100;class In extends kn{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{var s,i;const{motionValue:n}=this.options;n&&n.updatedAt!==G.now()&&this.tick(G.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(s=this.options).onStop)==null||i.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;lr(e);const{type:n=Kt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Kt;l!==Kt&&typeof a[0]!="number"&&(this.mixKeyframes=ne(cl,rr(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=Fn(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:m,type:x,onUpdate:g,finalKeyframe:b}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const p=this.currentTime-c*(this.playbackSpeed>=0?1:-1),S=this.playbackSpeed>=0?p<0:p>i;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let v=this.currentTime,P=s;if(h){const V=Math.min(this.currentTime,i)/a;let W=Math.floor(V),H=V%1;!H&&V>=1&&(H=1),H===1&&W--,W=Math.min(W,h+1),!!(W%2)&&(d==="reverse"?(H=1-H,m&&(H-=m/a)):d==="mirror"&&(P=r)),v=lt(0,1,H)*a}const A=S?{done:!1,value:u[0]}:P.next(v);o&&(A.value=o(A.value));let{done:R}=A;!S&&l!==null&&(R=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const B=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&R);return B&&x!==qe&&(A.value=Ln(u,this.options,b,this.speed)),g&&g(A.value),B&&this.finish(),A}then(e,n){return this.finished.then(e,n)}get duration(){return rt(this.calculatedDuration)}get time(){return rt(this.currentTime)}set time(e){var n;e=it(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(G.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=rt(this.currentTime))}play(){var i,o;if(this.isStopped)return;const{driver:e=Ka,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(i=this.options).onPlay)==null||o.call(i);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(G.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function ul(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const St=t=>t*180/Math.PI,Xe=t=>{const e=St(Math.atan2(t[1],t[0]));return Ze(e)},hl={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Xe,rotateZ:Xe,skewX:t=>St(Math.atan(t[1])),skewY:t=>St(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},Ze=t=>(t=t%360,t<0&&(t+=360),t),Ps=Xe,Ms=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),Vs=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),dl={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Ms,scaleY:Vs,scale:t=>(Ms(t)+Vs(t))/2,rotateX:t=>Ze(St(Math.atan2(t[6],t[5]))),rotateY:t=>Ze(St(Math.atan2(-t[2],t[0]))),rotateZ:Ps,rotate:Ps,skewX:t=>St(Math.atan(t[4])),skewY:t=>St(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Qe(t){return t.includes("scale")?1:0}function Je(t,e){if(!t||t==="none")return Qe(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=dl,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=hl,i=a}if(!i)return Qe(e);const o=s[e],r=i[1].split(",").map(pl);return typeof o=="function"?o(r):r[o]}const fl=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return Je(n,e)};function pl(t){return parseFloat(t.trim())}const Ot=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Nt=(()=>new Set(Ot))(),Rs=t=>t===Bt||t===D,ml=new Set(["x","y","z"]),gl=Ot.filter(t=>!ml.has(t));function yl(t){const e=[];return gl.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const Ct={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>Je(e,"x"),y:(t,{transform:e})=>Je(e,"y")};Ct.translateX=Ct.x;Ct.translateY=Ct.y;const wt=new Set;let tn=!1,en=!1,nn=!1;function cr(){if(en){const t=Array.from(wt).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=yl(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}en=!1,tn=!1,wt.forEach(t=>t.complete(nn)),wt.clear()}function ur(){wt.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(en=!0)})}function xl(){nn=!0,ur(),cr(),nn=!1}class Bn{constructor(e,n,s,i,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(wt.add(this),tn||(tn=!0,L.read(ur),L.resolveKeyframes(cr))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i==null?void 0:i.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}ul(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),wt.delete(this)}cancel(){this.state==="scheduled"&&(wt.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const vl=t=>t.startsWith("--");function bl(t,e,n){vl(e)?t.style.setProperty(e,n):t.style[e]=n}const Tl=Cn(()=>window.ScrollTimeline!==void 0),Sl={};function Cl(t,e){const n=Cn(t);return()=>Sl[e]??n()}const hr=Cl(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),$t=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,Fs={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:$t([0,.65,.55,1]),circOut:$t([.55,0,1,.45]),backIn:$t([.31,.01,.66,-.59]),backOut:$t([.33,1.53,.69,.99])};function dr(t,e){if(t)return typeof t=="function"?hr()?or(t,e):"ease-out":Xi(t)?$t(t):Array.isArray(t)?t.map(n=>dr(n,e)||Fs.easeOut):Fs[t]}function wl(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=dr(a,i);Array.isArray(h)&&(u.easing=h);const d={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return c&&(d.pseudoElement=c),t.animate(u,d)}function fr(t){return typeof t=="function"&&"applyToOptions"in t}function Al({type:t,...e}){return fr(t)&&hr()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class El extends kn{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,Sn(typeof e.type!="string");const c=Al(e);this.animation=wl(n,s,i,c,o),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=Ln(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):bl(n,s,u),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return rt(Number(e))}get time(){return rt(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=it(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&Tl()?(this.animation.timeline=e,Q):n(this)}}const pr={anticipate:Ki,backInOut:Yi,circInOut:Hi};function Dl(t){return t in pr}function Pl(t){typeof t.ease=="string"&&Dl(t.ease)&&(t.ease=pr[t.ease])}const Ls=10;class Ml extends El{constructor(e){Pl(e),lr(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new In({...r,autoplay:!1}),l=it(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-Ls).value,a.sample(l).value,Ls),a.stop()}}const ks=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(mt.test(t)||t==="0")&&!t.startsWith("url("));function Vl(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function Rl(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=ks(i,e),a=ks(o,e);return!r||!a?!1:Vl(t)||(n==="spring"||fr(n))&&s}function sn(t){t.duration=0,t.type}const Fl=new Set(["opacity","clipPath","filter","transform"]),Ll=Cn(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function kl(t){var u;const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!(((u=e==null?void 0:e.owner)==null?void 0:u.current)instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:c}=e.owner.getProps();return Ll()&&n&&Fl.has(n)&&(n!=="transform"||!c)&&!l&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const Il=40;class Bl extends kn{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var x;super(),this.stop=()=>{var g,b;this._animation&&(this._animation.stop(),(g=this.stopTimeline)==null||g.call(this)),(b=this.keyframeResolver)==null||b.cancel()},this.createdAt=G.now();const d={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:l,motionValue:c,element:u,...h},m=(u==null?void 0:u.KeyframeResolver)||Bn;this.keyframeResolver=new m(a,(g,b,p)=>this.onKeyframesResolved(g,b,d,!p),l,c,u),(x=this.keyframeResolver)==null||x.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:c,onUpdate:u}=s;this.resolvedAt=G.now(),Rl(e,o,r,a)||((ct.instantAnimations||!l)&&(u==null||u(Ln(e,s,n))),e[0]=e[e.length-1],sn(s),s.repeat=0);const d={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Il?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},m=!c&&kl(d)?new Ml({...d,element:d.motionValue.owner.current}):new In(d);m.finished.then(()=>this.notifyFinished()).catch(Q),this.pendingTimeline&&(this.stopTimeline=m.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=m}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),xl()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const Ol=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Nl(t){const e=Ol.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function mr(t,e,n=1){const[s,i]=Nl(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return Oi(r)?parseFloat(r):r}return Pn(i)?mr(i,e,n+1):i}function On(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const gr=new Set(["width","height","top","left","right","bottom",...Ot]),jl={test:t=>t==="auto",parse:t=>t},yr=t=>e=>e.test(t),xr=[Bt,D,ot,ft,Va,Ma,jl],Is=t=>xr.find(yr(t));function Ul(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||ji(t):!0}const zl=new Set(["brightness","contrast","saturate","opacity"]);function Wl(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Mn)||[];if(!s)return t;const i=n.replace(s,"");let o=zl.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const $l=/\b([a-z-]*)\(.*?\)/gu,rn={...mt,getAnimatableNone:t=>{const e=t.match($l);return e?e.map(Wl).join(" "):t}},Bs={...Bt,transform:Math.round},_l={rotate:ft,rotateX:ft,rotateY:ft,rotateZ:ft,scale:ce,scaleX:ce,scaleY:ce,scaleZ:ce,skew:ft,skewX:ft,skewY:ft,distance:D,translateX:D,translateY:D,translateZ:D,x:D,y:D,z:D,perspective:D,transformPerspective:D,opacity:Zt,originX:Cs,originY:Cs,originZ:D},Nn={borderWidth:D,borderTopWidth:D,borderRightWidth:D,borderBottomWidth:D,borderLeftWidth:D,borderRadius:D,radius:D,borderTopLeftRadius:D,borderTopRightRadius:D,borderBottomRightRadius:D,borderBottomLeftRadius:D,width:D,maxWidth:D,height:D,maxHeight:D,top:D,right:D,bottom:D,left:D,padding:D,paddingTop:D,paddingRight:D,paddingBottom:D,paddingLeft:D,margin:D,marginTop:D,marginRight:D,marginBottom:D,marginLeft:D,backgroundPositionX:D,backgroundPositionY:D,..._l,zIndex:Bs,fillOpacity:Zt,strokeOpacity:Zt,numOctaves:Bs},Yl={...Nn,color:U,backgroundColor:U,outlineColor:U,fill:U,stroke:U,borderColor:U,borderTopColor:U,borderRightColor:U,borderBottomColor:U,borderLeftColor:U,filter:rn,WebkitFilter:rn},vr=t=>Yl[t];function br(t,e){let n=vr(t);return n!==rn&&(n=mt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Kl=new Set(["auto","none","0"]);function Gl(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Kl.has(o)&&Qt(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=br(n,i)}class Hl extends Bn{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Pn(c))){const u=mr(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!gr.has(s)||e.length!==2)return;const[i,o]=e,r=Is(i),a=Is(o);if(r!==a)if(Rs(r)&&Rs(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else Ct[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Ul(e[i]))&&s.push(i);s.length&&Gl(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Ct[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=Ct[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{e.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function ql(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;e&&(s=e.current);const i=(n==null?void 0:n[t])??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const Tr=(t,e)=>e&&typeof t=="number"?e.transform(t):t;function Xl(t){return Ni(t)&&"offsetHeight"in t}const Os=30,Zl=t=>!isNaN(parseFloat(t));class Ql{constructor(e,n={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=s=>{var o;const i=G.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((o=this.events.change)==null||o.notify(this.current),this.dependents))for(const r of this.dependents)r.dirty()},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=G.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Zl(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new wn);const s=this.events[e].add(n);return e==="change"?()=>{s(),L.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e){this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=G.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Os)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Os);return Ui(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function kt(t,e){return new Ql(t,e)}const{schedule:jn,cancel:rd}=Zi(queueMicrotask,!1),et={x:!1,y:!1};function Sr(){return et.x||et.y}function Jl(t){return t==="x"||t==="y"?et[t]?null:(et[t]=!0,()=>{et[t]=!1}):et.x||et.y?null:(et.x=et.y=!0,()=>{et.x=et.y=!1})}function Cr(t,e){const n=ql(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function Ns(t){return!(t.pointerType==="touch"||Sr())}function tc(t,e,n={}){const[s,i,o]=Cr(t,n),r=a=>{if(!Ns(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{Ns(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const wr=(t,e)=>e?t===e?!0:wr(t,e.parentElement):!1,Un=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,ec=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function nc(t){return ec.has(t.tagName)||t.tabIndex!==-1}const de=new WeakSet;function js(t){return e=>{e.key==="Enter"&&t(e)}}function Ie(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const sc=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=js(()=>{if(de.has(n))return;Ie(n,"down");const i=js(()=>{Ie(n,"up")}),o=()=>Ie(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function Us(t){return Un(t)&&!Sr()}function ic(t,e,n={}){const[s,i,o]=Cr(t,n),r=a=>{const l=a.currentTarget;if(!Us(a))return;de.add(l);const c=e(l,a),u=(m,x)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",d),de.has(l)&&de.delete(l),Us(m)&&typeof c=="function"&&c(m,{success:x})},h=m=>{u(m,l===window||l===document||n.useGlobalTarget||wr(l,m.target))},d=m=>{u(m,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",d,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),Xl(a)&&(a.addEventListener("focus",c=>sc(c,i)),!nc(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}function Ar(t){return Ni(t)&&"ownerSVGElement"in t}function rc(t){return Ar(t)&&t.tagName==="svg"}const _=t=>!!(t&&t.getVelocity),oc=[...xr,U,mt],ac=t=>oc.find(yr(t)),Er=w.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function lc(t=!0){const e=w.useContext(vn);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=w.useId();w.useEffect(()=>{if(t)return i(o)},[t]);const r=w.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}const Dr=w.createContext({strict:!1}),zs={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},It={};for(const t in zs)It[t]={isEnabled:e=>zs[t].some(n=>!!e[n])};function cc(t){for(const e in t)It[e]={...It[e],...t[e]}}const uc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function be(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||uc.has(t)}let Pr=t=>!be(t);function hc(t){typeof t=="function"&&(Pr=e=>e.startsWith("on")?!be(e):t(e))}try{hc(require("@emotion/is-prop-valid").default)}catch{}function dc(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(Pr(i)||n===!0&&be(i)||!e&&!be(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}const Se=w.createContext({});function Ce(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Jt(t){return typeof t=="string"||Array.isArray(t)}const zn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Wn=["initial",...zn];function we(t){return Ce(t.animate)||Wn.some(e=>Jt(t[e]))}function Mr(t){return!!(we(t)||t.variants)}function fc(t,e){if(we(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Jt(n)?n:void 0,animate:Jt(s)?s:void 0}}return t.inherit!==!1?e:{}}function pc(t){const{initial:e,animate:n}=fc(t,w.useContext(Se));return w.useMemo(()=>({initial:e,animate:n}),[Ws(e),Ws(n)])}function Ws(t){return Array.isArray(t)?t.join(" "):t}const te={};function mc(t){for(const e in t)te[e]=t[e],Dn(e)&&(te[e].isCSSVariable=!0)}function Vr(t,{layout:e,layoutId:n}){return Nt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!te[t]||t==="opacity")}const gc={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},yc=Ot.length;function xc(t,e,n){let s="",i=!0;for(let o=0;o<yc;o++){const r=Ot[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=Tr(a,Nn[r]);if(!l){i=!1;const u=gc[r]||r;s+=`${u}(${c}) `}n&&(e[r]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function $n(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(Nt.has(l)){r=!0;continue}else if(Dn(l)){i[l]=c;continue}else{const u=Tr(c,Nn[l]);l.startsWith("origin")?(a=!0,o[l]=u):s[l]=u}}if(e.transform||(r||n?s.transform=xc(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;s.transformOrigin=`${l} ${c} ${u}`}}const _n=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Rr(t,e,n){for(const s in e)!_(e[s])&&!Vr(s,n)&&(t[s]=e[s])}function vc({transformTemplate:t},e){return w.useMemo(()=>{const n=_n();return $n(n,e,t),Object.assign({},n.vars,n.style)},[e])}function bc(t,e){const n=t.style||{},s={};return Rr(s,n,t),Object.assign(s,vc(t,e)),s}function Tc(t,e){const n={},s=bc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const Sc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Cc={offset:"strokeDashoffset",array:"strokeDasharray"};function wc(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Sc:Cc;t[o.offset]=D.transform(-s);const r=D.transform(e),a=D.transform(n);t[o.array]=`${r} ${a}`}function Fr(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},l,c,u){if($n(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:d}=t;h.transform&&(d.transform=h.transform,delete h.transform),(d.transform||h.transformOrigin)&&(d.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),d.transform&&(d.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&wc(h,i,o,r,!1)}const Lr=()=>({..._n(),attrs:{}}),kr=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Ac(t,e,n,s){const i=w.useMemo(()=>{const o=Lr();return Fr(o,e,kr(s),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Rr(o,t.style,t),i.style={...o,...i.style}}return i}const Ec=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Yn(t){return typeof t!="string"||t.includes("-")?!1:!!(Ec.indexOf(t)>-1||/[A-Z]/u.test(t))}function Dc(t,e,n,{latestValues:s},i,o=!1){const a=(Yn(t)?Ac:Tc)(e,s,i,t),l=dc(e,typeof t=="string",o),c=t!==w.Fragment?{...l,...a,ref:n}:{},{children:u}=e,h=w.useMemo(()=>_(u)?u.get():u,[u]);return w.createElement(t,{...c,children:h})}function $s(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function Kn(t,e,n,s){if(typeof e=="function"){const[i,o]=$s(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=$s(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function fe(t){return _(t)?t.get():t}function Pc({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:Mc(n,s,i,t),renderState:e()}}function Mc(t,e,n,s){const i={},o=s(t,{});for(const d in o)i[d]=fe(o[d]);let{initial:r,animate:a}=t;const l=we(t),c=Mr(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!Ce(h)){const d=Array.isArray(h)?h:[h];for(let m=0;m<d.length;m++){const x=Kn(t,d[m]);if(x){const{transitionEnd:g,transition:b,...p}=x;for(const S in p){let v=p[S];if(Array.isArray(v)){const P=u?v.length-1:0;v=v[P]}v!==null&&(i[S]=v)}for(const S in g)i[S]=g[S]}}}return i}const Ir=t=>(e,n)=>{const s=w.useContext(Se),i=w.useContext(vn),o=()=>Pc(t,e,s,i);return n?o():ca(o)};function Gn(t,e,n){var o;const{style:s}=t,i={};for(const r in s)(_(s[r])||e.style&&_(e.style[r])||Vr(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(i[r]=s[r]);return i}const Vc=Ir({scrapeMotionValuesFromProps:Gn,createRenderState:_n});function Br(t,e,n){const s=Gn(t,e,n);for(const i in t)if(_(t[i])||_(e[i])){const o=Ot.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const Rc=Ir({scrapeMotionValuesFromProps:Br,createRenderState:Lr}),Fc=Symbol.for("motionComponentSymbol");function Dt(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Lc(t,e,n){return w.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):Dt(n)&&(n.current=s))},[e])}const Hn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),kc="framerAppearId",Or="data-"+Hn(kc),Nr=w.createContext({});function Ic(t,e,n,s,i){var g,b;const{visualElement:o}=w.useContext(Se),r=w.useContext(Dr),a=w.useContext(vn),l=w.useContext(Er).reducedMotion,c=w.useRef(null);s=s||r.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=w.useContext(Nr);u&&!u.projection&&i&&(u.type==="html"||u.type==="svg")&&Bc(c.current,n,i,h);const d=w.useRef(!1);w.useInsertionEffect(()=>{u&&d.current&&u.update(n,a)});const m=n[Or],x=w.useRef(!!m&&!((g=window.MotionHandoffIsComplete)!=null&&g.call(window,m))&&((b=window.MotionHasOptimisedAnimation)==null?void 0:b.call(window,m)));return ua(()=>{u&&(d.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),u.scheduleRenderMicrotask(),x.current&&u.animationState&&u.animationState.animateChanges())}),w.useEffect(()=>{u&&(!x.current&&u.animationState&&u.animationState.animateChanges(),x.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)==null||p.call(window,m)}),x.current=!1),u.enteringChildren=void 0)}),u}function Bc(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:jr(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&Dt(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:c})}function jr(t){if(t)return t.options.allowProjection!==!1?t.projection:jr(t.parent)}function Be(t,{forwardMotionProps:e=!1}={},n,s){n&&cc(n);const i=Yn(t)?Rc:Vc;function o(a,l){let c;const u={...w.useContext(Er),...a,layoutId:Oc(a)},{isStatic:h}=u,d=pc(a),m=i(a,h);if(!h&&xn){Nc();const x=jc(u);c=x.MeasureLayout,d.visualElement=Ic(t,m,u,s,x.ProjectionNode)}return Ft.jsxs(Se.Provider,{value:d,children:[c&&d.visualElement?Ft.jsx(c,{visualElement:d.visualElement,...u}):null,Dc(t,a,Lc(m,d.visualElement,l),m,h,e)]})}o.displayName=`motion.${typeof t=="string"?t:`create(${t.displayName??t.name??""})`}`;const r=w.forwardRef(o);return r[Fc]=t,r}function Oc({layoutId:t}){const e=w.useContext(Bi).id;return e&&t!==void 0?e+"-"+t:t}function Nc(t,e){w.useContext(Dr).strict}function jc(t){const{drag:e,layout:n}=It;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}function Uc(t,e){if(typeof Proxy>"u")return Be;const n=new Map,s=(o,r)=>Be(o,r,t,e),i=(o,r)=>s(o,r);return new Proxy(i,{get:(o,r)=>r==="create"?s:(n.has(r)||n.set(r,Be(r,void 0,t,e)),n.get(r))})}function Ur({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function zc({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Wc(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}function Oe(t){return t===void 0||t===1}function on({scale:t,scaleX:e,scaleY:n}){return!Oe(t)||!Oe(e)||!Oe(n)}function vt(t){return on(t)||zr(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function zr(t){return _s(t.x)||_s(t.y)}function _s(t){return t&&t!=="0%"}function Te(t,e,n){const s=t-n,i=e*s;return n+i}function Ys(t,e,n,s,i){return i!==void 0&&(t=Te(t,i,s)),Te(t,n,s)+e}function an(t,e=0,n=1,s,i){t.min=Ys(t.min,e,n,s,i),t.max=Ys(t.max,e,n,s,i)}function Wr(t,{x:e,y:n}){an(t.x,e.translate,e.scale,e.originPoint),an(t.y,n.translate,n.scale,n.originPoint)}const Ks=.999999999999,Gs=1.0000000000001;function $c(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Mt(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Wr(t,r)),s&&vt(o.latestValues)&&Mt(t,o.latestValues))}e.x<Gs&&e.x>Ks&&(e.x=1),e.y<Gs&&e.y>Ks&&(e.y=1)}function Pt(t,e){t.min=t.min+e,t.max=t.max+e}function Hs(t,e,n,s,i=.5){const o=k(t.min,t.max,i);an(t,e,n,o,s)}function Mt(t,e){Hs(t.x,e.x,e.scaleX,e.scale,e.originX),Hs(t.y,e.y,e.scaleY,e.scale,e.originY)}function $r(t,e){return Ur(Wc(t.getBoundingClientRect(),e))}function _c(t,e,n){const s=$r(t,n),{scroll:i}=e;return i&&(Pt(s.x,i.offset.x),Pt(s.y,i.offset.y)),s}const qs=()=>({translate:0,scale:1,origin:0,originPoint:0}),Vt=()=>({x:qs(),y:qs()}),Xs=()=>({min:0,max:0}),O=()=>({x:Xs(),y:Xs()}),ln={current:null},_r={current:!1};function Yc(){if(_r.current=!0,!!xn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ln.current=t.matches;t.addEventListener("change",e),e()}else ln.current=!1}const Kc=new WeakMap;function Gc(t,e,n){for(const s in e){const i=e[s],o=n[s];if(_(i))t.addValue(s,i);else if(_(o))t.addValue(s,kt(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,kt(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Zs=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Hc{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Bn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const d=G.now();this.renderScheduledAt<d&&(this.renderScheduledAt=d,L.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=we(n),this.isVariantNode=Mr(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const d in h){const m=h[d];l[d]!==void 0&&_(m)&&m.set(l[d])}}mount(e){var n;this.current=e,Kc.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((s,i)=>this.bindToMotionValue(i,s)),_r.current||Yc(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ln.current,(n=this.parent)==null||n.addChild(this),this.update(this.props,this.presenceContext)}unmount(){var e;this.projection&&this.projection.unmount(),pt(this.notifyUpdate),pt(this.render),this.valueSubscriptions.forEach(n=>n()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),(e=this.parent)==null||e.removeChild(this);for(const n in this.events)this.events[n].clear();for(const n in this.features){const s=this.features[n];s&&(s.unmount(),s.isMounted=!1)}this.current=null}addChild(e){this.children.add(e),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(e)}removeChild(e){this.children.delete(e),this.enteringChildren&&this.enteringChildren.delete(e)}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=Nt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",r=>{this.latestValues[e]=r,this.props.onUpdate&&L.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o&&o(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in It){const n=It[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):O()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Zs.length;s++){const i=Zs[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Gc(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=kt(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(Oi(s)||ji(s))?s=parseFloat(s):!ac(s)&&mt.test(n)&&(s=br(e,n)),this.setBaseTarget(e,_(s)?s.get():s)),_(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=Kn(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!_(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new wn),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}scheduleRenderMicrotask(){jn.render(this.render)}}class Yr extends Hc{constructor(){super(...arguments),this.KeyframeResolver=Hl}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;_(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Kr(t,{style:e,vars:n},s,i){const o=t.style;let r;for(r in e)o[r]=e[r];i==null||i.applyProjectionStyles(o,s);for(r in n)o.setProperty(r,n[r])}function qc(t){return window.getComputedStyle(t)}class Xc extends Yr{constructor(){super(...arguments),this.type="html",this.renderInstance=Kr}readValueFromInstance(e,n){var s;if(Nt.has(n))return(s=this.projection)!=null&&s.isProjecting?Qe(n):fl(e,n);{const i=qc(e),o=(Dn(n)?i.getPropertyValue(n):i[n])||0;return typeof o=="string"?o.trim():o}}measureInstanceViewportBox(e,{transformPagePoint:n}){return $r(e,n)}build(e,n,s){$n(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return Gn(e,n,s)}}const Gr=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Zc(t,e,n,s){Kr(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(Gr.has(i)?i:Hn(i),e.attrs[i])}class Qc extends Yr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=O}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(Nt.has(n)){const s=vr(n);return s&&s.default||0}return n=Gr.has(n)?n:Hn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Br(e,n,s)}build(e,n,s){Fr(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Zc(e,n,s,i)}mount(e){this.isSVGTag=kr(e.tagName),super.mount(e)}}const Jc=(t,e)=>Yn(t)?new Qc(e):new Xc(e,{allowProjection:t!==w.Fragment});function Rt(t,e,n){const s=t.getProps();return Kn(s,e,n!==void 0?n:s.custom,t)}const cn=t=>Array.isArray(t);function tu(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,kt(n))}function eu(t){return cn(t)?t[t.length-1]||0:t}function nu(t,e){const n=Rt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=eu(o[r]);tu(t,r,a)}}function su(t){return!!(_(t)&&t.add)}function un(t,e){const n=t.getValue("willChange");if(su(n))return n.add(e);if(!n&&ct.WillChange){const s=new ct.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function Hr(t){return t.props[Or]}const iu=t=>t!==null;function ru(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(iu),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return!o||s===void 0?i[o]:s}const ou={type:"spring",stiffness:500,damping:25,restSpeed:10},au=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),lu={type:"keyframes",duration:.8},cu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},uu=(t,{keyframes:e})=>e.length>2?lu:Nt.has(t)?t.startsWith("scale")?au(e[1]):ou:cu;function hu({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const qn=(t,e,n,s={},i,o)=>r=>{const a=On(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-it(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:d=>{e.set(d),a.onUpdate&&a.onUpdate(d)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};hu(a)||Object.assign(u,uu(t,u)),u.duration&&(u.duration=it(u.duration)),u.repeatDelay&&(u.repeatDelay=it(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(sn(u),u.delay===0&&(h=!0)),(ct.instantAnimations||ct.skipAnimations)&&(h=!0,sn(u),u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const d=ru(u.keyframes,a);if(d!==void 0){L.update(()=>{u.onUpdate(d),u.onComplete()});return}}return a.isSync?new In(u):new Bl(u)};function du({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function qr(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),d=a[u];if(d===void 0||c&&du(c,u))continue;const m={delay:n,...On(o||{},u)},x=h.get();if(x!==void 0&&!h.isAnimating&&!Array.isArray(d)&&d===x&&!m.velocity)continue;let g=!1;if(window.MotionHandoffAnimation){const p=Hr(t);if(p){const S=window.MotionHandoffAnimation(p,u,L);S!==null&&(m.startTime=S,g=!0)}}un(t,u),h.start(qn(u,h,d,t.shouldReduceMotion&&gr.has(u)?{type:!1}:m,t,g));const b=h.animation;b&&l.push(b)}return r&&Promise.all(l).then(()=>{L.update(()=>{r&&nu(t,r)})}),l}function Xr(t,e,n,s=0,i=1){const o=Array.from(t).sort((c,u)=>c.sortNodePosition(u)).indexOf(e),r=t.size,a=(r-1)*s;return typeof n=="function"?n(o,r):i===1?o*s:a-o*s}function hn(t,e,n={}){var l;const s=Rt(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(qr(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:d}=i;return fu(t,e,c,u,h,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,u]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function fu(t,e,n=0,s=0,i=0,o=1,r){const a=[];for(const l of t.variantChildren)l.notify("AnimationStart",e),a.push(hn(l,e,{...r,delay:n+(typeof s=="function"?0:s)+Xr(t.variantChildren,l,s,i,o)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}function pu(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>hn(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=hn(t,e,n);else{const i=typeof e=="function"?Rt(t,e,n.custom):e;s=Promise.all(qr(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Zr(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const mu=Wn.length;function Qr(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Qr(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<mu;n++){const s=Wn[n],i=t.props[s];(Jt(i)||i===!1)&&(e[s]=i)}return e}const gu=[...zn].reverse(),yu=zn.length;function xu(t){return e=>Promise.all(e.map(({animation:n,options:s})=>pu(t,n,s)))}function vu(t){let e=xu(t),n=Qs(),s=!0;const i=l=>(c,u)=>{var d;const h=Rt(t,u,l==="exit"?(d=t.presenceContext)==null?void 0:d.custom:void 0);if(h){const{transition:m,transitionEnd:x,...g}=h;c={...c,...g,...x}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=Qr(t.parent)||{},h=[],d=new Set;let m={},x=1/0;for(let b=0;b<yu;b++){const p=gu[b],S=n[p],v=c[p]!==void 0?c[p]:u[p],P=Jt(v),A=p===l?S.isActive:null;A===!1&&(x=b);let R=v===u[p]&&v!==c[p]&&P;if(R&&s&&t.manuallyAnimateOnMount&&(R=!1),S.protectedKeys={...m},!S.isActive&&A===null||!v&&!S.prevProp||Ce(v)||typeof v=="boolean")continue;const B=bu(S.prevProp,v);let V=B||p===l&&S.isActive&&!R&&P||b>x&&P,W=!1;const H=Array.isArray(v)?v:[v];let ut=H.reduce(i(p),{});A===!1&&(ut={});const{prevResolvedValues:Ae={}}=S,oe={...Ae,...ut},ae=N=>{V=!0,d.has(N)&&(W=!0,d.delete(N)),S.needsAnimating[N]=!0;const K=t.getValue(N);K&&(K.liveStyle=!1)};for(const N in oe){const K=ut[N],J=Ae[N];if(m.hasOwnProperty(N))continue;let ht=!1;cn(K)&&cn(J)?ht=!Zr(K,J):ht=K!==J,ht?K!=null?ae(N):d.add(N):K!==void 0&&d.has(N)?ae(N):S.protectedKeys[N]=!0}S.prevProp=v,S.prevResolvedValues=ut,S.isActive&&(m={...m,...ut}),s&&t.blockInitialAnimation&&(V=!1);const jt=R&&B;V&&(!jt||W)&&h.push(...H.map(N=>{const K={type:p};if(typeof N=="string"&&s&&!jt&&t.manuallyAnimateOnMount&&t.parent){const{parent:J}=t,ht=Rt(J,N);if(J.enteringChildren&&ht){const{delayChildren:yt}=ht.transition||{};K.delay=Xr(J.enteringChildren,t,yt)}}return{animation:N,options:K}}))}if(d.size){const b={};if(typeof c.initial!="boolean"){const p=Rt(t,Array.isArray(c.initial)?c.initial[0]:c.initial);p&&p.transition&&(b.transition=p.transition)}d.forEach(p=>{const S=t.getBaseTarget(p),v=t.getValue(p);v&&(v.liveStyle=!0),b[p]=S??null}),h.push({animation:b})}let g=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(h):Promise.resolve()}function a(l,c){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(d=>{var m;return(m=d.animationState)==null?void 0:m.setActive(l,c)}),n[l].isActive=c;const u=r(l);for(const d in n)n[d].protectedKeys={};return u}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Qs(),s=!0}}}function bu(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Zr(e,t):!1}function xt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Qs(){return{animate:xt(!0),whileInView:xt(),whileHover:xt(),whileTap:xt(),whileDrag:xt(),whileFocus:xt(),exit:xt()}}class gt{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Tu extends gt{constructor(e){super(e),e.animationState||(e.animationState=vu(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();Ce(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let Su=0;class Cu extends gt{constructor(){super(...arguments),this.id=Su++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const wu={animation:{Feature:Tu},exit:{Feature:Cu}};function ee(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function re(t){return{point:{x:t.pageX,y:t.pageY}}}const Au=t=>e=>Un(e)&&t(e,re(e));function Gt(t,e,n,s){return ee(t,e,Au(n),s)}const Jr=1e-4,Eu=1-Jr,Du=1+Jr,to=.01,Pu=0-to,Mu=0+to;function Y(t){return t.max-t.min}function Vu(t,e,n){return Math.abs(t-e)<=n}function Js(t,e,n,s=.5){t.origin=s,t.originPoint=k(e.min,e.max,t.origin),t.scale=Y(n)/Y(e),t.translate=k(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Eu&&t.scale<=Du||isNaN(t.scale))&&(t.scale=1),(t.translate>=Pu&&t.translate<=Mu||isNaN(t.translate))&&(t.translate=0)}function Ht(t,e,n,s){Js(t.x,e.x,n.x,s?s.originX:void 0),Js(t.y,e.y,n.y,s?s.originY:void 0)}function ti(t,e,n){t.min=n.min+e.min,t.max=t.min+Y(e)}function Ru(t,e,n){ti(t.x,e.x,n.x),ti(t.y,e.y,n.y)}function ei(t,e,n){t.min=e.min-n.min,t.max=t.min+Y(e)}function qt(t,e,n){ei(t.x,e.x,n.x),ei(t.y,e.y,n.y)}function Z(t){return[t("x"),t("y")]}const eo=({current:t})=>t?t.ownerDocument.defaultView:null,ni=(t,e)=>Math.abs(t-e);function Fu(t,e){const n=ni(t.x,e.x),s=ni(t.y,e.y);return Math.sqrt(n**2+s**2)}class no{constructor(e,n,{transformPagePoint:s,contextWindow:i=window,dragSnapToOrigin:o=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=je(this.lastMoveEventInfo,this.history),m=this.startEvent!==null,x=Fu(d.offset,{x:0,y:0})>=this.distanceThreshold;if(!m&&!x)return;const{point:g}=d,{timestamp:b}=z;this.history.push({...g,timestamp:b});const{onStart:p,onMove:S}=this.handlers;m||(p&&p(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),S&&S(this.lastMoveEvent,d)},this.handlePointerMove=(d,m)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Ne(m,this.transformPagePoint),L.update(this.updatePoint,!0)},this.handlePointerUp=(d,m)=>{this.end();const{onEnd:x,onSessionEnd:g,resumeAnimation:b}=this.handlers;if(this.dragSnapToOrigin&&b&&b(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const p=je(d.type==="pointercancel"?this.lastMoveEventInfo:Ne(m,this.transformPagePoint),this.history);this.startEvent&&x&&x(d,p),g&&g(d,p)},!Un(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.distanceThreshold=r,this.contextWindow=i||window;const a=re(e),l=Ne(a,this.transformPagePoint),{point:c}=l,{timestamp:u}=z;this.history=[{...c,timestamp:u}];const{onSessionStart:h}=n;h&&h(e,je(l,this.history)),this.removeListeners=ne(Gt(this.contextWindow,"pointermove",this.handlePointerMove),Gt(this.contextWindow,"pointerup",this.handlePointerUp),Gt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),pt(this.updatePoint)}}function Ne(t,e){return e?{point:e(t.point)}:t}function si(t,e){return{x:t.x-e.x,y:t.y-e.y}}function je({point:t},e){return{point:t,delta:si(t,so(e)),offset:si(t,Lu(e)),velocity:ku(e,.1)}}function Lu(t){return t[0]}function so(t){return t[t.length-1]}function ku(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=so(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>it(e)));)n--;if(!s)return{x:0,y:0};const o=rt(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function Iu(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?k(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?k(n,t,s.max):Math.min(t,n)),t}function ii(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function Bu(t,{top:e,left:n,bottom:s,right:i}){return{x:ii(t.x,n,i),y:ii(t.y,e,s)}}function ri(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Ou(t,e){return{x:ri(t.x,e.x),y:ri(t.y,e.y)}}function Nu(t,e){let n=.5;const s=Y(t),i=Y(e);return i>s?n=Xt(e.min,e.max-s,t.min):s>i&&(n=Xt(t.min,t.max-i,e.min)),lt(0,1,n)}function ju(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const dn=.35;function Uu(t=dn){return t===!1?t=0:t===!0&&(t=dn),{x:oi(t,"left","right"),y:oi(t,"top","bottom")}}function oi(t,e,n){return{min:ai(t,e),max:ai(t,n)}}function ai(t,e){return typeof t=="number"?t:t[e]||0}const zu=new WeakMap;class Wu{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=O(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=e}start(e,{snapToCursor:n=!1,distanceThreshold:s}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const o=h=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(re(h).point)},r=(h,d)=>{const{drag:m,dragPropagation:x,onDragStart:g}=this.getProps();if(m&&!x&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Jl(m),!this.openDragLock))return;this.latestPointerEvent=h,this.latestPanInfo=d,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Z(p=>{let S=this.getAxisMotionValue(p).get()||0;if(ot.test(S)){const{projection:v}=this.visualElement;if(v&&v.layout){const P=v.layout.layoutBox[p];P&&(S=Y(P)*(parseFloat(S)/100))}}this.originPoint[p]=S}),g&&L.postRender(()=>g(h,d)),un(this.visualElement,"transform");const{animationState:b}=this.visualElement;b&&b.setActive("whileDrag",!0)},a=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d;const{dragPropagation:m,dragDirectionLock:x,onDirectionLock:g,onDrag:b}=this.getProps();if(!m&&!this.openDragLock)return;const{offset:p}=d;if(x&&this.currentDirection===null){this.currentDirection=$u(p),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",d.point,p),this.updateAxis("y",d.point,p),this.visualElement.render(),b&&b(h,d)},l=(h,d)=>{this.latestPointerEvent=h,this.latestPanInfo=d,this.stop(h,d),this.latestPointerEvent=null,this.latestPanInfo=null},c=()=>Z(h=>{var d;return this.getAnimationState(h)==="paused"&&((d=this.getAxisMotionValue(h).animation)==null?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new no(e,{onSessionStart:o,onStart:r,onMove:a,onSessionEnd:l,resumeAnimation:c},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:s,contextWindow:eo(this.visualElement)})}stop(e,n){const s=e||this.latestPointerEvent,i=n||this.latestPanInfo,o=this.isDragging;if(this.cancel(),!o||!i||!s)return;const{velocity:r}=i;this.startAnimation(r);const{onDragEnd:a}=this.getProps();a&&L.postRender(()=>a(s,i))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!ue(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=Iu(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,i=this.constraints;e&&Dt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=Bu(s.layoutBox,e):this.constraints=!1,this.elastic=Uu(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&Z(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=ju(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!Dt(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=_c(s,i.root,this.visualElement.getTransformPagePoint());let r=Ou(i.layout.layoutBox,o);if(n){const a=n(zc(r));this.hasMutatedConstraints=!!a,a&&(r=Ur(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=Z(u=>{if(!ue(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const d=i?200:1e6,m=i?40:1e7,x={type:"inertia",velocity:s?e[u]:0,bounceStiffness:d,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,x)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return un(this.visualElement,e),s.start(qn(e,s,0,n,this.visualElement,!1))}stopAnimation(){Z(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){Z(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){Z(n=>{const{drag:s}=this.getProps();if(!ue(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-k(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!Dt(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Z(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=Nu({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),Z(r=>{if(!ue(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(k(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;zu.set(this.visualElement,this);const e=this.visualElement.current,n=Gt(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();Dt(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),L.read(s);const r=ee(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(Z(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=dn,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function ue(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function $u(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class _u extends gt{constructor(e){super(e),this.removeGroupControls=Q,this.removeListeners=Q,this.controls=new Wu(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Q}unmount(){this.removeGroupControls(),this.removeListeners()}}const li=t=>(e,n)=>{t&&L.postRender(()=>t(e,n))};class Yu extends gt{constructor(){super(...arguments),this.removePointerDownListener=Q}onPointerDown(e){this.session=new no(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:eo(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:li(e),onStart:li(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&L.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=Gt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const pe={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ci(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const zt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(D.test(t))t=parseFloat(t);else return t;const n=ci(t,e.target.x),s=ci(t,e.target.y);return`${n}% ${s}%`}},Ku={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=mt.parse(t);if(i.length>5)return s;const o=mt.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=k(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}};let Ue=!1;class Gu extends w.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;mc(Hu),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),Ue&&o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),pe.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,Ue=!0,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||L.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),jn.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;Ue=!0,i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function io(t){const[e,n]=lc(),s=w.useContext(Bi);return Ft.jsx(Gu,{...t,layoutGroup:s,switchLayoutGroup:w.useContext(Nr),isPresent:e,safeToRemove:n})}const Hu={borderRadius:{...zt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:zt,borderTopRightRadius:zt,borderBottomLeftRadius:zt,borderBottomRightRadius:zt,boxShadow:Ku};function qu(t,e,n){const s=_(t)?t:kt(t);return s.start(qn("",s,e,n)),s.animation}const Xu=(t,e)=>t.depth-e.depth;class Zu{constructor(){this.children=[],this.isDirty=!1}add(e){bn(this.children,e),this.isDirty=!0}remove(e){Tn(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Xu),this.isDirty=!1,this.children.forEach(e)}}function Qu(t,e){const n=G.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(pt(s),t(o-e))};return L.setup(s,!0),()=>pt(s)}const ro=["TopLeft","TopRight","BottomLeft","BottomRight"],Ju=ro.length,ui=t=>typeof t=="string"?parseFloat(t):t,hi=t=>typeof t=="number"||D.test(t);function th(t,e,n,s,i,o){i?(t.opacity=k(0,n.opacity??1,eh(s)),t.opacityExit=k(e.opacity??1,0,nh(s))):o&&(t.opacity=k(e.opacity??1,n.opacity??1,s));for(let r=0;r<Ju;r++){const a=`border${ro[r]}Radius`;let l=di(e,a),c=di(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||hi(l)===hi(c)?(t[a]=Math.max(k(ui(l),ui(c),s),0),(ot.test(c)||ot.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=k(e.rotate||0,n.rotate||0,s))}function di(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const eh=oo(0,.5,Gi),nh=oo(.5,.95,Q);function oo(t,e,n){return s=>s<t?0:s>e?1:n(Xt(t,e,s))}function fi(t,e){t.min=e.min,t.max=e.max}function X(t,e){fi(t.x,e.x),fi(t.y,e.y)}function pi(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function mi(t,e,n,s,i){return t-=e,t=Te(t,1/n,s),i!==void 0&&(t=Te(t,1/i,s)),t}function sh(t,e=0,n=1,s=.5,i,o=t,r=t){if(ot.test(e)&&(e=parseFloat(e),e=k(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=k(o.min,o.max,s);t===o&&(a-=e),t.min=mi(t.min,e,n,a,i),t.max=mi(t.max,e,n,a,i)}function gi(t,e,[n,s,i],o,r){sh(t,e[n],e[s],e[i],e.scale,o,r)}const ih=["x","scaleX","originX"],rh=["y","scaleY","originY"];function yi(t,e,n,s){gi(t.x,e,ih,n?n.x:void 0,s?s.x:void 0),gi(t.y,e,rh,n?n.y:void 0,s?s.y:void 0)}function xi(t){return t.translate===0&&t.scale===1}function ao(t){return xi(t.x)&&xi(t.y)}function vi(t,e){return t.min===e.min&&t.max===e.max}function oh(t,e){return vi(t.x,e.x)&&vi(t.y,e.y)}function bi(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function lo(t,e){return bi(t.x,e.x)&&bi(t.y,e.y)}function Ti(t){return Y(t.x)/Y(t.y)}function Si(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class ah{constructor(){this.members=[]}add(e){bn(this.members,e),e.scheduleRender()}remove(e){if(Tn(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function lh(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:d,skewX:m,skewY:x}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),d&&(s+=`rotateY(${d}deg) `),m&&(s+=`skewX(${m}deg) `),x&&(s+=`skewY(${x}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const ze=["","X","Y","Z"],ch=1e3;let uh=0;function We(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function co(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=Hr(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",L,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&co(s)}function uo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=uh++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(fh),this.nodes.forEach(yh),this.nodes.forEach(xh),this.nodes.forEach(ph)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Zu)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new wn),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Ar(r)&&!rc(r),this.instance=r;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let u,h=0;const d=()=>this.root.updateBlockedByResize=!1;L.read(()=>{h=window.innerWidth}),t(r,()=>{const m=window.innerWidth;m!==h&&(h=m,this.root.updateBlockedByResize=!0,u&&u(),u=Qu(d,250),pe.hasAnimatedSinceResize&&(pe.hasAnimatedSinceResize=!1,this.nodes.forEach(Ai)))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:d,layout:m})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||Ch,{onLayoutAnimationStart:g,onLayoutAnimationComplete:b}=c.getProps(),p=!this.targetLayout||!lo(this.targetLayout,m),S=!h&&d;if(this.options.layoutRoot||this.resumeFrom||S||h&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const v={...On(x,"layout"),onPlay:g,onComplete:b};(c.shouldReduceMotion||this.options.layoutRoot)&&(v.delay=0,v.type=!1),this.startAnimation(v),this.setAnimationOrigin(u,S)}else h||Ai(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=m})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),pt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(vh),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&co(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Ci);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(wi);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(gh),this.nodes.forEach(hh),this.nodes.forEach(dh)):this.nodes.forEach(wi),this.clearAllSnapshots();const a=G.now();z.delta=lt(0,1e3/60,a-z.timestamp),z.timestamp=a,z.isProcessing=!0,Ve.update.process(z),Ve.preRender.process(z),Ve.render.process(z),z.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,jn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(mh),this.sharedNodes.forEach(bh)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,L.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){L.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!Y(this.snapshot.measuredBox.x)&&!Y(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=O(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!ao(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&this.instance&&(a||vt(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),wh(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:r}=this.options;if(!r)return O();const a=r.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(Ah))){const{scroll:u}=this.root;u&&(Pt(a.x,u.offset.x),Pt(a.y,u.offset.y))}return a}removeElementScroll(r){var l;const a=O();if(X(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:d}=u;u!==this.root&&h&&d.layoutScroll&&(h.wasRoot&&X(a,r),Pt(a.x,h.offset.x),Pt(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=O();X(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&Mt(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),vt(u.latestValues)&&Mt(l,u.latestValues)}return vt(this.latestValues)&&Mt(l,this.latestValues),l}removeTransform(r){const a=O();X(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!vt(c.latestValues))continue;on(c.latestValues)&&c.updateSnapshot();const u=O(),h=c.measurePageBox();X(u,h),yi(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return vt(this.latestValues)&&yi(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==z.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var d;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(d=this.parent)!=null&&d.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=z.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=O(),this.relativeTargetOrigin=O(),qt(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),X(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=O(),this.targetWithTransforms=O()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Ru(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):X(this.target,this.layout.layoutBox),Wr(this.target,this.targetDelta)):X(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=O(),this.relativeTargetOrigin=O(),qt(this.relativeTargetOrigin,this.target,m.target),X(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||on(this.parent.latestValues)||zr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var x;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(x=this.parent)!=null&&x.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===z.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;X(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,d=this.treeScale.y;$c(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=O());const{target:m}=r;if(!m){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(pi(this.prevProjectionDelta.x,this.projectionDelta.x),pi(this.prevProjectionDelta.y,this.projectionDelta.y)),Ht(this.projectionDelta,this.layoutCorrected,m,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==d||!Si(this.projectionDelta.x,this.prevProjectionDelta.x)||!Si(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",m))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Vt(),this.projectionDelta=Vt(),this.projectionDeltaWithTransform=Vt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=Vt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const d=O(),m=l?l.source:void 0,x=this.layout?this.layout.source:void 0,g=m!==x,b=this.getStack(),p=!b||b.members.length<=1,S=!!(g&&!p&&this.options.crossfade===!0&&!this.path.some(Sh));this.animationProgress=0;let v;this.mixTargetDelta=P=>{const A=P/1e3;Ei(h.x,r.x,A),Ei(h.y,r.y,A),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(qt(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Th(this.relativeTarget,this.relativeTargetOrigin,d,A),v&&oh(this.relativeTarget,v)&&(this.isProjectionDirty=!1),v||(v=O()),X(v,this.relativeTarget)),g&&(this.animationValues=u,th(u,c,this.latestValues,A,S,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=A},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(),this.pendingAnimation&&(pt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=L.update(()=>{pe.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=kt(0)),this.currentAnimation=qu(this.motionValue,[0,1e3],{...r,velocity:0,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),r.onUpdate&&r.onUpdate(u)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(ch),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&ho(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||O();const h=Y(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const d=Y(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+d}X(a,l),Mt(a,u),Ht(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new ah),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&We("z",r,c,this.animationValues);for(let u=0;u<ze.length;u++)We(`rotate${ze[u]}`,r,c,this.animationValues),We(`skew${ze[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}applyProjectionStyles(r,a){if(!this.instance||this.isSVG)return;if(!this.isVisible){r.visibility="hidden";return}const l=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,r.visibility="",r.opacity="",r.pointerEvents=fe(a==null?void 0:a.pointerEvents)||"",r.transform=l?l(this.latestValues,""):"none";return}const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){this.options.layoutId&&(r.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,r.pointerEvents=fe(a==null?void 0:a.pointerEvents)||""),this.hasProjected&&!vt(this.latestValues)&&(r.transform=l?l({},""):"none",this.hasProjected=!1);return}r.visibility="";const u=c.animationValues||c.latestValues;this.applyTransformsToTarget();let h=lh(this.projectionDeltaWithTransform,this.treeScale,u);l&&(h=l(u,h)),r.transform=h;const{x:d,y:m}=this.projectionDelta;r.transformOrigin=`${d.origin*100}% ${m.origin*100}% 0`,c.animationValues?r.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:r.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const x in te){if(u[x]===void 0)continue;const{correct:g,applyTo:b,isCSSVariable:p}=te[x],S=h==="none"?u[x]:g(u[x],c);if(b){const v=b.length;for(let P=0;P<v;P++)r[b[P]]=S}else p?this.options.visualElement.renderState.vars[x]=S:r[x]=S}this.options.layoutId&&(r.pointerEvents=c===this?fe(a==null?void 0:a.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop()}),this.root.nodes.forEach(Ci),this.root.sharedNodes.clear()}}}function hh(t){t.updateLayout()}function dh(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?Z(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],m=Y(d);d.min=s[h].min,d.max=d.min+m}):ho(o,e.layoutBox,s)&&Z(h=>{const d=r?e.measuredBox[h]:e.layoutBox[h],m=Y(s[h]);d.max=d.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+m)});const a=Vt();Ht(a,s,e.layoutBox);const l=Vt();r?Ht(l,t.applyTransform(i,!0),e.measuredBox):Ht(l,s,e.layoutBox);const c=!ao(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:d,layout:m}=h;if(d&&m){const x=O();qt(x,e.layoutBox,d.layoutBox);const g=O();qt(g,s,m.layoutBox),lo(x,g)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=g,t.relativeTargetOrigin=x,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function fh(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ph(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function mh(t){t.clearSnapshot()}function Ci(t){t.clearMeasurements()}function wi(t){t.isLayoutDirty=!1}function gh(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Ai(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function yh(t){t.resolveTargetDelta()}function xh(t){t.calcProjection()}function vh(t){t.resetSkewAndRotation()}function bh(t){t.removeLeadSnapshot()}function Ei(t,e,n){t.translate=k(e.translate,0,n),t.scale=k(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function Di(t,e,n,s){t.min=k(e.min,n.min,s),t.max=k(e.max,n.max,s)}function Th(t,e,n,s){Di(t.x,e.x,n.x,s),Di(t.y,e.y,n.y,s)}function Sh(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Ch={duration:.45,ease:[.4,0,.1,1]},Pi=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Mi=Pi("applewebkit/")&&!Pi("chrome/")?Math.round:Q;function Vi(t){t.min=Mi(t.min),t.max=Mi(t.max)}function wh(t){Vi(t.x),Vi(t.y)}function ho(t,e,n){return t==="position"||t==="preserve-aspect"&&!Vu(Ti(e),Ti(n),.2)}function Ah(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const Eh=uo({attachResizeListener:(t,e)=>ee(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),$e={current:void 0},fo=uo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!$e.current){const t=new Eh({});t.mount(window),t.setOptions({layoutScroll:!0}),$e.current=t}return $e.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),Dh={pan:{Feature:Yu},drag:{Feature:_u,ProjectionNode:fo,MeasureLayout:io}};function Ri(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&L.postRender(()=>o(e,re(e)))}class Ph extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=tc(e,(n,s)=>(Ri(this.node,s,"Start"),i=>Ri(this.node,i,"End"))))}unmount(){}}class Mh extends gt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ne(ee(this.node.current,"focus",()=>this.onFocus()),ee(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Fi(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&L.postRender(()=>o(e,re(e)))}class Vh extends gt{mount(){const{current:e}=this.node;e&&(this.unmount=ic(e,(n,s)=>(Fi(this.node,s,"Start"),(i,{success:o})=>Fi(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const fn=new WeakMap,_e=new WeakMap,Rh=t=>{const e=fn.get(t.target);e&&e(t)},Fh=t=>{t.forEach(Rh)};function Lh({root:t,...e}){const n=t||document;_e.has(n)||_e.set(n,{});const s=_e.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(Fh,{root:t,...e})),s[i]}function kh(t,e,n){const s=Lh(e);return fn.set(t,n),s.observe(t),()=>{fn.delete(t),s.unobserve(t)}}const Ih={some:0,all:1};class Bh extends gt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:Ih[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),d=c?u:h;d&&d(l)};return kh(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Oh(e,n))&&this.startObserver()}unmount(){}}function Oh({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const Nh={inView:{Feature:Bh},tap:{Feature:Vh},focus:{Feature:Mh},hover:{Feature:Ph}},jh={layout:{ProjectionNode:fo,MeasureLayout:io}},Uh={...wu,...Nh,...Dh,...jh},zh=Uc(Uh,Jc);var Xn={},Wh=mn;Object.defineProperty(Xn,"__esModule",{value:!0});var po=Xn.default=void 0,$h=Wh(pn()),_h=Ft;po=Xn.default=(0,$h.default)((0,_h.jsx)("path",{d:"M21.99 4c0-1.1-.89-2-1.99-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4zM20 4v13.17L18.83 16H4V4zM6 12h12v2H6zm0-3h12v2H6zm0-3h12v2H6z"}),"CommentOutlined");const Yh=()=>f(E,{sx:{mb:2,p:1,border:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC"},children:C(E,{sx:{display:"flex",alignItems:"center",mb:1},children:[f(E,{sx:{mr:1},children:f(j,{variant:"circular",width:24,height:24})}),C(E,{sx:{width:"100%"},children:[f(j,{variant:"text",width:"40%"}),C(E,{sx:{display:"flex",alignItems:"center",mt:1},children:[f(j,{variant:"rectangular",width:120,height:8,sx:{mr:1}}),f(j,{variant:"text",width:"20%"})]})]})]})}),Kh=()=>f(Lt,{elevation:1,sx:{p:2,mb:3,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF",boxShadow:"0 2px 6px rgba(0,0,0,0.03)"},children:C($,{spacing:2,children:[C($,{direction:"row",alignItems:"center",spacing:2,children:[f(j,{variant:"text",width:"30%"}),f(j,{variant:"rectangular",width:2,height:24}),C($,{direction:"row",alignItems:"center",spacing:1,children:[f(j,{variant:"circular",width:24,height:24}),f(j,{variant:"text",width:80}),f(j,{variant:"text",width:120})]})]}),C(E,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[f(j,{variant:"text",width:"25%"}),f(j,{variant:"rectangular",width:80,height:24})]}),C(F,{container:!0,spacing:3,children:[C(F,{item:!0,xs:12,md:6,children:[f(j,{variant:"text",width:"20%"}),f($,{direction:"row",spacing:1,children:f(j,{variant:"rectangular",width:200,height:32})})]}),C(F,{item:!0,xs:12,md:6,children:[f(j,{variant:"text",width:"20%"}),C(E,{sx:{display:"flex",alignItems:"center"},children:[f(j,{variant:"circular",width:24,height:24,sx:{mr:1}}),C(E,{children:[f(j,{variant:"text",width:150}),f(j,{variant:"text",width:100})]})]})]})]})]})}),mo=()=>f(F,{container:!0,spacing:2,children:f(F,{item:!0,xs:12,children:C(F,{container:!0,spacing:2,children:[f(F,{item:!0,xs:12,md:4,lg:3,children:f(Lt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#F9FAFB"},children:[...Array(5)].map((t,e)=>f(Yh,{},e))})}),f(F,{item:!0,xs:12,md:8,lg:9,children:C(Lt,{elevation:0,sx:{p:2,border:"1px solid #E2E8F0",borderRadius:3,bgcolor:"#FFFFFF"},children:[C(E,{sx:{display:"flex",justifyContent:"space-between",mb:2},children:[C(E,{children:[f(j,{variant:"text",width:200}),f(j,{variant:"text",width:100})]}),f(j,{variant:"rectangular",width:100,height:36})]}),[...Array(1)].map((t,e)=>f(Kh,{},e))]})})]})})}),Gh=({item:t,initiator:e=!1})=>{var g,b,p;const n=wo(),s=Ao(n.breakpoints.down("sm")),i=_t(t.createdAt),o=e?_t(t.updatedAt):_t(t.completedAt),r=o.diff(i,"minutes"),a=i.format("MMM D, YYYY"),l=i.format("h:mm A"),c=o.format("MMM D, YYYY"),u=o.format("h:mm A"),h=e?t.updatedAt:t.completedAt,d=s?16:20,m=s?10:12,x=s?1.5:2;return C(nt,{children:[f(E,{sx:{px:2,py:1.5,borderBottom:"1px solid #E2E8F0",borderTop:{xs:"1px solid #E2E8F0",md:"none"},borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"space-between",bgcolor:(b=(g=y)==null?void 0:g.chip)==null?void 0:b.background},children:C(M,{variant:"subtitle2",sx:{fontWeight:600,color:(p=y)==null?void 0:p.text.greyishBlue,display:"flex",alignItems:"center"},children:[f(Eo,{sx:{fontSize:18,mr:1}}),"Task Timeline"]})}),C(E,{sx:{position:"relative",ml:x,pb:2,width:"100%",maxWidth:"100%",overflow:"hidden",pt:2},children:[f(E,{sx:{position:"absolute",left:d/2-1,top:d+4,height:h?`calc(100% - ${d*2.5}px)`:`${d*4}px`,width:2,bgcolor:h?"#16A34A":"#F97316",zIndex:0}}),C(E,{sx:{position:"relative",mb:3,display:"flex",alignItems:"center"},children:[f(E,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:"#0284C7",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:f(Do,{sx:{color:"#FFFFFF",fontSize:m}})}),C(E,{sx:{ml:1.5,overflow:"hidden"},children:[f(M,{variant:s?"caption":"body2",color:"#0284C7",fontWeight:600,noWrap:!0,children:"Started"}),C(M,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[a," ",l]})]})]}),C(E,{sx:{position:"relative",mb:h?3:0,display:"flex",alignItems:"center"},children:[f(E,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:h?"#059669":"#F97316",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:h?f(Po,{sx:{color:"#FFFFFF",fontSize:m}}):f(Mo,{sx:{color:"#FFFFFF",fontSize:m}})}),f(E,{sx:{ml:1.5,display:"flex",alignItems:"center",bgcolor:h?"#F0FDF4":"#FFF7ED",borderRadius:1.5,px:1,py:.5,border:h?"1px solid #D1FAE5":"1px solid #FFEDD5",maxWidth:"100%"},children:f(M,{variant:"caption",sx:{color:h?"#059669":"#EA580C",fontWeight:600,whiteSpace:"nowrap"},children:h?`${r} min`:"Pending"})})]}),h&&C(E,{sx:{position:"relative",display:"flex",alignItems:"center"},children:[f(E,{sx:{width:d,height:d,borderRadius:"50%",bgcolor:"#16A34A",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1,flexShrink:0},children:f(me,{sx:{color:"#FFFFFF",fontSize:m}})}),C(E,{sx:{ml:1.5,overflow:"hidden"},children:[f(M,{variant:s?"caption":"body2",color:"#16A34A",fontWeight:600,noWrap:!0,children:"Completed"}),C(M,{variant:"caption",color:"text.secondary",sx:{display:"block",textOverflow:"ellipsis"},children:[c," ",u]})]})]})]})]})},T={COMPLETED:"COMPLETED",CANCELED:"CANCELED",READY:"READY",PENDING:"PENDING",RESERVED:"RESERVED"},Hh=Vo(Wt)(({status:t})=>({fontWeight:600,fontSize:"0.7rem",height:"24px",borderRadius:"12px",backgroundColor:t===T.COMPLETED?y.success.completedBackground:t===T.CANCELED?y.background.canceled:t===T.READY?y.warning.light:t===T.RESERVED?y.secondary.lightYellow:y.background.subtle,color:t===T.COMPLETED?y.success.completedDark:t===T.CANCELED?y.error.deepRed:t===T.READY?y.warning.orange:t===T.RESERVED?y.secondary.yellow:y.text.darkGrey,border:t===T.COMPLETED?`1px solid ${y.success.pale}`:t===T.CANCELED?`1px solid ${y.error.pale}`:t===T.READY?`1px solid ${y.warning.orange}`:t===T.READY?`1px solid ${y.secondary.teal}`:`1px solid ${y.border.light}`,"& .MuiChip-label":{letterSpacing:"1.4px"}})),qh=({item:t,index:e,setOpenSnackbar:n})=>{var s,i,o,r,a,l,c,u,h,d,m,x,g,b;return f(zh.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.4,delay:e*.1},children:f(Lt,{elevation:0,sx:{pl:2,mb:2,borderRadius:"12px",border:"1px solid",borderColor:t.status===T.COMPLETED?y.success.pale:t.status===T.CANCELED?y.error.pale:t.status===T.RESERVED?y.secondary.amber:y.border.cardBorder,backgroundColor:y.basic.white,position:"relative",overflow:"hidden",transition:"all 0.3s ease-in-out","&::before":{content:'""',position:"absolute",left:0,top:0,bottom:0,width:"5px",backgroundColor:t.status===T.COMPLETED?y.success.deepGreen:t.status===T.CANCELED?y.error.deepRed:t.status===T.READY?y.warning.orange:t.status===T.RESERVED?y.secondary.lightAmber:y.neutral[400]}},children:C(E,{sx:{display:"flex",flexDirection:"row"},children:[C(E,{sx:{width:"80%",pr:2,pt:2},children:[C($,{direction:"row",alignItems:"center",spacing:1,children:[C(M,{variant:"h6",sx:{fontSize:"1rem",fontWeight:700,color:y.text.darkBlue,pl:1,display:"flex",alignItems:"center"},children:[(i=(s=t==null?void 0:t.attributesDtos)==null?void 0:s[0])==null?void 0:i.approverGroup,((r=(o=t==null?void 0:t.attributesDtos)==null?void 0:o[0])==null?void 0:r.approverGroup)&&C("span",{style:{fontWeight:600,color:y.primary.accent,marginLeft:"4px"},children:["- ",t.subject]})]}),f(Io,{orientation:"vertical",flexItem:!0,sx:{height:20,borderColor:y.border.cardBorder}}),C($,{direction:"row",alignItems:"center",spacing:.5,children:[f(Bo,{fontSize:"small",sx:{color:y.text.darkGrey,pb:"2px"}}),f(M,{variant:"body2",sx:{fontWeight:500,color:y.text.greyishBlue,fontSize:"0.805rem"},children:"Initiated by"}),f(M,{variant:"body2",sx:{fontWeight:600,color:y.info.dark,fontSize:"0.755rem",letterSpacing:"0.5px"},children:t.createdBy})]})]}),f(E,{sx:{pt:2},children:C(F,{container:!0,spacing:3,alignItems:"flex-start",children:[C(F,{item:!0,xs:12,md:4,children:[f(M,{sx:{fontWeight:600,color:y.text.darkBlue,fontSize:"0.755rem",mb:1},children:(t==null?void 0:t.status)==="READY"?"Recipient Users":(t==null?void 0:t.status)==="RESERVED"?"Claimed by":(t==null?void 0:t.status)==="COMPLETED"?"Completed by":"Recipient Users"}),f($,{direction:"row",flexWrap:"wrap",useFlexGap:!0,gap:1,children:(t==null?void 0:t.status)==="COMPLETED"&&(t!=null&&t.processor)?f(bt,{title:t==null?void 0:t.processor,children:f(Wt,{label:t==null?void 0:t.processor,avatar:f(Ee,{sx:{width:18,height:18,bgcolor:y.background.subtle,color:y.text.darkBlue,fontSize:"0.55rem"},children:(a=t==null?void 0:t.processor)==null?void 0:a.split(" ").map(p=>p[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}):f(nt,{children:((l=t==null?void 0:t.recipientUsers)==null?void 0:l.length)>1?C(nt,{children:[f(bt,{title:t==null?void 0:t.recipientUsers[0],children:f(Wt,{label:t==null?void 0:t.recipientUsers[0],avatar:f(Ee,{sx:{width:18,height:18,bgcolor:y.background.subtle,color:y.text.darkBlue,fontSize:"0.55rem"},children:(c=t==null?void 0:t.recipientUsers[0])==null?void 0:c.split(" ").map(p=>p[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})}),f(bt,{title:f($,{spacing:.5,children:(u=t==null?void 0:t.recipientUsers)==null?void 0:u.slice(1).map((p,S)=>f(M,{variant:"caption",color:"inherit",children:p},S))}),arrow:!0,placement:"top",children:f(Wt,{label:`+ ${((h=t==null?void 0:t.recipientUsers)==null?void 0:h.length)-1}`,sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0",cursor:"pointer"}})})]}):(d=t==null?void 0:t.recipientUsers)==null?void 0:d.map((p,S)=>f(bt,{title:p,children:f(Wt,{label:p,avatar:f(Ee,{sx:{width:18,height:18,bgcolor:y.background.subtle,color:y.text.darkBlue,fontSize:"0.55rem"},children:p==null?void 0:p.split(" ").map(v=>v[0]).join("").toUpperCase()}),sx:{bgcolor:"#F9FAFB",color:"#334155",fontSize:"0.775rem","& .MuiChip-label":{px:1},boxShadow:"0 1px 2px rgba(0,0,0,0.04)",border:"1px solid #E2E8F0"}})},S))})})]}),f(F,{item:!0,xs:12,md:2,children:C(E,{children:[f(M,{variant:"caption",sx:{color:"#64748B",fontWeight:600,fontSize:"0.75rem",display:"flex",alignItems:"center",justifyContent:"flex-start",mb:1},children:C(E,{sx:{display:"flex",alignItems:"center"},children:[f(po,{sx:{fontSize:14,mr:.5}}),"Remarks"]})}),f(E,{sx:{maxHeight:120,overflowY:"auto",display:"flex",flexWrap:"wrap",gap:.5,p:.5,"&::-webkit-scrollbar":{height:"4px"},"&::-webkit-scrollbar-thumb":{background:y.box.scrollBackground,borderRadius:"4px"}},children:(x=(m=t==null?void 0:t.attributesDtos)==null?void 0:m[0])!=null&&x.remarks?f(Oo,{text:(b=(g=t==null?void 0:t.attributesDtos)==null?void 0:g[0])==null?void 0:b.remarks,maxChars:20,variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem"}}):f(M,{variant:"body2",color:"text.secondary",sx:{fontSize:"0.75rem",fontStyle:"italic"},children:"No remarks available"})})]})}),((t==null?void 0:t.status)===T.COMPLETED&&(t==null?void 0:t.processor)||(t==null?void 0:t.status)===T.CANCELED)&&C(F,{item:!0,xs:12,md:6,children:[f(M,{sx:{fontWeight:600,color:y.text.darkBlue,fontSize:"0.755rem"},children:"Status Info"}),C(E,{sx:{display:"flex",alignItems:"center",border:`1px solid ${y.border.cardBorder}`,borderRadius:"10px",p:2,bgcolor:y.chip.background,gap:2},children:[f(E,{sx:{display:"flex",alignItems:"center",justifyContent:"center",width:36,height:36,borderRadius:"50%",bgcolor:t.status===T.COMPLETED?"#D1FAE5":t.status===T.CANCELED?"#FEE2E2":"#E2E8F0",color:t.status===T.COMPLETED?"#059669":t.status===T.CANCELED?"#DC2626":"#64748B"},children:(t==null?void 0:t.status)===T.COMPLETED?f(me,{fontSize:"small"}):f(Ye,{fontSize:"small"})}),C(E,{children:[f(M,{variant:"body2",sx:{fontWeight:500,color:"#334155",mb:.5},children:t.status===T.COMPLETED?C(nt,{children:["Completed by"," ",f(E,{component:"span",sx:{color:"#1D4ED8",fontWeight:500},children:t.processor})]}):t.status===T.CANCELED?f(nt,{children:"Canceled"}):""}),f(M,{variant:"caption",sx:{color:"#64748B",fontSize:"0.75rem"},children:new Date(t.completedAt||t.createdAt).toLocaleString("en-US",{dateStyle:"medium",timeStyle:"short"})})]})]})]}),(t==null?void 0:t.status)===T.READY&&f(F,{item:!0,xs:12,children:C(E,{sx:{display:"flex",alignItems:"center",justifyContent:"center",py:1.5,px:2.5,borderRadius:"10px",backgroundColor:y.warning.pale,border:`1px dashed ${y.warning.orange}`,color:`${y.warning.orange} !important`,fontSize:"0.9375rem",letterSpacing:"0.5px",fontWeight:500,mt:1,mb:1},children:[f(Li,{fontSize:"small",sx:{mr:1.5}}),"In Progress - Awaiting Completion"]})})]})})]}),f(E,{sx:{width:"20%",pl:2},children:f(Gh,{item:t})})]})})})},Xh=async t=>{if(!t)return[];const e=[];let n={};if(Array.isArray(t))t.forEach((s,i)=>{const o=Object.keys(s)[0];n[o]=i,(s[o]||[]).forEach(a=>{var h,d,m,x,g;const l=(d=(h=a==null?void 0:a.attributesDtos)==null?void 0:h[0])==null?void 0:d.currentLevel,c=((x=(m=a==null?void 0:a.attributesDtos)==null?void 0:m[0])==null?void 0:x.currentLevelName)||o;let u=(a==null?void 0:a.status)||T.PENDING;e.push({id:a.id,level:parseInt(l,10),levelName:c,status:u,subject:((g=a.subject)==null?void 0:g.replace(/ & \d+$/,""))||"",createdBy:a.createdBy,createdAt:a.createdAt,performedBy:a.processor,completedAt:a.completedAt,comment:a.description,recipients:a.recipientUsers||[],originalLevelKey:o,displayOrder:i})})});else if(t&&typeof t=="object"){const s=Object.keys(t);s.forEach((i,o)=>{n[i]=o}),s.forEach(i=>{(t[i]||[]).forEach(r=>{var u,h,d,m,x;const a=(h=(u=r==null?void 0:r.attributesDtos)==null?void 0:u[0])==null?void 0:h.currentLevel,l=((m=(d=r==null?void 0:r.attributesDtos)==null?void 0:d[0])==null?void 0:m.currentLevelName)||i;let c=(r==null?void 0:r.status)||T.PENDING;e.push({id:r.id,level:parseInt(a,10),levelName:l,status:c,subject:((x=r.subject)==null?void 0:x.replace(/ & \d+$/,""))||"",createdBy:r.createdBy,createdAt:r.createdAt,performedBy:r.processor,completedAt:r.completedAt,comment:r.description,recipients:r.recipientUsers||[],originalLevelKey:i,displayOrder:n[i]})})})}return e},Zh=t=>!t||t.length===0?0:t.filter(e=>e.status!==T.READY&&e.status!==T.PENDING&&e.status!==T.RESERVED).length,Qh=t=>!t||(t==null?void 0:t.length)===0?T.PENDING:(t==null?void 0:t.every(o=>o.status===T.CANCELED))?T.CANCELED:(t==null?void 0:t.some(o=>o.status===T.READY))?T.READY:(t==null?void 0:t.every(o=>o.status===T.COMPLETED))?T.COMPLETED:(t==null?void 0:t.every(o=>o.status===T.RESERVED))?T.RESERVED:T.PENDING,Jh=({data:t,childRequestID:e})=>{const[n,s]=w.useState([]),[i,o]=w.useState(!1),[r,a]=w.useState(!0);w.useEffect(()=>{(n==null?void 0:n.length)==0?a(!0):a(!1)},[n]);const c=(()=>!t||!Array.isArray(t)||t.length===0?[]:t==null?void 0:t.map((S,v)=>{const[P,A]=Object.entries(S)[0];return{levelName:P,items:Array.isArray(A)?A:[],status:Qh(Array.isArray(A)?A:[]),completedCount:Zh(Array.isArray(A)?A:[]),totalCount:Array.isArray(A)?A.length:0,originalLevelNumber:v,displayOrder:v}}))();if(t&&Object.keys(t).length>0){const p=Object.keys(t),S={};p.forEach((v,P)=>{const A=parseInt(v,10);S[A]=P}),c.sort((v,P)=>{const A=S[v.originalLevelNumber]!==void 0?S[v.originalLevelNumber]:999,R=S[P.originalLevelNumber]!==void 0?S[P.originalLevelNumber]:999;return A-R})}const[u,h]=w.useState(null);w.useEffect(()=>{var p;c!=null&&c.length&&u===null&&h((p=c[c.length-1])==null?void 0:p.originalLevelNumber)},[c,u]);const d=w.useRef({});w.useEffect(()=>{(async()=>{const S=await Xh(t);s(S)})()},[t]);const m=p=>{h(p)},x=p=>{switch(p){case T.COMPLETED:return"Completed";case T.CANCELED:return"Canceled";case T.READY:return"Ready";case T.RESERVED:return"Reserved";default:return"Pending"}},g=p=>{switch(p){case T.COMPLETED:return f(me,{fontSize:"small",sx:{color:y.success.deepGreen}});case T.CANCELED:return f(Ye,{fontSize:"small",sx:{color:y.error.deepRed}});case T.READY:return f(Li,{fontSize:"small",sx:{color:y.warning.orange}});case T.RESERVED:return f(jo,{fontSize:"small",sx:{color:y.secondary.yellow}});default:return f(No,{fontSize:"small",sx:{color:y.text.darkGrey}})}},b=c.find(p=>p.originalLevelNumber===u);return w.useEffect(()=>{u&&d.current[u]&&d.current[u].scrollIntoView({behavior:"smooth",block:"center"})},[u]),r===!0?f(mo,{}):C(E,{sx:{pt:2,height:"78vh",overflow:"hidden !important",display:"flex",flexDirection:"column",justifyContent:"flex-start"},children:[f(F,{container:!0,children:C(F,{container:!0,sx:{border:"1px solid #E2E8F0",overflow:"hidden",height:"100%",borderRadius:2,position:"relative",backgroundColor:"#FFFFFF"},children:[f(F,{item:!0,xs:12,md:4,lg:2,children:f(Lt,{elevation:0,sx:{p:0,overflow:"hidden",height:"100%",backgroundColor:"#FFFFFF"},children:C(E,{sx:{p:2,borderRight:"1px solid #E2E8F0",borderRadius:2,bgcolor:"#F8FAFC",height:"73vh",overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"3.5px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"#CBD5E1"},"&::-webkit-scrollbar-track":{backgroundColor:"#F1F5F9"}},children:["  ",(c==null?void 0:c.length)>0&&c.map((p,S)=>{const v=u===p.originalLevelNumber,P=S>0&&c[S-1].status===T.COMPLETED;return p.status===T.READY||(p.status,T.PENDING),C(E,{ref:A=>d.current[p.originalLevelNumber]=A,sx:{mb:2,position:"relative"},children:[C(E,{onClick:()=>m(p.originalLevelNumber),sx:{display:"flex",alignItems:"center",borderRadius:2,px:2,py:2,height:"auto",bgcolor:p.status===T.COMPLETED?v?y.success.completedBackground:y.success.pale:p.status===T.CANCELED?v?y.background.canceled:y.error.pale:p.status===T.READY?v?y.warning.light:y.warning.pale:p.status===T.RESERVED?v?y.secondary.lightYellow:y.secondary.lightYellow:y.background.subtle,border:v?"2px solid":"1px solid",borderColor:p.status===T.COMPLETED?y.success.deepGreen:p.status===T.CANCELED?y.error.deepRed:p.status===T.READY?y.warning.orange:p.status===T.RESERVED?y.secondary.yellow:y.border.cardBorder,cursor:"pointer",transition:"all 0.2s ease",boxShadow:v?"0 0 0 2px rgba(59, 130, 246, 0.2)":"none",position:"relative","&:hover":{bgcolor:p.status===T.COMPLETED?"#D1FAE5":p.status===T.CANCELED?"#FEE2E2":p.status===T.READY?"#FFEDD5":p.status===T.RESERVED?"#FEF9C3":"#E2E8F0"}},children:[f(E,{sx:{width:40,height:40,borderRadius:"50%",display:"flex",justifyContent:"center",alignItems:"center",mr:2,bgcolor:p.status===T.COMPLETED?"#ECFDF5":p.status===T.CANCELED?"#FEF2F2":p.status===T.READY?"#FFF7ED":"#F1F5F9",border:"2px solid",borderColor:p.status===T.COMPLETED?"#10B981":p.status===T.CANCELED?"#EF4444":p.status===T.READY?"orange":"#CBD5E1",color:p.status===T.COMPLETED?"#059669":p.status===T.CANCELED?"#DC2626":p.status===T.READY?"orange":"#64748B",boxShadow:v?"0 0 0 2px rgba(59, 130, 246, 0.15)":"none",transform:v?"scale(1.1)":"scale(1)",transition:"all 0.2s ease"},children:p.status===T.COMPLETED?f(me,{fontSize:"small"}):p.status===T.CANCELED?f(Ye,{fontSize:"small"}):g(p.status)}),C(E,{sx:{flex:1},children:[f(M,{variant:"subtitle1",sx:{fontWeight:600,color:"#334155",fontSize:"0.95rem"},children:(p==null?void 0:p.levelName)||"undefined"}),f(E,{sx:{display:"flex",alignItems:"center"}}),C(E,{sx:{mt:.7,height:"30px"},children:[f(Ro,{variant:"determinate",value:p.completedCount/p.totalCount*100,sx:{borderRadius:6,height:.07,backgroundColor:"#e6e0d3","& .MuiLinearProgress-bar":{backgroundColor:"#3B82F6"}}},S),C(M,{variant:"caption",sx:{mt:.5,color:"#475569"},children:[p.completedCount," / ",p.totalCount," completed"]})]})]}),v&&f(E,{sx:{position:"absolute",right:12,display:"flex",alignItems:"center",justifyContent:"center",width:24,height:24,borderRadius:"50%",bgcolor:y.primary.main,color:y.basic.white,boxShadow:"0 2px 4px rgba(0,0,0,0.1)",animation:"fadeIn 0.3s ease-in-out","@keyframes fadeIn":{"0%":{opacity:0,transform:"scale(0.8)"},"100%":{opacity:1,transform:"scale(1)"}}},children:f(Fo,{fontSize:"small",sx:{fontSize:12}})})]}),S<c.length-1&&f(E,{sx:{position:"absolute",left:35,height:37,top:96,bottom:-18,width:2,bgcolor:p.status===T.COMPLETED?y.success.deepGreen:y.border.cardBorder,zIndex:0}})]},p.originalLevelNumber)})]})})}),f(F,{item:!0,xs:12,md:8,lg:10,children:f(Lo,{in:!0,timeout:300,children:f(Lt,{elevation:0,sx:{display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:b&&C(nt,{children:[C(E,{sx:{p:3,borderBottom:"1px solid #F1F5F9",bgcolor:"#F8FAFC",display:"flex",justifyContent:"space-between",alignItems:"center",flexShrink:0},children:[C(E,{children:[C(E,{sx:{display:"flex",alignItems:"center"},children:[f(M,{variant:"h6",sx:{fontWeight:600,color:"#334155"},children:(b==null?void 0:b.levelName)||"undefined"}),f(Hh,{label:x(b.status),status:b.status,size:"small",sx:{ml:2}})]}),C(M,{variant:"body2",sx:{mt:1,color:"#64748B"},children:[b.items.length," ",b.items.length===1?"activity":"activities"]})]}),C(E,{sx:{display:"flex",alignItems:"center"},children:[c.length>1&&c.findIndex(p=>p.originalLevelNumber===u)>0&&f(ge,{size:"small",onClick:()=>{const p=c.findIndex(S=>S.originalLevelNumber===u);p>0&&m(c[p-1].originalLevelNumber)},variant:"outlined",sx:{mr:1,borderColor:y.primary.main,color:y.primary.main,"&:hover":{borderColor:y.primary.dark,bgcolor:y.primary.light}},children:"Previous Level"}),c.length>1&&c.findIndex(p=>p.originalLevelNumber===u)<c.length-1&&f(ge,{size:"small",onClick:()=>{const p=c.findIndex(S=>S.originalLevelNumber===u);p<c.length-1&&m(c[p+1].originalLevelNumber)},variant:"contained",sx:{bgcolor:"#3B82F6","&:hover":{bgcolor:"#2563EB"}},children:"Next Level"})]})]}),C(E,{sx:{p:3,flexGrow:1,height:"70px",overflowY:"auto"},children:[b.items.map((p,S)=>f(qh,{item:p,index:S,setOpenSnackbar:o},p.id)),b.items.length===0&&f(E,{sx:{textAlign:"center",py:8,color:"#94A3B8"},children:f(M,{variant:"body1",children:"No activities found for this level"})})]},b.levelNumber)]})})})})]})}),f(ko,{openSnackBar:i,alertMsg:"Task ID Copied Successfully",alertType:"Success",handleSnackBarClose:()=>o(!1)})]})},ad=()=>{var es,ns,ss,is,rs,os,as,ls,cs,us,hs,ds,fs,ps;const[t,e]=w.useState(!0),n=Uo(),s=n.state.requestId,i=n.state.module,[o,r]=w.useState([]),[a,l]=w.useState(""),[c,u]=w.useState([]),[h,d]=w.useState(!1),[m,x]=w.useState(!1),[g,b]=w.useState(""),[p,S]=w.useState([]),[v,P]=w.useState(!1),[A,R]=w.useState(!1),[B,V]=w.useState(""),[W,H]=w.useState(),[ut,Ae]=w.useState(""),{destination:oe}=Qo(i),ae=()=>{ts(!0),P(!0)},jt=()=>{d(!1)},Zn=()=>{R(!0)},N=()=>{R(!1),Qn(-1)},K={overflow:"scroll",position:"absolute",top:"50%",left:"52%",transform:"translate(-50%, -50%)",width:"70%",height:"70%",bgcolor:(ns=(es=y)==null?void 0:es.primary)==null?void 0:ns.white,boxShadow:4,p:1};w.useEffect(()=>{l(zo("CC"))},[]);const J=Wo(vs.REQUEST_BENCH_TASK,!0,{}),ht=`${J==null?void 0:J.childRequestIds}`,yt=$o(q=>{var st,tt;return(tt=(st=q.commonSearchBar)==null?void 0:st.RequestHistory)==null?void 0:tt.reqId})||ht,Qn=_o(),go=()=>{e(!1),u([]);const q=tt=>{r(tt==null?void 0:tt.body),e(!1)},st=()=>{e(!1)};Pe(`/${oe}/${Me.TASK_ACTION_DETAIL.FETCH_REQUEST_HISTORY}?requestId=${yt}`,"get",q,st)},yo=[{field:"id",headerName:"ID",flex:1,hide:!0},{field:"createdAt",headerName:"Notification Date",flex:.5,renderCell:q=>f(M,{sx:{fontSize:"12px"},children:_t(q.row.createdAt).format("DD MMM YYYY")})},{field:"subject",headerName:"Subject",flex:2},{field:"actions",align:"center",flex:1,headerAlign:"center",headerName:"Actions",sortable:!1,renderCell:q=>f("div",{children:f(bt,{title:"View Mail Body",children:f(At,{onClick:()=>{b(dt.find(st=>st.id==q.row.id))},children:f(Jo,{})})})})}],xo=()=>{d(!1),x(!1)},Jn=[{field:"id",headerName:"Document ID",flex:1,hide:!0},{field:"docName",headerName:"Document Name",flex:1},{field:"uploadedOn",headerName:"Uploaded On",flex:1,align:"center",headerAlign:"center"},{field:"uploadedBy",headerName:"Uploaded By",sortable:!1,flex:1},{field:"attachmentType",headerName:"Attachment Type",sortable:!1,flex:1},{field:"action",headerName:"Action",sortable:!1,filterable:!1,align:"center",headerAlign:"center",width:75,renderCell:q=>f(nt,{children:f(na,{index:q.row.id,name:q.row.docName})})}],vo=()=>{let q=st=>{var tt=[];st.documentDetailDtoList.forEach(at=>{var Ut={id:at.documentId,docType:at.fileType,docName:at.fileName,uploadedOn:at.docCreationDate,uploadedBy:at.createdBy,attachmentType:at.attachmentType};tt.push(Ut)}),S(tt)};Pe(`/${ta}/${Me.TASK_ACTION_DETAIL.GET_CHILD_DOCS}/${yt}`,"get",q)},[bo,ts]=w.useState(!1),To=()=>{ts(!1),P(!1)},[dt,So]=w.useState([]),Co=q=>{x(!0);let st=Ut=>{So(Ut==null?void 0:Ut.body)},tt=()=>{},at=`/${oe}/${Me.TASK_ACTION_DETAIL.FETCH_MAILS}?requestId=${yt}`;at&&Pe(at,"get",st,tt)};return w.useEffect(()=>(go(),vo(),()=>{Yo(vs.REQUEST_BENCH_TASK)}),[]),C("div",{id:"container_outermost",children:[C(ms,{fullWidth:!0,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:bo,sx:{"& .MuiDialog-container":{"& .MuiPaper-root":{width:"100%",maxWidth:"max-content"}}},children:[C(gs,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(is=(ss=y)==null?void 0:ss.background)==null?void 0:is.header,display:"flex"},children:[f(M,{variant:"h6",children:"Attachments: "}),f(At,{sx:{width:"max-content"},onClick:To,children:f(bs,{})})]}),f(xs,{sx:{padding:".5rem 1rem"},children:f($,{children:f(E,{sx:{minWidth:800},children:C(ys,{sx:{height:"auto"},fullWidth:!0,children:[!!(p!=null&&p.length)&&f(De,{width:"800px",rows:p,columns:Jn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!(p!=null&&p.length)&&f(M,{variant:"body2",children:"No Attachments Found"})]})})})})]}),C(ms,{open:h,onClose:jt,hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},children:[C(gs,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:(os=(rs=y)==null?void 0:rs.background)==null?void 0:os.header,display:"flex"},children:[" ",f(M,{variant:"h6",children:"Attachments"}),f(At,{sx:{width:"max-content"},onClick:jt,children:f(bs,{})})]}),f(xs,{sx:{padding:".5rem 1rem"},children:f($,{children:f(E,{sx:{minWidth:800},children:C(ys,{sx:{height:"auto"},fullWidth:!0,children:[!!p.length&&f(De,{width:"70vw",rows:p,columns:Jn,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,stopPropagation_Column:"action"}),!p.length&&f(M,{variant:"body2",children:"No Attachments Found"})]})})})})]}),f("div",{className:"purchaseOrder",style:{...Ko,backgroundColor:(ls=(as=y)==null?void 0:as.background)==null?void 0:ls.container},children:f($,{spacing:1,children:C(nt,{children:[C(F,{container:!0,sx:Go,children:[C(F,{item:!0,md:6,sx:{outerContainer_Information:Ho,display:"flex"},children:[f(F,{children:f(At,{color:"primary","aria-label":"upload picture",component:"label",sx:qo,children:f(Xo,{sx:{fontSize:"25px",color:(us=(cs=y)==null?void 0:cs.basic)==null?void 0:us.black},onClick:()=>{Qn(-1)}})})}),C(F,{children:[f(M,{variant:"h3",children:f("strong",{children:"Request History"})}),f(M,{variant:"body2",color:y.secondary.grey,children:"This view displays the history of a Request"})]})]}),f(F,{item:!0,md:6,sx:{display:"flex"},children:C(F,{container:!0,direction:"row",justifyContent:"flex-end",alignItems:"center",spacing:0,children:[f(bt,{title:"Mail",children:f(At,{children:f(ki,{onClick:Co})})}),f(bt,{title:"Uploaded Attachments",arrow:!0,children:f(At,{onClick:ae,sx:{"&:active":{backgroundColor:"rgba(17,52,166,0.3)",color:`${(ds=(hs=y)==null?void 0:hs.basic)==null?void 0:ds.black}`},backgroundColor:v?"rgba(17,52,166,0.3)":"transparent"},children:f(Ii,{})})})]})})]}),f(F,{container:!0,spacing:1,children:f(Zo,{open:m,onClose:xo,"aria-labelledby":"modal-modal-title","aria-describedby":"modal-modal-description",children:f(E,{sx:K,children:g!=""?f(nt,{children:(dt==null?void 0:dt.length)==0?C($,{justifyItems:"center",alignItems:"center",mt:5,children:[f(M,{variant:"h3",color:(ps=(fs=y)==null?void 0:fs.basic)==null?void 0:ps.black,children:"No Mail Found for this Request ID"}),f(ge,{size:"small",variant:"contained",sx:{marginRight:"1rem"},onClick:()=>{b("")},children:"Close"})]}):f(nt,{children:C(F,{container:!0,sx:{height:"100%",p:2},children:[f(F,{item:!0,xs:12,children:C(E,{sx:{border:`1px solid ${y.basic.black}`,borderRadius:"8px",width:"100%",height:"100%",boxSizing:"border-box",display:"flex",flexDirection:"column",backgroundColor:y.basic.white},p:3,children:[f(M,{variant:"h6",sx:{fontSize:"18px",fontWeight:"600",color:y.text.primary,mb:2},children:(g==null?void 0:g.subject)||"No Subject"}),f(E,{sx:{mb:2},children:C($,{spacing:1.5,children:[C($,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(M,{sx:{color:y.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"To:"}),f(M,{sx:{flex:1,fontSize:"13px",color:y.text.charcoal,wordBreak:"break-word"},children:g==null?void 0:g.toParticipant})]}),C($,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(M,{sx:{color:y.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"Cc:"}),f(M,{sx:{flex:1,fontSize:"13px",color:y.text.charcoal,wordBreak:"break-word"},children:g==null?void 0:g.ccParticipant})]}),C($,{direction:"row",spacing:1,alignItems:"flex-start",children:[f(M,{sx:{color:y.text.secondary,width:"40px",flexShrink:0,fontSize:"13px",fontWeight:"500"},children:"From:"}),f(M,{sx:{flex:1,fontSize:"13px",color:y.text.charcoal,wordBreak:"break-word"},children:g==null?void 0:g.fromUser})]}),f(M,{sx:{fontSize:"12px",color:y.text.secondary,borderBottom:`1px solid ${y.border.light}`,pb:2},children:_t(g==null?void 0:g.createdAt).format("DD MMM YYYY hh:mm:ss a")})]})}),f(E,{sx:{flexGrow:1,overflowY:"auto",backgroundColor:y.background.default,borderRadius:"4px",p:2,minHeight:"200px","& *":{fontFamily:"inherit"}},children:f("div",{dangerouslySetInnerHTML:{__html:g==null?void 0:g.content}})})]})}),f(F,{item:!0,xs:12,sx:{display:"flex",justifyContent:"flex-end",mt:2},children:f(ge,{size:"medium",variant:"contained",onClick:()=>b(""),sx:{minWidth:"100px",textTransform:"none",boxShadow:"none","&:hover":{boxShadow:"none"}},children:"Close"})})]})})}):f(nt,{children:f(De,{rows:dt,columns:yo,pageSize:10,getRowIdValue:"id",hideFooter:!1,title:`Email List (${dt==null?void 0:dt.length})`})})})})}),C(M,{variant:"h6",sx:{mt:2,mb:2},children:["Parent Request ID: ",s]}),f(E,{sx:{textAlign:"center"},children:C(M,{variant:"h4",sx:{color:"#334155",fontWeight:"bold"},children:["📌 Task Activity Timeline ",yt]})}),(o==null?void 0:o.length)>0?f(Jh,{data:o,childRequestID:yt}):f(mo,{})]})})}),f(ea,{dialogState:A,openReusableDialog:Zn,closeReusableDialog:N,dialogTitle:B,dialogMessage:W,handleDialogConfirm:N,dialogOkText:"OK",dialogSeverity:ut})]})};export{ad as default};
