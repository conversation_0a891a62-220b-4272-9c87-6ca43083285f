import { useEffect, useState } from "react";
import { Grid, Typo<PERSON>, <PERSON>, Box, Stack, Chip,
  IconButton, FormControlLabel, Checkbox,
  CircularProgress,
  useTheme,
 }from "@mui/material";
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineDot,
  TimelineContent,
  TimelineConnector,
} from "@mui/lab";
import { Avatar, CardContent, Divider } from "@mui/material"
import { Message, MoreHoriz } from "@mui/icons-material"
 
import {
  Download as DownloadIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  InsertDriveFile as FileIcon,
  Visibility as VisibilityIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material"
import TableViewIcon  from '@mui/icons-material/TableView';
import moment from "moment";
import { container_Padding } from "../../common/commonStyles";
import { useSelector } from "react-redux";
import { MatDownload, MatView, DeleteRecord } from "../../DocumentManagement/UtilDoc";
import ReusableTable from "../../Common/ReusableTable";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import ReusableAttachementAndComments from "../../Common/ReusableAttachmentAndComments/ReusableAttachementAndComments";
import { doAjax } from "../../Common/fetchService";
import { setAttachmentType } from "../../../app/initialDataSlice";
import { useDispatch } from "react-redux";
import { colors } from "@constant/colors";
import AttachFileIcon from "@mui/icons-material/AttachFile";
import { REQUEST_STATUS, REQUEST_TYPE,BUTTON_NAME, ENABLE_STATUSES, INITIAL_PAYLOAD_MAP, MODULE_MAP } from "../../../constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLang from "@hooks/useLang";
import { useLocation } from "react-router-dom";
import BottomNav from "@material/BottomNav";
import { BUTTONS_ACTION_TYPE, TABS_NAME_VALUE } from "@constant/buttonPriority";
import { checkIncludedAndValidated, filterButtonsBasedOnTab,filterNavigation } from "@helper/helper";
import {useExtendDynamicButton} from '@hooks/useExtendDynamicButton';
import { destination_IDM } from "../../../../src/destinationVariables";
import {useGetChildAttachmentsQuery} from '@api/document/attachmentsApi'
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
 
const getFileIcon = (fileName) => {
  const extension = fileName?.split(".")?.pop()?.toLowerCase() || "";
 
  const iconProps = { fontSize: "small", sx: { mr: 1 } };
 
  switch (extension) {
    case "xlsx":
    case "xls":
    case "csv":
     
      return <TableViewIcon sx={{ color: colors.icon.excel }} />;
    case "pdf":
      return <PdfIcon {...iconProps} sx={{ color: colors.icon.pdf }} />;
    case "doc":
    case "docx":
      return <DocumentIcon {...iconProps} sx={{ color: colors.icon.doc }} />;
    case "ppt":
    case "pptx":
      return <DocumentIcon {...iconProps} sx={{ color: colors.icon.ppt }} />;
    default:
      return <FileIcon {...iconProps} sx={{ color: colors.icon.file }} />;
  }
};
 
const AttachmentsCommentsTab = ({
  attachmentsData = [],
  requestIdHeader = "",
  pcNumber = "",
  disabled = false,
  requestStatus,
  module,
  artifactName,
}) => {
  const [DownloadLoader, setDownloadLoader] = useState({});
  const appSettings = useSelector((state) => state.appSettings);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues)
  const [attachments, setAttachments] = useState([]);
  const [comments, setComments] = useState([]);
  const dispatch = useDispatch();
  const { t } = useLang();
  const theme = useTheme()
  const taskData = useSelector((state) => state.userManagement.taskData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const storedRows = useSelector((state) => state.request.materialRows);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const selectedModuleInitialPayload = INITIAL_PAYLOAD_MAP[module] || (() => ({}));
  const initialPayload = useSelector(selectedModuleInitialPayload);
  const changePayloadData = useSelector((state) => state?.payload?.dynamicKeyValues?.childRequestHeaderData)
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const reqBenchData = location?.state
  const hasAnyChildRequestId = changePayloadData?.ChildRequestId || initialPayload?.childRequestId;
  const {extendFilteredButtons} = useExtendDynamicButton(taskData,applicationConfig,destination_IDM,BUTTON_NAME)
  const { data } = useGetChildAttachmentsQuery({ requestId: requestIdHeader, hasAnyChildRequestId });
 
  let AttachmentsButton;
  const actionTypesToFilter = [
      BUTTONS_ACTION_TYPE.HANDLE_SEND_BACK,
      BUTTONS_ACTION_TYPE.HANDLE_VALIDATE,
      BUTTONS_ACTION_TYPE.HANDLE_CORRECTION,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,
      BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW,
      BUTTONS_ACTION_TYPE.HANDLE_SUBMIT
    ];
   
    if(initialPayload?.RequestType === REQUEST_TYPE.EXTEND ||
        initialPayload?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD){
            AttachmentsButton = filterButtonsBasedOnTab(
        extendFilteredButtons,
        actionTypesToFilter
      );
        }else{
          AttachmentsButton = []
        }
 
  const handleSnackbarOpen = () => {
    setShowSnackbar(true);
  };
 
  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };
 
  const disableCheck = !ENABLE_STATUSES.includes(requestStatus) || (requestIdHeader && !isreqBench);
 
  const soothingColors = {
    primary: colors.blue.main,
    light: colors.text.offWhite,
    accent: colors.primary.grey,
    text: colors.text.charcoal,
    secondaryText: colors.text.greyBlue,
    background: colors.background.paper,
  };
 
  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1.2,
      hideable: false,
      hidden: true,
    },
    {
      field: "attachmentType",
      headerName: t("Attachment Type"),
      flex: 1.5,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          sx={{
            backgroundColor: colors?.reportTile.lightBlue,
            color: colors.primary.lightPlus,
            fontWeight: "medium",
          }}
        />
      ),
    },
    {
      field: "docName",
      headerName: t("Document Name"),
      flex: 2,
      renderCell: (params) => (
        <Stack direction="row" spacing={1} alignItems="center">
        {getFileIcon(params.value)}
        <Typography variant="body2">{params.value}</Typography>
      </Stack>
      ),
    },
    {
      field: "uploadedOn",
      headerName: t("Uploaded On"),
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "uploadedBy",
      headerName: t("Uploaded By"),
      sortable: false,
      flex: 1,
    },
    {
      field: "view",
      headerName: t("View"),
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (
        cellValues
      ) => (
        <IconButton
          size="small"
          sx={{
            color: colors.icon.matView,
            "&:hover": { backgroundColor: "rgba(2, 136, 209, 0.1)" },
          }}
        >
          <MatView
              index={cellValues.row.id}
              name={cellValues?.row?.docName || cellValues?.row?.fileName}
              documentViewUrl={cellValues.row.documentViewUrl}
            />
        </IconButton>
      ),
    },
    {
      field: "action",
      headerName: t("Action"),
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (
        cellValues
      ) => (
        <Stack direction="row" spacing={0}>
          <IconButton
            size="small"
            sx={{
              color: colors?.icon.matDownload,
              "&:hover": { backgroundColor: "rgba(46, 125, 50, 0.1)" },
            }}
          >
              <MatDownload
              index={cellValues.row.id}
              name={cellValues?.row?.docName || cellValues?.row?.fileName}
 
            />
       
          </IconButton>
          <IconButton
            size="small"
            sx={{
              "&:hover": { backgroundColor: "rgba(241, 18, 18, 0.1)" },
            }}
          >
               <DeleteRecord  
                  index={cellValues.row.id}
                  name={cellValues.row.docName || cellValues?.row?.fileName}
                  setSnackbar={setShowSnackbar}
                  setopenSnackbar={setShowSnackbar}
                  setMessageDialogMessage={setMessageDialogMessage}
                  handleSnackbarOpen={handleSnackbarOpen}
                  setDownloadLoader={setDownloadLoader}
                  DownloadLoader={DownloadLoader}
                  requestId={requestIdHeader}
                  hasAnyChildRequestId={hasAnyChildRequestId}
                  iconColor={colors?.icon?.delete}
                  />
       
          </IconButton>
        </Stack>
      ),
    },
  ]
 
  useEffect(() => {
    if(isreqBench && reqBenchData?.reqStatus === (REQUEST_STATUS?.SYNDICATED_IN_SAP_DIRECT || REQUEST_STATUS?.SYNDICATED_PARTIALLY_DIRECT || REQUEST_STATUS?.SYNDICATION_FAILED_DIRECT)){
      let commentRows = [];
      var tempRow = {
        id: reqBenchData.requestId,
        comment: dynamicData?.otherPayloadData?.["Comments"] || "",
        user: reqBenchData.createdBy,
        createdAt: reqBenchData.changedOnAct,
        taskName: "Direct Syndication Task",
        role: "Requestor"
      };
      commentRows.push(tempRow);
      setComments(commentRows);
      return;
    }
    getComments();
  },[])
 
  useEffect(() => {
    if (data) {
      const attachmentRows = data.documentDetailDtoList.map((doc) => ({
        id: doc.documentId,
        docType: doc.fileType,
        docName: doc.fileName,
        uploadedOn: moment(doc.docCreationDate).format(appSettings.dateFormat),
        uploadedBy: doc.createdBy,
        attachmentType: doc.attachmentType,
        documentViewUrl: doc.documentViewUrl,
      }));
      dispatch(setAttachmentType(attachmentRows[0]?.attachmentType));
      setAttachments(attachmentRows);
    }
  }, [data]);
 
  const getComments = () => {
    const { destination } = filterNavigation(module);
    let requestId = requestIdHeader
    let hSuccess = (data) => {
      var commentRows = [];
      data.body.forEach((cmt) => {
        var tempRow = {
          id: cmt.requestId,
          comment: cmt.comment,
          user: cmt.createdByUser,
          createdAt: cmt.updatedAt,
          taskName: cmt.taskName,
          role:cmt.createdByRole
        };
        commentRows.push(tempRow);
      });
      setComments(commentRows);
    };
 
    let hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination}/${END_POINTS.TASK_ACTION_DETAIL.TASKDETAILS_FOR_REQUESTID}?requestId=${requestId}`,
      "get",
      hSuccess,
      hError
    );
  };
 
  return (
    <div>
      <Grid container spacing={2} sx={{padding:'10px',margin:0}}>
 
        {/* Attachments Table Section */}
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: colors.primary.white,
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: "1px solid #E0E0E0",
            mt: 2,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            ...container_Padding,
          }}
        >
          <Grid container sx={{ display: "flex", justifyContent: "space-between",flexDirection:'row',alignItems:'center' }}>
            <Box sx={{ display: "flex", justifyContent: "space-between",flexDirection:'row', paddingBottom:"12px",alignItems:'space-between',width:'100%' }}>
            <Typography variant="h6">
              <strong>{t("Attachments")}</strong>
            </Typography>
            {!disabled && attachmentsData?.map((attachment, index) => (
                  <ReusableAttachementAndComments
                    title="Material"
                    module={module}
                    useMetaData={false}
                    artifactId={`${attachment.MDG_ATTACHMENTS_NAME}_${pcNumber}`}
                    artifactName={artifactName}
                    attachmentType={attachment.MDG_ATTACHMENTS_NAME}
                    requestId={requestIdHeader}
                    attachments = {attachments}
                  />
            ))}
       
            </Box>
          </Grid>
          {attachments.length > 0 ? (
            <ReusableTable
              width="100%"
              rows={attachments}
              columns={attachmentColumns}
              hideFooter={false}
              getRowIdValue="id"
              autoHeight={true}
              disableSelectionOnClick={true}
              stopPropagation_Column="action"
            />
          ) : (
            <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
            <AttachFileIcon sx={{ fontSize: 40, color: soothingColors.accent,transform:'rotate(90deg)' }} />
            <Typography variant="body2" color={soothingColors.secondaryText}>
            {t("No Attachments Found")}
            </Typography>
          </Stack>
          )}
 
          {/* Comments Section */}
          {comments.length > 0 && (
          <Box sx={{ maxWidth: "100%", bgcolor: soothingColors.background, borderRadius: "12px", padding:"12px", marginTop:'12px', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
            <Stack direction="row" alignItems="center" spacing={1} mb={3}>
              <Typography variant="h6" fontWeight="bold" color={soothingColors.text}>
                {t("Comments")}
              </Typography>
            </Stack>
            
              <Timeline position="right"
              sx={{
                p: 0,
                m: 0,
                '& .MuiTimelineItem-root:before': {
                  flex: 0,
                  padding: 0,
                },
              }}>
                {comments.map((comment, index) => (
                  <TimelineItem key={comment.id} sx={{}}>
                    <TimelineSeparator>
                      <TimelineDot
                        sx={{
                          bgcolor: soothingColors.light,
                          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        }}
                      >
                        <Message size={18} sx={{color: theme.palette.primary.main }} />
          
                      </TimelineDot>
                    
                        <TimelineConnector sx={{ width: '2.2px' }} />
                  
                    </TimelineSeparator>
                    <TimelineContent sx={{ py: '12px', px: 2 }}>
                      <Card
                        variant="outlined"
                        sx={{
                          borderRadius: "12px",
                          borderColor: soothingColors.accent,
                          background: 'linear-gradient(135deg, #FFFFFF 0%, #F9FAFB 100%)',
                          transition: 'all 0.3s ease',
                        }}
                      >
                        <CardContent sx={{ p: 2, width: '100%', minWidth: 0 }}>
                          <Stack direction="row" justifyContent="space-between" alignItems="center">
                            <Stack direction="row" spacing={1.5} alignItems="center">
                              <Avatar
                                sx={{
                                  bgcolor: theme.palette.primary.light,
                                  color: theme.palette.primary.main,
                                  boxShadow: 'none',
                                  width: 32,
                                  height: 32,
                                  fontSize: '14px',
                                  transition: 'all 0.3s ease',
                                  '&:hover': { transform: 'rotate(5deg)' },
                                }}
                              >
                                {comment.user.charAt(0).toUpperCase()}
                              </Avatar>
                              <Typography fontWeight="bold" color={soothingColors.text}>
                                {comment.user}
                              </Typography>
                              <Chip
                                label={comment.taskName}
                                size="small"
                                sx={{
                                backgroundColor: theme.palette.primary.light,
                                  color: theme.palette.primary.main,
                                  fontSize: "12px",
                                  borderRadius: '16px',
                                }}
                              />
                            </Stack>
                            <Stack direction="row" alignItems="center" spacing={1}>
                              <Typography variant="body2" color={soothingColors.secondaryText} sx={{ fontSize: '12px' }}>
                                  {new Date(comment.createdAt?.replace(" ", "T") + "Z").toLocaleString(undefined, {timeStyle: 'short', dateStyle: 'short'})}
                              </Typography>
                            </Stack>
                          </Stack>
                          <Divider sx={{ my: 2, borderColor: soothingColors.accent }} />
                          <Typography variant="body2" color={soothingColors.text} sx={{my:1 ,fontSize: '14px', lineHeight: '1.6', wordBreak: 'break-word', overflowWrap: 'anywhere'}}>
                            {comment.comment || "-"}
                          </Typography>
                        </CardContent>
                      </Card>
                    </TimelineContent>
                  </TimelineItem>
                ))}
              </Timeline>
          </Box>
          )}
    
        </Grid>
      </Grid>
      {(!disableCheck || (requestIdHeader && !isreqBench) || (isreqBench && ENABLE_STATUSES.includes(requestStatus))) &&
        <Box sx={{
          borderTop: `1px solid ${colors.placeholder.light}`,
          padding: '16px'
        }}>
          {module === MODULE_MAP?.MAT || module === MODULE_MAP?.ART ? (
            <BottomNav
              activeTab={TABS_NAME_VALUE.ATTACHMENT_AND_COMMENTS}
              submitForApprovalDisabled={!checkIncludedAndValidated(storedRows)}
              filteredButtons={AttachmentsButton}
              module={module}
            />
          ) : (
            <BottomNavGlobal
              filteredButtons={AttachmentsButton}
              moduleName={module}
            />
          )}
        </Box>
      }
      {showSnackbar && (
        <ReusableSnackBar
          openSnackBar={showSnackbar}
          alertMsg={messageDialogMessage}
          handleSnackBarClose={handleSnackbarClose}
        />
      )}
    </div>
  );
};
 
export default AttachmentsCommentsTab;