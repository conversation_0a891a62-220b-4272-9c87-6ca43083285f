import React, { useState, useEffect, useCallback } from "react";
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, List, ListItem, Switch, Divider, Box, CircularProgress, Typography, FormControl, Select, MenuItem, Grid, Tabs, Tab, IconButton, ToggleButton, ToggleButtonGroup } from "@mui/material";
import DragIndicatorIcon from "@mui/icons-material/DragIndicator";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { useSelector } from "react-redux";

import { destination_Dashboard } from "../../destinationVariables";
import { CHART_TYPE } from "../../constant/enum";
import { doAjax } from "../Common/fetchService";
import { END_POINTS } from "../../constant/apiEndPoints";
import useLang from "@hooks/useLang";

// Helper function to parse options from decision table
const parseOptionsFromString = (optionsString) => {
  if (!optionsString) return [];
  return optionsString.split(',').map(option => ({
    value: option.trim(),
    label: option.trim()
  }));
};

// Mapping function to convert decision table labels to enum values
const mapChartTypeToEnum = (label) => {
  const labelMap = {
    'Bar': CHART_TYPE.BAR,
    'Stacked Bar': CHART_TYPE.STACKED_BAR,
    'Column': CHART_TYPE.COLUMN,
    'Stacked Column': CHART_TYPE.STACK_COLUMN,
    'Line': CHART_TYPE.LINE,
    'Stacked Line': CHART_TYPE.STACKED_LINE,
    'Area': CHART_TYPE.AREA,
    'Stacked Area': CHART_TYPE.STACKED_AREA,
    'Pie': CHART_TYPE.PIE,
    'Donut': CHART_TYPE.DONUT
  };

  return labelMap[label] || label.toUpperCase().replace(/\s+/g, '_');
};

// Helper function to get chart type options for a specific KPI
const getChartTypeOptions = (kpiId, decisionTableConfig) => {
  const kpi = decisionTableConfig?.find(item => item.MDG_KPI_ID === kpiId);
  if (!kpi?.MDG_KPI_GRAPH_OPTIONS) return [];

  return parseOptionsFromString(kpi.MDG_KPI_GRAPH_OPTIONS).map(option => ({
    value: mapChartTypeToEnum(option.value),
    label: option.label
  }));
};

// Helper function to get color palette options for a specific KPI
const getColorPaletteOptions = (kpiId, decisionTableConfig) => {
  const kpi = decisionTableConfig?.find(item => item.MDG_KPI_ID === kpiId);
  if (!kpi?.MDG_KPI_PALLET_OPTIONS) return [];

  return parseOptionsFromString(kpi.MDG_KPI_PALLET_OPTIONS);
};
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`dashboard-tabpanel-${index}`} aria-labelledby={`dashboard-tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 1 }}>{children}</Box>}
    </div>
  );
}

const ManageDashboardDialog = ({ open, onClose, onSave, decisionTableConfig, userPreferences, reportConfig, onRefreshSpecificGraphs }) => {
  const [tabValue, setTabValue] = useState(0);
  const [graphSettings, setGraphSettings] = useState([]);
  const [reportSettings, setReportSettings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [savingChanges, setSavingChanges] = useState(false);
  const [changedKpis, setChangedKpis] = useState({});
  const [changedReports, setChangedReports] = useState({});
  const [visibilityChangedKpis, setVisibilityChangedKpis] = useState({});
  const [sequencingMode, setSequencingMode] = useState('section'); // 'section' or 'overall'
  const { t } = useLang();

  const userData = useSelector((state) => state.userManagement.userData);
  const userId = userData?.user_id || "";
  const handleTabChange = (_, newValue) => {
    setTabValue(newValue);
  };

  // Drag and Drop Handlers
  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const { source, destination, type } = result;

    if (type === 'KPI_METRICS') {
      handleKpiMetricsDragEnd(source, destination);
    } else if (type === 'KPI_REPORTS') {
      handleKpiReportsDragEnd(source, destination);
    }
  };

  const handleKpiMetricsDragEnd = (source, destination) => {
    if (sequencingMode === 'section') {
      const sourceSection = source.droppableId;
      const destSection = destination.droppableId;

      if (sourceSection === destSection) {
        const sectionGraphs = graphSettings.filter(g => (g.section || 'General') === sourceSection);
        const sortedSectionGraphs = sectionGraphs.sort((a, b) => (a.secKpiSequence || 0) - (b.secKpiSequence || 0));

        const [reorderedItem] = sortedSectionGraphs.splice(source.index, 1);
        sortedSectionGraphs.splice(destination.index, 0, reorderedItem);

        // Update secKpiSequence for items in this section
        const updatedGraphs = graphSettings.map(graph => {
          if ((graph.section || 'General') === sourceSection) {
            const newIndex = sortedSectionGraphs.findIndex(g => g.id === graph.id);
            return { ...graph, secKpiSequence: newIndex + 1 };
          }
          return graph;
        });

        setGraphSettings(updatedGraphs);

        // Mark all items in this section as changed
        const sectionChanges = {};
        sortedSectionGraphs.forEach(graph => {
          sectionChanges[graph.id] = true;
        });
        setChangedKpis(prev => ({ ...prev, ...sectionChanges }));
      }
    } else {
      // Overall sequencing drag and drop
      const sortedGraphs = [...graphSettings].sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
      const [reorderedItem] = sortedGraphs.splice(source.index, 1);
      sortedGraphs.splice(destination.index, 0, reorderedItem);

      // Update sequence for all items
      const updatedGraphs = sortedGraphs.map((graph, index) => ({
        ...graph,
        sequence: index + 1
      }));

      setGraphSettings(updatedGraphs);

      // Mark all items as changed
      const allChanges = {};
      updatedGraphs.forEach(graph => {
        allChanges[graph.id] = true;
      });
      setChangedKpis(prev => ({ ...prev, ...allChanges }));
    }
  };

  const handleKpiReportsDragEnd = (source, destination) => {
    if (sequencingMode === 'section') {
      // Section-based drag and drop for reports
      const sourceSection = source.droppableId;
      const destSection = destination.droppableId;

      if (sourceSection === destSection) {
        const sectionReports = reportSettings.filter(r => (r.section || 'General') === sourceSection);
        const sortedSectionReports = sectionReports.sort((a, b) => (a.secKpiSequence || 0) - (b.secKpiSequence || 0));

        const [reorderedItem] = sortedSectionReports.splice(source.index, 1);
        sortedSectionReports.splice(destination.index, 0, reorderedItem);

        const updatedReports = reportSettings.map(report => {
          if ((report.section || 'General') === sourceSection) {
            const newIndex = sortedSectionReports.findIndex(r => r.id === report.id);
            return { ...report, secKpiSequence: newIndex + 1 };
          }
          return report;
        });

        setReportSettings(updatedReports);

        const sectionChanges = {};
        sortedSectionReports.forEach(report => {
          sectionChanges[report.id] = true;
        });
        setChangedReports(prev => ({ ...prev, ...sectionChanges }));
      }
    } else {
      // Overall sequencing for reports
      const sortedReports = [...reportSettings].sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
      const [reorderedItem] = sortedReports.splice(source.index, 1);
      sortedReports.splice(destination.index, 0, reorderedItem);

      const updatedReports = sortedReports.map((report, index) => ({
        ...report,
        sequence: index + 1
      }));

      setReportSettings(updatedReports);

      const allChanges = {};
      updatedReports.forEach(report => {
        allChanges[report.id] = true;
      });
      setChangedReports(prev => ({ ...prev, ...allChanges }));
    }
  };

  const initializeSettings = useCallback(() => {
    if (decisionTableConfig?.length > 0) {
      const settings = decisionTableConfig.map((kpi) => {
        const userPref = userPreferences?.find((p) => p.KpiId === kpi.MDG_KPI_ID);
        return {
          id: kpi.MDG_KPI_ID,
          prefId: userPref?.Id || null,
          name: kpi.MDG_KPI_NAME,
          enabled: String(kpi.MDG_KPI_VISIBILITY).toLowerCase() === "true", // <-- FIXED
          userEnabled: userPref ? userPref.KpiVisibility === true && userPref.IsActive === true : false,
          sequence: userPref?.KpiSequence || kpi.MDG_KPI_GRAPH_SEQUENCE || 0,
          sectionSequence: userPref?.SectionSequence || 0,
          secKpiSequence: userPref?.SecKpiSequence || 0,
          chartType: userPref?.KpiChartType || kpi.MDG_KPI_GRAPH_TYPE,
          column: kpi.MDG_KPI_GRAPH_COLUMN?.toLowerCase(),
          colorPallet:userPref?.KpiColPallet|| kpi.MDG_KPI_COLOR_PALLET,
          section: kpi.MDG_KPI_SECTION_NAME || 'General',
        };
      });
      const reports = reportConfig.map((report) => {
        const userPref = userPreferences?.find((p) => p.KpiId === report.MDG_KPI_ID);
        return {
          id: report.MDG_KPI_ID,
          prefId: userPref?.Id || null,
          name: report.MDG_KPI_NAME,
          enabled: ["true", "enabled"].includes(String(report.MDG_KPI_VISIBILITY).toLowerCase()),
          userEnabled: userPref ? userPref.KpiVisibility === true && userPref.IsActive === true : true,
          sequence: userPref?.KpiSequence || report.MDG_KPI_GRAPH_SEQUENCE || 0,
          sectionSequence: userPref?.SectionSequence || 0,
          secKpiSequence: userPref?.SecKpiSequence || 0,
          section: report.MDG_KPI_SECTION_NAME || 'General',
        };
      });
      
      setGraphSettings(settings);
      setReportSettings(reports);
      setChangedKpis({});
      setChangedReports({});
      setVisibilityChangedKpis({});
      setLoading(false);
    }
  }, [decisionTableConfig, userPreferences, reportConfig]);

 useEffect(() => {
  if (open) {
    setLoading(true);
    initializeSettings();
  } else {
    setTabValue(0); // reset only on close
  }
}, [open, initializeSettings]);

  const handleToggleGraph = (id) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, userEnabled: !graph.userEnabled } : graph)));

    // Track visibility changes separately
    setVisibilityChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));

    // Also mark as changed for preference saving
    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };
  const handleToggleReport = (id) => {
    setReportSettings((prev) => prev.map((report) => (report.id === id ? { ...report, userEnabled: !report.userEnabled } : report)));

    setChangedReports((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleChartTypeChange = (id, newChartType) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, chartType: newChartType } : graph)));
    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleColorPaletteChange = (id, newColorPalette) => {
    setGraphSettings((prev) => prev.map((graph) => (graph.id === id ? { ...graph, colorPallet: newColorPalette } : graph)));
    setChangedKpis((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  const handleSaveChanges = async () => {
    setSavingChanges(true);
    
    try {
      const changedKpiIds = Object.keys(changedKpis);
      const changedReportIds = Object.keys(changedReports);

      if (changedKpiIds.length === 0 && changedReportIds.length === 0) {
        onClose();
        return;
      }
      const kpiPrefs = graphSettings
        .filter((graph) => changedKpiIds.includes(graph.id))
        .map((graph) => ({
          Id: graph.prefId,
          UserId: userId,
          KpiId: graph.id,
          KpiType: "KPI Metrics",
          KpiChartType: graph.chartType,
          KpiChartName: graph.name,
          KpiColPallet: graph.colorPallet,
          KpiSequence: Number(graph.sequence),
          SectionSequence: Number(graph.sectionSequence),
          SecKpiSequence: Number(graph.secKpiSequence),
          KpiColumn: graph.column,
          KpiVisibility: graph.userEnabled,
          IsActive: graph.userEnabled,
        }));

      const reportPrefs = reportSettings
        .filter((report) => changedReportIds.includes(report.id))
        .map((report) => ({
          Id: report.prefId,
          UserId: userId,
          KpiId: report.id,
          KpiType: "KPI Reports",
          KpiChartType: "REPORT",
          KpiChartName: report.name,
          KpiColPallet: "",
          KpiSequence: 0,
          SectionSequence: 0,
          SecKpiSequence: 0,
          KpiColumn: "",
          KpiVisibility: report.userEnabled,
          IsActive: report.userEnabled,
        }));

      const newPrefs = [...kpiPrefs, ...reportPrefs];

      // Save user preferences
      await new Promise((resolve, reject) => {
        doAjax(`/${destination_Dashboard}${END_POINTS.DASHBOARD_APIS.SAVE_USER_CONFIG}`, "post", resolve, reject, newPrefs);
      });

      // Handle different types of changes
      const visibilityChangedKpiIds = Object.keys(visibilityChangedKpis);
      const settingChangedKpiIds = changedKpiIds.filter(id => !visibilityChangedKpiIds.includes(id));

      // Determine if we have visibility changes
      const hasVisibilityChanges = visibilityChangedKpiIds.length > 0;

      if (!hasVisibilityChanges && onRefreshSpecificGraphs && settingChangedKpiIds.length > 0) {
        // For setting changes only (chart type, color), refresh only those graphs
        onRefreshSpecificGraphs(settingChangedKpiIds);
      }

      // Call the onSave callback with visibility change flag
      if (onSave) {
        onSave(changedKpiIds, changedReportIds, hasVisibilityChanges);
      } else {
        onClose(); // Fallback to just closing if no onSave provided
      }
    } catch (error) {
      onClose();
    } finally {
      setSavingChanges(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="md">
      <DialogTitle>{t("Manage Dashboard")}</DialogTitle>
      <DialogContent dividers>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <DragDropContext onDragEnd={handleDragEnd}>
            {/* Sequencing Mode Toggle */}
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6">{t("Graph Sequencing")}</Typography>
              <ToggleButtonGroup
                value={sequencingMode}
                exclusive
                onChange={(_, newMode) => {
                  if (newMode !== null) {
                    setSequencingMode(newMode);
                  }
                }}
                aria-label="sequencing mode"
                size="small"
              >
                <ToggleButton value="section" aria-label="section-based">
                  {t("Section-Based")}
                </ToggleButton>
                <ToggleButton value="overall" aria-label="overall">
                  {t("Overall Sequence")}
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>

            <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}>
              <Tabs value={tabValue} onChange={handleTabChange} aria-label="dashboard management tabs">
                <Tab label={t("KPI Metrics")} id="dashboard-tab-0" aria-controls="dashboard-tabpanel-0" />
                <Tab label={t("KPI Reports")} id="dashboard-tab-1" aria-controls="dashboard-tabpanel-1" />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              {sequencingMode === 'section' ? (
                // Section-Based Sequencing
                (() => {
                  // Group graphs by section
                  const groupedGraphs = graphSettings.reduce((acc, graph) => {
                    const section = graph.section || 'General';
                    if (!acc[section]) acc[section] = [];
                    acc[section].push(graph);
                    return acc;
                  }, {});

                  // Sort sections by sectionSequence, then sort graphs within each section by secKpiSequence
                  const sortedSections = Object.keys(groupedGraphs).sort((a, b) => {
                    const sectionA = graphSettings.find(g => g.section === a)?.sectionSequence || 0;
                    const sectionB = graphSettings.find(g => g.section === b)?.sectionSequence || 0;
                    return sectionA - sectionB;
                  });

                  return sortedSections.map((sectionName) => (
                    <Box key={sectionName} sx={{ mb: 3 }}>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
                        {t(sectionName)} {t("Section")}
                      </Typography>
                      <Droppable droppableId={sectionName} type="KPI_METRICS">
                        {(provided) => (
                          <List {...provided.droppableProps} ref={provided.innerRef}>
                            {groupedGraphs[sectionName]
                              .sort((a, b) => (a.secKpiSequence || 0) - (b.secKpiSequence || 0))
                              .map((graph, sectionIndex) => (
                              <Draggable key={graph.id} draggableId={graph.id} index={sectionIndex}>
                                {(provided, snapshot) => (
                                  <React.Fragment>
                                    {sectionIndex > 0 && <Divider />}
                                    <ListItem
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      sx={{
                                        backgroundColor: snapshot.isDragging ? 'rgba(0, 0, 0, 0.1)' :
                                          sectionIndex % 2 === 0 ? 'transparent' : 'rgba(0, 0, 0, 0.02)',
                                        borderRadius: 1,
                                        mb: 1,
                                        border: '1px solid',
                                        borderColor: 'divider'
                                      }}
                                    >
                                      <Grid container spacing={2} alignItems="center">
                                        <Grid item xs={1} sx={{ textAlign: "center" }}>
                                          <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                            {sectionIndex + 1}
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={1}>
                                          <IconButton size="small" sx={{ cursor: 'grab' }} {...provided.dragHandleProps}>
                                            <DragIndicatorIcon />
                                          </IconButton>
                                        </Grid>
                                        <Grid item xs={3}>
                                          <Typography
                                            variant="body1"
                                            sx={{
                                              fontWeight: graph.enabled ? "normal" : "light",
                                              color: graph.enabled ? "text.primary" : "text.disabled",
                                              p: 1,
                                            }}
                                          >
                                            {t(graph.name)}
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={2}>
                                          <FormControl fullWidth size="small">
                                            <Select
                                              value={graph.chartType}
                                              onChange={(e) => handleChartTypeChange(graph.id, e.target.value)}
                                              displayEmpty
                                              sx={{
                                                '& .MuiSelect-select': {
                                                  fontWeight: graph.enabled ? "normal" : "light",
                                                  color: graph.enabled ? "text.primary" : "text.disabled",
                                                }
                                              }}
                                            >
                                              {getChartTypeOptions(graph.id, decisionTableConfig).map((option) => (
                                                <MenuItem key={option.value} value={option.value}>
                                                  {option.label}
                                                </MenuItem>
                                              ))}
                                            </Select>
                                          </FormControl>
                                        </Grid>
                                        <Grid item xs={2} sx={{ textAlign: "center" }}>
                                          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                            Enabled
                                          </Typography>
                                          <Switch
                                            checked={graph.userEnabled}
                                            onChange={() => handleToggleGraph(graph.id)}
                                            disabled={!graph.enabled}
                                            size="small"
                                          />
                                        </Grid>
                                        <Grid item xs={3}>
                                          <FormControl fullWidth size="small">
                                            <Select
                                              value={graph.colorPallet || "default"}
                                              onChange={(e) => handleColorPaletteChange(graph.id, e.target.value)}
                                              renderValue={(selected) => selected || "Color Palette"}
                                              displayEmpty
                                            >
                                              {getColorPaletteOptions(graph.id, decisionTableConfig).map((option) => (
                                                <MenuItem key={option.value} value={option.value}>
                                                  {option.label}
                                                </MenuItem>
                                              ))}
                                            </Select>
                                          </FormControl>
                                        </Grid>
                                      </Grid>
                                    </ListItem>
                                  </React.Fragment>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </List>
                        )}
                      </Droppable>
            </Box>
                  ));
                })()
              ) : (
                // Overall Sequencing
                <Droppable droppableId="overall-metrics" type="KPI_METRICS">
                  {(provided) => (
                    <List {...provided.droppableProps} ref={provided.innerRef}>
                      {graphSettings
                        .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
                        .map((graph, index) => (
                        <Draggable key={graph.id} draggableId={graph.id} index={index}>
                          {(provided, snapshot) => (
                            <React.Fragment>
                              {index > 0 && <Divider />}
                              <ListItem
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                sx={{
                                  backgroundColor: snapshot.isDragging ? 'rgba(0, 0, 0, 0.1)' :
                                    index % 2 === 0 ? 'transparent' : 'rgba(0, 0, 0, 0.02)',
                                  borderRadius: 1,
                                  mb: 1,
                                  border: '1px solid',
                                  borderColor: 'divider'
                                }}
                              >
                                <Grid container spacing={2} alignItems="center">
                                  <Grid item xs={1} sx={{ textAlign: "center" }}>
                                    <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                      {index + 1}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={1}>
                                    <IconButton size="small" sx={{ cursor: 'grab' }} {...provided.dragHandleProps}>
                                      <DragIndicatorIcon />
                                    </IconButton>
                                  </Grid>
                                  <Grid item xs={3}>
                                    <Typography
                                      variant="body1"
                                      sx={{
                                        fontWeight: graph.enabled ? "normal" : "light",
                                        color: graph.enabled ? "text.primary" : "text.disabled",
                                        p: 1,
                                      }}
                                    >
                                      {t(graph.name)}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={2}>
                                    <FormControl fullWidth size="small">
                                      <Select
                                        value={graph.chartType}
                                        onChange={(e) => handleChartTypeChange(graph.id, e.target.value)}
                                        displayEmpty
                                        sx={{
                                          '& .MuiSelect-select': {
                                            fontWeight: graph.enabled ? "normal" : "light",
                                            color: graph.enabled ? "text.primary" : "text.disabled",
                                          }
                                        }}
                                      >
                                        {getChartTypeOptions(graph.id, decisionTableConfig).map((option) => (
                                          <MenuItem key={option.value} value={option.value}>
                                            {option.label}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    </FormControl>
                                  </Grid>
                                  <Grid item xs={2} sx={{ textAlign: "center" }}>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                      Enabled
                                    </Typography>
                                    <Switch
                                      checked={graph.userEnabled}
                                      onChange={() => handleToggleGraph(graph.id)}
                                      disabled={!graph.enabled}
                                      size="small"
                                    />
                                  </Grid>
                                  <Grid item xs={3}>
                                    <FormControl fullWidth size="small">
                                      <Select
                                        value={graph.colorPallet || "default"}
                                        onChange={(e) => handleColorPaletteChange(graph.id, e.target.value)}
                                        renderValue={(selected) => selected || "Color Palette"}
                                        displayEmpty
                                      >
                                        {getColorPaletteOptions(graph.id, decisionTableConfig).map((option) => (
                                          <MenuItem key={option.value} value={option.value}>
                                            {option.label}
                                          </MenuItem>
                                        ))}
                                      </Select>
                                    </FormControl>
                                  </Grid>
                                </Grid>
                              </ListItem>
                            </React.Fragment>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </List>
                  )}
                </Droppable>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {sequencingMode === 'section' ? (
                // Section-Based Sequencing for Reports
                (() => {
                  // Group reports by section
                  const groupedReports = reportSettings.reduce((acc, report) => {
                    const section = report.section || 'General';
                    if (!acc[section]) acc[section] = [];
                    acc[section].push(report);
                    return acc;
                  }, {});

                  // Sort sections by sectionSequence, then sort reports within each section by secKpiSequence
                  const sortedSections = Object.keys(groupedReports).sort((a, b) => {
                    const sectionA = reportSettings.find(r => r.section === a)?.sectionSequence || 0;
                    const sectionB = reportSettings.find(r => r.section === b)?.sectionSequence || 0;
                    return sectionA - sectionB;
                  });

                  return sortedSections.map((sectionName) => (
                    <Box key={sectionName} sx={{ mb: 3 }}>
                      <Typography variant="h6" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
                        {t(sectionName)} {t("Reports")}
                      </Typography>
                      <Droppable droppableId={sectionName} type="KPI_REPORTS">
                        {(provided) => (
                          <List {...provided.droppableProps} ref={provided.innerRef}>
                            {groupedReports[sectionName]
                              .sort((a, b) => (a.secKpiSequence || 0) - (b.secKpiSequence || 0))
                              .map((report, sectionIndex) => (
                              <Draggable key={report.id} draggableId={report.id} index={sectionIndex}>
                                {(provided, snapshot) => (
                                  <React.Fragment>
                                    {sectionIndex > 0 && <Divider />}
                                    <ListItem
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      sx={{
                                        backgroundColor: snapshot.isDragging ? 'rgba(0, 0, 0, 0.1)' :
                                          sectionIndex % 2 === 0 ? 'transparent' : 'rgba(0, 0, 0, 0.02)',
                                        borderRadius: 1,
                                        mb: 1,
                                        border: '1px solid',
                                        borderColor: 'divider'
                                      }}
                                    >
                                      <Grid container spacing={2} alignItems="center">
                                        <Grid item xs={1} sx={{ textAlign: "center" }}>
                                          <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                            {sectionIndex + 1}
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={1}>
                                          <IconButton size="small" sx={{ cursor: 'grab' }} {...provided.dragHandleProps}>
                                            <DragIndicatorIcon />
                                          </IconButton>
                                        </Grid>
                                        <Grid item xs={8}>
                                          <Typography
                                            variant="body1"
                                            sx={{
                                              fontWeight: report.enabled ? "normal" : "light",
                                              color: report.enabled ? "text.primary" : "text.disabled",
                                              p: 1,
                                            }}
                                          >
                                            {t(report.name)}
                                          </Typography>
                                        </Grid>
                                        <Grid item xs={2} sx={{ textAlign: "center" }}>
                                          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                            Enabled
                                          </Typography>
                                          <Switch
                                            checked={report.userEnabled}
                                            onChange={() => handleToggleReport(report.id)}
                                            disabled={!report.enabled}
                                            size="small"
                                          />
                                        </Grid>
                                      </Grid>
                                    </ListItem>
                                  </React.Fragment>
                                )}
                              </Draggable>
                            ))}
                            {provided.placeholder}
                          </List>
                        )}
                      </Droppable>
                    </Box>
                  ));
                })()
              ) : (
                // Overall Sequencing for Reports
                <Droppable droppableId="overall-reports" type="KPI_REPORTS">
                  {(provided) => (
                    <List {...provided.droppableProps} ref={provided.innerRef}>
                      {reportSettings
                        .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
                        .map((report, index) => (
                        <Draggable key={report.id} draggableId={report.id} index={index}>
                          {(provided, snapshot) => (
                            <React.Fragment>
                              {index > 0 && <Divider />}
                              <ListItem
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                sx={{
                                  backgroundColor: snapshot.isDragging ? 'rgba(0, 0, 0, 0.1)' :
                                    index % 2 === 0 ? 'transparent' : 'rgba(0, 0, 0, 0.02)',
                                  borderRadius: 1,
                                  mb: 1,
                                  border: '1px solid',
                                  borderColor: 'divider'
                                }}
                              >
                                <Grid container spacing={2} alignItems="center">
                                  <Grid item xs={1} sx={{ textAlign: "center" }}>
                                    <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                      {index + 1}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={1}>
                                    <IconButton size="small" sx={{ cursor: 'grab' }} {...provided.dragHandleProps}>
                                      <DragIndicatorIcon />
                                    </IconButton>
                                  </Grid>
                                  <Grid item xs={8}>
                                    <Typography
                                      variant="body1"
                                      sx={{
                                        fontWeight: report.enabled ? "normal" : "light",
                                        color: report.enabled ? "text.primary" : "text.disabled",
                                        p: 1,
                                      }}
                                    >
                                      {t(report.name)}
                                    </Typography>
                                  </Grid>
                                  <Grid item xs={2} sx={{ textAlign: "center" }}>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                                      Enabled
                                    </Typography>
                                    <Switch
                                      checked={report.userEnabled}
                                      onChange={() => handleToggleReport(report.id)}
                                      disabled={!report.enabled}
                                      size="small"
                                    />
                                  </Grid>
                                </Grid>
                              </ListItem>
                            </React.Fragment>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </List>
                  )}
                </Droppable>
              )}
            </TabPanel>
          </DragDropContext>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={savingChanges}>
          {t("Cancel")}
        </Button>
        <Button onClick={handleSaveChanges} variant="contained" color="primary" disabled={savingChanges || (Object.keys(changedKpis).length === 0 && Object.keys(changedReports).length === 0)}>
          {savingChanges ? t("Saving...") : t("Save Changes")}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ManageDashboardDialog;











