import { useCallback } from "react";
import { useDispatch,useSelector } from "react-redux";
import { setMatOdataViews } from "@app/tabsDetailsSlice";
import { doAjax } from "@components/Common/fetchService";
import useLogger from "@hooks/useLogger";
import {destination_MaterialMgmt } from "../../../destinationVariables";
import { API_CODE,VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";

export function useViewsFromoData() {
  const dispatch = useDispatch();
  const { customError } = useLogger();
  const allTabsData = useSelector((state) => state.tabsData.allTabsData);


  const getViews = useCallback((materialType) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE?.STATUS_200) {
        dispatch(
          setMatOdataViews({
            matType: materialType,
            views: data?.body || [],
          })
        );
      } else {
        dispatch(
          setMatOdataViews({
            matType: materialType,
            views: [],
          })
        );
      }
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.DATA.GET_VIEWS_FOR_MAT}=${materialType}`,
      "get",
      hSuccess,
      hError
    );
  }, [dispatch]);

    const checkForHiddenView = useCallback(
    (viewName) => {
      if (!allTabsData || !viewName) return false;
      const viewKey = Object.keys(allTabsData).find(
        (key) => key.toLowerCase() === viewName.toLowerCase()
      );
      if (!viewKey) return false; // view not found
      const allFields = Object.values(allTabsData[viewKey]).flat();
      const allHidden = allFields.every(
        (field) => field.visibility === VISIBILITY_TYPE.HIDDEN
      );

      return !allHidden;
    },
    [allTabsData]
  );

  return { getViews, checkForHiddenView };
}
