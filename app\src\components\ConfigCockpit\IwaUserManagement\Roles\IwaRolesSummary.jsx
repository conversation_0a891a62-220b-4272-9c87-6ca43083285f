import { RoleSummary } from '@cw/rolesummary';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {APP_END_POINTS} from "@constant/appEndPoints";

const IwaRolesSummary = () => {
  const navigate = useNavigate();
  
  const [, setIsRoleEditable] = useState(false);
  const isVisible = {
    isCreateRoleVisible: true,
    isCopyCreateWithReferenceVisible: true,
    isDeleteVisible: true,
    isInActiveAndActiveVisible: true,
    isExportVisible: true,
    isSimpleRoleVisible: true,
    isModuleFeatureRoleVisible: true,
  };
     const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };


  const onRoleSummaryActionClick = (action, data) => {
    switch (action) {
      case 'createRole':
        navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.CREATE_ROLE, {
          state: data
            ? {
                mapGroups: data?.mapGroups ?? [],
                mapRoleCollections: data?.mapRoleCollections ?? [],
              }
            : undefined,
        });
        break;

      case 'viewRole':
      case 'editRole':
        if (data?.roleId && data?.roleVersionNo && data?.roleSegment) {
          setIsRoleEditable(action === 'editRole');
          const path = APP_END_POINTS.IWA_USER_MANAGEMENT.VIEW_AND_EDIT_ROLE;
          navigate(path, {
            state: {
              status: data?.status,
              action,
              roleId: data?.roleId,
              roleVersionNo: data?.roleVersionNo,
              roleSegment: data?.roleSegment,
            },
          });
        }

        break;

      default:
        console.warn('Unhandled action type:', action);
    }
  };

  return <RoleSummary isVisible={isVisible} onRoleSummaryActionClick={onRoleSummaryActionClick} dateTimeConfig={dateTimeConfig} appId='IWA' />;
};

export default IwaRolesSummary;
