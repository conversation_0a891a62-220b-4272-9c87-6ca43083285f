// hooks/useRequestHeaderFieldsIO.js
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { setRequestHeaderDTIO } from "../slice/InternalOrderSlice";
import { MODULE_MAP } from "@constant/enum";

const useRequestHeaderFieldsIO = ({ requestId, reqBench }) => {
  const dispatch = useDispatch();

  // Get request type directly from Redux
  const requestType = useSelector((state) => state.internalOrder.IOpayloadData?.requestHeaderData?.RequestType);

  const { getDtCall, dtData } = useGenericDtCall();

  const fetchHeaderFields = () => {
    const conditions = [
      {
        "MDG_CONDITIONS.MDG_MAT_SCENARIO": requestType || "Create",
        "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": MODULE_MAP.IO,
      },
    ];

    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_FMD_REQUEST_HEADER_CONFIG",
      version: "v2",
      conditions,
    };
    getDtCall(payload);
  };

  useEffect(() => {
    if (dtData) {
      const config = dtData?.result?.[0]?.MDG_MAT_REQUEST_HEADER_CONFIG ?? [];
      const formattedData = config
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setRequestHeaderDTIO(requestHeaderObj));
    }
  }, [dtData]);

  useEffect(() => {
    fetchHeaderFields();
  }, [requestId, reqBench, requestType]);
};

export default useRequestHeaderFieldsIO;
