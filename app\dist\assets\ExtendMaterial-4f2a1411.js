import{r as u,n as A,s as C,b7 as E,j as a,aZ as V,c as h,d as g,b3 as j,aa as D,b8 as F,a9 as M,F as p,O}from"./index-226a1e75.js";function w(n,o){return Array.isArray(o)&&o.find(f=>f.code===n)||""}const T=({label:n,value:o,units:c,onSave:f,isEditMode:z,isExtendMode:k,options:R=[],type:r})=>{var m;const[t,d]=u.useState(o),[$,x]=u.useState(!1),i=A(e=>e.AllDropDown.dropDown),S=C(),b=w(t,i);console.log("dropdownData",t),console.log("value e",o),console.log("label",n),console.log("units",c),console.log("transformedValue",b);const v=A(e=>e.edit.payload);console.log("editField",v),console.log("fieldData",{label:n,value:t,units:c,type:r});let s=n.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join("");return u.useEffect(()=>{d(o)},[o]),u.useEffect(()=>{console.log("lkey",s),console.log("data",o),S(E({keyname:s.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").split(" ").join(""),data:o||""}))},[]),console.log("editedValue[key] ",i[s]),console.log("editedValue[key] ",t),a(O,{item:!0,children:a(V,{children:k?h(p,{children:[a(g,{variant:"body2",color:"#777",children:n}),r==="Drop Down"?a(j,{options:i[s]??[],value:t&&((m=i[s])==null?void 0:m.filter(e=>e.code===t))||"",onChange:(e,l)=>{console.log("newValue",l),d(l.code),x(!0),console.log("keys",s)},getOptionLabel:e=>{var l,y;return console.log("optionoptionoption",e),e===""?"":`${e&&((l=e[0])==null?void 0:l.code)} - ${e&&((y=e[0])==null?void 0:y.desc)}`},renderOption:(e,l)=>(console.log("option vakue",l),a("li",{...e,children:a(g,{style:{fontSize:12},children:`${l==null?void 0:l.code} - ${l==null?void 0:l.desc}`})})),renderInput:e=>a(D,{...e,variant:"outlined",size:"small",label:null})}):r==="Input"?a(D,{variant:"outlined",size:"small",value:t,onChange:(e,l)=>{d(l)}}):r==="Calendar"?a(F,{size:"small",placeholder:"Select Date Range",format:"dd MMM yyyy",placement:"auto",sx:{height:"2.32rem !important"}}):r==="Radio Button"?a(M,{sx:{borderRadius:"0 !important"},checked:t,onChange:(e,l)=>{d(l)}}):""]}):a(p,{children:h(p,{children:[a(g,{variant:"body2",color:"#777",children:n}),h(g,{variant:"body2",fontWeight:"bold",children:[t," ",c]})]})})})})};export{T as default};
