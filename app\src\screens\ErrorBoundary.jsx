import React, { Component } from "react";
import { Box, Button, Typography, Collapse } from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import { ERROR_MESSAGES } from "@constant/enum";
import { colors } from "@constant/colors";
import useLang from "@hooks/useLang";

const ErrorBoundaryWrapper = (props) => {
  const { t } = useLang();
  return <ErrorBoundary {...props} t={t} />;
};

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null, showDetails: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ errorInfo });
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  handleReload = () => {
    window.history.back();
    setTimeout(() => window.location.reload(), 200);
  };

  toggleDetails = () => {
    this.setState((prevState) => ({ showDetails: !prevState.showDetails }));
  };

  render() {
    const { t } = this.props;

    if (this.state.hasError) {
      return (
        <Box
          sx={{
            height: "100vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            backgroundColor: colors?.black?.lightGrey,
            color: colors?.black?.light,
            padding: "20px",
            position: "relative",
            overflow: "hidden",
            backgroundImage: `url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500"><circle cx="100" cy="100" r="80" fill="%23FFCDD2"/><circle cx="400" cy="350" r="60" fill="%23BBDEFB"/></svg>')`,
            backgroundRepeat: "no-repeat",
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          {/* Card container */}
          <Box
            sx={{
              backgroundColor: colors.primary.white,
              padding: "20px 30px",
              borderRadius: "12px",
              boxShadow: "0 4px 20px rgba(0,0,0,0.15)",
              maxWidth: "95%",
              width: "550px", // wider card
              minHeight: "160px", // shorter card
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              gap: 1,
            }}
          >
            <ErrorOutlineIcon
              sx={{ fontSize: 60, color: colors?.error?.red, marginBottom: "5px" }}
            />

            {/* One-line message */}
            <Typography
              variant="h5"
              sx={{
                fontWeight: "bold",
                marginBottom: "6px",
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {t(ERROR_MESSAGES.WENT_WRONG)}
            </Typography>

            <Typography
              variant="body2"
              sx={{
                maxWidth: "90%",
                color: colors?.primary?.deepDark,
                textAlign: "center",
              }}
            >
              {t(ERROR_MESSAGES.UNEXPECTED_ERROR)}
            </Typography>

            <Button
              variant="contained"
              color="primary"
              onClick={this.handleReload}
              sx={{ textTransform: "none", marginTop: "10px" }}
            >
              {t("Go Back & Reload")} 🔄
            </Button>

            {process.env.NODE_ENV === "development" && this.state.error && (
              <Button
                variant="outlined"
                color="error"
                onClick={this.toggleDetails}
                sx={{ textTransform: "none", marginTop: "6px" }}
              >
                {this.state.showDetails ? t("Hide Details") : t("Show Details")}
              </Button>
            )}
          </Box>

          {/* Collapse for details */}
          <Collapse in={this.state.showDetails}>
            <Box
              sx={{
                marginTop: "20px",
                padding: "15px",
                borderRadius: "8px",
                backgroundColor: colors?.error?.lightPink,
                color: colors?.error?.darkRed,
                textAlign: "left",
                maxWidth: "600px",
                wordWrap: "break-word",
                fontSize: "14px",
                overflowY: "auto",
                maxHeight: "300px",
                border: "1px solid #d32f2f",
                boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
              }}
            >
              <Typography variant="body2">
                <strong>{t("Error")}:</strong> {this.state.error?.toString()}
              </Typography>
              <Typography variant="body2">
                <strong>{t("Details")}:</strong> {this.state.errorInfo?.componentStack}
              </Typography>
            </Box>
          </Collapse>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundaryWrapper;
