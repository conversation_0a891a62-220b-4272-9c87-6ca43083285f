import{r as l,j as e,c as r,aj as Ve,d as B,B as ze,$ as g,a4 as R,a8 as s,a6 as Le,ak as Ke,bS as Fe,al as Ze,O as o,Bm as d,a1 as we,a2 as ke,a3 as He,am as Je,fs as Xe,an as he,ai as et,F as z,C as w,au as k,aT as H,a0 as tt,g as rt,P as nt,s as lt,b as st,cw as at,cs as F,k as it,A as ot,N as ct,aZ as dt,V as ut,W as ht,i as pt,aa as Ct,a7 as ft,ac as mt,af as gt,cq as de,cI as ue,T as Be,bK as St,qU as xt,Z as Pe,Q as Et,ah as At,ag as Tt,aG as _t,bE as It,Bn as Rt}from"./index-226a1e75.js";import{d as bt}from"./CloudDownload-23cede9e.js";import{L as P}from"./LargeDropdown-b7ffdbd5.js";import{S as $e}from"./SingleSelectDropdown-ee61a6b7.js";const yt=({open:$,onClose:pe,handleSubmit:Ce=()=>{},title:W=d.CREATE_REQUEST,submitButtonText:fe=d.INITIATE_REQUEST,isLoading:L=!1})=>{var De;const[q,N]=l.useState("material"),[me,E]=l.useState([]),[Z,J]=l.useState([]),[U,X]=l.useState([]),[Y,ee]=l.useState([]),[Ne,S]=l.useState(null),[p,b]=l.useState({createdOn:null,top:{code:"10",desc:""}}),[j,ge]=l.useState([]),[te,v]=l.useState([]),[re,O]=l.useState([]),[Se,_]=l.useState(!1),[I,y]=l.useState(null),[xe,Q]=l.useState([]),[Ee,Ae]=l.useState([]),[ne,Te]=l.useState([]),[_e,V]=l.useState([]),[D,le]=l.useState([]),[Ie,K]=l.useState([]),[Re,se]=l.useState([]),[be,ae]=l.useState([]),[ie,G]=l.useState(""),ye=[{code:"material",desc:"Material"},{code:"cost_center",desc:"Cost Center"},{code:"profit_center",desc:"Profit Center"},{code:"general_ledger",desc:"General Ledger"},{code:"hierarchy",desc:"Hierarchy"}],oe=a=>({material:[{desc:"Rule-1",code:"MAT_MATERIAL_FIELD_CONFIG"},{desc:"Rule-2",code:"MAT_MATERIAL_TABLE_FIELD_CONFIG"},{desc:"Rule-3",code:"MAT_MATERIAL_COLUMN_FIELD_CONFIG"},{desc:"Rule-4",code:"MAT_MATERIAL_SEARCHSCREEN_FIELD_CONFIG"},{desc:"Rule-5",code:"MAT_MATERIAL_PLANT_FIELD_CONFIG"}],cost_center:[{code:"1",desc:"CC_COST_CENTER_CONFIG"},{code:"2",desc:"CC_BUDGET_CONFIG"},{code:"3",desc:"CC_ALLOCATION_CONFIG"}],profit_center:[{code:"1",desc:"PC_PROFIT_CENTER_CONFIG"},{code:"2",desc:"PC_REVENUE_CONFIG"},{code:"3",desc:"PC_ANALYSIS_CONFIG"}],general_ledger:[{code:"1",desc:"GL_ACCOUNT_CONFIG"},{code:"2",desc:"GL_POSTING_CONFIG"},{code:"3",desc:"GL_BALANCE_CONFIG"}],hierarchy:[{code:"1",desc:"HIE_STRUCTURE_CONFIG"},{code:"2",desc:"HIE_LEVEL_CONFIG"},{code:"3",desc:"HIE_RELATIONSHIP_CONFIG"}]})[a]||[],t=()=>{var f,m,A,T;const a=[];return(!Y||Y.length===0)&&a.push((f=d)==null?void 0:f.BR_FAIL),(m=p==null?void 0:p.top)!=null&&m.code||a.push((A=d)==null?void 0:A.NO_FAIL),p!=null&&p.createdOn||a.push((T=d)==null?void 0:T.CO_FAIL),a},i={fontSize:"12px",fontWeight:500},n=({children:a,sx:f,required:m=!1})=>r(B,{sx:{...i,marginBottom:"4px",...f},children:[a,m&&e("span",{style:{color:"red",marginLeft:"2px"},children:"*"})]}),C=()=>{var m,A;_(!0);const a=T=>{O(T.body),_(!1)},f=T=>{_(!1),console.log(T)};w(`/${k}${(A=(m=H)==null?void 0:m.DATA_CLEANSE_APIS)==null?void 0:A.GET_MATERIAL_GRP}`,"get",a,f)},c=(a="")=>{var T,Ge;_(!0);let f={materialNo:a,salesOrg:"",top:200,skip:0};const m=Me=>{_(!1),ge(Me.body)},A=Me=>{console.log(Me)};w(`/${k}${(Ge=(T=H)==null?void 0:T.DATA_CLEANSE_APIS)==null?void 0:Ge.SEARCH_MAT_NO}`,"post",m,A,f)},h=()=>{var m,A;_(!0);const a=T=>{_(!1),v(T.body)},f=T=>{_(!1)};w(`/${k}${(A=(m=H)==null?void 0:m.DATA_CLEANSE_APIS)==null?void 0:A.GET_MAT_TYPE}`,"get",a,f)},u=()=>{Q([{code:"1",desc:"Cost Center 1"},{code:"2",desc:"Cost Center 2"},{code:"3",desc:"Cost Center 3"}])},x=()=>{Ae([{code:"1",desc:"Profit Center 1"},{code:"2",desc:"Profit Center 2"},{code:"3",desc:"Profit Center 3"}])},M=()=>{Te([{code:"1",desc:"GL Account 1"},{code:"2",desc:"GL Account 2"},{code:"3",desc:"GL Account 3"}])},ce=()=>{V([{code:"1",desc:"Hierarchy Level 1"},{code:"2",desc:"Hierarchy Level 2"},{code:"3",desc:"Hierarchy Level 3"}])},We=a=>{const f=a.target.value;if(I&&clearTimeout(I),f.length>=4){const m=setTimeout(()=>{c(f)},500);y(m)}},qe=a=>{b({...p,createdOn:a})},Ue=(a,f)=>{if(f!==null){const m=f;let A={...p,[a]:m};b(A)}},Ye=a=>{switch(N(a),ve(),a){case"material":C(),h();break;case"cost_center":u();break;case"profit_center":x();break;case"general_ledger":M();break;case"hierarchy":ce();break}},ve=()=>{E([]),J([]),X([]),ee([]),S(null),le([]),K([]),se([]),ae([]),b({createdOn:null,top:{code:"10",desc:""}}),G("")},Oe=()=>{ve(),pe(),N("material"),G("")},je=[{code:"10",desc:""},{code:"20",desc:""},{code:"50",desc:""},{code:"100",desc:""},{code:"200",desc:""},{code:"500",desc:""},{code:"1000",desc:""}],Qe=()=>{switch(q){case"material":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(n,{children:d.MATERIAL}),e(g,{size:"small",fullWidth:!0,children:e(tt,{matGroup:j,selectedMaterialGroup:me,setSelectedMaterialGroup:E,isDropDownLoading:Se,placeholder:d.SELECT_MATERIAL,onInputChange:We,minCharacters:4})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:d.MATERIAL_TYPE}),e(P,{matGroup:te,selectedMaterialGroup:Z,setSelectedMaterialGroup:J,placeholder:d.SELECT_MATERIAL_TYPE})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:d.MATERIAL_GROUP}),e(P,{matGroup:re,selectedMaterialGroup:U,setSelectedMaterialGroup:X,placeholder:d.SELECT_MATERIAL_GROUP})]})]});case"cost_center":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Cost Center"}),e(P,{matGroup:xe,selectedMaterialGroup:D,setSelectedMaterialGroup:le,placeholder:d.SELECT_COST_CENTER})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:d.CATEGORY}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Category"}),e(s,{value:"production",children:"Production"}),e(s,{value:"administration",children:"Administration"}),e(s,{value:"sales",children:"Sales & Marketing"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Budget Range"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Budget Range"}),e(s,{value:"0-10000",children:"0 - 10,000"}),e(s,{value:"10000-50000",children:"10,000 - 50,000"}),e(s,{value:"50000+",children:"50,000+"})]})})]})]});case"profit_center":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Profit Center"}),e(P,{matGroup:Ee,selectedMaterialGroup:Ie,setSelectedMaterialGroup:K,placeholder:"Select Profit Center"})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Revenue Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Revenue Type"}),e(s,{value:"primary",children:"Primary Revenue"}),e(s,{value:"secondary",children:"Secondary Revenue"}),e(s,{value:"other",children:"Other Revenue"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Profit Margin Range"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Margin Range"}),e(s,{value:"0-10",children:"0% - 10%"}),e(s,{value:"10-25",children:"10% - 25%"}),e(s,{value:"25+",children:"25%+"})]})})]})]});case"general_ledger":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"GL Account"}),e(P,{matGroup:ne,selectedMaterialGroup:Re,setSelectedMaterialGroup:se,placeholder:"Select GL Account"})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Account Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Account Type"}),e(s,{value:"asset",children:"Asset"}),e(s,{value:"liability",children:"Liability"}),e(s,{value:"equity",children:"Equity"}),e(s,{value:"revenue",children:"Revenue"}),e(s,{value:"expense",children:"Expense"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Balance Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Balance Type"}),e(s,{value:"debit",children:"Debit Balance"}),e(s,{value:"credit",children:"Credit Balance"}),e(s,{value:"zero",children:"Zero Balance"})]})})]})]});case"hierarchy":return r(z,{children:[r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Hierarchy Level"}),e(P,{matGroup:_e,selectedMaterialGroup:be,setSelectedMaterialGroup:ae,placeholder:"Select Hierarchy Level"})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Parent Node"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Parent Node"}),e(s,{value:"root",children:"Root"}),e(s,{value:"branch1",children:"Branch 1"}),e(s,{value:"branch2",children:"Branch 2"})]})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{children:"Node Type"}),e(g,{size:"small",fullWidth:!0,children:r(R,{value:"",onChange:()=>{},displayEmpty:!0,sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:[e(s,{value:"",disabled:!0,children:"Select Node Type"}),e(s,{value:"parent",children:"Parent Node"}),e(s,{value:"child",children:"Child Node"}),e(s,{value:"leaf",children:"Leaf Node"})]})})]})]});default:return null}};return l.useEffect(()=>{$&&(C(),h())},[$]),l.useEffect(()=>()=>{I&&clearTimeout(I)},[I]),e(z,{children:r(et,{open:$,onClose:Oe,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,minHeight:"400px",overflow:"visible"}},sx:{"& .MuiDialog-container":{overflow:"visible"},"& .MuiDialog-paper":{overflow:"visible"}},children:[r(Ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",pb:1},children:[e(B,{variant:"h6",component:"div",children:W}),r(ze,{sx:{display:"flex",alignItems:"center",gap:2},children:[e(g,{size:"small",sx:{minWidth:150},children:e(R,{value:q,onChange:a=>Ye(a.target.value),sx:{fontSize:"12px"},MenuProps:{disablePortal:!1,PaperProps:{style:{zIndex:1302,maxHeight:200}}},children:ye.map(a=>e(s,{value:a.code,children:a.desc},a.code))})}),e(Le,{edge:"end",color:"inherit",onClick:Oe,"aria-label":"close",children:e(Ke,{})})]})]}),e(Fe,{}),e(Ze,{sx:{pt:3,overflow:"visible"},children:r(o,{container:!0,spacing:3,children:[Qe(),r(o,{item:!0,xs:12,md:6,children:[e(n,{required:!0,children:d.BUSINESS_RULE}),e(P,{matGroup:oe(q),selectedMaterialGroup:Y,setSelectedMaterialGroup:ee,placeholder:d.SELECT})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{required:!0,children:d.NO_OBJECTS}),e(g,{size:"small",fullWidth:!0,children:e($e,{options:je,value:(De=p==null?void 0:p.top)==null?void 0:De.code,onChange:a=>Ue("top",a),placeholder:"Select Number of Objects",disabled:!1,minWidth:"90%",listWidth:210})})]}),r(o,{item:!0,xs:12,md:6,children:[e(n,{required:!0,children:"Created On"}),e(g,{fullWidth:!0,sx:{padding:0},children:e(we,{dateAdapter:ke,children:e(He,{handleDate:qe,date:p==null?void 0:p.createdOn})})})]})]})}),e(Fe,{}),r(Je,{sx:{p:2,gap:1,flexDirection:"column"},children:[ie&&e(Xe,{severity:"error",sx:{width:"100%",mb:1},children:ie}),r(ze,{sx:{display:"flex",gap:1,width:"100%",justifyContent:"flex-end"},children:[e(he,{onClick:ve,variant:"outlined",color:"warning",disabled:L,children:"Clear"}),e(he,{onClick:()=>{const a=t();if(a.length>0){G(a.join(", "));return}G(""),Ce(p)},variant:"contained",color:"primary",disabled:L,sx:{minWidth:"120px"},children:L?"Creating...":fe})]})]})]})})},Nt=()=>{var oe;const[$,pe]=l.useState(null),[Ce,W]=l.useState(!1),[fe,L]=l.useState(""),[q,N]=l.useState(""),me=rt(),[E,Z]=l.useState({createdOn:null}),[J,U]=l.useState(!1),[X,Y]=l.useState(!1),[ee,Ne]=l.useState(""),[S,p]=l.useState([]),[b,j]=l.useState([]),[ge,te]=l.useState(0),[v,re]=l.useState(0),[O,Se]=l.useState((oe=nt)==null?void 0:oe.TOP_SKIP),_=lt(),I=st(),{showSnackbar:y}=at(),xe=()=>{setSelectedCreatedBy([]),setSelectedMaterial([]),setSelectedDivision([]),setSelectedReqType([]),setSelectedOptions([]);let t={...rbSearchForm,reqPriority:""};_(commonFilterUpdate({module:"RequestBench",filterData:t})),_(commonFilterClear({module:"RequestBench",days:7})),setClearClicked(!0)},Q={fontSize:"12px",fontWeight:500},Ee=F(it)(({theme:t})=>({marginTop:"0px !important",border:`1px solid ${t.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),Ae=F(ot)(({theme:t})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:t.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${t.palette.primary.light}20`}})),ne=F(he)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),Te=F(o)({padding:"0.75rem",gap:"0.5rem"}),_e=F(o)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),V=F(B)(({theme:t})=>({fontSize:"0.75rem",color:t.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}));l.useEffect(()=>{D()},[]),l.useEffect(()=>j([...S]),[S]);const D=(t="")=>{var c,h;const i={page:t==="pagination"?v:0,size:t==="pagination"?O:100,orderBy:"creationDate"};N(!0);const n=u=>{p((u==null?void 0:u.body)||[]),N(!1)},C=u=>{p([]),N(!1)};w(`/${k}${(h=(c=H)==null?void 0:c.DATA_CLEANSE_APIS)==null?void 0:h.CLEANSING_REQ}`,"post",n,C,i)},le=t=>{var c,h;W(!0);let i={requestId:t};const n=u=>{var ce;const x=URL.createObjectURL(u),M=document.createElement("a");M.href=x,M.setAttribute("download",`${t}_Data Cleanse.pdf`),document.body.appendChild(M),M.click(),document.body.removeChild(M),URL.revokeObjectURL(x),W(!1),L(""),y(`${t}${(ce=d)==null?void 0:ce.EXPORT_SUCCESS}`,"success")},C=()=>{W(!1),L(""),y(`Failed exporting ${t}_Data Cleanse.pdf`,"error")};w(`/${k}${(h=(c=H)==null?void 0:c.DATA_CLEANSE_APIS)==null?void 0:h.DOWNLOAD_PDF}`,"postandgetblob",n,C,i)},Ie=[{code:"Material",desc:""},{code:"Cost Center",desc:""},{code:"Profit Center",desc:""},{code:"General Ledger",desc:""},{code:"Hierarchy",desc:""}],K=t=>{var c,h,u;let i={RequestId:"",ObjectCount:0,UserId:"<EMAIL>",InitiatedOn:de(new Date).format("YYYY-MM-DDThh:mm:ss")??"",Status:"In-Progress",Top:((c=t==null?void 0:t.top)==null?void 0:c.code)||"10",Skip:"0",FromDate:de(t==null?void 0:t.createdOn[0]).format("YYYY-MM-DDThh:mm:ss")??"",ToDate:de(t==null?void 0:t.createdOn[1]).format("YYYY-MM-DDThh:mm:ss")??"",DtName:["MDG_MAT_MATERIAL_FIELD_CONFIG"],DtVersion:"v4",DtRegion:"US",DtScenario:"Create",DtMaterialType:"FERT"};const n=x=>{y(x==null?void 0:x.message,"success"),D(),U(!1)},C=x=>{y(x.message||d.ERROR,"error")};w(`/${k}${(u=(h=H)==null?void 0:h.DATA_CLEANSE_APIS)==null?void 0:u.INITIATE_DATA_QUALITY_CHECK}`,"post",n,C,[i])},Re=(t,i)=>({field:t,headerName:i,editable:!1,flex:1.4,renderCell:n=>e(It,{sx:{justifyContent:"flex-start",borderRadius:"4px",color:"#000",width:"100%",minWidth:"4.6rem",fontSize:"12px",background:Pe.statusColorMap[n.row.status.toLowerCase().replace(/[^a-z0-9]/gi,"")]||Pe.statusColorMap.default},label:n.row.status})}),se=t=>{Z({...E,createdOn:t})},be=t=>{if(t.target.value!==null){const i=t.target.name,n=t.target.value;let C={...E,[i]:n};Z(C)}},ae=t=>{if(!t){j([...S]),te(S==null?void 0:S.length);return}const i=S==null?void 0:S.filter(n=>{var h;let C=!1,c=Object.keys(n);for(let u=0;u<c.length&&(C=n[c[u]]?(n==null?void 0:n[c==null?void 0:c[u]])&&((h=n==null?void 0:n[c==null?void 0:c[u]].toString().toLowerCase())==null?void 0:h.indexOf(t==null?void 0:t.toLowerCase()))!=-1:!1,!C);u++);return C});j([...i]),te(i==null?void 0:i.length)},ie=t=>{const i=t.target.value;Se(i),re(0)},G=(t,i)=>{re(isNaN(i)?0:i)};l.useEffect(()=>{v!==0&&O*(v+1)>S.length&&S.length%O===0&&D("pagination")},[v]);const ye=[{field:"requestId",headerName:"Request ID",flex:1.5},{field:"module",headerName:"Module",flex:1,renderCell:t=>t.row.module||"Material"},{field:"userId",headerName:"Initiated By",flex:1},{field:"creationDate",headerName:"Initiated On",flex:1,renderCell:t=>{var i;return de((i=t==null?void 0:t.row)==null?void 0:i.initiatedOn).format("YYYY-MM-DD hh:mm")??""}},{field:"objectCount",headerName:"Object Count",flex:1},Re(Rt,"Status"),{field:"actions",headerName:"Actions",flex:1,headerAlign:"center",align:"center",renderCell:t=>{var i,n,C;return r("div",{children:[e(Be,{title:"Report",children:e(Le,{disabled:((i=t==null?void 0:t.row)==null?void 0:i.status)===`${ue.DRAFT}`,onClick:()=>{var c,h,u;if((c=t==null?void 0:t.row)!=null&&c.objectCount){le((h=t==null?void 0:t.row)==null?void 0:h.requestId);return}y((u=d)==null?void 0:u.FAILED_FETCHING_DATA,"error")},children:e(bt,{sx:{color:((n=t==null?void 0:t.row)==null?void 0:n.status)===`${ue.DRAFT}`?"#808080":"#ffd93f"}})})}),e(Be,{title:"View Requests",children:e(Le,{disabled:((C=t==null?void 0:t.row)==null?void 0:C.status)===`${ue.DRAFT}`,onClick:()=>{var c,h,u,x;if((c=t==null?void 0:t.row)!=null&&c.objectCount){me(`${(h=St)==null?void 0:h.DATA_CHECK}?requestId=${(u=t==null?void 0:t.row)==null?void 0:u.requestId}`,{state:t==null?void 0:t.row});return}y((x=d)==null?void 0:x.FAILED_FETCHING_DATA,"error")},children:e(xt,{sx:{fontSize:"20px",color:t.row.status===ue.DRAFT?"#808080":`${Pe.blue.indigo}`}})})})]})}}];return r("div",{style:{...ct,backgroundColor:"#FAFCFF"},children:[r(dt,{spacing:1,children:[e(o,{container:!0,mt:0,sx:Et,children:r(o,{item:!0,md:4,children:[e(B,{variant:"h3",children:e("strong",{children:d.DATA_CLEANSE})}),e(B,{variant:"body2",color:"#777",children:"This view displays the list of Data Cleanse Requests"})]})}),e(o,{container:!0,sx:{paddingBottom:"10px"},children:e(o,{item:!0,md:12,children:r(Ee,{expanded:X,onChange:(t,i)=>Y(i),sx:{mb:2},children:[r(Ae,{expandIcon:e(ut,{sx:{fontSize:"1.25rem",color:I.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterDC",children:[e(ht,{sx:{fontSize:"1.25rem",marginRight:1,color:I.palette.primary.dark}}),e(B,{sx:{fontSize:"0.875rem",fontWeight:600,color:I.palette.primary.dark},children:d.FILTER})]}),r(pt,{sx:{padding:0},children:[e(Te,{children:r(o,{container:!0,rowSpacing:1,spacing:2,alignItems:"center",sx:{padding:"0rem 1rem 0.5rem"},children:[r(o,{item:!0,md:2,children:[e(V,{sx:Q,children:"Module"}),e(g,{size:"small",fullWidth:!0,children:e($e,{options:Ie,value:ee,onChange:t=>{Ne(t)},placeholder:"Select Module",disabled:!1,minWidth:"90%",listWidth:210})})]}),r(o,{item:!0,md:2,children:[e(V,{sx:Q,children:"Request ID"}),e(Ct,{name:"requestId",fullWidth:!0,variant:"outlined",placeholder:"Request ID",size:"small",InputLabelProps:{shrink:!0},onChange:be,value:E==null?void 0:E.requestId})]}),r(o,{item:!0,md:2,children:[e(V,{sx:Q,children:"Created On"}),e(g,{fullWidth:!0,sx:{padding:0},children:e(we,{dateAdapter:ke,children:e(He,{handleDate:se,date:E==null?void 0:E.createdOn})})})]})]})}),r(_e,{children:[e(ne,{variant:"outlined",size:"small",startIcon:e(ft,{sx:{fontSize:"1rem"}}),onClick:xe,children:"Clear"}),e(ne,{variant:"contained",size:"small",startIcon:e(mt,{sx:{fontSize:"1rem"}}),onClick:()=>K(E),children:"Search"})]})]})]})})}),e(gt,{isLoading:q,module:"DataCleanse",width:"100%",title:"Data Cleanse Requests",rows:b||[],columns:ye,onSearch:t=>ae(t),onRefresh:D,page:v,showSearch:!0,showRefresh:!0,pageSize:O,rowCount:ge??(b==null?void 0:b.length)??0,onPageChange:G,onPageSizeChange:ie,getRowIdValue:"id",hideFooter:!0,showCustomNavigation:!0})]}),e(Tt,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:e(At,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:$,onChange:t=>{pe(t)},children:e(he,{size:"small",variant:"contained",className:"requestbutton",onClick:()=>{U(!0)},children:d.CLEANSE_REQUEST})})}),e(yt,{open:J,onClose:()=>U(!1),handleSubmit:K,title:d.NEW_REQUEST,submitButtonText:d.INITIATE_REQUEST,isLoading:!1}),e(_t,{blurLoading:Ce,loaderMessage:fe})]})};export{Nt as default};
