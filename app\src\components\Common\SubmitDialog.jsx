import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Stack,
  Box,
  IconButton,
  Button,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { useNavigate } from "react-router-dom";
import { colors } from "@constant/colors";

const SuccessDialog = ({
  open,
  onClose,
  title,
  message,
  subText,
  buttonText,
  redirectTo = "/",
  icon = <CheckCircleOutlineIcon  sx={{ fontSize: 40,color:colors.icon.green }} />,
  headerColor = "#eae9ff", 
}) => {
  const navigate = useNavigate();

  const handleClose = (event, reason) => {
    if (reason !== "backdropClick") {
      onClose?.();
    }
  };

  const handleConfirm = () => {
    onClose?.();
    if (redirectTo) {
      navigate(redirectTo);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          boxShadow: 6,
        },
      }}
    >
      <DialogTitle
        sx={{
          backgroundColor: headerColor,
          color: "#fff",
          m: 0,
          p: 2,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
        }}
      >
        <Typography variant="h5" fontWeight={600}>
          {title}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Stack direction="row" spacing={2} alignItems="center" mt={1}>
          <Box sx={{ color: headerColor }}>{icon}</Box>
          <Box>
            <Typography variant="subtitle1" fontWeight={800}>
              {message}
            </Typography>
            {subText && (
              <Typography variant="body2" color="text.secondary">
                {subText}
              </Typography>
            )}
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button
          onClick={handleConfirm}
          variant="contained"
          color="success"
          sx={{ minWidth: 160 }}
        >
          {buttonText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SuccessDialog;
