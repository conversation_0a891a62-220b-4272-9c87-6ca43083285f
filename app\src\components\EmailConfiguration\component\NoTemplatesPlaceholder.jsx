import { Box, Typography } from '@mui/material';
import DescriptionIcon from '@mui/icons-material/Description';
export default function NoTemplatesPlaceholder({ message = "No templates available" }) {
  return (
    <Box
      height="100%"
      display="flex"
      alignItems="center"
      justifyContent="center"
      textAlign="center"
    >
      <Box>
        <DescriptionIcon sx={{ fontSize: 64, color: '#e0e0e0', mb: 2 }} />
        <Typography variant="h6" color="text.primary">
          {message}
        </Typography>
        <Typography color="text.secondary">
          Please create a new template
        </Typography>
      </Box>
    </Box>
  );
}