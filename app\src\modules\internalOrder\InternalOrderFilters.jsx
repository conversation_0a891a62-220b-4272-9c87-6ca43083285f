import React, { useState, useEffect } from "react";
import {
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Checkbox,
  IconButton,
  InputAdornment,
  Select,
  FormControl,
  MenuItem,
  Button,
  useTheme,
} from "@mui/material";
import { useDispatch } from "react-redux";
import styled from "@emotion/styled";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import ReusablePreset from "@components/Common/ReusablePresetFilter";
import {
  button_Marginleft,
  button_Primary,
  container_filter,
  font_Small,
} from "@components/Common/commonStyles";
import { commonFilterClear, commonFilterUpdate } from "@app/commonFilterSlice";
import useLang from "@hooks/useLang";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES, MODULE, MODULE_MAP, SEARCH_FIELD_TYPES } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { destination_InternalOrder } from "../../../src/destinationVariables";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";
import { useSelector } from "react-redux";
import useLogger from "@hooks/useLogger";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor:theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: `${theme.palette.primary.light}20`,
  },
}));

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',
});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));

const InternalOrderFilters = ({
  searchParameters,
  onSearch,
  onClear,
  filterFieldData,
  setFilterFieldData,
  items
}) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const { customError } = useLogger();
  const { t } = useLang();
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [options, setOptions] = useState([]);
  const [selected, setSelected] = useState({});
  const [loading, setLoading] = useState({});
  const [matInputValue, setMatInputValue] = useState("");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [timerId, setTimerId] = useState(null);
  const ioSearch = useSelector((state) => state.commonFilter["internalOrder"]);
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: MODULE?.IO }));
    setFilterFieldData((prevState) => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach((key) => {
        updatedState[key] = { code: "", desc: "" };
      });
      return updatedState;
    });
    setSelectedOptions([]);
    onClear();
  };
  const handleChange = (key, value) => {
    setSelected(prev => ({
      ...prev,
      [key]: value,
    }));
    let tempFilterData = {
      ...ioSearch,
      [key]: value?.map(item => item?.code).join('$^$'),
    };
    dispatch(
            commonFilterUpdate({
              module:  MODULE?.IO ,
              filterData: tempFilterData,
            })
          );
  };

  const getMaterialSearch = (fieldKey, endpoint, parameter, inputValue) => {
    setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: true }));

    const hSuccess = (data) => {
      setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: false }));

      setOptions((prev) => ({
        ...prev,
        [fieldKey]: data?.body,
      }));

      setMaterialOptions(data?.body?.list);
    };

    const hError = (error) => {
      setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: false }));
    };
    doAjax(
      `/${destination_InternalOrder}/api/v1/lookup/${endpoint}?${parameter}=${inputValue}`,
      "get",
      hSuccess,
      hError,
    );
  }
  const handleMatInputChange = (e , fieldKey) => {
    const inputValue = e.target.value;
    setMatInputValue(inputValue);
    const config = SearchMapOption.find((item) => item.key === fieldKey);
    const { endpoint, parameter = "search" } = config;
    if (timerId) clearTimeout(timerId);

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(fieldKey, endpoint, parameter, inputValue);
      }, 200);
      setTimerId(newTimerId);
    }
  };

  const OptionData = (endpoint, jsonName) => {
    const hSuccess = (data) => {
      setOptions((prev) => ({ ...prev, [jsonName]: data?.body }));
    };

    const hError = () => {
      customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
    };

    doAjax(`/${destination_InternalOrder}/api/v1/lookup/${endpoint}`,
      "get",
      hSuccess,
      hError);
  };
  const renderFilterField = (item, index) => {
    const fieldKey = item?.MDG_MAT_JSON_FIELD_NAME;
    const fieldLabel = t(item?.MDG_MAT_UI_FIELD_NAME);

    const fieldProps = {
      matGroup: options[fieldKey],
      selectedMaterialGroup: selected[fieldKey] || [],
      setSelectedMaterialGroup: (val) => handleChange(fieldKey, val),
      placeholder: `SELECT ${fieldLabel}`,
      isDropDownLoading: loading[fieldKey],
      minWidth: "90%",
      onInputChange:(e) => handleMatInputChange(e, fieldKey)
    };

    const isMaterialDropdown =
      fieldKey === SEARCH_FIELD_TYPES.ORDER ||
      fieldKey === SEARCH_FIELD_TYPES.ORDERNAME;

    return (
      <Grid item md={2} key={index}>
        <LabelTypography sx={font_Small}>
          {fieldLabel}
          <span style={{ color: colors?.error?.dark }}>*</span>
        </LabelTypography>
        <FormControl size="small" fullWidth>
          {isMaterialDropdown ? (
            <MaterialDropdown {...fieldProps} />
          ) : (
            <LargeDropdown {...fieldProps} />
          )}
        </FormControl>
      </Grid>
    );
  };

  const SearchMapOption = [
    { key: SEARCH_FIELD_TYPES?.CONTROAREA, endpoint: "controlling-areas" },
    { key: SEARCH_FIELD_TYPES?.ORDERTYPE, endpoint: "order-types" },
    { key: SEARCH_FIELD_TYPES?.COMPANYCODEIO, endpoint: "company-codes" },
    { key: SEARCH_FIELD_TYPES?.CCIO, endpoint: "cost-centers" },
    { key: SEARCH_FIELD_TYPES?.CURRENCYIO, endpoint: "currencies" },
    { key: SEARCH_FIELD_TYPES?.PLANTIO, endpoint: "plants" },
    { key: SEARCH_FIELD_TYPES?.FUNAREA, endpoint: "functional-areas" },
    { key: SEARCH_FIELD_TYPES?.PROFITCEN, endpoint: "profit-centers" },
    { key: SEARCH_FIELD_TYPES?.GLACCIO, endpoint: "gl-accounts" },
    { key: SEARCH_FIELD_TYPES?.WBSELE, endpoint: "wbs-elements" },
    { key: SEARCH_FIELD_TYPES?.RESCC, endpoint: "responsible-cost-centers" },
    { key: SEARCH_FIELD_TYPES?.REQCC, endpoint: "requesting-cost-centers" },
    { key: SEARCH_FIELD_TYPES?.OBJCLASS, endpoint: "object-classes" },
    { key: SEARCH_FIELD_TYPES?.ORDER, endpoint: "getInternalOrder", parameter: "internalOrder" },
    { key: SEARCH_FIELD_TYPES?.ORDERNAME, endpoint: "description", parameter: "description" },
  ];
  useEffect(() => {
    SearchMapOption.forEach(({ key, endpoint }) => {
      OptionData(endpoint, key);
    });
  }, []);



  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color:theme.palette.primary.dark }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            className="filterIO"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark}} />
            <Typography
              sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: theme.palette.primary.dark,
              }}
            >
              {t("Filter Internal Order")}
            </Typography>
          </StyledAccordionSummary>

          <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
            <FilterContainer container>
              <Grid
                container
                rowSpacing={1}
                spacing={2}
                alignItems="center"
                sx={{ padding: "0rem 1rem 0.5rem" }}
              >
                {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                  .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                  .map((item, index) => {
                    const fieldType = item?.MDG_MAT_JSON_FIELD_NAME;

                    if ([
                      SEARCH_FIELD_TYPES.OBJCLASS,
                      SEARCH_FIELD_TYPES.REQCC,
                      SEARCH_FIELD_TYPES.RESCC,
                      SEARCH_FIELD_TYPES.WBSELE,
                      SEARCH_FIELD_TYPES.GLACCIO,
                      SEARCH_FIELD_TYPES.PROFITCEN,
                      SEARCH_FIELD_TYPES.FUNAREA,
                      SEARCH_FIELD_TYPES.PLANTIO,
                      SEARCH_FIELD_TYPES.CURRENCYIO,
                      SEARCH_FIELD_TYPES.CCIO,
                      SEARCH_FIELD_TYPES.COMPANYCODEIO,
                      SEARCH_FIELD_TYPES.ORDERNAME,
                      SEARCH_FIELD_TYPES.ORDER,
                      SEARCH_FIELD_TYPES.ORDERTYPE,
                      SEARCH_FIELD_TYPES.CONTROAREA
                    ].includes(fieldType)) {
                      return renderFilterField(item, index);
                    }
                    return null;
                  })
                }

                <Grid item md={2}>
                  <LabelTypography sx={font_Small}>{t("Add New Filters")}</LabelTypography>
                  <FormControl sx={{ width: "100%" }}>
                    <Select
                      sx={{
                        font_Small,
                        fontSize: "12px",
                        width: "100%",
                      }}
                      size="small"
                      multiple
                      limitTags={2}
                      value={selectedOptions}
                      onChange={(e) => setSelectedOptions(e.target.value)}
                      renderValue={(selected) => selected.join(", ")}
                      MenuProps={MenuProps}
                      endAdornment={
                        selectedOptions.length > 0 && (
                          <InputAdornment position="end" sx={{ marginRight: '10px' }}>
                            <IconButton
                              size="small"
                              onClick={() => setSelectedOptions([])}
                              aria-label="Clear selections"
                            >
                              <ClearIcon />
                            </IconButton>
                          </InputAdornment>
                        )
                      }
                    >
                      {items?.map((option) => (
                        <MenuItem key={option.title} value={option.title}>
                          <Checkbox
                            checked={selectedOptions.indexOf(option.title) > -1}
                          />
                          {option.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <Grid
                container
                sx={{
                  flexDirection: "row",
                  padding: "0rem 1rem 0.5rem",
                }}
                gap={1}
              >
                {selectedOptions.map((option, i) => (
                  <Grid item md={2} key={i}>
                    <LabelTypography sx={{ fontSize: "12px" }}>
                      {t(option)}
                    </LabelTypography>
                  </Grid>
                ))}
              </Grid>
            </FilterContainer>

            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleClear}
              >
                {t("Clear")}
              </ActionButton>

              <Grid sx={{ ...button_Marginleft }}>
                <ReusablePreset
                  moduleName={"InternalOrder"}
                  handleSearch={onSearch}
                />
              </Grid>

              <ActionButton
                variant="contained"
                size="small"
                startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                sx={{ ...button_Primary, ...button_Marginleft }}
                onClick={onSearch}
              >
                {t("Search")}
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default InternalOrderFilters;