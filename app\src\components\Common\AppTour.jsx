import { useEffect, useState } from "react";
import Joyride from "react-joyride";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { setGuideTour } from "../../app/applicationConfigReducer";
import { getPageKey } from "@helper/helper";
import { STEPS_BY_PAGE } from "@constant/enum";

const AppTour = ({ dynamicTheme }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const tourSelected = useSelector((state) => state.applicationConfig.guidetour);

  const [run, setRun] = useState(false);
  const [pageKey, setPageKey] = useState("app");

  useEffect(() => {
    let key = getPageKey(location.pathname);
    setPageKey(key);

    const hasSeenTourGlobal = localStorage.getItem("hasSeenTour_Global");
    const hasSeenTour = localStorage.getItem(`hasSeenTour_${key}`);

    if (!hasSeenTourGlobal && !hasSeenTour && location.pathname) {
      setRun(false);
      setTimeout(() => {
        setRun(true);
      }, 2000);
    } else {
      setRun(false);
    }
  }, [location.pathname]);

  const handleJoyrideCallback = (data) => {
    const { status } = data;

    if (status === "finished") {
      setRun(false);
      localStorage.setItem(`hasSeenTour_${pageKey}`, "true");
      dispatch(setGuideTour(false));
    } else if (status === "skipped") {
      setRun(false);
      localStorage.setItem("hasSeenTour_Global", "true");
      dispatch(setGuideTour(false));
    }
  };

  useEffect(() => {
    if (tourSelected && location.pathname) {
      let key = getPageKey(location.pathname);
      setPageKey(key);
      setRun(false);
      setRun(true);
    } else {
      setRun(false);
    }
  }, [tourSelected, location.pathname]);

  return (
    run && <Joyride
      steps={STEPS_BY_PAGE[pageKey]}
      run={run}
      continuous
      showProgress
      showSkipButton
      disableBeacon={true}
      disableOverlayClose
      spotlightClicks={true}
      styles={{
        options: { zIndex: 10000, width: 300, height: 200 },
        buttonNext: {
          backgroundColor: dynamicTheme.palette.primary.main,
          color: dynamicTheme.palette.text.light,
          borderRadius: dynamicTheme.shape.borderRadius,
        },
        buttonBack: {
          color: dynamicTheme.palette.primary.dark,
        },
      }}
      callback={handleJoyrideCallback}
    />
  );
};

export default AppTour;
