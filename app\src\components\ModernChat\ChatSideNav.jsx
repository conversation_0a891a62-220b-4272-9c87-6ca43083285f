import React from 'react';
import { 
  Box, 
  TextField, 
  Typography, 
  Avatar, 
  List, 
  ListItem, 
  Badge, 
  Tooltip, 
  IconButton, 
  InputAdornment,
  CircularProgress,
  Skeleton
} from '@mui/material';
import { Search, People, Info, Circle } from '@mui/icons-material';

const ChatSideNav = ({
  users,
  searchQuery,
  setSearchQuery,
  currentUser,
  currentChatId,
  startChat,
  getStatusColor,
  isLoading = false // Add loading prop
}) => {
  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Skeleton loader component for individual chat items
  const ChatItemSkeleton = () => (
    <ListItem sx={{ borderRadius: '4px', mb: 0.5 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
        <Skeleton variant="circular" width={36} height={36} />
        <Box sx={{ flex: 1, minWidth: 0 }}>
          <Skeleton variant="text" width="70%" height={20} />
          <Skeleton variant="text" width="50%" height={16} />
        </Box>
        <Skeleton variant="text" width={30} height={16} />
      </Box>
    </ListItem>
  );

  return (
    <Box sx={{
      width: '320px',
      backgroundColor: '#f3f2f1',
      borderRight: '1px solid #e1dfdd',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Search Bar */}
      <Box sx={{ p: 2, backgroundColor: '#faf9f8' }}>
        <TextField
          fullWidth
          size="small"
          placeholder="Search chats and groups"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          disabled={isLoading}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search sx={{ color: '#616161', fontSize: 20 }} />
              </InputAdornment>
            ),
            sx: {
              backgroundColor: 'white',
              borderRadius: '4px',
              '& .MuiOutlinedInput-notchedOutline': {
                border: '1px solid #e1dfdd'
              }
            }
          }}
        />
      </Box>

      {/* User Profile Section */}
      <Box sx={{ px: 2, pb: 2, backgroundColor: '#faf9f8' }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 2,
          p: 2,
          backgroundColor: 'white',
          borderRadius: '4px',
          border: '1px solid #e1dfdd'
        }}>
          {isLoading ? (
            <>
              <Skeleton variant="circular" width={32} height={32} />
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Skeleton variant="text" width="40%" height={20} />
                <Skeleton variant="text" width="80%" height={16} />
              </Box>
              <Skeleton variant="circular" width={8} height={8} />
            </>
          ) : (
            <>
              <Avatar sx={{
                width: 32,
                height: 32,
                backgroundColor:(theme) =>theme.palette.primary.dark,
                fontSize: '14px'
              }}>
                {currentUser?.email?.charAt(0).toUpperCase()}
              </Avatar>
              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#242424' }}>
                  You
                </Typography>
                <Typography variant="caption" sx={{
                  color: '#616161',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  display: 'block'
                }}>
                  {currentUser?.email}
                </Typography>
              </Box>
              <Circle sx={{ fontSize: 8, color: getStatusColor() }} />
            </>
          )}
        </Box>
      </Box>

      {/* Chat List */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <Box sx={{ px: 2, py: 1 }}>
          <Typography variant="body2" sx={{
            fontWeight: 600,
            color: '#616161',
            textTransform: 'uppercase',
            fontSize: '11px',
            letterSpacing: '0.5px'
          }}>
            Recent Chats
          </Typography>
        </Box>

        {isLoading ? (
          // Loading state with skeleton loaders
          <Box sx={{ px: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
              <CircularProgress size={24} sx={{ color: '#0078d4', mr: 2 }} />
              <Typography variant="body2" sx={{ color: '#616161' }}>
                Loading chats...
              </Typography>
            </Box>
            <List>
              {[1, 2, 3, 4, 5].map((item) => (
                <ChatItemSkeleton key={item} />
              ))}
            </List>
          </Box>
        ) : filteredUsers.length === 0 ? (
          // No chats found state
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <People sx={{ fontSize: 48, color: '#c4c4c4', mb: 2 }} />
            <Typography variant="body2" color="text.secondary" gutterBottom>
              No chats found
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Start a conversation to see it here
            </Typography>
          </Box>
        ) : (
          // Loaded chats list
          <List sx={{ px: 1 }}>
            {filteredUsers.map((data) => (
              <ListItem
                key={data?.id}
                onClick={() => startChat(data)}
                sx={{
                  borderRadius: '4px',
                  mb: 0.5,
                  cursor: 'pointer',
                  backgroundColor: currentChatId === data?.id ? '#e6f2ff' : 'transparent',
                  '&:hover': {
                    backgroundColor: currentChatId === data?.id ? '#e6f2ff' : '#f0f0f0'
                  },
                  border: currentChatId === data?.id ? '1px solid #0078d4' : '1px solid transparent'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, width: '100%' }}>
                  <Badge
                    overlap="circular"
                    anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                  >
                    <Avatar sx={{
                      width: 36,
                      height: 36,
                      backgroundColor:(theme) =>theme.palette.primary.dark,
                      fontSize: '14px'
                    }}>
                      {data?.name?.charAt(0).toUpperCase()}
                    </Avatar>
                  </Badge>

                  <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography variant="body2" sx={{
                      fontWeight: 500,
                      color: '#242424',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {data?.name}
                    </Typography>
                    <Typography variant="caption" sx={{
                      color: '#616161',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      {data?.type === 'GROUP' ? (
                        <>
                          <People sx={{ fontSize: 12 }} />
                          {data?.participants?.length} members
                          <Tooltip
                            title={
                              <Box>
                                {data?.participants?.slice(0, 3).map((participant, idx) => (
                                  <Typography key={participant.id ?? idx} variant="body2">
                                    {participant.email}
                                  </Typography>
                                ))}
                              </Box>
                            }
                            placement="bottom"
                            arrow
                          >
                            <IconButton
                              color="primary"
                              size="small"
                              sx={{
                                ml: 0.5,
                                minWidth: 'auto',
                                width: 16,
                                height: 16,
                                padding: 0,
                                color: '#616161'
                              }}
                              onClick={(e) => e.stopPropagation()}
                            >
                              <Info sx={{ fontSize: 12 }} />
                            </IconButton>
                          </Tooltip>
                        </>
                      ) : (
                        'Direct message'
                      )}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>
                    <Typography variant="caption" sx={{ color: '#616161', fontSize: '10px' }}>
                      {data?.type}
                    </Typography>
                  </Box>
                </Box>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};

export default ChatSideNav;