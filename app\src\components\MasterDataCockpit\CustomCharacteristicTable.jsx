import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { updateMaterialData } from "../../app/payloadSlice";
import { Box, Grid, TextField, Button, Typography, IconButton, Tooltip } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { useSelector } from "react-redux";
import { MATERIAL_VIEWS } from "@constant/enum";

const CustomCharacteristicTable = (props) => {
  const materialID = props.materialID || "SupplierForm";

  const characteristicRowsFromRedux = useSelector(
    (state) =>
      state.payload?.[materialID]?.payloadData?.[MATERIAL_VIEWS.CHARACTERISTIC]?.basic?.ToCharCreationValueData || []
  );
  const payloadState = useSelector((state) => state.payload);
  const characteristic = payloadState?.[materialID]?.payloadData?.[MATERIAL_VIEWS.CHARACTERISTIC]?.basic?.Json451;
  const defaultRows = [
    { Json466: "WHITE", Json467: "White", CharCreationValueId:null },
    { Json466: "BROWN", Json467: "Brown", CharCreationValueId:null },
    { Json466: "GREY", Json467: "Grey", CharCreationValueId:null },
    { Json466: "MAROON", Json467: "maroon", CharCreationValueId:null }
  ]
  const [rows, setRows] = useState([{ Json466: "", Json467: "", CharCreationValueId:null }]);
  const dispatch = useDispatch();

  useEffect(() => {
    if (characteristicRowsFromRedux && characteristicRowsFromRedux.length > 0) {
      setRows(characteristicRowsFromRedux);
    }
  }, [characteristicRowsFromRedux]);
  useEffect(() =>{
    if(characteristic && rows?.length <2 && !characteristicRowsFromRedux?.length){
      setRows(defaultRows);
      dispatch(updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.CHARACTERISTIC,
        itemID: "basic",
        keyName: "ToCharCreationValueData",
        data: defaultRows,
      }));
    }
  },[characteristic])

  const handleChange = (index, field, value) => {
    let updatedRows = JSON.parse(JSON.stringify(rows));
    updatedRows[index][field] = value;
    setRows(updatedRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.CHARACTERISTIC,
        itemID: "basic",
        keyName: "ToCharCreationValueData",
        data: updatedRows,
      })
    );
  };

  const addRow = () => {
    const newRows = [...rows, { Json466: "", Json467: "",  CharCreationValueId:null }];
    setRows(newRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.CHARACTERISTIC,
        itemID: "basic",
        keyName: "ToCharCreationValueData",
        data: newRows,
      })
    );
  };

  const removeRow = (index) => {
    const updatedRows = rows.filter((_, i) => i !== index);
    setRows(updatedRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.CHARACTERISTIC,
        itemID: "basic",
        keyName: "ToCharCreationValueData",
        data: updatedRows,
      })
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography fontWeight={700} mb={2}>Characteristic Details</Typography>
      <Grid container spacing={2} sx={{ fontWeight: 600, mb: 1 }}>
        <Grid item xs={4}><Typography>Characteristic Value</Typography></Grid>
        <Grid item xs={4}><Typography>Description</Typography></Grid>
        <Grid item xs={1}></Grid> {/* For the delete button header space */}
      </Grid>

      {rows.map((row, index) => (
        <Grid container spacing={2} key={index} alignItems="center" mb={1}>
          <Grid item xs={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Size"
              value={row.Json466}
              onChange={(e) => handleChange(index, "Json466", e.target.value)}
              disabled={props.disabled}
            />
          </Grid>
          <Grid item xs={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Colour"
              value={row.Json467}
              onChange={(e) => handleChange(index, "Json467", e.target.value)}
              disabled={props.disabled}
            />
          </Grid>
          <Grid item xs={1}>
            {index !== 0 && (
              <Tooltip title="Remove Row">
                <IconButton onClick={() => removeRow(index)} color="error">
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
          </Grid>
        </Grid>
      ))}

      <Button variant="outlined" onClick={addRow}>
        + Add Row
      </Button>
    </Box>
  );
};

export default CustomCharacteristicTable;
