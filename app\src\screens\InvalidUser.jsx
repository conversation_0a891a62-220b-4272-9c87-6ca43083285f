import React, { useState, useEffect } from "react";
import { Typo<PERSON>, Card } from "@mui/material";
import { destination_IWA_NPI } from "../destinationVariables";

export default function InvalidUser() {

  const [ownerDetails, setOwnerDetails] = useState({
    name: "",
    email: ""
  });

  //Fetch Owner Details
  const getOwnerDetails = () => {
    fetch(`/${destination_IWA_NPI}/api/v1/applicationsMDG/getApplicationOwnerEmailMDG`)
      .then((res) => res.json())
      .then((data) => {
        setOwnerDetails({
          name: data?.data?.applicationOwnerUsername,
          email: data?.data?.applicationOwnerEmail
        });
      });
  };

useEffect(() => {
  getOwnerDetails();
}, []);

  return (
  <div
    style={{
      width: "100vw",
      height: "100vh",
      position: "absolute",
      backgroundColor: "#FAFCFF",
      display: "flex",
    }}
  >
    <Card sx={{ padding: "2rem", margin: "auto", width: "40%" }}>
      <div
        id="alert-dialog-title"
        sx={{
          fontWeight: 600,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          textAlign: "center",
          fontSize: "16px",
        }}
      >
        <div style={{textAlign: "center", marginBottom: "1rem"}}>
        <img src="https://res.cloudinary.com/razeshzone/image/upload/v1588316204/house-key_yrqvxv.svg" className="img-key" alt="" />
</div>
        <Typography
          variant="h6"
          sx={{ textAlign: "center", color: "red", display: "flex", justifyContent: "center" }}
        >
          <span>You are not authorised to access this application.</span>
        </Typography>
        <Typography variant="h6"
          sx={{ textAlign: "center", fontWeight: "400" }}>
            Please contact the <a className='pointer_cursor' style={{color:'#3b30c8'}} href={`mailto:${ownerDetails.email}`}>admin</a> to get access.
          {/* {`Please contact the `}
          <a href={`mailto:${ownerDetails.email}`}>admin</a>. */}
        </Typography>
      </div>


    </Card>
  </div>
  );
}
