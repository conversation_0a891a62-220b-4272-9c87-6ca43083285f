import React, { useEffect, useState } from "react";
import { Box, Button, Grid, Stack, Typography } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import { container_Padding } from "@components/Common/commonStyles";
import useLang from "@hooks/useLang";
import { ToastContainer } from "react-toastify";
import { useSnackbar } from "@hooks/useSnackbar";
import {
  ERROR_MESSAGES,
  MODULE,
  MODULE_MAP,
  REGION,
  REQUEST_HEADER_FILED_NAMES,
  REQUEST_PRIORITY,
  REQUEST_TYPE,
  VISIBILITY_TYPE,
} from "@constant/enum";
import useGenericDtCall from "@hooks/useGenericDtCall";
import {
  setDropDownDataBNKY,
  setHeaderFieldsBnky,
  setTabValue,
  updateModuleFieldDataBK,
  setRequestHeaderResponse,
  setRequestHeader<PERSON>ayloadSingleField,
} from "./bnkySlice";
import { TEMPLATE_NAMES_BOM } from "@constant/changeTemplates";
import { destination_BankKey } from "../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import FilterFieldGlobal from "@components/MasterDataCockpit/FilterFieldGlobal";
import { requestHeaderTabs, setAllTabsData } from "@app/tabsDetailsSlice";
import DownloadDialog from "@components/Common/Downloaddialog";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { setActiveStep } from "@app/redux/stepperSlice";
import { APP_END_POINTS } from "@constant/appEndPoints";
import useDownloadExcel from "@hooks/useDownloadExcel";

const RequestHeaderBankKey = ({
  downloadClicked,
  setIsSecondTabEnabled,
  setIsAttachmentTabEnabled,
  setDownloadClicked
}) => {
  const REQUEST_TYPE_OPTIONS = [
    { code: "Create" },
    { code: "Change" },
    { code: "Create with Upload" },
    { code: "Change with Upload" },
  ];

  const { getDtCall, dtData } = useGenericDtCall();
  const { t } = useLang();
  const { showSnackbar } = useSnackbar();
  const { handleDownload, handleEmailDownload } = useDownloadExcel(MODULE?.BK);

  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isreqBench = queryParams.get("reqBench");
  const isWorkspace = queryParams.get("RequestId");

  const requestHeaderFields = useSelector(
    (state) => state.bankKey.headerFieldsBnky || {}
  );
  const requestHeaderCreatedId = useSelector(
    (state) => state.bankKey.requestHeaderID
  );
  const userData = useSelector((state) => state.userManagement.userData);
  const payloadFields = useSelector(
    (state) => state.bankKey.payload.requestHeaderData || {}
  );
  const requestHeaderResponse = useSelector(
    (state) => state.bankKey.requestHeaderResponse || {}
  );
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  useEffect(() => {
    dispatch(setDropDownDataBNKY({ keyName: "RequestPriority", data: REQUEST_PRIORITY }));
    dispatch(setDropDownDataBNKY({ keyName: "TemplateName", data: TEMPLATE_NAMES_BOM }));
    dispatch(setDropDownDataBNKY({ keyName: "Region", data: REGION }));
    dispatch(
      setDropDownDataBNKY({
        keyName: REQUEST_HEADER_FILED_NAMES.REQUEST_TYPE,
        data: REQUEST_TYPE_OPTIONS,
      })
    );
    if (!isWorkspace && !isreqBench && userData?.user_id) {
      dispatch(
        updateModuleFieldDataBK({
          keyName: "ReqCreatedBy",
          data: userData.user_id,
        })
      );
      dispatch(
        updateModuleFieldDataBK({
          keyName: "RequestStatus",
          data: "DRAFT",
        })
      );
    }
  }, [dispatch, isWorkspace, isreqBench, userData]);

  useEffect(() => {
    fetchHeaderFieldsFromDt();
  }, [payloadFields?.RequestType]);

  useEffect(() => {
    if (dtData) {
      const responseData = dtData.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
          value: item.MDG_MAT_DEFAULT_VALUE,
        }));
      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBnky(requestHeaderObj));
      dispatch(requestHeaderTabs(requestHeaderObj));
    }
  }, [dtData]);

  useEffect(() => {
    if (downloadClicked) {
      if (payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) {
        setOpenDownloadDialog(true);
        return;
      }
      if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
        setDialogOpen(true);
        return;
      }
    }
  }, [downloadClicked]);
  const checkAllFieldsFilled = () => {
    let allFilled = true;
    if (payloadFields && requestHeaderFields[Object.keys(requestHeaderFields)]?.length) {
      requestHeaderFields[Object.keys(requestHeaderFields)[0]]?.forEach(reqst => {

        if (!payloadFields[reqst.jsonName] && reqst.visibility === VISIBILITY_TYPE?.MANDATORY) {

          allFilled = false;
        }
      });
    } else {
      allFilled = false;
    }
    return allFilled;
  };


  const fetchHeaderFieldsFromDt = () => {
    getDtCall({
      decisionTableId: null,
      decisionTableName: "MDG_FMD_REQUEST_HEADER_CONFIG",
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": payloadFields?.RequestType?.code || REQUEST_TYPE?.CREATE,
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": MODULE_MAP?.BK,
        },
      ],
    });
  };
  const handleButtonClick = () => {
    const epochTime = new Date(payloadFields?.ReqCreatedOn)?.getTime();
    const nowDate = `/Date(${Date.now()})/`;

    const payload = {
      RequestId: requestHeaderCreatedId || "",
      ReqCreatedBy: userData?.user_id || "",
      ReqCreatedOn: epochTime ? `/Date(${epochTime})/` : nowDate,
      ReqUpdatedOn: epochTime ? `/Date(${epochTime})/` : nowDate,
      RequestType: payloadFields?.RequestType || "",
      RequestPriority: payloadFields?.RequestPriority || "",
      RequestDesc: payloadFields?.RequestDesc || "",
      RequestStatus: payloadFields?.RequestStatus || "DRAFT",
      TemplateName: payloadFields?.TemplateName || "",
      FieldName: Array.isArray(payloadFields?.FieldName)
        ? payloadFields.FieldName.join("$^$")
        : "",
      Region: payloadFields?.Region || "US",
      IsBifurcated: false,
    };

    doAjax(
      `/${destination_BankKey}${END_POINTS.BANKKEY_APIS.CREATE_BANKKEY_REQUEST_HEADER}`,
      "post",
      (response) => {
        const requestId = response?.body?.requestId;
        showSnackbar(`Request Header Created Successfully with request ID ${requestId}`, "success");
        dispatch(setRequestHeaderResponse(response?.body));

        setIsAttachmentTabEnabled(true);
        dispatch(setRequestHeaderPayloadSingleField({ keyName: REQUEST_HEADER_FILED_NAMES.REQUEST_ID, data: response?.body?.requestId}))


        if (
          payloadFields?.RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD
        ) {
          setOpenDownloadDialog(true);
          return;
        }
        if (payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD) {
          setDialogOpen(true);
          return;
        }

        dispatch(setTabValue(1));
        setIsSecondTabEnabled(true);
      },
      (error) => {
        showSnackbar(ERROR_MESSAGES?.ERROR_REQUEST_HEADER, "error");
      },
      payload
    );
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.BK);
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload(setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.BK);
      handleDownloadDialogClose();
    }
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const handleDownloadDialogClose = () => {
    setDownloadClicked(false);
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
    if (!isWorkspace) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
  };

  const headerFields = requestHeaderFields["Header Data"] || [];

  return (
    <div>
      <Stack spacing={2}>
        <Grid item md={12} 
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 0.25,
          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
          ...container_Padding,
        }}>
          <Typography sx={{ fontSize: 12, fontWeight: "700", pb: 1 }}>
            {t("Header Data")}
          </Typography>
          <Grid container spacing={1}>
            {headerFields
              .filter((f) => f.visibility !== VISIBILITY_TYPE?.HIDDEN)
              .sort((a, b) => a.sequenceNo - b.sequenceNo)
              .map((field) => (

                <FilterFieldGlobal
                  isHeader
                  key={field.jsonName}
                  field={field}
                  dropDownData={{}}
                  module={MODULE_MAP.BK}
                  disabled={isWorkspace || !!requestHeaderCreatedId}
                  requestHeader
                />
              ))}
          </Grid>
          {!isWorkspace && !requestHeaderCreatedId && (
            <Box sx={{ display: "flex", justifyContent: "flex-end", mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleButtonClick}
                disabled={!checkAllFieldsFilled()}
              >
                {t("Save Request Header")}
              </Button>
            </Box>
          )}
          <ReusableBackDrop
            blurLoading={blurLoading}
            loaderMessage={loaderMessage}
          />
        </Grid>
        <ToastContainer />
        <DownloadDialog
          onDownloadTypeChange={onDownloadTypeChange}
          open={openDownloadDialog}
          downloadType={downloadType}
          handleDownloadTypeChange={handleDownloadTypeChange}
          onClose={handleDownloadDialogClose}
        />
      </Stack>
    </div>
  );
};

export default RequestHeaderBankKey;
