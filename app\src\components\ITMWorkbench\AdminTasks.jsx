import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import configData from "../../data/configData";
import { returnUserGroupMap } from "../../data/userGroupData";
import { returnUserMap } from "../../data/userData";
import { accessToken, userData, userPermissions } from "../../data/propData";
import { useNavigate } from "react-router-dom";
import { setTaskData } from "../../app/userManagementSlice";
import { setHistoryPath } from "../../app/utilitySlice";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_CostCenter_Mass, destination_GeneralLedger, destination_IWA_NPI, destination_MaterialMgmt, destination_ProfitCenter_Mass, destination_BankKey } from "../../destinationVariables";
import { setIwmMyTask } from "../../app/initialDataSlice";
import { clearGeneralLedger } from "../../app/generalLedgerTabSlice";
import { setTabValue } from "../../app/requestDataSlice";
import { MODULE_MAP } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";

export default function AdminTasks() {
  let userData = useSelector((state) => state.userManagement.userData);
  const task = useSelector((state) => state.userManagement.taskData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  let dispatch = useDispatch();
  const navigate = useNavigate();
  const [userRawData, setUserRawData] = useState(null);
  const [userGroupRawData, setUserGroupRawData] = useState(null);
  const [rowData, setRowData] = useState({});
  const [userListBySystem, setUserListBySystem] = useState(null);
  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://ca-gbd-ca-caf-cw-mdg-iwm.cfapps.us10-001.hana.ondemand.com",
      },
      // {
      //   Description: "",
      //   Name: "IWAServices",
      //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
      // },
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };

  // const onTaskClick = (task) => {    //Old version
  const userPrefData = {
    // "Language": {
    //   "value": "English",
    //   "key": "EN"
    // },
    // "AppConfiguration": {
    //   "title": "Intelligent Work Management"
    // },
    // "Theme": {
    //   "key": "blueTheme"
    // },
    DateTimeFormat: {
      dateTimeFormat: "DD MMM YYYY||HH:mm",
      timeZone: "Asia/Calcutta",
    },
  };

  var MMUrl = {
    "Approver User Task - Mass": `/requestBench/createRequest`,
    "MDM User Task - Mass": `/requestBench/createRequest`,
  };
  var urlsMass = {
    "Approver User Task - Mass": `/masterDataCockpit/materialMaster/massMaterialTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/materialMaster/massMaterialTableRequestBench`,
  };
  var urls = {
    "Approver User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "Sales User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
    "Procurement User Task": `/masterDataCockpit/materialMaster/displayMaterialDetail/${task?.requestId}`,
  };

  var costUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
  };
  var costCreateUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
  };
  var costUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displayChangeCC/${task?.requestId}`,
  };
  var costCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/costCenterSunoco/displaySingleCCSunoco/${task?.requestId}`,
  };
  var costMassCreateUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench/${task?.requestId}`,
  };
  var costCenterMassChangeUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massChangeCostCenterTableRequestBench/${task?.requestId}`,
  };

  var bankKeyUrls = {
    "Approver User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/bankKey/displayBankKey/${task?.requestId}`,
  };

  var profitUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePC/${task?.requestId}`,
  };
  var massChangeCCPC = {
    "Approver User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
    "MDM User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
    "Finance User Task - Mass": `/masterDataCockpit/sunoco/sunocoCCPC/changeMassCCPCRequestBench/${task?.requestId}`,
  };
  var profitUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displayChangePCSunoco/${task?.requestId}`,
  };
  var profitCreateUrlsSun = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunRB/${task?.requestId}`,
  };
  var profitCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/profitCenterSunoco/displaySinglePCSunoco/${task?.requestId}`,
  };

  var sunocoCCPCCreateUrls = {
    "Approver User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/sunoco/CostandProfitCenter/viewSunocoCostCenter/${task?.requestId}`,
  };

  var PCGCreateUrls = {
    "Approver User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/et/hierarchyNodeProfitCenter/displayHierarchyNodeProfitCenter/${task?.requestId}`,
  };

  var generalLedgerUrls = {
    "Approver User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/generalLedger/displayGeneralLedger/${task?.requestId}`,
  };
  var generalLedgerUrlsChange = {
    "Approver User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
    "MDM User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
    "Finance User Task": `/masterDataCockpit/generalLedger/displayChangeGL/${task?.requestId}`,
  };
  var generalLedgerMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/generalLedger/massGLTableRequestBench`,
  };
  var generalLedgerMassChangeUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/generalLedger/massChangeGLTableRequestBench`,
  };
  var costMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/costCenter/massCostCenterTableRequestBench`,
  };

  var profitMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/profitCenter/massProfitCenterTableRequestBench`,
  };
  var profitMassUrlsSun = {
    "Approver User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
    "MDM User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
    "Finance User Task - Mass": `/masterDataCockpit/sunoco/profitCenterSunoco/massProfitCenterTableRequestBenchSunoco`,
  };

  var bankKeyMassUrls = {
    "Approver User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
    "MDM User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
    "Finance User Task - Mass": `/masterDataCockpit/et/bankKey/massBKTableRequestBench`,
  };

  const onTaskClick = (task) => {
    dispatch(setTaskData(task));
    if (task?.processDisplayName === "Material") {
      navigate(`/requestBench/createRequest?RequestId=${task?.ATTRIBUTE_1}`);
    }
    if (task?.processDisplayName === MODULE_MAP?.PCG) {
        navigate(`/${APP_END_POINTS?.CREATE_PCG}?RequestId=${task?.ATTRIBUTE_1}&RequestType=${task?.ATTRIBUTE_2}`);
    }
    if (task?.processDisplayName === MODULE_MAP?.CCG) {
        navigate(`/${APP_END_POINTS?.CREATE_CCG}?RequestId=${task?.ATTRIBUTE_1}&RequestType=${task?.ATTRIBUTE_2}`);
    }
    if (task?.processDisplayName === MODULE_MAP?.CEG) {
        navigate(`/${APP_END_POINTS?.CREATE_CEG}?RequestId=${task?.ATTRIBUTE_1}&RequestType=${task?.ATTRIBUTE_2}`);
    }

    dispatch(setHistoryPath({ url: window.location.pathname, module: "ITMWorkbench" }));

    console.log("task", task);
  };


  const fetchFilterViewList = () => {
    console.log("fetchFilterView");
  };
  const clearFilterView = () => {
    console.log("clearFilterView");
  };
  const fetchUserRawData = () => {
    doAjax(`/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`, "get", (resData) => {
      var tempData = resData.data;
      var tempUserData = tempData?.map((udata) => {
        return { ...udata, userId: udata?.emailId };
      });
      var finalData = { ...resData, data: tempUserData };
      setUserRawData(finalData);
      setUserListBySystem({ MDG: [...finalData.data] });
    });
  };

  const fetchUserGroupRawData = () => {
    doAjax(`/${destination_IWA_NPI}/api/v1/groupsMDG/getAllGroupsMDG`, "get", (resData) => {
      var tempData = resData.data;
      var tempGroupData = tempData?.map((gData) => {
        return { ...gData, groupName: gData?.name };
      });
      var finalData = { ...resData, data: tempGroupData };
      setUserGroupRawData(finalData);
    });
  };

  const onActionComplete = (successFlag, taskPayload) => {
    console.log("Success flag.", successFlag);
    console.log("Task Payload.", taskPayload);
  };

  const selectedFilterView = {
    filterData: [
      {
        id: "8670ebc5-eb32-477a-a47e-2a9d1ee62b8f",
        filter: "status",
        filterName: "Status",
        type: "ADVANCE",
        isChecked: false,
        filterKey: "itmStatus",
        dataType: "dropdown",
        filterSubItems: [
          {
            id: "828d7797-3cd0-4766-8748-b2ddea9f45c6",
            filter: "status",
            filterName: "Approved",
            type: "ADVANCE",
            isChecked: true,
          },
        ],
        count: 1,
      },
      {
        id: "314f4837-7700-4234-a7fd-26a983f0f3cf",
        filter: "ownerId",
        filterName: "Owner Id",
        type: "ADVANCE",
        isChecked: false,
        filterKey: "ownerId",
        dataType: "dropdown",
        filterSubItems: [
          {
            id: "abc178c6-7e12-421b-8c8e-a370e8d92931",
            filter: "ownerId",
            filterName: "<EMAIL>",
            type: "ADVANCE",
            isChecked: true,
          },
        ],
        count: 1,
      },
    ],
  };

  useEffect(() => {
    setIwmMyTask({});
    // fetchUserRawData();
    // fetchUserGroupRawData();
  }, []);
  console.log("userdata", returnUserMap(userRawData), returnUserGroupMap(userGroupRawData));
  return (
    <div style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }} className={"workspaceOverride"}>
        <WorkspaceComponent
          token={"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogIitJQzR1dUlwdm93ODJFT2xZZVhwRlJDbGxsR0RpL0t5Mkh4V0Jsck55KzA9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tu2ojptF8tG6SU4Yk6JTmF6h6J3hAcoCCzs26kz6H-P1MhYS2qChrYsGWSXffm3wNXuA3elPcXbQ4rnrpPiGZ47I7pfAm7T7d3gw6E-9bYqs8eBoPC5QtRdFCzvMAbZYKXbyCL3v35nXkZsbVHDnXXR-L7hBRejjy-a9wuJ54E5CmNo9j9eMlP0nTL9aBfmW6DvhPK6xn-T_RyiSZwS8RkLk39DZ7FZdLRURGLOj_yGymWgMIIUG0kUkPFGBuNSLhw_kNase6EqEUFljcISOEgM8rNZ8P0Fd3zEwL-O2zuV9VKoPO8442Z80Bl_b6FIVXsIwzCtOaCcNWcYsNTxCfQ"}
          configData={configData}
          destinationData={DestinationConfig}
          userData={{ ...userData, user_id: userData?.emailId }}
          userPreferences={userPrefData} //Not needed check
          userPermissions={userPermissions}
          userList={{}}
          groupList={{}}
          userListBySystem={userListBySystem}
          useWorkAccess={applicationConfig.environment === "localhost"?true:false}
          useConfigServerDestination={applicationConfig.environment === "localhost"?true:false}
          // inboxTypeKey={task?.inboxTypeKey}
          // workspaceLabel={task?.workspaceLabel}
          inboxTypeKey={"ADMIN_TASKS"}
          workspaceLabel={"Admin Tasks"}
          workspaceFiltersByAPIDriven={true}
          subInboxTypeKey={null}
          cachingBaseUrl={baseUrl_ITMJava}
          onTaskClick={onTaskClick}
          // onTaskLinkClick={onTaskLinkClick}  prev comment
          onActionComplete={onActionComplete}
          selectedFilterView={null}
          isFilterView={false}
          fetchFilterViewList={fetchFilterViewList}
          savedFilterViewData={[]}
          clearFilterView={clearFilterView}
          filterViewList={[]}
          selectedTabId={null}
          // forwardTaskData = {forwardedToUsers}
          userProcess={[]}
          // handleCustomActionApis={handleCustomActionApis}   prev comment
        />
    </div>
  );
}
