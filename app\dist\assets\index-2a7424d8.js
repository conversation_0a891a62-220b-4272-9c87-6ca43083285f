import{L as un,pa as dn,pb as mn,dk as hn,pc as fn,pd as gn,my as pn,mC as xn,hD as At,t as H,cb as M,r as Fe,fd as vn}from"./index-226a1e75.js";const In=Object.freeze(Object.defineProperty({__proto__:null,default:un,getListItemUtilityClass:dn,listItemClasses:mn},Symbol.toStringTag,{value:"Module"})),Tn=Object.freeze(Object.defineProperty({__proto__:null,default:hn,getMenuUtilityClass:fn,menuClasses:gn},Symbol.toStringTag,{value:"Module"}));function Sn(e){pn(1,arguments);var t=xn(e),o=t.getDay();return o}const si=Object.freeze(Object.defineProperty({__proto__:null,default:Sn},Symbol.toStringTag,{value:"Module"})),ci=At(Tn),wt=0,Oe=1,qe=2,Eo=4;function ao(e){return()=>e}function wn(e){e()}function tt(e,t){return o=>e(t(o))}function uo(e,t){return()=>e(t)}function Cn(e,t){return o=>e(t,o)}function Vt(e){return e!==void 0}function yn(...e){return()=>{e.map(wn)}}function Ke(){}function Ct(e,t){return t(e),e}function bn(e,t){return t(e)}function J(...e){return e}function q(e,t){return e(Oe,t)}function F(e,t){e(wt,t)}function Ft(e){e(qe)}function oe(e){return e(Eo)}function L(e,t){return q(e,Cn(t,wt))}function Te(e,t){const o=e(Oe,n=>{o(),t(n)});return o}function mo(e){let t,o;return n=>r=>{t=r,o&&clearTimeout(o),o=setTimeout(()=>{n(t)},e)}}function Lo(e,t){return e===t}function Z(e=Lo){let t;return o=>n=>{e(t,n)||(t=n,o(n))}}function W(e){return t=>o=>{e(o)&&t(o)}}function B(e){return t=>tt(t,e)}function be(e){return t=>()=>{t(e)}}function I(e,...t){const o=Hn(...t);return(n,r)=>{switch(n){case qe:Ft(e);return;case Oe:return q(e,o(r))}}}function He(e,t){return o=>n=>{o(t=e(t,n))}}function De(e){return t=>o=>{e>0?e--:t(o)}}function ke(e){let t=null,o;return n=>r=>{t=r,!o&&(o=setTimeout(()=>{o=void 0,n(t)},e))}}function D(...e){const t=new Array(e.length);let o=0,n=null;const r=Math.pow(2,e.length)-1;return e.forEach((i,l)=>{const s=Math.pow(2,l);q(i,c=>{const a=o;o=o|s,t[l]=c,a!==r&&o===r&&n&&(n(),n=null)})}),i=>l=>{const s=()=>{i([l].concat(t))};o===r?s():n=s}}function Hn(...e){return t=>e.reduceRight(bn,t)}function zn(e){let t,o;const n=()=>t==null?void 0:t();return function(r,i){switch(r){case Oe:return i?o===i?void 0:(n(),o=i,t=q(e,i),t):(n(),Ke);case qe:n(),o=null;return}}}function S(e){let t=e;const o=G();return(n,r)=>{switch(n){case wt:t=r;break;case Oe:{r(t);break}case Eo:return t}return o(n,r)}}function se(e,t){return Ct(S(t),o=>L(e,o))}function G(){const e=[];return(t,o)=>{switch(t){case wt:e.slice().forEach(n=>{n(o)});return;case qe:e.splice(0,e.length);return;case Oe:return e.push(o),()=>{const n=e.indexOf(o);n>-1&&e.splice(n,1)}}}}function he(e){return Ct(G(),t=>L(e,t))}function $(e,t=[],{singleton:o}={singleton:!0}){return{constructor:e,dependencies:t,id:Rn(),singleton:o}}const Rn=()=>Symbol();function Bn(e){const t=new Map,o=({constructor:n,dependencies:r,id:i,singleton:l})=>{if(l&&t.has(i))return t.get(i);const s=n(r.map(c=>o(c)));return l&&t.set(i,s),s};return o(e)}function ne(...e){const t=G(),o=new Array(e.length);let n=0;const r=Math.pow(2,e.length)-1;return e.forEach((i,l)=>{const s=Math.pow(2,l);q(i,c=>{o[l]=c,n=n|s,n===r&&F(t,o)})}),function(i,l){switch(i){case qe:{Ft(t);return}case Oe:return n===r&&l(o),q(t,l)}}}function j(e,t=Lo){return I(e,Z(t))}function Lt(...e){return function(t,o){switch(t){case qe:return;case Oe:return yn(...e.map(n=>q(n,o)))}}}var ae=(e=>(e[e.DEBUG=0]="DEBUG",e[e.INFO=1]="INFO",e[e.WARN=2]="WARN",e[e.ERROR=3]="ERROR",e))(ae||{});const kn={0:"debug",3:"error",1:"log",2:"warn"},En=()=>typeof globalThis>"u"?window:globalThis,Me=$(()=>{const e=S(3);return{log:S((t,o,n=1)=>{var r;const i=(r=En().VIRTUOSO_LOG_LEVEL)!=null?r:oe(e);n>=i&&console[kn[n]]("%creact-virtuoso: %c%s %o","color: #0253b3; font-weight: bold","color: initial",t,o)}),logLevel:e}},[],{singleton:!0});function ze(e,t,o){return Dt(e,t,o).callbackRef}function Dt(e,t,o){const n=H.useRef(null);let r=l=>{};const i=H.useMemo(()=>typeof ResizeObserver<"u"?new ResizeObserver(l=>{const s=()=>{const c=l[0].target;c.offsetParent!==null&&e(c)};o?s():requestAnimationFrame(s)}):null,[e,o]);return r=l=>{l&&t?(i==null||i.observe(l),n.current=l):(n.current&&(i==null||i.unobserve(n.current)),n.current=null)},{callbackRef:r,ref:n}}function Oo(e,t,o,n,r,i,l,s,c){const a=H.useCallback(f=>{const T=Ln(f.children,t,s?"offsetWidth":"offsetHeight",r);let p=f.parentElement;for(;!p.dataset.virtuosoScroller;)p=p.parentElement;const v=p.lastElementChild.dataset.viewportType==="window";let C;v&&(C=p.ownerDocument.defaultView);const x=l?s?l.scrollLeft:l.scrollTop:v?s?C.scrollX||C.document.documentElement.scrollLeft:C.scrollY||C.document.documentElement.scrollTop:s?p.scrollLeft:p.scrollTop,g=l?s?l.scrollWidth:l.scrollHeight:v?s?C.document.documentElement.scrollWidth:C.document.documentElement.scrollHeight:s?p.scrollWidth:p.scrollHeight,m=l?s?l.offsetWidth:l.offsetHeight:v?s?C.innerWidth:C.innerHeight:s?p.offsetWidth:p.offsetHeight;n({scrollHeight:g,scrollTop:Math.max(x,0),viewportHeight:m}),i==null||i(s?ho("column-gap",getComputedStyle(f).columnGap,r):ho("row-gap",getComputedStyle(f).rowGap,r)),T!==null&&e(T)},[e,t,r,i,l,n,s]);return Dt(a,o,c)}function Ln(e,t,o,n){const r=e.length;if(r===0)return null;const i=[];for(let l=0;l<r;l++){const s=e.item(l);if(s.dataset.index===void 0)continue;const c=parseInt(s.dataset.index),a=parseFloat(s.dataset.knownSize),f=t(s,o);if(f===0&&n("Zero-sized element, this should not happen",{child:s},ae.ERROR),f===a)continue;const T=i[i.length-1];i.length===0||T.size!==f||T.endIndex!==c-1?i.push({endIndex:c,size:f,startIndex:c}):i[i.length-1].endIndex++}return i}function ho(e,t,o){return t!=="normal"&&!(t!=null&&t.endsWith("px"))&&o(`${e} was not resolved to pixel value correctly`,t,ae.WARN),t==="normal"?0:parseInt(t??"0",10)}function _t(e,t,o){const n=H.useRef(null),r=H.useCallback(c=>{if(!(c!=null&&c.offsetParent))return;const a=c.getBoundingClientRect(),f=a.width;let T,p;if(t){const v=t.getBoundingClientRect(),C=a.top-v.top;p=v.height-Math.max(0,C),T=C+t.scrollTop}else{const v=l.current.ownerDocument.defaultView;p=v.innerHeight-Math.max(0,a.top),T=a.top+v.scrollY}n.current={offsetTop:T,visibleHeight:p,visibleWidth:f},e(n.current)},[e,t]),{callbackRef:i,ref:l}=Dt(r,!0,o),s=H.useCallback(()=>{r(l.current)},[r,l]);return H.useEffect(()=>{var c;if(t){t.addEventListener("scroll",s);const a=new ResizeObserver(()=>{requestAnimationFrame(s)});return a.observe(t),()=>{t.removeEventListener("scroll",s),a.unobserve(t)}}else{const a=(c=l.current)==null?void 0:c.ownerDocument.defaultView;return a==null||a.addEventListener("scroll",s),a==null||a.addEventListener("resize",s),()=>{a==null||a.removeEventListener("scroll",s),a==null||a.removeEventListener("resize",s)}}},[s,t,l]),i}const ue=$(()=>{const e=G(),t=G(),o=S(0),n=G(),r=S(0),i=G(),l=G(),s=S(0),c=S(0),a=S(0),f=S(0),T=G(),p=G(),v=S(!1),C=S(!1),x=S(!1);return L(I(e,B(({scrollTop:g})=>g)),t),L(I(e,B(({scrollHeight:g})=>g)),l),L(t,r),{deviation:o,fixedFooterHeight:a,fixedHeaderHeight:c,footerHeight:f,headerHeight:s,horizontalDirection:C,scrollBy:p,scrollContainerState:e,scrollHeight:l,scrollingInProgress:v,scrollTo:T,scrollTop:t,skipAnimationFrameInResizeObserver:x,smoothScrollTargetReached:n,statefulScrollTop:r,viewportHeight:i}},[],{singleton:!0}),ot={lvl:0};function Mo(e,t){const o=e.length;if(o===0)return[];let{index:n,value:r}=t(e[0]);const i=[];for(let l=1;l<o;l++){const{index:s,value:c}=t(e[l]);i.push({end:s-1,start:n,value:r}),n=s,r=c}return i.push({end:1/0,start:n,value:r}),i}function K(e){return e===ot}function nt(e,t){if(!K(e))return t===e.k?e.v:t<e.k?nt(e.l,t):nt(e.r,t)}function we(e,t,o="k"){if(K(e))return[-1/0,void 0];if(Number(e[o])===t)return[e.k,e.v];if(Number(e[o])<t){const n=we(e.r,t,o);return n[0]===-1/0?[e.k,e.v]:n}return we(e.l,t,o)}function me(e,t,o){return K(e)?Wo(t,o,1):t===e.k?re(e,{k:t,v:o}):t<e.k?fo(re(e,{l:me(e.l,t,o)})):fo(re(e,{r:me(e.r,t,o)}))}function Ue(){return ot}function yt(e,t,o){if(K(e))return[];const n=we(e,t)[0];return On(Mt(e,n,o))}function Ot(e,t){if(K(e))return ot;const{k:o,l:n,r}=e;if(t===o){if(K(n))return r;if(K(r))return n;{const[i,l]=Po(n);return pt(re(e,{k:i,l:jo(n),v:l}))}}else return t<o?pt(re(e,{l:Ot(n,t)})):pt(re(e,{r:Ot(r,t)}))}function Ve(e){return K(e)?[]:[...Ve(e.l),{k:e.k,v:e.v},...Ve(e.r)]}function Mt(e,t,o){if(K(e))return[];const{k:n,l:r,r:i,v:l}=e;let s=[];return n>t&&(s=s.concat(Mt(r,t,o))),n>=t&&n<=o&&s.push({k:n,v:l}),n<=o&&(s=s.concat(Mt(i,t,o))),s}function pt(e){const{l:t,lvl:o,r:n}=e;if(n.lvl>=o-1&&t.lvl>=o-1)return e;if(o>n.lvl+1){if(zt(t))return Ao(re(e,{lvl:o-1}));if(!K(t)&&!K(t.r))return re(t.r,{l:re(t,{r:t.r.l}),lvl:o,r:re(e,{l:t.r.r,lvl:o-1})});throw new Error("Unexpected empty nodes")}else{if(zt(e))return jt(re(e,{lvl:o-1}));if(!K(n)&&!K(n.l)){const r=n.l,i=zt(r)?n.lvl-1:n.lvl;return re(r,{l:re(e,{lvl:o-1,r:r.l}),lvl:r.lvl+1,r:jt(re(n,{l:r.r,lvl:i}))})}else throw new Error("Unexpected empty nodes")}}function re(e,t){return Wo(t.k!==void 0?t.k:e.k,t.v!==void 0?t.v:e.v,t.lvl!==void 0?t.lvl:e.lvl,t.l!==void 0?t.l:e.l,t.r!==void 0?t.r:e.r)}function jo(e){return K(e.r)?e.l:pt(re(e,{r:jo(e.r)}))}function zt(e){return K(e)||e.lvl>e.r.lvl}function Po(e){return K(e.r)?[e.k,e.v]:Po(e.r)}function Wo(e,t,o,n=ot,r=ot){return{k:e,l:n,lvl:o,r,v:t}}function fo(e){return jt(Ao(e))}function Ao(e){const{l:t}=e;return!K(t)&&t.lvl===e.lvl?re(t,{r:re(e,{l:t.r})}):e}function jt(e){const{lvl:t,r:o}=e;return!K(o)&&!K(o.r)&&o.lvl===t&&o.r.lvl===t?re(o,{l:re(e,{r:o.l}),lvl:t+1}):e}function On(e){return Mo(e,({k:t,v:o})=>({index:t,value:o}))}function Vo(e,t){return!!(e&&e.startIndex===t.startIndex&&e.endIndex===t.endIndex)}function rt(e,t){return!!(e&&e[0]===t[0]&&e[1]===t[1])}const Gt=$(()=>({recalcInProgress:S(!1)}),[],{singleton:!0});function Fo(e,t,o){return e[vt(e,t,o)]}function vt(e,t,o,n=0){let r=e.length-1;for(;n<=r;){const i=Math.floor((n+r)/2),l=e[i],s=o(l,t);if(s===0)return i;if(s===-1){if(r-n<2)return i-1;r=i-1}else{if(r===n)return i;n=i+1}}throw new Error(`Failed binary finding record in array - ${e.join(",")}, searched for ${t}`)}function Mn(e,t,o,n){const r=vt(e,t,n),i=vt(e,o,n,r);return e.slice(r,i+1)}function Ce(e,t){return Math.round(e.getBoundingClientRect()[t])}function bt(e){return!K(e.groupOffsetTree)}function Nt({index:e},t){return t===e?0:t<e?-1:1}function jn(){return{groupIndices:[],groupOffsetTree:Ue(),lastIndex:0,lastOffset:0,lastSize:0,offsetTree:[],sizeTree:Ue()}}function Pn(e,t){let o=K(e)?0:1/0;for(const n of t){const{endIndex:r,size:i,startIndex:l}=n;if(o=Math.min(o,l),K(e)){e=me(e,0,i);continue}const s=yt(e,l-1,r+1);if(s.some(Gn(n)))continue;let c=!1,a=!1;for(const{end:f,start:T,value:p}of s)c?(r>=T||i===p)&&(e=Ot(e,T)):(a=p!==i,c=!0),f>r&&r>=T&&p!==i&&(e=me(e,r+1,p));a&&(e=me(e,l,i))}return[e,o]}function Wn(e){return typeof e.groupIndex<"u"}function An({offset:e},t){return t===e?0:t<e?-1:1}function it(e,t,o){if(t.length===0)return 0;const{index:n,offset:r,size:i}=Fo(t,e,Nt),l=e-n,s=i*l+(l-1)*o+r;return s>0?s+o:s}function Do(e,t){if(!bt(t))return e;let o=0;for(;t.groupIndices[o]<=e+o;)o++;return e+o}function _o(e,t,o){if(Wn(e))return t.groupIndices[e.groupIndex]+1;{const n=e.index==="LAST"?o:e.index;let r=Do(n,t);return r=Math.max(0,r,Math.min(o,r)),r}}function Vn(e,t,o,n=0){return n>0&&(t=Math.max(t,Fo(e,n,Nt).offset)),Mo(Mn(e,t,o,An),_n)}function Fn(e,[t,o,n,r]){t.length>0&&n("received item sizes",t,ae.DEBUG);const i=e.sizeTree;let l=i,s=0;if(o.length>0&&K(i)&&t.length===2){const p=t[0].size,v=t[1].size;l=o.reduce((C,x)=>me(me(C,x,p),x+1,v),l)}else[l,s]=Pn(l,t);if(l===i)return e;const{lastIndex:c,lastOffset:a,lastSize:f,offsetTree:T}=Pt(e.offsetTree,s,l,r);return{groupIndices:o,groupOffsetTree:o.reduce((p,v)=>me(p,v,it(v,T,r)),Ue()),lastIndex:c,lastOffset:a,lastSize:f,offsetTree:T,sizeTree:l}}function Dn(e){return Ve(e).map(({k:t,v:o},n,r)=>{const i=r[n+1];return{endIndex:i?i.k-1:1/0,size:o,startIndex:t}})}function go(e,t){let o=0,n=0;for(;o<e;)o+=t[n+1]-t[n]-1,n++;return n-(o===e?0:1)}function Pt(e,t,o,n){let r=e,i=0,l=0,s=0,c=0;if(t!==0){c=vt(r,t-1,Nt),s=r[c].offset;const a=we(o,t-1);i=a[0],l=a[1],r.length&&r[c].size===we(o,t)[1]&&(c-=1),r=r.slice(0,c+1)}else r=[];for(const{start:a,value:f}of yt(o,t,1/0)){const T=a-i,p=T*l+s+T*n;r.push({index:a,offset:p,size:f}),i=a,s=p,l=f}return{lastIndex:i,lastOffset:s,lastSize:l,offsetTree:r}}function _n(e){return{index:e.index,value:e}}function Gn(e){const{endIndex:t,size:o,startIndex:n}=e;return r=>r.start===n&&(r.end===t||r.end===1/0)&&r.value===o}const Nn={offsetHeight:"height",offsetWidth:"width"},Re=$(([{log:e},{recalcInProgress:t}])=>{const o=G(),n=G(),r=se(n,0),i=G(),l=G(),s=S(0),c=S([]),a=S(void 0),f=S(void 0),T=S((h,d)=>Ce(h,Nn[d])),p=S(void 0),v=S(0),C=jn(),x=se(I(o,D(c,e,v),He(Fn,C),Z()),C),g=se(I(c,Z(),He((h,d)=>({current:d,prev:h.current}),{current:[],prev:[]}),B(({prev:h})=>h)),[]);L(I(c,W(h=>h.length>0),D(x,v),B(([h,d,w])=>{const z=h.reduce((O,k,P)=>me(O,k,it(k,d.offsetTree,w)||P),Ue());return{...d,groupIndices:h,groupOffsetTree:z}})),x),L(I(n,D(x),W(([h,{lastIndex:d}])=>h<d),B(([h,{lastIndex:d,lastSize:w}])=>[{endIndex:d,size:w,startIndex:h}])),o),L(a,f);const m=se(I(a,B(h=>h===void 0)),!0);L(I(f,W(h=>h!==void 0&&K(oe(x).sizeTree)),B(h=>[{endIndex:0,size:h,startIndex:0}])),o);const u=he(I(o,D(x),He(({sizes:h},[d,w])=>({changed:w!==h,sizes:w}),{changed:!1,sizes:C}),B(h=>h.changed)));q(I(s,He((h,d)=>({diff:h.prev-d,prev:d}),{diff:0,prev:0}),B(h=>h.diff)),h=>{const{groupIndices:d}=oe(x);if(h>0)F(t,!0),F(i,h+go(h,d));else if(h<0){const w=oe(g);w.length>0&&(h-=go(-h,w)),F(l,h)}}),q(I(s,D(e)),([h,d])=>{h<0&&d("`firstItemIndex` prop should not be set to less than zero. If you don't know the total count, just use a very high value",{firstItemIndex:s},ae.ERROR)});const y=he(i);L(I(i,D(x),B(([h,d])=>{const w=d.groupIndices.length>0,z=[],O=d.lastSize;if(w){const k=nt(d.sizeTree,0);let P=0,N=0;for(;P<h;){const E=d.groupIndices[N],U=d.groupIndices.length===N+1?1/0:d.groupIndices[N+1]-E-1;z.push({endIndex:E,size:k,startIndex:E}),z.push({endIndex:E+1+U-1,size:O,startIndex:E+1}),N++,P+=U+1}const Y=Ve(d.sizeTree);return P!==h&&Y.shift(),Y.reduce((E,{k:U,v:ie})=>{let fe=E.ranges;return E.prevSize!==0&&(fe=[...E.ranges,{endIndex:U+h-1,size:E.prevSize,startIndex:E.prevIndex}]),{prevIndex:U+h,prevSize:ie,ranges:fe}},{prevIndex:h,prevSize:0,ranges:z}).ranges}return Ve(d.sizeTree).reduce((k,{k:P,v:N})=>({prevIndex:P+h,prevSize:N,ranges:[...k.ranges,{endIndex:P+h-1,size:k.prevSize,startIndex:k.prevIndex}]}),{prevIndex:0,prevSize:O,ranges:[]}).ranges})),o);const R=he(I(l,D(x,v),B(([h,{offsetTree:d},w])=>{const z=-h;return it(z,d,w)})));return L(I(l,D(x,v),B(([h,d,w])=>{if(d.groupIndices.length>0){if(K(d.sizeTree))return d;let z=Ue();const O=oe(g);let k=0,P=0,N=0;for(;k<-h;){N=O[P];const Y=O[P+1]-N-1;P++,k+=Y+1}if(z=Ve(d.sizeTree).reduce((Y,{k:E,v:U})=>me(Y,Math.max(0,E+h),U),z),k!==-h){const Y=nt(d.sizeTree,N);z=me(z,0,Y);const E=we(d.sizeTree,-h+1)[1];z=me(z,1,E)}return{...d,sizeTree:z,...Pt(d.offsetTree,0,z,w)}}else{const z=Ve(d.sizeTree).reduce((O,{k,v:P})=>me(O,Math.max(0,k+h),P),Ue());return{...d,sizeTree:z,...Pt(d.offsetTree,0,z,w)}}})),x),{beforeUnshiftWith:y,data:p,defaultItemSize:f,firstItemIndex:s,fixedItemSize:a,gap:v,groupIndices:c,itemSize:T,listRefresh:u,shiftWith:l,shiftWithOffset:R,sizeRanges:o,sizes:x,statefulTotalCount:r,totalCount:n,trackItemSizes:m,unshiftWith:i}},J(Me,Gt),{singleton:!0});function $n(e){return e.reduce((t,o)=>(t.groupIndices.push(t.totalCount),t.totalCount+=o+1,t),{groupIndices:[],totalCount:0})}const Go=$(([{groupIndices:e,sizes:t,totalCount:o},{headerHeight:n,scrollTop:r}])=>{const i=G(),l=G(),s=he(I(i,B($n)));return L(I(s,B(c=>c.totalCount)),o),L(I(s,B(c=>c.groupIndices)),e),L(I(ne(r,t,n),W(([c,a])=>bt(a)),B(([c,a,f])=>we(a.groupOffsetTree,Math.max(c-f,0),"v")[0]),Z(),B(c=>[c])),l),{groupCounts:i,topItemsIndexes:l}},J(Re,ue)),je=$(([{log:e}])=>{const t=S(!1),o=he(I(t,W(n=>n),Z()));return q(t,n=>{n&&oe(e)("props updated",{},ae.DEBUG)}),{didMount:o,propsReady:t}},J(Me),{singleton:!0}),Un=typeof document<"u"&&"scrollBehavior"in document.documentElement.style;function No(e){const t=typeof e=="number"?{index:e}:e;return t.align||(t.align="start"),(!t.behavior||!Un)&&(t.behavior="auto"),t.offset||(t.offset=0),t}const st=$(([{gap:e,listRefresh:t,sizes:o,totalCount:n},{fixedFooterHeight:r,fixedHeaderHeight:i,footerHeight:l,headerHeight:s,scrollingInProgress:c,scrollTo:a,smoothScrollTargetReached:f,viewportHeight:T},{log:p}])=>{const v=G(),C=G(),x=S(0);let g=null,m=null,u=null;function y(){g&&(g(),g=null),u&&(u(),u=null),m&&(clearTimeout(m),m=null),F(c,!1)}return L(I(v,D(o,T,n,x,s,l,p),D(e,i,r),B(([[R,h,d,w,z,O,k,P],N,Y,E])=>{const U=No(R),{align:ie,behavior:fe,offset:xe}=U,ve=w-1,de=_o(U,h,ve);let ce=it(de,h.offsetTree,N)+O;ie==="end"?(ce+=Y+we(h.sizeTree,de)[1]-d+E,de===ve&&(ce+=k)):ie==="center"?ce+=(Y+we(h.sizeTree,de)[1]-d+E)/2:ce-=z,xe&&(ce+=xe);const Pe=Ie=>{y(),Ie?(P("retrying to scroll to",{location:R},ae.DEBUG),F(v,R)):(F(C,!0),P("list did not change, scroll successful",{},ae.DEBUG))};if(y(),fe==="smooth"){let Ie=!1;u=q(t,Xe=>{Ie=Ie||Xe}),g=Te(f,()=>{Pe(Ie)})}else g=Te(I(t,qn(150)),Pe);return m=setTimeout(()=>{y()},1200),F(c,!0),P("scrolling from index to",{behavior:fe,index:de,top:ce},ae.DEBUG),{behavior:fe,top:ce}})),a),{scrollTargetReached:C,scrollToIndex:v,topListHeight:x}},J(Re,ue,Me),{singleton:!0});function qn(e){return t=>{const o=setTimeout(()=>{t(!1)},e);return n=>{n&&(t(!0),clearTimeout(o))}}}function $t(e,t){e==0?t():requestAnimationFrame(()=>{$t(e-1,t)})}function Ut(e,t){const o=t-1;return typeof e=="number"?e:e.index==="LAST"?o:e.index}const ct=$(([{defaultItemSize:e,listRefresh:t,sizes:o},{scrollTop:n},{scrollTargetReached:r,scrollToIndex:i},{didMount:l}])=>{const s=S(!0),c=S(0),a=S(!0);return L(I(l,D(c),W(([f,T])=>!!T),be(!1)),s),L(I(l,D(c),W(([f,T])=>!!T),be(!1)),a),q(I(ne(t,l),D(s,o,e,a),W(([[,f],T,{sizeTree:p},v,C])=>f&&(!K(p)||Vt(v))&&!T&&!C),D(c)),([,f])=>{Te(r,()=>{F(a,!0)}),$t(4,()=>{Te(n,()=>{F(s,!0)}),F(i,f)})}),{initialItemFinalLocationReached:a,initialTopMostItemIndex:c,scrolledToInitialItem:s}},J(Re,ue,st,je),{singleton:!0});function $o(e,t){return Math.abs(e-t)<1.01}const lt="up",Qe="down",Kn="none",Yn={atBottom:!1,notAtBottomBecause:"NOT_SHOWING_LAST_ITEM",state:{offsetBottom:0,scrollHeight:0,scrollTop:0,viewportHeight:0}},Xn=0,at=$(([{footerHeight:e,headerHeight:t,scrollBy:o,scrollContainerState:n,scrollTop:r,viewportHeight:i}])=>{const l=S(!1),s=S(!0),c=G(),a=G(),f=S(4),T=S(Xn),p=se(I(Lt(I(j(r),De(1),be(!0)),I(j(r),De(1),be(!1),mo(100))),Z()),!1),v=se(I(Lt(I(o,be(!0)),I(o,be(!1),mo(200))),Z()),!1);L(I(ne(j(r),j(T)),B(([u,y])=>u<=y),Z()),s),L(I(s,ke(50)),a);const C=he(I(ne(n,j(i),j(t),j(e),j(f)),He((u,[{scrollHeight:y,scrollTop:R},h,d,w,z])=>{const O=R+h-y>-z,k={scrollHeight:y,scrollTop:R,viewportHeight:h};if(O){let N,Y;return R>u.state.scrollTop?(N="SCROLLED_DOWN",Y=u.state.scrollTop-R):(N="SIZE_DECREASED",Y=u.state.scrollTop-R||u.scrollTopDelta),{atBottom:!0,atBottomBecause:N,scrollTopDelta:Y,state:k}}let P;return k.scrollHeight>u.state.scrollHeight?P="SIZE_INCREASED":h<u.state.viewportHeight?P="VIEWPORT_HEIGHT_DECREASING":R<u.state.scrollTop?P="SCROLLING_UPWARDS":P="NOT_FULLY_SCROLLED_TO_LAST_ITEM_BOTTOM",{atBottom:!1,notAtBottomBecause:P,state:k}},Yn),Z((u,y)=>u&&u.atBottom===y.atBottom))),x=se(I(n,He((u,{scrollHeight:y,scrollTop:R,viewportHeight:h})=>{if($o(u.scrollHeight,y))return{changed:!1,jump:0,scrollHeight:y,scrollTop:R};{const d=y-(R+h)<1;return u.scrollTop!==R&&d?{changed:!0,jump:u.scrollTop-R,scrollHeight:y,scrollTop:R}:{changed:!0,jump:0,scrollHeight:y,scrollTop:R}}},{changed:!1,jump:0,scrollHeight:0,scrollTop:0}),W(u=>u.changed),B(u=>u.jump)),0);L(I(C,B(u=>u.atBottom)),l),L(I(l,ke(50)),c);const g=S(Qe);L(I(n,B(({scrollTop:u})=>u),Z(),He((u,y)=>oe(v)?{direction:u.direction,prevScrollTop:y}:{direction:y<u.prevScrollTop?lt:Qe,prevScrollTop:y},{direction:Qe,prevScrollTop:0}),B(u=>u.direction)),g),L(I(n,ke(50),be(Kn)),g);const m=S(0);return L(I(p,W(u=>!u),be(0)),m),L(I(r,ke(100),D(p),W(([u,y])=>!!y),He(([u,y],[R])=>[y,R],[0,0]),B(([u,y])=>y-u)),m),{atBottomState:C,atBottomStateChange:c,atBottomThreshold:f,atTopStateChange:a,atTopThreshold:T,isAtBottom:l,isAtTop:s,isScrolling:p,lastJumpDueToItemResize:x,scrollDirection:g,scrollVelocity:m}},J(ue)),It="top",Tt="bottom",po="none";function xo(e,t,o){return typeof e=="number"?o===lt&&t===It||o===Qe&&t===Tt?e:0:o===lt?t===It?e.main:e.reverse:t===Tt?e.main:e.reverse}function vo(e,t){var o;return typeof e=="number"?e:(o=e[t])!=null?o:0}const qt=$(([{deviation:e,fixedHeaderHeight:t,headerHeight:o,scrollTop:n,viewportHeight:r}])=>{const i=G(),l=S(0),s=S(0),c=S(0),a=se(I(ne(j(n),j(r),j(o),j(i,rt),j(c),j(l),j(t),j(e),j(s)),B(([f,T,p,[v,C],x,g,m,u,y])=>{const R=f-u,h=g+m,d=Math.max(p-R,0);let w=po;const z=vo(y,It),O=vo(y,Tt);return v-=u,v+=p+m,C+=p+m,C-=u,v>f+h-z&&(w=lt),C<f-d+T+O&&(w=Qe),w!==po?[Math.max(R-p-xo(x,It,w)-z,0),R-d-m+T+xo(x,Tt,w)+O]:null}),W(f=>f!=null),Z(rt)),[0,0]);return{increaseViewportBy:s,listBoundary:i,overscan:c,topListHeight:l,visibleRange:a}},J(ue),{singleton:!0});function Zn(e,t,o){if(bt(t)){const n=Do(e,t);return[{index:we(t.groupOffsetTree,n)[0],offset:0,size:0},{data:o==null?void 0:o[0],index:n,offset:0,size:0}]}return[{data:o==null?void 0:o[0],index:e,offset:0,size:0}]}const Rt={bottom:0,firstItemIndex:0,items:[],offsetBottom:0,offsetTop:0,top:0,topItems:[],topListHeight:0,totalCount:0};function xt(e,t,o,n,r,i){const{lastIndex:l,lastOffset:s,lastSize:c}=r;let a=0,f=0;if(e.length>0){a=e[0].offset;const x=e[e.length-1];f=x.offset+x.size}const T=o-l,p=s+T*c+(T-1)*n,v=a,C=p-f;return{bottom:f,firstItemIndex:i,items:Io(e,r,i),offsetBottom:C,offsetTop:a,top:v,topItems:Io(t,r,i),topListHeight:t.reduce((x,g)=>g.size+x,0),totalCount:o}}function Uo(e,t,o,n,r,i){let l=0;if(o.groupIndices.length>0)for(const f of o.groupIndices){if(f-l>=e)break;l++}const s=e+l,c=Ut(t,s),a=Array.from({length:s}).map((f,T)=>({data:i[T+c],index:T+c,offset:0,size:0}));return xt(a,[],s,r,o,n)}function Io(e,t,o){if(e.length===0)return[];if(!bt(t))return e.map(a=>({...a,index:a.index+o,originalIndex:a.index}));const n=e[0].index,r=e[e.length-1].index,i=[],l=yt(t.groupOffsetTree,n,r);let s,c=0;for(const a of e){(!s||s.end<a.index)&&(s=l.shift(),c=t.groupIndices.indexOf(s.start));let f;a.index===s.start?f={index:c,type:"group"}:f={groupIndex:c,index:a.index-(c+1)+o},i.push({...f,data:a.data,offset:a.offset,originalIndex:a.index,size:a.size})}return i}const _e=$(([{data:e,firstItemIndex:t,gap:o,sizes:n,totalCount:r},i,{listBoundary:l,topListHeight:s,visibleRange:c},{initialTopMostItemIndex:a,scrolledToInitialItem:f},{topListHeight:T},p,{didMount:v},{recalcInProgress:C}])=>{const x=S([]),g=S(0),m=G();L(i.topItemsIndexes,x);const u=se(I(ne(v,C,j(c,rt),j(r),j(n),j(a),f,j(x),j(t),j(o),e),W(([d,w,,z,,,,,,,O])=>{const k=O&&O.length!==z;return d&&!w&&!k}),B(([,,[d,w],z,O,k,P,N,Y,E,U])=>{const ie=O,{offsetTree:fe,sizeTree:xe}=ie,ve=oe(g);if(z===0)return{...Rt,totalCount:z};if(d===0&&w===0)return ve===0?{...Rt,totalCount:z}:Uo(ve,k,O,Y,E,U||[]);if(K(xe))return ve>0?null:xt(Zn(Ut(k,z),ie,U),[],z,E,ie,Y);const de=[];if(N.length>0){const Ge=N[0],ye=N[N.length-1];let Be=0;for(const b of yt(xe,Ge,ye)){const _=b.value,Q=Math.max(b.start,Ge),le=Math.min(b.end,ye);for(let te=Q;te<=le;te++)de.push({data:U==null?void 0:U[te],index:te,offset:Be,size:_}),Be+=_}}if(!P)return xt([],de,z,E,ie,Y);const ce=N.length>0?N[N.length-1]+1:0,Pe=Vn(fe,d,w,ce);if(Pe.length===0)return null;const Ie=z-1,Xe=Ct([],Ge=>{for(const ye of Pe){const Be=ye.value;let b=Be.offset,_=ye.start;const Q=Be.size;if(Be.offset<d){_+=Math.floor((d-Be.offset+E)/(Q+E));const te=_-ye.start;b+=te*Q+te*E}_<ce&&(b+=(ce-_)*Q,_=ce);const le=Math.min(ye.end,Ie);for(let te=_;te<=le&&!(b>=w);te++)Ge.push({data:U==null?void 0:U[te],index:te,offset:b,size:Q}),b+=Q+E}});return xt(Xe,de,z,E,ie,Y)}),W(d=>d!==null),Z()),Rt);L(I(e,W(Vt),B(d=>d==null?void 0:d.length)),r),L(I(u,B(d=>d.topListHeight)),T),L(T,s),L(I(u,B(d=>[d.top,d.bottom])),l),L(I(u,B(d=>d.items)),m);const y=he(I(u,W(({items:d})=>d.length>0),D(r,e),W(([{items:d},w])=>d[d.length-1].originalIndex===w-1),B(([,d,w])=>[d-1,w]),Z(rt),B(([d])=>d))),R=he(I(u,ke(200),W(({items:d,topItems:w})=>d.length>0&&d[0].originalIndex===w.length),B(({items:d})=>d[0].index),Z())),h=he(I(u,W(({items:d})=>d.length>0),B(({items:d})=>{let w=0,z=d.length-1;for(;d[w].type==="group"&&w<z;)w++;for(;d[z].type==="group"&&z>w;)z--;return{endIndex:d[z].index,startIndex:d[w].index}}),Z(Vo)));return{endReached:y,initialItemCount:g,itemsRendered:m,listState:u,rangeChanged:h,startReached:R,topItemsIndexes:x,...p}},J(Re,Go,qt,ct,st,at,je,Gt),{singleton:!0}),qo=$(([{fixedFooterHeight:e,fixedHeaderHeight:t,footerHeight:o,headerHeight:n},{listState:r}])=>{const i=G(),l=se(I(ne(o,e,n,t,r),B(([s,c,a,f,T])=>s+c+a+f+T.offsetBottom+T.bottom)),0);return L(j(l),i),{totalListHeight:l,totalListHeightChanged:i}},J(ue,_e),{singleton:!0}),Jn=$(([{viewportHeight:e},{totalListHeight:t}])=>{const o=S(!1),n=se(I(ne(o,e,t),W(([r])=>r),B(([,r,i])=>Math.max(0,r-i)),ke(0),Z()),0);return{alignToBottom:o,paddingTopAddition:n}},J(ue,qo),{singleton:!0}),Ko=$(()=>({context:S(null)})),Qn=({itemBottom:e,itemTop:t,locationParams:{align:o,behavior:n,...r},viewportBottom:i,viewportTop:l})=>t<l?{...r,align:o??"start",behavior:n}:e>i?{...r,align:o??"end",behavior:n}:null,Yo=$(([{gap:e,sizes:t,totalCount:o},{fixedFooterHeight:n,fixedHeaderHeight:r,headerHeight:i,scrollingInProgress:l,scrollTop:s,viewportHeight:c},{scrollToIndex:a}])=>{const f=G();return L(I(f,D(t,c,o,i,r,n,s),D(e),B(([[T,p,v,C,x,g,m,u],y])=>{const{align:R,behavior:h,calculateViewLocation:d=Qn,done:w,...z}=T,O=_o(T,p,C-1),k=it(O,p.offsetTree,y)+x+g,P=k+we(p.sizeTree,O)[1],N=u+g,Y=u+v-m,E=d({itemBottom:P,itemTop:k,locationParams:{align:R,behavior:h,...z},viewportBottom:Y,viewportTop:N});return E?w&&Te(I(l,W(U=>!U),De(oe(l)?1:2)),w):w&&w(),E}),W(T=>T!==null)),a),{scrollIntoView:f}},J(Re,ue,st,_e,Me),{singleton:!0});function To(e){return e?e==="smooth"?"smooth":"auto":!1}const er=(e,t)=>typeof e=="function"?To(e(t)):t&&To(e),tr=$(([{listRefresh:e,totalCount:t,fixedItemSize:o,data:n},{atBottomState:r,isAtBottom:i},{scrollToIndex:l},{scrolledToInitialItem:s},{didMount:c,propsReady:a},{log:f},{scrollingInProgress:T},{context:p},{scrollIntoView:v}])=>{const C=S(!1),x=G();let g=null;function m(h){F(l,{align:"end",behavior:h,index:"LAST"})}q(I(ne(I(j(t),De(1)),c),D(j(C),i,s,T),B(([[h,d],w,z,O,k])=>{let P=d&&O,N="auto";return P&&(N=er(w,z||k),P=P&&!!N),{followOutputBehavior:N,shouldFollow:P,totalCount:h}}),W(({shouldFollow:h})=>h)),({followOutputBehavior:h,totalCount:d})=>{g&&(g(),g=null),oe(o)?requestAnimationFrame(()=>{oe(f)("following output to ",{totalCount:d},ae.DEBUG),m(h)}):g=Te(e,()=>{oe(f)("following output to ",{totalCount:d},ae.DEBUG),m(h),g=null})});function u(h){const d=Te(r,w=>{h&&!w.atBottom&&w.notAtBottomBecause==="SIZE_INCREASED"&&!g&&(oe(f)("scrolling to bottom due to increased size",{},ae.DEBUG),m("auto"))});setTimeout(d,100)}q(I(ne(j(C),t,a),W(([h,,d])=>h&&d),He(({value:h},[,d])=>({refreshed:h===d,value:d}),{refreshed:!1,value:0}),W(({refreshed:h})=>h),D(C,t)),([,h])=>{oe(s)&&u(h!==!1)}),q(x,()=>{u(oe(C)!==!1)}),q(ne(j(C),r),([h,d])=>{h&&!d.atBottom&&d.notAtBottomBecause==="VIEWPORT_HEIGHT_DECREASING"&&m("auto")});const y=S(null),R=G();return L(Lt(I(j(n),B(h=>{var d;return(d=h==null?void 0:h.length)!=null?d:0})),I(j(t))),R),q(I(ne(I(R,De(1)),c),D(j(y),s,T,p),B(([[h,d],w,z,O,k])=>d&&z&&(w==null?void 0:w({context:k,totalCount:h,scrollingInProgress:O}))),W(h=>!!h),ke(0)),h=>{g&&(g(),g=null),oe(o)?requestAnimationFrame(()=>{oe(f)("scrolling into view",{}),F(v,h)}):g=Te(e,()=>{oe(f)("scrolling into view",{}),F(v,h),g=null})}),{autoscrollToBottom:x,followOutput:C,scrollIntoViewOnChange:y}},J(Re,at,st,ct,je,Me,ue,Ko,Yo)),or=$(([{data:e,firstItemIndex:t,gap:o,sizes:n},{initialTopMostItemIndex:r},{initialItemCount:i,listState:l},{didMount:s}])=>(L(I(s,D(i),W(([,c])=>c!==0),D(r,n,t,o,e),B(([[,c],a,f,T,p,v=[]])=>Uo(c,a,f,T,p,v))),l),{}),J(Re,ct,_e,je),{singleton:!0}),nr=$(([{didMount:e},{scrollTo:t},{listState:o}])=>{const n=S(0);return q(I(e,D(n),W(([,r])=>r!==0),B(([,r])=>({top:r}))),r=>{Te(I(o,De(1),W(i=>i.items.length>1)),()=>{requestAnimationFrame(()=>{F(t,r)})})}),{initialScrollTop:n}},J(je,ue,_e),{singleton:!0}),Xo=$(([{scrollVelocity:e}])=>{const t=S(!1),o=G(),n=S(!1);return L(I(e,D(n,t,o),W(([r,i])=>!!i),B(([r,i,l,s])=>{const{enter:c,exit:a}=i;if(l){if(a(r,s))return!1}else if(c(r,s))return!0;return l}),Z()),t),q(I(ne(t,e,o),D(n)),([[r,i,l],s])=>{r&&s&&s.change&&s.change(i,l)}),{isSeeking:t,scrollSeekConfiguration:n,scrollSeekRangeChanged:o,scrollVelocity:e}},J(at),{singleton:!0}),Kt=$(([{scrollContainerState:e,scrollTo:t}])=>{const o=G(),n=G(),r=G(),i=S(!1),l=S(void 0);return L(I(ne(o,n),B(([{scrollHeight:s,scrollTop:c,viewportHeight:a},{offsetTop:f}])=>({scrollHeight:s,scrollTop:Math.max(0,c-f),viewportHeight:a}))),e),L(I(t,D(n),B(([s,{offsetTop:c}])=>({...s,top:s.top+c}))),r),{customScrollParent:l,useWindowScroll:i,windowScrollContainerState:o,windowScrollTo:r,windowViewportRect:n}},J(ue)),rr=$(([{sizeRanges:e,sizes:t},{headerHeight:o,scrollTop:n},{initialTopMostItemIndex:r},{didMount:i},{useWindowScroll:l,windowScrollContainerState:s,windowViewportRect:c}])=>{const a=G(),f=S(void 0),T=S(null),p=S(null);return L(s,T),L(c,p),q(I(a,D(t,n,l,T,p,o)),([v,C,x,g,m,u,y])=>{const R=Dn(C.sizeTree);g&&m!==null&&u!==null&&(x=m.scrollTop-u.offsetTop),x-=y,v({ranges:R,scrollTop:x})}),L(I(f,W(Vt),B(ir)),r),L(I(i,D(f),W(([,v])=>v!==void 0),Z(),B(([,v])=>v.ranges)),e),{getState:a,restoreStateFrom:f}},J(Re,ue,ct,je,Kt));function ir(e){return{align:"start",index:0,offset:e.scrollTop}}const lr=$(([{topItemsIndexes:e}])=>{const t=S(0);return L(I(t,W(o=>o>=0),B(o=>Array.from({length:o}).map((n,r)=>r))),e),{topItemCount:t}},J(_e));function Zo(e){let t=!1,o;return()=>(t||(t=!0,o=e()),o)}const sr=Zo(()=>/iP(ad|od|hone)/i.test(navigator.userAgent)&&/WebKit/i.test(navigator.userAgent)),cr=$(([{deviation:e,scrollBy:t,scrollingInProgress:o,scrollTop:n},{isAtBottom:r,isScrolling:i,lastJumpDueToItemResize:l,scrollDirection:s},{listState:c},{beforeUnshiftWith:a,gap:f,shiftWithOffset:T,sizes:p},{log:v},{recalcInProgress:C}])=>{const x=he(I(c,D(l),He(([,m,u,y],[{bottom:R,items:h,offsetBottom:d,totalCount:w},z])=>{const O=R+d;let k=0;return u===w&&m.length>0&&h.length>0&&(h[0].originalIndex===0&&m[0].originalIndex===0||(k=O-y,k!==0&&(k+=z))),[k,h,w,O]},[0,[],0,0]),W(([m])=>m!==0),D(n,s,o,r,v,C),W(([,m,u,y,,,R])=>!R&&!y&&m!==0&&u===lt),B(([[m],,,,,u])=>(u("Upward scrolling compensation",{amount:m},ae.DEBUG),m))));function g(m){m>0?(F(t,{behavior:"auto",top:-m}),F(e,0)):(F(e,0),F(t,{behavior:"auto",top:-m}))}return q(I(x,D(e,i)),([m,u,y])=>{y&&sr()?F(e,u-m):g(-m)}),q(I(ne(se(i,!1),e,C),W(([m,u,y])=>!m&&!y&&u!==0),B(([m,u])=>u),ke(1)),g),L(I(T,B(m=>({top:-m}))),t),q(I(a,D(p,f),B(([m,{groupIndices:u,lastSize:y,sizeTree:R},h])=>{function d(w){return w*(y+h)}if(u.length===0)return d(m);{let w=0;const z=nt(R,0);let O=0,k=0;for(;O<m;){O++,w+=z;let P=u.length===k+1?1/0:u[k+1]-u[k]-1;O+P>m&&(w-=z,P=m-O+1),O+=P,w+=d(P),k++}return w}})),m=>{F(e,m),requestAnimationFrame(()=>{F(t,{top:m}),requestAnimationFrame(()=>{F(e,0),F(C,!1)})})}),{deviation:e}},J(ue,at,_e,Re,Me,Gt)),ar=$(([e,t,o,n,r,i,l,s,c,a,f])=>({...e,...t,...o,...n,...r,...i,...l,...s,...c,...a,...f}),J(qt,or,je,Xo,qo,nr,Jn,Kt,Yo,Me,Ko)),Jo=$(([{data:e,defaultItemSize:t,firstItemIndex:o,fixedItemSize:n,gap:r,groupIndices:i,itemSize:l,sizeRanges:s,sizes:c,statefulTotalCount:a,totalCount:f,trackItemSizes:T},{initialItemFinalLocationReached:p,initialTopMostItemIndex:v,scrolledToInitialItem:C},x,g,m,{listState:u,topItemsIndexes:y,...R},{scrollToIndex:h},d,{topItemCount:w},{groupCounts:z},O])=>(L(R.rangeChanged,O.scrollSeekRangeChanged),L(I(O.windowViewportRect,B(k=>k.visibleHeight)),x.viewportHeight),{data:e,defaultItemHeight:t,firstItemIndex:o,fixedItemHeight:n,gap:r,groupCounts:z,initialItemFinalLocationReached:p,initialTopMostItemIndex:v,scrolledToInitialItem:C,sizeRanges:s,topItemCount:w,topItemsIndexes:y,totalCount:f,...m,groupIndices:i,itemSize:l,listState:u,scrollToIndex:h,statefulTotalCount:a,trackItemSizes:T,...R,...O,...x,sizes:c,...g}),J(Re,ct,ue,rr,tr,_e,st,cr,lr,Go,ar));function ur(e,t){const o={},n={};let r=0;const i=e.length;for(;r<i;)n[e[r]]=1,r+=1;for(const l in t)Object.hasOwn(n,l)||(o[l]=t[l]);return o}const ft=typeof document<"u"?H.useLayoutEffect:H.useEffect;function Yt(e,t,o){const n=Object.keys(t.required||{}),r=Object.keys(t.optional||{}),i=Object.keys(t.methods||{}),l=Object.keys(t.events||{}),s=H.createContext({});function c(g,m){g.propsReady&&F(g.propsReady,!1);for(const u of n){const y=g[t.required[u]];F(y,m[u])}for(const u of r)if(u in m){const y=g[t.optional[u]];F(y,m[u])}g.propsReady&&F(g.propsReady,!0)}function a(g){return i.reduce((m,u)=>(m[u]=y=>{const R=g[t.methods[u]];F(R,y)},m),{})}function f(g){return l.reduce((m,u)=>(m[u]=zn(g[t.events[u]]),m),{})}const T=H.forwardRef((g,m)=>{const{children:u,...y}=g,[R]=H.useState(()=>Ct(Bn(e),w=>{c(w,y)})),[h]=H.useState(uo(f,R));ft(()=>{for(const w of l)w in y&&q(h[w],y[w]);return()=>{Object.values(h).map(Ft)}},[y,h,R]),ft(()=>{c(R,y)}),H.useImperativeHandle(m,ao(a(R)));const d=o;return M.jsx(s.Provider,{value:R,children:o?M.jsx(d,{...ur([...n,...r,...l],y),children:u}):u})}),p=g=>{const m=H.useContext(s);return H.useCallback(u=>{F(m[g],u)},[m,g])},v=g=>{const m=H.useContext(s)[g],u=H.useCallback(y=>q(m,y),[m]);return H.useSyncExternalStore(u,()=>oe(m),()=>oe(m))},C=g=>{const m=H.useContext(s)[g],[u,y]=H.useState(uo(oe,m));return ft(()=>q(m,R=>{R!==u&&y(ao(R))}),[m,u]),u},x=H.version.startsWith("18")?v:C;return{Component:T,useEmitter:(g,m)=>{const u=H.useContext(s)[g];ft(()=>q(u,m),[m,u])},useEmitterValue:x,usePublisher:p}}const ut=H.createContext(void 0),Xt=H.createContext(void 0),Qo=typeof document<"u"?H.useLayoutEffect:H.useEffect;function Bt(e){return"self"in e}function dr(e){return"body"in e}function en(e,t,o,n=Ke,r,i){const l=H.useRef(null),s=H.useRef(null),c=H.useRef(null),a=H.useCallback(p=>{let v,C,x;const g=p.target;if(dr(g)||Bt(g)){const u=Bt(g)?g:g.defaultView;x=i?u.scrollX:u.scrollY,v=i?u.document.documentElement.scrollWidth:u.document.documentElement.scrollHeight,C=i?u.innerWidth:u.innerHeight}else x=i?g.scrollLeft:g.scrollTop,v=i?g.scrollWidth:g.scrollHeight,C=i?g.offsetWidth:g.offsetHeight;const m=()=>{e({scrollHeight:v,scrollTop:Math.max(x,0),viewportHeight:C})};p.suppressFlushSync?m():vn.flushSync(m),s.current!==null&&(x===s.current||x<=0||x===v-C)&&(s.current=null,t(!0),c.current&&(clearTimeout(c.current),c.current=null))},[e,t,i]);H.useEffect(()=>{const p=r||l.current;return n(r||l.current),a({suppressFlushSync:!0,target:p}),p.addEventListener("scroll",a,{passive:!0}),()=>{n(null),p.removeEventListener("scroll",a)}},[l,a,o,n,r]);function f(p){const v=l.current;if(!v||(i?"offsetWidth"in v&&v.offsetWidth===0:"offsetHeight"in v&&v.offsetHeight===0))return;const C=p.behavior==="smooth";let x,g,m;Bt(v)?(g=Math.max(Ce(v.document.documentElement,i?"width":"height"),i?v.document.documentElement.scrollWidth:v.document.documentElement.scrollHeight),x=i?v.innerWidth:v.innerHeight,m=i?window.scrollX:window.scrollY):(g=v[i?"scrollWidth":"scrollHeight"],x=Ce(v,i?"width":"height"),m=v[i?"scrollLeft":"scrollTop"]);const u=g-x;if(p.top=Math.ceil(Math.max(Math.min(u,p.top),0)),$o(x,g)||p.top===m){e({scrollHeight:g,scrollTop:m,viewportHeight:x}),C&&t(!0);return}C?(s.current=p.top,c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{c.current=null,s.current=null,t(!0)},1e3)):s.current=null,i&&(p={behavior:p.behavior,left:p.top}),v.scrollTo(p)}function T(p){i&&(p={behavior:p.behavior,left:p.top}),l.current.scrollBy(p)}return{scrollByCallback:T,scrollerRef:l,scrollToCallback:f}}const kt="-webkit-sticky",So="sticky",Zt=Zo(()=>{if(typeof document>"u")return So;const e=document.createElement("div");return e.style.position=kt,e.style.position===kt?kt:So});function Jt(e){return e}const mr=$(()=>{const e=S(s=>`Item ${s}`),t=S(s=>`Group ${s}`),o=S({}),n=S(Jt),r=S("div"),i=S(Ke),l=(s,c=null)=>se(I(o,B(a=>a[s]),Z()),c);return{components:o,computeItemKey:n,EmptyPlaceholder:l("EmptyPlaceholder"),FooterComponent:l("Footer"),GroupComponent:l("Group","div"),groupContent:t,HeaderComponent:l("Header"),HeaderFooterTag:r,ItemComponent:l("Item","div"),itemContent:e,ListComponent:l("List","div"),ScrollerComponent:l("Scroller","div"),scrollerRef:i,ScrollSeekPlaceholder:l("ScrollSeekPlaceholder"),TopItemListComponent:l("TopItemList")}}),hr=$(([e,t])=>({...e,...t}),J(Jo,mr)),fr=({height:e})=>M.jsx("div",{style:{height:e}}),gr={overflowAnchor:"none",position:Zt(),zIndex:1},tn={overflowAnchor:"none"},pr={...tn,display:"inline-block",height:"100%"},wo=H.memo(function({showTopList:e=!1}){const t=A("listState"),o=ge("sizeRanges"),n=A("useWindowScroll"),r=A("customScrollParent"),i=ge("windowScrollContainerState"),l=ge("scrollContainerState"),s=r||n?i:l,c=A("itemContent"),a=A("context"),f=A("groupContent"),T=A("trackItemSizes"),p=A("itemSize"),v=A("log"),C=ge("gap"),x=A("horizontalDirection"),{callbackRef:g}=Oo(o,p,T,e?Ke:s,v,C,r,x,A("skipAnimationFrameInResizeObserver")),[m,u]=H.useState(0);to("deviation",E=>{m!==E&&u(E)});const y=A("EmptyPlaceholder"),R=A("ScrollSeekPlaceholder")||fr,h=A("ListComponent"),d=A("ItemComponent"),w=A("GroupComponent"),z=A("computeItemKey"),O=A("isSeeking"),k=A("groupIndices").length>0,P=A("alignToBottom"),N=A("initialItemFinalLocationReached"),Y=e?{}:{boxSizing:"border-box",...x?{display:"inline-block",height:"100%",marginLeft:m!==0?m:P?"auto":0,paddingLeft:t.offsetTop,paddingRight:t.offsetBottom,whiteSpace:"nowrap"}:{marginTop:m!==0?m:P?"auto":0,paddingBottom:t.offsetBottom,paddingTop:t.offsetTop},...N?{}:{visibility:"hidden"}};return!e&&t.totalCount===0&&y?M.jsx(y,{...X(y,a)}):M.jsx(h,{...X(h,a),"data-testid":e?"virtuoso-top-item-list":"virtuoso-item-list",ref:g,style:Y,children:(e?t.topItems:t.items).map(E=>{const U=E.originalIndex,ie=z(U+t.firstItemIndex,E.data,a);return O?Fe.createElement(R,{...X(R,a),height:E.size,index:E.index,key:ie,type:E.type||"item",...E.type==="group"?{}:{groupIndex:E.groupIndex}}):E.type==="group"?Fe.createElement(w,{...X(w,a),"data-index":U,"data-item-index":E.index,"data-known-size":E.size,key:ie,style:gr},f(E.index,a)):Fe.createElement(d,{...X(d,a),...on(d,E.data),"data-index":U,"data-item-group-index":E.groupIndex,"data-item-index":E.index,"data-known-size":E.size,key:ie,style:x?pr:tn},k?c(E.index,E.groupIndex,E.data,a):c(E.index,E.data,a))})})}),xr={height:"100%",outline:"none",overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},vr={outline:"none",overflowX:"auto",position:"relative"},Ye=e=>({height:"100%",position:"absolute",top:0,width:"100%",...e?{display:"flex",flexDirection:"column"}:{}}),Ir={position:Zt(),top:0,width:"100%",zIndex:1};function X(e,t){if(typeof e!="string")return{context:t}}function on(e,t){return{item:typeof e=="string"?void 0:t}}const Tr=H.memo(function(){const e=A("HeaderComponent"),t=ge("headerHeight"),o=A("HeaderFooterTag"),n=ze(H.useMemo(()=>i=>{t(Ce(i,"height"))},[t]),!0,A("skipAnimationFrameInResizeObserver")),r=A("context");return e?M.jsx(o,{ref:n,children:M.jsx(e,{...X(e,r)})}):null}),Sr=H.memo(function(){const e=A("FooterComponent"),t=ge("footerHeight"),o=A("HeaderFooterTag"),n=ze(H.useMemo(()=>i=>{t(Ce(i,"height"))},[t]),!0,A("skipAnimationFrameInResizeObserver")),r=A("context");return e?M.jsx(o,{ref:n,children:M.jsx(e,{...X(e,r)})}):null});function Qt({useEmitter:e,useEmitterValue:t,usePublisher:o}){return H.memo(function({children:n,style:r,...i}){const l=o("scrollContainerState"),s=t("ScrollerComponent"),c=o("smoothScrollTargetReached"),a=t("scrollerRef"),f=t("context"),T=t("horizontalDirection")||!1,{scrollByCallback:p,scrollerRef:v,scrollToCallback:C}=en(l,c,s,a,void 0,T);return e("scrollTo",C),e("scrollBy",p),M.jsx(s,{"data-testid":"virtuoso-scroller","data-virtuoso-scroller":!0,ref:v,style:{...T?vr:xr,...r},tabIndex:0,...i,...X(s,f),children:n})})}function eo({useEmitter:e,useEmitterValue:t,usePublisher:o}){return H.memo(function({children:n,style:r,...i}){const l=o("windowScrollContainerState"),s=t("ScrollerComponent"),c=o("smoothScrollTargetReached"),a=t("totalListHeight"),f=t("deviation"),T=t("customScrollParent"),p=t("context"),v=H.useRef(null),C=t("scrollerRef"),{scrollByCallback:x,scrollerRef:g,scrollToCallback:m}=en(l,c,s,C,T);return Qo(()=>{var u;return g.current=T||((u=v.current)==null?void 0:u.ownerDocument.defaultView),()=>{g.current=null}},[g,T]),e("windowScrollTo",m),e("scrollBy",x),M.jsx(s,{ref:v,"data-virtuoso-scroller":!0,style:{position:"relative",...r,...a!==0?{height:a+f}:{}},...i,...X(s,p),children:n})})}const wr=({children:e})=>{const t=H.useContext(ut),o=ge("viewportHeight"),n=ge("fixedItemHeight"),r=A("alignToBottom"),i=A("horizontalDirection"),l=H.useMemo(()=>tt(o,c=>Ce(c,i?"width":"height")),[o,i]),s=ze(l,!0,A("skipAnimationFrameInResizeObserver"));return H.useEffect(()=>{t&&(o(t.viewportHeight),n(t.itemHeight))},[t,o,n]),M.jsx("div",{"data-viewport-type":"element",ref:s,style:Ye(r),children:e})},Cr=({children:e})=>{const t=H.useContext(ut),o=ge("windowViewportRect"),n=ge("fixedItemHeight"),r=A("customScrollParent"),i=_t(o,r,A("skipAnimationFrameInResizeObserver")),l=A("alignToBottom");return H.useEffect(()=>{t&&(n(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))},[t,o,n]),M.jsx("div",{"data-viewport-type":"window",ref:i,style:Ye(l),children:e})},yr=({children:e})=>{const t=A("TopItemListComponent")||"div",o=A("headerHeight"),n={...Ir,marginTop:`${o}px`},r=A("context");return M.jsx(t,{style:n,...X(t,r),children:e})},br=H.memo(function(e){const t=A("useWindowScroll"),o=A("topItemsIndexes").length>0,n=A("customScrollParent"),r=A("context"),i=n||t?zr:Hr,l=n||t?Cr:wr;return M.jsxs(i,{...e,...X(i,r),children:[o&&M.jsx(yr,{children:M.jsx(wo,{showTopList:!0})}),M.jsxs(l,{children:[M.jsx(Tr,{}),M.jsx(wo,{}),M.jsx(Sr,{})]})]})}),{Component:nn,useEmitter:to,useEmitterValue:A,usePublisher:ge}=Yt(hr,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",scrollIntoViewOnChange:"scrollIntoViewOnChange",itemContent:"itemContent",groupContent:"groupContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",groupCounts:"groupCounts",topItemCount:"topItemCount",firstItemIndex:"firstItemIndex",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"HeaderFooterTag",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",horizontalDirection:"horizontalDirection",skipAnimationFrameInResizeObserver:"skipAnimationFrameInResizeObserver"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",autoscrollToBottom:"autoscrollToBottom",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},br),Hr=Qt({useEmitter:to,useEmitterValue:A,usePublisher:ge}),zr=eo({useEmitter:to,useEmitterValue:A,usePublisher:ge}),Rr=nn,Br=nn,kr=$(()=>{const e=S(a=>M.jsxs("td",{children:["Item $",a]})),t=S(null),o=S(a=>M.jsxs("td",{colSpan:1e3,children:["Group ",a]})),n=S(null),r=S(null),i=S({}),l=S(Jt),s=S(Ke),c=(a,f=null)=>se(I(i,B(T=>T[a]),Z()),f);return{components:i,computeItemKey:l,context:t,EmptyPlaceholder:c("EmptyPlaceholder"),FillerRow:c("FillerRow"),fixedFooterContent:r,fixedHeaderContent:n,itemContent:e,groupContent:o,ScrollerComponent:c("Scroller","div"),scrollerRef:s,ScrollSeekPlaceholder:c("ScrollSeekPlaceholder"),TableBodyComponent:c("TableBody","tbody"),TableComponent:c("Table","table"),TableFooterComponent:c("TableFoot","tfoot"),TableHeadComponent:c("TableHead","thead"),TableRowComponent:c("TableRow","tr"),GroupComponent:c("Group","tr")}}),Er=$(([e,t])=>({...e,...t}),J(Jo,kr)),Lr=({height:e})=>M.jsx("tr",{children:M.jsx("td",{style:{height:e}})}),Or=({height:e})=>M.jsx("tr",{children:M.jsx("td",{style:{border:0,height:e,padding:0}})}),Mr={overflowAnchor:"none"},Co={position:Zt(),zIndex:2,overflowAnchor:"none"},yo=H.memo(function({showTopList:e=!1}){const t=V("listState"),o=V("computeItemKey"),n=V("firstItemIndex"),r=V("context"),i=V("isSeeking"),l=V("fixedHeaderHeight"),s=V("groupIndices").length>0,c=V("itemContent"),a=V("groupContent"),f=V("ScrollSeekPlaceholder")||Lr,T=V("GroupComponent"),p=V("TableRowComponent"),v=(e?t.topItems:[]).reduce((x,g,m)=>(m===0?x.push(g.size):x.push(x[m-1]+g.size),x),[]),C=(e?t.topItems:t.items).map(x=>{const g=x.originalIndex,m=o(g+n,x.data,r),u=e?g===0?0:v[g-1]:0;return i?Fe.createElement(f,{...X(f,r),height:x.size,index:x.index,key:m,type:x.type||"item"}):x.type==="group"?Fe.createElement(T,{...X(T,r),"data-index":g,"data-item-index":x.index,"data-known-size":x.size,key:m,style:{...Co,top:l}},a(x.index,r)):Fe.createElement(p,{...X(p,r),...on(p,x.data),"data-index":g,"data-item-index":x.index,"data-known-size":x.size,"data-item-group-index":x.groupIndex,key:m,style:e?{...Co,top:l+u}:Mr},s?c(x.index,x.groupIndex,x.data,r):c(x.index,x.data,r))});return M.jsx(M.Fragment,{children:C})}),jr=H.memo(function(){const e=V("listState"),t=V("topItemsIndexes").length>0,o=Se("sizeRanges"),n=V("useWindowScroll"),r=V("customScrollParent"),i=Se("windowScrollContainerState"),l=Se("scrollContainerState"),s=r||n?i:l,c=V("trackItemSizes"),a=V("itemSize"),f=V("log"),{callbackRef:T,ref:p}=Oo(o,a,c,s,f,void 0,r,!1,V("skipAnimationFrameInResizeObserver")),[v,C]=H.useState(0);oo("deviation",k=>{v!==k&&(p.current.style.marginTop=`${k}px`,C(k))});const x=V("EmptyPlaceholder"),g=V("FillerRow")||Or,m=V("TableBodyComponent"),u=V("paddingTopAddition"),y=V("statefulTotalCount"),R=V("context");if(y===0&&x)return M.jsx(x,{...X(x,R)});const h=(t?e.topItems:[]).reduce((k,P)=>k+P.size,0),d=e.offsetTop+u+v-h,w=e.offsetBottom,z=d>0?M.jsx(g,{context:R,height:d},"padding-top"):null,O=w>0?M.jsx(g,{context:R,height:w},"padding-bottom"):null;return M.jsxs(m,{"data-testid":"virtuoso-item-list",ref:T,...X(m,R),children:[z,t&&M.jsx(yo,{showTopList:!0}),M.jsx(yo,{}),O]})}),Pr=({children:e})=>{const t=H.useContext(ut),o=Se("viewportHeight"),n=Se("fixedItemHeight"),r=ze(H.useMemo(()=>tt(o,i=>Ce(i,"height")),[o]),!0,V("skipAnimationFrameInResizeObserver"));return H.useEffect(()=>{t&&(o(t.viewportHeight),n(t.itemHeight))},[t,o,n]),M.jsx("div",{"data-viewport-type":"element",ref:r,style:Ye(!1),children:e})},Wr=({children:e})=>{const t=H.useContext(ut),o=Se("windowViewportRect"),n=Se("fixedItemHeight"),r=V("customScrollParent"),i=_t(o,r,V("skipAnimationFrameInResizeObserver"));return H.useEffect(()=>{t&&(n(t.itemHeight),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:100}))},[t,o,n]),M.jsx("div",{"data-viewport-type":"window",ref:i,style:Ye(!1),children:e})},Ar=H.memo(function(e){const t=V("useWindowScroll"),o=V("customScrollParent"),n=Se("fixedHeaderHeight"),r=Se("fixedFooterHeight"),i=V("fixedHeaderContent"),l=V("fixedFooterContent"),s=V("context"),c=ze(H.useMemo(()=>tt(n,m=>Ce(m,"height")),[n]),!0,V("skipAnimationFrameInResizeObserver")),a=ze(H.useMemo(()=>tt(r,m=>Ce(m,"height")),[r]),!0,V("skipAnimationFrameInResizeObserver")),f=o||t?Fr:Vr,T=o||t?Wr:Pr,p=V("TableComponent"),v=V("TableHeadComponent"),C=V("TableFooterComponent"),x=i?M.jsx(v,{ref:c,style:{position:"sticky",top:0,zIndex:2},...X(v,s),children:i()},"TableHead"):null,g=l?M.jsx(C,{ref:a,style:{bottom:0,position:"sticky",zIndex:1},...X(C,s),children:l()},"TableFoot"):null;return M.jsx(f,{...e,...X(f,s),children:M.jsx(T,{children:M.jsxs(p,{style:{borderSpacing:0,overflowAnchor:"none"},...X(p,s),children:[x,M.jsx(jr,{},"TableBody"),g]})})})}),{Component:rn,useEmitter:oo,useEmitterValue:V,usePublisher:Se}=Yt(Er,{required:{},optional:{restoreStateFrom:"restoreStateFrom",context:"context",followOutput:"followOutput",firstItemIndex:"firstItemIndex",itemContent:"itemContent",groupContent:"groupContent",fixedHeaderContent:"fixedHeaderContent",fixedFooterContent:"fixedFooterContent",overscan:"overscan",increaseViewportBy:"increaseViewportBy",totalCount:"totalCount",topItemCount:"topItemCount",initialTopMostItemIndex:"initialTopMostItemIndex",components:"components",groupCounts:"groupCounts",atBottomThreshold:"atBottomThreshold",atTopThreshold:"atTopThreshold",computeItemKey:"computeItemKey",defaultItemHeight:"defaultItemHeight",fixedItemHeight:"fixedItemHeight",itemSize:"itemSize",scrollSeekConfiguration:"scrollSeekConfiguration",data:"data",initialItemCount:"initialItemCount",initialScrollTop:"initialScrollTop",alignToBottom:"alignToBottom",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel"},methods:{scrollToIndex:"scrollToIndex",scrollIntoView:"scrollIntoView",scrollTo:"scrollTo",scrollBy:"scrollBy",getState:"getState"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",totalListHeightChanged:"totalListHeightChanged",itemsRendered:"itemsRendered",groupIndices:"groupIndices"}},Ar),Vr=Qt({useEmitter:oo,useEmitterValue:V,usePublisher:Se}),Fr=eo({useEmitter:oo,useEmitterValue:V,usePublisher:Se}),Dr=rn,_r=rn,bo={bottom:0,itemHeight:0,items:[],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},Gr={bottom:0,itemHeight:0,items:[{index:0}],itemWidth:0,offsetBottom:0,offsetTop:0,top:0},{ceil:Ho,floor:St,max:et,min:Et,round:zo}=Math;function Ro(e,t,o){return Array.from({length:t-e+1}).map((n,r)=>({data:o===null?null:o[r+e],index:r+e}))}function Nr(e){return{...Gr,items:e}}function gt(e,t){return e&&e.width===t.width&&e.height===t.height}function $r(e,t){return e&&e.column===t.column&&e.row===t.row}const Ur=$(([{increaseViewportBy:e,listBoundary:t,overscan:o,visibleRange:n},{footerHeight:r,headerHeight:i,scrollBy:l,scrollContainerState:s,scrollTo:c,scrollTop:a,smoothScrollTargetReached:f,viewportHeight:T},p,v,{didMount:C,propsReady:x},{customScrollParent:g,useWindowScroll:m,windowScrollContainerState:u,windowScrollTo:y,windowViewportRect:R},h])=>{const d=S(0),w=S(0),z=S(bo),O=S({height:0,width:0}),k=S({height:0,width:0}),P=G(),N=G(),Y=S(0),E=S(null),U=S({column:0,row:0}),ie=G(),fe=G(),xe=S(!1),ve=S(0),de=S(!0),ce=S(!1),Pe=S(!1);q(I(C,D(ve),W(([b,_])=>!!_)),()=>{F(de,!1)}),q(I(ne(C,de,k,O,ve,ce),W(([b,_,Q,le,,te])=>b&&!_&&Q.height!==0&&le.height!==0&&!te)),([,,,,b])=>{F(ce,!0),$t(1,()=>{F(P,b)}),Te(I(a),()=>{F(t,[0,0]),F(de,!0)})}),L(I(fe,W(b=>b!=null&&b.scrollTop>0),be(0)),w),q(I(C,D(fe),W(([,b])=>b!=null)),([,b])=>{b&&(F(O,b.viewport),F(k,b.item),F(U,b.gap),b.scrollTop>0&&(F(xe,!0),Te(I(a,De(1)),_=>{F(xe,!1)}),F(c,{top:b.scrollTop})))}),L(I(O,B(({height:b})=>b)),T),L(I(ne(j(O,gt),j(k,gt),j(U,(b,_)=>b&&b.column===_.column&&b.row===_.row),j(a)),B(([b,_,Q,le])=>({gap:Q,item:_,scrollTop:le,viewport:b}))),ie),L(I(ne(j(d),n,j(U,$r),j(k,gt),j(O,gt),j(E),j(w),j(xe),j(de),j(ve)),W(([,,,,,,,b])=>!b),B(([b,[_,Q],le,te,We,Ze,Ne,,dt,Ee])=>{const{column:Le,row:Je}=le,{height:mt,width:Ht}=te,{width:no}=We;if(Ne===0&&(b===0||no===0))return bo;if(Ht===0){const co=Ut(Ee,b),an=co+Math.max(Ne-1,0);return Nr(Ro(co,an,Ze))}const ht=ln(no,Ht,Le);let $e,Ae;dt?_===0&&Q===0&&Ne>0?($e=0,Ae=Ne-1):($e=ht*St((_+Je)/(mt+Je)),Ae=ht*Ho((Q+Je)/(mt+Je))-1,Ae=Et(b-1,et(Ae,ht-1)),$e=Et(Ae,et(0,$e))):($e=0,Ae=-1);const ro=Ro($e,Ae,Ze),{bottom:io,top:lo}=Bo(We,le,te,ro),so=Ho(b/ht),cn=so*mt+(so-1)*Je-io;return{bottom:io,itemHeight:mt,items:ro,itemWidth:Ht,offsetBottom:cn,offsetTop:lo,top:lo}})),z),L(I(E,W(b=>b!==null),B(b=>b.length)),d),L(I(ne(O,k,z,U),W(([b,_,{items:Q}])=>Q.length>0&&_.height!==0&&b.height!==0),B(([b,_,{items:Q},le])=>{const{bottom:te,top:We}=Bo(b,le,_,Q);return[We,te]}),Z(rt)),t);const Ie=S(!1);L(I(a,D(Ie),B(([b,_])=>_||b!==0)),Ie);const Xe=he(I(ne(z,d),W(([{items:b}])=>b.length>0),D(Ie),W(([[b,_],Q])=>{const le=b.items[b.items.length-1].index===_-1;return(Q||b.bottom>0&&b.itemHeight>0&&b.offsetBottom===0&&b.items.length===_)&&le}),B(([[,b]])=>b-1),Z())),Ge=he(I(j(z),W(({items:b})=>b.length>0&&b[0].index===0),be(0),Z())),ye=he(I(j(z),D(xe),W(([{items:b},_])=>b.length>0&&!_),B(([{items:b}])=>({endIndex:b[b.length-1].index,startIndex:b[0].index})),Z(Vo),ke(0)));L(ye,v.scrollSeekRangeChanged),L(I(P,D(O,k,d,U),B(([b,_,Q,le,te])=>{const We=No(b),{align:Ze,behavior:Ne,offset:dt}=We;let Ee=We.index;Ee==="LAST"&&(Ee=le-1),Ee=et(0,Ee,Et(le-1,Ee));let Le=Wt(_,te,Q,Ee);return Ze==="end"?Le=zo(Le-_.height+Q.height):Ze==="center"&&(Le=zo(Le-_.height/2+Q.height/2)),dt&&(Le+=dt),{behavior:Ne,top:Le}})),c);const Be=se(I(z,B(b=>b.offsetBottom+b.bottom)),0);return L(I(R,B(b=>({height:b.visibleHeight,width:b.visibleWidth}))),O),{customScrollParent:g,data:E,deviation:Y,footerHeight:r,gap:U,headerHeight:i,increaseViewportBy:e,initialItemCount:w,itemDimensions:k,overscan:o,restoreStateFrom:fe,scrollBy:l,scrollContainerState:s,scrollHeight:N,scrollTo:c,scrollToIndex:P,scrollTop:a,smoothScrollTargetReached:f,totalCount:d,useWindowScroll:m,viewportDimensions:O,windowScrollContainerState:u,windowScrollTo:y,windowViewportRect:R,...v,gridState:z,horizontalDirection:Pe,initialTopMostItemIndex:ve,totalListHeight:Be,...p,endReached:Xe,propsReady:x,rangeChanged:ye,startReached:Ge,stateChanged:ie,stateRestoreInProgress:xe,...h}},J(qt,ue,at,Xo,je,Kt,Me));function ln(e,t,o){return et(1,St((e+o)/(St(t)+o)))}function Bo(e,t,o,n){const{height:r}=o;if(r===void 0||n.length===0)return{bottom:0,top:0};const i=Wt(e,t,o,n[0].index);return{bottom:Wt(e,t,o,n[n.length-1].index)+r,top:i}}function Wt(e,t,o,n){const r=ln(e.width,o.width,t.column),i=St(n/r),l=i*o.height+et(0,i-1)*t.row;return l>0?l+t.row:l}const qr=$(()=>{const e=S(T=>`Item ${T}`),t=S({}),o=S(null),n=S("virtuoso-grid-item"),r=S("virtuoso-grid-list"),i=S(Jt),l=S("div"),s=S(Ke),c=(T,p=null)=>se(I(t,B(v=>v[T]),Z()),p),a=S(!1),f=S(!1);return L(j(f),a),{components:t,computeItemKey:i,context:o,FooterComponent:c("Footer"),HeaderComponent:c("Header"),headerFooterTag:l,itemClassName:n,ItemComponent:c("Item","div"),itemContent:e,listClassName:r,ListComponent:c("List","div"),readyStateChanged:a,reportReadyState:f,ScrollerComponent:c("Scroller","div"),scrollerRef:s,ScrollSeekPlaceholder:c("ScrollSeekPlaceholder","div")}}),Kr=$(([e,t])=>({...e,...t}),J(Ur,qr)),Yr=H.memo(function(){const e=ee("gridState"),t=ee("listClassName"),o=ee("itemClassName"),n=ee("itemContent"),r=ee("computeItemKey"),i=ee("isSeeking"),l=pe("scrollHeight"),s=ee("ItemComponent"),c=ee("ListComponent"),a=ee("ScrollSeekPlaceholder"),f=ee("context"),T=pe("itemDimensions"),p=pe("gap"),v=ee("log"),C=ee("stateRestoreInProgress"),x=pe("reportReadyState"),g=ze(H.useMemo(()=>m=>{const u=m.parentElement.parentElement.scrollHeight;l(u);const y=m.firstChild;if(y){const{height:R,width:h}=y.getBoundingClientRect();T({height:R,width:h})}p({column:ko("column-gap",getComputedStyle(m).columnGap,v),row:ko("row-gap",getComputedStyle(m).rowGap,v)})},[l,T,p,v]),!0,!1);return Qo(()=>{e.itemHeight>0&&e.itemWidth>0&&x(!0)},[e]),C?null:M.jsx(c,{className:t,ref:g,...X(c,f),"data-testid":"virtuoso-item-list",style:{paddingBottom:e.offsetBottom,paddingTop:e.offsetTop},children:e.items.map(m=>{const u=r(m.index,m.data,f);return i?M.jsx(a,{...X(a,f),height:e.itemHeight,index:m.index,width:e.itemWidth},u):Fe.createElement(s,{...X(s,f),className:o,"data-index":m.index,key:u},n(m.index,m.data,f))})})}),Xr=H.memo(function(){const e=ee("HeaderComponent"),t=pe("headerHeight"),o=ee("headerFooterTag"),n=ze(H.useMemo(()=>i=>{t(Ce(i,"height"))},[t]),!0,!1),r=ee("context");return e?M.jsx(o,{ref:n,children:M.jsx(e,{...X(e,r)})}):null}),Zr=H.memo(function(){const e=ee("FooterComponent"),t=pe("footerHeight"),o=ee("headerFooterTag"),n=ze(H.useMemo(()=>i=>{t(Ce(i,"height"))},[t]),!0,!1),r=ee("context");return e?M.jsx(o,{ref:n,children:M.jsx(e,{...X(e,r)})}):null}),Jr=({children:e})=>{const t=H.useContext(Xt),o=pe("itemDimensions"),n=pe("viewportDimensions"),r=ze(H.useMemo(()=>i=>{n(i.getBoundingClientRect())},[n]),!0,!1);return H.useEffect(()=>{t&&(n({height:t.viewportHeight,width:t.viewportWidth}),o({height:t.itemHeight,width:t.itemWidth}))},[t,n,o]),M.jsx("div",{ref:r,style:Ye(!1),children:e})},Qr=({children:e})=>{const t=H.useContext(Xt),o=pe("windowViewportRect"),n=pe("itemDimensions"),r=ee("customScrollParent"),i=_t(o,r,!1);return H.useEffect(()=>{t&&(n({height:t.itemHeight,width:t.itemWidth}),o({offsetTop:0,visibleHeight:t.viewportHeight,visibleWidth:t.viewportWidth}))},[t,o,n]),M.jsx("div",{ref:i,style:Ye(!1),children:e})},ei=H.memo(function({...e}){const t=ee("useWindowScroll"),o=ee("customScrollParent"),n=o||t?ni:oi,r=o||t?Qr:Jr,i=ee("context");return M.jsx(n,{...e,...X(n,i),children:M.jsxs(r,{children:[M.jsx(Xr,{}),M.jsx(Yr,{}),M.jsx(Zr,{})]})})}),{Component:ti,useEmitter:sn,useEmitterValue:ee,usePublisher:pe}=Yt(Kr,{optional:{context:"context",totalCount:"totalCount",overscan:"overscan",itemContent:"itemContent",components:"components",computeItemKey:"computeItemKey",data:"data",initialItemCount:"initialItemCount",scrollSeekConfiguration:"scrollSeekConfiguration",headerFooterTag:"headerFooterTag",listClassName:"listClassName",itemClassName:"itemClassName",useWindowScroll:"useWindowScroll",customScrollParent:"customScrollParent",scrollerRef:"scrollerRef",logLevel:"logLevel",restoreStateFrom:"restoreStateFrom",initialTopMostItemIndex:"initialTopMostItemIndex",increaseViewportBy:"increaseViewportBy"},methods:{scrollTo:"scrollTo",scrollBy:"scrollBy",scrollToIndex:"scrollToIndex"},events:{isScrolling:"isScrolling",endReached:"endReached",startReached:"startReached",rangeChanged:"rangeChanged",atBottomStateChange:"atBottomStateChange",atTopStateChange:"atTopStateChange",stateChanged:"stateChanged",readyStateChanged:"readyStateChanged"}},ei),oi=Qt({useEmitter:sn,useEmitterValue:ee,usePublisher:pe}),ni=eo({useEmitter:sn,useEmitterValue:ee,usePublisher:pe});function ko(e,t,o){return t!=="normal"&&!(t!=null&&t.endsWith("px"))&&o(`${e} was not resolved to pixel value correctly`,t,ae.WARN),t==="normal"?0:parseInt(t??"0",10)}const ri=ti,ii=Object.freeze(Object.defineProperty({__proto__:null,GroupedTableVirtuoso:_r,GroupedVirtuoso:Br,LogLevel:ae,TableVirtuoso:Dr,Virtuoso:Rr,VirtuosoGrid:ri,VirtuosoGridMockContext:Xt,VirtuosoMockContext:ut},Symbol.toStringTag,{value:"Module"})),ai=At(ii),ui=At(In);export{ai as a,ui as b,Sn as c,si as g,ci as r};
