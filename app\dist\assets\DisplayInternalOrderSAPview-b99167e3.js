import{r as p,C as E,bj as L,aT as N,u as j,g as W,bk as $,t as k,c as n,j as e,Q as B,O as a,a6 as P,a_ as z,a$ as F,d as m,Z as s,aZ as y,B as G,b6 as D,b5 as M,ag as Y}from"./index-226a1e75.js";import{u as Z}from"./useInternalOrderFieldConfig-3b995eab.js";import{d as w,a as Q,b as q}from"./Category-83dc6e58.js";import{d as H}from"./Description-d98685cc.js";import{G as J}from"./GenericTabsForChange-3fc180cd.js";import"./FilterField-868050e3.js";import"./useChangeLogUpdate-23c3e0f8.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./AutoCompleteType-63e88d3d.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";const K=b=>{const[x,i]=p.useState({}),[r,u]=p.useState(!1),[d,t]=p.useState(null);return p.useEffect(()=>{b&&(u(!0),t(null),E(`/${L}${N.DISPLAY_INTERNAL_ORDER.DISPLAY_MASS_DTO}`,"post",c=>{var l;const T=((l=c==null?void 0:c.body)==null?void 0:l[0])||{};i(T),u(!1)},c=>{i({}),t(c),u(!1)},{internalOrder:b}))},[b]),{fieldValues:x,loading:r,error:d}},pe=()=>{var O,S;const b=j(),x=W(),{t:i}=$(),r=b.state,{fetchInternalOrderFieldConfig:u,fieldConfigByOrderType:d}=Z(),{fieldValues:t,loading:c,error:T}=K(r==null?void 0:r.Order),l=({label:o,value:h,labelWidth:R="25%",centerWidth:A="5%",icon:C})=>n(y,{flexDirection:"row",alignItems:"center",children:[C&&e("div",{style:{marginRight:"10px"},children:C}),e(m,{variant:"body2",color:s.secondary.grey,style:{width:R},children:o}),e(m,{variant:"body2",fontWeight:"bold",sx:{width:A,textAlign:"center"},children:":"}),e(m,{variant:"body2",fontWeight:"bold",justifyContent:"flex-start",children:h||""})]});p.useEffect(()=>{r!=null&&r.OrderType&&u(r==null?void 0:r.OrderType)},[]),p.useEffect(()=>{},[d]);const[g,I]=k.useState(0),_=r==null?void 0:r.OrderType,f=(((O=d==null?void 0:d[_])==null?void 0:O.allTabsData)||[]).filter(o=>o.tab.toLowerCase()!=="header"),v=f.map(o=>o.tab);return n("div",{children:[e(a,{container:!0,sx:B,children:e(a,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:n(a,{md:9,sx:{display:"flex"},children:[e(P,{color:"primary",sx:z,onClick:()=>x(-1),children:e(F,{sx:{fontSize:"25px",color:"#000000"}})}),n(a,{item:!0,md:12,children:[e(m,{variant:"h3",children:e("strong",{children:i("Display Internal Order")})}),e(m,{variant:"body2",color:"#777",children:i("This view displays the details of the Internal Order")})]})]})})}),n(a,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:s.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[n(y,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[e(a,{item:!0,children:e(l,{label:i("Internal Order"),value:(t==null?void 0:t.Order)||(r==null?void 0:r.Order)||"",labelWidth:"35%",icon:e(w,{sx:{color:s.blue.indigo,fontSize:"20px"}})})}),e(a,{item:!0,children:e(l,{label:i("Company Code"),value:(t==null?void 0:t.CompCode)||(r==null?void 0:r.CompCode)||"",labelWidth:"35%",icon:e(Q,{sx:{color:s.blue.indigo,fontSize:"20px"}})})})]}),n(y,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[e(a,{item:!0,children:e(l,{label:i("Order Type"),value:(t==null?void 0:t.OrderType)||(r==null?void 0:r.OrderType)||"",labelWidth:"35%",icon:e(q,{sx:{color:s.blue.indigo,fontSize:"20px"}})})}),e(a,{item:!0,children:e(l,{label:i("Order Description"),value:(t==null?void 0:t.OrderName)||(r==null?void 0:r.OrderName)||"",labelWidth:"35%",icon:e(H,{sx:{color:s.blue.indigo,fontSize:"20px"}})})})]})]}),f.length>0&&n(G,{sx:{marginTop:"30px",border:"1px solid #e0e0e0",padding:"16px",background:s.primary.white,borderRadius:"8px"},children:[e(M,{value:g,onChange:(o,h)=>I(h),"aria-label":"internal order tabs",sx:{marginBottom:2,borderBottom:1,borderColor:"divider","& .MuiTabs-indicator":{backgroundColor:s.primary.main,height:"3px"}},variant:"scrollable",scrollButtons:"auto",children:v.map((o,h)=>e(D,{label:i(o)},h))}),e(Y,{elevation:2,sx:{p:3,borderRadius:4,marginTop:2},children:e(J,{disabled:!0,basicDataTabDetails:(S=f[g])==null?void 0:S.data,dropDownData:{},activeViewTab:v[g]})})]})]})};export{pe as default};
