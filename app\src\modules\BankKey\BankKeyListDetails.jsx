//COMMENTED CODES WILL BE NEEDED AND WILL REMOVE THEM IN THE FUTURE
import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from "react";
import { useSelector, useDispatch } from "react-redux";
import { destination_BankKey, destination_IDM } from "../../destinationVariables";
import { v4 as uuidv4 } from "uuid";
import { doAjax } from "@components/Common/fetchService";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ReusableDataTable from "@components/Common/ReusableTable";
import FlexibleValidationDialog from "@components/Common/FlexibleValidationDialog";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useLocation, useNavigate } from "react-router-dom";
import {
  TextField,
  IconButton,
  Box,
  Typography,
  Paper,
  Button,
  Tabs,
  Tab,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  Checkbox,
  Tooltip,
  FormLabel,
} from "@mui/material";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { colors } from "@constant/colors";
import { setRowsHeaderData, updateModuleFieldDataBK, setOpenDialog, setSelectedRowID, setRowsBodyData, setDropDownDataBNKY, setBankKeyPayload, setBankKeyPayloadIndividual } from "./bnkySlice";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import CropFreeIcon from "@mui/icons-material/CropFree";
import CloseFullscreenIcon from "@mui/icons-material/CloseFullscreen";
import useLang from "@hooks/useLang";
import CustomDialog from "@components/Common/ui/CustomDialog";
import useBankKeyFieldConfig from "./hooks/useBankKeyFieldConfig";
import { ENABLE_STATUSES, MODULE, MODULE_MAP, REQUEST_STATUS, SUCCESS_MESSAGES } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { checkIncludedAndValidated, getValidationStatus } from "../../helper/helper";
import { useSnackbar } from "../../hooks/useSnackbar";
import { BK_HEADER_MANDATORY, DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, REGION_CODE, VALIDATION_STATUS } from "../../constant/enum";
import { button_Outlined, button_Primary } from "../../components/common/commonStyles";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import LibraryAddIcon from '@mui/icons-material/LibraryAdd';
import { useTheme } from "@emotion/react";
import { fetchTransportationZone } from "../../../src/functions";

const BankKeyListDetails = forwardRef(({
  setIsAttachmentTabEnabled,
  setCompleted,
  requestStatus,
},ref) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  let task = useSelector((state) => state?.userManagement.taskData);
  const { selectedRowID, mandatoryFields } = useSelector((state) => state.bankKey);
  const bankKeyData = useSelector((state => state.bankKey));
  const bankKeyTabs = useSelector((state) => state.bankKey.bankKeyTabs || []);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isrequestId = queryParams.get("RequestId");
  const reqBench = queryParams.get("reqBench");
  const reqBenchData = location?.state || {};
  const openDialog = useSelector((state) => state.bankKey.isOpenDialog);
  const { loading, error, fetchBankKeyFieldConfig } = useBankKeyFieldConfig();
  const rowsHeaderData = useSelector((state) => state.bankKey.payload.rowsHeaderData);
  const rowsBodyData = useSelector((state) => state.bankKey.payload?.rowsBodyData || {});
  const initialPayload = useSelector((state) => state.bankKey.payload?.requestHeaderData || {});
  const reduxPayload = useSelector((state => state.bankKey.payload));
  const requestType = reduxPayload?.requestHeaderData?.RequestType;
  const changeLogBK = useSelector((state) => state.changeLog.createChangeLogDataBK || {});
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [isAddRowEnabled, setIsAddRowEnabled] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [withReference, setWithReference] = useState("yes");
  const [selectedBankCtry, setSelectedBankCtry] = useState("");
  const [selectedBankKey, setSelectedBankKey] = useState("");
  const [focusedRow, setFocusedRow] = useState(null);
  const [charCountMap, setCharCountMap] = useState({});
  const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({ data: {}, isVisible: false });
  const [newRowId, setNewRowId] = useState();
  const [customMessageDialog, setCustomMessageDialog] = useState({
    open: false,
    message: "",
    title: ""
  });
  const [isAddRowMode, setIsAddRowMode] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const { showSnackbar } = useSnackbar();
  const isreqBench = queryParams.get("reqBench");
  const disableCheck = (reqBench && !ENABLE_STATUSES.includes(requestStatus)) 
  let taskData = useSelector((state) => state.userManagement.taskData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [wfLevels, setWfLevels] = useState([]);
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  const [errorFieldMap, setErrorFieldMap] = useState({});
  const theme = useTheme()
  const [selectedPreviousRowId,setSelectedPreviousRowId]=useState();
  const [activeTab,setActiveTab]=useState("");
  const [firstErrorField, setFirstErrorField] = useState(null);

  useEffect(() => {
    if (rowsHeaderData) {
      const allRowsValidated = checkIncludedAndValidated(rowsHeaderData);
      setIsAddRowEnabled(allRowsValidated);
    }
  }, [rowsHeaderData]);

  const handleBKCountry = (bkCountry) => {
    if (bkCountry) {
      const region = initialPayload?.Region || REGION_CODE.US;
      const keyToCheck = `${region}-${bkCountry}`;
      const bkCountryExists = keyToCheck in bankKeyTabs;

      if (!bkCountryExists) {
        fetchBankKeyFieldConfig(keyToCheck, bkCountry);
        fetchTransportationZone(bkCountry,dispatch);
      }
    }
  }
  useEffect(() => {
    if (
      (isrequestId && !reqBench) ||
      (reqBench && reqBenchData?.reqStatus !== REQUEST_STATUS?.DRAFT) ||
      (reqBench && reqBenchData?.reqStatus === REQUEST_STATUS?.DRAFT && reqBenchData?.objectNumbers !== "Not Available")) {
      dispatch(setOpenDialog(false));
    }
  }, []);

  useEffect(() => {
    if (rowsHeaderData?.[0]?.id) {
      handleBKCountry(rowsHeaderData?.[0]?.BankCtry)
    }
  }, [rowsHeaderData])

  // const [fieldToScroll, setFieldToScroll] = useState(null);

// Add this simple scroll function
// const scrollToField = (fieldName) => {
//   const attemptScroll = (attempt = 1) => {
//     const maxAttempts = 5;
    
//     const selectors = [
//       `[name="${fieldName}"]`,
//       `#${fieldName}`,
//       `input[name="${fieldName}"]`,
//       `select[name="${fieldName}"]`,
//       `textarea[name="${fieldName}"]`,
//       `[data-field="${fieldName}"]`,
//       `.MuiTextField-root input[name="${fieldName}"]`,
//       `.MuiSelect-root[name="${fieldName}"]`,
//       `.MuiFormControl-root [name="${fieldName}"]`
//     ];

//     let element = null;
//     for (const selector of selectors) {
//       try {
//         element = document.querySelector(selector);
//         if (element) break;
//       } catch (e) {}
//     }

//     if (element) {
//       element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
//       setTimeout(() => {
//         if (element.focus && !element.disabled) {
//           element.focus();
//         }
//         element.style.boxShadow = '0 0 0 3px rgba(255, 0, 0, 0.5)';
//         setTimeout(() => element.style.boxShadow = '', 2000);
//       }, 100);
      
//       return true;
//     }

//     if (attempt < maxAttempts) {
//       setTimeout(() => attemptScroll(attempt + 1), attempt * 200);
//     } else {
//       console.warn(`Could not find field: ${fieldName}`);
//     }
//   };

//   attemptScroll();
// };

// Add this useEffect to watch for dialog close
// useEffect(() => {
//   if (!missingFieldsDialogOpen && fieldToScroll) {
//     setTimeout(() => {
//       scrollToField(fieldToScroll);
//       setFieldToScroll(null);
//     }, 500); // Increased delay
//   }
// }, [missingFieldsDialogOpen, fieldToScroll, selectedTab]);
const [hasDialogOpenedOnce, setHasDialogOpenedOnce] = useState(false);

useEffect(() => {
  if (openDialog && !hasDialogOpenedOnce) {
    setHasDialogOpenedOnce(true);
  }
}, [openDialog]);

const handleValidate = async (row) => {
  const missing = [];
  const errorFieldsForRow = [];

  let firstErrorField = null;
  let firstErrorTabIndex = null;

  // 🔹 Step 1: Header mandatory fields (assume they belong to first tab, index 0)
   for (const key of BK_HEADER_MANDATORY || []) {
    let value = row[key.jsonName];
    if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
      missing.push(key.fieldName);

      if (!firstErrorField) {
        firstErrorField = key.jsonName;
        firstErrorTabIndex = 0; // header tab index
      }
    }
  };

  // 🔹 Step 2: Object-level mandatory fields (grouped by tab index in mandatoryFields)
  const objectLevelMandatoryFields = mandatoryFields?.[`${initialPayload?.Region}-${row?.BankCtry}`] || {};
let tabIndex = 0;
  for (const tabKey of Object.keys(objectLevelMandatoryFields)) {
    const fields = objectLevelMandatoryFields[tabKey];
    for (const field of fields) {
      let value = rowsBodyData?.[row.id]?.[field.jsonName];
      if (value === null || value === undefined || (typeof value === "string" && value.trim() === "")) {
        errorFieldsForRow.push(field.jsonName);
        missing.push(field.fieldName);

        if (!firstErrorField) {
          firstErrorField = field.jsonName;
          firstErrorTabIndex = tabIndex; // tab index automatically
        }
      }
    };tabIndex++;
  };

  if (missing.length > 0) {
    // 🔹 Mark row invalid
    const updatedRows = rowsHeaderData.map((item) =>
      item.id === row.id ? { ...item, validated: false } : item
    );
    dispatch(setRowsHeaderData(updatedRows));
setSelectedTab(firstErrorTabIndex);
setSelectedRow(row);
    dispatch(setSelectedRowID(row?.id));
  // Save the field to scroll to
  // setFieldToScroll(firstErrorField);
    setMissingFields(missing);
    setMissingFieldsDialogOpen(true);
    setErrorFieldMap((prev) => ({
      ...prev,
      [row.id]: errorFieldsForRow,
    }));

    // Call this in your validation when error is detected
    // if (firstErrorField !== null && firstErrorTabIndex !== null) {
    //   setSelectedTab(firstErrorTabIndex); // switch tab
    //   setFieldToScroll(firstErrorField);  // mark field to scroll after dialog closes
    // }
    return {
      validated: false,
      rowId: row.id,
      missing,
      errorTabIndex: firstErrorTabIndex,
      errorField: firstErrorField
    };
  }

  try {
    // 🔹 Step 3: Duplicate check
    const bankCtry = row.BankCtry;
    const bankKey = row.BankKey;
    const bankName = row.BankName;

    if (bankCtry && bankKey) {
      const duplicateResult = await checkDuplicateBankDetails(bankCtry, bankKey, bankName);

      if (duplicateResult.isDuplicate) {
        const updatedRows = rowsHeaderData.map((item) =>
          item.id === row.id ? { ...item, validated: false } : item
        );
        dispatch(setRowsHeaderData(updatedRows));

        setCustomMessageDialog({
          open: true,
          message: duplicateResult.message,
          title: "Duplicate Bank Details Found",
        });
        return;
      }
    }

    // 🔹 Step 4: Mark row as validated
    const updatedRows = rowsHeaderData.map((item) =>
      item.id === row.id ? { ...item, validated: true } : item
    );
    dispatch(setRowsHeaderData(updatedRows));

    setErrorFieldMap((prev) => {
    const updated = { ...prev };
    delete updated[row.id];
    return updated;
    });
    // showSnackbar("Validation Successful", "success");
    setCompleted([true, true]);
    setIsAttachmentTabEnabled(true);

    const allRowsValidated = checkIncludedAndValidated(updatedRows);
    setIsAddRowEnabled(allRowsValidated);
  } catch (error) {
    showSnackbar("Validation failed due to an error", "error");
  }
   return {
    validated: true,
    row
  };
};
useImperativeHandle(ref,()=>({
  validateRow: handleValidate,
}))


  const checkDuplicateBankDetails = (bankCtry, bankKey, bankName) => {
    return new Promise((resolve, reject) => {
      const payload = [
        {
          bankCtry: bankCtry,
          bankKey: bankKey,
          requestNo: bankKeyData?.requestHeaderResponse?.requestId || initialPayload?.RequestId || "",
          bankName: bankName,
        },
      ];

      const successHandler = (data) => {
        if (data?.body?.length) {
          const errorMessage = `Duplicate bank details found: ${data.body[0].split("$^$")[0]} (${data.body[0].split("$^$")[1]})`;
          resolve({ isDuplicate: true, message: errorMessage });
        } else {
          resolve({ isDuplicate: false, message: "" });
        }
      };

      const errorHandler = (error) => {
        customError(error);
        resolve({ isDuplicate: false, message: "" });
      };

      let localDuplicateCount = 0;
      let duplicateMessage = "";

      rowsHeaderData?.forEach((key) => {
        if (key?.BankCtry && key?.BankKey) {
          if (key?.BankCtry === bankCtry && key?.BankKey === bankKey) {
            localDuplicateCount++;
          }
        }
      });

      if (localDuplicateCount > 1) {
        duplicateMessage = `Duplicate bank details found locally: ${bankCtry} - ${bankKey}`;
        resolve({ isDuplicate: true, message: duplicateMessage });
      } else {
        doAjax(
          `/${destination_BankKey}${END_POINTS.MASS_ACTION?.BANK_DUPLICATE_CHECK}`,
          "post",
          successHandler,
          errorHandler,
          payload
        );
      }
    });
  };

  const handleDelete = () => {
    const params = isDeleteDialogVisible?.data;
    dispatch(setRowsHeaderData(rowsHeaderData?.filter((row) => row.id !== params?.row?.id)));
    let localRowsBodyData = { ...rowsBodyData }
    delete localRowsBodyData[params?.row?.id]
    dispatch(setRowsBodyData(localRowsBodyData))
    setSelectedRow(rowsHeaderData?.[0])
    setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false });
  };

  const isFieldMandatory = (jsonName) => {
    return BK_HEADER_MANDATORY?.some(field => field.jsonName === jsonName);
  };

  const getBankKey = (value) => {
    setIsDropDownLoading(true)
    const url = `/${destination_BankKey}/data/getBankKeyBasedOnCountry`
    doAjax(
      url,
      "post",
      (data) => {
        setIsDropDownLoading(false)
        dispatch(setDropDownDataBNKY({
          keyName: "BankKey",
          data: data?.body || []
        }))
      },
      () => { setIsDropDownLoading(false) },
      { bankCtry: value }
    )
  }

  const columns = [
    {
      field: "included",
      headerName: "Included",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          disabled={disableCheck}
          onChange={(e) =>
            handleRowInputChange(e.target.checked, params.row.id, "included")
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: "Line Number",
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = rowsHeaderData.findIndex((row) => row.id === params.row.id);
        return <div>{(rowIndex + 1) * 10}</div>;
      },
    },
    {
      field: "BankCtry",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Ctry/Reg.")}
          {isFieldMandatory("BankCtry") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={bankKeyData?.dropDownData?.BankCtry || []}
            value={
              bankKeyData?.dropDownData?.BankCtry?.find(
                (item) => item.code === params.row.BankCtry
              ) || null
            }
            onChange={(newValue) =>
              handleRowInputChange(newValue?.code, params.row.id, "BankCtry", params)
            }
            placeholder={t("Select Bank Country")}
            disabled={disableCheck}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "BankKey",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Key")}
          {isFieldMandatory("BankKey") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankKey || "";
        const maxLength = 9; // Default or from config
        const currentLength = value.length;
        const [isFocused, setIsFocused] = React.useState(false);

        return (
          <Box sx={{ position: "relative", width: "100%" }}>
            <TextField
              value={value}
              onChange={(e) => {
                const input = e.target.value.toUpperCase();
                if (/^\d*$/.test(input) && input.length <= maxLength) {
                  handleRowInputChange(input, params.row.id, "BankKey");
                }
              }}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              inputProps={{ maxLength }}
              variant="outlined"
              size="small"
              placeholder="Enter Bank Key"
              disabled={disableCheck}
              fullWidth
              helperText={
                isFocused &&
                (currentLength === maxLength
                  ? `Max Length Reached`
                  : `${currentLength}/${maxLength}`)
              }
              FormHelperTextProps={{
                sx: {
                  color: isFocused && currentLength === maxLength ? "red" : "blue",
                  position: "absolute",
                  bottom: "-20px",
                },
              }}
              sx={{
                "& .MuiInputBase-input": {
                  padding: "10px 14px",
                },
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                },
              }}
            />
          </Box>
        );
      }
    },
    {
      field: "BankName",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Name")}
          {isFieldMandatory("BankName") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankName || "";
        const maxLength = 60; // Set appropriate max length for bank name
        const currentLength = value.length;
        const [isFocused, setIsFocused] = React.useState(false);

        return (
          <Box sx={{ position: "relative", width: "100%" }}>
            <TextField
              value={value}
              onChange={(e) => {
                const input = e.target.value.toUpperCase();
                if (input.length <= maxLength) {
                  handleRowInputChange(input, params.row.id, "BankName");
                }
              }}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              inputProps={{ maxLength }}
              variant="outlined"
              size="small"
              placeholder="Enter Bank Name"
              disabled={disableCheck}
              fullWidth
              helperText={
                isFocused &&
                (currentLength === maxLength
                  ? `Max Length Reached`
                  : `${currentLength}/${maxLength}`)
              }
              FormHelperTextProps={{
                sx: {
                  color: isFocused && currentLength === maxLength ? "red" : "blue",
                  position: "absolute",
                  bottom: "-20px",
                },
              }}
              sx={{
                "& .MuiInputBase-input": {
                  padding: "10px 14px",
                },
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                },
              }}
            />
          </Box>
        );
      },
    },
    {
      field: "BankBranch",
      flex: 1,
      headerAlign: "center",
      renderHeader: () => (
        <span>
          {t("Bank Branch")}
          {isFieldMandatory("BankBranch") && (
            <span style={{ color: "red" }}> *</span>
          )}
        </span>
      ),
      renderCell: (params) => {
        const value = params.row.BankBranch || "";
        const maxLength = 40; // Set appropriate max length for bank branch
        const currentLength = value.length;
        const [isFocused, setIsFocused] = React.useState(false);

        return (
          <Box sx={{ position: "relative", width: "100%" }}>
            <TextField
              value={value}
              onChange={(e) => {
                const input = e.target.value.toUpperCase();
                if (input.length <= maxLength) {
                  handleRowInputChange(input, params.row.id, "BankBranch");
                }
              }}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              inputProps={{ maxLength }}
              variant="outlined"
              size="small"
              placeholder="Enter Bank Branch"
              disabled={disableCheck}
              fullWidth
              helperText={
                isFocused &&
                (currentLength === maxLength
                  ? `Max Length Reached`
                  : `${currentLength}/${maxLength}`)
              }
              FormHelperTextProps={{
                sx: {
                  color: isFocused && currentLength === maxLength ? "red" : "blue",
                  position: "absolute",
                  bottom: "-20px",
                },
              }}
              sx={{
                "& .MuiInputBase-input": {
                  padding: "10px 14px",
                },
                "& .MuiInputBase-root.Mui-disabled": {
                  "& > input": {
                    WebkitTextFillColor: colors.black.dark,
                    color: colors.black.dark,
                  },
                },
              }}
            />
          </Box>
        );
      },
    },
    {
      field: "action",
      headerName: "Action",
      flex: 0.5,
      headerAlign: "center",
      align: "center",
      renderHeader: () => (
        <span style={{ fontWeight: "bold" }}>{t("Action")}</span>
      ),
      renderCell: (params) => {
        let deleteId = params.row.id
        let validateStatus = getValidationStatus(params?.row);
        return (
          <Box>
            <Tooltip
              title={
                validateStatus === "success"
                  ? "Validated Successfully"
                  : validateStatus === "error"
                    ? "Validation Failed"
                    : "Click to Validate"
              }
            >
              <IconButton
                disabled={disableCheck}
                onClick={(e) => {
                  e.stopPropagation();
                  handleValidate(params.row);
                }}
                color={validateStatus === "success" ? "success" : validateStatus === "error" ? "error" : "default"}>
                {validateStatus === "error" ? <CancelOutlinedIcon /> : <TaskAltIcon />}
              </IconButton>
            </Tooltip>
            <Tooltip
              title={t("Delete Row")}
              disabled={disableCheck || /^\d+$/.test(String(deleteId))}>
              <IconButton
                onClick={() => {
                  setIsDeleteDialogVisible({ ...isDeleteDialogVisible, data: params, isVisible: true });
                }}
                color="error"
              >
                <DeleteOutlineOutlinedIcon />
              </IconButton>
            </Tooltip>
          </Box>
        );
      },
    },
  ];
  const handleDialogClose = () => {
    dispatch(setOpenDialog(false));
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
    if (isTabsZoomed) setIsTabsZoomed(false);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed) setIsGridZoomed(false);
  };

  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
  };

  const handleRowInputChange = (value, id, field, rowData) => {
    if (field === "BankCtry") {
      handleBKCountry(value)
    }
    if (field === "BankName") {
      dispatch(
        updateModuleFieldDataBK({
          uniqueId: selectedRowID || selectedRow?.id,
          keyName: "Name",
          data: value,
        })
      );
    }
    else if (field === "BankKey") {
      dispatch(
        updateModuleFieldDataBK({
          uniqueId: selectedRowID || selectedRow?.id,
          keyName: "BankKey",
          data: value,
        })
      );
    }
    const updatedRows = rowsHeaderData.map((row) =>
      row.id === id ? { ...row, [field]: value, validated: "default" } : row
    );
    dispatch(setRowsHeaderData(updatedRows));

    if (rowData?.row) {
      let newRow = JSON.parse(JSON.stringify(rowData?.row));
      newRow[field] = value;
      handleRowClick({ row: newRow })
    }
  };

  const handleAddRow = () => {
    const id = uuidv4();
    setNewRowId(id);
    setIsAddRowMode(true);
    setWithReference("yes"); // Reset radio to default

    // dispatch(
    //   setRowsHeaderData([
    //     ...rowsHeaderData,
    //     {
    //     id,
    //     BankCtry: "",
    //     BankKey: "",
    //     BankName: "",
    //     BankBranch: "",
    //     included: true,
    //     validated: "default",
    //   },
    //   ])
    // );
    dispatch(setOpenDialog(true));
  };

const handleTabChange = (event, newValue) => {
  setSelectedTab(newValue);
};

  useEffect(() => {
    if (!selectedRow?.BankCtry) {
      setSelectedRow(rowsHeaderData[0]);
      dispatch(setSelectedRowID(rowsHeaderData[0]?.id))
      setNewRowId(rowsHeaderData[0]?.id)
    }
  }, [rowsHeaderData]);

  const [rowRegionData, setRowRegionData] = useState({});

  const getRegionBasedOnCountry = (countryCode, fieldData, rowId) => {
    const hSuccess = (data) => {
      // Store region data for this specific row
      setRowRegionData((prev) => ({
        ...prev,
        [rowId]: data.body,
      }));

      // Also update the general dropdown data for UI rendering
      setDropdownDataRegion(data.body);

      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "BankCtry", data: data.body },
      });
    };
    if (!countryCode) {
      const hError = (error) => {
        console.log(error);
      };

      doAjax(
        `/${destination_BankKey}/data/getRegionBasedOnCountry?country=${countryCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  }

  const handleAddWithRef = async (bankCtry, bankKey) => {
    setBlurLoading(true)
    const url = `/${destination_BankKey}/data/displayBankKeySAPData`
    var payload = {
      bankCtry,
      bankKey
    }

    const hSuccess = (response) => {
      setBlurLoading(false)
      const apiResponse = response?.body[0] || {};
      let rowsBody = { ...rowsBodyData };
      let rowsHeader = [];
      const dynamicKey = newRowId || "";
      if (!dynamicKey) return;

      if (dynamicKey === "first_request_id_requestor") {
        rowsHeader?.push({
          id: newRowId || "",
          BankCtry: apiResponse?.BankCtry || selectedBankCtry,
          BankKey: "",
          BankName: "",
          BankBranch: apiResponse?.Tobankaddress?.BankBranch || "",
          included: true,
          validated: "default"
        })
      } else {
        rowsHeader = [...rowsHeaderData];

        const newRow = {
          id: newRowId || "",
          BankCtry: apiResponse?.BankCtry || selectedBankCtry,
          BankKey: "",
          BankName: "",
          BankBranch: apiResponse?.Tobankaddress?.BankBranch || "",
          included: true,
          validated: "default"
        };

        const existingIndex = rowsHeaderData.findIndex(row => row.id === newRowId);

        if (existingIndex !== -1) {
          rowsHeader[existingIndex] = newRow;
        } else {
          rowsHeader.push(newRow);
        }
      }

      rowsBody[dynamicKey] = {
        ...apiResponse,
        ...(apiResponse?.Tobankaddress || {}),
        BankNo: "",
        Name: ""
      };

      dispatch(setBankKeyPayloadIndividual({ keyName: "rowsHeaderData", data: rowsHeader }));
      dispatch(setBankKeyPayloadIndividual({ keyName: "rowsBodyData", data: rowsBody }));

      setSelectedBankCtry("")
      setSelectedBankKey("")
    }

    const hError = () => {
      setBlurLoading(false)
    }

    doAjax(url, "post", hSuccess, hError, payload)
  };

  const handleProceed = async () => {
   
    if (withReference === "no") {
      dispatch(setOpenDialog(false));
      setIsAddRowMode(false);
    } else {
      if (selectedPreviousRowId) {
        handleAddWithPreviousRowRef(selectedPreviousRowId);
      }
      else{
      await handleAddWithRef(selectedBankCtry, selectedBankKey);
      dispatch(setOpenDialog(false));
      setIsAddRowMode(false);}
    }
  };
const handleAddWithPreviousRowRef = (previousRowId) => {
    const id = uuidv4();
    const existingRowsBody = { ...rowsBodyData };
    const previousRowData = existingRowsBody[previousRowId] || {};
  
   const previousRowHeader =
  rowsHeaderData.find((row) => row.id === previousRowId) || {};

    // Extract table fields and payload from previous row
    const previousPayload = previousRowData || {};
  
    existingRowsBody[id] = {
      // Table fields at root level (copy from previous row)
      // BankCtry: previousRowData.BankCtry || "",
      // BankKey: previousRowData.BankKey || "",
      // BankName: previousRowData.BankName || "",
      // BankBranch: previousRowData.BankBranch || "",
      // included: true,
      // Copy payload data from previous row
         ...previousPayload
    };
  const newItem = {
  id,
  BankCtry: previousRowHeader.BankCtry || "",
  BankKey: "",
  BankName:  "",
  BankBranch: previousRowHeader.BankBranch || "",
  included: true,
  validated: "default",
};
    const updatedRowsHeader = [...rowsHeaderData, newItem];
    // Update Redux with the new rowsBodyData
    dispatch(setBankKeyPayloadIndividual({ keyName: "rowsBodyData", data: existingRowsBody }));
    dispatch(setBankKeyPayloadIndividual({ keyName: "rowsHeaderData", data: updatedRowsHeader }));
    dispatch(setOpenDialog(false));
    showSnackbar("success with copy from line number", "success");
  };
  const handleRowClick = (params) => {
    const clickedRow = params.row;
    handleBKCountry(clickedRow?.BankCtry)
    setSelectedRow(clickedRow);
    dispatch(setSelectedRowID(clickedRow?.id));
  };

  return (
    <div>
      {error && (
        <Typography color="error">{t("Error loading data")}</Typography>
      )}
      <div
        style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}
      >
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : 0,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed
              ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
              : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("List of Bank Keys")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={handleAddRow}
                disabled={!isAddRowEnabled || disableCheck|| (isrequestId && !reqBench)}
              >
                {t("+ Add")}
              </Button>
              <Tooltip
                title={isGridZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <div>
                <ReusableDataTable
                  isLoading={loading}
                  rows={rowsHeaderData}
                  columns={columns}
                  pageSize={10}
                  rowHeight={70}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  callback_onRowSingleClick={handleRowClick}
                  // getRowClassName={(params) =>
                  //   selectedRow?.id === selectedRowID ? "Mui-selected" : ""
                  // }
                  showFilter={false}
                  showColumns={false}
                  rowSelectionModel={selectedRowID ? [selectedRowID] : []} 
                />
              </div>
            </div>
          </div>
        </Box>
      </div>

      {/* with and without reference */}

      {openDialog && (
        <Dialog
          fullWidth
          open={openDialog}
          maxWidth="lg"
          onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              padding: "0.75rem 1rem",
              background: (theme) => theme.palette.primary.light,
              borderBottom: "1px solid #d6d6f0",
            }}
          >
            {/* <Typography variant="h6">Add New Bank Key</Typography> */}
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <LibraryAddIcon sx={{ mr: 1, color: theme.palette.primary.dark }} />
              <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.primary.dark }}>
                Add New Bank Key
              </Typography>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              alignItems: "center",
              justifyContent: "center",
              margin: "0px 25px",
            }}
          >
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel
                component="legend"
                sx={{
                  padding: "15px 0px",
                  fontWeight: "600",
                  fontSize: "15px",
                }}
              >
                Do You Want To Continue
              </FormLabel>
              <RadioGroup
                row
                aria-label="profit-center-number"
                name="profit-center-number"
                value={withReference}
                onChange={(event) => setWithReference(event.target.value)}
              >
                <FormControlLabel
                  value="yes"
                  control={<Radio />}
                  label="With Reference"
                />
                <FormControlLabel
                  value="no"
                  control={<Radio />}
                  label="Without Reference"
                />
              </RadioGroup>
            </FormControl>
            <Grid container spacing={2}>
              {/* First row: 4 dropdowns */}
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={bankKeyData?.dropDownData?.BankCtry || []}
                      value={
                        bankKeyData?.dropDownData?.BankCtry?.find(
                          (item) => item.code === selectedBankCtry
                        ) || null
                      }
                      onChange={(newValue) => {
                        setSelectedBankCtry(newValue?.code || "");
                        getBankKey(newValue?.code);
                        // getRegionBasedOnCountry(newValue?.code)
                      }}
                      placeholder="Select Bank Country"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={bankKeyData?.dropDownData?.BankKey || []}
                      value={
                        bankKeyData?.dropDownData?.BankKey?.find(
                          (item) => item.code === selectedBankKey
                        ) || null
                      }
                      onChange={(newValue) => {
                        setSelectedBankKey(newValue?.code || "");
                      }}
                      isLoading={isDropDownLoading}
                      placeholder="Select Bank Key"
                      disabled={withReference === "no"}
                      minWidth="90%"
                      listWidth={235}
                    />
                  </Grid>
                </Grid>
              </Grid>
              {rowsHeaderData[0].validated === true && (
                  <>
                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '10px 0' }}>
                        <Typography variant="body2" sx={{ color: '#666', fontWeight: 'bold' }}>
                          OR
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={12}>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <SingleSelectDropdown
                            options={rowsHeaderData.map((item, index) => {
                              const lineNumber = (index + 1) * 10;
                              return {
                                code: lineNumber.toString(),
                                desc: `${lineNumber}`
                              };
                            })}
                            value={
                              selectedPreviousRowId ? (() => {
                                const selectedRow = rowsHeaderData.find(r => r.id === selectedPreviousRowId);
                                if (selectedRow) {
                                  const index = rowsHeaderData.findIndex(r => r.id === selectedPreviousRowId);
                                  const lineNumber = (index + 1) * 10;
                                  return {
                                    code: lineNumber.toString(),
                                  }
                                }
                                return null;
                              })() : null
                            }
                            onChange={(newValue) => {
                              const lineNumber = parseInt(newValue?.code || "0");
                              const rowIndex = (lineNumber / 10) - 1;
                              const selectedRow = rowsHeaderData[rowIndex];
                              setSelectedPreviousRowId(selectedRow?.id || "");
                            }}
                            placeholder="SELECT BANK KEY LINE NUMBER"
                            minWidth="90%"
                            listWidth={450}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </>
                )}
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              // sx={{
              //   width: "max-content",
              //   textTransform: "capitalize",
              // }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button
              type="save"
              disabled={!(selectedBankCtry && selectedBankKey) && withReference === "yes" &&!selectedPreviousRowId }
              onClick={handleProceed}
              variant="contained"
            >
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
      )}        
      {/* Missing Fields Dialog */}
            <FlexibleValidationDialog
              open={missingFieldsDialogOpen}
              onClose={() => setMissingFieldsDialogOpen(false)}
              missingFields={missingFields}
              t={t}
            />  
        {/* Custom Message Dialog for Duplicacy */}
        <FlexibleValidationDialog
          open={customMessageDialog.open}
          onClose={() => setCustomMessageDialog({ open: false, message: "", title: "" })}
          customMessage={customMessageDialog.message}
          title={customMessageDialog.title}
          t={t}
        />

      {isDeleteDialogVisible?.isVisible && (
        <CustomDialog isOpen={isDeleteDialogVisible?.isVisible} titleIcon={<DeleteOutlineOutlinedIcon size="small" color="error" sx={{ fontSize: "20px" }} />} Title={t("Delete Row") + "!"} handleClose={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
          <DialogContent sx={{ mt: 2 }}>{t(DIALOUGE_BOX_MESSAGES.DELETE_MESSAGE)}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={() => setIsDeleteDialogVisible({ ...isDeleteDialogVisible, isVisible: false })}>
              {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleDelete}>
              {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}

      {selectedRow &&
        (
          <Box
            sx={{
              position: isTabsZoomed ? "fixed" : "relative",
              top: isTabsZoomed ? 0 : "auto",
              left: isTabsZoomed ? 0 : "auto",
              right: isTabsZoomed ? 0 : "auto",
              bottom: isTabsZoomed ? 0 : "auto",
              width: isTabsZoomed ? "100vw" : "100%",
              height: isTabsZoomed ? "100vh" : "auto",
              zIndex: isTabsZoomed ? 1004 : 0,
              backgroundColor: isTabsZoomed ? "white" : "transparent",
              padding: isTabsZoomed ? "20px" : "0",
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              boxShadow: isTabsZoomed
                ? "0px 0px 15px rgba(0, 0, 0, 0.2)"
                : "none",
              transition: "all 0.3s ease",
              borderRadius: "8px",
              border: "1px solid #e0e0e0",
            }}
          >
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                padding: "8px 16px",
                borderRadius: "8px 8px 0 0",
              }}
            >
              <Typography variant="h6">{t("View Details")}</Typography>
              <Tooltip
                title={isTabsZoomed ? "Exit Zoom" : "Zoom In"}
                sx={{ zIndex: "1009" }}
              >
                <IconButton
                  onClick={toggleTabsZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
              <Tabs
                value={selectedTab}
                onChange={handleTabChange}
                indicatorColor="primary"
                textColor="primary"
                aria-label="Request tabs"
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  top: 0,
                  position: "sticky",
                  zIndex: 1000,
                  backgroundColor: colors.background.container,
                  borderBottom: `1px solid ${colors.border.light}`,
                  "& .MuiTab-root": {
                    minHeight: "48px",
                    textTransform: "none",
                    fontSize: "14px",
                    fontWeight: 600,
                    color: colors.black.graphite,
                    "&.Mui-selected": {
                      color: colors.primary.main,
                      fontWeight: 700,
                    },
                    "&:hover": {
                      color: colors.primary.main,
                      opacity: 0.8,
                    },
                  },
                  "& .MuiTabs-indicator": {
                    backgroundColor: colors.primary.main,
                    height: "3px",
                  },
                }}
              >
                {bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.map((tab, index) => (
                  <Tab key={index} label={tab.tab} />
                ))}
              </Tabs>

              <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
                {bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab] && (
                  <GenericTabsGlobal
                    key={
                      (selectedRow?.id || selectedRowID || rowsHeaderData[0]?.id) +
                      (errorFieldMap[
                        selectedRow?.id || selectedRowID || rowsHeaderData[0]?.id
                      ]?.join(",") || "")
                    }
                    disabled={disableCheck}
                    basicDataTabDetails={bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab].data}
                    activeViewTab={bankKeyTabs?.[`${initialPayload?.Region}-${selectedRow?.BankCtry}`]?.[selectedTab].tab}
                    uniqueId={selectedRow?.id || selectedRowID || rowsHeaderData[0]?.id}
                    selectedRow={selectedRow || {}}
                    module={MODULE_MAP?.BK}
                    fieldErrors={
                      errorFieldMap[
                      selectedRow?.id || selectedRowID || rowsHeaderData[0]?.id
                      ] || []
                    }
                    missingFieldsDialogOpen={missingFieldsDialogOpen}
                  />
                )}
              </Paper>
              <Box
                sx={{
                  borderTop: "1px solid #e0e0e0",
                  padding: "16px",
                }}
              >

              </Box>
            </Box>
          </Box>
        )}
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </div>
  );
});

export default BankKeyListDetails;

