import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  FormControl,
  FormControlLabel,
  FormGroup,
  Grid,
  Popover,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import {
  container_filter,
  font_Small,
} from "../common/commonStyles";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useDispatch, useSelector } from "react-redux";
import DateRange from "../Common/DateRangePicker";
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import FilterListIcon from '@mui/icons-material/FilterList';
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import {
  destination_DocumentManagement,
  destination_MaterialMgmt,
} from "../../destinationVariables";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { doAjax } from "@components/Common/fetchService";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { END_POINTS } from "@constant/apiEndPoints";
import { colors } from "@constant/colors";
import { styled, useTheme } from '@mui/material/styles';
import { DECISION_TABLE_NAME, ROLES, SEARCH_FIELD_TYPES } from "@constant/enum";
import useLogger from "@hooks/useLogger";
import useLang  from "@hooks/useLang";



const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor:  theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    '&:hover fieldset': {
      borderColor: colors.primary.main,
    },
  },
});

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));

const DocumentFilter = ({
  handleSearch,
  getFilter  
}) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 7);
  const FilterSearchForm = useSelector(
    (state) => state.commonFilter["DocumentManagement"]
  );
  const { customError } = useLogger();
  let userData = useSelector((state) => state.userManagement.userData);
  let userRoles = useSelector((state) => state.userManagement.roles);
  const { t } = useLang();
  const [requestType, setRequestType] = useState([]);
  const [docOptions, setDocOptions] = useState([]);
  const [selectedCreatedBy, setSelectedCreatedBy] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [createdByOptions, setCreatedByOptions] = useState([]);
  const [docType, setDocType] = useState([]);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const [attachmentOptions, setAttachmentOptions] = useState([]);
  const [attachmentType, setAttachmentType] = useState([]);
  const [timerId, setTimerId] = useState(null);
  const [clearClicked, setClearClicked] = useState(false);
  const { getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall();
  const [searchResponse,setSeacrhResponse]  = useState([])

  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const fetchSearchParameterFromDt = () => {
    let payload = {
          decisionTableId: null,
          decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
          version: "v2",
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_REGION":"US",
              "MDG_CONDITIONS.MDG_MODULE":"Material",
              "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Document Management",
              "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
            },
          ],
        };
        getSearchParams(payload);
  }

  useEffect(() => {
    getDocType();
    getAttachmentType();
    fetchSearchParameterFromDt()
  }, []);
  useEffect(() => {
    if(dtSearchParamsResponse){
      setSeacrhResponse(dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE)
    }
  },[dtSearchParamsResponse])

  useEffect(() => {
    var tempReqType = requestType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      requestType: tempReqType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [requestType]);

  useEffect(() => {
    var tempDocType = docType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      docType: tempDocType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [docType]);

  useEffect(() => {
    var tempAttType = attachmentType.map((item) => item).join("$^$");

    let tempFilterData = {
      ...FilterSearchForm,
      attType: tempAttType,
    };
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: tempFilterData,
      })
    );
  }, [attachmentType]);

  useEffect(() => {
      var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");
  
      let tempFilterData = {
        ...FilterSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: tempFilterData,
        })
      );
    }, [selectedCreatedBy]);

  const requestTypeOptions = [
    "Create",
    "Change",
    "Extend",
    "Create with Upload",
    "Change with Upload",
    "Extend with Upload",
    "Finance Costing",
  ];

  const handleClear = () => {
    setDocType([]);
    setRequestType([]);
    setSelectedCreatedBy([]);
    setAttachmentType([]);
    dispatch(
      commonFilterUpdate({
        module: "DocumentManagement",
        filterData: {
          ...FilterSearchForm,
          transactionId: "",
          transactionType: [],
          docType: "",
          attType: "",
          uploadedBy: "",
          childRequestId: "",
          createdBy: "",
          number: "",
          requestType: "",
          uploadedDate: [backDate, presentDate]
        },
      })
    );
    dispatch(
      commonFilterClear({
        module: "DocumentManagement",
        days: 7,
      })
    );
    setClearClicked(true);
  };

  const handleDate = (e) => {
    
    if (e !== null) {
      const uploadedDate = e.map(date =>
        new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()))
      );
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: {
            ...FilterSearchForm,
            uploadedDate: uploadedDate,
          },
        })
      );
    }
  };

  const getDocType = () => {
    const hSuccess = (data) => {
      setDocOptions(data?.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.GET_FILE_TYPE}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAttachmentType = () => {
    const hSuccess = (data) => {
      setAttachmentOptions(data?.body);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_DocumentManagement}/${END_POINTS.DMS_API.GET_ATTACHMENT_TYPE}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleInputChange = (e) => {
    if (e.target.value !== null) {
      const fieldName = e.target.name;
      const value = e.target.value;
  
      let tempFilterData = {
        ...FilterSearchForm,
        [fieldName]: value,
      };
      
      dispatch(
        commonFilterUpdate({
          module: "DocumentManagement",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleSelectAll = (selectedValues, options, setValues) => {
  if (selectedValues.length === options.length) {
    setValues([]);
  } else {
    setValues(options);
  }
};

  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === createdByOptions.length) {
      setSelectedCreatedBy([]);
    } else {
      setSelectedCreatedBy(createdByOptions);
    }
  };

  const handleCreatedByInputChange = (e) => {
    const inputValue = e.target.value;
    if (inputValue == "") {
      return;
    }
    if (timerId) {
      clearTimeout(timerId);
    }

    const newTimerId = setTimeout(() => {
      getCreatedBy(inputValue);
    }, 500);

    setTimerId(newTimerId);
  };

  const getCreatedBy = (inputValue) => {
      setIsDropDownLoading(true);
      let payload = {
        createdBy: inputValue,
      };
      const hSuccess = (data) => {
        setIsDropDownLoading(false);
        setCreatedByOptions(data.body);
      };
      const hError = (error) => {
        setIsDropDownLoading(false);
        customError(error);
      };
      doAjax(`/${destination_MaterialMgmt}/${END_POINTS.REQUEST_BENCH.CREATED_BY}`, "post", hSuccess, hError, payload);
    };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some((selectedOption) => selectedOption?.code === option?.code);
  };

  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: theme.palette.primary.dark }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            className="filterDM"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark }} />
            <Typography
              sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: theme.palette.primary.dark,
              }}
            >
              {t("Filter Document")}
            </Typography>
          </StyledAccordionSummary>
          <AccordionDetails sx={{ padding: 0 }}>
            <FilterContainer container spacing={0.5}>
              {searchResponse?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                .map((item, index) => {
                  return (
                    <React.Fragment key={index}>
                      {item?.MDG_MAT_FIELD_TYPE === SEARCH_FIELD_TYPES.INPUT && 
                        <Grid item md={2}>
                          <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                          <StyledTextField sx={font_Small} size="small" name={item?.MDG_MAT_JSON_FIELD_NAME} fullWidth onChange={handleInputChange} placeholder={t(`ENTER ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()} value={FilterSearchForm?.[item?.MDG_MAT_JSON_FIELD_NAME]} />
                        </Grid>
                      }
                      {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.REQUESTTYPE &&
                        <Grid item md={2}>
                        <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                        <AutoCompleteSimpleDropDown
                          options={requestTypeOptions.filter(name => name !== "Select All")}
                          value={requestType}
                          onChange={(value) => {
                            if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                              handleSelectAll(requestType, requestTypeOptions, setRequestType);
                            } else {
                              setRequestType(value);
                            }
                          }}
                          placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                        />
                      </Grid>
                      }
                      {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.DOCTYPE &&
                        <Grid item md={2}>
                        <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                          <AutoCompleteSimpleDropDown
                            options={[
                              ...docOptions.filter((name) => name !== "Select All"),
                            ]}
                            value={docType}
                            onChange={(value) => {
                              if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                                handleSelectAll(docType, docOptions, setDocType);
                              } else {
                                setDocType(value);
                              }
                            }}
                            placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                          />
                      </Grid>
                      }
                      {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.ATTACHMENTTYPE &&
                        <Grid item md={2}>
                        <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                          <AutoCompleteSimpleDropDown
                            options={[
                              ...attachmentOptions.filter((name) => name !== "Select All"),
                            ]}
                            value={attachmentType}
                            onChange={(value) => {
                              if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                                handleSelectAll(attachmentType, attachmentOptions, setAttachmentType);
                              } else {
                                setAttachmentType(value);
                              }
                            }}
                            placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                          />
                      </Grid>
                      }
                      {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.UPLOADEDBY &&
                      (
                        <>
                        {userRoles.includes(`${ROLES.SUPER_USER}`) ? (
                        <Grid item md={3}>
                          <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                          <FormControl fullWidth size="small">
                            <Autocomplete
                              fullWidth
                              size="small"
                              value={selectedCreatedBy}
                              multiple
                              disableCloseOnSelect
                              limitTags={1}
                              noOptionsText={
                                isDropDownLoading ? (
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "center",
                                      mt: 1,
                                      zIndex: 9999,
                                      top: "10px",
                                    }}
                                  >
                                    <CircularProgress size={20} />
                                  </Box>
                                ) : (
                                  t("No Data Available")
                                )
                              }
                              onChange={(e, value, reason) => {
                                if (reason === "clear" || value?.length === 0) {
                                  setSelectedCreatedBy([]);
                                  // TO BE USED LATER
                                  //setselectedPreset([]);
                                  return;
                                }

                                if (value.length > 0 && value[value.length - 1]?.code === "Select All") {
                                  handleSelectAllCreatedBy();
                                } else {
                                  setSelectedCreatedBy(value);
                                }
                              }}
                              options={createdByOptions?.length ? [{ code: "Select All", desc: "" }, ...createdByOptions] : createdByOptions ?? []}
                              getOptionLabel={(option) => {
                                if (option?.code) return `${option?.code} - ${option?.desc}` ?? "";
                                else return "";
                              }}
                              renderOption={(props, option, { selected }) => (
                                <li {...props}>
                                  <FormGroup>
                                    <FormControlLabel
                                      control={<Checkbox checked={isCreatedBySelected(option) || (option?.code === "Select All" && selectedCreatedBy?.length === createdByOptions?.length)} />}
                                      label={
                                        <Typography style={{ fontSize: 12 }}>
                                          {option?.desc ? (
                                            <>
                                              <strong>{option.code}</strong> - {option.desc}
                                            </>
                                          ) : (
                                            <strong>{option.code}</strong>
                                          )}
                                        </Typography>
                                      }
                                    />
                                  </FormGroup>
                                </li>
                              )}
                              renderTags={(selected, getTagProps) => {
                                const selectedOptionsText = selected.map((option) => `${option.code} `).join("<br />");
                                return selected.length > 1 ? (
                                  <>
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${selected[0]?.code}`}
                                      {...getTagProps({ index: 0 })}
                                    />
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`+${selected.length - 1}`}
                                      onMouseEnter={(event) => handlePopoverOpen(event, selectedOptionsText)}
                                      onMouseLeave={handlePopoverClose}
                                    />
                                    <Popover
                                      id={popoverId}
                                      open={isPopoverVisible}
                                      anchorEl={popoverAnchorEl}
                                      onClose={handlePopoverClose}
                                      anchorOrigin={{
                                        vertical: "bottom",
                                        horizontal: "center",
                                      }}
                                      transformOrigin={{
                                        vertical: "top",
                                        horizontal: "center",
                                      }}
                                      onMouseEnter={handleMouseEnterPopover}
                                      onMouseLeave={handleMouseLeavePopover}
                                      ref={popoverRef}
                                      sx={{
                                        "& .MuiPopover-paper": {
                                          backgroundColor: "#f5f5f5",
                                          boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                                          borderRadius: "8px",
                                          padding: "10px",
                                          fontSize: "0.875rem",
                                          color: "#4791db",
                                          border: "1px solid #ddd",
                                        },
                                      }}
                                    >
                                      <Box
                                        sx={{
                                          maxHeight: "270px",
                                          overflowY: "auto",
                                          padding: "5px",
                                        }}
                                        dangerouslySetInnerHTML={{ __html: popoverContent }}
                                      />
                                    </Popover>
                                  </>
                                ) : (
                                  selected.map((option, index) => (
                                    <Chip
                                      sx={{
                                        height: 25,
                                        fontSize: "0.85rem",
                                        ".MuiChip-label": { padding: "0 6px" },
                                      }}
                                      label={`${option?.code}`}
                                      {...getTagProps({ index })}
                                    />
                                  ))
                                );
                              }}
                              renderInput={(params) => (
                                <Tooltip title={params.inputProps.value.length == 0 ? "Type to search" : ""} arrow disableHoverListener={params.inputProps.value.length >= 1} placement="top">
                                  <TextField
                                    sx={{
                                      fontSize: "12px !important",
                                      "& .MuiOutlinedInput-root": {
                                        height: 35,
                                      },
                                      "& .MuiInputBase-input": {
                                        padding: "10px 14px",
                                      },
                                    }}
                                    {...params}
                                    variant="outlined"
                                    placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                                    onChange={(e) => {
                                      handleCreatedByInputChange(e);
                                    }}
                                  />
                                </Tooltip>
                              )}
                            />
                          </FormControl>
                        </Grid>
                      ) : (
                        <Grid item md={3}>
                          <LabelTypography sx={font_Small}>Uploaded By</LabelTypography>

                          <TextField fullWidth size="small" disabled={true} value={userData?.emailId} onChange={() => {}} placeholder={t("ENTER UPLOADED BY")} />
                        </Grid>
                      )}
                        </>
                      )
                      }
                      {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.UPLOADEDDATE &&
                        <Grid item md={2}>
                          <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                          <FormControl fullWidth size="small">
                            <LocalizationProvider dateAdapter={AdapterDateFns}>
                              <DateRange
                                handleDate={handleDate}
                                date={FilterSearchForm?.uploadedDate}
                              />
                            </LocalizationProvider>
                          </FormControl>
                        </Grid>
                      }

                    </React.Fragment>
                  )
                })
              }
            </FilterContainer>
            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleClear}
              >
                {t("Clear")}
              </ActionButton>

              <ActionButton
                variant="contained"
                size="small"
                startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleSearch}
              >
                {t("Search")}
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default DocumentFilter;
