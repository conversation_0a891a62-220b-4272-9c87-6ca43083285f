import { useDispatch } from "react-redux";
import { message } from "antd";
import { useCallback } from "react";

import { setTreeData, updateTreeChanges } from "@app/hierarchyDataSlice";
 import useDuplicateCheckHierarchy from "../../../hooks/useDuplicateCheckHierarchy"

const useAddOperation = ({
  form,
  editForm,
  tagForm, // ✅
  rawTreeData,
  currentParent,
  currentEditNode,
  currentTagParent, // ✅
  object,
  moduleObject,
  requestorPayload,
  reduxPayload,
  MODULE_KEY_MAP,
  setIsModalVisible,
  setIsEditModalVisible,
  setIsTagModalVisible, // ✅
  setExpandedKeys,
  addToChangeLog,
  treeChanges,
  setIsAddingNode,
  setIsAddingTag,
  setIsEditingDescription,
}) => {
  const dispatch = useDispatch();
  const {
    checkForNodeDuplicacy,
    checkForDescriptionDuplicacy,
    checkForObjectDuplicacy,
  } = useDuplicateCheckHierarchy();
  const findLabel = (node, label) => {
    if (node.label === label) return true;
    if (node.child && node.child.length > 0) {
      for (let i = 0; i < node.child.length; i++) {
        if (findLabel(node.child[i], label)) return true;
      }
    }
    return false;
  };

  const generateNextChildId = (parentNode) => {
    if (!parentNode.child || parentNode.child.length === 0) {
      return `${parentNode.id}_0`;
    }
    const childNumbers = parentNode.child.map((child) => {
      const parts = child.id.split("_");
      return parseInt(parts[parts.length - 1], 10);
    });
    const maxChildNumber = Math.max(...childNumbers);
    return `${parentNode.id}_${maxChildNumber + 1}`;
  };

  const addChildNode = (nodes, parentId, newNode) => {
    return nodes.map((node) => {
      if (node.id === parentId) {
        return {
          ...node,
          child: [...(node.child || []), newNode],
        };
      }
      if (node.child) {
        return {
          ...node,
          child: addChildNode(node.child, parentId, newNode),
        };
      }
      return node;
    });
  };

  // ✅ Add Node
  const handleAddNode = useCallback(async () => {
    setIsAddingNode(true); 
    try {
      const values = await form.validateFields();
      const { label, description } = values;

      if (findLabel(rawTreeData[0], label)) {
        message.error(`Node "${label}" already exists in the hierarchy!`);
        setIsAddingNode(false);
        return;
      }

      if (findDuplicateDescription(rawTreeData[0], description)) {
        message.error(`Description "${description}" already exists in the hierarchy!`);
        setIsAddingNode(false);
        return;
      }

      const controllingArea =
        requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
        reduxPayload?.ControllingArea ||
        "";

      const duplicateResponse = await checkForNodeDuplicacy(label, object, controllingArea, "", "");

      if (duplicateResponse?.body?.isDbDuplicate) {
        message.error(`Node "${label}" already exists in some ongoing request!`);
        setIsAddingNode(false);
        return;
      }

      if (
        duplicateResponse?.body?.PresentInHier === "X" ||
        duplicateResponse?.body?.PresentInCA === "X" ||
        duplicateResponse?.body?.PresentInCOA === "X"
      ) {
        message.error(`Node "${label}" already exists in the hierarchy!`);
        setIsAddingNode(false);
        return;
      }

      const duplicateResponseForDesc = await checkForDescriptionDuplicacy(
        description,
        object,
        controllingArea,
        "",
        ""
      );

      if (
        Object.keys(duplicateResponseForDesc.body).length !== 0 &&
        duplicateResponseForDesc?.body?.isDbDuplicate === true
      ) {
        message.error(`Description "${description}" already present in some ongoing request!`);
        setIsAddingNode(false);
        return;
      }

      dispatch(
        updateTreeChanges({
          nodeLabel: label,
          changes: {
            isNewNode: true,
            isMoved: false,
            tags: [],
            oldParentNode: currentParent?.label,
            newParentNode: null,
            description,
          },
        })
      );

      const newNode = {
        id: currentParent
          ? generateNextChildId(currentParent)
          : `0_${rawTreeData.length}`,
        label,
        description,
        isParent: true,
        child: [],
      };

      const updatedTreeData = currentParent
        ? addChildNode(rawTreeData, currentParent.id, newNode)
        : [...rawTreeData, newNode];

      addToChangeLog("ADD NODE", `${label} added under ${currentParent?.label}`);
      dispatch(setTreeData(updatedTreeData));
      setIsModalVisible(false);
      form.resetFields();

      if (currentParent) {
        setExpandedKeys((prev) => [...prev, currentParent.id]);
      }
      setIsAddingNode(false);
      message.success(`${label} added successfully`);
    } catch (error) {
      setIsAddingNode(false);
      console.error("Validation failed:", error);
    }
  }, [
    form,
    rawTreeData,
    currentParent,
    object,
    requestorPayload,
    reduxPayload,
    MODULE_KEY_MAP,
    setIsModalVisible,
    setExpandedKeys,
    addToChangeLog,
    dispatch,
    setIsAddingNode,
    setIsAddingTag,
    setIsEditingDescription,
  ]);

   //Check for duplicate description in UI
  const findDuplicateDescription = (tree, description) => {
    if (tree.description === description) {
      return true;
    }

    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findDuplicateDescription(tree.child[i], description)) {
          return true;
        }
      }
    }

    return false;
  };

    // Helper to update node description
  const updateNodeDescription = (nodes, nodeId, newDescription) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          description: newDescription,
        };
      }
      if (node.child) {
        return {
          ...node,
          child: updateNodeDescription(node.child, nodeId, newDescription),
        };
      }
      return node;
    });
  };

  // ✅ Edit Description
  const handleEditDescription = useCallback(async () => {
    setIsEditingDescription(true);
    try {
      const values = await editForm.validateFields();
      const updatedDescription = values.description;

      if (findDuplicateDescription(rawTreeData[0], updatedDescription)) {
        message.error(`Description "${updatedDescription}" already exists in the hierarchy!`);
        setIsEditingDescription(false);
        return;
      }

      const controllingArea =
        requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
        reduxPayload?.ControllingArea ||
        "";

      const duplicateDescResponse = await checkForDescriptionDuplicacy(
        currentEditNode.label,
        object,
        controllingArea,
        "",
        ""
      );

      if (
        Object.keys(duplicateDescResponse.body).length !== 0 &&
        duplicateDescResponse?.body?.isDbDuplicate === true
      ) {
        message.error(`Description "${updatedDescription}" already present in some ongoing request!`);
        setIsEditingDescription(false);
        return;
      }

      const existingNode = treeChanges?.[currentEditNode?.label];

      if (existingNode) {
        dispatch(
          updateTreeChanges({
            nodeLabel: currentEditNode.label,
            changes: {
              description: updatedDescription,
            },
          })
        );
      }
      else{
         dispatch(
          updateTreeChanges({
            nodeLabel: currentEditNode.label,
            changes: {
              description: updatedDescription,
            },
          })
        );
      }

      const updatedTreeData = updateNodeDescription(
        rawTreeData,
        currentEditNode.id,
        updatedDescription
      );

      addToChangeLog(
        "CHANGED DESCRIPTION",
        `${currentEditNode.label} Node description changed from ${currentEditNode.description} to ${updatedDescription}`
      );

      dispatch(setTreeData(updatedTreeData));
      setIsEditModalVisible(false);
      editForm.resetFields();
      setIsEditingDescription(false);
      message.success("Description updated successfully");
    } catch (error) {
      setIsEditingDescription(false);
      console.error("Validation failed:", error);
    }
  }, [
    editForm,
    rawTreeData,
    currentEditNode,
    object,
    moduleObject,
    requestorPayload,
    reduxPayload,
    MODULE_KEY_MAP,
    treeChanges,
    dispatch,
    setIsEditModalVisible,
    addToChangeLog,
  ]);



    const findTag = (tree, tag) => {
    if (tree.tags && tree.tags.includes(tag)) {
      return true;
    }
    if (tree.child && tree.child.length > 0) {
      for (let i = 0; i < tree.child.length; i++) {
        if (findTag(tree.child[i], tag)) {
          return true;
        }
      }
    }
    return false;
  };


    // Helper to add tag to node
  const addTagToNode = (nodes, nodeId, newTag) => {
    return nodes.map((node) => {
      if (node.id === nodeId) {
        return {
          ...node,
          tags: [...(node.tags || []), newTag],
        };
      }
      if (node.child) {
        return { ...node, child: addTagToNode(node.child, nodeId, newTag) };
      }
      return node;
    });
  };
  
  const handleAddTag = useCallback(async () => {
    setIsAddingTag(true);
  try {
    const values = await tagForm.validateFields();
    const newTag = values.tag;

    if (findTag(rawTreeData[0], newTag)) {
      message.error(`${moduleObject} "${newTag}" already exists in the hierarchy!`);
      setIsAddingTag(false);
      return;
    }

    const duplicateResponse = await checkForObjectDuplicacy(
      newTag,
      object,
      requestorPayload[MODULE_KEY_MAP?.[object]?.CTRL_AREA]?.[0]?.code ||
        reduxPayload?.ControllingArea ||
        "",
      requestorPayload[MODULE_KEY_MAP?.[object]?.COA]?.[0]?.code ||
        reduxPayload?.ChartOfAccount ||
        "",
      requestorPayload?.[MODULE_KEY_MAP?.[object]?.CTR_GRP]?.[0]?.code ||
        requestorPayload?.[MODULE_KEY_MAP?.[object]?.CTR_GRP] ||
        reduxPayload?.ParentNode ||
        ""
    );

    if (
      duplicateResponse?.body.PresentInCA !== "X" &&
      duplicateResponse?.body.PresentInCOA !== "X"
    ) {
      message.error(`Invalid ${moduleObject}`);
      setIsAddingTag(false);
      return;
    }

    if (duplicateResponse?.body?.isDuplicate) {
      message.error(`${moduleObject} "${newTag}" already exists in the database!`);
      setIsAddingTag(false);
      return;
    }

    if (duplicateResponse.body.PresentInHier === "X") {
      message.error(`${moduleObject} "${newTag}" already exists in the hierarchy!`);
      setIsAddingTag(false);
      return;
    }

    const updatedTreeData = addTagToNode(
      rawTreeData,
      currentTagParent.id,
      newTag
    );

    addToChangeLog(
      `ADD ${moduleObject}`,
      `${newTag} added to ${currentTagParent?.label}`
    );

    const currentNode = treeChanges?.[currentTagParent?.label] || {};
    const currentTags = Array.isArray(currentNode.tags) ? currentNode.tags : [];

    const updatedTags = currentTags?.includes(newTag)
      ? currentTags
      : [...currentTags, newTag];

    dispatch(
      updateTreeChanges({
        nodeLabel: currentTagParent?.label,
        changes: {
          tags: updatedTags,
        },
      })
    );

    dispatch(setTreeData(updatedTreeData));
    setIsTagModalVisible(false);
    tagForm.resetFields();
    setExpandedKeys((prev) => [...prev, currentTagParent.id]);
    setIsAddingTag(false);
    message.success(`${newTag} added successfully`);
  } catch (error) {
    setIsAddingTag(false);
    console.error("Validation failed:", error);
  }
}, [
  tagForm,
  rawTreeData,
  currentTagParent,
  object,
  moduleObject,
  requestorPayload,
  reduxPayload,
  MODULE_KEY_MAP,
  addToChangeLog,
  treeChanges,
  dispatch,
  setIsTagModalVisible,
  setExpandedKeys,
]);


  return {
    handleAddNode,
    handleEditDescription,
    handleAddTag,
  };
};


export default useAddOperation;