import{n as R,s as Jn,u as Yn,C as F,bI as Ao,aT as Nt,aJ as ln,aO as Je,r as o,j as n,aR as yr,g as Co,a as To,c as p,aa as ho,B as O,bU as te,y$ as Zo,z0 as zn,z1 as er,z2 as Dr,aF as Po,ai as Mn,aj as lo,al as In,d as De,L as xo,gr as pr,gq as co,h as No,F as an,f as mr,an as Ue,am as _n,aX as D,aG as Kn,af as uo,ag as Un,z3 as ro,a9 as fo,z4 as tr,z5 as nr,z6 as Ar,fC as po,bK as Fe,fP as Te,fQ as ve,aM as or,ae as Ro,z7 as xr,z8 as Nr,eX as st,O as xt,b1 as Rr,aZ as Ir,aD as _r,z9 as Pr,ap as Cr,za as Or,cz as Lr,zb as Vn,aY as vr,aP as kr,zc as wr,zd as so,ze as Eo,zf as rr,T as ao,a6 as Xn,$ as Mr,b<PERSON> as Ur,bP as Br,bQ as sr,bG as ar,bl as Wr,bm as qr,bn as $r,bo as ir,bp as io,bq as Gr,Z as Ge,b6 as lr,b5 as cr,zg as sn,t as dr,xH as Fr,bf as Rn,zh as kn,fB as mo,aK as ur,zi as Hr,eZ as zr,bJ as go,dn as jr,bc as gr,zj as Vr,y3 as Xr,bd as Kr,xY as Jr,zk as Yr,zl as Qr,zm as Zr,zn as es,cI as jn,d6 as ts,d7 as ns,br as os,ad as rs,d8 as ss,a$ as as,d3 as is,d4 as ls,d5 as cs,d9 as ds,fY as us,wQ as gs,wR as hr,wS as hs,zo as fs,zp as ps,zq as ms,zr as Cs,c3 as Ts,d0 as Ss,c4 as bs}from"./index-226a1e75.js";import{u as Tr,F as Es}from"./FilterFieldGlobal-b5a561ef.js";import{S as Oo,D as Io,B as Bn,A as ys,P as Ds}from"./PreviewPage-262cf4cb.js";import{d as _o}from"./FeedOutlined-2c089703.js";import{F as wn}from"./FilterChangeDropdown-2d228e28.js";import{u as Sr,E as As}from"./ErrorReportDialog-e2a11116.js";import{S as pt}from"./SingleSelectDropdown-ee61a6b7.js";import{u as Lo}from"./useProfitCenterChangeFieldConfig-8887057c.js";import{g as xs}from"./fieldHelper-f0c04ddb.js";import{O as Ns,C as Rs}from"./ChangeLogGL-eb869bb7.js";import{u as Is}from"./useProfitcenterRequestHeaderConfig-b7f65f7d.js";import{u as _s}from"./UseProfitCenterFieldConfig-9f979553.js";import{d as Ps}from"./TaskAlt-9d2cabf1.js";import{G as fr}from"./GenericTabsGlobal-6faba7da.js";import{d as yo,a as Do}from"./CloseFullscreen-e3b32947.js";import{d as Os}from"./PermIdentityOutlined-842d404f.js";import{d as Ls}from"./TrackChangesTwoTone-f7d7fb26.js";import{d as vs}from"./FileUploadOutlined-3ff8ee58.js";import{E as ks}from"./ExcelOperationsCard-3cc40005.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./AttachFile-fd8e4fbe.js";import"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";import"./CloudUpload-17ed0189.js";import"./utilityImages-067c3dc2.js";import"./Delete-3f2fc9ef.js";import"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";import"./Description-d98685cc.js";import"./DataObject-2e0c0294.js";import"./Download-f2e7dedd.js";import"./useFinanceCostingRows-699f667f.js";import"./CheckCircleOutline-70edf41f.js";import"./lz-string-127b8448.js";import"./ErrorHistory-e3f4447c.js";import"./CloudDownload-23cede9e.js";import"./AttachmentUploadDialog-5b2112e0.js";const br=()=>{R(H=>H.payload.payloadData);const pe=R(H=>H.applicationConfig);R(H=>{var Ne;return(Ne=H.userManagement)==null?void 0:Ne.taskData}),Jn();const Me=Yn();return new URLSearchParams(Me.search).get("RequestType"),{getDynamicWorkflowDT:(H,Ne=1,de,Re,b)=>new Promise((U,A)=>{let i={decisionTableId:null,decisionTableName:Re,version:de,conditions:[{"MDG_CONDITIONS.MDG_PC_REQUEST_TYPE":"Create or Other Changes","MDG_CONDITIONS.MDG_PC_SEGMENT":H||"NA"}]};const z=W=>{var mt,me,d,it,x;if(W.statusCode===ln.STATUS_200){let Ie=((me=(mt=W==null?void 0:W.data)==null?void 0:mt.result[0])==null?void 0:me.MDG_MAT_DYNAMIC_WF_DT)||[];if(b===((d=Je)==null?void 0:d.BK)){Ie=((x=(it=W==null?void 0:W.data)==null?void 0:it.result[0])==null?void 0:x.MDG_BNKY_DYNAMIC_WORKFLOW_DT_ACTION_TYPE)||[];const He=new Map;Ie.forEach(Se=>{if(Se.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(Ne)){const S=Se.MDG_MAT_SENDBACK_ALLOWED;typeof S=="string"&&S.trim().length>0&&S.split(",").map(re=>{const $=re.trim().match(/^(-?\d+)-(.*)$/);return $?{key:parseInt($[1],10),Name:$[2].trim()}:null}).filter(Boolean).forEach(({key:re,Name:$})=>{He.has(re)||He.set(re,$)})}});const Rt=Array.from(He,([Se,S])=>({key:Se,Name:S}));U(Rt);return}let q=[];Ie==null||Ie.forEach(He=>{He.MDG_DYNAMIC_WF_APPROVAL_LEVEL===parseInt(Ne)&&He.MDG_MAT_SENDBACK_ALLOWED.split(",").map(Se=>parseInt(Se)).forEach(Se=>q.push(Se))}),q=[...new Set(q)],U(q)}else A(new Error("Failed to fetch workflow levels"))},Z=W=>{A(W)};pe.environment==="localhost"?F(`/${Ao}${Nt.INVOKE_RULES.LOCAL}`,"post",z,Z,i):F(`/${Ao}${Nt.INVOKE_RULES.PROD}`,"post",z,Z,i)})}},ws=o.forwardRef(function(Me,ge){return n(yr,{direction:"down",ref:ge,...Me})}),Er=({reqBench:pe,requestId:Me,apiResponses:ge,downloadClicked:at,setDownloadClicked:H,module:Ne,template:de,isDisabled:Re,fieldDisable:b})=>{var on,Zn,eo,to,Tn,no,$n,oo;const U=Co(),A=Jn(),{t:i}=To(),{fetchedProfitCenterData:z,fetchReqBenchData:Z,changedFieldsMap:W}=R(t=>t.profitCenter),mt=R(t=>t.changeLog.createChangeLogDataGL),me=R(t=>t.profitCenter.showGrid),d=R(t=>t.profitCenter.payload.requestHeaderData),it=R(t=>t.request.requestHeader);Lo("Profit Center");const x=R(t=>t.request.requestHeader),Ie=R(t=>t.payload.filteredButtons),q=R(t=>t==null?void 0:t.userManagement.taskData),He=Yn(),Se=new URLSearchParams(He.search).get("RequestId"),{updateChangeLogGlForChange:S}=Tr(),[It,re]=o.useState(!0),[$,Ye]=o.useState("");o.useState("");const[se,cn]=o.useState(null),[ae,h]=o.useState([]),[Qe,tt]=o.useState(!1),[be,Ce]=o.useState([]),[Ct,Tt]=o.useState([]),[he,nt]=o.useState([]),[lt,Wt]=o.useState(!1),[qt,_e]=o.useState(!1),[$t,ut]=o.useState(""),[gt,Ae]=o.useState(""),[Be,ot]=o.useState(""),[rt,Gt]=o.useState("systemGenerated"),[Kt,dn]=o.useState([]),[_t,St]=o.useState({});o.useState(""),o.useState([]);const[Jt,G]=o.useState([]);o.useState([]);const[ze,We]=o.useState([]),[Ee,ct]=o.useState([]),[j,un]=o.useState(!1),[Pt,Ot]=o.useState(""),[v,ie]=o.useState("success");o.useState(!1),o.useState(!1);const[oe,je]=o.useState([]),[ye,Ve]=o.useState(!1),[le,bt]=o.useState([]),[Pe,Ft]=o.useState(""),[Wn,Pn]=o.useState([]),[gn,hn]=o.useState(!1),[Yt,Et]=o.useState(!1),[ht,Lt]=o.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),{getDynamicWorkflowDT:Qt}=br();o.useEffect(()=>{const t=async()=>{var s,l;try{const f=await Qt((s=rowsHeaderData==null?void 0:rowsHeaderData[0])==null?void 0:s.businessSegment,q==null?void 0:q.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_PC_DT",(l=Je)==null?void 0:l.PC);bt(f)}catch(f){customError(f)}};d!=null&&d.Region&&(q!=null&&q.ATTRIBUTE_3)&&t()},[d==null?void 0:d.Region,q==null?void 0:q.ATTRIBUTE_3]);const{getButtonsDisplayGlobal:Oe,showWfLevels:Ht}=Sr(),V=R(t=>{var s;return(s=t==null?void 0:t.payload)==null?void 0:s.changeFieldSelectiondata}),fn=Array.isArray(V)?V:[],zt=d==null?void 0:d.FieldName,pn=(Zn=(on=ge==null?void 0:ge[0])==null?void 0:on.Torequestheaderdata)==null?void 0:Zn.FieldName,Ze=pe?pn:zt,jt=Array.isArray(Ze)?Ze.map(t=>t.trim()):typeof Ze=="string"?Ze.split(",").map(t=>t.trim()):[],{allFields:yt,mandatoryFields:vt,headerFields:On,fieldMetaMap:Vt}=xs(fn,jt);o.useEffect(()=>{(q!=null&&q.ATTRIBUTE_1||Se)&&Oe("Profit Center","MDG_DYN_BTN_DT","v3")},[q]);const Dt=()=>{re(!1),H(!1),U("/requestbench")},kt=t=>{if(!he.length||!$)return;const s=he==null?void 0:he.map(C=>({controllingArea:$,profitCenter:C,changedFieldsToCheck:(x==null?void 0:x.fieldName)??(d==null?void 0:d.FieldName)})),l=C=>{if(!(C==null?void 0:C.some(y=>(y==null?void 0:y.statusCode)!==200)))t==="OK"?(re(!1),pe!=="true"&&A(xr(!0)),wt()):t==="Download"&&Ln();else{const y=C.filter(I=>I.statusCode===400);let _=[];y==null||y.forEach((I,K)=>{var ce,At,Bt,Sn,vn,bn,En,yn,Dn,Le,rn;const J={id:`${(ce=I==null?void 0:I.body)==null?void 0:ce.profitCenter}_${K}`,objectNo:(At=I==null?void 0:I.body)==null?void 0:At.profitCenter,reqId:(vn=(Sn=(Bt=I==null?void 0:I.body)==null?void 0:Bt.matchingRequests)==null?void 0:Sn.map(Ke=>Ke==null?void 0:Ke.matchingRequestHeaderId))==null?void 0:vn.filter(Boolean),childReqId:(yn=(En=(bn=I==null?void 0:I.body)==null?void 0:bn.matchingRequests)==null?void 0:En.map(Ke=>Ke==null?void 0:Ke.matchingChildHeaderIdsSet))==null?void 0:yn.filter(Boolean),requestedBy:(rn=(Le=(Dn=I==null?void 0:I.body)==null?void 0:Dn.matchingRequests)==null?void 0:Le.map(Ke=>Ke==null?void 0:Ke.RequestCreatedBy))==null?void 0:rn.filter(Boolean)};_.push(J)}),je(_),Ve(!0)}},f=C=>{console.error("Failed to fetch profit center details",C)};F(`/${te}/alter/checkDuplicatePCRequest`,"post",l,f,s)},mn=t=>{kt(t)},g=(t,s,l)=>{const f=t==null?void 0:t.code;S({uniqueId:s.row.id,jsonName:s==null?void 0:s.field,currentValue:f,requestId:x==null?void 0:x.RequestId,childRequestId:Me});const C={...s.row,[s.field]:f};A(pe?nr(C):tr(C))},k=[{field:"included",width:80,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderHeader:()=>{const t=pe?Z:z,s=t.every(f=>f.included),l=t.some(f=>f.included);return n(fo,{checked:s,indeterminate:!s&&l,disabled:Re||b,onChange:f=>{const C=t.map(B=>({...B,included:f.target.checked}));A(pe?zn(C):ro(C))}})},renderCell:t=>{var f;const s=pe?Z:z,l=s.findIndex(C=>C.id===t.row.id);return n(fo,{checked:((f=s[l])==null?void 0:f.included)||!1,disabled:Re||b,onChange:C=>{const B=[...s];B[l]={...B[l],included:C.target.checked},A(pe?zn(B):ro(B))}})}},{field:"lineNumber",headerName:"Sl No",width:70,align:"center",headerAlign:"center",renderCell:t=>{const s=(pe?Z:z).findIndex(l=>l.id===t.row.id);return n("div",{children:s+1})}},{field:"controllingArea",headerName:"Controlling Area",width:150,editable:!1,renderCell:t=>n("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:t.value||""})},{field:"companyCode",headerName:"Company Codes",width:150,editable:!1,renderCell:t=>n("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:t.value||""})},{field:"profitCenter",headerName:"Profit Center",width:150,editable:!1,renderCell:t=>n("span",{style:{color:"#9e9e9e",pointerEvents:"none",cursor:"default"},children:t.value||""})},{field:"profitCenterName",headerName:"Short Description",width:250,editable:!0},{field:"description",headerName:"Long Description",width:300,editable:!0},{field:"segment",headerName:"Segment",width:150,editable:!1,renderCell:t=>{const s=t.field,l=Jt||[],f=l.find(C=>C.code===t.row[s]||C.desc===t.row[s])||null;return n(pt,{options:l,value:f,onChange:C=>g(C,t),placeholder:`Select ${t.colDef.headerName}`,disabled:Re,minWidth:"90%",listWidth:235})}},{field:"businessSegment",headerName:"Business Segment",width:200,editable:!0,renderCell:t=>{const s=t.field,l=ze||[],f=l.find(C=>C.code===t.row[s]||C.desc===t.row[s])||null;return n(pt,{options:l,value:f,onChange:C=>g(C,t),placeholder:`Select ${t.colDef.headerName}`,disabled:Re,minWidth:"90%",listWidth:235})}},{field:"userResponsible",headerName:"PC User Responsible",width:250,editable:!0},{field:"personResponsible",headerName:"PC Person Responsible",width:250,editable:!0},{field:"lockIndicator",headerName:"Lock Indicator",width:150,editable:!1,renderCell:()=>{if((d==null?void 0:d.TemplateName)==="All Other Fields")return"Unblock";if((d==null?void 0:d.TemplateName)==="Block")return"Block"}},{field:"createdBy",headerName:"Created By",width:150},{field:"validFrom",headerName:"Valid From",width:150},{field:"validTo",headerName:"Valid To",width:150},{field:"city",headerName:"City",width:150,editable:!0},{field:"country",headerName:"Country/Reg.",width:150,editable:!0,renderCell:t=>{const s=Kt||[],l=s.find(f=>f.code===t.row.country||f.desc===t.row.country)||null;return n(pt,{options:s,value:l,onChange:f=>g(f,t),placeholder:"Select Country",disabled:Re,minWidth:"90%",listWidth:235})}},{field:"region",headerName:"Region",width:150,editable:!0,renderCell:t=>{const s=t.row.country;s&&!_t[s]&&ue(s);const l=_t[s]||[],f=l.find(C=>C.code===t.row.region)||null;return n(pt,{options:l,value:f,onChange:C=>g(C,t),placeholder:"Select Region",disabled:Re,minWidth:"90%",listWidth:235})}},{field:"street",headerName:"Street",width:150,editable:!0},{field:"pocode",headerName:"Postal Code",width:150,editable:!0},{field:"name1",headerName:"Name 1",width:150,editable:!0},{field:"name2",headerName:"Name 2",width:150,editable:!0},{field:"name3",headerName:"Name 3",width:150,editable:!0},{field:"name4",headerName:"Name 4",width:150,editable:!0}],ne=t=>s=>{const l=s.target.value.toUpperCase();t.api.setEditCellValue({id:t.id,field:t.field,value:l}),S({uniqueId:t.row.id,jsonName:t==null?void 0:t.field,currentValue:l,requestId:x==null?void 0:x.RequestId,childRequestId:Me})},Xe=k.slice(0,2),ee={field:"businessSegment",headerName:"Business Segment",width:200,editable:!0,renderCell:t=>{const s=t.field,l=ze||[],f=l.find(C=>C.code===t.row[s]||C.desc===t.row[s])||null;return n(pt,{options:l,value:f,onChange:C=>g(C,t),placeholder:"Select Business Segment",disabled:Re,minWidth:"90%",listWidth:235})},renderHeader:()=>p("span",{children:["Business Segment"," ",vt.includes("Business Segment")&&n("span",{style:{color:"red"},children:"*"})]})},N=k.slice(2).filter(t=>{var s;return yt.includes((s=t.headerName)==null?void 0:s.trim())}).map(t=>{var _,I;const s=(_=t.headerName)==null?void 0:_.trim(),l=vt.includes(s),f=["description","profitCenterName","personResponsible","city","street","pocode","name1","name2","name3","name4"].includes(t.field),C=(I=t.field)==null?void 0:I.toLowerCase(),y=(Vt[C]||{}).maxLength;return{...t,headerName:s,editable:!0,renderHeader:()=>l?p("span",{children:[s," ",n("span",{style:{color:"red"},children:"*"})]}):s,...f&&{renderCell:K=>{var J;return p(O,{sx:{position:"relative",width:"100%"},children:[n(ho,{value:K.value||"",variant:"outlined",size:"small",disabled:Re,onChange:ne(K),fullWidth:!0,InputProps:{style:{paddingBottom:"5px"}}}),p(O,{sx:{position:"absolute",bottom:0,left:14,fontSize:"8px",color:"blue",pointerEvents:"none"},children:[((J=K.value)==null?void 0:J.length)||0,"/",y]})]})},renderEditCell:K=>{const J=K.value||"",ce=J.length,At=ce>=y;return p(O,{sx:{position:"relative",width:"100%"},children:[n(ho,{value:J,autoFocus:!0,onFocus:Bt=>{Bt.target.setSelectionRange(0,Bt.target.value.length)},onChange:ne(K),variant:"outlined",size:"small",fullWidth:!0,placeholder:`Enter ${s}`,inputProps:{maxLength:y+1,style:{paddingBottom:"15px"}}}),n(O,{sx:{position:"absolute",bottom:0,left:14,fontSize:"9px",color:At?"red":"blue",pointerEvents:"none"},children:At?"Max length reached":`${ce}/${y}`})]})}}}}),X=[...Xe,...N,ee],L=[{field:"profitCenterName",headerName:"Short Description"},{field:"description",headerName:"Long Description"},{field:"personResponsible",headerName:"PC Person Responsible"}],ke=t=>(A(tr(t)),t),xe=t=>(A(nr(t)),t),qe=t=>{cn(t.row)};o.useEffect(()=>{$&&be.length>0&&Cn()},[$,be]),o.useEffect(()=>{we()},[]);const we=()=>{const t=l=>{h(l.body),A({type:"SET_DROPDOWN",payload:{keyName:"CompanyCode",data:l.body}})},s=l=>{console.log(l)};F(`/${te}/data/getCompCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,"get",t,s)},dt=()=>{const t=l=>{G(l.body),A({type:"SET_DROPDOWN",payload:{keyName:"Segment",data:l.body}})},s=l=>{console.log(l)};F(`/${te}/data/getSegment`,"get",t,s)},ft=()=>{const t=l=>{const f=(l.body||[]).map(C=>({code:C,desc:C}));We(f),A({type:"SET_DROPDOWN",payload:{keyName:"businessSegment",data:f}})},s=l=>{console.log(l)};F(`/${te}/data/getBusinessSegment`,"get",t,s)},$e=()=>{const t=l=>{dn(l.body||[]),A({type:"SET_DROPDOWN",payload:{keyName:"Country",data:l.body||[]}})},s=l=>{console.log(l)};F(`/${te}/data/getCountryOrReg`,"get",t,s)},ue=t=>{const s=f=>{St(C=>({...C,[t]:f.body||[]})),A({type:"SET_DROPDOWN",payload:{keyName:`Region_${t}`,data:f.body||[]}})},l=f=>{console.log(f)};F(`/${te}/data/getRegionBasedOnCountry?country=${t}`,"get",s,l)};o.useEffect(()=>{$e()},[]),o.useEffect(()=>{},[Kt]),o.useEffect(()=>{dt(),ft()},[]),o.useEffect(()=>{be.length===0?(Tt([]),nt([])):Cn(be)},[be]);const Cn=(t=[])=>{Tt([]);const s=`${$}`;t.forEach(l=>{const f={controllingArea:s,companyCode:l,top:"100",skip:"0"};F(`/${te}/data/getProfitCentersNo`,"post",C=>{var B;if(Array.isArray((B=C.body)==null?void 0:B.list)){const y=C.body.list.map(_=>({code:_.code,desc:_.desc,companyCode:l}));Tt(_=>{const I=new Set(_.map(J=>J.code)),K=y.filter(J=>!I.has(J.code));return[..._,...K]})}},C=>console.error("Profit Center fetch failed",C),f)})},et=t=>{const s=de==null?void 0:de.toLowerCase().includes("block");return t.map(l=>{var f,C,B,y,_,I,K,J,ce,At,Bt,Sn,vn,bn,En,yn,Dn,Le,rn,Ke;return{id:l.profitCenter,profitCenter:l.profitCenter,controllingArea:l.controllingArea,profitCenterName:l.profitCenterName,description:((f=l.basicDataTabDto)==null?void 0:f.Description)||"",segment:((C=l.basicDataTabDto)==null?void 0:C.Segment)||"",pcaamnum:((B=l.basicDataTabDto)==null?void 0:B.PCAAMNumber)||"",lockIndicator:s&&!((y=l==null?void 0:l.indicatorsTabDto)!=null&&y.LockIndicator)?"X":"",userResponsible:((_=l.basicDataTabDto)==null?void 0:_.UserResponsible)||"",personResponsible:((I=l.basicDataTabDto)==null?void 0:I.PersonResponsible)||"",createdBy:((K=l.historyTabDto)==null?void 0:K.ReqCreatedBy)||"",validFrom:((J=l.basicDataTabDto)==null?void 0:J.ValidfromDate)||"",validTo:((ce=l.basicDataTabDto)==null?void 0:ce.ValidtoDate)||"",city:((At=l.addressTabDto)==null?void 0:At.City)||"",country:((Bt=l.addressTabDto)==null?void 0:Bt.Country)||"",street:((Sn=l.addressTabDto)==null?void 0:Sn.Street)||"",pocode:((vn=l.addressTabDto)==null?void 0:vn.PostalCode)||"",region:((bn=l.addressTabDto)==null?void 0:bn.Regio)||"",name1:((En=l.addressTabDto)==null?void 0:En.Name1)||"",name2:((yn=l.addressTabDto)==null?void 0:yn.Name2)||"",name3:((Dn=l.addressTabDto)==null?void 0:Dn.Name3)||"",name4:((Le=l.addressTabDto)==null?void 0:Le.Name4)||"",companyCode:((Ke=(rn=l.compCodesTabDto)==null?void 0:rn.CompanyCode)==null?void 0:Ke[0])||""}})},wt=()=>{if(!he.length||!$)return;const t={coAreaPCs:he.map(f=>({profitCenter:f,controllingArea:$}))},s=f=>{const C=(f==null?void 0:f.body)||[];ct(C);const B=et(C);A(pe==="true"?zn(B):ro(B)),A(ro(B)),A(Nr(B))},l=f=>{console.error("Failed to fetch profit center details",f)};F(`/${te}/data/getProfitCentersData`,"post",s,l,t)};o.useEffect(()=>{if(pe==="true"&&de&&Array.isArray(ge)&&ge.length>0&&Z.length===0){const t=Zo(ge);A(zn(t)),A(er(t))}},[ge,pe,de]),o.useEffect(()=>{if(q&&Object.keys(q).length!==0&&de&&Array.isArray(ge)&&ge.length>0){const t=Zo(ge);A(zn(t)),A(er(t))}},[ge,q,de]),o.useEffect(()=>{at&&re(!0)},[at]);const Xt=(ge??[]).map(t=>{let s={};if(typeof t.changedFields=="object"&&t.changedFields!==null)s=t.changedFields;else if(typeof t.ChangedFields=="string")try{s=JSON.parse(t.ChangedFields)}catch{s={}}const{changedFields:l,ChangedFields:f,...C}=t;return{...C,changedFields:s}});o.useEffect(()=>{if(!Xt||Xt.length===0)return;const t={};Xt.forEach(s=>{t[s.ProfitCenterID]=s.changedFields||{}}),A(Dr(t))},[ge]);const fe=async(t,s)=>{var y,_,I,K,J;if(t===Ar.VALIDATE){Mt();return}Ae(!0);let l=(J=(K=(I=(y=Nt)==null?void 0:y.MASTER_BUTTON_APIS)==null?void 0:I[(_=Je)==null?void 0:_.PC])==null?void 0:K[d==null?void 0:d.RequestType])==null?void 0:J[t];const f=po(d,it,q,Z,z,mt,s),C=ce=>{Ae(!1),(ce==null?void 0:ce.statusCode)===200||(ce==null?void 0:ce.statusCode)===201?(Lt({title:Te.TITLE,message:ce.message,subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),Et(!0)):(ce==null?void 0:ce.statusCode)===500||(ce==null?void 0:ce.statusCode)===501?(Lt({title:ve.TITLE,message:ce.message,subText:ve.SUBTEXT,buttonText:ve.BUTTONTEXT,redirectTo:ve.REDIRECT}),Et(!0)):(setSnackbarOpen(!0),Ot("Unexpected response received."))},B=ce=>{Ae(!1),setSnackbarOpen(!0),Ot("Error occurred while validating the request"),console.error("Error saving draft:",ce)};F(l==null?void 0:l.URL,"POST",C,B,f)},Mt=t=>{Ae(!0);const s=Array.isArray(Z)&&Z.length>0?Z:Array.isArray(z)&&z.length>0?z:[],l=[];if(s.forEach((y,_)=>{const I=L.filter(({field:K})=>{const J=y==null?void 0:y[K];return J==null||J.toString().trim()===""});I.length>0&&l.push({rowIndex:_+1,rowId:(y==null?void 0:y.id)||(y==null?void 0:y.ProfitCenterID)||"N/A",missingHeaders:I.map(K=>K.headerName)})}),l.length>0){const y=l.flatMap(_=>_.missingHeaders.map(I=>`Line ${_.rowIndex} - ${I}`));Pn(y),hn(!0),Ae(!1);return}const f=po(d,it,q,Z,z,mt),C=y=>{Ae(!1),(y==null?void 0:y.statusCode)===ln.STATUS_200||(y==null?void 0:y.statusCode)===ln.STATUS_201?(Lt({title:Te.TITLE,message:y.message,subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),Et(!0)):(y==null?void 0:y.statusCode)===ln.STATUS_500||(y==null?void 0:y.statusCode)===STATUS_501?(Lt({title:ve.TITLE,message:y.message,subText:ve.SUBTEXT,buttonText:ve.BUTTONTEXT,redirectTo:ve.REDIRECT}),Et(!0)):(setSnackbarOpen(!0),Ot("Unexpected response received."))},B=y=>{Ae(!1),ie("error"),Ot("Error occurred while saving the draft."),un(!0)};F(`/${te}/massAction/validateMassProfitCenter`,"POST",C,B,f)},Zt=()=>{_e(!0)},qn=()=>{_e(!1)},Ln=()=>{tt(!0)},Y=()=>{tt(!1),Gt("systemGenerated")},en=t=>{var s;Gt((s=t==null?void 0:t.target)==null?void 0:s.value)},Ut=()=>{rt==="systemGenerated"&&(tn(),Y()),rt==="mailGenerated"&&(So(),Y())},tn=()=>{var f,C;ot("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),Ae(!0),re(!1),H(!1),Se||U((f=Fe)==null?void 0:f.REQUEST_BENCH);let t={coAreaPCs:he.map(B=>({profitCenter:B,controllingArea:$})),requestId:(d==null?void 0:d.RequestId)||(x==null?void 0:x.requestId)||"",templateHeaders:d!=null&&d.FieldName?(C=d.FieldName)==null?void 0:C.join("$^$"):"",templateName:d!=null&&d.TemplateName?d.TemplateName:"",dtName:"MDG_CHANGE_TEMPLATE_DT",version:"v6"};const s=B=>{var I;if((B==null?void 0:B.size)==0){Ae(!1),ot(""),or((I=Ro)==null?void 0:I.NO_DATA_FOUND,"error",{position:"top-center",largeWidth:!0}),setTimeout(()=>{var K;U((K=Fe)==null?void 0:K.REQUEST_BENCH)},2600);return}const y=URL.createObjectURL(B),_=document.createElement("a");_.href=y,_.setAttribute("download",`${d==null?void 0:d.TemplateName}_Mass Change.xlsx`),document.body.appendChild(_),_.click(),document.body.removeChild(_),URL.revokeObjectURL(y),Ae(!1),ot(""),Wt(!0),ut(`${d==null?void 0:d.TemplateName}_Mass Change.xlsx has been downloaded successfully.`),ie("success"),Zt(),setTimeout(()=>{var K;U((K=Fe)==null?void 0:K.REQUEST_BENCH)},2600)},l=()=>{var B;Ae(!1),ot(""),or((B=Ro)==null?void 0:B.ERR_DOWNLOADING_EXCEL,"error",{position:"top-center"}),setTimeout(()=>{var y;U((y=Fe)==null?void 0:y.REQUEST_BENCH)},2600)};F(`/${te}/excel/downloadExcelWithData`,"postandgetblob",s,l,t)},So=()=>{var C,B,y;Ae(!0),onClose();let t=((C=Templates[x==null?void 0:x.TemplateName])==null?void 0:C.map(_=>_.key))||[],s={};activeTab===0?s={materialDetails:[t.reduce((_,I)=>(_[I]=convertedValues!=null&&convertedValues[I]?convertedValues==null?void 0:convertedValues[I]:"",_),{})],templateHeaders:x!=null&&x.FieldName?(B=x.FieldName)==null?void 0:B.join("$^$"):"",requestId:Se||(x==null?void 0:x.RequestId)||"",templateName:x!=null&&x.TemplateName?x.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""}:s={materialDetails:[t.reduce((_,I)=>(_[I]=rowsOfMaterialData.map(K=>{var J;return(J=K[I])==null?void 0:J.trim()}).filter(K=>K!=="").join(",")||"",_),{})],templateHeaders:x!=null&&x.FieldName?(y=x.FieldName)==null?void 0:y.join("$^$"):"",requestId:Se||(x==null?void 0:x.RequestId)||"",templateName:x!=null&&x.TemplateName?x.TemplateName:"",dtName:"MDG_MAT_CHANGE_TEMPLATE",version:"v4",rolePrefix:""};const l=()=>{Ae(!1),ot(""),Wt(!0),ut("Download has been started. You will get the Excel file via email."),ie("success"),Zt(),setTimeout(()=>{var _;U((_=Fe)==null?void 0:_.REQUEST_BENCH)},2600)},f=()=>{Ae(!1),Wt(!0),ut("Oops! Something went wrong. Please try again later."),ie("danger"),Zt(),setTimeout(()=>{var _;U((_=Fe)==null?void 0:_.REQUEST_BENCH)},2600)};F(`/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,"postandgetblob",l,f,s)};(t=>t&&Object.values(t).every(s=>typeof s=="object"&&s!==null&&Object.keys(s).length===0))(W);const nn=Ct.filter(t=>be.includes(t.companyCode)).map(t=>({code:t.code,desc:t.desc}));return p("div",{children:[lt&&n(Po,{openSnackBar:qt,alertMsg:$t,alertType:v,handleSnackBarClose:qn}),n(Oo,{open:Yt,onClose:()=>Et(!1),title:ht.title,message:ht.message,subText:ht.subText,buttonText:ht.buttonText,redirectTo:ht.redirectTo}),ye&&n(Ns,{duplicateFieldsArr:oe,moduleName:(eo=Je)==null?void 0:eo.PC,open:ye,onClose:()=>Ve(!1)}),p(Mn,{open:gn,onClose:()=>hn(!1),"aria-labelledby":"missing-fields-dialog-title",maxWidth:"sm",fullWidth:!0,children:[n(lo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:"Missing Mandatory Fields"}),p(In,{sx:{pt:2},children:[n(De,{variant:"body1",gutterBottom:!0,children:"Please complete the following mandatory fields:"}),n(mr,{dense:!0,children:Wn.length>0?Wn.map((t,s)=>{const l=t.match(/^(Line \d+)( - .*)$/);return p(xo,{disablePadding:!0,children:[n(pr,{sx:{minWidth:30},children:n(co,{fontSize:"small",color:"warning"})}),n(No,{primary:l?p(an,{children:[n("strong",{children:l[1]}),l[2]]}):t})]},s)}):n(xo,{children:n(No,{primary:n(De,{variant:"body2",children:"No missing fields found."})})})})]}),n(_n,{children:n(Ue,{onClick:()=>hn(!1),variant:"contained",color:"primary",children:"Close"})})]}),((d==null?void 0:d.TemplateName)||at)&&p(an,{children:[!pe&&z.length===0&&Object.keys(q||{}).length===0&&p(an,{children:[p(Mn,{open:It,onClose:(t,s)=>{s!=="backdropClick"&&s!=="escapeKeyDown"&&Dt()},maxWidth:"sm",fullWidth:!0,children:[p(O,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(_o,{color:"primary",sx:{marginRight:"0.5rem"}}),p(De,{variant:"h6",component:"div",color:"primary",children:[d==null?void 0:d.TemplateName," ",i("Search Filter"),"(s)"]})]}),p(In,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"controllingArea",label:i("Controlling Area")},dropDownData:{controllingArea:[{code:"ETCA",desc:"ETCA"}]},selectedValues:{controllingArea:$?[{code:$}]:[]},handleSelectionChange:(t,s)=>{Ye(s.length>0?s[0].code||s[0]:"")},formatOptionLabel:t=>t.code&&t.desc?`${t.code} - ${t.desc}`:t.code||"",singleSelect:!0,errors:{}})}),n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"companyCode",label:i("Company Code")},dropDownData:{companyCode:ae||[]},selectedValues:{companyCode:be.map(t=>ae.find(s=>s.code===t)||{code:t})},handleSelectAll:t=>{be.length===(ae==null?void 0:ae.length)?Ce([]):Ce((ae==null?void 0:ae.map(s=>s.code))||[])},handleSelectionChange:(t,s)=>{const l=s.map(f=>typeof f=="string"?f:f.code||f);Ce(l)},formatOptionLabel:t=>t.code&&t.desc?`${t.code} - ${t.desc}`:t.code||"",isSelectAll:!0,errors:{}})}),n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"profitCenter",label:i("Profit Center")},dropDownData:{profitCenter:nn},selectedValues:{profitCenter:he.map(t=>nn.find(s=>s.code===t)||{code:t})},handleSelectAll:t=>{Ct.filter(s=>be.includes(s.companyCode)),he.length===nn.length?nt([]):nt(nn.map(s=>s.code))},handleSelectionChange:(t,s)=>{const l=s.map(f=>typeof f=="string"?f:f.code);nt(l)},formatOptionLabel:t=>typeof t=="string"?t:`${t.code}${t.desc?` - ${t.desc}`:""}`,isSelectAll:!0,errors:{}})})]}),n(_n,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:p(O,{sx:{display:"flex",gap:1},children:[n(Ue,{onClick:Dt,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:i("Cancel")}),(d==null?void 0:d.RequestType)!==((to=D)==null?void 0:to.CHANGE_WITH_UPLOAD)&&n(Ue,{onClick:()=>{mn("OK")},variant:"contained",disabled:!$||be.length===0||he.length===0,sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:i("OK")}),(d==null?void 0:d.RequestType)===((Tn=D)==null?void 0:Tn.CHANGE_WITH_UPLOAD)&&n(Ue,{onClick:()=>{mn("Download")},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:i("Download")})]})})]}),n(Io,{onDownloadTypeChange:Ut,open:Qe,downloadType:rt,handleDownloadTypeChange:en,onClose:Y}),n(Kn,{blurLoading:gt,loaderMessage:Be})]}),me&&p(O,{sx:{mt:4},children:[n(O,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"16px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:n(De,{variant:"h5",sx:{fontWeight:600},children:i("List of Profit Centers")})}),n(Un,{elevation:3,sx:{borderRadius:3,overflow:"hidden",border:"1px solid #e0e0e0",backgroundColor:"#fafbff"},children:n(O,{children:n(uo,{rows:z,columns:X,rowHeight:70,pageSize:10,tempheight:"50vh",getRowIdValue:"id",editMode:"row",status_onRowSingleClick:!0,callback_onRowSingleClick:qe,processRowUpdate:ke,experimentalFeatures:{newEditingApi:!0},isCellEditable:t=>!["profitCenter","companyCode","controllingArea"].includes(t.field),getRowClassName:t=>(se==null?void 0:se.id)===t.row.id?"Mui-selected":""})})}),n(O,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:n(Bn,{handleSaveAsDraft:fe,handleSubmitForReview:fe,handleSubmitForApprove:fe,handleSendBack:fe,handleCorrection:fe,handleRejectAndCancel:fe,handleValidateAndSyndicate:fe,validateAllRows:Mt,filteredButtons:Ie,moduleName:Ne,showWfLevels:Ht,selectedLevel:Pe,workFlowLevels:le,setSelectedLevel:Ft})})]})]}),p(an,{children:[Z.length===1&&((no=Z[0])==null?void 0:no.profitCenter)===null&&Object.keys(q||{}).length===0&&p(an,{children:[p(Mn,{open:It,TransitionComponent:ws,keepMounted:!0,onClose:(t,s)=>{s!=="backdropClick"&&s!=="escapeKeyDown"&&Dt()},maxWidth:"sm",fullWidth:!0,children:[p(O,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[n(_o,{color:"primary",sx:{marginRight:"0.5rem"}}),p(De,{variant:"h6",component:"div",color:"primary",children:[d==null?void 0:d.TemplateName," ",i("Search Filter"),"(s)"]})]}),p(In,{sx:{padding:"1.5rem 1.5rem 1rem"},children:[n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"controllingArea",label:"Controlling Area"},dropDownData:{controllingArea:[{code:"ETCA",desc:"ETCA"}]},selectedValues:{controllingArea:$?[{code:$}]:[]},handleSelectionChange:(t,s)=>{Ye(s.length>0?s[0].code||s[0]:"")},formatOptionLabel:t=>t.code&&t.desc?`${t.code} - ${t.desc}`:t.code||"",singleSelect:!0,errors:{}})}),n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"companyCode",label:i("Company Code")},dropDownData:{companyCode:ae||[]},selectedValues:{companyCode:be.map(t=>ae.find(s=>s.code===t)||{code:t})},handleSelectAll:t=>{be.length===(ae==null?void 0:ae.length)?Ce([]):Ce((ae==null?void 0:ae.map(s=>s.code))||[])},handleSelectionChange:(t,s)=>{const l=s.map(f=>typeof f=="string"?f:f.code||f);Ce(l)},formatOptionLabel:t=>t.code&&t.desc?`${t.code} - ${t.desc}`:t.code||"",isSelectAll:!0,errors:{}})}),n(O,{sx:{marginBottom:"1rem"},children:n(wn,{param:{key:"profitCenter",label:i("Profit Center")},dropDownData:{profitCenter:nn},selectedValues:{profitCenter:he.map(t=>nn.find(s=>s.code===t)||{code:t})},handleSelectAll:t=>{const s=Ct.filter(l=>be.includes(l.companyCode));he.length===s.length?nt([]):nt(s.map(l=>l.code))},handleSelectionChange:(t,s)=>{const l=s.map(f=>typeof f=="string"?f:f.code||f);nt(l)},formatOptionLabel:t=>typeof t=="string"?t:`${t.code}${t.desc?` - ${t.desc}`:""}`,isSelectAll:!0,errors:{}})})]}),n(_n,{sx:{padding:"0.5rem 1.5rem",display:"flex",alignItems:"center"},children:p(O,{sx:{display:"flex",gap:1},children:[n(Ue,{onClick:Dt,color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:i("Cancel")}),(d==null?void 0:d.RequestType)!==(($n=D)==null?void 0:$n.CHANGE_WITH_UPLOAD)&&n(Ue,{onClick:()=>{mn("OK")},variant:"contained",disabled:!$||be.length===0||he.length===0,sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:i("OK")}),(d==null?void 0:d.RequestType)===((oo=D)==null?void 0:oo.CHANGE_WITH_UPLOAD)&&n(Ue,{onClick:()=>{Ln()},variant:"contained",sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:i("Download")})]})})]}),n(Io,{onDownloadTypeChange:Ut,open:Qe,downloadType:rt,handleDownloadTypeChange:en,onClose:Y}),n(Kn,{blurLoading:gt,loaderMessage:Be})]}),pe==="true"&&p(O,{sx:{marginTop:"20px",padding:"16px"},children:[n(De,{variant:"h5",gutterBottom:!0,children:i("Profit Center Lists")}),n(Un,{elevation:4,sx:{p:0,borderRadius:2,overflow:"hidden",mt:"50px"},children:n("div",{children:n(uo,{rows:Z,columns:X,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,editMode:"cell",callback_onRowSingleClick:qe,processRowUpdate:xe,experimentalFeatures:{newEditingApi:!0},isCellEditable:t=>!["profitCenter","companyCode","controllingArea"].includes(t.field),getRowClassName:t=>(se==null?void 0:se.id)===t.row.id?"Mui-selected":""})})}),n(O,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:n(Bn,{handleSaveAsDraft:fe,handleSubmitForReview:fe,handleSubmitForApprove:fe,handleSendBack:fe,handleCorrection,handleRejectAndCancel:fe,handleValidateAndSyndicate:fe,validateAllRows:Mt,filteredButtons:Ie,moduleName:Ne,showWfLevels:Ht,selectedLevel:Pe,workFlowLevels:le,setSelectedLevel:Ft})})]}),q&&Object.keys(q).length>0&&p(O,{sx:{marginTop:"20px",padding:"16px"},children:[n(De,{variant:"h5",gutterBottom:!0,children:i("Profit Center Lists")}),n(Un,{elevation:4,sx:{p:0,borderRadius:2,overflow:"hidden",mt:"50px"},children:n("div",{children:n(uo,{rows:Z,columns:X,pageSize:10,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,editMode:"cell",callback_onRowSingleClick:qe,processRowUpdate:xe,experimentalFeatures:{newEditingApi:!0},isCellEditable:t=>!["profitCenter","companyCode","controllingArea"].includes(t.field),getRowClassName:t=>(se==null?void 0:se.id)===t.row.id?"Mui-selected":""})})}),n(O,{sx:{display:"flex",justifyContent:"right",mt:3,gap:2},children:n(Bn,{handleSaveAsDraft:fe,handleSubmitForReview:fe,handleSubmitForApprove:fe,handleSendBack:fe,handleCorrection,handleRejectAndCancel:fe,handleValidateAndSyndicate:fe,validateAllRows:Mt,filteredButtons:Ie,moduleName:Ne,showWfLevels:Ht,selectedLevel:Pe,workFlowLevels:le,setSelectedLevel:Ft})})]})]})]})},Ms=({apiResponse:pe,reqBench:Me,downloadClicked:ge,setDownloadClicked:at,setIsSecondTabEnabled:H,setIsAttachmentTabEnabled:Ne})=>{const Re=window.location.hash.split("/");Re[Re.length-1];const{t:b}=To();new Date().toLocaleDateString("en-GB");const A=Jn(),i=R(v=>v.profitCenter.payload.requestHeaderData),z=R(v=>v.request.requestHeader),Z=R(v=>v.userManagement.userData),W=R(v=>v.tabsData.requestHeaderData),mt=R(v=>v.AllDropDown.dropDown.FieldName||[]),me=Yn(),d=new URLSearchParams(me.search);d.get("reqBench");const it=d.get("RequestId"),x=`/Date(${Date.now()})/`,[Ie,q]=o.useState(""),[He,Rt]=o.useState(""),[Se,S]=o.useState(""),[It,re]=o.useState(""),[$,Ye]=o.useState([]),se=Co(),[cn,ae]=o.useState(!1);o.useState(!1),o.useState("");const[h,Qe]=o.useState("success"),[tt,be]=o.useState(!1),[Ce,Ct]=o.useState("systemGenerated"),[Tt,he]=o.useState(),[nt,lt]=o.useState(!1),[Wt,qt]=o.useState(!1),[_e,$t]=o.useState(!1),[ut,gt]=o.useState(""),[Ae,Be]=o.useState(""),[ot,rt]=o.useState(!1),{getChangeTemplate:Gt}=Lo("Profit Center"),{getRequestHeaderTemplatePc:Kt}=Is(),dn=[{code:"Create",tooltip:"Create New ProfitCenter Directly in Application"},{code:"Change",tooltip:"Modify Existing ProfitCenter Directly in Application"},{code:"Create with Upload",tooltip:"Create New ProfitCenter with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing ProfitCenter with Excel Upload"}],_t=[{code:"US",desc:"USA"},{code:"EU",desc:"EUROPE"}],St=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}],Jt=R(v=>{var ie,oe;return(oe=(ie=v.AllDropDown)==null?void 0:ie.dropDown)==null?void 0:oe.TemplateName});A(st({keyName:"RequestStatus",data:"DRAFT"})),A(st({keyName:"ReqCreatedBy",data:Z==null?void 0:Z.user_id})),o.useEffect(()=>{var v;if(ge){if((i==null?void 0:i.RequestType)===D.CREATE_WITH_UPLOAD){rt(!0);return}if((i==null?void 0:i.RequestType)===((v=D)==null?void 0:v.CHANGE_WITH_UPLOAD)){qt(!0);return}}},[ge]);const G=()=>{var ie,oe;let v=!0;return i&&((ie=W[Object.keys(W)])!=null&&ie.length)?(oe=W[Object.keys(W)[0]])==null||oe.forEach(je=>{var ye;!i[je.jsonName]&&je.visibility===((ye=_r)==null?void 0:ye.MANDATORY)&&(v=!1)}):v=!1,v};o.useEffect(()=>{((i==null?void 0:i.RequestType)==="Change"||(i==null?void 0:i.RequestType)==="Change with Upload")&&Gt(i==null?void 0:i.TemplateName)},[i==null?void 0:i.RequestType]),o.useEffect(()=>{Kt()},[i==null?void 0:i.RequestType]),o.useEffect(()=>{const v=Ie&&He&&Se.trim()!=="",ie=Ie!=="Change"||Ie==="Change with Upload"||It&&$.length>0;ae(v&&ie)},[Ie,He,Se,It,$]);const ze=()=>{const v=new Date(i==null?void 0:i.ReqCreatedOn).getTime(),ie=mt.map(le=>le.code||"").join(", ")||"";qt(!1);const oe={RequestId:"",Region:i==null?void 0:i.Region,ReqCreatedBy:"<EMAIL>",ReqCreatedOn:v?`/Date(${v})/`:x,ReqUpdatedOn:v?`/Date(${v})/`:x,RequestType:(i==null?void 0:i.RequestType)||"",RequestDesc:(i==null?void 0:i.RequestDesc)||"",RequestStatus:"DRAFT",RequestPriority:(i==null?void 0:i.RequestPriority)||"",FieldName:ie,TemplateName:(i==null?void 0:i.TemplateName)||"",ChangeCategory:"",IsHierarchyGroup:!1},je=le=>{var bt,Pe,Ft;if(lt(!0),he(`Request Header Created Successfully! Request ID: ${(bt=le==null?void 0:le.body)==null?void 0:bt.requestId}`),be(!1),Qe("success"),Ee(),A(Pr(le==null?void 0:le.body)),A(Cr(le.body)),A(Or({keyName:Lr.REQUEST_ID,data:(Pe=le==null?void 0:le.body)==null?void 0:Pe.requestId})),(i==null?void 0:i.RequestType)===D.CREATE_WITH_UPLOAD||(i==null?void 0:i.RequestType)===D.EXTEND_WITH_UPLOAD){rt(!0);return}if((i==null?void 0:i.RequestType)===((Ft=D)==null?void 0:Ft.CHANGE_WITH_UPLOAD)){qt(!0);return}setTimeout(()=>{A(Vn(1)),H(!0)},2500)},ye=le=>{console.error("APIError",le),lt(!0),Qe("error"),he("Error occured while saving Request Header"),Ee()};F(`/${te}/massAction/createRequestHeader`,"post",je,ye,oe)},We=()=>{at(!1),rt(!1),Ct("systemGenerated")},Ee=()=>{$t(!0)},ct=()=>{$t(!1)},j=v=>{var ie;Ct((ie=v==null?void 0:v.target)==null?void 0:ie.value)},un=()=>{Ce==="systemGenerated"&&(Pt(),We()),Ce==="mailGenerated"&&(Ot(),We())},Pt=()=>{gt("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),Be(!0);let v={scenario:(i==null?void 0:i.RequestType)||(z==null?void 0:z.requestType)||"",rolePrefix:"ETP",dtName:"MDG_PC_FIELD_CONFIG",version:"v2",requestId:(i==null?void 0:i.RequestId)||(z==null?void 0:z.requestId)||""};const ie=ye=>{var bt;if((ye==null?void 0:ye.size)==0){Be(!1),gt(""),lt(!0),he("No data found for the selected criteria."),Qe("danger"),Ee();return}const Ve=URL.createObjectURL(ye),le=document.createElement("a");le.href=Ve,le.setAttribute("download","Mass_Create.xlsx"),document.body.appendChild(le),le.click(),document.body.removeChild(le),URL.revokeObjectURL(Ve),Be(!1),gt(""),lt(!0),he(`${i!=null&&i.TemplateName?`${i==null?void 0:i.TemplateName}_Mass Change`:"Mass_Create"}.xlsx has been downloaded successfully.`),Qe("success"),Ee(),se((bt=Fe)==null?void 0:bt.REQUEST_BENCH)},oe=()=>{Be(!1)},je=`/${te}${(i==null?void 0:i.RequestType)===D.EXTEND_WITH_UPLOAD?Nt.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:Nt.EXCEL.DOWNLOAD_EXCEL}`;F(je,"postandgetblob",ie,oe,v)},Ot=()=>{Be(!0);let v={region:i==null?void 0:i.Region,scenario:i==null?void 0:i.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:i!=null&&i.RequestId?i==null?void 0:i.RequestId:""};const ie=()=>{var ye;Be(!1),gt(""),lt(!0),he((ye=vr)==null?void 0:ye.DOWNLOAD_MAIL_INITIATED),Qe("success"),Ee(),setTimeout(()=>{var Ve;se((Ve=Fe)==null?void 0:Ve.REQUEST_BENCH)},2600)},oe=()=>{var ye;Be(!1),lt(!0),he((ye=Ro)==null?void 0:ye.ERR_DOWNLOADING_EXCEL),Qe("danger"),Ee(),setTimeout(()=>{var Ve;se((Ve=Fe)==null?void 0:Ve.REQUEST_BENCH)},2600)},je=`/${destination_MaterialMgmt}${(i==null?void 0:i.RequestType)===D.EXTEND_WITH_UPLOAD?Nt.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:Nt.EXCEL.DOWNLOAD_EXCEL_MAIL}`;F(je,"post",ie,oe,v)};return n("div",{children:p(Ir,{spacing:2,children:[Object.entries(W).map(([v,ie])=>p(xt,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Rr},children:[n(De,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:v}),n(O,{children:n(xt,{container:!0,spacing:1,children:ie.filter(oe=>oe.visibility!=="Hidden").sort((oe,je)=>oe.sequenceNo-je.sequenceNo).map(oe=>n(Es,{isHeader:!0,field:oe,dropDownData:{RequestType:dn,Region:_t,RequestPriority:St,TemplateName:Jt},disabled:it||!!(z!=null&&z.requestId),requestHeader:!0},oe.id))})}),!it&&!(z!=null&&z.requestId)&&n(O,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:n(Ue,{variant:"contained",color:"primary",disabled:!G(),onClick:ze,children:b("Save Request Header")})})]},v)),n(Kn,{blurLoading:Ae,loaderMessage:ut}),nt&&n(Po,{openSnackBar:_e,alertMsg:Tt,alertType:h,handleSnackBarClose:ct}),Wt&&n(Er,{downloadClicked:ge,setDownloadClicked:at}),n(Io,{onDownloadTypeChange:un,open:ot,downloadType:Ce,handleDownloadTypeChange:j,onClose:We})]})})},Us=({reqBench:pe,setIsAttachmentTabEnabled:Me,setCompleted:ge,module:at,isDisabled:H,fieldDisable:Ne,apiResponses:de})=>{var vo,ko,wo,Mo,Uo,Bo,Wo,qo,$o,Go;const Re=o.useRef(!1),b=Jn();Co();const{t:U}=To(),{customError:A}=kr();o.useRef(null);let i=R(e=>e==null?void 0:e.userManagement.taskData);const z=R(e=>e.profitCenter.validatedRows);Tr();const Z=R(e=>{var r;return(r=e==null?void 0:e.profitCenter)==null?void 0:r.validatedRowsStatus}),{selectedRowId:W,tabs:mt}=R(e=>e.profitCenterTab),me=R(e=>(e.profitCenter.profitCenterTabs||[]).filter(a=>a.tab!=="Initial Screen")),d=Yn(),x=new URLSearchParams(d.search).get("RequestId"),Ie=R(e=>e.request.requestHeader),q=R(e=>e.profitCenter.isOpenDialog),{loading:He,error:Rt,fetchProfitCenterFieldConfig:Se}=_s(),S=R(e=>e.profitCenter.payload.rowsHeaderData),It=R(e=>e.profitCenter.payload),re=R(e=>{var r;return((r=e.profitCenter.payload)==null?void 0:r.rowsBodyData)||{}}),$=R(e=>{var r;return((r=e.profitCenter.payload)==null?void 0:r.rowsHeaderData)||{}}),Ye=R(e=>{var r;return((r=e.profitCenter.payload)==null?void 0:r.requestHeaderData)||{}}),se=R(e=>e.changeLog.createChangeLogDataGL),cn=R(e=>e.payload.dynamicKeyValues),ae=R(e=>e.payload.filteredButtons),[h,Qe]=o.useState(null),[tt,be]=o.useState(0);o.useState({});const[Ce,Ct]=o.useState([]),[Tt,he]=o.useState([]),[nt,lt]=o.useState([]),[Wt,qt]=o.useState([]),[_e,$t]=o.useState([]),[ut,gt]=o.useState([]),[Ae,Be]=o.useState([]),[ot,rt]=o.useState([]),[Gt,Kt]=o.useState([]),[dn,_t]=o.useState(!1),[St,Jt]=o.useState([]),[G,ze]=o.useState(!1),[We,Ee]=o.useState(!1),[ct,j]=o.useState(!1),[un,Pt]=o.useState(!1);o.useState("");const[Ot,v]=o.useState(!1),[ie,oe]=o.useState(""),[je,ye]=o.useState("success"),[Ve,le]=o.useState(!1),[bt,Pe]=o.useState(!1),[Ft,Wn]=o.useState(""),{getButtonsDisplayGlobal:Pn,showWfLevels:gn}=Sr(),[hn,Yt]=o.useState({}),[Et,ht]=o.useState({}),[Lt,Qt]=o.useState({}),[Oe,Ht]=o.useState(!1),[V,fn]=o.useState(!1),[zt,pn]=o.useState("yes"),[Ze,jt]=o.useState(""),[yt,vt]=o.useState(""),[On,Vt]=o.useState([]),[Dt,kt]=o.useState(""),[mn,g]=o.useState([]),[k,ne]=o.useState(),[Xe,ee]=o.useState(!1);o.useState(null);const[N,X]=o.useState({}),[L,ke]=o.useState([]),[xe,qe]=o.useState(null),[we,dt]=o.useState({}),[ft,$e]=o.useState(!1),[ue,Cn]=o.useState([]),[et,wt]=o.useState(!1);o.useState(0);const[Xt,fe]=o.useState([]),[Mt,Zt]=o.useState(""),[qn,Ln]=o.useState([]),[Y,en]=o.useState(!1),[Ut,tn]=o.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),{getDynamicWorkflowDT:So}=br();h!=null&&h.profitCenterNumber&&(Z==null||Z[h.profitCenterNumber]),o.useEffect(()=>{const e=async()=>{var r,a;try{const c=await So((r=$==null?void 0:$[0])==null?void 0:r.businessSegment,i==null?void 0:i.ATTRIBUTE_3,"v4","MDG_DYNAMIC_WF_PC_DT",(a=Je)==null?void 0:a.PC);fe(c)}catch(c){A(c)}};Ye!=null&&Ye.Region&&(i!=null&&i.ATTRIBUTE_3)&&e()},[Ye==null?void 0:Ye.Region,i==null?void 0:i.ATTRIBUTE_3]),o.useEffect(()=>{const e=localStorage.getItem("validatedRows_PC");if(e)try{const r=JSON.parse(e),a=Object.fromEntries(Object.entries(r).filter(([c])=>c!=="undefined"));b(wr(a))}catch(r){console.error("Failed to parse validated rows from localStorage",r)}},[]),o.useEffect(()=>{me!=null&&me.length||Se()},[]),o.useEffect(()=>{var e,r,a;S.length>=1&&((e=S[0])!=null&&e.profitCenterNumber||(r=S[0])!=null&&r.controllingArea||(a=S[0])!=null&&a.companyCode)&&b(so(!1))},[]),o.useEffect(()=>{(i!=null&&i.ATTRIBUTE_1||x)&&Pn("Profit Center","MDG_DYN_BTN_DT","v3")},[i]);const Qn=["controllingArea","profitCenterNumber","companyCode","businessSegment","longDescription"];o.useEffect(()=>{try{const e=JSON.parse(localStorage.getItem("validatedRowsStatus"))||{};Object.entries(e).forEach(([r,a])=>{b(Eo({rowId:r,status:a}))})}catch{A(err)}},[b]);const nn=async(e,r,a)=>{const c=[],m=(e==null?void 0:e.lineNumber)||S.findIndex(T=>T.id===e.id)+1;((r==null?void 0:r.AssignToPrctr)===null||(r==null?void 0:r.AssignToPrctr)===void 0||(r==null?void 0:r.AssignToPrctr)==="")&&(r.AssignToPrctr=!0);const E=a.flatMap(T=>Object.values(T.data).flat()),w=[];E.forEach(T=>{if(T.visibility==="Mandatory"){const P=r[T.jsonName];(P==null||typeof P=="string"&&P.trim()==="")&&(w.push(T.jsonName),c.push(`Line ${m} - ${T.fieldName}`))}});const u={companyCode:"Company Code",profitCenterNumber:"Profit Center Number",businessSegment:"Business Segment",controllingArea:"Controlling Area",longDescription:"Long Description"};Qn.forEach(T=>{const P=e[T],Q=u[T]||T;P==null||typeof P=="string"&&P.trim()===""?(w.push(Q),c.push(`Line ${m} - ${Q}`)):T==="profitCenterNumber"&&(P.length!==10||!/^[a-zA-Z0-9]+$/.test(P))&&c.push(`Line ${m} - ${Q}`)});const M=c.length===0?"success":"error";b(Eo({rowId:e.profitCenterNumber,status:M})),b(kn({rowId:e.id}));try{const T=JSON.parse(localStorage.getItem(Rn.STATUS_VALIDATE))||{},P=String(e.profitCenterNumber);T[P]=M,localStorage.setItem(Rn.STATUS_VALIDATE,JSON.stringify(T))}catch{A(err)}if(M==="error"){const T=[...new Set(c)];Jt(T),_t(!0),dt(P=>({...P,[e.id]:w}))}else ht(T=>({...T,[e.id]:JSON.parse(JSON.stringify(e))})),Qt(T=>{const{id:P,...Q}=r;return{...T,[e.id]:JSON.parse(JSON.stringify(Q))}})};o.useEffect(()=>{if(S.length&&Object.keys(re).length){const e={},r={};S.forEach(a=>{const c=a.id;if(Et[c]||(e[c]=JSON.parse(JSON.stringify(a))),!Lt[c]&&re[c]){const{id:m,...E}=re[c];r[c]=JSON.parse(JSON.stringify(E))}}),ht(a=>({...a,...e})),Qt(a=>({...a,...r}))}},[S,re]);const on=e=>Qn.includes(e),Zn=[{field:"included",headerName:"",flex:.3,align:"center",headerAlign:"center",sortable:!1,disableColumnMenu:!0,renderHeader:()=>{const e=S.length>0&&S.every(a=>a.included),r=S.some(a=>a.included);return n(fo,{indeterminate:!e&&r,checked:e,disabled:H||Ne,onChange:a=>{const c=a.target.checked,m=S.map(E=>({...E,included:c}));b(sn(m))}})},renderCell:e=>n(fo,{checked:e.row.included,disabled:H||Ne,onChange:r=>C(r.target.checked,e.row.id,"included")})},{field:"lineNumber",headerName:"Sl No",flex:.25,align:"center",headerAlign:"center",renderCell:e=>{const r=S.findIndex(a=>a.id===e.row.id);return n("div",{children:(r+1)*10})}},{field:"controllingArea",headerName:"Controlling Area",align:"left",headerAlign:"left",flex:1,renderHeader:()=>p("span",{children:[U("Controlling Area"),on("controllingArea")&&n("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>n(O,{sx:{width:"100%"},children:n(pt,{options:_e||[],value:(_e==null?void 0:_e.find(r=>r.code===e.row.controllingArea))||null,onChange:r=>{C(r,e.row.id,"controllingArea");const a=(r==null?void 0:r.code)||"";a?I(a,"ETP"):(Ct([]),b({type:"SET_DROPDOWN",payload:{keyName:"CompanyCode",data:[]}}))},placeholder:U("Select Controlling Area"),disabled:H||Ne,minWidth:"90%",listWidth:235})})},{field:"companyCode",headerName:"Company Code",align:"left",headerAlign:"left",flex:1,renderHeader:()=>p("span",{children:[U("Company Code"),on("companyCode")&&n("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>n(O,{sx:{width:"80%"},children:n(pt,{options:Ce||[],value:(Ce==null?void 0:Ce.find(r=>r.code===e.row.companyCode))||null,onChange:r=>C(r,e.row.id,"companyCode"),placeholder:U("Select Company Code"),disabled:H||Ne,minWidth:"90%",listWidth:235})})},{field:"profitCenterNumber",align:"left",headerAlign:"left",flex:1,renderHeader:()=>p("span",{children:[U("Profit Center Number"),on("profitCenterNumber")&&n("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{const r=e.row.companyCode,a=typeof r=="string"?r:typeof r=="object"&&r!==null?r.value??"":"",c=a?`P${a}`:"",m=e.row.profitCenterNumber||"",E=m.startsWith(c)?m.slice(c.length):"",w=m.length>0&&(m.length!==10||!/^[a-zA-Z0-9]+$/.test(m));return p(O,{sx:{position:"relative",width:"100%"},children:[n(ho,{value:E,onChange:T=>{const P=T.target.value.replace(/[^0-9]/g,"").slice(0,10-c.length);C(c+P,e.row.id,"profitCenterNumber")},onKeyDown:T=>{["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight"].includes(T.key)||/^[0-9]$/.test(T.key)||T.preventDefault()},onPaste:T=>{const P=T.clipboardData.getData("text");/^[0-9]+$/.test(P)||T.preventDefault()},variant:"outlined",size:"small",fullWidth:!0,inputProps:{maxLength:10-c.length,style:{paddingLeft:`${c.length+2}ch`,fontSize:"0.875rem",height:"35px",boxSizing:"border-box",display:"flex",alignItems:"center"}},disabled:H||Ne,sx:{"& .MuiOutlinedInput-root":{height:"35px"},"& .MuiInputBase-input":{fontFamily:"inherit",fontWeight:500}}}),n(O,{sx:{position:"absolute",top:"50%",left:"14px",transform:"translateY(-50%)",pointerEvents:"none",fontSize:"0.875rem",fontWeight:500,fontFamily:"inherit",color:"rgba(0, 0, 0, 0.7)"},children:c}),w&&n(O,{sx:{position:"absolute",bottom:-16,left:14,color:"red",fontSize:"10px",pointerEvents:"none"},children:"Must be 10 character"})]})}},{field:"longDescription",align:"left",headerAlign:"left",flex:1.5,renderHeader:()=>p("span",{children:[U("Long Description"),on("longDescription")&&n("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{const[r,a]=dr.useState(e.row.longDescription||""),c=dr.useRef(null),m=e.row.longDescription||"",E=40;return n(O,{sx:{position:"relative",width:"100%"},children:n(ho,{inputRef:c,value:r,onChange:u=>{const M=u.target.selectionStart;a(u.target.value[0]===" "?u.target.value.trimStart():u.target.value.toUpperCase().replace(/[^a-zA-Z0-9-&()#.'/$%, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#.'/$%,])\s*/g,"$1").replace(/([-&()#.'/$%,])\s+/g,"$1").trimStart()),requestAnimationFrame(()=>{c.current&&c.current.setSelectionRange(M,M)})},onBlur:()=>{C(r,e.row.id,"longDescription"),en(!1)},onKeyDown:u=>{u.key===" "&&u.stopPropagation()},disabled:H,variant:"outlined",size:"small",placeholder:"Enter Long Description",fullWidth:!0,inputProps:{maxLength:E,style:{textTransform:"uppercase"}},sx:{"& .MuiOutlinedInput-root":{height:"50px"},"& .MuiInputBase-input":{padding:"18px 14px",height:"100%",boxSizing:"border-box"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Ge.black.dark,color:Ge.black.dark}},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:Ge.black.dark,color:Ge.black.dark}},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:Y&&m.length>=E?"red":""},"& fieldset":{borderColor:Y&&m.length>=E?"red":""}}},helperText:m.length===E?"Max Length Reached":`${m.length}/${E}`,FormHelperTextProps:{sx:{color:Y&&m.length===E?"red":"blue",position:"absolute",bottom:"-20px"}}})})}},{field:"businessSegment",flex:1,align:"left",headerAlign:"left",renderHeader:()=>p("span",{children:[U("Business Segment"),on("businessSegment")&&n("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>n(pt,{options:Tt||[],value:e.row.businessSegment,onChange:r=>C(r,e.row.id,"businessSegment"),placeholder:U("Select Business Segment"),disabled:H||Ne,minWidth:"90%",listWidth:235})},{field:"action",headerName:"Action",align:"left",headerAlign:"left",width:150,renderHeader:()=>n("span",{style:{fontWeight:"bold"},children:U("Action")}),renderCell:e=>{const r=e.row.id,a={id:r,...re[r]},c=e.row.profitCenterNumber,m=c&&Z[c]||"idle";return n(O,{children:n(ao,{title:m==="success"?"Validated Successfully":m==="error"?"Validation Failed":"Click to Validate",children:n(Xn,{onClick:w=>{w.stopPropagation(),nn(e.row,a,me)},color:m,disabled:H,children:m==="error"?n(Fr,{}):n(Ps,{})})})})}}];o.useEffect(()=>{kt(""),Vt([]),Ze&&yt&&eo()},[Ze,yt]);const eo=()=>{if(!Ze||!yt)return;const e={controllingArea:Ze,companyCode:yt,top:"100",skip:"0"};F(`/${te}/data/getProfitCentersNo`,"post",r=>{var a;if(Array.isArray((a=r.body)==null?void 0:a.list)){const c=r.body.list.map(m=>({code:m.code,desc:m.desc})).filter((m,E,w)=>m.code&&w.findIndex(u=>u.code===m.code)===E);Vt(c.map(m=>({code:m.code,desc:m.desc})))}},r=>{console.error("Profit Center fetch failed",r)},e)},to=()=>{v(!1)},Tn=()=>{b(so(!1))},no=()=>{Ht(!Oe),V&&fn(!1)},$n=()=>{fn(!V),Oe&&Ht(!1)};o.useEffect(()=>{z&&Object.keys(z).length>0&&localStorage.setItem("validatedRows_PC",JSON.stringify(z))},[z]),o.useEffect(()=>{if(!Array.isArray(S)||S.length===0){ze(!0),j(!1);return}const e=S.every(r=>{const a=(r==null?void 0:r.profitCenterNumber)??(r==null?void 0:r.id);return a?(Z==null?void 0:Z[String(a)])==="success":!1});ze(e),j(e)},[S,re,z,Et,Lt]);const oo=(e,r,a)=>{const c=[],m=(e==null?void 0:e.lineNumber)||S.findIndex(T=>T.id===e.id)+1,E={...r};(E.AssignToPrctr===null||E.AssignToPrctr===void 0||E.AssignToPrctr==="")&&(E.AssignToPrctr=!0);const w=a.flatMap(T=>Object.values(T.data).flat()),u=[];w.forEach(T=>{if(T.visibility==="Mandatory"){const P=E[T.jsonName];(P==null||typeof P=="string"&&P.trim()==="")&&(u.push(T.jsonName),c.push(`Line ${m} - ${T.fieldName}`))}});const M={companyCode:"Company Code",profitCenterNumber:"Profit Center Number",businessSegment:"Business Segment",controllingArea:"Controlling Area",longDescription:"Long Description"};return Qn.forEach(T=>{const P=e[T],Q=M[T]||T;P==null||typeof P=="string"&&P.trim()===""?c.push(`Line ${m} - ${Q}`):T==="profitCenterNumber"&&(P.length!==10||!/^[a-zA-Z0-9]+$/.test(P))&&c.push(`Line ${m} - ${Q}`)}),{missing:c,status:c.length>0?"error":"success"}},t=(e,r)=>{const a={},c={},m={};e.forEach((M,T)=>{var Gn,Fn,Hn;const P=r[M.id],Q=M.lineNumber||T+1,An=(Gn=P==null?void 0:P.ProfitCenterName)==null?void 0:Gn.trim(),xn=(Fn=P==null?void 0:P.Description)==null?void 0:Fn.trim(),Nn=(Hn=M==null?void 0:M.profitCenterNumber)==null?void 0:Hn.trim();An&&(a[An]||(a[An]=[]),a[An].push(Q)),xn&&(c[xn]||(c[xn]=[]),c[xn].push(Q)),Nn&&(m[Nn]||(m[Nn]=[]),m[Nn].push(Q))});const E=Object.entries(a).filter(([M,T])=>T.length>1).map(([M,T])=>({type:"Short Description",value:M,lines:T})),w=Object.entries(c).filter(([M,T])=>T.length>1).map(([M,T])=>({type:"Long Description",value:M,lines:T})),u=Object.entries(m).filter(([M,T])=>T.length>1).map(([M,T])=>({type:"Profit Center Number",value:M,lines:T}));return[...E,...w,...u]},s=()=>{let e=[],r="";if(S.forEach(a=>{const c={id:a.id,...re[a.id]},{missing:m,status:E}=oo(a,c,me);r=t(S,re),b(Eo({rowId:a.id,status:E}));try{const w=JSON.parse(localStorage.getItem(Rn.STATUS_VALIDATE))||{},u=String(a.profitCenterNumber||a.id);w[u]=E,localStorage.setItem(Rn.STATUS_VALIDATE,JSON.stringify(w))}catch(w){console.error("Failed to save validation status to localStorage",w)}E==="error"?e.push(...m):(ht(w=>({...w,[a.id]:JSON.parse(JSON.stringify(a))})),Qt(w=>{const{id:u,...M}=c;return{...w,[a.id]:JSON.parse(JSON.stringify(M))}}),b(kn({rowId:a.id})))}),e.length>0){const a=[...new Set(e)];Jt(a),_t(!0)}else{if(r.length>0){Cn(r),$e(!0);return}l(),ye("success"),oe("All Rows Validated Successfully"),v(!0),ge([!0,!1]),Me(!0)}},l=()=>{Pe(!0);const e=mo(It,Ie,x,i,cn,se),r=c=>{Pe(!1),(c==null?void 0:c.statusCode)===200||(c==null?void 0:c.statusCode)===201?(tn({title:Te.TITLE,message:c.message,subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),wt(!0)):(c==null?void 0:c.statusCode)===500||(c==null?void 0:c.statusCode)===501?(tn({title:ve.TITLE,message:c.message,subText:ve.SUBTEXT,buttonText:ve.BUTTONTEXT,redirectTo:ve.REDIRECT}),wt(!0)):(Pt(!0),oe("Unexpected response received."))},a=c=>{Pe(!1),Pt(!0),oe("Error occurred while validating the request"),console.error("Error saving draft:",c)};F(`/${te}/massAction/validateMassProfitCenter`,"POST",r,a,e)},f=()=>{_t(!1)},C=(e,r,a)=>{if(a==="controllingArea"){const m=(e==null?void 0:e.code)||e;b(st({uniqueId:W||(h==null?void 0:h.id),keyName:"controllingArea",data:m,viewID:null}));const E=S.map(w=>w.id===r?{...w,controllingArea:m}:w);b(sn(E)),Yt(w=>({...w,[r]:!0}));return}if(a==="companyCode"){const m=(e==null?void 0:e.code)||e;b(st({uniqueId:W||(h==null?void 0:h.id),keyName:"CompanyCode",data:m,viewID:"Comp Codes"}));const E=`P${m}`;b(st({uniqueId:W||(h==null?void 0:h.id),keyName:"profitCenterNumber",data:E,viewID:"Comp Codes"}));const w=S.map(u=>u.id===r?{...u,companyCode:m,profitCenterNumber:E}:u);b(sn(w)),Yt(u=>({...u,[r]:!0}));return}a==="longDescription"&&b(st({uniqueId:W||(h==null?void 0:h.id),keyName:"Description",data:e,viewID:"Basic Data"}));const c=S.map(m=>m.id===r?{...m,[a]:e}:m);b(sn(c)),Yt(m=>({...m,[r]:!0}))};o.useEffect(()=>{if(S.length>0){const e=S[0];Qe(e),b(rr(e==null?void 0:e.ProfitCenterID)),Re.current=!0}},[b]);const B=e=>{const r=e.row;Qe(r),Ln([e.id]),b(rr(r==null?void 0:r.ProfitCenterID))},y=()=>{const e=ur();ne(e),ee(!0),pn("yes"),jt(""),vt(""),kt(""),ke([]),b(so(!0))},_=(e,r)=>{be(r)};o.useEffect(()=>{h!=null&&h.controllingArea&&I(h.controllingArea,"ETP")},[h==null?void 0:h.controllingArea]);const I=(e="",r="")=>{const a=m=>{Ct(m.body),b({type:"SET_DROPDOWN",payload:{keyName:"CompanyCode",data:m.body}})},c=m=>{console.log(m)};F(`/${te}/data/getCompCodeBasedOnControllingArea?controllingArea=${e}&rolePrefix=${r}`,"get",a,c)},K=()=>{const e=a=>{const c=a.body.map(m=>({code:m,desc:m}));he(c),b({type:"SET_DROPDOWN",payload:{keyName:"businessSegment",data:c}})},r=a=>{console.log(a)};F(`/${te}/data/getBusinessSegment`,"get",e,r)},J=()=>{const e=W||(h==null?void 0:h.id);if(!e)return;const r=c=>{b(zr({keyName:"PrctrHierGrp",data:(c==null?void 0:c.body)||[],keyName2:e}))},a=c=>{console.log(c)};F(`/${te}/data/getProfitCtrGroup?controllingArea=ETCA`,"get",r,a)};o.useEffect(()=>{h!=null&&h.controllingArea&&J(h.controllingArea)},[h==null?void 0:h.controllingArea]),o.useEffect(()=>{(W||h!=null&&h.id)&&J()},[W,h==null?void 0:h.id]);const ce=()=>{const e=a=>{qt(a.body),b({type:"SET_DROPDOWN",payload:{keyName:"Template",data:a.body}})},r=a=>{console.log(a)};F(`/${te}/data/getFormPlanningTemp`,"get",e,r)},At=()=>{const e=a=>{const c=(a==null?void 0:a.body)||[];$t(c),b({type:"SET_DROPDOWN",payload:{keyName:"controllingArea",data:c}}),(h==null?void 0:h.controllingArea)&&J()},r=a=>{console.error("Failed to fetch Controlling Area:",a)};F(`/${te}/data/getControllingArea`,"get",e,r)};o.useEffect(()=>{(!Array.isArray(_e)||_e.length===0)&&(S==null?void 0:S.length)>0&&At()},[_e,S]);const Bt=()=>{const e=a=>{lt(a.body),b({type:"SET_DROPDOWN",payload:{keyName:"TaxJurisdiction",data:a.body}})},r=a=>{console.log(a)};F(`/${te}/data/getJurisdiction`,"get",e,r)};o.useEffect(()=>{var e;Sn(),ce(),At(),Bt(),K(),S.length>0&&((e=S[0])!=null&&e.controllingArea)&&J()},[S.length]),o.useEffect(()=>{var e;S.length>0&&((e=S[0])!=null&&e.controllingArea)&&J()},[S,_e]),o.useEffect(()=>{if(h!=null&&h.controllingArea){const e=setTimeout(()=>{J(h.controllingArea)},500);return()=>clearTimeout(e)}},[h==null?void 0:h.controllingArea]);const Sn=()=>{const e=a=>{gt(a.body),b({type:"SET_DROPDOWN",payload:{keyName:"Country",data:a.body}})},r=a=>{console.log(a)};F(`/${te}/data/getCountryOrReg`,"get",e,r)},[vn,bn]=o.useState({}),En=(e,r,a)=>{const c=E=>{bn(w=>({...w,[a]:E.body})),Be(E.body),b({type:"SET_DROPDOWN",payload:{keyName:"Region",data:E.body}})},m=E=>{console.log(E)};F(`/${te}/data/getRegionBasedOnCountry?country=${e}`,"get",c,m)},yn=()=>{const e=a=>{rt(a.body),b(go({keyName:"Segment",data:a.body}))},r=a=>{console.log(a)};F(`/${te}/data/getSegment`,"get",e,r)};o.useEffect(()=>{yn()},[]);const Dn=()=>{const e=a=>{Kt(a.body),b({type:"SET_DROPDOWN",payload:{keyName:"Language",data:a.body}})},r=a=>{console.log(a)};F(`/${te}/data/getLanguageKey`,"get",e,r)};o.useEffect(()=>{Dn()},[]);const Le=async(e,r)=>{var w,u,M,T,P;if(console.log("type",e),e==="VALIDATE"){s();return}Pe(!0);let a=(P=(T=(M=(w=Nt)==null?void 0:w.MASTER_BUTTON_APIS)==null?void 0:M[(u=Je)==null?void 0:u.PC])==null?void 0:T[Ye==null?void 0:Ye.RequestType])==null?void 0:P[e];const c=mo(It,Ie,x,i,cn,se,r),m=Q=>{Pe(!1),(Q==null?void 0:Q.statusCode)===200||(Q==null?void 0:Q.statusCode)===201?(tn({title:Te.TITLE,message:Q.message,subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),wt(!0)):(Q==null?void 0:Q.statusCode)===500||(Q==null?void 0:Q.statusCode)===501?(tn({title:ve.TITLE,message:Q.message,subText:ve.SUBTEXT,buttonText:ve.BUTTONTEXT,redirectTo:ve.REDIRECT}),wt(!0)):(Pt(!0),oe("Unexpected response received."))},E=Q=>{Pe(!1),Pt(!0),oe("Error occurred while validating the request"),console.error("Error saving draft:",Q)};F(a==null?void 0:a.URL,"POST",m,E,c)},rn=(e,r)=>{Pe(!0);const a={coAreaPCs:[{controllingArea:Ze,profitCenter:Dt}]},c=E=>{var An,xn,Nn,Gn,Fn,Hn,Fo,Ho,zo,jo,Vo,Xo,Ko,Jo;const w=(E==null?void 0:E.body)||[],u=w[0];if(!u){Pe(!1),Tn();return}const M=((An=u==null?void 0:u.addressTabDto)==null?void 0:An.Country)||"",T=((xn=u==null?void 0:u.addressTabDto)==null?void 0:xn.Regio)||"",P=r.map(bo=>{var Yo,Qo;return bo.id===e?{...bo,controllingArea:u.controllingArea||"",companyCode:((Qo=(Yo=u.compCodesTabDto)==null?void 0:Yo.CompanyCode)==null?void 0:Qo[0])||""}:bo});b({type:"profitCenter/setProfitCenterRows",payload:P});const Q={[e]:{PersonResponsible:((Nn=u.basicDataTabDto)==null?void 0:Nn.PersonResponsible)||"",PrctrHierGrp:((Gn=u.basicDataTabDto)==null?void 0:Gn.PrctrHierGrp)||"",Segment:((Fn=u.basicDataTabDto)==null?void 0:Fn.Segment)||"",CompanyCode:((Hn=u.compCodesTabDto)==null?void 0:Hn.CompanyCode)||[],Language:((Fo=u.communicationTabDto)==null?void 0:Fo.Language)||"",Country:M,Street:((Ho=u.addressTabDto)==null?void 0:Ho.Street)||"",City:((zo=u.addressTabDto)==null?void 0:zo.City)||"",Regio:T,LockIndicator:!!((jo=u.indicatorsTabDto)!=null&&jo.LockIndicator),ReqCreatedBy:((Vo=u.historyTabDto)==null?void 0:Vo.ReqCreatedBy)||"",ReqCreatedOn:((Xo=u.historyTabDto)==null?void 0:Xo.ReqCreatedOn)||""}};b({type:"profitCenter/setProfitCenterTab",payload:Q}),b(st({uniqueId:e,keyName:"CompanyCode",data:((Jo=(Ko=u==null?void 0:u.compCodesTabDto)==null?void 0:Ko.CompanyCode)==null?void 0:Jo[0])||"",viewID:"Comp Codes"})),b(st({uniqueId:e,keyName:"profitCenterNumber",data:(u==null?void 0:u.profitCenter)||"",viewID:"Comp Codes"})),b(st({uniqueId:e,keyName:"Country",data:M,viewID:"Address"})),b(st({uniqueId:e,keyName:"Regio",data:T,viewID:"Address"})),o.useEffect(()=>{M&&e&&En(M,null,e)},[M,e]),g(w),Pe(!1),Tn(),J()},m=E=>{console.error("Error fetching profit center data",E),Pe(!1)};F(`/${te}/data/getProfitCentersData`,"post",c,m,a)},Ke=()=>{b(so(!1));const e=ur(),a=Math.max(0,...S.map(c=>c.lineNumber||0))+10;if(zt==="yes"){if(L!=null&&L.length){const c=L[0],m=Number(c.code/10-1),E=S[m];if(!E)return;const w={id:e,controllingArea:(E==null?void 0:E.controllingArea)||"",profitCenterNumber:"",longDescription:"",businessSegment:(E==null?void 0:E.businessSegment)||"",companyCode:(E==null?void 0:E.companyCode)||"",included:!0,isNew:!0,lineNumber:a};b(sn([...S,w])),b(kn({rowId:e})),ze(!1);const u=re==null?void 0:re[E.id];if(u){const M={PersonResponsible:(u==null?void 0:u.PersonResponsible)||"",PrctrHierGrp:(u==null?void 0:u.PrctrHierGrp)||"",Segment:(u==null?void 0:u.Segment)||"",CompanyCode:(u==null?void 0:u.CompanyCode)||"",Language:(u==null?void 0:u.Language)||"",Country:(u==null?void 0:u.Country)||"",Street:(u==null?void 0:u.Street)||"",City:(u==null?void 0:u.City)||"",Regio:(u==null?void 0:u.Regio)||"",LockIndicator:!!(u!=null&&u.LockIndicator),ReqCreatedBy:(u==null?void 0:u.ReqCreatedBy)||"",ReqCreatedOn:(u==null?void 0:u.ReqCreatedOn)||""};b(Hr({[e]:M})),b(st({uniqueId:e,keyName:"CompanyCode",data:M.CompanyCode,viewID:"Comp Codes"})),b(st({uniqueId:e,keyName:"Country",data:M.Country,viewID:"Address"})),b(st({uniqueId:e,keyName:"Regio",data:M.Regio,viewID:"Address"})),b(st({uniqueId:e,keyName:"profitCenterNumber",data:"",viewID:"Comp Codes"}))}return}if(Xe){const c={id:e,controllingArea:"",profitCenterNumber:"",lineNumber:a,longDescription:"",businessSegment:"",companyCode:"",included:!0,isNew:!0},m=[...S,c];b(sn(m)),b(kn({rowId:e})),ze(!1),rn(e,m)}else{const c={id:e,controllingArea:"",profitCenterNumber:"",longDescription:"",businessSegment:"",lineNumber:a,companyCode:"",included:!0,isNew:!0},m=[...S,c];b(sn(m)),b(kn({rowId:e})),ze(!1),rn(e,m)}}else if(S.length>=0){const c={id:e,controllingArea:"",profitCenterNumber:"",longDescription:"",businessSegment:"",companyCode:"",lineNumber:a,included:!0,isNew:!0};b(sn([...S,c])),b(kn({rowId:e})),ze(!1)}else console.warn("No existing valid data in rows. Skipping row addition.")};return p("div",{children:[n(Po,{openSnackBar:Ot,alertMsg:ie,handleSnackBarClose:to,alertType:je,isLoading:Ve}),n(Oo,{open:et,onClose:()=>wt(!1),title:Ut.title,message:Ut.message,subText:Ut.subText,buttonText:Ut.buttonText,redirectTo:Ut.redirectTo}),Rt&&n(De,{color:"error",children:U("Error loading data")}),n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:p(O,{sx:{position:Oe?"fixed":"relative",top:Oe?0:"auto",left:Oe?0:"auto",right:Oe?0:"auto",bottom:Oe?0:"auto",width:Oe?"100vw":"100%",height:Oe?"100vh":"auto",zIndex:Oe?1004:1,backgroundColor:Oe?"white":"transparent",padding:Oe?"20px":"0",display:"flex",flexDirection:"column",boxShadow:Oe?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[p(O,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(De,{variant:"h6",children:U("List of Profit Centers")}),p(O,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(Ue,{variant:"contained",color:"primary",size:"small",onClick:y,disabled:!G||H||Ne,children:U("+ Add")}),n(ao,{title:Oe?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(Xn,{onClick:no,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:Oe?n(yo,{}):n(Do,{})})})]})]}),n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n("div",{children:n(uo,{isLoading:He,rows:S,columns:Zn,pageSize:10,rowHeight:70,getRowIdValue:"id",selectionModel:qn,status_onRowSingleClick:!0,callback_onRowSingleClick:B,getRowClassName:e=>(h==null?void 0:h.id)===e.row.id?"Mui-selected":""})})})})]})}),q&&p(Mn,{fullWidth:!0,open:q,maxWidth:"lg",onClose:(e,r)=>{r!=="backdropClick"&&Tn()},sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(lo,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:n(De,{variant:"h6",children:"Add New ProfitCenter"})}),p(In,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[p(Mr,{component:"fieldset",sx:{paddingBottom:"2%"},children:[n(Ur,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:"How would you like to proceed?"}),p(Br,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:zt,onChange:e=>{const r=e.target.value;pn(r),r==="no"&&(jt(""),vt(""),kt(""),ke([]),X({}),qe(null))},children:[n(ar,{value:"yes",control:n(sr,{}),label:"With Reference"}),n(ar,{value:"no",control:n(sr,{}),label:"Without Reference"})]})]}),zt==="yes"&&n(xt,{container:!0,spacing:2,children:n(xt,{item:!0,xs:12,children:p(xt,{container:!0,spacing:2,children:[p(xt,{item:!0,xs:3,children:[p(De,{variant:"subtitle2",gutterBottom:!0,children:["Controlling Area",n("span",{style:{color:"red"},children:"*"})]}),n(pt,{options:_e||[],value:(_e==null?void 0:_e.find(e=>e.code===Ze))||null,onChange:e=>{ke([]),jt((e==null?void 0:e.code)||""),I(e==null?void 0:e.code,"ETP"),Vt([])},placeholder:"Select Controlling Area",minWidth:"90%",listWidth:235})]}),p(xt,{item:!0,xs:3,children:[p(De,{variant:"subtitle2",gutterBottom:!0,children:["Company Code",n("span",{style:{color:"red"},children:"*"})]}),n(pt,{options:Ce||[],value:(Ce==null?void 0:Ce.find(e=>e.code===yt))||null,onChange:e=>{ke([]),vt((e==null?void 0:e.code)||""),Vt([])},placeholder:"Select Company Code",minWidth:"90%",listWidth:235})]}),p(xt,{item:!0,xs:3,children:[p(De,{variant:"subtitle2",gutterBottom:!0,children:["Profitcenter Number",n("span",{style:{color:"red"},children:"*"})]}),n(pt,{options:On,value:On.find(e=>e.code===Dt)||null,onChange:e=>{ke([]),kt((e==null?void 0:e.code)||"")},placeholder:"Select Profit Center",minWidth:"90%",listWidth:235})]}),S.length>0&&p(an,{children:[n(xt,{item:!0,xs:1,sx:{textAlign:"center"},children:n(De,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),p(xt,{item:!0,xs:3,children:[p(De,{variant:"subtitle2",gutterBottom:!0,children:["Line Number",n("span",{style:{color:"red"},children:"*"})]}),n(pt,{options:S.map((e,r)=>({code:e.lineNumber})),value:L[0],onChange:e=>{ke(e?[e]:[]),jt(""),vt(""),kt(""),X({}),qe(null)},minWidth:180,listWidth:266,placeholder:U("Select Line Number"),disabled:xe==null?void 0:xe.code,getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,r)=>p("li",{...e,children:[n("strong",{children:r==null?void 0:r.code}),r!=null&&r.desc?` - ${r==null?void 0:r.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})]})]})]})})})]}),p(_n,{sx:{display:"flex",justifyContent:"end"},children:[n(Ue,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Tn,variant:"outlined",children:"Cancel"}),n(Ue,{className:"button_primary--normal",type:"save",disabled:zt==="yes"&&!(Ze&&yt&&Dt||(L==null?void 0:L.length)>0),onClick:Ke,variant:"contained",children:"Proceed"})]})]}),p(Mn,{open:dn,onClose:f,"aria-labelledby":"missing-fields-dialog-title",maxWidth:"sm",fullWidth:!0,children:[p(lo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[n(co,{fontSize:"medium"}),U("Missing Mandatory Fields")]}),p(In,{sx:{pt:2},children:[n(De,{variant:"body1",gutterBottom:!0,children:U("Please complete the following mandatory fields:")}),n(mr,{dense:!0,children:St.map((e,r)=>{const a=e.match(/^(Line \d+)( - .*)$/);return p(xo,{disablePadding:!0,children:[n(pr,{sx:{minWidth:30},children:n(co,{fontSize:"small",color:"warning"})}),n(No,{primary:a?p(an,{children:[n("strong",{children:a[1]}),a[2]]}):e})]},r)})})]}),n(_n,{sx:{pr:3,pb:2},children:n(Ue,{onClick:f,variant:"contained",color:"warning",sx:{textTransform:"none",fontWeight:500},children:U("Close")})})]}),p(Mn,{open:ft,onClose:()=>$e(!1),maxWidth:"sm",fullWidth:!0,children:[p(lo,{id:"missing-fields-dialog-title",sx:{backgroundColor:"#fff3e0",color:"#e65100",display:"flex",alignItems:"center",gap:1,fontWeight:"bold"},children:[n(co,{fontSize:"medium"}),U("Duplicate Description")]}),n(In,{dividers:!0,children:ue.length===0?n(De,{children:"No duplicates found."}):n(Wr,{component:Un,elevation:0,children:p(qr,{size:"small",children:[n($r,{children:p(ir,{children:[n(io,{children:n("strong",{children:"Type"})}),n(io,{children:n("strong",{children:"Remarks"})})]})}),n(Gr,{children:ue.map((e,r)=>p(ir,{children:[n(io,{children:e.type}),p(io,{children:[n("strong",{children:e.value})," found in Line(s):"," ",e.lines.join(", ")]})]},r))})]})})}),n(_n,{children:n(Ue,{onClick:()=>$e(!1),children:"Close"})})]}),h&&(pe==="true"&&W?p(O,{sx:{position:V?"fixed":"relative",top:V?0:"auto",left:V?0:"auto",right:V?0:"auto",bottom:V?0:"auto",width:V?"100vw":"100%",height:V?"100vh":"auto",zIndex:V?1004:1,backgroundColor:V?"white":"transparent",padding:V?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:V?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[p(O,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(De,{variant:"h6",children:U(`${at} Details`)}),n(ao,{title:V?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(Xn,{onClick:$n,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:V?n(yo,{}):n(Do,{})})})]}),p(O,{sx:{mt:3},children:[n(cr,{value:tt,onChange:_,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:Ge.background.container,borderBottom:`1px solid ${Ge.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:Ge.black.graphite,"&.Mui-selected":{color:Ge.primary.main,fontWeight:700},"&:hover":{color:Ge.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:Ge.primary.main,height:"3px"}},children:me.map((e,r)=>n(lr,{label:e.tab},r))}),n(Un,{elevation:2,sx:{p:3,borderRadius:4},children:me[tt]&&n(fr,{disabled:H,basicDataTabDetails:me[tt].data,dropDownData:{CompanyCode:Ce,Country:ut,Language:Gt,Template:Wt,TaxJurisdiction:nt,BusinessSegment:Tt,controllingArea:_e},activeViewTab:me[tt].tab,uniqueId:(h==null?void 0:h.id)||W||((vo=S[0])==null?void 0:vo.id),selectedRow:h||{},fieldErrors:we[(h==null?void 0:h.id)||W||((ko=S[0])==null?void 0:ko.id)]||[]},((h==null?void 0:h.id)||W||((wo=S[0])==null?void 0:wo.id))+(((Uo=we[(h==null?void 0:h.id)||W||((Mo=S[0])==null?void 0:Mo.id)])==null?void 0:Uo.join(","))||""))})]})]}):p(O,{sx:{position:V?"fixed":"relative",top:V?0:"auto",left:V?0:"auto",right:V?0:"auto",bottom:V?0:"auto",width:V?"100vw":"100%",height:V?"100vh":"auto",zIndex:V?1004:1,backgroundColor:V?"white":"transparent",padding:V?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:V?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[p(O,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(De,{variant:"h6",children:U("View Details")}),n(ao,{title:V?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(Xn,{onClick:$n,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:V?n(yo,{}):n(Do,{})})})]}),p(O,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[n(cr,{value:tt,onChange:_,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:Ge.background.container,borderBottom:`1px solid ${Ge.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:Ge.black.graphite,"&.Mui-selected":{color:Ge.primary.main,fontWeight:700},"&:hover":{color:Ge.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:Ge.primary.main,height:"3px"}},children:me.map((e,r)=>n(lr,{label:e.tab},r))}),n(Un,{elevation:2,sx:{p:3,borderRadius:4},children:me[tt]&&n(fr,{disabled:H,basicDataTabDetails:me[tt].data,dropDownData:{CompanyCode:Ce,Country:ut,Language:Gt,Template:Wt,TaxJurisdiction:nt,businessSegment:Tt},activeViewTab:me[tt].tab,uniqueId:(h==null?void 0:h.id)||W||((Bo=S[0])==null?void 0:Bo.id),selectedRow:h||{},fieldErrors:we[(h==null?void 0:h.id)||W||((Wo=S[0])==null?void 0:Wo.id)]||[]},((h==null?void 0:h.id)||W||((qo=S[0])==null?void 0:qo.id))+(((Go=we[(h==null?void 0:h.id)||W||(($o=S[0])==null?void 0:$o.id)])==null?void 0:Go.join(","))||""))}),n(O,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Bn,{handleSaveAsDraft:Le,handleSubmitForReview:Le,handleSubmitForApprove:Le,handleSendBack:Le,handleCorrection:Le,handleRejectAndCancel:Le,handleValidateAndSyndicate:Le,validateAllRows:s,isSaveAsDraftEnabled:We,validateEnabled:ct,filteredButtons:ae,moduleName:at,showWfLevels:gn,selectedLevel:Mt,workFlowLevels:Xt,setSelectedLevel:Zt})})]})]})),n(Bn,{handleSaveAsDraft:Le,handleSubmitForReview:Le,handleSubmitForApprove:Le,handleSendBack:Le,handleCorrection:Le,handleRejectAndCancel:Le,handleValidateAndSyndicate:Le,validateAllRows:s,isSaveAsDraftEnabled:We,validateEnabled:ct,filteredButtons:ae,moduleName:at,showWfLevels:gn,selectedLevel:Mt,workFlowLevels:Xt,setSelectedLevel:Zt}),n(Kn,{blurLoading:bt,loaderMessage:Ft})]})},Bs=["Request Header","Profit Center List","Attachments & Comments","Preview"],Pa=()=>{var gn,hn,Yt,Et,ht,Lt,Qt,Oe,Ht,V,fn,zt,pn,Ze,jt,yt,vt,On,Vt,Dt,kt,mn;const{t:pe}=To(),Me=R(g=>g.CommonStepper.activeStep),{getChangeTemplate:ge}=Lo(Je.PC),at=R(g=>g.changeLog.createChangeLogDataGL);let H=R(g=>g==null?void 0:g.userManagement.taskData);const Ne=R(g=>g.payload.filteredButtons),de=R(g=>g.profitCenter.payload.requestHeaderData),Re=R(g=>{var k;return(k=g.request.requestHeader)==null?void 0:k.requestId}),b=R(g=>g.applicationConfig),U=R(g=>g.request.requestHeader),A=R(g=>g.request.requestHeader.requestType),i=R(g=>g.profitCenter.payload),z=(gn=i==null?void 0:i.requestHeaderData)==null?void 0:gn.TemplateName,Z=R(g=>g.payload.dynamicKeyValues),{fetchedProfitCenterData:W,fetchReqBenchData:mt}=R(g=>g.profitCenter),me=R(g=>g.changeLog.createChangeLogDataGL),d=Jn(),[it,x]=o.useState(!1);o.useState(!1);const[Ie,q]=o.useState(!1),[He,Rt]=o.useState(!1),[Se,S]=o.useState([]),[It,re]=o.useState([!1,!1,!1]),$=Co(),[Ye,se]=o.useState(!1),[cn,ae]=o.useState(""),[h,Qe]=o.useState(!1),[tt,be]=o.useState(!1),[Ce,Ct]=o.useState([]),[Tt,he]=o.useState(""),[nt,lt]=o.useState(!1),[Wt,qt]=o.useState(),[_e,$t]=o.useState(!1),[ut,gt]=o.useState(5),[Ae,Be]=o.useState(!1),[ot,rt]=o.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),Gt=R(g=>{var k;return(k=g.profitCenterDropdownData)==null?void 0:k.isOdataApiCalled}),{fetchAllDropdownFMD:Kt}=jr(te,go),dn=g=>{d(Vn(g))},_t=R(g=>{var k;return((k=g==null?void 0:g.payload)==null?void 0:k.changeFieldSelectiondata)||[]}),St=Yn(),Jt=(hn=St==null?void 0:St.state)==null?void 0:hn.moduleName,G=St.state,ze=new URLSearchParams(St.search),We=ze.get("reqBench"),Ee=ze.get("RequestId"),ct=ze.get("RequestId"),j=ze.get("RequestType"),un=(G==null?void 0:G.childRequestIds)!=="Not Available"&&typeof H=="object"&&H!==null&&Object.keys(H).length===0&&We==="true",Pt=Object.keys(H).length!==0,Ot=()=>{Rt(!0)},v=()=>{lt(!0)};o.useEffect(()=>{var k;let g;return Ae&&ut>0&&(g=setInterval(()=>{gt(ne=>ne-1)},1e3)),ut===0&&(clearInterval(g),$((k=Fe)==null?void 0:k.REQUEST_BENCH)),()=>clearInterval(g)},[Ae,ut,$]);const ie=()=>{var Xe,ee,N,X,L,ke,xe,qe,we;const g={dtName:j===((Xe=D)==null?void 0:Xe.CREATE)||j===((ee=D)==null?void 0:ee.CREATE_WITH_UPLOAD)||A===((N=D)==null?void 0:N.CREATE)||A===((X=D)==null?void 0:X.CREATE_WITH_UPLOAD)?"MDG_PC_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT",version:j===((L=D)==null?void 0:L.CREATE)||j===((ke=D)==null?void 0:ke.CREATE_WITH_UPLOAD)||A===((xe=D)==null?void 0:xe.CREATE)||A===((qe=D)==null?void 0:qe.CREATE_WITH_UPLOAD)?"v2":"v6",requestId:ct,scenario:A||j||((we=D)==null?void 0:we.CREATE_WITH_UPLOAD),isChild:!!(H&&Object.keys(H).length!==0||G.isChildRequest)},k=dt=>{const ft=URL.createObjectURL(dt),$e=document.createElement("a");$e.href=ft,$e.setAttribute("download",`${g!=null&&g.scenario?g==null?void 0:g.scenario:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild($e),$e.click(),document.body.removeChild($e),URL.revokeObjectURL(ft),se(!1),ae(""),qt(`${g!=null&&g.scenario?g==null?void 0:g.scenario:"Mass_Create"}_Data Export.xlsx has been exported successfully.`)},ne=dt=>{console.error("Attachment fetch error:",dt)};F(`/${te}/excel/exportPCExcel`,"postandgetblob",k,ne,g)},oe=g=>{lt(g)},je=g=>{let k="";k="getAllProfitCenterFromExcel",ae("Initiating Excel Upload"),se(!0);const ne=new FormData;[...g].forEach(N=>ne.append("files",N)),ne.append("dtName",j===D.CREATE_WITH_UPLOAD||j===D.EXTEND_WITH_UPLOAD?"MDG_PC_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT"),ne.append("version",j===D.CREATE_WITH_UPLOAD||j===D.EXTEND_WITH_UPLOAD?"v2":"v6"),ne.append("requestId",Ee||""),ne.append("IsSunoco","false"),ne.append("screenName",j||"");const Xe=N=>{var X,L;N.statusCode===200?(be(!1),se(!1),ae(""),$((X=Fe)==null?void 0:X.REQUEST_BENCH)):(be(!1),se(!1),ae(""),$((L=Fe)==null?void 0:L.REQUEST_BENCH))},ee=N=>{var X;se(!1),ae(""),$((X=Fe)==null?void 0:X.REQUEST_BENCH)};F(`/${te}/massAction/${k}`,"postformdata",Xe,ee,ne)};o.useEffect(()=>(Gt||(Kt("profitCenter"),gr(Rn.MODULE,Je.PC),d(Vr(!0))),gr(Rn.MODULE,Je.PC),ye(),he(Xr("PC")),()=>{Kr(Rn.MODULE)}),[]);const ye=()=>{const g={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"ET Profit Center","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":D.CREATE,"MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null},k=ee=>{var N,X,L;if((ee==null?void 0:ee.statusCode)===200){const ke=((L=(X=(N=ee==null?void 0:ee.data)==null?void 0:N.result)==null?void 0:X[0])==null?void 0:L.MDG_ATTACHMENTS_ACTION_TYPE)??[];ke.map((xe,qe)=>({id:qe,attachmentName:xe==null?void 0:xe.MDG_ATTACHMENTS_NAME,changeEntryFields:xe==null?void 0:xe.MDG_ATTACH_CHNG_ENT_FIELDS})),Ct(ke)}else console.warn("Unexpected statusCode:",ee==null?void 0:ee.statusCode)},ne=ee=>{console.error("Attachment fetch error:",ee)},Xe=b.environment==="localhost"?Nt.INVOKE_RULES.LOCAL:Nt.INVOKE_RULES.PROD;F(`/${Ao}${Xe}`,"post",k,ne,g)},Ve=(g="",k="")=>{const ne=ee=>{d(bs({keyName:"CompCode",data:(ee==null?void 0:ee.body)||[]}))},Xe=ee=>{console.log(ee)};F(`/${te}/data/getCompCodeBasedOnControllingArea?controllingArea=${g}&rolePrefix=${k}`,"get",ne,Xe)},le=g=>{se(!0);const k=(G==null?void 0:G.childRequestIds)!=="Not Available",ne={sort:"id,asc",parentId:k?"":g,massCreationId:k&&(j===D.CREATE||j===D.CREATE_WITH_UPLOAD)?g:"",massChangeId:k&&(j===D.CHANGE||j===D.CHANGE_WITH_UPLOAD)?g:"",page:0,size:10},Xe=N=>{var dt,ft,$e,ue,Cn;if(se(!1),((dt=N==null?void 0:N.body)==null?void 0:dt.status)==="Processing"){rt({message:"Request is in Process !!",subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),gt(5),Be(!0);return}const X=(N==null?void 0:N.body)||[];d(gs(N==null?void 0:N.totalElements)),(N==null?void 0:N.totalPages)===1||(N==null?void 0:N.currentPage)+1===(N==null?void 0:N.totalPages)?(d(hr(N==null?void 0:N.totalElements)),d(hs(!0))):d(hr(((N==null?void 0:N.currentPage)+1)*(N==null?void 0:N.pageSize)));let L=(ft=N==null?void 0:N.body[0])==null?void 0:ft.Torequestheaderdata,ke=($e=N==null?void 0:N.body[0])==null?void 0:$e.TotalIntermediateTasks,xe={RequestId:L.RequestId,RequestPrefix:L.RequestPrefix,ReqCreatedBy:L.ReqCreatedBy,ReqCreatedOn:L.ReqCreatedOn,ReqUpdatedOn:L.ReqUpdatedOn,RequestType:L.RequestType,RequestDesc:L.RequestDesc,RequestStatus:L.RequestStatus,RequestPriority:L.RequestPriority,FieldName:L.FieldName,TemplateName:L.TemplateName,Division:L.Division,region:L.region,leadingCat:L.leadingCat,firstProd:L.firstProd,launchDate:L.launchDate,isBifurcated:L.isBifurcated,screenName:L.screenName,TotalIntermediateTasks:ke,childRequestId:(Cn=(ue=X==null?void 0:X[0])==null?void 0:ue.ToChildHeaderdata)==null?void 0:Cn.RequestId};d(fs(xe)),S(X);const qe=L==null?void 0:L.RequestType;(qe===D.CHANGE||qe===D.CHANGE_WITH_UPLOAD)&&ge();const we=ps(X);X.forEach(et=>{var wt,Xt,fe,Mt;if(et.ProfitCenterID&&(ms(et.Country,d,et.ProfitCenterID),Cs(et.COArea,d,et.ProfitCenterID)),(wt=X==null?void 0:X[0])!=null&&wt.COArea&&Ve((Xt=X==null?void 0:X[0])==null?void 0:Xt.COArea,"ETP"),(fe=et==null?void 0:et.Torequestheaderdata)!=null&&fe.TemplateName){let Zt=(Mt=et==null?void 0:et.Torequestheaderdata)==null?void 0:Mt.TemplateName;const qn=_t.filter(Y=>(Y==null?void 0:Y.MDG_CHANGE_TEMPLATE_NAME)===Zt&&(Y==null?void 0:Y.MDG_MAT_CHANGE_TYPE)==="Item"&&(Y==null?void 0:Y.MDG_MAT_FIELD_VISIBILITY)!=="Hidden"&&(Y==null?void 0:Y.MDG_MAT_FIELD_VISIBILITY)!=="Display").sort((Y,en)=>{const Ut=Number(Y==null?void 0:Y.MDG_MAT_FIELD_SEQUENCE)||0,tn=Number(en==null?void 0:en.MDG_MAT_FIELD_SEQUENCE)||0;return Ut-tn}),Ln=[...new Set(qn.map(Y=>Y==null?void 0:Y.MDG_MAT_FIELD_NAME).filter(Boolean))].map(Y=>({code:Y}));d(go({keyName:"FieldName",data:Ln||[]}))}}),d(Ts(we==null?void 0:we.payload)),d(Ss(we==null?void 0:we.payload))},ee=N=>{console.error("Error fetching PC Create data:",N)};F(`/${te}/data/displayMassProfitCenterDto`,"post",Xe,ee,ne)};o.useEffect(()=>{it&&re(g=>{const k=[...g];return k[0]=!0,k})},[it]),o.useEffect(()=>{Ie&&re(g=>{const k=[...g];return k[1]=!0,k})},[Ie]),o.useEffect(()=>((async()=>{ct?(await le(ct),(j===D.CHANGE_WITH_UPLOAD&&!(G!=null&&G.length)||j===D.CREATE_WITH_UPLOAD||j===D.EXTEND_WITH_UPLOAD)&&((G==null?void 0:G.reqStatus)===jn.DRAFT||(G==null?void 0:G.reqStatus)===jn.UPLOAD_FAILED)?(d(Vn(0)),x(!1),q(!1)):(d(Vn(1)),x(!0),q(!0))):d(Vn(0))})(),()=>{d(Jr()),d(Yr()),d(Cr({})),d(Qr()),d(Zr()),d(es()),d(go({keyName:"FieldName",data:[]}))}),[Ee,d]);const bt=()=>{var g,k,ne;Ee&&!We?$((g=Fe)==null?void 0:g.MY_TASK):We?$((k=Fe)==null?void 0:k.REQUEST_BENCH):!Ee&&!We&&$((ne=Fe)==null?void 0:ne.MASTER_DATA_PC)},Pe=()=>{Qe(!1)},Wn={profitCenterDetails:j===((Yt=D)==null?void 0:Yt.CREATE)||j===((Et=D)==null?void 0:Et.CREATE_WITH_UPLOAD)||A===((ht=D)==null?void 0:ht.CREATE)||A===((Lt=D)==null?void 0:Lt.CREATE_WITH_UPLOAD)?mo(i,U,Ee,H):po(de,U,H,mt,W,me),dtName:j===((Qt=D)==null?void 0:Qt.CREATE)||j===((Oe=D)==null?void 0:Oe.CREATE_WITH_UPLOAD)||A===((Ht=D)==null?void 0:Ht.CREATE)||A===((V=D)==null?void 0:V.CREATE_WITH_UPLOAD)?"MDG_PC_FIELD_CONFIG":"MDG_CHANGE_TEMPLATE_DT",version:j===((fn=D)==null?void 0:fn.CREATE)||j===((zt=D)==null?void 0:zt.CREATE_WITH_UPLOAD)||A===((pn=D)==null?void 0:pn.CREATE)||A===((Ze=D)==null?void 0:Ze.CREATE_WITH_UPLOAD)?"v2":"v6",requestId:Ee||"",scenario:j||A,templateName:(de==null?void 0:de.TemplateName)||"",region:"US"},Pn=async(g,k)=>{var X,L,ke,xe,qe,we,dt,ft,$e;se(!0);let ne=(qe=(xe=(ke=(X=Nt)==null?void 0:X.MASTER_BUTTON_APIS)==null?void 0:ke[(L=Je)==null?void 0:L.PC])==null?void 0:xe[de==null?void 0:de.RequestType])==null?void 0:qe[g];const Xe=j===((we=D)==null?void 0:we.CREATE)||j===((dt=D)==null?void 0:dt.CREATE_WITH_UPLOAD)||A===((ft=D)==null?void 0:ft.CREATE)||A===(($e=D)==null?void 0:$e.CREATE_WITH_UPLOAD)?mo(i,U,Ee,H,Z,at,k):po(de,U,H,mt,W,me,k),ee=ue=>{se(!1),(ue==null?void 0:ue.statusCode)===ln.STATUS_200||(ue==null?void 0:ue.statusCode)===ln.STATUS_201?(rt({title:Te.TITLE,message:ue.message,subText:Te.SUBTEXT,buttonText:Te.BUTTONTEXT,redirectTo:Te.REDIRECT}),Be(!0)):(ue==null?void 0:ue.statusCode)===ln.STATUS_500||(ue==null?void 0:ue.statusCode)===ln.STATUS_501?(rt({title:ve.TITLE,message:ue.message,subText:ve.SUBTEXT,buttonText:ve.BUTTONTEXT,redirectTo:ve.REDIRECT}),Be(!0)):(setSnackbarOpen(!0),setAlertMsg("Unexpected response received."))},N=ue=>{se(!1),showSnackbar(ue==null?void 0:ue.error,"error")};F(ne==null?void 0:ne.URL,"POST",ee,N,Xe)};return p("div",{children:[p(O,{sx:{padding:2},children:[p(xt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Ee||Re?p(O,{children:[p(De,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(Os,{sx:{fontSize:"1.5rem"}}),pe("Request Header ID:")," ",n("span",{children:Re?U==null?void 0:U.requestId:`${Ee}`})]}),z&&p(De,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(_o,{sx:{fontSize:"1.5rem"}}),"Template Name: ",n("span",{children:z})]})]}):n("div",{style:{flex:1}}),nt&&n(Rs,{module:Je.PC,open:!0,closeModal:oe,requestId:(de==null?void 0:de.RequestId)||Ee,requestType:"create"}),Me===1&&p(O,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[n(Ue,{variant:"outlined",size:"small",title:"Error Report",disabled:!ct,onClick:()=>$t(!0),color:"primary",children:n(ss,{sx:{padding:"2px"}})}),n(Ue,{variant:"outlined",disabled:!ct,size:"small",onClick:v,title:"Change Log",children:n(Ls,{sx:{padding:"2px"}})}),n(Ue,{variant:"outlined",disabled:!ct,size:"small",onClick:ie,title:"Export Excel",children:n(vs,{sx:{padding:"2px"}})})]})]}),n(Xn,{onClick:()=>{var g;if(We==="true"){$((g=Fe)==null?void 0:g.REQUEST_BENCH);return}Qe(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:n(as,{sx:{fontSize:"25px",color:"#000000"}})}),n(cs,{nonLinear:!0,activeStep:Me,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"-35px 14% 10px",marginTop:"-35px"},children:Bs.map((g,k)=>n(is,{children:n(ls,{color:"inherit",disabled:k===1&&!it,onClick:()=>dn(k),sx:{fontSize:"50px",fontWeight:"bold"},children:n("span",{style:{fontSize:"15px",fontWeight:"bold"},children:g})})},g))}),Me===0&&p(an,{children:[n(Ms,{apiResponse:Se,reqBench:We,downloadClicked:He,setDownloadClicked:Rt,setIsSecondTabEnabled:x,setIsAttachmentTabEnabled:q}),(j===D.CHANGE_WITH_UPLOAD||j===D.CREATE_WITH_UPLOAD||j===D.EXTEND_WITH_UPLOAD)&&((G==null?void 0:G.reqStatus)==jn.DRAFT&&!((jt=G==null?void 0:G.material)!=null&&jt.length)||(G==null?void 0:G.reqStatus)==jn.UPLOAD_FAILED)&&n(ks,{handleDownload:Ot,setEnableDocumentUpload:be,enableDocumentUpload:tt,handleUploadMaterial:je})]}),Me===1&&de.RequestType&&(de.RequestType==="Change"||de.RequestType==="Change with Upload"?n(Er,{reqBench:We,requestId:Ee,apiResponses:Se,setIsAttachmentTabEnabled:q,setCompleted:re,downloadClicked:He,setDownloadClicked:Rt,template:z,module:(yt=Je)==null?void 0:yt.PC,isDisabled:un,fieldDisable:Pt}):n(Us,{reqBench:We,apiResponses:Se,setIsAttachmentTabEnabled:q,setCompleted:re,module:(vt=Je)==null?void 0:vt.PC,isDisabled:un,fieldDisable:Pt})),Me===2&&n(ys,{requestStatus:G!=null&&G.reqStatus?G==null?void 0:G.reqStatus:jn.ENABLE_FOR_FIRST_TIME,attachmentsData:Ce,requestIdHeader:Re||Ee,pcNumber:Tt,module:(On=Je)==null?void 0:On.PC,artifactName:ts.PC}),Me===3&&n(O,{sx:{width:"100%",overflow:"auto"},children:n(Ds,{module:(Vt=Je)==null?void 0:Vt.PC,payloadForPreviewDownloadExcel:Wn})})]}),n(Oo,{open:Ae,onClose:()=>Be(!1),title:ot.title,message:ot.message,subText:ot.subText,buttonText:ot.buttonText,redirectTo:ot.redirectTo}),n(Kn,{blurLoading:Ye,loaderMessage:cn}),n(As,{dialogState:_e,closeReusableDialog:()=>$t(!1),module:(Dt=Je)==null?void 0:Dt.PC,isHierarchyCheck:!1}),h&&p(ns,{isOpen:h,titleIcon:n(ds,{size:"small",sx:{color:(mn=(kt=Ge)==null?void 0:kt.secondary)==null?void 0:mn.amber,fontSize:"20px"}}),Title:"Warning",handleClose:Pe,children:[n(In,{sx:{mt:2},children:us.LEAVE_PAGE_MESSAGE}),p(_n,{children:[n(Ue,{variant:"outlined",size:"small",sx:{...os},onClick:Pe,children:pe("No")}),n(Ue,{variant:"contained",size:"small",sx:{...rs},onClick:bt,children:pe("Yes")})]})]}),Me!==0&&Me!==1&&n(Bn,{handleSaveAsDraft:Pn,handleSubmitForReview:Pn,filteredButtons:Ne,moduleName:Jt})]})};export{Pa as default};
