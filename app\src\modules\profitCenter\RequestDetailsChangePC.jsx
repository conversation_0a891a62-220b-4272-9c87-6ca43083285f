import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  Select,
  Tooltip,
  MenuItem,
  FormControl,
  Slide,
  tooltipClasses,
  InputLabel,
  Typography,
  IconButton,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
  RadioGroup,
  FormControlLabel,
  Radio,
  List,
  ListItem,
  ListItemIcon,
} from "@mui/material";
import { destination_ProfitCenter_Mass } from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";

import DownloadDialog from "../../components/Common/DownloadDialog";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";

import {
  API_CODE,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADER_MESSAGES,
  MODULE_MAP,
  SUCCESS_DIALOG_MESSAGE,
  FAILURE_DIALOG_MESSAGE,
  BUTTON_TYPE,
} from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformProfitCenterResponseChange,
  showToast,
} from "../../functions";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";
import {
  setFetchedProfitCenterDataPc,
  setOriginalProfitCenterDataPc,
  setFetchReqBenchDataPc,
  setOriginalReqBenchDataPc,
  updateProfitCenterRowPc,
  setShowGrid,
  updateReqBenchRowPc,
  setChangedFieldsMapPc,
  resetProfitCenterStatePc,
} from "@app/profitCenterTabsSlice";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableDataTable from "../../components/Common/ReusableTable";

import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import useLang from "@hooks/useLang";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import useProfitCenterChangeFieldConfig from "@hooks/useProfitCenterChangeFieldConfig";
import { getFieldsForTemplate } from "@helper/fieldHelper";
import { colors } from "@constant/colors";
import ObjectLockDialog from "@components/Common/ObjectLockDialog";
import { changePayloadForPC } from "../../functions";
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import SuccessDialog from "@components/Common/SubmitDialog";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { END_POINTS } from "@constant/apiEndPoints";
import useDynamicWorkflowDTPC from "./useDynamicWorkflowDTPC";
import { setProfitCenterApiData } from "@app/redux/profitCenterTabSlice";

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const RequestDetailsChangePC = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
  module,
  template,
  isDisabled,
  fieldDisable,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { t } = useLang();
  const { fetchedProfitCenterData, fetchReqBenchData, changedFieldsMap } =
    useSelector((state) => state.profitCenter);
    const changeChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogDataGL
  );

  const showGrid = useSelector((state) => state.profitCenter.showGrid);

  const requestHeaderData = useSelector(
    (state) => state.profitCenter.payload.requestHeaderData
  );
  
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  

  const { getChangeTemplate } =
    useProfitCenterChangeFieldConfig("Profit Center");

  const initialPayload = useSelector((state) => state.request.requestHeader);

  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const taskData = useSelector((state) => state?.userManagement.taskData);

  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");

  const { updateChangeLogGlForChange } = useChangeLogUpdateGl();

  const [open, setOpen] = useState(true);
  const [dropdown1Value, setDropdown1Value] = useState("");
  const [dropdown2Value, setDropdown2Value] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [profitCenterOptions, setProfitCenterOptions] = useState([]);
  const [selectedProfitCenters, setSelectedProfitCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [regionOptionsMap, setRegionOptionsMap] = useState({});
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [dropdownBusinessSegment, setDropdownBusinessSegment] = useState([]);
  const [profitcenterResponse, setProfitcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [isValidated, setIsValidated] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);

  const [wfLevels,setWfLevels] = useState([]);
  const [selectedLevel, setSelectedLevel] = useState('');

  const [missingFields, setMissingFields] = useState([]);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);

  const [dialogData, setDialogData] = useState({
    title: "",
    message: "",
    subText: "",
    buttonText: "",
    redirectTo: "",
  });

  const { getDynamicWorkflowDT } = useDynamicWorkflowDTPC();

  useEffect(() => {
      const fetchWorkflowLevels = async () => {
        try {
          const workflowLevelsDtData = await getDynamicWorkflowDT(
            rowsHeaderData?.[0]?.businessSegment,
            taskData?.ATTRIBUTE_3,
            "v4",
            "MDG_DYNAMIC_WF_PC_DT",
            MODULE_MAP?.PC
          );
          setWfLevels(workflowLevelsDtData);
        } catch (err) {
          customError(err);
        }
      };
      if (requestHeaderData?.Region && taskData?.ATTRIBUTE_3) {
        fetchWorkflowLevels();
      }
    }, [requestHeaderData?.Region, taskData?.ATTRIBUTE_3]);

  const { getButtonsDisplayGlobal,showWfLevels } = useButtonDTConfig();

  const changeFieldDataRaw = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata
  );
  const changeFieldData = Array.isArray(changeFieldDataRaw)
    ? changeFieldDataRaw
    : [];

  const fieldNameListRaw = requestHeaderData?.FieldName;
  const reqBenchFieldName = apiResponses?.[0]?.Torequestheaderdata?.FieldName;

  const rawFieldName = reqBench ? reqBenchFieldName : fieldNameListRaw;
  const fieldNameList = Array.isArray(rawFieldName)
    ? rawFieldName.map((f) => f.trim())
    : typeof rawFieldName === "string"
    ? rawFieldName.split(",").map((f) => f.trim())
    : [];
  
  const { allFields, mandatoryFields, headerFields, fieldMetaMap } =
    getFieldsForTemplate(changeFieldData, fieldNameList);

  useEffect(() => {
    if (taskData?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("Profit Center", "MDG_DYN_BTN_DT", "v3");
    }
  }, [taskData]);

  // Function to handle opening and closing the popup
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };
  const getObjectLock = (params) => {
  

  if (!selectedProfitCenters.length || !dropdown1Value) return;

  const objectLockPayload = selectedProfitCenters?.map((pc) => ({
    controllingArea: dropdown1Value,
    profitCenter: pc,
    changedFieldsToCheck: initialPayload?.fieldName ?? requestHeaderData?.FieldName,
  }));

  const successHandler = (response) => {
    const hasError = response?.some((item) => item?.statusCode !== 200);

    if (!hasError) {
      if (params === "OK") {
        setOpen(false);

        if (reqBench !== "true") {
          dispatch(setShowGrid(true));
        }

        fetchProfitCenterDetails(); 
      } else if (params === "Download") {
        handleDownloadDialogOpen();
      }
    } else {
      const filteredData = response.filter((item) => item.statusCode === 400);
      let duplicateFieldsArr = [];

      filteredData?.forEach((item, index) => {
        const dataHash = {
          id: `${item?.body?.profitCenter}_${index}`, // ✅ UNIQUE ID
          objectNo: item?.body?.profitCenter,
          reqId: item?.body?.matchingRequests
            ?.map((req) => req?.matchingRequestHeaderId)
            ?.filter(Boolean),
          childReqId: item?.body?.matchingRequests
            ?.map((req) => req?.matchingChildHeaderIdsSet)
            ?.filter(Boolean),
          requestedBy: item?.body?.matchingRequests
            ?.map((req) => req?.RequestCreatedBy)
            ?.filter(Boolean),
        };

        duplicateFieldsArr.push(dataHash);
      });

      setDuplicateFieldsData(duplicateFieldsArr);
      setShowTableInDialog(true);
    }
  };

  const errorHandler = (err) => {
    console.error("Failed to fetch profit center details", err);
  };

  doAjax(
    `/${destination_ProfitCenter_Mass}/alter/checkDuplicatePCRequest`,
    "post",
    successHandler,
    errorHandler,
    objectLockPayload
  );
};


  const handleOk = (params) => {
    getObjectLock(params);
  };

  const handleDropdownDataChange = (e, params, value) => {
    const newValue = e?.code;
    updateChangeLogGlForChange({
      uniqueId: params.row.id,
      jsonName: params?.field,
      currentValue: newValue,
      requestId: initialPayload?.RequestId,
      childRequestId: requestId,
    });
    const updatedRow = {
      ...params.row,
      [params.field]: newValue,
    };
    if (reqBench) {
      dispatch(updateReqBenchRowPc(updatedRow));
    } else {
      dispatch(updateProfitCenterRowPc(updatedRow));
    }
  };
  const allColumns = [
    {
  field: "included",
  width: 80,
  align: "center",
  headerAlign: "center",
  sortable: false,
  disableColumnMenu: true,
  renderHeader: () => {
    const data = reqBench ? fetchReqBenchData : fetchedProfitCenterData;
    const allSelected = data.every((row) => row.included);
    const someSelected = data.some((row) => row.included);

    return (
      <Checkbox
        checked={allSelected}
        indeterminate={!allSelected && someSelected}
        disabled={isDisabled || fieldDisable}
        onChange={(e) => {
          const updatedData = data.map((row) => ({
            ...row,
            included: e.target.checked,
          }));
          if (reqBench) {
            dispatch(setFetchReqBenchDataPc(updatedData));
          } else {
            dispatch(setFetchedProfitCenterDataPc(updatedData));
          }
        }}
      />
    );
  },
  renderCell: (params) => {
    const data = reqBench ? fetchReqBenchData : fetchedProfitCenterData;
    const rowIndex = data.findIndex((row) => row.id === params.row.id);

    return (
      <Checkbox
        checked={data[rowIndex]?.included || false}
        disabled={isDisabled || fieldDisable}
        onChange={(e) => {
          const updatedData = [...data];
          updatedData[rowIndex] = {
            ...updatedData[rowIndex],
            included: e.target.checked,
          };
          if (reqBench) {
            dispatch(setFetchReqBenchDataPc(updatedData));
          } else {
            dispatch(setFetchedProfitCenterDataPc(updatedData));
          }
        }}
      />
    );
  },
},




    {
      field: "lineNumber",
      headerName: "Sl No",
      width: 70,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchData : fetchedProfitCenterData
        ).findIndex((row) => row.id === params.row.id);

        return <div>{rowIndex + 1}</div>;
      },
    },

    {
      field: "controllingArea",
      headerName: "Controlling Area",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "companyCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "profitCenter",
      headerName: "Profit Center",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },

    {
      field: "profitCenterName",
      headerName: "Short Description",
      width: 250,
      
      editable: true,
    },
    {
      field: "description",
      headerName: "Long Description",
      width: 300,
      
      editable: true,
    },
    {
      field: "segment",
      headerName: "Segment",
      width: 150,
      
      editable: false,
      renderCell: (params) => {
        const field = params.field;

        const options = dropdownDataSegment || [];

        // Try to find the current value in dropdown options
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(e) => handleDropdownDataChange(e, params, selectedValue)}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={isDisabled}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    //NOTE:use for other template
    // {
    //   field: "pcaamnum",
    //   headerName: "PC AAM Number",
    //   width: 150,
    //   editable: true,
    // },
    {
      field: "businessSegment",
      headerName: "Business Segment",
      width: 200,
      
      editable: true,
      renderCell: (params) => {
        const field = params.field;
        const options = dropdownBusinessSegment || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(e) => handleDropdownDataChange(e, params, selectedValue)}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={isDisabled}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "userResponsible",
      headerName: "PC User Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "personResponsible",
      headerName: "PC Person Responsible",
      width: 250,
      editable: true,
    },
    {
      field: "lockIndicator",
      headerName: "Lock Indicator",
      width: 150,
      
      editable: false,
      renderCell: () => {
        if (requestHeaderData?.TemplateName === "All Other Fields") {
          return "Unblock";
        } else if (requestHeaderData?.TemplateName === "Block") {
          return "Block";
        }
      },
    },

    { field: "createdBy", headerName: "Created By", width: 150 },
    {
      field: "validFrom",
      headerName: "Valid From",
      width: 150,
    },
    { field: "validTo", headerName: "Valid To", width: 150 },
    { field: "city", headerName: "City", width: 150, editable: true },

    {
      field: "country",
      headerName: "Country/Reg.",
      width: 150,
      
      editable: true,
      renderCell: (params) => {
        const options = dropdownDataCountry || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row.country ||
              item.desc === params.row.country
          ) || null;

        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
           
            onChange={(e) => handleDropdownDataChange(e, params, selectedValue)}
            placeholder="Select Country"
            disabled={isDisabled}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "region",
      headerName: "Region",
      width: 150,
      editable: true,
    
      renderCell: (params) => {
  const selectedCountry = params.row.country;

  // if country is selected but no regions are loaded yet → call API
  if (selectedCountry && !regionOptionsMap[selectedCountry]) {
    getRegionBasedOnCountry(selectedCountry);
  }

  const options = regionOptionsMap[selectedCountry] || [];
  const selectedValue =
    options.find((item) => item.code === params.row.region) || null;

  return (
    <SingleSelectDropdown
      options={options}
      value={selectedValue}
      onChange={(e) => handleDropdownDataChange(e, params, selectedValue)}
      placeholder="Select Region"
      disabled={isDisabled}
      minWidth="90%"
      listWidth={235}
    />
  );
}

    },

    { field: "street", headerName: "Street", width: 150, editable: true },
    { field: "pocode", headerName: "Postal Code", width: 150, editable: true },
    // { field: "region", headerName: "Region", width: 150, editable: true },
    { field: "name1", headerName: "Name 1", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];

  const handleGridTextChange = (params) => (e) => {
    const newValue = e.target.value.toUpperCase();
    params.api.setEditCellValue({
      id: params.id,
      field: params.field,
      value: newValue,
    });

  
    updateChangeLogGlForChange({
      uniqueId: params.row.id,
      jsonName: params?.field,
      currentValue: newValue,
      requestId: initialPayload?.RequestId,
      childRequestId: requestId,
    });
  };

  const fixedColumns = allColumns.slice(0, 2); // Included & Sl No
  const businessSegmentColumn = {
    field: "businessSegment",
    headerName: "Business Segment",
    width: 200,
    editable: true,
    renderCell: (params) => {
      const field = params.field;
      const options = dropdownBusinessSegment || [];

      const selectedValue =
        options.find(
          (item) =>
            item.code === params.row[field] || item.desc === params.row[field]
        ) || null;

      return (
        <SingleSelectDropdown
          options={options}
          value={selectedValue}
          onChange={(e) => handleDropdownDataChange(e, params, selectedValue)}
          placeholder="Select Business Segment"
          disabled={isDisabled}
          minWidth="90%"
          listWidth={235}
        />
      );
    },
    renderHeader: () => (
      <span>
        Business Segment{" "}
        {mandatoryFields.includes("Business Segment") && (
          <span style={{ color: "red" }}>*</span>
        )}
      </span>
    ),
  };

  const dynamicColumns = allColumns
    .slice(2)
    .filter((col) => allFields.includes(col.headerName?.trim()))
    .map((col) => {
      const trimmedHeader = col.headerName?.trim();
      const isMandatory = mandatoryFields.includes(trimmedHeader);

      const isEditableField = [
        "description",
        "profitCenterName",
        "personResponsible",
        "city",
        "street",
        "pocode",
        "name1",
        "name2",
        "name3",
        "name4",
      ].includes(col.field);

      const normalizedField = col.field?.toLowerCase();

      const fieldMeta = fieldMetaMap[normalizedField] || {};
      const maxLength = fieldMeta.maxLength;

      return {
        ...col,
        headerName: trimmedHeader,
        editable: true,
        renderHeader: () =>
          isMandatory ? (
            <span>
              {trimmedHeader} <span style={{ color: "red" }}>*</span>
            </span>
          ) : (
            trimmedHeader
          ),
        ...(isEditableField && {
          renderCell: (params) => (
            <Box sx={{ position: "relative", width: "100%" }}>
              <TextField
                value={params.value || ""}
                variant="outlined"
                size="small"
                disabled={isDisabled}
                
                onChange={handleGridTextChange(params)}
                fullWidth
                InputProps={{
                  style: {
                    paddingBottom: "5px",
                  },
                }}
              />
              <Box
                sx={{
                  position: "absolute",
                  bottom: 0,
                  left: 14,
                  fontSize: "8px",
                  color: "blue",
                  pointerEvents: "none",
                }}
              >
                {params.value?.length || 0}/{maxLength}
              </Box>
            </Box>
          ),

          renderEditCell: (params) => {
            const currentValue = params.value || "";
           
            const currentLength = currentValue.length;
            const hasReachedMax = currentLength >= maxLength;

            return (
              <Box sx={{ position: "relative", width: "100%" }}>
                <TextField
                  value={currentValue}
                  autoFocus
                  onFocus={(e) => {
                    e.target.setSelectionRange(0, e.target.value.length);
                  }}
                  
                  onChange={handleGridTextChange(params)}
                  variant="outlined"
                  size="small"
                  fullWidth
                  placeholder={`Enter ${trimmedHeader}`}
                  inputProps={{
                    maxLength: maxLength + 1, // Allow 1 extra to detect overflow visually
                    style: { paddingBottom: "15px" },
                  }}
                />
                <Box
                  sx={{
                    position: "absolute",
                    bottom: 0,
                    left: 14,
                    fontSize: "9px",
                    color: hasReachedMax ? "red" : "blue",
                    pointerEvents: "none",
                  }}
                >
                  {hasReachedMax
                    ? "Max length reached"
                    : `${currentLength}/${maxLength}`}
                </Box>
              </Box>
            );
          },
        }),
      };
    });

  // Final columns array
  const columns = [...fixedColumns, ...dynamicColumns, businessSegmentColumn];

  const mandatoryFieldName = [
    { field: "profitCenterName", headerName: "Short Description" },
    { field: "description", headerName: "Long Description" },
    { field: "personResponsible", headerName: "PC Person Responsible" },
    // {field: "segment",headerName: "Segment"}
  ];

  const processRowUpdate = (newRow) => {
    dispatch(updateProfitCenterRowPc(newRow));

    return newRow;
  };

  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowPc(newRow));

    return newRow;
  };

  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };

  useEffect(() => {
    if (dropdown1Value && selectedCompanyCodes.length > 0) {
      fetchProfitCenters();
    }
  }, [dropdown1Value, selectedCompanyCodes]);

  useEffect(() => {
    getCompanyCode();
  }, []);

  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCompCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };

  const getSegment = () => {
    const hSuccess = (data) => {
      setDropdownDataSegment(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Segment", data: data.body },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getSegment`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessSegment = () => {
    const hSuccess = (data) => {
      const formatted = (data.body || []).map((item) => ({
        code: item,
        desc: item,
      }));

      setDropdownBusinessSegment(formatted);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "businessSegment", data: formatted },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getBusinessSegment`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCountryData = () => {
    const hSuccess = (data) => {
      setDropdownDataCountry(data.body || []);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body || [] },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };

  const getRegionBasedOnCountry = (countryCode) => {
    const hSuccess = (data) => {
      setRegionOptionsMap((prev) => ({
        ...prev,
        [countryCode]: data.body || [],
      }));

      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: `Region_${countryCode}`, data: data.body || [] },
      });
    };

    const hError = (error) => {
      console.log(error);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  useEffect(() => {
    getCountryData();
  }, []);
  useEffect(() => {}, [dropdownDataCountry]);

  

  useEffect(() => {
    getSegment();
    getBusinessSegment();
  }, []);

  useEffect(() => {
    if (selectedCompanyCodes.length === 0) {
      setProfitCenterOptions([]);
      setSelectedProfitCenters([]);
    } else {
      fetchProfitCenters(selectedCompanyCodes);
    }
  }, [selectedCompanyCodes]);

  const handleCloseDialog = () => {
    setMissingFieldsDialogOpen(false);
    setMissingFields([]);
  };

  const fetchProfitCenters = (companyCodes = []) => {
    setProfitCenterOptions([]); // clear before fetching

    const controllingArea = `${dropdown1Value}`;

    companyCodes.forEach((companyCode) => {
      const payload = {
        controllingArea,
        companyCode,
        top: "100",
        skip: "0",
      };

      doAjax(
        `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
        "post",
        (data) => {
          if (Array.isArray(data.body?.list)) {
            const newProfitCenters = data.body.list.map((item) => ({
              code: item.code,
              desc: item.desc,
              companyCode: companyCode, // Needed for filtering
            }));

            // Merge without duplicates
            setProfitCenterOptions((prev) => {
              const existingCodes = new Set(prev.map((pc) => pc.code));
              const uniqueNew = newProfitCenters.filter(
                (pc) => !existingCodes.has(pc.code)
              );
              return [...prev, ...uniqueNew];
            });
          }
        },
        (err) => console.error("Profit Center fetch failed", err),
        payload
      );
    });
  };

  const transformProfitCenterData = (data) => {
    const isBlockTemplate = template?.toLowerCase().includes("block");

    return data.map((item) => ({
      id: item.profitCenter,
      profitCenter: item.profitCenter,
      controllingArea: item.controllingArea,
      profitCenterName: item.profitCenterName,
      description: item.basicDataTabDto?.Description || "",
      segment: item.basicDataTabDto?.Segment || "",
      pcaamnum: item.basicDataTabDto?.PCAAMNumber || "",
      lockIndicator:
        isBlockTemplate && !item?.indicatorsTabDto?.LockIndicator ? "X" : "",
      userResponsible: item.basicDataTabDto?.UserResponsible || "",
      personResponsible: item.basicDataTabDto?.PersonResponsible || "",
      createdBy: item.historyTabDto?.ReqCreatedBy || "",
      validFrom: item.basicDataTabDto?.ValidfromDate || "",
      validTo: item.basicDataTabDto?.ValidtoDate || "",
      city: item.addressTabDto?.City || "",
      country: item.addressTabDto?.Country || "",
      street: item.addressTabDto?.Street || "",
      pocode: item.addressTabDto?.PostalCode || "",
      region: item.addressTabDto?.Regio || "",
      name1: item.addressTabDto?.Name1 || "",
      name2: item.addressTabDto?.Name2 || "",
      name3: item.addressTabDto?.Name3 || "",
      name4: item.addressTabDto?.Name4 || "",
      

      // ✅ Add this to fetch first company code (if array is not empty)
      companyCode: item.compCodesTabDto?.CompanyCode?.[0] || "",
    }));
  };

  const fetchProfitCenterDetails = () => {
    if (!selectedProfitCenters.length || !dropdown1Value) return;

    const payload = {
      coAreaPCs: selectedProfitCenters.map((pc) => ({
        profitCenter: pc,
        controllingArea: dropdown1Value,
      })),
    };

    const successHandler = (data) => {
      const rawData = data?.body || [];

      setProfitcenterResponse(rawData);

      const transformed = transformProfitCenterData(rawData);
      if(reqBench==="true"){
              dispatch(setFetchReqBenchDataPc(transformed));
            }else{
              dispatch(setFetchedProfitCenterDataPc(transformed));
            }
       

      dispatch(setFetchedProfitCenterDataPc(transformed));
      dispatch(setOriginalProfitCenterDataPc(transformed));
    };

    const errorHandler = (err) => {
      console.error("Failed to fetch profit center details", err);
    };

    doAjax(
      `/${destination_ProfitCenter_Mass}/data/getProfitCentersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  useEffect(() => {
    if (
      reqBench === "true" &&
      template &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchData.length === 0
    ) {
      const transformedData = transformProfitCenterResponseChange(
        apiResponses,
        template
      );
      dispatch(setFetchReqBenchDataPc(transformedData));
      dispatch(setOriginalReqBenchDataPc(transformedData));
    }
  }, [apiResponses, reqBench, template]);

  useEffect(() => {
    if (
      taskData &&
      Object.keys(taskData).length !== 0 &&
      template &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0
    ) {
      const transformedData = transformProfitCenterResponseChange(
        apiResponses,
        template
      );

      dispatch(setFetchReqBenchDataPc(transformedData));
      dispatch(setOriginalReqBenchDataPc(transformedData));
    }
  }, [apiResponses, taskData, template]);

  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};

    // Prioritize the already parsed object if present
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }

    const { changedFields: _, ChangedFields, ...rest } = item;

    return {
      ...rest,
      changedFields,
    };
  });

  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;

    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.ProfitCenterID] = row.changedFields || {};
    });

    dispatch(setChangedFieldsMapPc(newChangedFieldsMap));
  }, [apiResponses]);

  const handleButtonClick = async (type, remarks) => {
      
      if (type === BUTTON_TYPE.VALIDATE) {
        validateAllRows(type);
        return;
      }
      setBlurLoading(true);
      let apiEndpoint =
        END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.PC]?.[
          requestHeaderData?.RequestType
        ]?.[type];
  
      const finalPayload = changePayloadForPC(
        requestHeaderData,
        requestHeaderSlice,
        taskData,
        fetchReqBenchData,
        fetchedProfitCenterData,
        changeChangeLogData,
        remarks
      )

  
      const hSuccess = (data) => {
        setBlurLoading(false);
  
        if (data?.statusCode === 200 || data?.statusCode === 201) {
          setDialogData({
            title: SUCCESS_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
            buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else if (data?.statusCode === 500 || data?.statusCode === 501) {
          setDialogData({
            title: FAILURE_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
            buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else {
          // fallback - maybe show a snackbar or console log
          setSnackbarOpen(true);
          setAlertMsg("Unexpected response received.");
        }
      };
      const hError = (error) => {
        setBlurLoading(false);
        setSnackbarOpen(true);
        setAlertMsg("Error occurred while validating the request");
        console.error("Error saving draft:", error);
      };
  
      doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
    };

  
  const validateAllRows = (row) => {
    
    setBlurLoading(true);
    const allData =
      Array.isArray(fetchReqBenchData) && fetchReqBenchData.length > 0
        ? fetchReqBenchData
        : Array.isArray(fetchedProfitCenterData) &&
          fetchedProfitCenterData.length > 0
        ? fetchedProfitCenterData
        : [];
    
    const errors = [];
   

    allData.forEach((row, index) => {
      const missingFields = mandatoryFieldName.filter(({ field }) => {
        const value = row?.[field];
        return (
          value === undefined ||
          value === null ||
          value.toString().trim() === ""
        );
      });

      if (missingFields.length > 0) {
        errors.push({
          rowIndex: index + 1,
          rowId: row?.id || row?.ProfitCenterID || "N/A",
          missingHeaders: missingFields.map((f) => f.headerName),
        });
      }
    });

    if (errors.length > 0) {
      // 👇 Build an array of "Line X - HeaderName"
      const dialogFieldList = errors.flatMap((err) =>
        err.missingHeaders.map((header) => `Line ${err.rowIndex} - ${header}`)
      );

      setMissingFields(dialogFieldList);
      setMissingFieldsDialogOpen(true);
      setBlurLoading(false); // stop spinner if dialog opens
      return;
    }

    const finalPayload=changePayloadForPC(
      requestHeaderData,
      requestHeaderSlice,
      taskData,
      fetchReqBenchData,
      fetchedProfitCenterData,
      changeChangeLogData,
    )
    const hSuccess = (data) => {
      setBlurLoading(false);
      if (
        data?.statusCode === API_CODE.STATUS_200 ||
        data?.statusCode === API_CODE.STATUS_201
      ) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (
        data?.statusCode === API_CODE.STATUS_500 ||
        data?.statusCode === STATUS_501
      ) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        // fallback - maybe show a snackbar or console log
        setSnackbarOpen(true);
        setAlertMsg("Unexpected response received.");
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    // 🔄 API call
    doAjax(
      `/${destination_ProfitCenter_Mass}/massAction/validateMassProfitCenter`,
      "POST",
      hSuccess,
      hError,
      finalPayload
    );
  };

  

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    let payload = {
      coAreaPCs: selectedProfitCenters.map((pc) => ({
        profitCenter: pc,
        controllingArea: dropdown1Value,
      })),
      requestId:
        requestHeaderData?.RequestId || initialPayload?.requestId || "",
      templateHeaders: requestHeaderData?.FieldName
        ? requestHeaderData.FieldName?.join("$^$")
        : "",
      templateName: requestHeaderData?.TemplateName
        ? requestHeaderData.TemplateName
        : "",
      dtName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v6",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_ProfitCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );

  const isEmpty = isChangeFieldEmpty(changedFieldsMap);
  const filteredProfitCenters = profitCenterOptions
    .filter((pc) => selectedCompanyCodes.includes(pc.companyCode))
    .map((pc) => ({ code: pc.code, desc: pc.desc }));

  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      <SuccessDialog
        open={successDialogOpen}
        onClose={() => setSuccessDialogOpen(false)}
        title={dialogData.title}
        message={dialogData.message}
        subText={dialogData.subText}
        buttonText={dialogData.buttonText}
        redirectTo={dialogData.redirectTo}
      />
      {showTableInDialog && (
        <ObjectLockDialog
          duplicateFieldsArr={duplicateFieldsData}
          moduleName={MODULE_MAP?.["PC"]}
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
        />
      )}
      <Dialog
        open={missingFieldsDialogOpen}
        onClose={() => setMissingFieldsDialogOpen(false)}
        aria-labelledby="missing-fields-dialog-title"
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle
          id="missing-fields-dialog-title"
          sx={{
            backgroundColor: "#fff3e0",
            color: "#e65100",
            display: "flex",
            alignItems: "center",
            gap: 1,
            fontWeight: "bold",
          }}
        >
          Missing Mandatory Fields
        </DialogTitle>

        <DialogContent sx={{ pt: 2 }}>
          <Typography variant="body1" gutterBottom>
            Please complete the following mandatory fields:
          </Typography>

          <List dense>
            {missingFields.length > 0 ? (
              missingFields.map((field, index) => {
                const match = field.match(/^(Line \d+)( - .*)$/);
                return (
                  <ListItem key={index} disablePadding>
                    <ListItemIcon sx={{ minWidth: 30 }}>
                      <WarningAmberIcon fontSize="small" color="warning" />
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        match ? (
                          <>
                            <strong>{match[1]}</strong>
                            {match[2]}
                          </>
                        ) : (
                          field
                        )
                      }
                    />
                  </ListItem>
                );
              })
            ) : (
              <ListItem>
                <ListItemText
                  primary={
                    <Typography variant="body2">
                      No missing fields found.
                    </Typography>
                  }
                />
              </ListItem>
            )}
          </List>
        </DialogContent>

        <DialogActions>
          <Button
            onClick={() => setMissingFieldsDialogOpen(false)}
            variant="contained"
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {!reqBench &&
            fetchedProfitCenterData.length === 0 &&
            Object.keys(taskData || {}).length === 0 && (
              <>
                <Dialog
                  open={open}
                  onClose={(event, reason) => {
                    if (
                      reason !== "backdropClick" &&
                      reason !== "escapeKeyDown"
                    ) {
                      handleClose();
                    }
                  }}
                  maxWidth="sm"
                  fullWidth
                >
                  <Box
                    sx={{
                      backgroundColor: "#e3f2fd",
                      padding: "1rem 1.5rem",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <FeedOutlinedIcon
                      color="primary"
                      sx={{ marginRight: "0.5rem" }}
                    />
                    <Typography variant="h6" component="div" color="primary">
                      {requestHeaderData?.TemplateName} {t("Search Filter")}(s)
                    </Typography>
                  </Box>

                  <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                    <Box sx={{ marginBottom: "1rem" }}>
                      <FilterChangeDropdown
                        param={{
                          key: "controllingArea",
                          label: t("Controlling Area"),
                        }}
                        dropDownData={{
                          controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                        }}
                        selectedValues={{
                          controllingArea: dropdown1Value
                            ? [{ code: dropdown1Value }]
                            : [],
                        }}
                        handleSelectionChange={(key, value) => {
                          setDropdown1Value(
                            value.length > 0 ? value[0].code || value[0] : ""
                          );
                        }}
                        formatOptionLabel={(option) => {
                          if (option.code && option.desc) {
                            return `${option.code} - ${option.desc}`;
                          }
                          return option.code || "";
                        }}
                        singleSelect={true}
                        errors={{}}
                      />
                    </Box>
                    <Box sx={{ marginBottom: "1rem" }}>
                      <FilterChangeDropdown
                        param={{ key: "companyCode", label: t("Company Code") }}
                        dropDownData={{
                          companyCode: dropdownDataCompany || [],
                        }}
                        selectedValues={{
                          companyCode: selectedCompanyCodes.map(
                            (code) =>
                              dropdownDataCompany.find(
                                (item) => item.code === code
                              ) || {
                                code,
                              }
                          ),
                        }}
                        handleSelectAll={(key) => {
                          if (
                            selectedCompanyCodes.length ===
                            dropdownDataCompany?.length
                          ) {
                            setSelectedCompanyCodes([]);
                          } else {
                            setSelectedCompanyCodes(
                              dropdownDataCompany?.map((item) => item.code) ||
                                []
                            );
                          }
                        }}
                        handleSelectionChange={(key, value) => {
                          const newCompanyCodes = value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          );
                          setSelectedCompanyCodes(newCompanyCodes);
                        }}
                        formatOptionLabel={(option) => {
                          if (option.code && option.desc) {
                            return `${option.code} - ${option.desc}`;
                          }
                          return option.code || "";
                        }}
                        isSelectAll={true}
                        errors={{}}
                      />
                    </Box>

                    {/* Profit Center Dropdown */}
                    <Box sx={{ marginBottom: "1rem" }}>
                      <FilterChangeDropdown
                        param={{
                          key: "profitCenter",
                          label: t("Profit Center"),
                        }}
                        dropDownData={{ profitCenter: filteredProfitCenters }}
                        selectedValues={{
                          profitCenter: selectedProfitCenters.map(
                            (code) =>
                              filteredProfitCenters.find(
                                (pc) => pc.code === code
                              ) || { code }
                          ),
                        }}
                        handleSelectAll={(key) => {
                          const filteredPCs = profitCenterOptions.filter((pc) =>
                            selectedCompanyCodes.includes(pc.companyCode)
                          );
                          if (
                            selectedProfitCenters.length ===
                            filteredProfitCenters.length
                          ) {
                            setSelectedProfitCenters([]);
                          } else {
                            setSelectedProfitCenters(
                              filteredProfitCenters.map((pc) => pc.code)
                            );
                          }
                        }}
                        handleSelectionChange={(key, value) => {
                          const pcCodes = value.map((item) =>
                            typeof item === "string" ? item : item.code
                          );
                          setSelectedProfitCenters(pcCodes);
                        }}
                        formatOptionLabel={(option) =>
                          typeof option === "string"
                            ? option
                            : `${option.code}${
                                option.desc ? ` - ${option.desc}` : ""
                              }`
                        }
                        isSelectAll={true}
                        errors={{}}
                      />
                    </Box>
                  </DialogContent>

                  <DialogActions
                    sx={{
                      padding: "0.5rem 1.5rem",
                      display: "flex",
                      alignItems: "center",
                    }}
                  >
                    <Box sx={{ display: "flex", gap: 1 }}>
                      <Button
                        onClick={handleClose}
                        color="error"
                        variant="outlined"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          textTransform: "none",
                          borderColor: "#cc3300",
                          fontWeight: 500,
                        }}
                      >
                        {t("Cancel")}
                      </Button>
                      {requestHeaderData?.RequestType !==
                        REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                        <Button
                          onClick={() => {
                            handleOk("OK");
                          }}
                          variant="contained"
                          disabled={
                            !dropdown1Value ||
                            selectedCompanyCodes.length === 0 ||
                            selectedProfitCenters.length === 0
                          }
                          sx={{
                            height: 36,
                            minWidth: "3.5rem",
                            backgroundColor: "#3B30C8",
                            textTransform: "none",
                            fontWeight: 500,
                            "&:hover": {
                              backgroundColor: "#2c278f",
                            },
                          }}
                        >
                          {t("OK")}
                        </Button>
                      )}
                      {requestHeaderData?.RequestType ===
                        REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                        <Button
                          // handleDownloadDialogOpen();
                          onClick={() => {
                            handleOk("Download");
                          }}
                          variant="contained"
                          sx={{
                            height: 36,
                            minWidth: "3.5rem",
                            backgroundColor: "#3B30C8",
                            textTransform: "none",
                            fontWeight: 500,
                            "&:hover": {
                              backgroundColor: "#2c278f",
                            },
                          }}
                        >
                          {t("Download")}
                        </Button>
                      )}
                    </Box>
                  </DialogActions>
                </Dialog>

                <DownloadDialog
                  onDownloadTypeChange={onDownloadTypeChange}
                  open={openDownloadDialog}
                  downloadType={downloadType}
                  handleDownloadTypeChange={handleDownloadTypeChange}
                  onClose={handleDownloadDialogClose}
                />
                <ReusableBackDrop
                  blurLoading={blurLoading}
                  loaderMessage={loaderMessage}
                />
              </>
            )}
          {showGrid && (
            <Box sx={{ mt: 4 }}>
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  padding: "16px 16px",
                  backgroundColor: "#f5f5f5",
                  borderRadius: "8px 8px 0 0",
                }}
              >
                <Typography variant="h5" sx={{ fontWeight: 600 }}>
                  {t("List of Profit Centers")}
                </Typography>
              </Box>

              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box>
                  <ReusableDataTable
                    rows={fetchedProfitCenterData}
                    columns={columns}
                    rowHeight={70}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      ![
                        "profitCenter",
                        "companyCode",
                        "controllingArea",
                      ].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>

              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <BottomNavGlobal
                handleSaveAsDraft={handleButtonClick}
                handleSubmitForReview={handleButtonClick}
                handleSubmitForApprove={handleButtonClick}
                handleSendBack={handleButtonClick}
                handleCorrection={handleButtonClick}
                handleRejectAndCancel={handleButtonClick}
                handleValidateAndSyndicate={handleButtonClick}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
                moduleName={module}
                showWfLevels = {showWfLevels}
                selectedLevel={selectedLevel}
                workFlowLevels={wfLevels}
                setSelectedLevel={setSelectedLevel}
              />
              </Box>
            </Box>
          )}
        </>
      )}
      <>
        {fetchReqBenchData.length === 1 &&
          fetchReqBenchData[0]?.profitCenter === null &&
          // reqBench === "true" &&
          Object.keys(taskData || {}).length === 0 && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason !== "backdropClick" &&
                    reason !== "escapeKeyDown"
                  ) {
                    handleClose();
                  }
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} {t("Search Filter")}(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "controllingArea",
                        label: "Controlling Area",
                      }}
                      dropDownData={{
                        controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                      }}
                      selectedValues={{
                        controllingArea: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: t("Company Code") }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany.find(
                              (item) => item.code === code
                            ) || {
                              code,
                            }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const newCompanyCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedCompanyCodes(newCompanyCodes);
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  {/* Profit Center Dropdown */}
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "profitCenter", label: t("Profit Center") }}
                      dropDownData={{ profitCenter: filteredProfitCenters }}
                      selectedValues={{
                        profitCenter: selectedProfitCenters.map(
                          (code) =>
                            filteredProfitCenters.find(
                              (pc) => pc.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        const filteredPCs = profitCenterOptions.filter((pc) =>
                          selectedCompanyCodes.includes(pc.companyCode)
                        );
                        if (
                          selectedProfitCenters.length === filteredPCs.length
                        ) {
                          setSelectedProfitCenters([]);
                        } else {
                          setSelectedProfitCenters(
                            filteredPCs.map((pc) => pc.code)
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const pcCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedProfitCenters(pcCodes);
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : `${option.code}${
                              option.desc ? ` - ${option.desc}` : ""
                            }`
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      {t("Cancel")}
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                            handleOk("OK");
                          }}
                        variant="contained"
                        disabled={
                          !dropdown1Value ||
                          selectedCompanyCodes.length === 0 ||
                          selectedProfitCenters.length === 0
                        }
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("OK")}
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        {t("Download")}
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
        {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              {t("Profit Center Lists")}
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  // isLoading={loading}
                  rows={fetchReqBenchData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    ![
                      "profitCenter",
                      "companyCode",
                      "controllingArea",
                    ].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              
              <BottomNavGlobal
                handleSaveAsDraft={handleButtonClick}
                handleSubmitForReview={handleButtonClick}
                handleSubmitForApprove={handleButtonClick}
                handleSendBack={handleButtonClick}
                handleCorrection={handleCorrection}
                handleRejectAndCancel={handleButtonClick}
                handleValidateAndSyndicate={handleButtonClick}
                validateAllRows={validateAllRows}
                
                filteredButtons={filteredButtons}
                moduleName={module}
                showWfLevels = {showWfLevels}
                selectedLevel={selectedLevel}
                workFlowLevels={wfLevels}
                setSelectedLevel={setSelectedLevel}
              />
            </Box>
          </Box>
        )}
        {taskData && Object.keys(taskData).length > 0 && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              {t("Profit Center Lists")}
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  // isLoading={loading}
                  rows={fetchReqBenchData}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    ![
                      "profitCenter",
                      "companyCode",
                      "controllingArea",
                    ].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleButtonClick}
                handleSubmitForReview={handleButtonClick}
                handleSubmitForApprove={handleButtonClick}
                handleSendBack={handleButtonClick}
                handleCorrection={handleCorrection}
                handleRejectAndCancel={handleButtonClick}
                handleValidateAndSyndicate={handleButtonClick}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
                moduleName={module}
                showWfLevels = {showWfLevels}
                selectedLevel={selectedLevel}
                workFlowLevels={wfLevels}
                setSelectedLevel={setSelectedLevel}
              />
            </Box>
          </Box>
        )}
      </>
    </div>
  );
};

export default RequestDetailsChangePC;
