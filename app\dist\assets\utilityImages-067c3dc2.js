const t="/assets/applicationIsDown-a5d16904.jpg",g="/assets/botImage-1ed20d68.png",i="/assets/csv-097ca35c.png",p="/assets/doc-350050dc.png",n="/assets/jpg-eb6cef34.png",a="/assets/nonotificationfound-8e4020eb.png",o="/assets/pageNotFound-debf572b.jpg",_="/assets/png-5e305448.png",c="/assets/ppt-53eb128f.png",l="/assets/runtimeError-4ef68ec5.png",r="/assets/txt-26043b09.png",m="/assets/userIcon-1d1f0a7c.png",y="/assets/userLogout-9d300568.jpg",u="/assets/xls-8fb4ec6b.png";let f={},I=[{key:"csv",fileName:"csv.png"},{key:"doc",fileName:"doc.png"},{key:"docx",fileName:"doc.png"},{key:"jpg",fileName:"jpg.png"},{key:"ppt",fileName:"ppt.png"},{key:"png",fileName:"png.png"},{key:"txt",fileName:"txt.png"},{key:"xls",fileName:"xls.png"},{key:"xlsx",fileName:"xls.png"},{key:"applicationIsDown",fileName:"applicationIsDown.jpg"},{key:"scp_AppheaderImg",fileName:"scp-logo_appheader.png"},{key:"viatris_AppheaderImg",fileName:"viatris_AppHeaderImg.png"},{key:"woc_AppheaderImg",fileName:"woc_AppHeaderImg.png"},{key:"internalServerError",fileName:"applicationIsDown.jpg"},{key:"runtimeError",fileName:"runtimeError.png"},{key:"pageNotFound",fileName:"pageNotFound.jpg"}];I.forEach(async e=>{const s=new URL(Object.assign({"/src/utilityImages/applicationIsDown.jpg":t,"/src/utilityImages/botImage.png":g,"/src/utilityImages/csv.png":i,"/src/utilityImages/doc.png":p,"/src/utilityImages/jpg.png":n,"/src/utilityImages/nonotificationfound.png":a,"/src/utilityImages/pageNotFound.jpg":o,"/src/utilityImages/png.png":_,"/src/utilityImages/ppt.png":c,"/src/utilityImages/runtimeError.png":l,"/src/utilityImages/txt.png":r,"/src/utilityImages/userIcon.png":m,"/src/utilityImages/userLogout.jpg":y,"/src/utilityImages/xls.png":u})[`/src/utilityImages/${e.fileName}`],self.location).href;f[e.key]=s});export{f as i};
