import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
  Box,
  TextField,
  Autocomplete,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  Checkbox,
  FormControlLabel,
  Grid,
  InputAdornment,
  Card,
  CardContent,
  Chip,
  Paper
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";

const genericMaterials = [
  { code: "5000083", desc: "Linen Mens Shirt" },
  { code: "GEN001", desc: "Beverage Base" },
  { code: "GEN002", desc: "Snack Mix" },
  { code: "GEN003", desc: "Cosmetic Foundation" },
  { code: "GEN004", desc: "Pharmaceutical Tablet" },
  { code: "GEN005", desc: "Electronic Component" }
];

// Sample variant data
const variantData = {
  "5000083": [
    { code: "5000083001", desc: "Linen Mens Shirt, Small-38, Deep Blue" },
    { code: "5000083002", desc: "Linen Mens Shirt, Medium-40, Deep Blue" },
    { code: "5000083003", desc: "Linen Mens Shirt, Large-42, Deep Blue" }
  ],
  "GEN001": [
    { code: "VAR001", desc: "Coca Cola - 330ml Can" },
    { code: "VAR002", desc: "Coca Cola - 500ml Bottle" },
    { code: "VAR003", desc: "Pepsi - 330ml Can" },
    { code: "VAR004", desc: "Sprite - 330ml Can" },
    { code: "VAR005", desc: "Fanta - 330ml Can" }
  ],
  "GEN002": [
    { code: "VAR006", desc: "Trail Mix - Nuts & Raisins" },
    { code: "VAR007", desc: "Trail Mix - Chocolate Mix" },
    { code: "VAR008", desc: "Popcorn - Salted" },
    { code: "VAR009", desc: "Crackers - Cheese" },
    { code: "VAR010", desc: "Chips - BBQ Flavor" }
  ],
  "GEN003": [
    { code: "VAR011", desc: "Foundation - Light Beige" },
    { code: "VAR012", desc: "Foundation - Medium Tan" },
    { code: "VAR013", desc: "Foundation - Dark Brown" },
    { code: "VAR014", desc: "Foundation - Fair Ivory" },
    { code: "VAR015", desc: "Foundation - Olive Tone" }
  ]
};

const AddComponentsToArticleDialog = ({
  open,
  onClose,
  articleNumber,
  articleDetails,
  title = "Add Components",
  handleSave,
  ...props
}) => {
  const [selectedGenericMaterialCode, setSelectedGenericMaterialCode] = useState(null);
  const [selectedGenericMaterialDesc, setSelectedGenericMaterialDesc] = useState(null);
  const [variants, setVariants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedVariants, setSelectedVariants] = useState([]);

  // Calculate total quantity
  const totalQuantity = selectedVariants.reduce((sum, variant) => sum + variant.quantity, 0);

  // Effect to populate form with existing articleDetails data
  useEffect(() => {
    if (articleDetails && open) {
      // Set generic material data
      if (articleDetails.GenericMaterial && articleDetails.GenericMaterialDescription) {
        const materialCode = { code: articleDetails.GenericMaterial, desc: articleDetails.GenericMaterialDescription };
        setSelectedGenericMaterialCode(materialCode);
        setSelectedGenericMaterialDesc(materialCode);
        
        // Load variants for this material
        const materialVariants = variantData[articleDetails.GenericMaterial] || [];
        setVariants(materialVariants);
        
        // Set selected variants with quantities and UOM from existing data
        if (articleDetails.Tocomponentsdata && articleDetails.Tocomponentsdata.length > 0) {
          const existingVariants = articleDetails.Tocomponentsdata.map(component => {
            const variantInfo = materialVariants.find(v => v.code === component.VariantCode);
            return {
              code: component.VariantCode,
              desc: component.VariantDescription || (variantInfo ? variantInfo.desc : ''),
              quantity: component.Quantity || 1,
              uom: component.Uom || 'EA - Each'
            };
          });
          setSelectedVariants(existingVariants);
        }
      }
    }
  }, [articleDetails, open]);

  const handleGenericMaterialCodeChange = async (event, newValue) => {
    setSelectedGenericMaterialCode(newValue);

    if (newValue) {
      // Auto-fill description based on code
      const material = genericMaterials.find(m => m.code === newValue.code);
      if (material) {
        setSelectedGenericMaterialDesc(material);
      }

      // Load variants with API simulation
      setLoading(true);
      setVariants([]);

      setTimeout(() => {
        const materialVariants = variantData[newValue.code] || [];
        setVariants(materialVariants);
        setLoading(false);
      }, 1500);
    } else {
      setSelectedGenericMaterialDesc(null);
      setVariants([]);
    }
  };

  const handleGenericMaterialDescChange = async (event, newValue) => {
    setSelectedGenericMaterialDesc(newValue);

    if (newValue) {
      // Auto-fill code based on description
      const material = genericMaterials.find(m => m.desc === newValue.desc);
      if (material) {
        setSelectedGenericMaterialCode(material);
      }

      // Load variants with API simulation
      setLoading(true);
      setVariants([]);

      setTimeout(() => {
        const materialVariants = variantData[newValue.code] || [];
        setVariants(materialVariants);
        setLoading(false);
      }, 1500);
    } else {
      setSelectedGenericMaterialCode(null);
      setVariants([]);
    }
  };

  const handleVariantSelect = (variant, checked) => {
    if (checked) {
      setSelectedVariants(prev => [...prev, { ...variant, quantity: 1, uom: 'EA - Each' }]);
    } else {
      setSelectedVariants(prev => prev.filter(v => v.code !== variant.code));
    }
  };

  const handleQuantityChange = (variantCode, newQuantity) => {
    if (newQuantity < 1) return;
    setSelectedVariants(prev =>
      prev.map(v =>
        v.code === variantCode ? { ...v, quantity: newQuantity } : v
      )
    );
  };

  const incrementQuantity = (variantCode) => {
    setSelectedVariants(prev =>
      prev.map(v =>
        v.code === variantCode ? { ...v, quantity: v.quantity + 1 } : v
      )
    );
  };

  const decrementQuantity = (variantCode) => {
    setSelectedVariants(prev =>
      prev.map(v =>
        v.code === variantCode ? { ...v, quantity: Math.max(1, v.quantity - 1) } : v
      )
    );
  };

  const handleSaveComponents = () => {
    const componentData = {
      ArticleComponentsId: articleDetails?.ArticleComponentsId || "",
      GenericMaterial: selectedGenericMaterialCode?.code || null,
      GenericMaterialDescription: selectedGenericMaterialDesc?.desc || null,
      Tocomponentsdata: selectedVariants.map(variant => {
        // Find existing component ID if it exists
        const existingComponent = articleDetails?.Tocomponentsdata?.find(
          comp => comp.VariantCode === variant.code
        );
        return {
          ComponentsId: existingComponent?.ComponentsId || "",
          VariantCode: variant.code,
          VariantDescription: variant.desc,
          Quantity: variant.quantity,
          Uom: variant.uom || "EA - Each"
        };
      })
    };

    // Use handleSave function passed from parent (same pattern as RequestDetails)
    if (handleSave) {
      handleSave({
        id: articleNumber,
        field: "articleComponents",
        value: componentData
      });
    }
    onClose();
  };

  const handleDialogClose = () => {
    setSelectedGenericMaterialCode(null);
    setSelectedGenericMaterialDesc(null);
    setVariants([]);
    setSelectedVariants([]);
    setLoading(false);
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleDialogClose}
      maxWidth={false}
      PaperProps={{
        sx: {
          width: '900px',
          maxWidth: '90vw'
        }
      }}
      {...props}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>{title}</Typography>
          </Box>
          <Box display="flex" alignItems="center" gap={2}>
            <IconButton onClick={handleDialogClose} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2, pb: 1 }}>
        <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: '#f8f9fa', border: '1px solid #e0e0e0' }}>
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="caption" display="block" sx={{ fontWeight: 600, mb: 0.5, color: '#666' }}>
                GENERIC MATERIAL CODE
              </Typography>
              <Autocomplete
                options={genericMaterials}
                getOptionLabel={(option) => option.code}
                value={selectedGenericMaterialCode}
                onChange={handleGenericMaterialCodeChange}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    placeholder="Select code..."
                    sx={{
                      '& .MuiInputBase-root': {
                        bgcolor: 'white',
                        height: '36px'
                      }
                    }}
                  />
                )}
                isOptionEqualToValue={(option, value) => option.code === value.code}
              />
            </Grid>

            <Grid item xs={6}>
              <Typography variant="caption" display="block" sx={{ fontWeight: 600, mb: 0.5, color: '#666' }}>
                DESCRIPTION
              </Typography>
              <Autocomplete
                options={genericMaterials}
                getOptionLabel={(option) => option.desc}
                value={selectedGenericMaterialDesc}
                onChange={handleGenericMaterialDescChange}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    variant="outlined"
                    size="small"
                    placeholder="Select description..."
                    sx={{
                      '& .MuiInputBase-root': {
                        bgcolor: 'white',
                        height: '36px'
                      }
                    }}
                  />
                )}
                isOptionEqualToValue={(option, value) => option.desc === value.desc}
              />
            </Grid>
          </Grid>
        </Paper>

        {/* Loading indicator */}
        {loading && (
          <Box display="flex" justifyContent="center" alignItems="center" py={2}>
            <CircularProgress size={24} />
            <Typography variant="body2" ml={1.5} color="text.secondary">Loading variants...</Typography>
          </Box>
        )}

        {/* Variants List */}
        {variants.length > 0 && !loading && (
          <Box>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1.5}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                Available Variants
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                {selectedVariants.length > 0 && (
                  <Chip
                    label={`Total Quantity: ${totalQuantity}`}
                    color="primary"
                    size="small"
                    variant="filled"
                  />
                )}
              </Box>
            </Box>

            <Box sx={{ maxHeight: '400px', overflowY: 'auto' }}>
              {variants.map((variant, index) => {
                const isSelected = selectedVariants.find(v => v.code === variant.code);
                const selectedVariant = selectedVariants.find(v => v.code === variant.code);

                return (
                  <Card
                    key={variant.code}
                    elevation={0}
                    sx={{
                      border: isSelected ? '2px solid #1976d2' : '1px solid #e0e0e0',
                      mb: 1,
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        boxShadow: 1
                      }
                    }}
                  >
                    <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                      <Grid container spacing={1.5} alignItems="center">
                        {/* Checkbox and Variant Info */}
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            sx={{ m: 0 }}
                            control={
                              <Checkbox
                                checked={!!isSelected}
                                onChange={(e) => handleVariantSelect(variant, e.target.checked)}
                                color="primary"
                                size="small"
                              />
                            }
                            label={
                              <Box ml={1}>
                                <Typography variant="body2" sx={{ fontWeight: 500, lineHeight: 1.2 }}>
                                  {variant.code}
                                </Typography>
                                <Typography variant="body2" color="text.secondary" sx={{ lineHeight: 1.2 }}>
                                  {variant.desc}
                                </Typography>
                              </Box>
                            }
                          />
                        </Grid>

                        {/* Quantity and UOM Controls */}
                        {isSelected && (
                          <>
                            <Grid item xs={6} sm={3}>
                              <Typography variant="caption" display="block" sx={{ fontWeight: 600, mb: 0.5, color: '#666' }}>
                                Quantity
                              </Typography>
                              <TextField
                                type="number"
                                size="small"
                                value={selectedVariant?.quantity || 1}
                                onChange={(e) => handleQuantityChange(variant.code, parseInt(e.target.value) || 1)}
                                InputProps={{
                                  startAdornment: (
                                    <InputAdornment position="start">
                                      <IconButton
                                        size="small"
                                        onClick={() => decrementQuantity(variant.code)}
                                        disabled={selectedVariant?.quantity <= 1}
                                        sx={{ p: 0.3 }}
                                      >
                                        <RemoveIcon fontSize="small" />
                                      </IconButton>
                                    </InputAdornment>
                                  ),
                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <IconButton
                                        size="small"
                                        onClick={() => incrementQuantity(variant.code)}
                                        sx={{ p: 0.3 }}
                                      >
                                        <AddIcon fontSize="small" />
                                      </IconButton>
                                    </InputAdornment>
                                  )
                                }}
                                inputProps={{
                                  min: 1,
                                  style: { textAlign: 'center', padding: '6px 8px' }
                                }}
                                sx={{
                                  '& .MuiInputBase-root': {
                                    height: '32px'
                                  }
                                }}
                              />
                            </Grid>

                            <Grid item xs={6} sm={3}>
                              <Typography variant="caption" display="block" sx={{ fontWeight: 600, mb: 0.5, color: '#666' }}>
                                UOM
                              </Typography>
                              <TextField
                                value={selectedVariant?.uom?.split(' - ')[0] || 'EA'}
                                size="small"
                                disabled
                                fullWidth
                                inputProps={{
                                  style: { textAlign: 'center', padding: '6px 8px' }
                                }}
                                sx={{
                                  '& .MuiInputBase-root': {
                                    height: '32px'
                                  }
                                }}
                              />
                            </Grid>
                          </>
                        )}
                      </Grid>
                    </CardContent>
                  </Card>
                );
              })}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2, gap: 1.5 }}>
        <Button
          onClick={handleDialogClose}
          variant="outlined"
          sx={{ minWidth: '100px' }}
        >
          Close
        </Button>
        <Button
          onClick={handleSaveComponents}
          variant="contained"
          disabled={selectedVariants.length === 0 || !selectedGenericMaterialCode || !selectedGenericMaterialDesc}
          sx={{
            minWidth: '140px',
            fontWeight: 600
          }}
        >
          Save ({selectedVariants.length})
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddComponentsToArticleDialog;