import React, { useEffect, useState } from 'react';
import {
    <PERSON>ton,
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Grid,
    IconButton,
    Typography,
    Box,
    Paper,
    Fade,
    Chip,
    Autocomplete,
    FormControl,
    Alert,
    CircularProgress
} from '@mui/material';
import { Close, Clear } from '@mui/icons-material';
import useGenericDtCall from '@hooks/useGenericDtCall';
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, DECISION_TABLE_NAME, REQUEST_TYPE } from '@constant/enum';
import { useSnackbar } from '@hooks/useSnackbar';
import { END_POINTS } from '@constant/apiEndPoints';
import { destination_Admin } from '../../../destinationVariables';

const CreateInApplicationFilter = ({ open, onClose, onApplyFilters, setFiltersObj }) => {
    const [filters, setFilters] = useState({
        region: '',
        workflowName: '',
        module: '',
        scenario: '',
        template: '',
        validityDateRange: null,
        bifurcationGroup: ''
    });

    const [errorMessage, setErrorMessage] = useState('');
    const [isCheckingDuplicate, setIsCheckingDuplicate] = useState(false);
    const { showSnackbar } = useSnackbar();
    const { getDtCall, dtData } = useGenericDtCall();

    const [dropdownData, setDropDownData] = useState({
        region: [
            { code: 'US', desc: 'USA' },
            { code: 'EUR', desc: 'Europe' },
        ],
        module: [
            { code: 'Material', desc: 'Material' },
            { code: 'Article', desc: 'Article' },
            { code: 'Profit Center', desc: 'Profit Center' },
            { code: 'Cost Center', desc: 'Cost Center' },
            { code: 'Bank Key', desc: 'Bank Key' },
            { code: 'General Ledger', desc: 'General Ledger' },
            { code: 'Cost Center Group', desc: 'Cost Center Group' },
            { code: 'Profit Center Group', desc: 'Profit Center Group' },
            { code: 'Cost Element Group', desc: 'Cost Element Group' },
            { code: 'Bill Of Material', desc: 'Bill Of Material' },
            { code: 'Internal Order', desc: 'Internal Order' }
        ],
        scenario: [
            { code: 'Create', desc: 'Create' },
            { code: 'Change', desc: 'Change' },
            { code: 'Create with Upload', desc: 'Create' },
            { code: 'Change with Upload', desc: 'Change' },
        ],
        template: [
            { code: 'Logistic Data', desc: 'Logistic Data' },
            { code: 'MRP Data', desc: 'MRP Data' },
            { code: 'Item Cat Group', desc: 'Item Cat Group' },
            { code: 'Set to DNU', desc: 'Set to DNU' },
            { code: 'Warehouse View 2', desc: 'Custom Template' },
            { code: 'Change Status', desc: 'Advanced Template' },
            { code: 'Change Descriptions', desc: 'Advanced Template' }
        ],
        bifurcationGroup: []
    });

    // Check if all mandatory fields are filled
    const areAllFieldsFilled = () => {
        let requiredFields =
            filters?.scenario === REQUEST_TYPE.CREATE ?
                ['region', 'workflowName', 'module', 'scenario',] :
                ['region', 'workflowName', 'module', 'scenario', 'bifurcationGroup', 'template'];
        return requiredFields.every(field => {
            if (field === 'workflowName') {
                return filters[field].trim() !== '';
            }
            return filters[field] !== '';
        });
    };

    const fetchBifGrps = () => {
        let payload = {
            decisionTableId: null,
            decisionTableName: DECISION_TABLE_NAME.DICISIONTABLENAME,
            version: "v3",
            conditions: [
                {
                    "MDG_CONDITIONS.MDG_MAT_REGION": filters?.region || "US",
                    "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": filters?.scenario || REQUEST_TYPE.CREATE,
                },
            ],
        };
        getDtCall(payload);
    };

    const checkForDuplicates = async (filtersData) => {
        return new Promise((resolve, reject) => {
            setIsCheckingDuplicate(true);
            setErrorMessage('');

            const payload = {
                workflowName: filtersData.workflowName,
                region: filtersData.region,
                module: filtersData.module,
                scenario: filtersData.scenario,
                template: filtersData?.scenario === REQUEST_TYPE.CREATE ? "NA" : filtersData.template,
                bifurcationGroup: filtersData.bifurcationGroup || "GROUP-1"
            };

            const hSuccess = (data) => {
                setIsCheckingDuplicate(false);
                resolve(data);
            };

            const hError = (error) => {
                setIsCheckingDuplicate(false);
                const errorMsg = error?.message || WORKFLOW_DATA_CONSTANTS.CHECKDUP_ERR_MSG;
                setErrorMessage(errorMsg);
                reject(error);
            };

            const url = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.DUPLICACY_CHECK}`;
            doAjax(url, "post", hSuccess, hError, payload);
        });
    };

    useEffect(() => {
        if (filters?.scenario && filters?.region) {
            fetchBifGrps()
        }
    }, [filters?.scenario, filters?.region]);

    useEffect(() => {
        if (dtData) {
            let responseData = dtData?.result[0]?.MDG_MAT_MATERIAL_BIFURCATION_ACTION_TYPE || [];

            const uniqueGroups = [
                ...new Set(responseData.map((item) => item.MDG_MATERIAL_GROUPS))
            ].map((group) => ({
                code: group,
                desc: group
            }));

            setDropDownData({
                ...dropdownData,
                bifurcationGroup: uniqueGroups
            })
        }
    }, [dtData]);

    const handleFilterChange = (field, value) => {
        // Clear error message when user starts typing/selecting
        if (errorMessage) {
            setErrorMessage('');
        }

        if (field === 'workflowName') {
            setFilters(prev => ({
                ...prev,
                [field]: value
            }));
        } else {
            setFilters(prev => ({
                ...prev,
                [field]: value ? value.code : ''
            }));
        }
    };

    const handleApply = async () => {
        try {
            const processedFilters = {
                ...filters,
                validFrom: filters.validityDateRange ? filters.validityDateRange[0] : '',
                validTo: filters.validityDateRange ? filters.validityDateRange[1] : ''
            };
            delete processedFilters.validityDateRange;
            const response = await checkForDuplicates(processedFilters);
            if (response?.statusCode === API_CODE?.STATUS_200) {
                showSnackbar(response?.data, "success");
                onApplyFilters(processedFilters);
                setFiltersObj(processedFilters);
                onClose();
            } else {
                showSnackbar(response?.data, "error");
            }

        } catch (error) {
            
        }
    };

    const handleClear = () => {
        setFilters({
            region: '',
            workflowName: '',
            module: '',
            scenario: '',
            template: '',
            validityDateRange: null,
            bifurcationGroup: ''
        });
        setErrorMessage('');
    };

    const handleClose = () => {
        setErrorMessage('');
        onClose();
    };

    const getDisplayValue = (fieldName, code) => {
        if (!code) return null;
        const option = dropdownData[fieldName]?.find(item => item.code === code);
        return option || null;
    };

    const renderAutocomplete = (field, label, options, disabled = false) => (
        <Autocomplete
            value={getDisplayValue(field, filters[field])}
            onChange={(event, newValue) => handleFilterChange(field, newValue)}
            options={options}
            disabled={disabled}
            getOptionLabel={(option) => option ? option.desc : ''}
            isOptionEqualToValue={(option, value) => option?.code === value?.code}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label={
                        <span>
                            {label}
                            <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
                        </span>
                    }
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                />
            )}
            renderOption={(props, option) => (
                <Typography
                    {...props}
                    component="li"
                    style={{
                        fontSize: 12,
                        padding: '8px 16px',
                        width: '100%',
                        cursor: 'pointer',
                        display: 'flex',
                        alignItems: 'start',
                    }}
                >
                    <span style={{
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                    }}
                        title={`${option?.code}${option?.desc ? ` - ${option?.desc}` : ""}`}
                    >
                        <strong>{option?.code}</strong>
                    </span>
                </Typography>
            )}
            fullWidth
        />
    );

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="md"
            fullWidth
            TransitionComponent={Fade}
            TransitionProps={{ timeout: 300 }}
            PaperProps={{
                sx: {
                    borderRadius: 3,
                    background: 'linear-gradient(145deg, #ffffff 0%, #f8f9ff 100%)',
                    boxShadow: '0 20px 60px rgba(102, 126, 234, 0.15)',
                }
            }}
        >
            <DialogTitle sx={{ pb: 1 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box display="flex" alignItems="center" gap={1}>
                        <Typography variant="h5" fontWeight={600}>
                            Create Request
                        </Typography>
                    </Box>
                    <IconButton onClick={handleClose} size="small">
                        <Close />
                    </IconButton>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Configure your parameters to begin with the workflow creation
                </Typography>
            </DialogTitle>

            <DialogContent sx={{ pt: 2 }}>
                <Grid container spacing={3} sx={{ pt: 2 }}>

                    <Grid item xs={12} sm={6}>
                        <TextField
                            fullWidth
                            label={
                                <span>
                                    Workflow Name
                                    <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
                                </span>
                            }
                            value={filters.workflowName}
                            onChange={(e) => {
                                // Allow only letters, digits, period, underscore, hyphen, and spaces
                                const allowedPattern = /^[a-zA-Z0-9._\- ]*$/;
                                const inputValue = e.target.value;
                                
                                if (allowedPattern.test(inputValue)) {
                                    handleFilterChange('workflowName', inputValue);
                                }
                            }}
                            onKeyPress={(e) => {
                                // Additional validation on key press
                                const allowedChars = /[a-zA-Z0-9._\- ]/;
                                if (!allowedChars.test(e.key)) {
                                    e.preventDefault();
                                }
                            }}
                            inputProps={{
                                pattern: "[a-zA-Z0-9._\\- ]*",
                                title: "Only letters, numbers, periods, underscores, hyphens, and spaces are allowed"
                            }}
                            sx={{ 
                                '& .MuiOutlinedInput-root': { borderRadius: 2 }
                            }}
                        />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                        {renderAutocomplete('region', 'Region', dropdownData.region)}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                        {renderAutocomplete('module', 'Module', dropdownData.module)}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                        {renderAutocomplete('scenario', 'Scenario', dropdownData.scenario)}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                        {renderAutocomplete('template', 'Template', dropdownData.template, filters?.scenario === REQUEST_TYPE.CREATE ? true : false)}
                    </Grid>

                    <Grid item xs={12} sm={6}>
                        {renderAutocomplete('bifurcationGroup', 'Bifurcation Group', dropdownData.bifurcationGroup)}
                    </Grid>
                </Grid>

                {/* Error Message Display */}
                {errorMessage && (
                    <Box sx={{ mt: 3 }}>
                        <Alert severity="error" onClose={() => setErrorMessage('')}>
                            {errorMessage}
                        </Alert>
                    </Box>
                )}
            </DialogContent>

            <DialogActions sx={{ px: 3, pb: 3, pt: 2, gap: 1 }}>
                <Button
                    onClick={handleClear}
                    startIcon={<Clear />}
                    color="inherit"
                    variant="outlined"
                    sx={{
                        textTransform: 'none',
                        px: 3
                    }}
                >
                    Clear All
                </Button>
                <Box flex={1} />
                <Button
                    onClick={handleClose}
                    color="warning"
                    variant="outlined"
                    sx={{
                        textTransform: 'none',
                        px: 4
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleApply}
                    variant="contained"
                    disabled={!areAllFieldsFilled() || isCheckingDuplicate}
                    startIcon={isCheckingDuplicate ? <CircularProgress size={16} /> : null}
                    sx={{
                        textTransform: 'none',
                        px: 4,
                    }}
                >
                    {isCheckingDuplicate ? 'Checking...' : 'Proceed'}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default CreateInApplicationFilter;