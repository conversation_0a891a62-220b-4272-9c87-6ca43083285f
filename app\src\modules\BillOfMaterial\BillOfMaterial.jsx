import React, { useState, useEffect } from "react";
import {
  BottomNavigation,
  Button,
  Grid,
  Paper,
  Typography,
  Box,
  Tooltip,
  IconButton,
} from "@mui/material";
import { Stack } from "@mui/system";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import InfoIcon from '@mui/icons-material/Info';
import ReusableTable from '@components/Common/ReusableTable';
import BillOfMaterialFilters from '@BillOfMaterial/BillOfMaterialFilters';
import {
  outermostContainer,
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import { 
  clearPayload, 
  clearRequiredFields, 
  setChangeFieldRows, 
  setChangeFieldRowsDisplay,
  setDisplayPayload
} from '@app/payloadSlice';
import { destination_BOM } from "../../destinationVariables";
import { commonFilterClear } from "@app/commonFilterSlice";
import { clearPaginationData } from "@app/paginationSlice";
import { setTaskData } from "@app/userManagementSlice";
import { v4 as uuidv4 } from "uuid";
import { commonSearchBarClear } from "../../app/commonSearchBarSlice";
import useLang from "@hooks/useLang";
import useLogger from "@hooks/useLogger";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { 
  API_CODE, 
  DECISION_TABLE_NAME, 
  MODULE, 
  MODULE_MAP, 
  PAGESIZE, 
  PROCESS_TASK_NAME,
  VISIBILITY_TYPE 
} from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { showToast } from "../../functions";
import useGenericDtCall from "@hooks/useGenericDtCall";
import moment from "moment";
import { END_POINTS } from "@constant/apiEndPoints";
import HistoryIcon from '@mui/icons-material/History';

const BillOfMaterial = () => {
  const { customError } = useLogger();
  const { getDtCall: getMasterDataColumn, dtData: masterDataDtResponse } = useGenericDtCall();
  const { getDtCall: getSearchParams, dtData: dtSearchParamsResponse } = useGenericDtCall();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  // State management
  const [displayFlag, setDisplayFlag] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [IsFilterDropDownLoading, setIsFilterDropDownLoading] = useState(false);
  const [clearClicked, setClearClicked] = useState(false);
  const [value, setValue] = useState(null);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [pageSize, setPageSize] = useState(PAGESIZE?.TOP_SKIP);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const [searchParameters, setSearchParameters] = useState([]);
  const [statusOfSelectAllFirstData, setStatusOfSelectAllFirstData] = useState(false);
  const [items, setItem] = useState();
  const [roCount, setroCount] = useState(0);
  const [Count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const BomSearchForm = useSelector(
      (state) => state.commonFilter[MODULE_MAP?.BOM]
    );

  let dynamicDataApis = {};

  // API and data fetching functions
  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    let payload = {};
    setIsFilterDropDownLoading(true);
    
    const hSuccess = (data) => {
      setIsFilterDropDownLoading(false);
      const newOptions = data.body;
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
    };
    
    const hError = (error) => {
      setIsFilterDropDownLoading(false);
    };
    
    if (selectedItem === "Profit Center") { 
      doAjax(apiEndpoint, "post", hSuccess, hError, payload); 
    } else { 
      doAjax(apiEndpoint, "get", hSuccess, hError); 
    }
  };

  const fetchMasterDataColumns = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": PROCESS_TASK_NAME?.BOM,
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
        },
      ],
    };
    getMasterDataColumn(payload);
  };

  const fetchSearchParameterFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": PROCESS_TASK_NAME?.BOM,
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
        },
      ],
    };
    getSearchParams(payload);
  };

  const createMultiValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
      const displayCount = values.length - 1;

      if (values.length === 0) return "-";

      const formatText = (text) => {
        const [code, ...rest] = text.split('-');
        return (
          <>
            <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
          </>
        );
      };

      return (
        <Box sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          minWidth: 0
        }}>
          <Tooltip title={values[0]} placement="top" arrow>
            <Typography
              variant="body2"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                flex: 1,
                minWidth: 0,
              }}
            >
              {formatText(values[0])}
            </Typography>
          </Tooltip>
          {displayCount > 0 && (
            <Box sx={{
              display: "flex",
              alignItems: "center",
              ml: 1,
              flexShrink: 0
            }}>
              <Tooltip
                arrow
                placement="right"
                title={
                  <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Additional {displayName}s ({displayCount})
                    </Typography>
                    {values.slice(1).map((value, idx) => (
                      <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                        {formatText(value)}
                      </Typography>
                    ))}
                  </Box>
                }
              >
                <Box sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer"
                }}>
                  <InfoIcon
                    sx={{
                      fontSize: "1rem",
                      color: "primary.main",
                      "&:hover": { color: "primary.dark" }
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      ml: 0.5,
                      color: "primary.main",
                      fontSize: "11px"
                    }}
                  >
                    +{displayCount}
                  </Typography>
                </Box>
              </Tooltip>
            </Box>
          )}
        </Box>
      );
    }
  });

  const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const [firstPart, ...rest] = params.value?.split(" - ") || [];
      return (
        <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
          <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
        </span>
      );
    },
  });

  const displayCell = () => (
    {
      field: "dataValidation",
      headerName: t("Audit History"),
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const handleChangelogClick = (event) => {
          event.stopPropagation();
          navigate(APP_END_POINTS?.AUDIT_LOG, {
          state: {
              materialNumber: params.row.Material,
              module: MODULE_MAP?.BOM
            }
          });
        };
  
        return (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <Tooltip title="View Audit Log" placement="top">
              <IconButton
                onClick={handleChangelogClick}
                size="small"
                sx={{
                  color: 'primary.main',
                  marginLeft:'20px',
                  '&:hover': {
                    color: 'primary.dark',
                    backgroundColor: 'rgba(25, 118, 210, 0.04)',
                    transform: 'scale(1.05)',
                    marginLeft:'20px'
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <HistoryIcon sx={{ fontSize: '1.5rem' }} />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  );

  const createMasterDataColums = (data) => {
    const columns = [];
    let sortedData = data?.sort(
      (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
    ) || [];
    
    if (sortedData) {
      sortedData?.forEach((item) => {
        if (item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY) {
          if (item?.MDG_MAT_UI_FIELD_NAME) {
            const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
            const headerName = item.MDG_MAT_UI_FIELD_NAME;
            
            if(fieldName==="DataValidation"){
              columns.push(displayCell());
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
              columns.push(createMultiValueCell(fieldName, headerName));
            } else if (item.MDG_MAT_FIELD_TYPE === "Single") {
              columns.push(createSingleValueCell(fieldName, headerName));
            }
          }
        }
      });
    }
    return columns;
  };

  // Event handlers
  const handleSearchAction = (value) => {
    if (!value) {
      setroCount(Count);
      setPage(0);
      setTableData([...rmDataRows]);
      return;
    }
    
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
          row?.[keys?.[k]]
            .toString()
            .toLowerCase()
            ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setroCount(selected?.length);
  };

  const getFilter = () => {
    setPage(0)
    setTableLoading(true);
    setStatusOfSelectAllFirstData(false);
    let payload = {
        material: BomSearchForm?.Material ?? "",
        plant: BomSearchForm?.Plant ?? "",
        bomUsage: BomSearchForm?.BOMUsage ?? "",
        document: BomSearchForm?.Document ?? "",
        documentType: BomSearchForm?.DocType ?? "",
        altBOM:   BomSearchForm?.AlternativeBOM ?? "",
        component: BomSearchForm?.Component ?? "",
        fromDate:
          moment(BomSearchForm?.Date[0]).format("YYYYMMDD") ?? "",
        toDate:
          moment(BomSearchForm?.Date[1]).format("YYYYMMDD") ?? "",
        top: pageSize,
        skip: 0,
      };
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        var rows = [];
        for (let index = 0; index < data?.body?.length; index++) {
          var tempObj = data?.body[index];
          var tempRow = {
            id: uuidv4(),
            Plant: tempObj["Plant"],
            BOMUsage: tempObj["BOMUsage"],
            AlternativeBOM: tempObj["AltBOM"],
            Material: tempObj["Material"],
            Document: tempObj["Document"]!== "" ? tempObj["Document"] : "-",
            DocType: tempObj["DocumentType"]!== "" ? tempObj["DocumentType"] : "-",
            Component: tempObj["Component"]!== "" ? tempObj["Component"] : "-",

          };
          rows.push(tempRow);
        }
        setRmDataRows(rows.reverse());
        setTableLoading(false);
        setroCount(data.count);
        setCount(data.count);
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        showToast(data?.message, "error");
        setTableLoading(false);
      }
    };
    const hError = (error) => { 
      customError(error);
      setTableLoading(false);
    };
     doAjax(
        `/${destination_BOM}${END_POINTS?.MASS_ACTION?.BOM_MASTER_DATA}`,
        "post",
        hSuccess,
        hError,
        payload
      );
  };

  const getFilterBasedOnPagination = () => {
    setTableLoading(true);
    let payload = {
      material: BomSearchForm?.Material ?? "",
      plant: BomSearchForm?.Plant ?? "",
      bomUsage: BomSearchForm?.BOMUsage ?? "",
      document: BomSearchForm?.Document ?? "",
      documentType: BomSearchForm?.DocType ?? "",
      altBOM:   BomSearchForm?.AlternativeBOM ?? "",
      component: BomSearchForm?.Component ?? "",
      fromDate:
        moment(BomSearchForm?.Date[0]).format("YYYYMMDD") ?? "",
      toDate:
        moment(BomSearchForm?.Date[1]).format("YYYYMMDD") ?? "",
      top: pageSize,
      skip: pageSize * (page) ?? 0,
    };
    const hSuccess = (data) => {
      var rows = [];
      for (let index = 0; index < data?.body?.length; index++) {
          var tempObj = data?.body[index];
          var tempRow = {
            id: uuidv4(),
            Plant: tempObj["Plant"],
            BOMUsage: tempObj["BOMUsage"],
            AlternativeBOM: tempObj["AltBOM"],
            Material: tempObj["Material"],
            Document: tempObj["Document"]!== "" ? tempObj["Document"] : "-",
            DocType: tempObj["DocumentType"]!== "" ? tempObj["DocumentType"] : "-",
            Component: tempObj["Component"]!== "" ? tempObj["Component"] : "-",

          };
          rows.push(tempRow);
      }
      setRmDataRows((prevRows) => [...prevRows, ...rows]);
      setTableLoading(false);
    }
    const hError = (error) => {
      customError(error);
      setTableLoading(false);
    };
     doAjax(
        `/${destination_BOM}${END_POINTS?.MASS_ACTION?.BOM_MASTER_DATA}`,
        "post",
        hSuccess,
        hError,
        payload
      );
  };

  const onRowsSelectionHandler = (ids) => {
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    // Handle selection logic
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  const handleSelectAllData = () => {
    setStatusOfSelectAllFirstData(true);
    getFilterAfterSelectAllOptions();
  };

  const handleFirstPageOptions = () => {
    setStatusOfSelectAllFirstData(true);
    setPage(0);
  };

  const getFilterAfterSelectAllOptions = () => {
    setPage(0);
    setTableLoading(true);
    let payload = {
      material: BomSearchForm?.Material ?? "",
      plant: BomSearchForm?.Plant ?? "",
      bomUsage: BomSearchForm?.BOMUsage ?? "",
      document: BomSearchForm?.Document ?? "",
      documentType: BomSearchForm?.DocType ?? "",
      altBOM:   BomSearchForm?.AlternativeBOM ?? "",
      component: BomSearchForm?.Component ?? "",
      fromDate:
        moment(BomSearchForm?.Date[0]).format("YYYYMMDD") ?? "",
      toDate:
        moment(BomSearchForm?.Date[1]).format("YYYYMMDD") ?? "",
      top: Count,
      skip: 0,
    };
    
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        var rows = [];
        for (let index = 0; index < data?.body?.length; index++) {
          var tempObj = data?.body[index];
          var tempRow = {
            id: uuidv4(),
            Plant: tempObj["Plant"],
            BOMUsage: tempObj["BOMUsage"],
            AlternativeBOM: tempObj["AltBOM"],
            Material: tempObj["Material"],
            Document: tempObj["Document"]!== "" ? tempObj["Document"] : "-",
            DocType: tempObj["DocumentType"]!== "" ? tempObj["DocumentType"] : "-",
            Component: tempObj["Component"]!== "" ? tempObj["Component"] : "-",
          };
          rows.push(tempRow);
        }
        setRmDataRows(rows.reverse());
        setTableLoading(false);
        setPage(Math.floor(rows?.length / pageSize));
        setroCount(data.count);
        setCount(data.count);
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        showToast(data?.message, "error");
        setTableLoading(false);
      }
    };
    
    const hError = (error) => {
      customError(error);
    }; 
    doAjax(
      `/${destination_BOM}${END_POINTS?.MASS_ACTION?.BOM_MASTER_DATA}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const refreshPage = () => {
    getFilter();
  };

  const resetRedux = () => {
    dispatch(clearPayload());
    dispatch(clearRequiredFields());
    dispatch(clearPaginationData());
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
    dispatch(setTaskData({}));
  };

  // Effects
  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);

  useEffect(() => {
    resetRedux();
    fetchOptionsForDynamicFilter([dynamicDataApis]);
    return () => {
      dispatch(commonFilterClear({
        module: MODULE?.BOM,
        days: 7
      }));
    };
  }, []);

  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  useEffect(() => {
    if (masterDataDtResponse) {
      const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
      setDynamicColumns(columnsGlobal);
    }
    if (dtSearchParamsResponse) {
      const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
      const additionalData = response?.filter((item) => {
        return item.MDG_MAT_FILTER_TYPE === "Additional";
      }).map((item) => {
        return { title: t(item.MDG_MAT_UI_FIELD_NAME) };
      });
      setSearchParameters(response);
      setItem(additionalData);
    }
  }, [masterDataDtResponse, dtSearchParamsResponse]);

  useEffect(() => {
    getFilter();
  }, [pageSize]);

  useEffect(() => {
    fetchMasterDataColumns("US");
    fetchSearchParameterFromDt();
  }, []);

  useEffect(() => {
    if (!statusOfSelectAllFirstData) {
      if (page != 0 && page * pageSize >= rmDataRows?.length) {
        getFilterBasedOnPagination();
      }
    }
  }, [page, pageSize]);

  return (
    <>
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{t("Bill Of Material")}</strong>
              </Typography>
              <Typography sx={{mb:"0.5rem" }} variant="body2" color="#777">
                {t("This view displays the Bill of Materials")}
              </Typography>
            </Grid>
            
            <BillOfMaterialFilters
              searchParameters={searchParameters}
              onSearch={getFilter}
              onClear={() => setClearClicked(true)}
              filterFieldData={filterFieldData}
              setFilterFieldData={setFilterFieldData}
              items={items}
            />
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                paginationLoading={tableLoading}
                module={"Bill Of Material"}
                width="100%"
                title={t("List of Bill Of Material")}
                rows={tableData ?? []}
                columns={dynamicColumns ?? []}
                showSearch={true}
                showRefresh={true}
                showSelectedCount={true}
                showExport={true}
                onSearch={(value) => handleSearchAction(value)}
                onRefresh={refreshPage}
                pageSize={pageSize}
                page={page}
                onPageSizeChange={handlePageSizeChange}
                rowCount={roCount ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                getRowIdValue={"id"}
                hideFooter={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                tempheight={'calc(100vh - 320px)'}
                onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  const articleNumber = params.row.Number;
                  const matlType = params?.row?.materialType?.split(" - ")[0];
                  setDisplayFlag(true);

                  navigate(
                    `/masterDataCockpit/articleMaster/DisplayArticleSAPView/${articleNumber}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                showCustomNavigation={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showFirstPageoptions={true}
                showSelectAllOptions={true}
                onSelectAllOptions={handleSelectAllData}
                onSelectFirstPageOptions={handleFirstPageOptions}
              />
            </Stack>
          </Grid>

          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >
              <Button
                size="small"
                variant="contained"
                className="createRequestButtonBOM"
                onClick={() => {
                  navigate(APP_END_POINTS?.CREATE_BOM);
                  dispatch(setDisplayPayload({}));
                }}
              >
                {t("Create Request")}
              </Button>
            </BottomNavigation>
          </Paper>
        </Stack>
      </div>
    </>
  );
};

export default BillOfMaterial;