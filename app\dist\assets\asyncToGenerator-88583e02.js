function s(c,u,i,r,o,a,n){try{var t=c[a](n),e=t.value}catch(f){return void i(f)}t.done?u(e):Promise.resolve(e).then(r,o)}function l(c){return function(){var u=this,i=arguments;return new Promise(function(r,o){var a=c.apply(u,i);function n(e){s(a,r,o,n,t,"next",e)}function t(e){s(a,r,o,n,t,"throw",e)}n(void 0)})}}const v=Object.freeze(Object.defineProperty({__proto__:null,default:l},Symbol.toStringTag,{value:"Module"}));export{l as _,v as a};
