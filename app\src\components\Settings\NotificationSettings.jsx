import React, { useState } from 'react';
import {
    <PERSON>rid,
    <PERSON>ack,
    Typography,
    Switch,
    Box,
    Button
} from '@mui/material';
import useLang from '@hooks/useLang';
import { useDispatch, useSelector } from 'react-redux';
import { setEmailPreference, setNotificationPreference } from '@app/notificationSlice';
import { destination_Websocket } from '../../destinationVariables';
import { doAjax } from '../Common/fetchService';
import { API_CODE } from '@constant/enum';
import { showToast } from '../../functions';
import { ToastContainer } from 'react-toastify';

const NotificationSettings = () => {
    const notificationPreference = useSelector((state) => state.notifications.notificationPreference);
    const emailPreference = useSelector((state) => state.notifications.emailPreference);
    const userData = useSelector((state) => state.userManagement.userData);
    const [inAppNotifications, setInAppNotifications] = useState(notificationPreference);
    const [emailNotifications, setEmailNotifications] = useState(emailPreference);
    const { t } = useLang();
    const dispatch = useDispatch();

    const handleInAppChange = (event) => {
        dispatch(setNotificationPreference(event.target.checked))
        setInAppNotifications(event.target.checked);
    };

    const handleEmailChange = (event) => {
        dispatch(setEmailPreference(event.target.checked))
        setEmailNotifications(event.target.checked);
    };

    const handleSave = () => {
        const payload = {
            "emailNotification": emailNotifications,
            "smsNotification": null,
            "inAppNotification": inAppNotifications,
            "pushNotification": null,
            "snoozed": null,
            "snoozeUntil": null
        }
        let hSuccess = (data) => {
            if (data?.statusCode === API_CODE?.STATUS_200)
                showToast(data?.message, "success")
            else
                showToast(data?.message, "error")
        };
        let hError = (error) => {
            showToast(error?.message, "error")
        };
        doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}`,
            "post",
            hSuccess,
            hError,
            payload
        );
    };

    return (
        <>
        <Box sx={{ p: 3, position: 'relative', minHeight: '400px' }}>
            <Typography variant="h5" sx={{ mb: 4 }}>
                {t("Notification Settings")}
            </Typography>
            <Stack spacing={2}>
                <Grid container alignItems="center" justifyContent="space-between">
                    <Grid item>
                        <Typography variant="body1">
                            In App Notifications
                        </Typography>
                    </Grid>
                    <Grid item>
                        <Switch
                            checked={inAppNotifications}
                            onChange={handleInAppChange}
                            color="primary"
                        />
                    </Grid>
                </Grid>

                <Grid container alignItems="center" justifyContent="space-between">
                    <Grid item>
                        <Typography variant="body1">
                            Email Notifications
                        </Typography>
                    </Grid>
                    <Grid item>
                        <Switch
                            checked={emailNotifications}
                            onChange={handleEmailChange}
                            color="primary"
                        />
                    </Grid>
                </Grid>
            </Stack>
        </Box>
        <Box 
            sx={{ 
                position: 'absolute', 
                bottom: 16, 
                right: 24,
                display: 'flex',
                gap: 1 
            }}
        >
            <Button
                variant="contained"
                onClick={handleSave}
                sx={{ textTransform: "capitalize" }}
            >
                {t("Save")}
            </Button>
        </Box>
        <ToastContainer/>
        </>
    );
}

export default NotificationSettings