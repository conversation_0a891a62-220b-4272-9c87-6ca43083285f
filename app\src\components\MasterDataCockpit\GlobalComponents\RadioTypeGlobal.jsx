import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { colors } from "@constant/colors";
import { Checkbox, Grid, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import { updateModuleFieldDataCC } from "@app/costCenterTabsSlice";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { CHANGE_LOG_STATUSES, MODULE, MODULE_PAYLOAD_DATA, OBJECT_MUMBER_KEY, VISIBILITY_TYPE } from "@constant/enum";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";
import useLang from "@hooks/useLang";
import { useLocation } from "react-router-dom";



const RadioTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange, module } = props;
  const dispatch = useDispatch();
  const location = useLocation();
  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
   const rowsBodyData = MODULE_PAYLOAD_DATA[module] || (() => ({}));
   const objectKey = OBJECT_MUMBER_KEY[module] || (() => ({}));
    const rowsObjectNumber = rowsBodyData?.[uniqueId]?.[objectKey]

  let requestStatus = rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"];
  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload);
  const valueFromPayloadCC = useSelector((state) => state.costCenter.payload);
  const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
  const valueFromPayloadIO = useSelector((state) => state.internalOrder.IOpayloadData || {});

  const valueToCheck =
  module === MODULE?.CC ?
  valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  field?.value : module === "GeneralLedger"?
  valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ??
  field?.value ?? "" : module === MODULE.IO ?
  valueFromPayloadIO?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName] ?? valueFromPayloadIO?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? "" :
  valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
  field?.value ??
  false;
  const initialFieldValue = valueToCheck === "X" || valueToCheck === true || valueToCheck === "TRUE" ? true : false;
  const [localValue, setLocalValue] = useState(initialFieldValue);

  const SAPview = [
  "displayGeneralLedgerMasterdata",
  "displayProfitCenter",
  "displayCostCenter",
  "DisplayMaterialSAPView",
  "DisplayBankKeySAPView"
].some((path) => location?.pathname?.includes(path));
      const { t } = useLang();

  useEffect(() => {
    setLocalValue(initialFieldValue);
    if(initialFieldValue) {
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: initialFieldValue,
          viewID: field?.viewName,
        })
      );
    }
  }, [initialFieldValue]);


const handleCheckBoxChange = (e) => {
    const updatedValue = e.target.checked;
    setLocalValue(updatedValue);
    if(module === "CostCenter"){
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
             {
         updateChangeLogGl({
          objectNumber: rowsObjectNumber,
          uniqueId: uniqueId || "",
          viewName: props?.field?.viewName,
          plantData: '',
          fieldName: props?.field?.fieldName,
          jsonName: props?.field?.jsonName,
          currentValue: updatedValue,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }
    }
    else if(module === "GeneralLedger"){
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
       {
         updateChangeLogGl({
          objectNumber: rowsObjectNumber,
          uniqueId: uniqueId || "",
          viewName: props?.field?.viewName,
          plantData: '',
          fieldName: props?.field?.fieldName,
          jsonName: props?.field?.jsonName,
          currentValue: updatedValue,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }
    }
    else if(module === MODULE.IO){
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
    }
    else{
    dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
      {
         updateChangeLogGl({
          objectNumber: rowsObjectNumber,
          uniqueId: uniqueId || "",
          viewName: props?.field?.viewName,
          plantData: '',
          fieldName: props?.field?.fieldName,
          jsonName: props?.field?.jsonName,
          currentValue: updatedValue,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }
    }
  };

  return (
    <Grid item md={2}>
       {SAPview ? (
              <div
                style={{
                  padding: "16px",
                  backgroundColor: colors.primary.white,
                  borderRadius: "8px",
                  boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                  margin: "16px 0",
                  transition: "all 0.3s ease",
                }}
              >
                <Typography
                  variant="body1"
                  style={{
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    maxWidth: "100%",
                    fontWeight: 600,
                    fontSize: "12px",
                    marginBottom: "4px",
                    display: "flex",
                    alignItems: "center",
                  }}
                   title={field.fieldName}
                >
                  {field.fieldName}
                  {field.visibility === "Required" ||
                  field.visibility === "0" || field?.visibility === VISIBILITY_TYPE.MANDATORY ? (
                    <span style={{ color: colors.error.dark }}>*</span>
                  ) : (
                    ""
                  )}
                </Typography>
      
                <div
                  style={{
                    fontSize: "0.8rem",
                    color: colors.black.dark,
                    marginTop: "4px",
                  }}
                >
                  <span
                    style={{
                      fontWeight: 500,
                      color: colors.secondary.grey,
                      letterSpacing: "0.5px", 
                      wordSpacing: "1px",
                    }}
                  >
                    {localValue ? "Yes" : "No"}
                    
                  </span>
                </div>
              </div>
            ) : (
        <>
          <Typography
            variant="body2"
            color={colors.secondary.grey}
            sx={{
              whiteSpace: "nowrap",
              overflow: "hidden",
              textOverflow: "ellipsis",
              maxWidth: "100%",
              
            }}
            title={field.fieldName}
          >
            {field.fieldName}
            {field.visibility === "Required" ||
            field.visibility === "0" || field?.visibility === VISIBILITY_TYPE.MANDATORY ? (
              <span style={{ color: colors.error.dark,marginLeft:10 }}>*</span>
            ) : (
              ""
            )}
          </Typography>
          <Checkbox
            sx={{
              padding: 0,
              marginTop:"5px",
              "&.Mui-disabled": {
                color: colors.hover.light,
              },
              "&.Mui-disabled.Mui-checked": {
                color: colors.hover.light,
              },
            }}
            disabled={disabled}
            checked={localValue}
            onChange={handleCheckBoxChange}
          />
        </>
      )}
    </Grid>
  );
};

export default RadioTypeGlobal;
