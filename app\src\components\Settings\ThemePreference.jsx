import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, useTheme } from "@mui/material";
import ThemeCard from "./ThemeCard";
import { useSelector, useDispatch } from "react-redux";
import { customTheme, themesPalette } from "../../theme";
import { setThemePreference } from "../../app/userManagementSlice"; // update path as needed
import { destination_Websocket } from "../../destinationVariables";
import { doAjax } from "../Common/fetchService";
import { API_CODE } from "../../constant/enum";
import { showToast } from "../../functions";
import { ToastContainer } from "react-toastify";

const paletteKeys = [
  { label: "pri.main", key: "primary.main" },
  { label: "pri.light", key: "primary.light" },
  { label: "sec.main", key: "secondary.main" },
  { label: "sec.light", key: "secondary.light" },
  { label: "bg.default", key: "background.default" },
  { label: "bg.paper", key: "background.paper" },
  { label: "txt.primary", key: "text.primary" },
  { label: "txt.secondary", key: "text.secondary" },
];

const themesArr = Array.from(themesPalette.values());

const ThemePreference = () => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const ThemePref = useSelector((state) => state.userManagement.themePreference) || themesArr[0]?.themeKey;
  const userData = useSelector((state) => state.userManagement.userData);

  const handleThemeSelect = (themeKey) => {
    dispatch(setThemePreference(themeKey));
  };

  const handleApplyTheme = () => {
    localStorage.setItem("selectedTheme", ThemePref); // optional: use ThemeProvider context update instead of reload
    showToast("Theme applied successfully", "success");
  };

  const handleSaveAsDefault = () => {
    localStorage.setItem("selectedTheme", ThemePref);
    const payload = {
        "emailNotification": null,
        "smsNotification": null,
        "inAppNotification": null,
        "pushNotification": null,
        "snoozed": null,
        "snoozeUntil": null,
        "theme": ThemePref
    }
    let hSuccess = (data) => {
        if(data?.statusCode === API_CODE?.STATUS_200)
        showToast(data?.message, "success")
        else
        showToast(data?.message, "error")
    };
    let hError = (error) => {
        showToast(error?.message, "error")
    };
    doAjax(`/${destination_Websocket}/notification-preferences/${userData?.emailId}`,
        "post",
        hSuccess,
        hError,
        payload
    );
  };
  return (
    <>
    <Box sx={{ margin: { xs: "1rem", md: "0 10%" } }}>
      <Grid container direction="column" spacing={4}>
        <Grid item>
          <Toolbar sx={{ pl: 2, pr: 2 }}>
            <Box>
              <Typography variant="h5" fontWeight={600}>
                Theme Preference
              </Typography>
              <Typography variant="body2" color="text.secondary">
                You can select a theme from the options below.
              </Typography>
            </Box>
          </Toolbar>

          <Divider />

          <Stack direction="row" justifyContent="flex-end" spacing={2} sx={{ m: 2 }}>
            <Button
              variant="contained"
              size="small"
              onClick={handleApplyTheme}
              color="primary"
            >
              Apply Theme
            </Button>
            <Button variant="outlined" size="small" onClick={handleSaveAsDefault}>
              Save as Default
            </Button>
          </Stack>

          <Grid container spacing={3} sx={{ p: 2 }}>
            {themesArr.map((theme) => (
              <Grid item xs={12} md={6} key={theme.themeKey}>
                <ThemeCard theme={theme} selected={ThemePref === theme.themeKey} onSelect={() => handleThemeSelect(theme.themeKey)} paletteKeys={paletteKeys} />
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Box>
    <ToastContainer/>
    </>
  );
};

export default ThemePreference;
