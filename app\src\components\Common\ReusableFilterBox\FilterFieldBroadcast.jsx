import React, { useState, useRef } from 'react';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  Box,
  Grid,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Autocomplete,
  Chip,
  Popover,
  Tooltip,
  CircularProgress,
  FormGroup,
  FormControlLabel,
  styled,
  useTheme
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import SearchIcon from '@mui/icons-material/Search';
import useLang from '../../../hooks/useLang';
import { colors } from '../../../constant/colors';
// import { commonFilterUpdate } from '../../app/commonFilterSlice';

// Define field types constants
const SEARCH_FIELD_TYPES = {
  INPUT: 'INPUT',
  MULTISELECT: 'MULTISELECT',
  DROPDOWN: 'DROPDOWN',
  DATERANGE: 'DATERANGE',
  AUTOCOMPLETE: 'AUTOCOMPLETE',
  CATEGORY: 'CATEGORY',
  STATUS: 'STATUS',
  SUPPLIER: 'SUPPLIER'
};

// Styled components
const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));

const StyledTextField = styled(TextField)({
  '& .MuiOutlinedInput-root': {
    borderRadius: '4px',
    '&:hover fieldset': {
      borderColor: colors.primary.main,
    },
  },
});

// AutoComplete Dropdown Component
const AutoCompleteSimpleDropDown = ({ options, value, onChange, placeholder, multiple = true }) => {
  const [inputValue, setInputValue] = useState('');
  
  return (
    <Autocomplete
      multiple={multiple}
      size="small"
      options={options || []}
      value={value || (multiple ? [] : '')}
      onChange={(event, newValue) => onChange(newValue)}
      inputValue={inputValue}
      onInputChange={(event, newInputValue) => setInputValue(newInputValue)}
      renderInput={(params) => (
        <StyledTextField
          {...params}
          placeholder={placeholder}
          variant="outlined"
        />
      )}
      renderTags={(selected, getTagProps) => {
        if (selected.length <= 1) {
          return selected.map((option, index) => (
            <Chip
              key={index}
              size="small"
              label={option}
              {...getTagProps({ index })}
              sx={{ height: 24, fontSize: '0.75rem' }}
            />
          ));
        }
        return (
          <>
            <Chip
              size="small"
              label={selected[0]}
              {...getTagProps({ index: 0 })}
              sx={{ height: 24, fontSize: '0.75rem' }}
            />
            <Chip
              size="small"
              label={`+${selected.length - 1}`}
              sx={{ height: 24, fontSize: '0.75rem' }}
            />
          </>
        );
      }}
      sx={{
        '& .MuiAutocomplete-inputRoot': {
          padding: '2px 8px',
        },
      }}
    />
  );
};

const DateRange = ({ handleDate, date }) => {
  const formatDate = (dateValue) => {
    if (!dateValue) return '';
    return new Date(dateValue).toISOString().split('T')[0];
  };

  return (
    <Box sx={{ display: 'flex', gap: 1 }}>
      <StyledTextField
        type="date"
        size="small"
        value={formatDate(date?.[0])}
        onChange={(e) => handleDate([new Date(e.target.value), date?.[1]])}
        InputLabelProps={{ shrink: true }}
        sx={{ flex: 1 }}
      />
      <StyledTextField
        type="date"
        size="small"
        value={formatDate(date?.[1])}
        onChange={(e) => handleDate([date?.[0], new Date(e.target.value)])}
        InputLabelProps={{ shrink: true }}
        sx={{ flex: 1 }}
      />
    </Box>
  );
};

const FilterFieldBroadcast = ({
  searchParameters,
  filterData,
  onFilterChange,
  onSearch,
  onClear,
  moduleName = "BroadcastHome",
  isLoading = false,
}) => {
  const dispatch = useDispatch();
  const theme = useTheme()
  const filterForm = useSelector(
    (state) => state.commonFilter?.[moduleName] || {}
  );
  const { t } = useLang();

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    if (onFilterChange) {
      onFilterChange(name, value);
    }
  };

  const renderFieldByType = (item, index) => {
    const fieldName = item?.filterName;
    const fieldTitle = item?.filterTitle;
    const fieldType = item?.type;
    const fieldData = item?.filterData || [];

    switch (fieldType) {
      case SEARCH_FIELD_TYPES.INPUT:
      case 'text':
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <StyledTextField
              size="small"
              name={fieldName}
              fullWidth
              onChange={handleInputChange}
              placeholder={t(`ENTER ${fieldTitle}`).toUpperCase()}
              value={filterForm?.[fieldName] || ''}
            />
          </Grid>
        );

      case SEARCH_FIELD_TYPES.MULTISELECT:
      case 'multiSelect':
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <AutoCompleteSimpleDropDown
              options={fieldData}
              value={filterForm?.[fieldName] || []}
              onChange={(newValue) => {
                if (onFilterChange) {
                  onFilterChange(fieldName, newValue);
                }
              }}
              placeholder={t(`SELECT ${fieldTitle}`).toUpperCase()}
            />
          </Grid>
        );

      case SEARCH_FIELD_TYPES.DROPDOWN:
      case 'dropdown':
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <FormControl fullWidth size="small">
              <Select
                value={filterForm?.[fieldName] || ''}
                onChange={(e) => {
                  if (onFilterChange) {
                    onFilterChange(fieldName, e.target.value);
                  }
                }}
                displayEmpty
                sx={{
                  fontSize: '0.875rem',
                  height: '36px',
                }}
              >
                <MenuItem value="">
                  <em>{t(`SELECT ${fieldTitle}`).toUpperCase()}</em>
                </MenuItem>
                {fieldData.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        );

      case SEARCH_FIELD_TYPES.DATERANGE:
      case 'dateRange':
        return (
          <Grid item md={3} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
              <LocalizationProvider dateAdapter={AdapterDateFns}>
                <DateRange
                  handleDate={(dateRange) => {
                    if (onFilterChange) {
                      onFilterChange(fieldName, dateRange);
                    }
                  }}
                  date={filterForm?.[fieldName]}
                />
              </LocalizationProvider>
            </FormControl>
          </Grid>
        );

      case SEARCH_FIELD_TYPES.CATEGORY:
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <AutoCompleteSimpleDropDown
              options={['Announcements', 'Videos', 'Events']}
              value={filterForm?.[fieldName] || []}
              onChange={(newValue) => {
                if (onFilterChange) {
                  onFilterChange(fieldName, newValue);
                }
              }}
              placeholder={t(`SELECT ${fieldTitle}`).toUpperCase()}
            />
          </Grid>
        );

      case SEARCH_FIELD_TYPES.STATUS:
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <AutoCompleteSimpleDropDown
              options={['Active', 'Inactive', 'Draft', 'Archived']}
              value={filterForm?.[fieldName] || []}
              onChange={(newValue) => {
                if (onFilterChange) {
                  onFilterChange(fieldName, newValue);
                }
              }}
              placeholder={t(`SELECT ${fieldTitle}`).toUpperCase()}
            />
          </Grid>
        );

      case SEARCH_FIELD_TYPES.SUPPLIER:
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <AutoCompleteSimpleDropDown
              options={fieldData}
              value={filterForm?.[fieldName] || []}
              onChange={(newValue) => {
                if (onFilterChange) {
                  onFilterChange(fieldName, newValue);
                }
              }}
              placeholder={t(`SELECT ${fieldTitle}`).toUpperCase()}
            />
          </Grid>
        );

      default:
        return (
          <Grid item md={2} key={index}>
            <LabelTypography>{t(fieldTitle)}</LabelTypography>
            <StyledTextField
              size="small"
              name={fieldName}
              fullWidth
              onChange={handleInputChange}
              placeholder={t(`ENTER ${fieldTitle}`).toUpperCase()}
              value={filterForm?.[fieldName] || ''}
            />
          </Grid>
        );
    }
  };

  return (
    <Grid container>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: theme.palette.primary.dark }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            className="filterBroadCast"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark }} />
            <Typography
            sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: theme.palette.primary.dark,
            }}
            >
              {t("Filter Broadcast")}
            </Typography>
          </StyledAccordionSummary>
          
          <AccordionDetails sx={{ padding: 0 }}>
            <FilterContainer container>
              {searchParameters
              ?.map((item, index) => renderFieldByType(item, index))
              }
            </FilterContainer>
            
            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                onClick={onClear}
                disabled={isLoading}
              >
                {t("Clear")}
              </ActionButton>

              <ActionButton
                variant="contained"
                size="small"
                startIcon={isLoading ? <CircularProgress size={16} color="inherit" /> : <SearchIcon sx={{ fontSize: '1rem' }} />}
                onClick={onSearch}
                disabled={isLoading}
              >
                {isLoading ? t("Searching...") : t("Search")}
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default FilterFieldBroadcast;