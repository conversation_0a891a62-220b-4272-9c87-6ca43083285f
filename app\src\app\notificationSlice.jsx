import { createSlice } from "@reduxjs/toolkit";

let initialState = {
    accountActivity:{
      data:[],
      count:0
    },
    notification:{
      data:[],
      count:0
    },
    notifications:[],
    notificationPreference: false,
    emailPreference: false
}
const notificationReducerSlice = createSlice({
    name: "notificationSlice",
    initialState,
    reducers: {
      setAccountActivities(state, action) {
        
        state.accountActivity.data = action.payload
        state.accountActivity.count = action.payload.length
        return state;
      },
      setNotification(state, action) {
        state.notification.data = action.payload
        state.notification.count = action.payload.length
        return state;
      },
      setNotificationData(state, action) {
        // const exists = state.notifications?.some(notif => notif.id === action.payload.id);
        // if (!exists) {
          state.notifications?.unshift(action.payload);
        // }
        return state;
      },
      setNotificationPreference(state, action) {
          state.notificationPreference = action.payload;
        return state;
      },
      setEmailPreference(state, action) {
          state.emailPreference = action.payload;
        return state;
      },
      setNotificationsDirect(state, action) {
          state.notifications = action.payload;
        return state;
      },
      clearNotificationData(state, action) {
        state.notifications = []
        return state;
      },
      updateNotificationData(state, action) {
        if (action.payload === "markAllAsRead") {
          state.notifications = state.notifications?.map((notif) => ({
              ...notif,
              isRead: true
          }));
        } else {
            const { indexId, updatedData } = action.payload;
            state.notifications = state.notifications?.map((notif, index) =>
                notif?.id === indexId ? { ...notif, ...updatedData } : notif
            );
        }
      },
      updateNotification(state,action){
        state.notification.data = action.payload
        // state.notification.count = action.payload.length
        return state;
      },
      pushNotification(state, action) {
        console.log(action.payload,'actionpayload')
        if(action.payload.notificationType?.toLowerCase() === 'account activity'){
          state.accountActivity.data = [action.payload,...state.accountActivity.data]
          state.accountActivity.count = state.accountActivity.count+1
        }
        if(action.payload.notificationType?.toLowerCase() === 'notification'){
          state.notification.data = [action.payload,...state.notification.data]
          state.notification.count = state.notification.count+1
        }
        return state;
      },
      resetNotificationCount:(state,action)=>{
        state.notification.count = 0
        return state;
      },
      resetAcntActivityCount:(state,action)=>{
        state.accountActivity.count = 0
        return state;
      }
    },
  });
  
  export const {setNotificationPreference, setEmailPreference, setNotification, setNotificationData, setNotificationsDirect, updateNotificationData, clearNotificationData, updateNotification, setAccountActivities,resetAcntActivityCount,pushNotification,resetNotificationCount} = notificationReducerSlice.actions;
  
  export default notificationReducerSlice.reducer;