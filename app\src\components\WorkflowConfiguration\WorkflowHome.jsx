import { IosShare, Refresh } from "@mui/icons-material";
import useLang from "@hooks/useLang";
import useDownloadExcel from "@hooks/useDownloadExcel";
import ScenarioDialogPopup from './WorkflowHomeScreen/ScenarioDialogPopup';
import ExcelOperationsDialogPopup from './WorkflowHomeScreen/ExcelOperationsDialogPopup';
import { BottomNavigation, Box, Button, Grid, IconButton, Paper, Stack, Tab, Tabs, Tooltip, Typography } from "@mui/material";
import * as React from "react";
import { iconButton_SpacingSmall, outermostContainer, outermostContainer_Information } from "../common/commonStyles";
import WorkflowFilter from "./WorkflowFilter";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined';
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { destination_Admin } from "../../destinationVariables";
import moment from "moment";
import { doAjax } from "../Common/fetchService";
import ReusableBackDrop from "../Common/ReusableBackDrop";
import CreateInApplicationFilter from "./WorkflowHomeScreen/CreateInApplicationFilter";
import { API_CODE, changeLogWFColumns, ACTIVE_TAB_MODULE_MAP, MODULE_MAP, REQUEST_TYPE } from "@constant/enum";
import { useSnackbar } from "@hooks/useSnackbar";
import ChangeLogWF from "./WorkflowGenerator/ChangeLogWF";
import { exportToExcelGlobal } from "@helper/helper";
import { END_POINTS } from "@constant/apiEndPoints";
import SelectionSummaryWF from "./WorkflowHomeScreen/SelectionSummaryWF";
import AttachmentUploadDialog from "@components/Common/AttachmentUploadDialog";
import WorkflowTable from "./WorkflowTable";

function WorkflowHome() {
    const navigate = useNavigate();
    const [wfSearchForm, setWfSearchForm] = useState({});
    const [isLoading, setIsLoading] = useState(true);
    const { t } = useLang();
    const [activeTab, setActiveTab] = useState(0);
    const [rmDataRows, setRmDataRows] = useState([]);
    const [filters, setFilters] = useState([]);
    const [blurLoading, setBlurLoading] = useState(false);
    const [loaderMessage, setLoaderMessage] = useState("");
    const [openChangeLog, setOpenChangeLog] = useState(false);
    const [changeLogData, setChangeLogData] = useState([]);
    const { showSnackbar } = useSnackbar();
    const [tableData, setTableData] = useState([]);
    const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
    const [selectedOption, setSelectedOption] = useState("Material");
    const [selectedRows, setSelectedRows] = useState([]);
    const { handleDownloadWF, handleUploadWF, handleMassCancelWF, handleMassDownloadWF } = useDownloadExcel();
    const [dialogState, setDialogState] = useState({ createWorkflow: false, excelOperations: false });
    const [enableChangeWorkflowUpload, setEnableChangeWorkflowUpload] = useState(false);
    const [open, setOpen] = useState(false);

    const handleChanged = (event) => {
        setSelectedOption(event.target.value);
    };

    const handleOpenDialog = () => {
        setOpen(true);
    };

    const handleCloseDialog = () => {
        setOpen(false);
    };

    const handleApplyFilters = (filters) => {
        navigate("createWorkflow", { 
            state: { 
                mode: "create", 
                headerData: filters, 
                workflowData: {}, 
                module: filters?.module || ACTIVE_TAB_MODULE_MAP?.[activeTab] 
            } 
        });
    };

    useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);
    
    const handleDownload = () => {
        handleDownloadWF(setLoaderMessage, setBlurLoading, selectedOption || ACTIVE_TAB_MODULE_MAP?.[activeTab], "create");
    };
    
    const handleUpload = (file) => {
        handleUploadWF(file, setLoaderMessage, setBlurLoading, selectedOption || ACTIVE_TAB_MODULE_MAP?.[activeTab], setEnableDocumentUpload, handleClose, setActiveTab, REQUEST_TYPE?.CREATE_WITH_UPLOAD);
    }

    const handleMassCancel = (selectedWorkflowIds) => {
        handleMassCancelWF(selectedWorkflowIds, setLoaderMessage, setBlurLoading, setSelectedRows, activeTab, getAllWorflows)
    };
    
    const handleChangeWorkflowUpload = (files) => {
        setEnableChangeWorkflowUpload(false);
        handleUploadWF(files, setLoaderMessage, setBlurLoading, selectedOption, setEnableChangeWorkflowUpload, () => {}, setActiveTab, REQUEST_TYPE?.CHANGE_WITH_UPLOAD);
    };

    const handleMassDownload = (selectedWorkflowIds) => {
        handleMassDownloadWF(selectedWorkflowIds, setLoaderMessage, setBlurLoading, setSelectedRows, activeTab)
    };

    const handleDownloadWFAllData = () => {
        handleDownloadWF(setLoaderMessage, setBlurLoading, ACTIVE_TAB_MODULE_MAP?.[activeTab], "");
    };

    const handleChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    const handleChangeLogClose = () => {
        setOpenChangeLog(false);
    };

    const handleChangeLogExport = () => {
        const payload = { module: ACTIVE_TAB_MODULE_MAP?.[activeTab] };
        setBlurLoading(true);
        const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.CONSOLIDATED_CHANGELOG}`;
        const hSuccess = (data) => {
            setBlurLoading(false);
            if (data?.statusCode === API_CODE.STATUS_200) {
                const rows = data?.data || [];
                exportToExcelGlobal(rows, changeLogWFColumns, { fileName: `Change Log - ${ACTIVE_TAB_MODULE_MAP?.[activeTab]}` });
            }
        }
        const hError = () => {
            setBlurLoading(false);
        }

        doAjax(newUrl, "post", hSuccess, hError, payload);
    };

    const handleClose = () => {
        setDialogState(prevState => ({
            ...prevState,
            excelOperations: false,
        }));
    };

    const handleSelectionChange = (newSelection) => {
        setSelectedRows(newSelection)
    };

    useEffect(() => {
        getAllWorflows(activeTab)
    }, [activeTab]);

    const getAllWorflows = (activeTab = 0) => {
        setIsLoading(true);
        var payload = {
            "page": 0,
            "size": 100,
            "orderBy": "createdOn",
            "module": ACTIVE_TAB_MODULE_MAP?.[activeTab] || MODULE_MAP?.MAT,
            "region": wfSearchForm?.region || [],
            "scenario": wfSearchForm?.scenario || [],
            "bifurcationGroups": wfSearchForm?.bifurcationGroup || [],
            "createdBy": wfSearchForm?.createdBy || [],
            "workflowName": wfSearchForm?.workflowName || [],
            "fromDate": wfSearchForm?.createdOn?.[0] ? moment(wfSearchForm?.createdOn[0]).format("YYYY-MM-DDThh:mm:ss") : "",
            "toDate": wfSearchForm?.createdOn?.[1] ? moment(wfSearchForm?.createdOn[1]).format("YYYY-MM-DDThh:mm:ss") : "",
        }
        const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.FETCH_WF_LIST}`;
        const hSuccess = (data) => {
            if (data?.statusCode === API_CODE?.STATUS_200) {
                var rows = [];
                for (let index = 0; index < data?.data?.length; index++) {
                    var tempObj = data?.data?.[index];
                    var tempRow = {
                        id: tempObj?.Workflow_Id || tempObj?.id || "",
                        workflowId: tempObj?.Workflow_Id || tempObj?.id || "",
                        workflowName: tempObj?.Workflow_Name,
                        scenario: tempObj?.Scenario,
                        createdAt: moment(tempObj.Created_At).format("DD MMM YYYY") ?? "",
                        createdBy: tempObj?.Created_By,
                        updatedAt: moment(tempObj.Last_Updated_At).format("DD MMM YYYY") ?? "",
                    };
                    rows.push(tempRow);
                }
                setRmDataRows(rows);
                setIsLoading(false);
            }
            else {
                setRmDataRows([]);
                setIsLoading(false);
            }
        };

        const hError = (error) => {
            setRmDataRows([]);
            setIsLoading(false);
        };
        doAjax(newUrl, "post", hSuccess, hError, payload);
    };

    let tabsArray = [
        t("Material"),
        "Article",
        "Profit Center",
        "Cost Center",
        "Bank Key",
        "General Ledger",
        "Cost Center Group",
        "Profit Center Group",
        "Cost Element Group",
        "Bill Of Material",
        "Internal Order"
    ];

    return (
        <>
            <div className="printScreen" id={"container_outermost"}>
                <ScenarioDialogPopup
                    open={dialogState.createWorkflow}
                    onClose={() => setDialogState((prev) => ({ ...prev, createWorkflow: false }))}
                    setDialogState={setDialogState}
                    handleOpenDialog={handleOpenDialog}
                />
                <div
                    className="ServiceRequest"
                    style={{
                        ...outermostContainer,
                        margin: "0rem 0rem",
                        backgroundColor: "#FAFCFF",
                    }}
                >
                    <Stack>
                        <Grid container mt={0} sx={outermostContainer_Information}>
                            <Grid item md={5} xs={12}>
                                <Typography variant="h3">
                                    <strong>{t("Workflow Management")}</strong>
                                </Typography>
                                <Typography variant="body2" color="#777">
                                    {t("This view displays the list of Workflows")}
                                </Typography>
                            </Grid>
                            <Grid item md={7} xs={12} sx={{ display: "flex" }}>
                                <Grid container direction="row" justifyContent="flex-end" alignItems="center" spacing={0} mt={0}>
                                    <Tooltip title="Reload">
                                        <IconButton sx={{ ...iconButton_SpacingSmall }} onClick={() => getAllWorflows(activeTab)}>
                                            <Refresh />
                                        </IconButton>
                                    </Tooltip>
                                    <Tooltip title="Export Table">
                                        <IconButton sx={{ ...iconButton_SpacingSmall }} onClick={() => exportToExcelGlobal(tableData, [], { fileName: `Workflow List - ${ACTIVE_TAB_MODULE_MAP?.[activeTab]}` })}>
                                            <IosShare />
                                        </IconButton>
                                    </Tooltip>
                                </Grid>
                            </Grid>
                        </Grid>
                        
                        <WorkflowFilter 
                            handleSearch={() => { }} 
                            setWfSearchForm={setWfSearchForm} 
                            wfSearchForm={wfSearchForm} 
                            activeTab={activeTab} 
                            getAllWorflows={getAllWorflows} 
                        />
                        
                        <Grid container>
                            <Tabs
                                className='module-WF'
                                value={activeTab}
                                onChange={handleChange}
                                variant="scrollable"
                                sx={{
                                    background: "#FFF",
                                    borderBottom: "1px solid #BDBDBD",
                                    width: "100%",
                                }}
                                aria-label="mui tabs example"
                            >
                                {tabsArray.map((factor, index) => (
                                    <Tab
                                        disabled={false}
                                        sx={{ fontSize: "12px", fontWeight: "700" }}
                                        key={index}
                                        label={factor}
                                    />
                                ))}
                            </Tabs>
                        </Grid>

                        <ExcelOperationsDialogPopup
                            open={dialogState.excelOperations}
                            handleClose={handleClose}
                            setDialogState={setDialogState}
                            selectedOption={selectedOption}
                            handleChanged={handleChanged}
                            MODULE_MAP={MODULE_MAP}
                            handleDownload={handleDownload}
                            setEnableDocumentUpload={setEnableDocumentUpload}
                            enableDocumentUpload={enableDocumentUpload}
                            handleUpload={handleUpload}
                        />

                        <Grid container>
                            <Box sx={{ width: "100%" }}>
                                <WorkflowTable
                                    isLoading={isLoading}
                                    tableData={tableData}
                                    selectedRows={selectedRows}
                                    activeTab={activeTab}
                                    onSelectionChange={handleSelectionChange}
                                    onSearch={(value) => handleSearchAction(value)}
                                    onChangeLogExport={handleChangeLogExport}
                                    onDownloadAllData={handleDownloadWFAllData}
                                    onDelete={(params) => setIsDeleteDialogVisible({ data: params, isVisible: true })}
                                    onView={(workflowId) => fetchWorkflowDetails(workflowId, "view")}
                                    onEdit={(workflowId) => fetchWorkflowDetails(workflowId, "edit")}
                                    onViewChangelog={(workflowId) => fetchChangeLogDetails(workflowId)}
                                    rmDataRows={rmDataRows}
                                    setTableData={setTableData}
                                />
                            </Box>
                        </Grid>

                        <CreateInApplicationFilter
                            open={open}
                            onClose={handleCloseDialog}
                            onApplyFilters={handleApplyFilters}
                            setFiltersObj={setFilters}
                        />

                        {enableChangeWorkflowUpload && (
                            <AttachmentUploadDialog
                                artifactId="" 
                                artifactName="" 
                                setOpen={setEnableChangeWorkflowUpload} 
                                handleUpload={handleChangeWorkflowUpload}
                                showModuleDropdown={true}
                                selectedOption={selectedOption}
                                handleChanged={handleChanged}
                                dialogTitle="Change Workflow"
                            />
                        )}

                        <ChangeLogWF
                            open={openChangeLog}
                            onClose={handleChangeLogClose}
                            data={changeLogData}
                        />

                        <Paper
                            sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
                            elevation={2}
                        >
                            <BottomNavigation
                                className="container_BottomNav"
                                showLabels
                                sx={{
                                    display: "flex",
                                    justifyContent: "flex-end",
                                }} >
                                <Tooltip title= "Edit Single or Multiple Workflows through upload">
                                <Button
                                    className='buttonChangeWorkFlow'
                                    size="small"
                                    variant="contained"
                                    sx={{m: 1}}
                                    onClick={() => setEnableChangeWorkflowUpload(true)}
                                    startIcon={<ModeEditOutlineOutlinedIcon />}
                                >
                                    {t("Change Workflow")}
                                </Button>
                                </Tooltip>
                                <Tooltip title= "Create New Workflows">
                                <Button
                                    className='buttonCreateWorkFlow'
                                    size="small"
                                    variant="contained"
                                    onClick={() => setDialogState({ ...dialogState, createWorkflow: true })}
                                    startIcon={<AddOutlinedIcon />}
                                >
                                    {t("Create Workflow")}
                                </Button>
                                </Tooltip>
                            </BottomNavigation>
                        </Paper>

                        {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}

                        <SelectionSummaryWF
                            selectedRows={selectedRows}
                            count={tableData.length}
                            tableData={tableData}
                            handleMassCancel={handleMassCancel}
                            handleMassDownload={handleMassDownload}
                            maxLimit={10}
                        />
                    </Stack>
                </div>
            </div>
        </>
    );

    // Helper functions that will be used by WorkflowTable component
    function fetchWorkflowDetails(workflowId, mode) {
        setBlurLoading(true);
        const payload = { workflowId };
        const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.FETCH_WF_DETAILS}`;
        const hSuccess = (data) => {
            setBlurLoading(false);
            const { headerData, ...workflowData } = data?.data || {};
            if (mode === "view") {
                navigate("viewWorkflow", { state: { mode, headerData, workflowData, module: ACTIVE_TAB_MODULE_MAP?.[activeTab] } })
            } else if (mode === "edit") {
                navigate("editWorkflow", { state: { mode, headerData, workflowData, module: ACTIVE_TAB_MODULE_MAP?.[activeTab] } })
            }
        }
        const hError = () => {
            setBlurLoading(false);
        }
        doAjax(newUrl, "post", hSuccess, hError, payload);
    }

    function fetchChangeLogDetails(workflowId) {
        setBlurLoading(true);
        const payload = { workflowId };
        const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.FETCH_CHANGELOG}`;
        const hSuccess = (data) => {
            setBlurLoading(false);
            if (data?.statusCode === API_CODE.STATUS_200) {
                const dataWithIds = data?.data?.map((item, index) => ({
                    ...item,
                    id: item.id || index + 1
                }));
                setChangeLogData(dataWithIds);
                setOpenChangeLog(true);
            }
        }

        const hError = () => {
            setBlurLoading(false);
        }

        doAjax(newUrl, "post", hSuccess, hError, payload);
    }

    function handleSearchAction(value) {
        if (!value) {
            setTableData([...rmDataRows]);
            return;
        }
        const selected = rmDataRows.filter((row) => {
            let rowMatched = false;
            let keys = Object.keys(row);

            for (let k = 0; k < keys.length; k++) {
                rowMatched = !row[keys[k]]
                    ? false
                    : row?.[keys?.[k]] &&
                    row?.[keys?.[k]]
                        .toString()
                        .toLowerCase()
                        ?.indexOf(value?.toLowerCase()) != -1;

                if (rowMatched) break;
            }
            return rowMatched;
        });

        setTableData([...selected]);
    }
}

export default WorkflowHome;