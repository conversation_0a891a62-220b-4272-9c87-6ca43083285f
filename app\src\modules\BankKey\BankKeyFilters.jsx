import React, { useState, useEffect } from "react";
import {
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Checkbox,
  IconButton,
  InputAdornment,
  Select,
  FormControl,
  MenuItem,
  Button,
  useTheme,
} from "@mui/material";
import { useDispatch } from "react-redux";
import styled from "@emotion/styled";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ClearIcon from "@mui/icons-material/Clear";
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import ReusablePreset from "@components/Common/ReusablePresetFilter";
import {
  button_Marginleft,
  button_Primary,
  container_filter,
  font_Small,
} from "@components/Common/commonStyles";
import { commonFilterClear, commonFilterUpdate } from "@app/commonFilterSlice";
import useLang from "@hooks/useLang";
import { colors } from "@constant/colors";
import { ERROR_MESSAGES, MODULE,MODULE_MAP, SEARCH_FIELD_TYPES } from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import { destination_BankKey } from "../../../src/destinationVariables";
import { useSelector } from "react-redux";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import useLogger from "@hooks/useLogger";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor:theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: `${theme.palette.primary.light}20`,
  },
}));

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',
});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));

const BankKeyFilters = ({ 
  searchParameters, 
  onSearch, 
  onClear, 
  filterFieldData, 
  setFilterFieldData,
  items 
}) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const { customError } = useLogger();
  const { t } = useLang();
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [options, setOptions] = useState([]);
  const [selected, setSelected] = useState({});
  const [loading, setLoading] = useState({});
  const [matInputValue, setMatInputValue] = useState("");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [timerId, setTimerId] = useState(null);
  const bkSearchForm = useSelector((state) => state.commonFilter["BankKey"]);
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };

  const handleClear = () => {
    dispatch(commonFilterClear({ module: MODULE_MAP?.BK }));
    setFilterFieldData((prevState) => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach((key) => {
        updatedState[key] = { code: "", desc: "" };
      });
      return updatedState;
    });
    setSelectedOptions([]);
    onClear();
  };
  const handleChange = (key, value) => {
    const valueArray = key === SEARCH_FIELD_TYPES?.BANKCTRY
      ? (value ? [value] : [])
      : (Array.isArray(value) ? value : []);

    setSelected((prev) => ({
      ...prev,
      [key]: valueArray,
    }));

    let tempFilterData = {
      ...bkSearchForm,
      [key]: valueArray.map((item) => item?.code).join('$^$'),
    };

    dispatch(
      commonFilterUpdate({
        module: MODULE?.BK,
        filterData: tempFilterData,
      })
    );
  };
  const getMaterialSearch = (fieldKey, endpoint, payloadKey, inputValue) => {
    setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: true }))

    const hSuccess = (data) => {
      setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: false }));
      setOptions((prev) => ({ ...prev, [fieldKey]: data?.body || [] }));
    };

    const hError = () => {
      setIsDropDownLoading((prev) => ({ ...prev, [fieldKey]: false }));
    };

    // POST payload with input value
    const payload = { [payloadKey]: inputValue,
      top:100,
      skip:0
     };

    doAjax(
      `/${destination_BankKey}/data/${endpoint}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleMatInputChange = (e, fieldKey) => {
    const inputValue = e.target.value;
    setMatInputValue(inputValue);
    const config = SearchMapOption.find((item) => item.key === fieldKey);
    const { endpoint, payloadKey } = config;

    if (timerId) clearTimeout(timerId);

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(fieldKey, endpoint, payloadKey, inputValue);
      }, 200);
      setTimerId(newTimerId);
    }
  };
  const OptionData = (endpoint, jsonName, payload = {}) => {
    const hSuccess = (data) => {
      setOptions((prev) => ({
        ...prev,
        [jsonName]: data?.body || [],
      }));
    };

    const hError = () => {
      customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
    };
    doAjax(
      `/${destination_BankKey}/data/${endpoint}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const renderFilterField = (item, index) => {
    const fieldKey = item?.MDG_MAT_JSON_FIELD_NAME;
    const fieldLabel = t(item?.MDG_MAT_UI_FIELD_NAME);

    const fieldProps = {
    matGroup: options[fieldKey] || [],
    selectedMaterialGroup: selected[fieldKey] || [],
    setSelectedMaterialGroup: (val) => handleChange(fieldKey, val),
    placeholder: `SELECT ${fieldLabel}`,
    isDropDownLoading: loading[fieldKey],
    minWidth: "90%",
    onInputChange: (e) => handleMatInputChange(e, fieldKey),
  };
  const dropdownMap = {
    [SEARCH_FIELD_TYPES.BANKCTRY]: () => (
      <SingleSelectDropdown
        options={options[fieldKey] || []}
        value={selected[fieldKey]?.[0] || null}
        onChange={(val) => handleChange(fieldKey, val)}
        placeholder={`SELECT ${fieldLabel}`}
        disabled={loading[fieldKey]}
        isLoading={loading[fieldKey]}
        minWidth="90%"
      />
    ),
    [SEARCH_FIELD_TYPES.REGION]: () => <LargeDropdown {...fieldProps} />,
    default: () => <MaterialDropdown {...fieldProps} />,
  };

  const DropdownComponent = dropdownMap[fieldKey] || dropdownMap.default;

     return (
    <Grid item md={2} key={index}>
      <LabelTypography sx={font_Small}>
        {fieldLabel}
        <span style={{ color: colors?.error?.dark }}>*</span>
      </LabelTypography>
      <FormControl size="small" fullWidth>
        {DropdownComponent()}
      </FormControl>
    </Grid>
  );
};
  const SearchMapOption = [
    { key: SEARCH_FIELD_TYPES?.REGION, endpoint: "getRegionBasedOnCountry", dependsOn: SEARCH_FIELD_TYPES?.BANKCTRY },
    { key: SEARCH_FIELD_TYPES?.BANKKEYY, endpoint: "getSearchParamsBankKey", payloadKey: "bankKey" },
    { key: SEARCH_FIELD_TYPES?.BANKNO, endpoint: "getSearchParamsBankNumber", payloadKey: "bankNumber" },
    { key: SEARCH_FIELD_TYPES?.CITY, endpoint: "getSearchParamsCity", payloadKey: "city"},
    { key: SEARCH_FIELD_TYPES?.STREETLNG, endpoint: "getSearchParamsStreet", payloadKey: "street" },
    { key: SEARCH_FIELD_TYPES?.BANKBRANCH, endpoint: "getSearchParamsBankBranch", payloadKey: "bankBranch" },
    { key: SEARCH_FIELD_TYPES?.BANKCTRY, endpoint: "postCountry" },
    { key: SEARCH_FIELD_TYPES?.BANKNAME, endpoint: "getSearchParamsBankName", payloadKey: "bankName" },
  ];
  useEffect(() => {
  SearchMapOption.forEach(({ key, endpoint, dependsOn, payloadKey }) => {
    if (payloadKey) return;
    if (dependsOn) {
      const depValue = selected[dependsOn] || [];
      if (depValue.length) {
        const bankCtry = depValue[0]?.code?.trim() || "";
        if (bankCtry) {
          OptionData(endpoint, key, { bankCtry });
        }
      }
    } else {
      OptionData(endpoint, key);
    }
  });
}, [selected]);


  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={true} className="filterBK">
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem',color: theme.palette.primary.dark}} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color:theme.palette.primary.dark }} />
            <Typography
              sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: theme.palette.primary.dark,
              }}
            >
              {t("Filter Bank Key")}
            </Typography>
          </StyledAccordionSummary>
          
          <AccordionDetails sx={{ padding: "1rem 1rem 0.5rem" }}>
            <FilterContainer container>
              <Grid
                container
                rowSpacing={1}
                spacing={2}
                alignItems="center"
                sx={{ padding: "0rem 1rem 0.5rem" }}
              >
                {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                  .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                  .map((item, index) => {
                    const fieldType = item?.MDG_MAT_JSON_FIELD_NAME;
                    
                    if ([
                      SEARCH_FIELD_TYPES.REGION,
                      SEARCH_FIELD_TYPES.BANKKEYY,
                      SEARCH_FIELD_TYPES.BANKNO,
                      SEARCH_FIELD_TYPES.CITY,
                      SEARCH_FIELD_TYPES.STREETLNG,
                      SEARCH_FIELD_TYPES.BANKBRANCH,
                      SEARCH_FIELD_TYPES.BANKCTRY,
                      SEARCH_FIELD_TYPES.BANKNAME
                    ].includes(fieldType)) {
                      return renderFilterField(item, index);
                    }
                    return null;
                  })
                }
                
                <Grid item md={2}>
                  <LabelTypography sx={font_Small}>{t("Add New Filters")}</LabelTypography>
                  <FormControl sx={{ width: "100%" }}>
                    <Select
                      sx={{
                        font_Small,
                        fontSize: "12px",
                        width: "100%",
                      }}
                      size="small"
                      multiple
                      limitTags={2}
                      value={selectedOptions}
                      onChange={(e) => setSelectedOptions(e.target.value)}
                      renderValue={(selected) => selected.join(", ")}
                      MenuProps={MenuProps}
                      endAdornment={
                        selectedOptions.length > 0 && (
                          <InputAdornment position="end" sx={{ marginRight: '10px' }}>
                            <IconButton
                              size="small"
                              onClick={() => setSelectedOptions([])}
                              aria-label="Clear selections"
                            >
                              <ClearIcon />
                            </IconButton>
                          </InputAdornment>
                        )
                      }
                    >
                      {items?.map((option) => (
                        <MenuItem key={option.title} value={option.title}>
                          <Checkbox
                            checked={selectedOptions.indexOf(option.title) > -1}
                          />
                          {option.title}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
              
              <Grid
                container
                sx={{
                  flexDirection: "row",
                  padding: "0rem 1rem 0.5rem",
                }}
                gap={1}
              >
                {selectedOptions.map((option, i) => (
                  <Grid item md={2} key={i}>
                    <LabelTypography sx={{ fontSize: "12px" }}>
                      {t(option)}
                    </LabelTypography>
                  </Grid>
                ))}
              </Grid>
            </FilterContainer>

            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: '1rem' }} />}
                onClick={handleClear}
                
              >
                {t("Clear")}
              </ActionButton>
              
              <Grid sx={{ ...button_Marginleft }}>
                <ReusablePreset
                  moduleName={"BankKey"}
                  handleSearch={onSearch}
                />
              </Grid>
              
              <ActionButton
                variant="contained"
                size="small"
                startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                sx={{ ...button_Primary, ...button_Marginleft }}
                onClick={onSearch}
              >
                {t("Search")}
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default BankKeyFilters;