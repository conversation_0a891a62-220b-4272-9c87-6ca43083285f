import { useState,useCallback } from "react";
import { useDispatch,useSelector } from "react-redux";
import { setUserDetails, setRoles } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { ERROR_MESSAGES, LOADING_MESSAGE } from "../constant/enum";
import { destination_MaterialMgmt, destination_IWA_NEW } from "../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { getUrlByName } from "@utils/dynamicUrl";
const {VITE_APP_TOKEN} = import.meta.env;

const combineUserData = (userRes, rolesRes) => {
  const u = userRes?.data;
  const user = {
    id: u.userDetails.masterUserId,
    user_id: u.userDetails.businessEmailId,
    firstName: u.userDetails.firstName,
    lastName: u.userDetails.lastName,
    emailId: u.userDetails.businessEmailId,
    displayName: `${u.userDetails.firstName} ${u.userDetails.lastName}`,
    userName: u.userDetails.userName,
  };

  const roles = rolesRes?.data?.map((r) => r.roleName);

  return {
    ...user,
    roles,
  };
};

const useUserBootstrap = () => {
  const fallbackUser = {
    id: "P001344",
    user_id: "<EMAIL>",
    firstName: "Suvendu",
    lastName: "Samantaray",
    emailId: "<EMAIL>",
    displayName: "Suvendu Samantaray",
    userName: "INC02117",
    roles: [""],
  };
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [moduleAccessStatus, setModuleAccessStatus] = useState("loading");
  const { customError } = useLogger();
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [isLoggedOut,setIsLoggedOut] = useState(false)
  const isLocalEnv = ["localhost", "127.0.0.1"].includes(applicationConfig.environment)

  const baseUrl = isLocalEnv ? getUrlByName(destination_MaterialMgmt) : getUrlByName(destination_IWA_NEW)
  const url_user = isLocalEnv ? `${baseUrl}${END_POINTS.API.USER_DETAILS}` : `${baseUrl}${END_POINTS.API.USER_DETAILS_PROD}`

  const fetchAndDispatchUser = useCallback(async () => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_USER_ACCESS);
    if (isLocalEnv) {
      dispatch(setUserDetails(fallbackUser));
      dispatch(setRoles(fallbackUser.roles || []));
      setModuleAccessStatus(true);
      setLoaderMessage("");
      setLoading(false);
    }

    let userRes = null;
    let rolesRes = null;

    try {
      await new Promise((resolve, reject) => {
        fetch(url_user, {
          method: "GET",
          headers: {
            ...(isLocalEnv && { authorization: `Bearer ${VITE_APP_TOKEN}` })
          },
          mode: "cors"
        })
          .then(async (response) => {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('text/html')) {
              setIsLoggedOut(true);
              return
            }
            if (contentType && contentType.includes('application/json')) {
              const res = await response.json();
              userRes = res;
              resolve();
            }
          })
          .catch((error) => {
            if (isLocalEnv) {
              resolve();
            } else {
              reject(error);
            }
          });
      });

      const email = userRes?.data?.userDetails?.businessEmailId;
      const version = userRes?.data?.version ?? 1;
      const url_roles = isLocalEnv ? 
        `/${destination_MaterialMgmt}${END_POINTS.API.ROLES}?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG` : 
        `/${destination_IWA_NEW}${END_POINTS.API.ROLES_PROD}?businessEmailId=${encodeURIComponent(email)}&userVersionNo=${version}&includeRoleDetails=false&iwaAppIds=MDG`;
      await new Promise((resolve, reject) => {
        doAjax(
          url_roles,
          "get",
          (res) => {
            rolesRes = res;
            resolve();
          },
          isLocalEnv
            ? () => {
                resolve();
              }
            : reject
        );
      });

      const finalUserData = combineUserData(userRes, rolesRes);
      dispatch(setUserDetails(finalUserData));
      dispatch(setRoles(finalUserData.roles));
      setModuleAccessStatus(true);
    } catch (error) {
      customError(ERROR_MESSAGES?.ERROR_FETCHING_USER, error);
      if (!isLocalEnv) {     
        setModuleAccessStatus(false);
        window.location.href = "/do/logout"
      }
      
    } finally {
      if (!isLocalEnv) {
        setLoaderMessage("");
        setLoading(false);
      }
    }
  }, []);

  return {
    fetchAndDispatchUser,
    loading,
    loaderMessage,
    moduleAccessStatus,
    isLoggedOut
  };
};

export default useUserBootstrap;