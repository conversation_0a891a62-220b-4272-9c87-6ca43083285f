import { useState, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { REQUEST_TYPE, LOCAL_STORAGE_KEYS, ERROR_MESSAGES, API_CODE } from "@constant/enum";
import { getLocalStorage, } from "@helper/helper";
import { setDataLoading, setDisplayPayload, setDynamicKeyValue, resetPayloadData, setFCRows } from "@app/payloadslice";
import { updateCurrentCount, updateTotalCount, updateNextButtonStatus } from "@app/paginationSlice";
import useMaterialChangeFieldConfig from "@hooks/useMaterialChangeFieldConfig";
import useChangeMaterialRows from "@hooks/useChangeMaterialRows";
import useFinanceCostingRows from "@hooks/useFinanceCostingRows";
import useLogger from "@hooks/useLogger";
import { setMaterialRows } from "@app/requestDataSlice";
import { useSnackbar } from "@hooks/useSnackbar";
import { destination_ArticleMgmt } from '@destinations';
import { doAjax } from '@components/Common/fetchService';
import { transformApiResponseToReduxPayload } from '../../../functions';

const useDisplayData = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const { getChangeTemplate } = useMaterialChangeFieldConfig();
  const { fetchDisplayDataRows } = useChangeMaterialRows();
  const { createFCRows } = useFinanceCostingRows();
  const { customError } = useLogger();
  const { showSnackbar } = useSnackbar();

  const getDisplayData = useCallback(async (requestId, RequestType, reqBench, taskData, rowData) => {
    return new Promise((resolve, reject) => {
      setLoading(true);
      setError(null);
      dispatch(setDataLoading(true));

      const idToUse = requestId;
      const savedTask = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK, true, {});
      const effectiveRequestType = RequestType || taskData?.ATTRIBUTE_2 || savedTask?.ATTRIBUTE_2;
      let payload = reqBench
        ? {
          massCreationId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse : "") : "",
          massChildCreationId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) ? idToUse : "") : "",
          massChangeId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse : "") : "",
          massExtendId: !rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse : "") : "",
          massSchedulingId: !rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse : "") : "",
          screenName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : effectiveRequestType,
          dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "MDG_MAT_MATERIAL_FIELD_CONFIG",
          version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "v2",
          page: 0,
          size: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? 100 : (effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
          sort: "",
          ApproverGroup: taskData?.ATTRIBUTE_5,
          Region: "",
          massChildSchedulingId: rowData?.isBifurcated ? (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? idToUse : "") : "",
          massChildExtendId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? idToUse : "") : "",
          massChildChangeId: rowData?.isBifurcated ? ((effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? idToUse : "") : "",
        }
        : {
          massCreationId: "",
          massChangeId: "",
          massSchedulingId: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING || effectiveRequestType === "Finance Costing" ? idToUse : "",
          massExtendId: "",
          screenName: effectiveRequestType === "MASS_CREATE" || effectiveRequestType === "Mass Create" || effectiveRequestType === REQUEST_TYPE.CREATE ? REQUEST_TYPE.CREATE : effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : REQUEST_TYPE.CHANGE,
          dtName: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "MDG_MAT_MATERIAL_FIELD_CONFIG",
          version: effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING ? "" : "v2",
          page: 0,
          size: (effectiveRequestType === REQUEST_TYPE.FINANCE_COSTING || RequestType === REQUEST_TYPE.FINANCE_COSTING) ? 100 : (RequestType === REQUEST_TYPE.CHANGE || RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? 10 : 50,
          sort: "",
          ApproverGroup: taskData?.ATTRIBUTE_5,
          Region: "",
          massChildCreationId: effectiveRequestType === "MASS_CREATE" || effectiveRequestType === "Mass Create" || effectiveRequestType === REQUEST_TYPE.CREATE || effectiveRequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? idToUse : "",
          massChildSchedulingId: "",
          massChildExtendId: effectiveRequestType === REQUEST_TYPE.EXTEND || effectiveRequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD ? idToUse : "",
          massChildChangeId: effectiveRequestType === "MASS_CHANGE" || effectiveRequestType === "Mass Change" || effectiveRequestType === REQUEST_TYPE.CHANGE || effectiveRequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ? idToUse : "",
        };

      const hSuccess = async (data) => {
        try {
          if (data?.statusCode === API_CODE.STATUS_200) {
            dispatch(setDataLoading(false));
            setLoading(false);
            const apiResponse = data.body;
            dispatch(updateTotalCount(data?.totalElements));

            if (data?.totalPages === 1 || data?.currentPage + 1 === data?.totalPages) {
              dispatch(updateCurrentCount(data?.totalElements));
              dispatch(updateNextButtonStatus(true));
            } else {
              dispatch(updateCurrentCount((data?.currentPage + 1) * data?.pageSize));
            }

            if (taskData?.ATTRIBUTE_2 === REQUEST_TYPE.CHANGE || taskData?.ATTRIBUTE_2 === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CHANGE) {
              dispatch(
                setDynamicKeyValue({
                  keyName: "requestHeaderData",
                  data: apiResponse[0]?.Torequestheaderdata,
                })
              );
              getChangeTemplate(apiResponse[0]?.Torequestheaderdata || "", apiResponse[0] || {});
              fetchDisplayDataRows(apiResponse);
              resolve(data);
              return;
            }

            if (RequestType === REQUEST_TYPE.FINANCE_COSTING || taskData?.ATTRIBUTE_2 === REQUEST_TYPE.FINANCE_COSTING) {
              const payloadData = {
                ReqCreatedBy: apiResponse[0]?.Torequestheaderdata?.ReqCreatedBy,
                RequestStatus: apiResponse[0]?.Torequestheaderdata?.RequestStatus,
                Region: apiResponse[0]?.Torequestheaderdata?.Region,
                ReqCreatedOn: new Date().toISOString(),
                ReqUpdatedOn: new Date().toISOString(),
                RequestType: apiResponse[0]?.Torequestheaderdata?.RequestType,
                RequestDesc: apiResponse[0]?.Torequestheaderdata?.RequestDesc,
                RequestPriority: apiResponse[0]?.Torequestheaderdata?.RequestPriority,
                LeadingCat: apiResponse[0]?.Torequestheaderdata?.LeadingCat,
                RequestId: apiResponse[0]?.Torequestheaderdata?.RequestId,
                TemplateName: apiResponse[0]?.Torequestheaderdata?.TemplateName,
              };

              dispatch(resetPayloadData({ data: payloadData }));
              const fcRows = await createFCRows(apiResponse);
              dispatch(setFCRows(fcRows));
              resolve(data);
              return;
            }

            const transformedPayload = await transformApiResponseToReduxPayload(apiResponse);
            await dispatch(setDisplayPayload({ data: transformedPayload?.payload }));

            const numericKeys = Object.keys(transformedPayload?.payload).filter((key) => !isNaN(Number(key)));
            const extractedData = {};
            numericKeys.forEach((key) => {
              extractedData[key] = transformedPayload?.payload[key];
            });
            dispatch(setMaterialRows(Object.values(extractedData)?.map((item) => item.headerData)));
            resolve(data);
          } else {
            showSnackbar(data?.message, 'error');
          }
        }
        catch (error) {
          customError(ERROR_MESSAGES.ERROR_GET_DISPLAY_DATA);
          setError(error);
          setLoading(false);
          reject(error);
        }
      };

      const hError = (error) => {
        customError(ERROR_MESSAGES.ERROR_FETCHING_DATA);
        setError(error);
        setLoading(false);
        dispatch(setDataLoading(false));
        reject(error);
      };

      doAjax(`/${destination_ArticleMgmt}/data/displayMassMaterialDTO`, "post", hSuccess, hError, payload);
    });
  }, [dispatch]);

  return {
    getDisplayData,
    loading,
    error,
    clearError: () => setError(null)
  };
};

export default useDisplayData;