import { useEffect, useRef, useState } from "react";
import SearchBar from "../common/SearchBar";
import {
  List,
  ListItem,
  Paper,
  Tab,
  Tabs,
  ToggleButton,
  ToggleButtonGroup,
} from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import DescriptionIcon from "@mui/icons-material/Description";
import useLogger from "@hooks/useLogger";
import {
  Box,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Stack,
  TablePagination,
  Tooltip,
  Typography,
} from "@mui/material";
import {
  container_table,
  outerContainer_Information,
  outermostContainer_Information,
  outermostContainer,
} from "../common/commonStyles";
import {
  ArrowCircleLeftOutlined,
  IosShare,
  Refresh,
} from "@mui/icons-material";
import { doAjax } from "../common/fetchService";
import { useDispatch } from "react-redux";
import { commonSearchBarClear } from "../../app/commonSearchBarSlice";
import { saveExcel } from "../../functions";
import LoadingComponent from "../common/LoadingComponent";
import { useLocation, useNavigate } from "react-router-dom";
import ErrorExcelTable from "./ErrorExcelTable";
import { colors } from "@constant/colors";
import { ERROR_FIELD_MAP, MODULE_MAP, REQUEST_STATUS } from "@constant/enum";
import { filterNavigation } from "@helper/helper";
import ReusableDataTable from "../Common/ReusableTable";
import { useTabularData } from "@modules/modulesHooks/hierarchyHooks/useHierarchyErrorHistory";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { useSelector } from "react-redux";

const ErrorHistory = ({ isHierarchyCheck = false, module }) => {
  const CHARACTER_LIMIT = 30;

  const renderFormattedValue = (value) => {
    if (!value || value.trim() === "") return "-";

    const isLongText = value.length > CHARACTER_LIMIT;
    const truncated = value.slice(0, CHARACTER_LIMIT) + "...";
    const isCommaSeparated = value.includes(",");

    // For comma-separated values: render list
    if (isCommaSeparated) {
      const items = value
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean);

      return (
        <List dense>
          {items.map((item, idx) => (
            <ListItem
              key={idx}
              sx={{ display: "list-item", p: 0, fontSize: "0.875rem", fontWeight: 800 }}
            >
              {item}
            </ListItem>
          ))}
        </List>
      );
    }

    return (
      <Stack direction="row" alignItems="center" spacing={1}>
        <Typography
          sx={{
            fontWeight: 700,
            fontSize: "0.875rem",
            color: colors.black.main,
            whiteSpace: "pre-line",
          }}
        >
          {isLongText ? truncated : value}
        </Typography>
        {isLongText && (
          <Tooltip title={value} placement="top">
            <IconButton size="small">
              <InfoOutlinedIcon sx={{ fontSize: "1rem" }} />
            </IconButton>
          </Tooltip>
        )}
      </Stack>
    );
  };
  const { customError } = useLogger();
  const getStatusColor = (status) => {
    return status === "Resolved"
      ? "success"
      : status === "Pending"
        ? "warning"
        : "error";
  };

  const formatDate = (timestamp) => new Date(timestamp).toLocaleDateString();
  const formatTime = (timestamp) => new Date(timestamp).toLocaleTimeString();
  const [errorType, setErrorType] = useState("sap");

  const dispatch = useDispatch();
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const RequestID = urlSearchParams.get("RequestId");
  const navigate = useNavigate();
  const [massLogData, setMassLogData] = useState([]);
  const [massSearchData, setMassSearchData] = useState([]);
  const [tabsData, setTabsData] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
const display =
location.state?.display ??
("childRequest" in (location.state ?? {}) ? true :
"isChildRequest" in (location.state ?? {}) ? false :
false);
  const [errorData, setErrorData] = useState([]);
  const [triggerExcelAction, setTriggerExcelAction] = useState(false);

  const task = useSelector((state) => state?.userManagement.taskData);

  const isHierarchyModule =
    location.state?.isHierarchyCheck ?? isHierarchyCheck;
  const childRequest =
    location.state?.childRequest === true||location.state?.isChildRequest === true || Object.keys(task).length !== 0
      ? "TRUE"
      : "FALSE";
  const moduleName = location.state?.module || module;
  const {
    destination,
    errorHistoryUrl,
    downloadErrorHistoryParentUrl,
    downloadErrorHistoryChildUrl,
  } = filterNavigation(moduleName);

  const { tabs, currentTabData } = useTabularData(tabsData, activeTab);
  const handleMassHistory = () => {
    const payload = {
      isChild: childRequest,
      requestId: RequestID,
      isHierarchyGroup:
        moduleName === MODULE_MAP.PCG ||
        moduleName === MODULE_MAP.CCG ||
        moduleName === MODULE_MAP.CEG
          ? "TRUE"
          : "FALSE",
    };

    const hSuccess = (data) => {
      if (isHierarchyModule) {
        setTabsData(data) || [];
      } else {
        setMassLogData(data?.body);
        setMassSearchData(data?.body);
        setPageCount(data?.count ?? data?.body?.length);
      }

      setLoading(false);
      dispatch(commonSearchBarClear({ module: "ErrorHistory" }));
    };

    const hError = (error) => {
      customError(error);
      setLoading(false);
    };

    setLoading(true);

    doAjax(
      `/${destination}/${errorHistoryUrl}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const [showMore, setShowMore] = useState({});

  const toggleShowMore = (key) => {
    setShowMore((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  useEffect(() => {
    handleMassHistory();
  }, [moduleName]);

  const fieldConfig = ERROR_FIELD_MAP[moduleName] || [];
  const renderValue = (val) => {
    const key = val?.materialNo || val?.profitCenter;
    const isSuccess = fieldConfig.every(
      (err) =>
        err.value === REQUEST_STATUS.VALIDATED_SUCCESS ||
        err.value === REQUEST_STATUS.SYNDICATED_IN_SAP
    );

    const hasAtLeastOne = fieldConfig.some((err) => !!err.value);

    return (
      <Card
        variant="outlined"
        sx={{
          width: "100%",
          minHeight: 100,
          backgroundColor: `${colors.basic.lighterGrey}`,
          borderRadius: 2,
          boxShadow: "0px 2px 6px rgba(0, 0, 0, 0.08)",
          padding: 1,
          margin: "10px 0",
          overflow: "hidden",
        }}
      >
        <CardContent sx={{ padding: 1 }}>
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={1.5}
          >
            <Typography
              fontWeight="bold"
              color={isSuccess ? "success.main" : "error.main"}
              sx={{ fontSize: "0.9rem" }}
            >
              {moduleName} No. {key}
            </Typography>
            <Chip
              label={isSuccess ? "Success" : "Error"}
              color={isSuccess ? "success" : "error"}
              size="small"
              sx={{ fontWeight: "medium", height: "21px", fontSize: "0.65rem" }}
            />
          </Box>

          {hasAtLeastOne ? (
            <Box>
              {fieldConfig.map((err, idx) => {
                if (!err.value) return null;
                const isHTML = /<[a-z][\s\S]*>/i.test(err.value);

                return (
                  <Box key={idx} sx={{ mb: 1 }}>
                    <Typography
                      variant="subtitle2"
                      sx={{ fontSize: "0.72rem", fontWeight: 600, mb: 0.5 }}
                    >
                      {err.label}
                    </Typography>
                    {isHTML ? (
                      <div
                        dangerouslySetInnerHTML={{ __html: err.value }}
                        style={{
                          fontSize: "0.75rem",
                          lineHeight: 1.1,
                          wordBreak: "break-word",
                          overflowWrap: "break-word",
                        }}
                      />
                    ) : (
                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          fontSize: "0.75rem",
                          lineHeight: 1.1,
                          wordBreak: "break-word",
                          overflowWrap: "break-word",
                        }}
                      >
                        {err.value}
                      </Typography>
                    )}
                  </Box>
                );
              })}
            </Box>
          ) : (
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: "0.875rem", mt: 1 }}
            >
              No SAP Error Found!
            </Typography>
          )}
        </CardContent>
      </Card>
    );
  };

  const handleSearchAction = (value) => {
    if (Boolean(value)) {
      const searchTerm = value.toString().toLowerCase();
      const filterData = massLogData.filter((val) =>
        val?.materialNo?.toString().toLowerCase().includes(searchTerm)
      );
      setMassSearchData(filterData);
    } else {
      handleMassHistory();
    }
  };
  const columns = [
    {
      field: "sapMessage",
      headerName: "Error Log",

      align: "center",
      headerAlign: "center",
      flex: 3,
      renderCell: (params) => (
        <Box sx={{ width: "100%" }}>{renderValue(params?.row)}</Box>
      ),
    },
  ];
  const handleChangeRowsPerPage = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPageNumber(0);
  };
  const handleChangePage = (event, newPage) => {
    setPageNumber(newPage);
  };
  const [pageCount, setPageCount] = useState(0);

  const [pageNumber, setPageNumber] = useState(0);
  const [pageSize, setPageSize] = useState(10);

  const startIndex = pageNumber * pageSize;
  const endIndex = startIndex + pageSize;
  const displayedData = massSearchData?.slice(startIndex, endIndex);

  const moduleFieldKeyMap = {
    "Profit Center": "profitCenter",
    "Cost Center": "costCenter",
    "General Ledger": "glAccount",
    "Bank Key": "bankKey",
    "Material":"materialNo",
    "Internal Order":"InternalOrderErrorId",
  };
  const fieldKey = moduleFieldKeyMap[moduleName];


  const hasNoErrorData = () => {
    return massSearchData.every(
      (item) => !item?.sapMessage && !item?.materialDuplicateError
    );
  };

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      const columnsLocal = [
        {
          field: fieldKey,
          headerName: `${moduleName} Number`,
        },
        ...fieldConfig
        .filter(({ label, key }) => label.trim() !== "-" && key.trim() !== "-") 
        .map(({ label, key }) => (
        {
          field: key,
          headerName: label,
        })),
      ]
        columnsLocal.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field, width: item.width });
        }
      });
      const presentDate = new Date();
      saveExcel({
        fileName: `${moduleName} Error Logsheet`,
        columns: excelColumns,
        rows: massLogData,
      });
    },
     button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const excelTableRef = useRef();
  const handleChange = (event, newErrorType) => {

    if (newErrorType !== null) {
      setErrorType(newErrorType);
      
    }
    
  };
  useEffect(() => {
  if (errorType === "excel" ) {
    excelTableRef.current.triggerExcelErrorAction();
  }
}, [errorType]);
const handleClick = () => {

    if (errorType === "sap") {
    handleMassHistory();
  } else if (errorType === "excel") {
    excelTableRef.current?.triggerExcelErrorAction();
  }
  };

  const handleExport = () => {
    if (errorType === "sap") {
    functions_ExportAsExcel.convertJsonToExcel();
  } else if (errorType === "excel") {
    excelTableRef.current?.triggerExcelExportAction();
  }
}

  return (
    <div id={"container_outermost"}>
      <div
        className="purchaseOrder"
        style={{
          ...outermostContainer,
          backgroundColor: `${colors.primary.veryLight}`,
        }}
      >
        <Stack spacing={1}>
          <>
            <Grid
              container
              sx={outermostContainer_Information}
              alignItems="center"
            >
              <Grid
                item
                md={6}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  ...outerContainer_Information,
                }}
              >
                <Stack direction="row" spacing={2} alignItems="center">
                  {display && (
                    <IconButton
                      onClick={() => {
                        navigate(-1);
                      }}
                      color="primary"
                      aria-label="upload picture"
                      component="label"
                    >
                      <ArrowCircleLeftOutlined
                        sx={{ fontSize: "25px", color: "#000000" }}
                      />
                    </IconButton>
                  )}
                  <Stack>
                    <Typography variant="h3">
                      <strong>Error History - {RequestID}</strong>
                    </Typography>
                    <Typography variant="body2" color="#777">
                      This view displays the error history of a Request
                    </Typography>
                  </Stack>
                </Stack>
              </Grid>

              {true &&(
                <Grid
                  item
                  md={6}
                  sx={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center",
                  }}
                >
                  <Grid
                    container
                    direction="row"
                    justifyContent="flex-end"
                    alignItems="center"
                    spacing={0}
                    marginBottom={"10px"}
                  >
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Reload"
                      placement="bottom"
                      arrow
                    >
                      <IconButton onClick={handleClick}>
                        <Refresh />
                      </IconButton>
                    </Tooltip>
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Export Table"
                      placement="bottom"
                      arrow
                    >
                      <IconButton onClick={handleExport}>
                        <IosShare />
                      </IconButton>
                    </Tooltip>
                    <Tooltip
                      slotProps={{ tooltip: { sx: { fontSize: "0.9em" } } }}
                      title="Search"
                    >
                      <SearchBar
                        title="Type Material Number to know its status"
                        handleSearchAction={(e) => handleSearchAction(e)}
                        keyName="errorHistorySearch"
                        message={"Search"}
                        module="ErrorHistory"
                        clearSearchBar={() => handleMassHistory()}
                      />
                    </Tooltip>
                  </Grid>
                </Grid>
              )}
            </Grid>

            <Stack
              sx={{
                padding: "16px",
                pb: "0 !important",
                width: "100%",
                maxWidth: "100%",
                ...container_table,
              }}
            >
              <ToggleButtonGroup
                value={errorType}
                exclusive
                onChange={handleChange}
                sx={{
                  width: "40%",
                  "& .MuiToggleButton-root": {
                    borderRadius: "0 !important",
                  },
                  "& .MuiToggleButton-root:first-of-type": {
                    borderTopLeftRadius: "8px !important",
                    borderBottomLeftRadius: "8px !important",
                  },
                  "& .MuiToggleButton-root:last-of-type": {
                    borderTopRightRadius: "8px !important",
                    borderBottomRightRadius: "8px !important",
                  },
                }}
              >
                <ToggleButton
                  value="sap"
                  sx={{
                    flex: 1,
                    p: 1,
                    color:
                      errorType === "sap"
                        ? `${colors.reportTile.blue}`
                        : `${colors.primary.grey}`,
                    backgroundColor:
                      errorType === "sap"
                        ? `${colors.reportTile.lightBlue}`
                        : "transparent",
                    "&:hover": {
                      backgroundColor: `${colors.reportTile.lightBlue}`,
                    },
                  }}
                >
                  <ErrorOutlineIcon sx={{ fontSize: 18, mr: 1 }} />
                  SAP/DB Error Log
                </ToggleButton>

                <ToggleButton
                  value="excel"
                  sx={{
                    flex: 1,
                    p: 1,
                    color:
                      errorType === "excel"
                        ? `${colors.reportTile.blue}`
                        : `${colors.primary.grey}`,
                    backgroundColor:
                      errorType === "excel"
                        ? `${colors.reportTile.lightBlue}`
                        : "transparent",
                    "&:hover": {
                      backgroundColor: `${colors.reportTile.lightBlue}`,
                    },
                  }}
                >
                  <DescriptionIcon sx={{ fontSize: 18, mr: 1 }} />
                  Excel Upload Error
                </ToggleButton>
              </ToggleButtonGroup>

              {isHierarchyModule && errorType === "sap" ? (
                <>
                  <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 3 }}>
                    <Tabs
                      value={activeTab}
                      onChange={(event, newValue) => setActiveTab(newValue)}
                      variant="scrollable"
                      scrollButtons="auto"
                      sx={{
                        "& .MuiTab-root": {
                          textTransform: "none",
                          fontWeight: "medium",
                        },
                      }}
                    >
                      {tabs.map((tab, index) => (
                        <Tab
                          key={tab}
                          label={tab}
                          id={`tab-${index}`}
                          aria-controls={`tabpanel-${index}`}
                        />
                      ))}
                    </Tabs>
                  </Box>

                  <ReusableDataTable
                    rows={currentTabData.rows}
                    columns={currentTabData.columns}
                    getRowIdValue={"id"}
                    autoHeight
                    scrollbarSize={10}
                    sx={{
                      "& .MuiDataGrid-row:hover": {
                        backgroundColor: "#EAE9FF40",
                      },
                      backgroundColor: "#fff",
                    }}
                  />
                </>
              ) : errorType === "sap" && massLogData?.length > 0 ? (
                <Box
                  id="container_outermost"
                  sx={{
                    backgroundColor: colors.basic.white,
                    borderRadius: 2,
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    mt: 2,
                    px: 2,
                    py: 1,
                  }}
                >
                  {displayedData.length > 0 ? (
                    // NOTE:use for error handling purpose
                    // hasNoErrorData() ? (
                    //   <Box sx={{ textAlign: "center", py: 4 }}>
                    //     <Typography
                    //       sx={{
                    //         fontSize: "0.875rem",
                    //         fontWeight: 500,
                    //         color: "#777",
                    //       }}
                    //     >
                    //       {ERROR_MESSAGES.NO_DATA_AVAILABLE}
                    //     </Typography>
                    //   </Box>
                    // )
                    // :
                    <Stack
                      spacing={2}
                      sx={{
                        maxHeight: "calc(100vh - 289px)",
                        overflowY: "auto",
                      }}
                    >
                      {displayedData.map((row, index) => (
                        <Paper
                          key={index}
                          elevation={2}
                          sx={{
                            p: 3,
                            borderRadius: 2,
                            backgroundColor: colors.basic.white,
                            position: "relative",
                            border: "1px solid #e0e0e0",
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "flex-start",
                              mb: 2,
                            }}
                          >
                            <Typography
                              variant="subtitle1"
                              sx={{
                                fontWeight: 700,
                                color: colors.blue.main,
                              }}
                            >
                              {moduleName} No. {row?.[fieldKey]}
                            </Typography>
                            </Box>
                            <Grid container spacing={2}>
                              {fieldConfig.map(({ label, key }, i) => {
                                const value = row[key]; // dynamic access based on key

                                return (
                                  <Grid item xs={4} key={i}>
                                    <Card
                                      variant="outlined"
                                      sx={{
                                        height: "100%",
                                        border: `3px solid ${value && value !== REQUEST_STATUS.VALIDATED_SUCCESS && value!==REQUEST_STATUS.SYNDICATED_IN_SAP
                                          ? colors.border.error
                                          : "#e0e0e0"
                                          }`,
                                      }}
                                    >
                                      <CardContent>
                                        <Typography
                                          sx={{
                                            fontWeight: 600,
                                            fontSize: "0.875rem",
                                            color: colors.blue.indigo,
                                          }}
                                        >
                                          {label}
                                        </Typography>
                                        <Typography
                                          sx={{
                                            fontWeight: 700,
                                            fontSize: "0.875rem",
                                            color: colors.black.main,
                                            whiteSpace: "pre-line",
                                          }}
                                        >
                                          {value !== null && value !== undefined && value !== ""
                                            ? value
                                            : "-"}
                                        </Typography>
                                      </CardContent>
                                    </Card>
                                  </Grid>
                                );
                              })}
                            </Grid>
                          </Paper>
                        ))}
                      </Stack>
                    )
                   : (
                    <Box sx={{ textAlign: "center", py: 4 }}>
                      <Typography
                        sx={{
                          fontSize: "0.875rem",
                          fontWeight: 500,
                          color: "#777",
                        }}
                      >
                        No data found
                      </Typography>
                    </Box>
                  )}

                  {/* Pagination */}
                  <Box
                    sx={{
                      mt: 2,
                      borderTop: "1px solid #e0e0e0",
                      backgroundColor: colors.basic.white,
                      p: "8px 16px",
                    }}
                  >
                    <TablePagination
                      component="div"
                      count={pageCount}
                      page={pageNumber}
                      onPageChange={handleChangePage}
                      rowsPerPage={pageSize}
                      onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                  </Box>
                </Box>
              ) : (
                errorType !== "excel" && (
                  <Card
                    sx={{
                      width: "100%",
                      mt: 2,
                      border: "1px solid #e0e0e0",
                      borderRadius: 2,
                      boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Stack spacing={2} alignItems="center">
                        <Typography
                          variant="h5"
                          sx={{ color: colors.black.light, fontWeight: 500 }}
                        >
                          No Data Found for this Request ID
                        </Typography>
                      </Stack>
                    </CardContent>
                  </Card>
                )
              )}
            {errorType === "excel" && <ErrorExcelTable module={moduleName} ref={excelTableRef} errorType={errorType} />}
            </Stack>

            {loading && <LoadingComponent />}
          </>
        </Stack>
      </div>
    </div>
  );
};

export default ErrorHistory;


