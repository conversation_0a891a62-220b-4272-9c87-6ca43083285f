import { useState, useEffect } from "react";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Modal from "@mui/material/Modal";
import { doAjax } from "../common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import moment from "moment";
import ChangeCircleOutlinedIcon from "@mui/icons-material/ChangeCircleOutlined";
import { useSelector } from "react-redux";
import { Grid, Stack, IconButton, Tooltip, Tabs, Tab } from "@mui/material";
import ReusableDataTable from "../Common/ReusableTable";
import CloseIcon from "@mui/icons-material/Close";
import { v4 as uuidv4 } from "uuid";
import ReusableIcon from "../Common/ReusableIcon";
import {
	iconButton_SpacingSmall,
} from "../Common/commonStyles";
import { CHANGE_LOG_TEMPS } from "../../constant/changeLogTemplates.js";
import { saveExcelMultiSheets, } from "../../functions";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import { extractDataBaedOnTemplateName, formatDateValue, getObjectValue, getSegregatedPart, mergeArrays } from "@helper/helper";
import { CHANGE_TEMPLATES_FIELD_IDENTIFICATION, TEMPLATE_NAME_MANIPULATION } from "@constant/changeTemplates";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";

const ChangeLog = ({ open, closeModal, requestId, requestType }) => {
	const { customError } = useLogger()
	const [loading, setloading] = useState(true);
	const [apiResponse, setApiResponse] = useState(null);
	const payloadData = useSelector((state) => state.payload.payloadData?.data || state.payload?.payloadData);
	const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
	const templateKey = payloadData?.TemplateName;
	const [tabData, setTabData] = useState(() => {
		const tabConfig = CHANGE_LOG_TEMPS[templateKey] || {};
		return Object.keys(tabConfig).map((tabName) => ({
			label: tabName,
			columns: tabConfig[tabName],
			rows: [],
		}));
	});
	const [selectedTab, setSelectedTab] = useState(() => {
		if (tabData.length > 0) {
			return { number: 0, label: tabData[0].label };
		}
		return { number: 0, label: "" };
	});

	const handleTabChange = (event, newValue) => {
		setSelectedTab({ number: newValue, label: tabData[newValue].label });
	};
	const style = {
		position: "absolute",
		top: "50%",
		left: "52%",
		transform: "translate(-50%, -50%)",
		width: "80%",
		height: "auto",
		bgcolor: "#fff",
		boxShadow: 4,
		p: 2,
	};

	const onClose = () => {
		closeModal(false)
	}

	useEffect(() => {
		const fetchChangeLogData = async () => {
			if (open && !apiResponse) {
				try {
					const result = await changelogget(requestId, requestType);
					setApiResponse(result);
				} catch (error) {
					customError("Error fetching changelog data:", error);
				}
			}
		};
		
		fetchChangeLogData();
	}, [open, requestId]);

	useEffect(() => {
		if (apiResponse && selectedTab) {
			try {
				setTabData((prevTabData) =>
					prevTabData?.map((tab) => {
						const templateData = getObjectValue(TEMPLATE_NAME_MANIPULATION, templateKey);
						const tabelName = typeof templateData === 'object' 
							? templateData[tab?.label]
							: templateData;
						const extraField = getObjectValue(CHANGE_TEMPLATES_FIELD_IDENTIFICATION, tabelName);
						const newRows = extractDataBaedOnTemplateName(apiResponse[tabelName], tabelName);
						return {
							...tab,
							rows: newRows?.map((row) => ({
								id: uuidv4(),
								...row,
								Material: getSegregatedPart(row?.ObjectNo, 1),
								SAPValue: formatDateValue(row?.SAPValue), 
								PreviousValue: formatDateValue(row?.PreviousValue),
								CurrentValue: formatDateValue(row?.CurrentValue),
								ChangedOn: formatDateValue(row?.ChangedOn),
								...(extraField?.length > 0 && {
									[extraField[0]]: getSegregatedPart(row?.ObjectNo, 2),
								}),
								...(extraField?.length > 1 && {
									[extraField[1]]: getSegregatedPart(row?.ObjectNo, 3),
								}),
							})),
						};
					})
				);
			} catch (error) {
				customError(ERROR_MESSAGES.CHANGE_LOG_MESSAGE, error);
			}
		}
	}, [apiResponse]);
	const changelogget = (requestId) => {
		setloading(true);
		const url = `/${destination_MaterialMgmt}/${END_POINTS?.CHG_DISPLAY_REQUESTOR.FETCH_CHANGELOG_DATA}`;
		let changeLogPayload = {
			requestId: dynamicData?.childRequestHeaderData?.ChildRequestId ? null : requestId,
			ChildRequestId:dynamicData?.childRequestHeaderData?.ChildRequestId
		};
		
		return new Promise((resolve, reject) => {
			const hSuccess = (data) => {
				if (data?.statusCode === API_CODE.STATUS_200 && data?.body?.length > 0) {
					const result = mergeArrays(data?.body);
					setloading(false);
					resolve(result);
				} else {
					setloading(false);
					resolve([]);
				}
			};

			const hError = (error) => {
				setloading(false);
				customError(error);
				reject(error);
			};

			doAjax(url, "post", hSuccess, hError, changeLogPayload);
		});
	};

	const presentDate = new Date();
	const backDate = new Date();
	backDate.setDate(backDate.getDate() - 15);

	const functions_ExportAsExcel = {
		convertJsonToExcel: () => {
			const sheetsData = tabData.map((tab) => {
				const columns = tab.columns.fieldName.map((field, idx) => ({
					header: tab.columns.headerName[idx],
					key: field
				}));
				return {
					sheetName: tab.label,
					fileName: `Changelog Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
					columns: columns,
					rows: tab.rows
				};
			});
			saveExcelMultiSheets(sheetsData);
		},
		button: () => {
			return (
				<Button
					sx={{
						textTransform: "capitalize",
						position: "absolute",
						right: 0,
						top: 0,
					}}
					onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
				>
					Download
				</Button>
			);
		},
	};

	return (
		<>
		{loading && <ReusableBackDrop blurLoading={loading} loaderMessage={LOADING_MESSAGE.CHANGELOG_LOADING}/>}
		<Modal
			open={open}
			onClose={onClose}
			aria-labelledby="modal-modal-title"
			aria-describedby="modal-modal-description"
		>
			<Box sx={style}>
				<Stack>
					<Grid
						item
						md={12}
						sx={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
					>
						<Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
							<ChangeCircleOutlinedIcon
								sx={{
									color: "black",
									fontSize: "20px",
									"&:hover": {
										transform: "rotate(360deg)",
										transition: "0.9s",
									},
									textAlign: "center",
									marginTop: "4px",
								}}
							/>
							<Typography
								id="modal-modal-title"
								variant="subtitle1"
								fontSize={"16px"}
								fontWeight={"bold"}
								sx={{ color: "black" }}
							>
								Change Log
							</Typography>
						</Box>

						<Box sx={{ display: "flex", alignItems: "center", gap: "8px" }}>
							<Tooltip title="Export Table">
								<IconButton
									sx={iconButton_SpacingSmall}
									onClick={functions_ExportAsExcel.convertJsonToExcel}
								>
									<ReusableIcon iconName={"IosShare"} />
								</IconButton>
							</Tooltip>

							<IconButton sx={{ padding: "0 0 0 5px" }} onClick={onClose}>
								<CloseIcon />
							</IconButton>
						</Box>
					</Grid>
				</Stack>

				<Tabs
					value={selectedTab?.number}
					onChange={handleTabChange}
					variant="scrollable"
            		scrollButtons="auto"
					aria-label="modal tabs"
				>
					{tabData?.map((tab, index) => (
						<Tab key={index} label={tab.label} />
					))}
				</Tabs>

				<div
					className="tab-content"
					style={{ position: "relative", height: "100%", marginTop: 16 }}
				>
					{tabData?.map((tab, index) => {
						return (
							selectedTab?.number === index && (
								<Typography
									key={index}
									id={`modal-tab-content-${index}`}
									sx={{ mt: 1 }}
								>
									<Grid item sx={{ position: "relative" }}>
										<Stack>
											<ReusableDataTable
												rows={tab?.rows}
												columns={tab?.columns?.fieldName?.map((col, index) => ({
													field: col,
													headerName: tab?.columns?.headerName[index],
													flex: 1,
													minWidth: 100,
												}))}
												getRowIdValue={"id"}
												pageSize={tab?.columns?.fieldName?.length}
												autoHeight
												scrollbarSize={10}
												sx={{
													"& .MuiDataGrid-row:hover": {
														backgroundColor: "#EAE9FF40",
													},
													backgroundColor: "#fff",
												}}
											/>
										</Stack>
									</Grid>
								</Typography>
							)
						)
					})}
				</div>
			</Box>
		</Modal>
		</>
	);
};

export default ChangeLog;
