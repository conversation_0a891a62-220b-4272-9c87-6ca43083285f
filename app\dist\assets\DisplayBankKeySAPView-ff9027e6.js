import{b9 as ne,n as N,g as re,u as se,s as ie,aP as te,a as le,r as i,dn as de,dp as ce,bc as pe,bd as E,C as ye,c as y,j as a,Q as fe,O as t,a6 as he,a_ as be,a$ as ge,d as T,Z as h,aZ as R,B as w,b5 as me,b6 as ue,ag as ke,aO as G,ae as xe,bf as B,bg as Be,dg as P,aG as Se,dq as Te}from"./index-226a1e75.js";import{d as Ke,a as Ae,b as ve}from"./Category-83dc6e58.js";import{d as Ce}from"./Description-d98685cc.js";import{u as Ee}from"./useBankKeyFieldConfig-85a40c89.js";import{G as Re}from"./GenericTabsGlobal-6faba7da.js";import"./FilterFieldGlobal-b5a561ef.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";const S=({label:K,value:l,icon:A})=>a(t,{item:!0,xs:6,children:y(R,{flexDirection:"row",alignItems:"center",spacing:1,children:[A&&a(w,{children:A}),a(T,{variant:"body2",color:h.secondary.grey,children:K}),y(T,{variant:"body2",fontWeight:"bold",children:[": ",l||""]})]})}),Ve=()=>{var I;const K=ne(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),l=N(s=>{const n=s.bankKey.bankKeyTabs||{};return Object.values(n).flat().filter(c=>(c==null?void 0:c.tab)!=="Initial Screen")}),{loading:A,error:L,fetchBankKeyFieldConfig:$}=Ee(),W=re();K();const z=se(),U=ie(),e=z.state,{customError:_}=te(),{t:g}=le(),[u,O]=i.useState([]),[k,F]=i.useState([]),[V,v]=i.useState(!1),[q,C]=i.useState(""),[x,Y]=i.useState(0),[Z,we]=i.useState(null),[Q,Le]=i.useState(""),M=i.useRef(!1),j=i.useRef(!1),{fetchAllDropdownFMD:X}=de(P,Te),H=N(s=>{var n;return(n=s.bankKey)==null?void 0:n.isOdataApiCalled});i.useEffect(()=>(H||(X("bankKey"),U(ce(!0))),pe(B.MODULE,G.BK),()=>{E(B.MODULE),E(B.CURRENT_TASK),E(B.ROLE)}),[]),i.useEffect(()=>{(!l||l.length===0)&&$(e==null?void 0:e.BankKey)},[]),i.useEffect(()=>{!M.current&&(e!=null&&e.BankCtry)&&(e!=null&&e.BankKey)&&(M.current=!0,ee())},[e==null?void 0:e.BankCtry,e==null?void 0:e.BankKey]);const J=(s,n)=>{Y(n)},D=(s,n)=>{if(!s||!Array.isArray(s)||!n)return[];const f={"Control Data":n==null?void 0:n.toControlData,"Address Data":n==null?void 0:n.Tobankaddress};return s.map(o=>{const c=o==null?void 0:o.tab;if(!c||!(o!=null&&o.data))return o;const b=f[c];if(!b)return o;const m={};for(const r in o.data){if(!o.data[r]||!Array.isArray(o.data[r])){m[r]=o.data[r];continue}m[r]=o.data[r].map(d=>{if(!d||typeof d!="object")return d;const ae=d.jsonName;let p=b==null?void 0:b[ae];if(d.dataType==="Date"&&typeof p=="string"&&p.includes("/Date"))try{p=Be(p)}catch(oe){oe("Error converting SAP date:",L),p=d.value}return typeof p=="boolean"&&(p=p?"TRUE":"FALSE"),{...d,value:p??d.value}})}return{...o,data:m}})};i.useEffect(()=>{const s=l&&Array.isArray(l)&&l.length>0,n=k&&typeof k=="object"&&Object.keys(k).length>0;if(s&&n&&!j.current){j.current=!0;try{const f=D(l,k);O(f)}catch(f){console.error("Error mapping tabs to config:",f),O(l)}}},[l,k]);const ee=()=>{if(!(e!=null&&e.BankCtry)||!(e!=null&&e.BankKey)){console.error("Missing required parameters: BankCtry or BankKey");return}v(!0),C("Fetching bank key data...");const s={bankCtry:e==null?void 0:e.BankCtry,bankKey:e==null?void 0:e.BankKey},n=o=>{var c,b,m;v(!1),C("");try{const r=((c=o==null?void 0:o.body)==null?void 0:c[0])||{},d={...r,toControlData:{SwiftCode:(b=r==null?void 0:r.Tobankaddress)==null?void 0:b.SwiftCode,IbanRule:r==null?void 0:r.IbanRule,BankNo:(m=r==null?void 0:r.Tobankaddress)==null?void 0:m.BankNo}};F(d)}catch(r){r("Error processing response data:",L),F({})}},f=o=>{console.error("Error fetching bank key data",o),v(!1),C(""),_&&_("Failed to fetch bank key data. Please try again.")};ye(`/${P}/data/displayBankKeySAPData`,"post",n,f,s)};return y("div",{style:{backgroundColor:"#FAFCFF"},children:[a(t,{container:!0,sx:fe,children:a(t,{item:!0,md:12,sx:{padding:"16px",display:"flex"},children:y(t,{md:9,sx:{display:"flex"},children:[a(he,{color:"primary",sx:be,onClick:()=>W(-1),children:a(ge,{sx:{fontSize:"25px",color:"#000000"}})}),y(t,{item:!0,md:12,children:[a(T,{variant:"h3",children:a("strong",{children:g("Display Bank Key")})}),a(T,{variant:"body2",color:"#777",children:g("This view displays the details of the Bank Key")})]})]})})}),y(t,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",sx:{justifyContent:"space-between",alignItems:"center",paddingLeft:"29px",backgroundColor:h.basic.lighterGrey,borderRadius:"10px",boxShadow:"0 2px 8px rgba(0,0,0,0.06)"},children:[y(R,{width:"48%",spacing:1,sx:{padding:"10px 15px",borderRight:"1px solid #eaedf0"},children:[a(t,{item:!0,children:a(S,{label:g("Bank Country"),value:(e==null?void 0:e.BankCtry)||"",labelWidth:"35%",icon:a(Ke,{sx:{color:h.blue.indigo,fontSize:"20px"}})})}),a(t,{item:!0,children:a(S,{label:g("Bank Key Number"),value:(e==null?void 0:e.BankKey)||"",labelWidth:"35%",icon:a(Ae,{sx:{color:h.blue.indigo,fontSize:"20px"}})})})]}),y(R,{width:"48%",spacing:1,marginRight:"-10%",sx:{padding:"10px 15px"},children:[a(t,{item:!0,children:a(S,{label:g("Bank Name"),value:(e==null?void 0:e.BankName)||"",labelWidth:"35%",icon:a(ve,{sx:{color:h.blue.indigo,fontSize:"20px"}})})}),a(t,{item:!0,children:a(S,{label:g("Bank Branch"),value:(e==null?void 0:e.BankBranch)||"",labelWidth:"35%",icon:a(Ce,{sx:{color:h.blue.indigo,fontSize:"20px"}})})})]})]}),a(t,{children:u.length>0?y(w,{sx:{mt:3},children:[a(me,{value:x,onChange:J,indicatorColor:"primary",textColor:"primary",variant:"scrollable",scrollButtons:"auto",sx:{borderBottom:1,borderColor:"divider",mb:2},children:u.map((s,n)=>a(ue,{label:s.tab},n))}),a(ke,{elevation:2,sx:{p:3,borderRadius:8},children:u[x]&&a(Re,{disabled:!0,basicDataTabDetails:u[x].data,dropDownData:"",activeViewTab:u[x].tab,uniqueId:Q,selectedRow:Z||{},module:(I=G)==null?void 0:I.BK})})]}):a(w,{sx:{marginTop:"30px",border:`1px solid ${h.secondary.grey}`,padding:"16px",background:`${h.primary.white}`,textAlign:"center"},children:a("span",{children:xe.NO_DATA_AVAILABLE})})}),a(Se,{blurLoading:V,loaderMessage:q})]})};export{Ve as default};
