import{qa as o,cb as e,r as i,qb as y,q2 as z}from"./index-226a1e75.js";const M=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("circle",{cx:"10",cy:"10",r:"8.75",stroke:"currentColor",strokeWidth:"1.25"}),e.jsx("path",{d:"M15.2841 6.25L7.78409 13.75L4.375 10.3409",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}),I=o(M),W=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("path",{d:"M15.2841 6.25L7.78409 13.75L4.375 10.3409",stroke:"currentColor",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("rect",{x:"1.25",y:"1.25",width:"17.5",height:"17.5",rx:"5",stroke:"currentColor",strokeWidth:"1.25"})]})}),B=o(W),G=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsx("g",{id:"Icons /General",children:e.jsx("path",{d:"M1.25 6.25C1.25 3.48858 3.48858 1.25 6.25 1.25H13.75C16.5114 1.25 18.75 3.48858 18.75 6.25V13.75C18.75 16.5114 16.5114 18.75 13.75 18.75H6.25C3.48858 18.75 1.25 16.5114 1.25 13.75V6.25Z",stroke:"currentColor",strokeWidth:"1.25"})})}),w=o(G),H=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsx("g",{id:"Icons /General",children:e.jsx("circle",{id:"Container",cx:"10",cy:"10",r:"9.44444",stroke:"currentColor",strokeWidth:"1.11111"})})}),k=o(H),$=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("path",{d:"M1.25 6.25C1.25 3.48858 3.48858 1.25 6.25 1.25H13.75C16.5114 1.25 18.75 3.48858 18.75 6.25V13.75C18.75 16.5114 16.5114 18.75 13.75 18.75H6.25C3.48858 18.75 1.25 16.5114 1.25 13.75V6.25Z",fill:"currentColor"}),e.jsx("path",{d:"M15.2841 6.25L7.78409 13.75L4.375 10.3409",stroke:"var(--contrast-text)",strokeWidth:"1.25",strokeLinecap:"round",strokeLinejoin:"round"})]})}),u=o($),V=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("path",{d:"M1.25073 6.25C1.25073 3.48857 3.48931 1.25 6.25073 1.25H13.7507C16.5122 1.25 18.7507 3.48857 18.7507 6.25V13.75C18.7507 16.5114 16.5122 18.75 13.7507 18.75H6.25073C3.48931 18.75 1.25073 16.5114 1.25073 13.75V6.25Z",fill:"currentColor"}),e.jsx("path",{d:"M15.5556 10H4.44446",stroke:"var(--contrast-text)",strokeWidth:"1.25556",strokeLinecap:"round"})]})}),E=o(V),q=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("circle",{cx:"10",cy:"10",r:"9.44444",fill:"currentColor",stroke:"currentColor",strokeWidth:"1.11111"}),e.jsx("path",{d:"M16.2963 5.55554L8.14816 13.7037L4.44446 9.99999",stroke:"var(--background-default)",strokeWidth:"1.48148",strokeLinecap:"round",strokeLinejoin:"round"})]})}),g=o(q),Z=r=>e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"none",...r,children:e.jsxs("g",{id:"Icons /General",children:[e.jsx("circle",{cx:"10",cy:"10",r:"9.44444",fill:"currentColor",stroke:"currentColor",strokeWidth:"1.11111"}),e.jsx("path",{d:"M14.4444 10H5.55554",stroke:"var(--background-default)",strokeWidth:"1.11111",strokeLinecap:"round"})]})}),S=o(Z),A=({checked:r,defaultChecked:p=!1,size:c="medium",circle:t=!1,onChange:a,disabled:l,indeterminate:m=!1,label:C,...f})=>{const[n,d]=i.useState(r||p),[x,h]=i.useState(!1);i.useEffect(()=>{r!==void 0&&d(r)},[r]);const L=(b,v)=>{d(v),a&&a(b,v)},s=i.useMemo(()=>{switch(c){case"small":return"xsmall";default:return"small"}},[c]),j=i.useMemo(()=>l?t?n?e.jsx(g,{color:"var(--text-disabled)",size:s}):e.jsx(k,{color:"var(--text-disabled)",size:s}):n?e.jsx(u,{fill:"var(--text-disabled)",color:"var(--text-disabled)",size:s}):e.jsx(w,{color:"var(--text-disabled)",size:s}):x&&!n?t?e.jsx(I,{size:s}):e.jsx(B,{size:s}):t?n?e.jsx(g,{color:"var(--primary-main)",fill:"var(--primary-main)",size:s}):e.jsx(k,{size:s}):n?e.jsx(u,{color:"var(--primary-main)",fill:"var(--primary-main)",size:s}):e.jsx(w,{size:s}),[n,t,l,x,s]);return e.jsxs("div",{style:{display:"flex",alignItems:"center"},children:[e.jsx(y,{checkedIcon:j,icon:j,indeterminate:m,indeterminateIcon:t?e.jsx(S,{size:s,color:"var(--primary-main)"}):e.jsx(E,{size:s,color:"var(--primary-main)"}),onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),onChange:L,disabled:l,checked:n,...f}),e.jsx(z,{variant:"body1",children:C})]})};export{B as a,E as b,w as c,g as d,S as e,I as i,u as l,A as o,k as t};
