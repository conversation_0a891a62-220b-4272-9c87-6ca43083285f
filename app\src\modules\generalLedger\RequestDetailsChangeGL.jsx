import React, { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  Button,
  Select,
  MenuItem,
  Slide,
  Typography,
  Box,
  Paper,
  Checkbox,
  ListItemText,
  TextField,
} from "@mui/material";
import {
  destination_GeneralLedger_Mass
} from "../../destinationVariables";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { colors } from "@constant/colors";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { END_POINTS } from "@constant/apiEndPoints";
import {
  API_CODE,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  LOADER_MESSAGES,
  LOCAL_STORAGE_KEYS,
  DROP_DOWN_SELECT_OR_MAP,
  CHANGE_LOG_STATUSES,
  SUCCESS_DIALOG_MESSAGE,
} from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  showToast,
  transformGLResponseChange,
  changePayloadForGL,
  transformGlData,
} from "../../functions";
import { APP_END_POINTS } from "@constant/appEndPoints";

import useButtonDTConfig from "@hooks/useButtonDTConfig";
import { resetGLStateGL, updateReqBenchRowGL } from "@app/generalLedgerTabSlice";
import { setChangedFieldsMapGL, setFetchedGeneralLedgerDataGL, setFetchReqBenchDataGL, setOriginalGeneralLedgerDataGL, setOriginalReqBenchDataGL, updateGeneralLedgerRowGL } from "@app/generalLedgerTabSlice";
import { doAjax } from "../../components/Common/fetchService";
import DownloadDialog from "../../components/Common/DownloadDialog";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import ReusableDataTable from "../../components/Common/ReusableTable";
import moment from "moment";
import { setDependentDropdown, setDropDown } from "./slice/generalLedgerDropDownSlice";
import { getLocalStorage } from "@helper/glhelper";
import useLogger from "@hooks/useLogger";
import { useChangeLogUpdateGl } from './../../hooks/useChangeLogUpdateGl';
import SuccessDialog from "@components/Common/SubmitDialog";
import { FAILURE_DIALOG_MESSAGE, MODULE_MAP } from "@constant/enum";
import ObjectLockDialog from "@components/Common/ObjectLockDialog";
import { setDropDown as changeFieldDropdown } from "@app/dropDownDataSlice";
import { setCreatePayloadCopyForChangeLog } from '@app/changeLogReducer';


const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const RequestDetailsChangeGL = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
  moduleName,
  setIsAttachmentTabEnabled,
  isDisabled
}) => {  
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    fetchedGeneralLedgerData,
    originalGLData,
    fetchReqBenchData,
    originalReqBenchData,
    changedFieldsMap,
  } = useSelector((state) => state.generalLedger);
  
  const requestHeaderData = useSelector(
    (state) => state.generalLedger.payload.requestHeaderData
  );

														

													  
  const { updateChangeLogGlForChange } = useChangeLogUpdateGl();

  const initialPayload = useSelector((state) => state.request.requestHeader);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
																		   
  const isButtonEnabled = fetchReqBenchData?.reqStatus === "Validated-Requestor";
  const rmSearchForm = useSelector(
      (state) => state.commonFilter["GeneralLedger"]
    );

  const { customError } = useLogger()
  
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const RequestId = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const isrequestId = queryParams.get("RequestId");
  const selectedModule = getLocalStorage(LOCAL_STORAGE_KEYS.MODULE,true, {})
  
  const selectedModuleSelector = DROP_DOWN_SELECT_OR_MAP[selectedModule] || (() => ({}));
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader);
  const createChangeLogDataforChange = useSelector((state) => state?.changeLog.createChangeLogDataGL);
  const allDropDownData = useSelector(selectedModuleSelector)
  const [open, setOpen] = useState(true);
  const [dropdown1Value, setDropdown1Value] = useState("");
  const [dropdown2Value, setDropdown2Value] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [selectedAccountType, setSelectedAccountType] = useState([]);
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataCountry, setDropdownDataCountry] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState("");
  const [dropdownDataRegion, setDropdownDataRegion] = useState([]);
  const [dropdownDataSegment, setDropdownDataSegment] = useState([]);
  const [dropdownAamnum, setDropdwnAamnum] = useState([]);
  const [costcenterResponse, setCostcenterResponse] = useState([]);
  const [openSnackBar, setOpenSnackBar] = useState(false);
  const [alertMsg, setAlertMsg] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [isLoading, setIsLoading] = useState(false);
  const [dropdownDataAccountType, setDropdownDataAccountType] = useState([]);
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const templateFullData = useSelector((state) => state?.payload?.changeFieldSelectiondata || []);
  const dropdownFields = ["Taxcategory","Sortkey","HouseBank","AccountId","ReconAcc"]
  const [showGrid, setShowGrid] = useState(false);
  const [dialogData, setDialogData] = useState({
    title: "",
    message: "",
    subText: "",
    buttonText: "",
    redirectTo: "",
  });


  useEffect(()=>{
    if((fetchedGeneralLedgerData?.length > 0) && ( reqBench !== "true")){
      
      setShowGrid(true);
      setIsAttachmentTabEnabled(true);
    }
    
  },[])

  useEffect(()=>{
    getChartOfAccount()
  },[])

  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal(MODULE_MAP?.GL, "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);
    const getCompanyCode = (coa) => {
      const hSuccess = (data) => {
        setDropdownDataCompany(data.body)
        dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountType = () => {
  
      const hSuccess = (data) => {
        setDropdownDataAccountType(data.body)
        dispatch(setDropDown({ keyName: "AccountType", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountCurrency = (compCode,id) => {
      const hSuccess = (data) => {
        dispatch(
                setDependentDropdown({
                  keyName: "AccountCurrency",
                  data: data.body || [],
                  keyName2: id,
                })
              )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
     const getFiledStatusGroup = (compCode,id) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "FieldStsGrp",
            data: data.body,
            keyName2: id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getTaxCategory = (compCode,id) => {
  
      const hSuccess = (data) => {
       dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: id,
        })
      )

      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getHouseBank = (compCode,id) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "House Bank",
            data: data.body || [],
            keyName2:id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccontId = (compCode) => {
      const hSuccess = (data) => {

        dispatch(
          setDependentDropdown({
            keyName: "AccountId",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };
  
  
    const getCostElementCategory = (accType,id) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "CostEleCategory",
            data: data.body || [],
            keyName2:id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getreconAccountType = (id) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "ReconAcc",
            data: data.body,
            keyName2: id,
          })
        )

      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
        "get",
        hSuccess,
        hError
      );
    };

    const getSortKey = (compCode,id) => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "Sortkey",
              data: data.body || [],
              keyName2: id,
            })
          )
        };

        
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getSortKey`,
          "get",
          hSuccess,
          hError
        );
      };
  
    const getPlanningLevel = (compCode) => {
      const hSuccess = (data) => {
  
        dispatch(
          setDependentDropdown({
            keyName: "Planninglevel",
            data: data.body || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
        "get",
        hSuccess,
        hError
      );
    };
  
    const getAccountGroup = (coa) => {
      const hSuccess = (data) => {
  
        let accGrparr = []
        data?.body?.map((item) => {
          let hash = {}
          hash["code"] = item?.AccountGroup
          hash["desc"] = item?.Description
          hash["FromAcct"] = item?.FromAcct
          hash["ToAcct"] = item?.ToAcct
          accGrparr?.push(hash)
        })
        setDropdownDataAccountType(accGrparr)
        dispatch(
          setDependentDropdown({
            keyName: "AccountGroup",
            data: accGrparr || [],
            keyName2: selectedRowId || selectedRow?.id,
          })
        )
  
        dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
        "get",
        hSuccess,
        hError
      );
    };
  
  
    const getChartOfAccount = () => {
      const hSuccess = (data) => {
        dispatch(setDropDown({ keyName: "COA", data: data.body }));
      };
  
      const hError = (error) => {
        customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getChartOfAccounts`,
        "get",
        hSuccess,
        hError
      );
    };
  
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };

  const getObjectLock = (params) => {
      if (!selectedCostCenters.length ) return;
																														
      const objectLockPayload = selectedCostCenters?.map((gl) => ({
        glAccount: gl?.code,
        compCode: gl?.compCode,
        changedFieldsToCheck: initialPayload?.fieldName ? initialPayload?.fieldName :requestHeaderData?.FieldName,
      }));



  
      const successHandler = (response) => {
        const hasError = response?.some((item) => item?.statusCode !== 200);
        if (!hasError) {
          if (params === "OK") {
            setOpen(false);
              if(reqBench !== 'true'){
                setShowGrid(true);
                setIsAttachmentTabEnabled(true);
              }
              fetchCostCenterDetails(); 
          } else if (params === "Download") {
            handleDownloadDialogOpen();
          }
        } else {
          const filteredData = response.filter((item) => item.statusCode === 400);
          let duplicateFieldsArr = [];

            
          filteredData?.forEach((item, index) => {
            const dataHash = {
              id: `${item?.body?.glAccount}_${index}`, // ✅ UNIQUE ID
              objectNo: item?.body?.glAccount,
              reqId: item?.body?.matchingRequests
                ?.map((req) => req?.matchingRequestHeaderId)
                ?.filter(Boolean),
              childReqId: item?.body?.matchingRequests
                ?.map((req) => req?.matchingChildHeaderIdsSet)
                ?.filter(Boolean),
              requestedBy: item?.body?.matchingRequests
                ?.map((req) => req?.RequestCreatedBy)
                ?.filter(Boolean),
            };
  
            duplicateFieldsArr.push(dataHash);
          });
          setDuplicateFieldsData(duplicateFieldsArr);
          setShowTableInDialog(true);
        }
      };
  
      const errorHandler = (err) => {
        customError(err)
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/${END_POINTS.DATA.GET_DUPLICATE_GL_REQUEST}`,
        "post",
        successHandler,
        errorHandler,
        objectLockPayload
      );
  };

  const handleOk = (params) => {
    getObjectLock(params);
    
  };
  const allColumns = [
    {
      field: "included",
      headerName: "Included",
      sortable: false,
      filterable: false,
      width: 65,
      disableColumnMenu: true,
       renderHeader: () => {
          const data = reqBench ? fetchReqBenchData : fetchedGeneralLedgerData;
          const allSelected = data.every((row) => row.included);
          const someSelected = data.some((row) => row.included);
      
          return (
            <Checkbox
              checked={allSelected}
              indeterminate={!allSelected && someSelected}
              onChange={(e) => {
                const updatedData = data.map((row) => ({
                  ...row,
                  included: e.target.checked,
                }));
                if (reqBench) {
                  dispatch(setFetchReqBenchDataGL(updatedData)); // adjust to your actual setter
                } else {
                  dispatch(setFetchedGeneralLedgerDataGL(updatedData)); // adjust to your actual setter
                }
              }}
            />
          );
        },
        renderCell: (params) => {
          const data = reqBench ? fetchReqBenchData : fetchedGeneralLedgerData
          const rowIndex = data.findIndex((row) => row.id === params.row.id);
      
          return (
            <Checkbox
              checked={data[rowIndex]?.included || false}
              onChange={(e) => {
                const updatedData = [...data];
                updatedData[rowIndex] = {
                  ...updatedData[rowIndex],
                  included: e.target.checked,
                };
                if (reqBench) {
                  dispatch(setFetchReqBenchDataGL(updatedData));
                } else {
                  dispatch(setFetchedGeneralLedgerDataGL(updatedData));
                }
              }}
            />
          );
        },
    },
    {
      field: "lineNumber",
      headerName: "Sl No",
    },
    {
      field: "GLAccount",
      headerName: "General Ledger",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "CompanyCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
     {
      field: "Accounttype",
      headerName: "Account Type",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
     {
      field: "AccountGroup",
      headerName: "Account Group",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "COA",
      headerName: "Chart Of Account",
      width: 150,
      editable: false,
    },
    {
      field: "GLname",
      headerName: "Short Text",
      width: 200,
      editable: true,
    },
    {
      field: "Description",
      headerName: "Long Text",
      width: 200,
      editable: true,
    },
    {
      field: "Balanceinlocrcy",
      headerName: "Only Balance In Local Currency",
      width: 250,
      editable: true,
    },
    {
      field: "Taxcategory",
      headerName: "Tax Category",
      width: 250,
      editable: true,
    },
    {
      field: "Pstnwotax",
      headerName: "Posting Without Tax Allowed",
      width: 250,
      editable: true,
    },
    { field: "ReconAcc", headerName: "Recon. Account For Account Type", width: 150 },
    {
      field: "Openitmmanage",
      headerName: "Open Item Management",
      width: 150,
    },
    { field: "OpenItemManagebyLedgerGrp", headerName: "Open Item Management By Ledger Group", width: 150 },
    { field: "Sortkey", headerName: "Sort Key", width: 150, editable: true },
    {
      field: "FieldStsGrp",
      headerName: "Field Status Group",
      width: 150,
      editable: true,
    },
    { field: "PostAuto", headerName: "Post Automatically Only", width: 150, editable: true },
    { field: "HouseBank", headerName: "House Bank", width: 150, editable: true },
    { field: "AccountId", headerName: "Account Id", width: 150, editable: true },
    { field: "PostingBlockedCoCd", headerName: "Blocked For Posting Company Code", width: 150, editable: true },
    { field: "PostingBlockedCOA", headerName: "Blocked For Posting at COA", width: 150, editable: true },
    { field: "name2", headerName: "Name 2", width: 150, editable: true },
    { field: "name3", headerName: "Name 3", width: 150, editable: true },
    { field: "name4", headerName: "Name 4", width: 150, editable: true },
  ];


  const handleDropdownDataChange = (e, params, value) => {
    

    
    const newValue = e.target.value;    
      updateChangeLogGlForChange({
        uniqueId: params.row.id,
        filedName:params?.colDef?.headerName,

        jsonName: params?.field,
        currentValue: newValue,
        requestId: initialPayload?.RequestId,
        childRequestId: requestId,
        accountNumber:params.row.GLAccount
      });
   
    const updatedRow = {
      ...params.row,
      [params.field]: newValue,
    };
    if (reqBench) {
      dispatch(updateReqBenchRowGL(updatedRow));
    } else {
      dispatch(updateGeneralLedgerRowGL(updatedRow));
    }
  };

  const handleGridTextChange = (params) => (e) => {
    const newValue = e.target.value.toUpperCase();
    params.api.setEditCellValue({
      id: params.id,
      field: params.field,
      value: newValue,
    });

      updateChangeLogGlForChange({
        uniqueId: params.row.id,
        filedName:params?.colDef?.headerName,
        jsonName: params?.field,
        currentValue: newValue,
        requestId: initialPayload?.RequestId,
        childRequestId: requestId,
        accountNumber:params.row.GLAccount
      });
 
  };

  const handleGridRadioChange = (params) => (e) => {
   params.api.setEditCellValue({
      id: params.id,
      field: params.field,
      value: ! params.value,
    })

  
      updateChangeLogGlForChange({
        uniqueId: params.row.id,
        filedName:params?.colDef?.headerName,
        jsonName: params?.field,
        currentValue: ! params.value,
        requestId: initialPayload?.RequestId,
        childRequestId: requestId,
        accountNumber:params.row.GLAccount
      });

  };

  const fieldNameList = requestHeaderData?.FieldName || [];
  const fixedColumns = allColumns.slice(0, 6);

    const dynamicColumns = allColumns
      .slice(5)
      .filter((col) => fieldNameList?.includes(col.headerName))
      .map((col) => {
        if (dropdownFields?.includes(col?.field)) {
          return {
            ...col,
            editable: false,
            renderCell: (params) => {
              const value = params.value || "";
              return (
                <Select
                  value={value}
                  onChange={(e) => handleDropdownDataChange(e, params, value)}
                  size="small"
                  fullWidth
                  sx={{ minHeight: "36px" }}
                >
                  {
                    Array.isArray(allDropDownData?.[col?.field]?.[params?.row?.id]) &&
                    allDropDownData[col.field][params.row.id].length > 0 ? (
                      allDropDownData[col.field][params.row.id].map((option, idx) => (
                        <MenuItem key={idx} value={option?.code}>
                          {option?.code}
                        </MenuItem>
                      ))
                    ) : null
                  }
                </Select>
              );
            },
          };
        }
        if ([
            "HouseBank",
            "FieldStsGrp"           
          ].includes(col.field)) {
          
          return {
            ...col,
            editable: false,
            renderCell: (params) => {
              const value = params.value || "";
              const row = params.row || "";
              return (
                <Select
                  value={value}
                  onChange={(e) => handleDropdownDataChange(e, params, value)}
                  size="small"
                  fullWidth
                  sx={{ minHeight: "36px" }}
                >
                {
                  Array.isArray(allDropDownData?.[col?.field]?.[params?.row?.id]) &&
                  allDropDownData[col.field][params.row.id].length > 0 ? (
                    allDropDownData[col.field][params.row.id].map((option, idx) => (
                      <MenuItem key={idx} value={option?.code}>
                        {option?.code}
                      </MenuItem>
                    ))
                  ) : null
                }
                </Select>
              );
            },
          };
        }
        if (
          [
            "Description",
            "GLname",
            "GLAccount",
          ].includes(col.field)
        ) {
          return {
            ...col,
            editable: true,
            renderCell: (params) => (
              <TextField
                value={params.value || ""}
                 onChange={handleGridTextChange(params)}
                variant="outlined"
                size="small"
                fullWidth
              />
            ),
            renderEditCell: (params) => (
              <TextField
                value={params.value || ""}
                 onChange={handleGridTextChange(params)}
                variant="outlined"
                size="small"
                fullWidth
                placeholder={
                  col.field === "longDescription"
                    ? "Enter Long Description"
                    : "Enter Short Description"
                }
                sx={{
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: "#000",
                      color: "#000",
                    },
                  },
                }}
              />
            ),
          };
        }
        if (
          [
            "Openitmmanage",
            "Pstnwotax",
            "OpenItemManagebyLedgerGrp",
            "PostingBlockedCoCd",
            "PostingBlockedCOA",
            "PostAuto",
            "Balanceinlocrcy"
          ].includes(col.field)
        ) {
          return {
            ...col,
            editable: true,
            renderCell: (params) => (
              

              <Checkbox
                sx={{
                  padding: 0,
                  marginTop:"5px",
                  "&.Mui-disabled": {
                  color: colors.hover.light,
                  },
                  "&.Mui-disabled.Mui-checked": {
                  color: colors.hover.light,
                  },
                }}
                checked={params.value}
                onChange={handleGridRadioChange(params)}
              />
            ),
            renderEditCell: (params) => (
              <Checkbox
                sx={{
                  padding: 0,
                  marginTop:"5px",
                  "&.Mui-disabled": {
                  color: colors.hover.light,
                  },
                  "&.Mui-disabled.Mui-checked": {
                  color: colors.hover.light,
                  },
                }}
                checked={params.value}
                onChange={handleGridRadioChange(params)}
              />
              
            ),
          };
        }
        return {
          ...col,
          editable: true,
        };
      });

  const columns = [...fixedColumns, ...dynamicColumns];
  const adjustedColumns =
    columns.length < 10
      ? columns.map((col) => {
        if (col.field === "included" || col.field === "lineNumber") {
          // keep fixed for first 2 columns
          return {
            ...col,
            width: col.width || 100,  // fallback width if not set
            flex: undefined,          // no stretching
          };
        }
        return {
          ...col,
          flex: 2,
          minWidth: 200 
        };
      })
      : columns;
  const processRowUpdate = (newRow) => {
     dispatch(updateGeneralLedgerRowGL(newRow));
    return newRow;
  };
  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowGL(newRow));
    return newRow;
  };

  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };

  useEffect(() => {
      getCompanyCode(dropdown1Value)
    
  }, [dropdown1Value]);


  const fetchGLAccount =() =>{
    const hSuccess = (data) => {

      let arr=[]
      data?.body?.map((item)=>{
        let hash={}
          hash["code"] =item?.code
        arr?.push(hash)
      })
      setCostCenterOptions((prev) => [
        ...new Set([...prev, ...arr]),
      ]);
     
    };

    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountByCOA?chartAccount=ETCN`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGlAcountDropdownDataFromSearchFilter = (valueAcctype) => {
      let payload = {
        glAccount: "",
        chartOfAccount: dropdown1Value,
        postAutoOnly: "",
        companyCode: selectedCompanyCodes?.join(","),
        taxCategory: "",
        glAcctLongText: "",
        postingWithoutTaxAllowed: "",
        blockedForPostingInCOA: "",
        shortText: "",
        blockedForPostingInCompany: "",
        accountGroup:  '',
        glAccountType: valueAcctype?.[0]?.code,
        fieldStatusGroup: "",
        openItemMgmtbyLedgerGroup: "",
        openItemManagement: "",
        reconAccountforAcctType: "",
        fromDate:
          moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
        toDate:
          moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
        createdBy: "",
        top: 100,
        skip: 0,
      };
      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          let glAccountArr = [];
  
          data?.body?.list?.forEach((item) => {
              let glAccountHash = {};
              glAccountHash["code"] = item?.GLAccount;
              glAccountHash["desc"] = item?.GLname;
              glAccountHash["coa"] = item?.COA;
              glAccountHash["accType"] = item?.Accounttype;
              glAccountHash["accGroup"] = item?.AccountGroup;
              glAccountHash["compCode"] = item?.CompanyCode;
              glAccountArr.push(glAccountHash);
          });

    
      
      setCostCenterOptions((prev) => [
        ...new Set([...prev, ...glAccountArr]),
      ]);
    }
      };
      const hError = (error) => {
        customError(error);
      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }


  const fetchCostCenterDetails = () => {
    if (!selectedCostCenters.length || !dropdown1Value) return;

    const glAccCOACoCode = [];

      selectedCostCenters.forEach(gl => {
          glAccCOACoCode.push({
            glAccount: gl?.code,
            chartOfAccount: gl?.coa,
            companyCode: gl?.compCode
          });
      });
     const payload = {
      glAccCOACoCode: glAccCOACoCode
    };

    const successHandler = (data) => {
      const rawData = data?.body || [];
      setCostcenterResponse(rawData);
      const transformed = transformGlData(rawData);
      rawData?.map((item)=>{
        getFiledStatusGroup(item?.CompanyCode,item?.GeneralLedgerID)
      })


    transformed?.map((item)=>{
      if (item?.COA) {
        getSortKey(item?.CompanyCode,item?.id)

      }
      if (item?.CompanyCode) {
        getTaxCategory(item?.CompanyCode, item?.id)
        getHouseBank(item?.CompanyCode, item?.id)
        getFiledStatusGroup(item?.CompanyCode, item?.id)
        getreconAccountType(item?.id)
        
      }
      if (item?.Accounttype) {
        
        getCostElementCategory(item?.Accounttype, item?.id)

      }

      })


       let rowsBodyData = {};
      // let count =0
      transformed?.forEach((data) => {
        const dynamicKey = data?.id ?? "";
        rowsBodyData[dynamicKey] = {
          ...data,
        };
      });

      let requestHeaderData ={}
      let rowsHeaderData ={}

      

      const CopyDataForGLInitial = {
        requestHeaderData,
        rowsHeaderData,
        rowsBodyData,
      };
                
      if(reqBench=== "true"){
         dispatch(setFetchReqBenchDataGL(transformed));
      }else{
        dispatch(setFetchedGeneralLedgerDataGL(transformed));
      }
      dispatch(setCreatePayloadCopyForChangeLog(CopyDataForGLInitial));
      const payload = {
    requestHeaderData,
    rowsHeaderData,
    rowsBodyData,
  };
    };

    const errorHandler = (err) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };

  useEffect(() => {
    if (
      reqBench === "true" &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchData.length === 0
    ) {
      let transformedData =''
      if(apiResponses[0]?.GeneralLedgerID !== null){
       transformedData = transformGLResponseChange(apiResponses);
    }
      if (transformedData?.length > 0){
      transformedData?.map((item)=>{
      if (item?.COA) {
        getSortKey(item?.COA,item?.id)
      }
      if (item?.compCode) {
        getTaxCategory(item?.compCode, item?.id)
        getHouseBank(item?.compCode, item?.id)
        getFiledStatusGroup(item?.compCode, item?.id)
        getreconAccountType(item?.id)
        
      }
      if (item?.accountType) {
        getCostElementCategory(item?.accountType, item?.id)
      }

      })
      dispatch(setFetchReqBenchDataGL(transformedData));
      dispatch(setOriginalReqBenchDataGL(transformedData));
    }
  }
	 
  }, [apiResponses, reqBench]);


  useEffect (()=>{


    if(templateFullData?.length > 0){
      const filteredAndSortedFields = templateFullData
      .filter((item) => item?.MDG_MAT_TEMPLATE === requestHeaderData?.TemplateName )
      .sort((a, b) => {
        const seqA = Number(a?.MDG_MAT_FIELD_SEQUENCE) || 0;
        const seqB = Number(b?.MDG_MAT_FIELD_SEQUENCE) || 0;
        return seqA - seqB;
      });
     const uniqueFieldNames = [
      ...new Set(
        filteredAndSortedFields
          .map((item) => item?.MDG_MAT_UI_FIELD_NAME)
          .filter(Boolean)
      ),
    ].map((field) => ({ code: field }));
    dispatch(
      setDropDown({
        keyName: "FieldName",
        data: uniqueFieldNames || [],
      })
    );
    }

  },[templateFullData])


 

  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }

    const { changedFields: _, ChangedFields, ...rest } = item;

    return {
      ...rest,
      changedFields,
    };
  });

  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;

    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.CostCenterID] = row.changedFields || {};
    });

    dispatch(setChangedFieldsMapGL(newChangedFieldsMap));
  }, [apiResponses]);


  const handleSaveAsDraft = (remarks='',userInput='') => {
    
    const Payload = changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,'')
     const hSuccess = (data) => {
         if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
  
        setOpenSnackBar(true);
        setAlertMsg("Unexpected response received.");
      }
      };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );

  };
  const handleSendBack = (remarks='',userInput='') => {

    const Payload =  changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,createChangeLogDataforChange)

    const hSuccess = (data) => {
      setIsLoading(false);
      setAlertType("success");
      setAlertMsg(
        "Cost Centers change submission for save as draft initiated"
      );
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving the draft.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };
  const handleRejectAndCancel = (remarks='',userInput='') => {
    const Payload = changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,createChangeLogDataforChange)
     const hSuccess = (data) => {
         if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
  
        setOpenSnackBar(true);
        setAlertMsg("Unexpected response received.");
      }
      };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while Reject.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForReview = (remarks='',userInput='') => {
    const Payload =  changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,createChangeLogDataforChange)

    const hSuccess = (data) => {
         if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
  
        setOpenSnackBar(true);
        setAlertMsg("Unexpected response received.");
      }
      };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSubmitForApprove = (remarks='',userInput='') => {
     const Payload = changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,createChangeLogDataforChange)


    const hSuccess = (data) => {

      setIsLoading(false);
      setAlertType("success");
      setAlertMsg("Cost Centers successfuly Approved");
      setOpenSnackBar(true);

      setTimeout(() => {
        navigate("/requestbench");
      }, 2000);
    };

    const hError = (error) => {
      setIsLoading(false);
      setAlertType("error");
      setAlertMsg("Error occurred while saving submit for review.");
      setOpenSnackBar(true);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const validateAllRows = (remarks='',userInput='') => {
    setBlurLoading(true)
    const Payload = changePayloadForGL(
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks='',userInput='')
      const hSuccess = (data) => {
				setBlurLoading(false)
         if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
  
        setOpenSnackBar(true);
        setAlertMsg("Unexpected response received.");
      }
      };
  
      const hError = (error) => {
        setIsLoading(false);
        setAlertType("error");
        setAlertMsg("Error occurred while Validate.");
        setOpenSnackBar(true);
      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`,
        "POST",
        hSuccess,
        hError,
        Payload
      );
    };

  const handleValidateAndSyndicate = (type,remarks='',userInput='') => {
    const Payload = changePayloadForGL(              
    requestHeaderData,
    initialPayload,
    task,
    isreqBench,
    fetchReqBenchData,
    fetchedGeneralLedgerData,remarks,userInput,createChangeLogDataforChange)
    const hSuccess = (data) => {
         if (data?.statusCode === 200 || data?.statusCode === 201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === 500 || data?.statusCode === 501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
  
        setOpenSnackBar(true);
        setAlertMsg("Unexpected response received.");
      }
      };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      type === "VALIDATE"
        ? `/${destination_GeneralLedger_Mass}/massAction/validateMassGeneralLedger`
        : `/${destination_GeneralLedger_Mass}/massAction/changeGeneralLedgersApproved`,
      "POST",
      hSuccess,
      hError,
      Payload
    );
  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    const glAccCOACoCode = [];

      selectedCostCenters.forEach(gl => {

        glAccCOACoCode.push({
            glAccount: gl?.code,
            coa: gl?.coa,
            compCode: gl?.compCode,
            accountType: gl?.accType
        });
      })
        
    let payload ={
      "dtName": "MDG_GL_CHANGE_TEMPLATE_DT",
      "version": "v3",
      "templateHeaders": fieldNameList?.join(','),
      "templateName": requestHeaderData?.TemplateName,
      "requestId": requestHeaderData?.RequestId || initialPayload?.requestId || "",
      "GlAccount": glAccCOACoCode,
      "headers": fieldNameList
    }

    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const highlightedColumns = columns.map((col) => ({
    ...col,
    renderCell: (params) => {
      const isChanged =
        changedFieldsMap[params.row.CostCenterID]?.[col.field];
      return (
        <div
          style={{
            backgroundColor: isChanged ? "rgba(255, 229, 100, 0.6)" : "inherit",
            padding: "0 4px",
            borderRadius: 4,
            height: "100%",
            display: "flex",
            alignItems: "center",
          }}
        >
          {params.value}
        </div>
      );
    },
  }));

  const isChangeFieldEmpty = (changedFieldsMap) =>
    changedFieldsMap &&
    Object.values(changedFieldsMap).every(
      (fields) =>
        typeof fields === "object" &&
        fields !== null &&
        Object.keys(fields).length === 0
    );

  return (
    <div>
      <SuccessDialog
        open={successDialogOpen}
        onClose={() => setSuccessDialogOpen(false)}
        title={dialogData.title}
        message={dialogData.message}
        subText={dialogData.subText}
        buttonText={dialogData.buttonText}
        redirectTo={dialogData.redirectTo}
      />
      {showTableInDialog && (
        <ObjectLockDialog
          duplicateFieldsArr={duplicateFieldsData}
          moduleName={MODULE_MAP?.["GL"]}
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
        />
      )}
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {fetchedGeneralLedgerData?.length === 0 && reqBench!== 'true' && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} Search Filter(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "chartOfAccount",
                        label: "Chart Of Account",
                      }}
                      dropDownData={{
                        chartOfAccount: allDropDownData?.["COA"] || [],
                      }}
                      selectedValues={{
                        chartOfAccount: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                        setSelectedCostCenters([])
                        setSelectedAccountType([])
                        setSelectedCompanyCodes([])
                        getAccountType( value[0].code)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                        setSelectedCostCenters([])
                        setSelectedAccountType([])
                      }}
                      
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "accountType", label: "account type" }}
                      dropDownData={{ accountType: dropdownDataAccountType || [] }}
                      selectedValues={{
                        accountType: selectedAccountType.map(
                          (code) =>
                            dropdownDataAccountType?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                     
                      handleSelectionChange={(key, value) => {
                        setSelectedAccountType(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                        setSelectedCostCenters([])
                        getGlAcountDropdownDataFromSearchFilter(value)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "generalLedger", label: "general Ledger" }}
                      dropDownData={{
                        generalLedger: costCenterOptions
                      }}
                      selectedValues={{
                        generalLedger: selectedCostCenters?.map((item) => ({ code: item.code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters?.length ===
                          costCenterOptions?.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                     handleSelectionChange={(key, value) => {
                        const enrichedSelection = value.map((item) => {
                          if (typeof item === "string") {
                            return costCenterOptions.find((opt) => opt.code === item) || { code: item };
                          }
                          if (!item.desc || !item.coa || !item.accGroup || !item.accType) {
                            
                            return costCenterOptions.find((opt) => opt.code === item.code) || item;
                          }
                          return item;
                        });

                        setSelectedCostCenters(enrichedSelection);
                      }}
                     
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option?.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                         onClick={() => {
                          handleOk("OK");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                General ledger Lists
              </Typography>

              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedGeneralLedgerData}
                    columns={adjustedColumns}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={processRowUpdate}
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      !["costCenter", "companyCode"].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>

              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  Save as draft
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                  disabled={!isButtonEnabled}
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      <>
        {fetchReqBenchData.length === 0 && reqBench === "true" && (
          <>
            <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    {requestHeaderData?.TemplateName} Search Filter(s)
                  </Typography>
                </Box>

                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "chartOfAccount",
                        label: "Chart Of Account",
                      }}
                      dropDownData={{
                        chartOfAccount: allDropDownData?.["COA"] || [],
                      }}
                      selectedValues={{
                        chartOfAccount: dropdown1Value
                          ? [{ code: dropdown1Value }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setDropdown1Value(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                        setSelectedCostCenters([])
                        setSelectedAccountType([])
                        setSelectedCompanyCodes([])
                        getAccountType( value[0].code)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedCompanyCodes(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                        setSelectedCostCenters([])
                        setSelectedAccountType([])
                      }}
                      
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "accountType", label: "account type" }}
                      dropDownData={{ accountType: dropdownDataAccountType || [] }}
                      selectedValues={{
                        accountType: selectedAccountType.map(
                          (code) =>
                            dropdownDataAccountType?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                     
                      handleSelectionChange={(key, value) => {
                        setSelectedAccountType(
                          value.map((item) =>
                            typeof item === "string" ? item : item.code || item
                          )
                        );
                        setSelectedCostCenters([])
                        getGlAcountDropdownDataFromSearchFilter(value)
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>

                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "generalLedger", label: "general Ledger" }}
                      dropDownData={{
                        generalLedger: costCenterOptions
                      }}
                      selectedValues={{
                        generalLedger: selectedCostCenters?.map((item) => ({ code: item.code })) || [],
                      }}
                      handleSelectAll={(key) => {
                        if (
                          selectedCostCenters?.length ===
                          costCenterOptions?.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(costCenterOptions || []);
                        }
                      }}
                     handleSelectionChange={(key, value) => {
                        const enrichedSelection = value.map((item) => {
                          if (typeof item === "string") {
                            return costCenterOptions.find((opt) => opt.code === item) || { code: item };
                          }
                          if (!item.desc || !item.coa || !item.accGroup || !item.accType) {
                            
                            return costCenterOptions.find((opt) => opt.code === item.code) || item;
                          }
                          return item;
                        });

                        setSelectedCostCenters(enrichedSelection);
                      }}
                     
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option?.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>

                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                         onClick={() => {
                          handleOk("OK");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleDownloadDialogOpen();
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>

            <DownloadDialog
              onDownloadTypeChange={onDownloadTypeChange}
              open={openDownloadDialog}
              downloadType={downloadType}
              handleDownloadTypeChange={handleDownloadTypeChange}
              onClose={handleDownloadDialogClose}
            />
            <ReusableBackDrop
              blurLoading={blurLoading}
              loaderMessage={loaderMessage}
            />
          </>
        )}

         {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              General Ledger Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  rows={fetchReqBenchData ?? []}
                  columns={adjustedColumns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={processRowUpdateReqBench}
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
                moduleName ={moduleName}
              />
            </Box>
          </Box>
        )}
        
      </>
    </div>
  );
};

export default RequestDetailsChangeGL;
