import{bw as qs,r,n as oe,o as vt,s as Qs,g as Xs,a as Zs,b as Os,at as Ie,t as Ee,x as p,bx as ms,aC as Fs,by as js,bz as ws,bA as en,bB as tn,E as ln,z as de,C as L,j as l,c as x,aE as Yt,N as sn,S as Wt,O as E,Q as nn,bC as an,d as ne,U as rn,V as cn,W as on,i as dn,X as re,Y as N,$ as z,b3 as X,B as R,bD as Z,F as T,aa as O,bE as M,bF as ee,bG as te,a9 as m,T as F,a4 as Ht,a8 as Jt,h as un,a5 as gn,a6 as Kt,a7 as Cn,a1 as hn,a2 as fn,a3 as Sn,an as tt,br as xn,ab as qt,ad as Qt,af as An,ag as pn,ah as En,k as Tn,A as yn,bH as k,bI as Xt,w as Mn,aD as In,bJ as le,aK as Zt,J as Ot,bK as bn,aO as Nn}from"./index-226a1e75.js";import{d as _n}from"./History-09ae589c.js";import"./utilityImages-067c3dc2.js";import{R as Pn}from"./ReusablePresetFilter-da63464b.js";import"./SingleSelectDropdown-ee61a6b7.js";const Rn={singleETCCPayloadGI:{}},mt=qs({name:"costCenterET",initialState:Rn,reducers:{setSingleCostCenterETPayloadGI:(_,I)=>(_.singleETCCPayloadGI[I.payload.keyName]=I.payload.data,_),clearCostCenterPayload:_=>{_.singleETCCPayloadGI={}}}}),{setSingleCostCenterETPayloadGI:On,clearCostCenterPayload:$n}=mt.actions;mt.reducer;const mn=()=>{const[_,I]=r.useState(!1);r.useState(!1),r.useState(!1);const[Ft,lt]=r.useState(!1),[jt,wt]=r.useState(""),[el,tl]=r.useState(),[Dn,st]=r.useState(!1),[ll,zn]=r.useState([]),[nt,sl]=r.useState("");r.useState(!1),r.useState([]),oe(e=>e.AllDropDown.dropDown),r.useState([]),r.useState(!1),r.useState([]);const[at,nl]=r.useState(""),[rt,al]=r.useState(""),[it,rl]=r.useState(""),[v,be]=r.useState({}),[Y,Ne]=r.useState([]),[W,_e]=r.useState([]),[B,ue]=r.useState([]),[G,ge]=r.useState([]),[H,Pe]=r.useState([]),[j,Re]=r.useState([]),[J,Ce]=r.useState([]),[U,he]=r.useState([]),[$e,ct]=r.useState([]),[De,ot]=r.useState([]),[ze,Le]=r.useState([]),[ke,Be]=r.useState([]),[Ge,dt]=r.useState([]),[Ue,il]=r.useState([]),[Ve,ve]=r.useState([]),[Ye,We]=r.useState([]),[ut,He]=r.useState([]),[Je,Ke]=r.useState([]),[gt,cl]=r.useState(""),[Ct,ol]=r.useState(""),[ht,dl]=r.useState({}),[ft,ul]=r.useState(""),[St,gl]=r.useState(""),[xt,qe]=r.useState([]),[Cl,hl]=r.useState([]),{getDtCall:fl,dtData:fe}=vt(),{getDtCall:Sl,dtData:Se}=vt(),[Qe,xl]=r.useState([]),[Al,Ln]=r.useState(""),t=oe(e=>e.commonFilter.CostCenter),[K,xe]=r.useState([]),[V,Ae]=r.useState([]),[q,pe]=r.useState([]),h=Qs(),Xe=Xs(),[pl,At]=r.useState(0),[Ze,Oe]=r.useState(0),[me,El]=r.useState(10),Tl=oe(e=>e.applicationConfig),[kn,yl]=r.useState(0);r.useState(""),r.useState([]),r.useState([]),r.useState("ALL OTHER CHANGES"),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState("systemGenerated"),r.useState([]);const{t:b}=Zs(),[Fe,Ml]=r.useState(),Te=Os(),pt=r.useMemo(()=>B.length>0?B:ze.length>0?ze:[],[B,ze]),Et=r.useMemo(()=>V.length>0?V:Je.length>0?Je:[],[V,Je]),Tt=r.useMemo(()=>G.length>0?G:ke.length>0?ke:[],[G,ke]),yt=r.useMemo(()=>U.length>0?U:Ye.length>0?Ye:[],[U,Ye]),[Bn,Il]=r.useState([]),bl=Ie(Tn,{target:"em8t5865"})(({theme:e})=>({marginTop:"0px !important",border:`1px solid ${e.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),Nl=Ie(yn,{target:"em8t5864"})(({theme:e})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:e.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${e.palette.primary.light}20`}}),""),$=Ie(ne,{target:"em8t5863"})({fontSize:"0.75rem",color:Te.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500},""),_l=(e,n)=>{Oe(n)},Pl=r.useCallback(e=>{const n=e.target.value;El(n),Oe(0),yl(0)},[]),Mt=r.useMemo(()=>J.length>0?J:Ve.length>0?Ve:[],[J,Ve]),It=r.useMemo(()=>H.length>0?H:Ue.length>0?Ue:[],[H,Ue]),bt=r.useMemo(()=>j.length>0?j:Ge.length>0?Ge:[],[j,Ge]),Nt=r.useMemo(()=>Y.length>0?Y:$e.length>0?$e:[],[Y,$e]),_t=r.useMemo(()=>W.length>0?W:De.length>0?De:[],[W,De]),Rl=e=>{e!==null&&h(p({module:"CostCenter",filterData:{...t,createdOn:e}}))};Ee.useRef(!0),r.useState(!0),r.useState(!1),r.useState(!1),r.useState(!1),r.useState(!1),r.useState([]);const je=oe(e=>e.costCenter.singleCCPayload);oe(e=>{var n;return(n=e==null?void 0:e.profitCenter)==null?void 0:n.buttonsIDM});const $l=48,Dl=8,Pt={PaperProps:{style:{maxHeight:$l*4.5+Dl,width:250}}};r.useEffect(()=>{Object.keys(v).forEach(e=>{var u;const n=(u=v[e])==null?void 0:u.map(g=>g==null?void 0:g.code).join("$^$");let s={...t,[e]:n};h(p({module:"CostCenter",filterData:s}))})},[v]),r.useEffect(()=>{var e=K.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Company Code":e};h(p({module:"CostCenter",filterData:n}))},[K]),r.useEffect(()=>{var e=Y.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,createdBy:e};h(p({module:"CostCenter",filterData:n}))},[Y]),r.useEffect(()=>{var e=j.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,location:e};h(p({module:"CostCenter",filterData:n}))},[j]),r.useEffect(()=>{var e=J.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,costCenterName:e};h(p({module:"CostCenter",filterData:n}))},[J]),r.useEffect(()=>{var e=H.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,street:e};h(p({module:"CostCenter",filterData:n}))},[H]),r.useEffect(()=>{var e=W.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Profit Center":e};h(p({module:"CostCenter",filterData:n}))},[W]),r.useEffect(()=>{var e=B.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center":e};h(p({module:"CostCenter",filterData:n}))},[B]),r.useEffect(()=>{var e=U.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"User Responsible":e};h(p({module:"CostCenter",filterData:n}))},[U]),r.useEffect(()=>{var e=G.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,personResponsible:e};h(p({module:"CostCenter",filterData:n}))},[G]),r.useEffect(()=>{var e=q.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center Category":e};h(p({module:"CostCenter",filterData:n}))},[q]),r.useEffect(()=>{var e=V.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,description:e};h(p({module:"CostCenter",filterData:n}))},[V]),r.useEffect(()=>{Object.keys(v).forEach(e=>{var u;const n=(u=v[e])==null?void 0:u.map(g=>g==null?void 0:g.code).join("$^$");let s={...t,[e]:n};h(p({module:"CostCenter",filterData:s}))})},[v]),r.useEffect(()=>{var e=K.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Company Code":e};h(p({module:"CostCenter",filterData:n}))},[K]),r.useEffect(()=>{var e=Y.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,createdBy:e};h(p({module:"CostCenter",filterData:n}))},[Y]),r.useEffect(()=>{var e=j.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,location:e};h(p({module:"CostCenter",filterData:n}))},[j]),r.useEffect(()=>{var e=J.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,costCenterName:e};h(p({module:"CostCenter",filterData:n}))},[J]),r.useEffect(()=>{var e=H.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,street:e};h(p({module:"CostCenter",filterData:n}))},[H]),r.useEffect(()=>{var e=W.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Profit Center":e};h(p({module:"CostCenter",filterData:n}))},[W]),r.useEffect(()=>{var e=B.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center":e};h(p({module:"CostCenter",filterData:n}))},[B]),r.useEffect(()=>{var e=U.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"User Responsible":e};h(p({module:"CostCenter",filterData:n}))},[U]),r.useEffect(()=>{var e=G.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,personResponsible:e};h(p({module:"CostCenter",filterData:n}))},[G]),r.useEffect(()=>{var e=q.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center Category":e};h(p({module:"CostCenter",filterData:n}))},[q]),r.useEffect(()=>{var e=V.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,description:e};h(p({module:"CostCenter",filterData:n}))},[V]),r.useEffect(()=>{Object.keys(v).forEach(e=>{var u;const n=(u=v[e])==null?void 0:u.map(g=>g==null?void 0:g.code).join("$^$");let s={...t,[e]:n};h(p({module:"CostCenter",filterData:s}))})},[v]),r.useEffect(()=>{var e=K.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Company Code":e};h(p({module:"CostCenter",filterData:n}))},[K]),r.useEffect(()=>{var e=Y.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,createdBy:e};h(p({module:"CostCenter",filterData:n}))},[Y]),r.useEffect(()=>{var e=j.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,location:e};h(p({module:"CostCenter",filterData:n}))},[j]),r.useEffect(()=>{var e=J.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,costCenterName:e};h(p({module:"CostCenter",filterData:n}))},[J]),r.useEffect(()=>{var e=H.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,street:e};h(p({module:"CostCenter",filterData:n}))},[H]),r.useEffect(()=>{var e=W.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Profit Center":e};h(p({module:"CostCenter",filterData:n}))},[W]),r.useEffect(()=>{var e=B.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center":e};h(p({module:"CostCenter",filterData:n}))},[B]),r.useEffect(()=>{var e=U.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"User Responsible":e};h(p({module:"CostCenter",filterData:n}))},[U]),r.useEffect(()=>{var e=G.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,personResponsible:e};h(p({module:"CostCenter",filterData:n}))},[G]),r.useEffect(()=>{var e=q.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,"Cost Center Category":e};h(p({module:"CostCenter",filterData:n}))},[q]),r.useEffect(()=>{var e=V.map(s=>s==null?void 0:s.code).join("$^$");let n={...t,description:e};h(p({module:"CostCenter",filterData:n}))},[V]),r.useEffect(()=>{Ze*me>=(Q==null?void 0:Q.length)&&Ns()},[Ze,me]),r.useEffect(()=>{et()},[]),r.useState(!1);const[D,ie]=r.useState(null),[zl,ye]=r.useState(!1),[Ll,kl]=r.useState("1"),[Rt,Bl]=r.useState(""),[Q,$t]=r.useState([]),[se,Dt]=r.useState([]),[Gn,Gl]=r.useState([]),[Un,Ul]=r.useState({}),[w,zt]=r.useState({}),i=oe(e=>{var n;return(n=e==null?void 0:e.AllDropDown)==null?void 0:n.dropDown}),[Vl,Vn]=r.useState([...Q]);console.log("rmDataRows",Q),console.log("tableData",Vl),r.useState(""),r.useState(!1),r.useState(!1),r.useState(!0),r.useState("sm"),r.useState(!1),r.useState(!1),r.useState("");const[vl,vn]=r.useState("");r.useState(je==null?void 0:je.ControllingArea),r.useState(""),r.useState(""),r.useState(""),r.useState(!1),r.useState(!1),console.log("newCompanyCode",vl),r.useState(!1),r.useState([]),r.useState(!1),r.useState(!1),Ee.useRef(null),r.useState(0),r.useState(!1),r.useState(!1),Ee.useRef(null),Ee.useRef(null),r.useState(0),r.useState(0),r.useState(!0),r.useState([]),r.useState(!1),r.useState(""),r.useState([]),r.useState([]),r.useState([]),r.useState([]),r.useState([]);const[Yn,Yl]=r.useState([]);r.useState("ALL OTHER CHANGES"),r.useState([]),r.useState([]),r.useState("yes"),oe(e=>e.commonSearchBar.CostCenter);const Wl=()=>{K.length===i.CompanyCode.length?(xe([]),He([])):xe(i==null?void 0:i.CompanyCode)},Hl=()=>{var e;B.length===((e=i==null?void 0:i.ETCCSearchData)==null?void 0:e.length)?(ue([]),Le([])):(ue(i==null?void 0:i.ETCCSearchData),Yl(newSelected))},Jl=()=>{var e;J.length===((e=i==null?void 0:i.ETCCNameSearchData)==null?void 0:e.length)?(Ce([]),ve([])):Ce(i==null?void 0:i.ETCCNameSearchData)},Kl=e=>{const n=e.target.value;Bl(n),D&&clearTimeout(D)},ql=()=>{var e;G.length===((e=i==null?void 0:i.ETPersonResponsibleSearch)==null?void 0:e.length)?(ge([]),Be([])):ge(i==null?void 0:i.ETPersonResponsibleSearch)},Ql=()=>{var e;U.length===((e=i==null?void 0:i.ETUserResponsible)==null?void 0:e.length)?(he([]),We([])):he(i==null?void 0:i.ETUserResponsible)},Xl=()=>{V.length===(i==null?void 0:i.ETDescriptionSearchData.length)?(Ae([]),Ke([])):Ae(i==null?void 0:i.ETDescriptionSearchData)},Zl=()=>{q.length===(i==null?void 0:i.CostCenterCategorySearch.length)?(pe([]),qe([])):pe(i==null?void 0:i.CostCenterCategorySearch)},Ol=(e,n)=>{{var s=n;let u={...t,controllingArea:s};h(p({module:"CostCenter",filterData:u}))}},ml=e=>{const n=e.target.value;if(ul(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{getStreet(n)},500);ie(s)}},Fl=e=>{const n=e.target.value;if(cl(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{getProfitCenterSearch(n)},500);ie(s)}},jl=e=>{const n=e.target.value;if(ol(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{getLocation(n)},500);ie(s)}},wl=e=>{const n=e.target.value;if(gl(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{getCreatedBy(n)},500);ie(s)}};`& .${ms.tooltip}`+"";const es=e=>{if(e.target.value!==null){var n=e.target.value;let s={...t,blockingStatus:n};h(p({module:"CostCenter",filterData:s}))}};let ts={"FERC Indicator":`/${k}/data/getSearchParamsFercInd`,"Functional Area":`/${k}/data/getSearchParamsFuncArea`,"Country/Reg":`/${k}/data/getSearchParamsCountryReg`};const ls=e=>{const n=e.target.value;Dt(n),Gl([]),n.forEach(async s=>{const u=ts[s];bs(u,s)})},ss=e=>K.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),ns=e=>B.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),as=e=>G.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),rs=e=>U.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),is=e=>q.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),cs=e=>V.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),os=e=>(console.log(e,"isoptionselected"),Y.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code))),ds=e=>(console.log(e,"isoptionselected"),H.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code))),us=e=>(console.log(e,"isoptionselected"),W.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code))),gs=(e,n)=>{var s;return(s=v[e])==null?void 0:s.some(u=>(u==null?void 0:u.code)===(n==null?void 0:n.code))},Cs={"Short Description":"CostCenterName","FERC Indicator":"fercIndicator","Functional Area":"functionalArea","Profit Center":"ProfitCenter",Street:"street",Location:"location","Country/Reg":"countryreg",Region:"region","Created On":"createdOn","Created By":"createdBy"},we=["Blocked","Unblocked",""],hs=()=>{var u,g,S;I(!0);const e={controllingArea:(u=t==null?void 0:t.controllingArea)!=null&&u.code?((g=t==null?void 0:t.controllingArea)==null?void 0:g.code)===""?"ETCA":(S=t==null?void 0:t.controllingArea)==null?void 0:S.code:"ETCA",rolePrefix:"ETP",top:200,skip:0},n=f=>{h(le({keyName:"CompCodeSearch",data:f.body})),I(!1)},s=f=>{console.log(f)};L(`/${k}/data/getSearchParamsCompCode`,"post",n,s,e)},fs=()=>{I(!0);const e=s=>{h(le({keyName:"ControllingArea",data:s.body})),I(!1)},n=s=>{console.log(s)};L(`/${k}/data/getSearchParamsConArea`,"get",e,n)},Ss=e=>{var g,S,f;I(!0);const n={controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",rolePrefix:"ETP",userRespons:e,top:200,skip:0},s=c=>{h(le({keyName:"ETUserResponsible",data:c.body})),I(!1)},u=c=>{console.log(c)};L(`/${k}/data/getSearchParamsUserRespons`,"post",s,u,n)},xs=e=>{var g,S,f;I(!0);const n={controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",rolePrefix:"ETP",personRespons:e,top:200,skip:0},s=c=>{h(le({keyName:"ETPersonResponsibleSearch",data:c.body})),I(!1)},u=c=>{console.log(c)};L(`/${k}/data/getSearchParamsPersonRespons`,"post",s,u,n)},As=()=>{var u,g,S;I(!0);const e={controllingArea:(u=t==null?void 0:t.controllingArea)!=null&&u.code?((g=t==null?void 0:t.controllingArea)==null?void 0:g.code)===""?"ETCA":(S=t==null?void 0:t.controllingArea)==null?void 0:S.code:"ETCA",rolePrefix:"ETP",top:200,skip:0},n=f=>{h(le({keyName:"CostCenterCategorySearch",data:f.body})),I(!1)},s=f=>{console.log(f)};L(`/${k}/data/getSearchParamsCCCategory`,"post",n,s,e)},ps=e=>{var g,S,f;I(!0);let n={controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",rolePrefix:"ETP",description:e,top:200,skip:0};const s=c=>{h(le({keyName:"ETDescriptionSearchData",data:c.body})),I(!1)},u=c=>{console.log(c)};L(`/${k}/data/getSearchParamsDescription`,"post",s,u,n)},Es=e=>{var g,S,f;I(!0);let n={costCenter:e,controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",rolePrefix:"ETP",top:200,skip:0};const s=c=>{h(le({keyName:"ETCCSearchData",data:c.body})),I(!1)},u=c=>{console.log(c)};L(`/${k}/data/getSearchParamsCostCenter`,"post",s,u,n)},Ts=()=>{const e=s=>{h(le({keyName:"CostCenter",data:s.body}))},n=s=>{console.log(s)};L(`/${k}/data/getCostCenter`,"get",e,n)},ys=()=>{const e=s=>{h(le({keyName:"FunctionalArea",data:s.body}))},n=s=>{console.log(s)};L(`/${k}/data/getFunctionalArea`,"get",e,n)},Ms=()=>{var u,g,S;I(!0);const e={controllingArea:(u=t==null?void 0:t.controllingArea)!=null&&u.code?((g=t==null?void 0:t.controllingArea)==null?void 0:g.code)===""?"ETCA":(S=t==null?void 0:t.controllingArea)==null?void 0:S.code:"ETCA",rolePrefix:"ETP",top:200,skip:0},n=f=>{zt(c=>({...c,Region:f.body})),I(!1)},s=f=>{console.log(f)};L(`/${k}/data/getSearchParamsRegion`,"post",n,s,e)},Is=()=>{let e={decisionTableId:null,decisionTableName:"MDG_CUSTOM_DROPDOWN_LIST",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":"Cost Center","MDG_CONDITIONS.MDG_FIELD_NAME":"Controlling Area"}],systemFilters:null,systemOrders:null,filterString:null};const n=u=>{var g,S;if(u.statusCode===200){const f=((S=(g=u==null?void 0:u.data)==null?void 0:g.result[0])==null?void 0:S.MDG_CUSTOM_LOOKUP_ACTION_TYPE)||[];console.log("questionData",f);let c=[];f==null||f.map(o=>{let C={};C.code=o==null?void 0:o.MDG_LOOKUP_CODE,C.desc=o==null?void 0:o.MDG_LOOKUP_DESC,c.push(C)}),h(le({keyName:"NewControllingArea",data:c}))}},s=u=>{console.log(u)};Tl.environment==="localhost"?L(`/${Xt}/rest/v1/invoke-rules`,"post",n,s,e):L(`/${Xt}/v1/invoke-rules`,"post",n,s,e)};r.useEffect(()=>{As(),Is(),fs(),Ms(),ys(),Ts(),hs(),h(Fs({})),h(js()),h(ws({})),h(en()),h(tn()),h($n()),h(ln())},[]);const bs=(e,n)=>{var S,f,c,o,C,a,d,y,A;I(!0);let s;n==="FERC Indicator"?s={controllingArea:(S=t==null?void 0:t.controllingArea)!=null&&S.code?((f=t==null?void 0:t.controllingArea)==null?void 0:f.code)===""?"ETCA":(c=t==null?void 0:t.controllingArea)==null?void 0:c.code:"ETCA",rolePrefix:"ETP",fercInd:"",top:200,skip:0}:n==="Functional Area"?s={controllingArea:(o=t==null?void 0:t.controllingArea)!=null&&o.code?((C=t==null?void 0:t.controllingArea)==null?void 0:C.code)===""?"ETCA":(a=t==null?void 0:t.controllingArea)==null?void 0:a.code:"ETCA",rolePrefix:"ETP",funcArea:"",top:200,skip:0}:s={controllingArea:(d=t==null?void 0:t.controllingArea)!=null&&d.code?((y=t==null?void 0:t.controllingArea)==null?void 0:y.code)===""?"ETCA":(A=t==null?void 0:t.controllingArea)==null?void 0:A.code:"ETCA",rolePrefix:"ETP",top:200,skip:0},L(e,"post",P=>{const ae=P.body;zt(ce=>({...ce,[n]:ae})),I(!1)},P=>{console.log(P)},s)},Lt=new Date;Lt.setDate(Lt.getDate()-15);const et=e=>{var g,S,f;ye(!0),Oe(0);let n={costCenterName:(t==null?void 0:t.costCenterName)??"",costCenter:(t==null?void 0:t["Cost Center"])??"",controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",companyCode:(t==null?void 0:t["Company Code"])??"",profitCenter:(t==null?void 0:t["Profit Center"])??"",hierarchyArea:(t==null?void 0:t["Hierarchy Area"])??"",rolePrefix:"ETP",costCenterCategory:(t==null?void 0:t["Cost Center Category"])??"",createdBy:(t==null?void 0:t.createdBy)??"",fromDate:de(t==null?void 0:t.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:de(t==null?void 0:t.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",businessArea:"",personResponsible:(t==null?void 0:t.personResponsible)??"",userResponsible:(t==null?void 0:t["User Responsible"])??"",functionalArea:(t==null?void 0:t["Functional Area"])??"",fercIndicator:(t==null?void 0:t["FERC Indicator"])??"",street:(t==null?void 0:t.street)??"",location:(t==null?void 0:t.location)??"",description:(t==null?void 0:t.description)??"",country:(t==null?void 0:t["Country/Reg"])??"",region:(t==null?void 0:t.Region)??"",blockingStatus:(t==null?void 0:t.blockingStatus)==="Blocked"?"X":(t==null?void 0:t.blockingStatus)==="Unblocked"?"Y":"",top:100,skip:0};const s=c=>{var d,y,A,P;if(c.statusCode===200){var o=[];for(let ae=0;ae<((y=(d=c==null?void 0:c.body)==null?void 0:d.list)==null?void 0:y.length);ae++){var C=(A=c==null?void 0:c.body)==null?void 0:A.list[ae],a={id:Zt(),description:C==null?void 0:C.Description,controllingArea:C==null?void 0:C.controllingArea,companyCode:C==null?void 0:C.CompanyCode,hierarchyArea:C==null?void 0:C.HeirarchyArea,costCenterCategory:C==null?void 0:C.CCtrCategory,costCenter:C==null?void 0:C.costCenter,CostCenterName:C==null?void 0:C.CostCenterName,businessArea:C.BusinessArea!==""?`${C.BusinessArea}`:"Not Available",functionalArea:C.FunctionalArea!==""?`${C.FunctionalArea}`:"Not Available",personResponsible:C.PersonResponsible!==""?`${C.PersonResponsible}`:"Not Available",userResponsible:C.UserResponsible!==""?`${C.UserResponsible}`:"Not Available",fercIndicator:C.FERCindicator!==""?`${C.FERCindicator}`:"Not Available",street:C.Street!==""?`${C.Street}`:"Not Available",location:C.Location!==""?`${C.Location}`:"Not Available",countryreg:C.Country!==""?`${C.Country}`:"Not Available",region:C.Region!==""?`${C.Region}`:"Not Available",createdOn:C.CreatedOn!==""?`${de(C.CreatedOn).format("DD MMM YYYY")}`:"Not Available",createdBy:C.CreatedBy!==""?`${C.CreatedBy}`:"Not Available",ProfitCenter:C.ProfitCenter!==""?`${C.ProfitCenter}`:"Not Available",blockingStatus:C.BlockingStatus==="X"?"Blocked":"Unblocked"};o.push(a)}$t(o),ye(!1),kt(o.length),At((P=c==null?void 0:c.body)==null?void 0:P.count)}else c.statusCode===400&&(wt("Warning"),tl("Please Select Lesser Fields as the URL is getting too long !!"),Ut())},u=c=>{console.log(c)};L(`/${k}/data/getCostCentersBasedOnAdditionalParams`,"post",s,u,n)},Ns=e=>{var g,S,f,c;ye(!0);let n={costCenterName:(t==null?void 0:t.costCenterName)??"",costCenter:(t==null?void 0:t["Cost Center"])??"",rolePrefix:"ETP",controllingArea:(g=t==null?void 0:t.controllingArea)!=null&&g.code?((S=t==null?void 0:t.controllingArea)==null?void 0:S.code)===""?"ETCA":(f=t==null?void 0:t.controllingArea)==null?void 0:f.code:"ETCA",companyCode:(t==null?void 0:t["Company Code"])??"",profitCenter:(t==null?void 0:t["Profit Center"])??"",hierarchyArea:((c=t==null?void 0:t["Hierarchy Area"])==null?void 0:c.code)??"",costCenterCategory:(t==null?void 0:t["Cost Center Category"])??"",createdBy:(t==null?void 0:t.createdBy)??"",fromDate:de(t==null?void 0:t.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:de(t==null?void 0:t.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",businessArea:"",personResponsible:(t==null?void 0:t.personResponsible)??"",userResponsible:(t==null?void 0:t["User Responsible"])??"",functionalArea:(t==null?void 0:t["Functional Area"])??"",fercIndicator:(t==null?void 0:t["FERC Indicator"])??"",street:(t==null?void 0:t.street)??"",location:(t==null?void 0:t.location)??"",description:(t==null?void 0:t.description)??"",country:(t==null?void 0:t["Country/Reg"])??"",region:(t==null?void 0:t.Region)??"",blockingStatus:(t==null?void 0:t.blockingStatus)==="Blocked"?"X":(t==null?void 0:t.blockingStatus)==="Unblocked"?"Y":"",top:100,skip:(Q==null?void 0:Q.length)??0};const s=o=>{var y,A,P,ae;var C=[];for(let ce=0;ce<((A=(y=o==null?void 0:o.body)==null?void 0:y.list)==null?void 0:A.length);ce++){var a=(P=o==null?void 0:o.body)==null?void 0:P.list[ce],d={id:Zt(),description:a==null?void 0:a.Description,controllingArea:a==null?void 0:a.controllingArea,companyCode:a==null?void 0:a.CompanyCode,hierarchyArea:a==null?void 0:a.HeirarchyArea,costCenterCategory:a==null?void 0:a.CCtrCategory,costCenter:a==null?void 0:a.costCenter,CostCenterName:a==null?void 0:a.CostCenterName,businessArea:a.BusinessArea!==""?`${a.BusinessArea}`:"Not Available",functionalArea:a.FunctionalArea!==""?`${a.FunctionalArea}`:"Not Available",personResponsible:a.PersonResponsible!==""?`${a.PersonResponsible}`:"Not Available",userResponsible:a.UserResponsible!==""?`${a.UserResponsible}`:"Not Available",fercIndicator:a.FERCindicator!==""?`${a.FERCindicator}`:"Not Available",street:a.Street!==""?`${a.Street}`:"Not Available",location:a.Location!==""?`${a.Location}`:"Not Available",countryreg:a.Country!==""?`${a.Country}`:"Not Available",region:a.Region!==""?`${a.Region}`:"Not Available",createdOn:a.CreatedOn!==""?`${de(a.CreatedOn).format("DD MMM YYYY")}`:"Not Available",createdBy:a.CreatedBy!==""?`${a.CreatedBy}`:"Not Available",ProfitCenter:a.ProfitCenter!==""?`${a.ProfitCenter}`:"Not Available",blockingStatus:a.BlockingStatus==="X"?"Blocked":"Unblocked"};C.push(d)}$t(ce=>[...ce,...C]),ye(!1),kt(C.length),At((ae=o==null?void 0:o.body)==null?void 0:ae.count)},u=o=>{console.log(o)};L(`/${k}/data/getCostCentersBasedOnAdditionalParams`,"post",s,u,n)},[Wn,kt]=r.useState(0),[_s,Bt]=r.useState(!1),[Ps,Hn]=r.useState(""),[Rs,Jn]=r.useState(),$s=()=>{Bt(!0)},Gt=()=>{Bt(!1)},Ut=()=>{lt(!0)},Vt=()=>{lt(!1)},Ds=()=>{h(Mn({module:"CostCenter"})),pe([]),xe([]),ge([]),he([]),_e([]),ue([]),Ce([]),Re([]),Ae([]),Ne([]),Pe([]),be({}),qe([]),He([]),Be([]),We([]),ot([]),Le([]),ve([]),dt([]),Ke([]),ct([]),Ul({})},zs=e=>{let n=Me==null?void 0:Me.map(g=>g.field);const s=Q.filter(g=>e.includes(g.id));let u=[];s==null||s.map(g=>{let S={};n.forEach(f=>{g[f]!==null&&(S[f]=g[f]||"")}),S.controllingArea=g.controllingArea,u.push(S),Il(u)})};r.useState([]),r.useState([]),r.useState(!1),r.useState(null),r.useState(null),r.useState([]),r.useState(null),r.useState("");const Ls=(e,n)=>({field:e,headerName:n,editable:!1,flex:1,renderCell:s=>{const u=s.value?s.value.split(",").map(f=>f.trim()):[],g=u.length-1;if(u.length===0)return"-";const S=f=>{const[c,...o]=f.split("-");return x(T,{children:[l("strong",{children:c}),o.length?` - ${o.join("-")}`:""]})};return x(R,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[l(F,{title:u[0],placement:"top",arrow:!0,children:l(ne,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:S(u[0])})}),g>0&&l(R,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:l(F,{arrow:!0,placement:"right",title:x(R,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[x(ne,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",n,"s (",g,")"]}),u.slice(1).map((f,c)=>l(ne,{variant:"body2",sx:{mb:.5},children:S(f)},c))]}),children:x(R,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[l(InfoIcon,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),x(ne,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",g]})]})})})]})}}),ks=(e,n)=>({field:e,headerName:n,editable:!1,flex:1,renderCell:s=>{var S;const[u,...g]=((S=s.value)==null?void 0:S.split(" - "))||[];return x("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[l("strong",{children:u})," ",g.length?`- ${g.join(" - ")}`:""]})}}),Bs=()=>({field:"dataValidation",headerName:b("Audit History"),editable:!1,flex:1,renderCell:e=>l(R,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:l(F,{title:"View Audit Log",placement:"top",children:l(Kt,{onClick:s=>{var u,g;s.stopPropagation(),Xe((u=bn)==null?void 0:u.AUDIT_LOG,{state:{materialNumber:e.row.costCenter,module:(g=Nn)==null?void 0:g.CC}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:l(_n,{sx:{fontSize:"1.5rem"}})})})})}),Gs=()=>{console.log("Error");let e={decisionTableId:null,decisionTableName:Ot.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Cost Center","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};fl(e)},Us=()=>{let e={decisionTableId:null,decisionTableName:Ot.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Cost Center","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};Sl(e)},Me=[{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"costCenter",headerName:"Cost Center",editable:!1,flex:1},{field:"description",headerName:"Long Description",editable:!1,flex:1},{field:"costCenterCategory",headerName:"Cost Center Category",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"userResponsible",headerName:"User Responsible",editable:!1,flex:1},{field:"blockingStatus",headerName:"Blocking Status",editable:!1,flex:1}],Vs=se==null?void 0:se.map(e=>{const n=Cs[e];return n?{field:n,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null);[...Me,...Vs];const vs=e=>{const n=[];let s=(e==null?void 0:e.sort((u,g)=>u.MDG_MAT_SEQUENCE_NO-g.MDG_MAT_SEQUENCE_NO))||[];return s&&(s==null||s.forEach(u=>{if((u==null?void 0:u.MDG_MAT_VISIBILITY)===In.DISPLAY&&u!=null&&u.MDG_MAT_UI_FIELD_NAME){const g=u.MDG_MAT_JSON_FIELD_NAME,S=u.MDG_MAT_UI_FIELD_NAME;g==="DataValidation"?n.push(Bs()):u.MDG_MAT_FIELD_TYPE==="Multiple"?n.push(Ls(g,S)):u.MDG_MAT_FIELD_TYPE==="Single"&&n.push(ks(g,S))}})),n};r.useEffect(()=>{var e,n,s,u;if(fe){const g=vs((n=(e=fe==null?void 0:fe.result)==null?void 0:e[0])==null?void 0:n.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);hl(g)}if(Se){const g=(u=(s=Se==null?void 0:Se.result)==null?void 0:s[0])==null?void 0:u.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,S=g==null?void 0:g.filter(f=>f.MDG_MAT_FILTER_TYPE==="Additional").map(f=>({title:f.MDG_MAT_UI_FIELD_NAME}));xl(g),Ml(S)}},[fe,Se]),r.useEffect(()=>{Gs(),Us()},[]);let Ys=r.useRef(null);const Ws=e=>{const n=e.target.value;if(sl(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{ps(n)},500);ie(s)}},Hs=e=>{const n=e.target.value;if(nl(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{Ss(n)},500);ie(s)}},Js=e=>{const n=e.target.value;if(al(n),D&&clearTimeout(D),n.length>=4){const s=setTimeout(()=>{xs(n)},500);ie(s)}},Ks=e=>{const n=e.target.value;rl(n),n.length>=4&&setTimeout(()=>{Es(n)},500)};return r.useCallback(e=>{st(e)},[st]),l(T,{children:x("div",{ref:Ys,children:[l(Yt,{dialogState:_s,openReusableDialog:$s,closeReusableDialog:Gt,dialogTitle:Ps,dialogMessage:Rs,handleDialogConfirm:Gt,dialogOkText:"OK",dialogSeverity:Al}),l(Yt,{dialogState:Ft,openReusableDialog:Ut,closeReusableDialog:Vt,dialogTitle:jt,dialogMessage:el,handleDialogConfirm:Vt,dialogSeverity:"danger",showCancelButton:!1,dialogOkText:"OK"}),l("div",{style:{...sn,backgroundColor:"#FAFCFF"},children:x(Wt,{spacing:1,children:[l(E,{container:!0,sx:nn,children:x(E,{item:!0,md:5,sx:an,children:[l(ne,{variant:"h3",children:l("strong",{children:b("Cost Center")})}),l(ne,{variant:"body2",color:"#777",children:b("This view displays the list of Cost Centers")})]})}),l(E,{container:!0,sx:rn,children:l(E,{item:!0,md:12,children:x(bl,{defaultExpanded:!0,className:"filterCC",children:[x(Nl,{expandIcon:l(cn,{sx:{fontSize:"1.25rem",color:Te.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[l(on,{sx:{fontSize:"1.25rem",marginRight:1,color:Te.palette.primary.dark}}),l(ne,{sx:{fontSize:"0.875rem",fontWeight:600,color:Te.palette.primary.dark},children:b("Filter Cost Center")})]}),x(dn,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[x(E,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[x(E,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[Qe==null?void 0:Qe.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,n)=>e.MDG_MAT_SEQUENCE_NO-n.MDG_MAT_SEQUENCE_NO).map((e,n)=>{var s,u,g,S,f;return x(Ee.Fragment,{children:[(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.CONTROLINGAREA&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{size:"small",fullWidth:!0,children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",value:t==null?void 0:t.controllingArea,onChange:Ol,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",options:(i==null?void 0:i.ControllingArea)??[],getOptionLabel:c=>c!=null&&c.code?`${c==null?void 0:c.code}`??"":"",renderOption:(c,o)=>l("li",{...c,children:l(ne,{style:{fontSize:12},children:o!=null&&o.desc?x(T,{children:[l("strong",{children:o.code})," -"," ",o.desc]}):l("strong",{children:o.code})})}),renderInput:c=>l(O,{sx:{fontSize:"12px !important"},...c,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.COSTCENTER&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",sx:{paddingBottom:"0.7rem"},children:l(X,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:pt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){ue([]),Le([]);return}o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?Hl():ue(o)},renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(s=i==null?void 0:i.ETCCSearchData)!=null&&s.length?[{code:"Select All",desc:"Select All"},...i==null?void 0:i.ETCCSearchData]:(i==null?void 0:i.ETCCSearchData)??[],getOptionLabel:c=>c!=null&&c.code?`${c==null?void 0:c.code}`??"":"",renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:ns(o)||(o==null?void 0:o.code)==="Select All"&&(B==null?void 0:B.length)===((a=i==null?void 0:i.ETCCSearchData)==null?void 0:a.length)}),label:x(T,{children:[l("strong",{children:o.code})," -"," ",o.desc]})})})})},renderInput:c=>l(F,{title:it.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:it.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:pt.length===0?"Select Cost Center":"",onChange:o=>{Ks(o)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.COMPANYCODE&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",sx:{paddingBottom:"0.7rem"},children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,limitTags:1,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",options:[{code:"Select All",desc:"Select All"},...(i==null?void 0:i.CompanyCode)??[]],disableCloseOnSelect:!0,onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){xe([]),He([]);return}console.log(o,"valueinauto"),o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?Wl():xe(o)},getOptionLabel:c=>c!=null&&c.code?`${c==null?void 0:c.code}`??"":"",value:K.length>0?K:ut.length>0?ut:[],renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:ss(o)||(o==null?void 0:o.code)==="Select All"&&(K==null?void 0:K.length)===((a=i==null?void 0:i.CompanyCode)==null?void 0:a.length)}),label:x(T,{children:[l("strong",{children:o.code})," -"," ",o.desc]})})})})},renderInput:c=>l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:"Select Company Code"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.LONGDESC&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Et,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){Ae([]),Ke([]);return}o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?Xl():Ae(o)},limitTags:1,options:(u=i==null?void 0:i.ETDescriptionSearchData)!=null&&u.length?[{code:"Select All"},...i==null?void 0:i.ETDescriptionSearchData]:(i==null?void 0:i.ETDescriptionSearchData)??[],getOptionLabel:c=>c!=null&&c.code?(c==null?void 0:c.code)??"":"",renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:cs(o)||(o==null?void 0:o.code)==="Select All"&&(V==null?void 0:V.length)===((a=i==null?void 0:i.ETDescriptionSearchData)==null?void 0:a.length)}),label:l(T,{children:l("strong",{children:o.code})})})})})},renderInput:c=>l(F,{title:nt.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:nt.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:Et.length===0?"Select Long Description":"",onChange:o=>{Ws(o)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.COSTCENTERCAT&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:q.length>0?q:xt.length>0?xt:[],noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){pe([]),qe([]);return}o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?Zl():pe(o)},limitTags:1,options:(g=i==null?void 0:i.CostCenterCategorySearch)!=null&&g.length?[{code:"Select All",desc:"Select All"},...i==null?void 0:i.CostCenterCategorySearch]:(i==null?void 0:i.CostCenterCategorySearch)??[],getOptionLabel:c=>c!=null&&c.code?`${c==null?void 0:c.code}`??"":"",renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:is(o)||(o==null?void 0:o.code)==="Select All"&&(q==null?void 0:q.length)===((a=i==null?void 0:i.CostCenterCategorySearch)==null?void 0:a.length)}),label:x(T,{children:[l("strong",{children:o.code})," -"," ",o.desc]})})})})},renderInput:c=>l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:"Select Cost Center Category"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.PERSONRES&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Tt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){ge([]),Be([]);return}o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?ql():ge(o)},renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(S=i==null?void 0:i.ETPersonResponsibleSearch)!=null&&S.length?[{code:"Select All"},...i==null?void 0:i.ETPersonResponsibleSearch]:(i==null?void 0:i.ETPersonResponsibleSearch)??[],getOptionLabel:c=>c!=null&&c.code?(c==null?void 0:c.code)??"":"",renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:as(o)||(o==null?void 0:o.code)==="Select All"&&(G==null?void 0:G.length)===((a=i==null?void 0:i.ETPersonResponsibleSearch)==null?void 0:a.length)}),label:l(T,{children:l("strong",{children:o.code})})})})})},renderInput:c=>l(F,{title:rt.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:rt.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:Tt.length===0?"Select Person Responsible":"",onChange:o=>{Js(o)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.USERRES&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{size:"small",fullWidth:!0,sx:{paddingBottom:"0.7rem"},children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:yt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",onChange:(c,o,C)=>{var a;if(C==="clear"||(o==null?void 0:o.length)===0){he([]),We([]);return}o.length>0&&((a=o[o.length-1])==null?void 0:a.code)==="Select All"?Ql():he(o)},limitTags:1,options:(f=i==null?void 0:i.ETUserResponsible)!=null&&f.length?[{code:"Select All"},...i==null?void 0:i.ETUserResponsible]:(i==null?void 0:i.ETUserResponsible)??[],getOptionLabel:c=>c!=null&&c.code?(c==null?void 0:c.code)??"":"",renderOption:(c,o,{selected:C})=>{var a;return l("li",{...c,children:l(ee,{children:l(te,{control:l(m,{checked:rs(o)||(o==null?void 0:o.code)==="Select All"&&(U==null?void 0:U.length)===((a=i==null?void 0:i.ETUserResponsible)==null?void 0:a.length)}),label:l(T,{children:l("strong",{children:o.code})})})})})},renderTags:(c,o)=>c.length>0?x(T,{children:[l(M,{label:c[0].code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),c.length>1&&l(M,{label:`+${c.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,renderInput:c=>l(F,{title:at.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:at.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...c,variant:"outlined",placeholder:yt.length===0?"Select User Responsible":"",onChange:o=>{Hs(o)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===re.BLOCKINGSAT&&x(E,{item:!0,md:2,children:[l($,{sx:N,children:b(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l(z,{fullWidth:!0,size:"small",children:l(Ht,{placeholder:"Select Blocking Status",sx:{height:"35px"},size:"small",value:t==null?void 0:t.blockingStatus,name:"blockingStatus",onChange:c=>es(c),displayEmpty:!0,MenuProps:Pt,children:we==null?void 0:we.map(c=>l(Jt,{sx:N,value:c,style:{fontSize:"12px !important",height:"35px"},children:l(un,{sx:N,primary:c,style:{fontSize:"12px !important"}})},c))})})]})]},n)}),x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Add New Filters")}),l(z,{fullWidth:!0,children:l(Ht,{sx:{font_Small:N,height:"35px",fontSize:"12px"},size:"small",multiple:!0,limitTags:2,value:se,onChange:ls,renderValue:e=>e.join(", "),MenuProps:{MenuProps:Pt},endAdornment:se.length>0&&l(gn,{position:"end",sx:{marginRight:"15px"},children:l(Kt,{size:"small",sx:{height:"10px",width:"10px"},onClick:()=>{Dt([])},"aria-label":"Clear selections",children:l(Cn,{})})}),children:Fe==null?void 0:Fe.map(e=>x(Jt,{value:e.title,children:[l(m,{checked:se.indexOf(e.title)>-1}),e.title]},e.title))})}),l(E,{style:{display:"flex",justifyContent:"space-around"}})]})]}),l(E,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",sx:{padding:"0.5rem 1rem 0.5rem"},children:l(E,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:se==null?void 0:se.map((e,n)=>{var s,u,g,S,f,c,o,C;return console.log("fercvalue",e,t[e],t),e==="Short Description"?l(T,{children:x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Short Description")}),l(z,{fullWidth:!0,size:"small",sx:{paddingBottom:"0.7rem"},children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Mt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){Ce([]),ve([]);return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?Jl():Ce(d)},limitTags:1,options:(s=i==null?void 0:i.ETCCNameSearchData)!=null&&s.length?[{code:"Select All"},...i==null?void 0:i.ETCCNameSearchData]:(i==null?void 0:i.ETCCNameSearchData)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,d,{selected:y})=>{var A;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:isCostCenterNameSelected(d)||(d==null?void 0:d.code)==="Select All"&&(J==null?void 0:J.length)===((A=i==null?void 0:i.ETCCNameSearchData)==null?void 0:A.length)}),label:l(T,{children:l("strong",{children:d.code})})})})})},renderInput:a=>l(F,{title:Rt.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:Rt.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:Mt.length===0?"Select Short Description":"",onChange:d=>{Kl(d)}})})})})]})}):e==="Created On"?x(E,{item:!0,md:2,children:[l($,{sx:N,children:e}),l(z,{size:"small",fullWidth:!0,children:l(hn,{dateAdapter:fn,children:l(Sn,{handleDate:Rl,date:ll})})})]}):e==="Street"?x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Street")}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:It,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",limitTags:1,renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){Pe([]),il([]);return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?handleSelectAllStreet():Pe(d)},options:(u=i==null?void 0:i.ETStreetSearchData)!=null&&u.length?[{code:"Select All"},...i==null?void 0:i.ETStreetSearchData]:(i==null?void 0:i.ETStreetSearchData)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,d,{selected:y})=>{var A;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:ds(d)||(d==null?void 0:d.code)==="Select All"&&(H==null?void 0:H.length)===((A=i==null?void 0:i.ETStreetSearchData)==null?void 0:A.length)}),label:l(T,{children:l("strong",{children:d.code})})})})})},renderInput:a=>l(F,{title:ft.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:ft.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:It.length===0?"Select Street":"",onChange:d=>{ml(d)}})})})})]}):e==="Location"?l(T,{children:x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Location")}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:bt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){Re([]),dt([]);return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?handleSelectAllLocation():Re(d)},options:(g=i==null?void 0:i.ETLocationSearchData)!=null&&g.length?[{code:"Select All"},...i==null?void 0:i.ETLocationSearchData]:(i==null?void 0:i.ETLocationSearchData)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,d,{selected:y})=>{var A;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:isLocationSelected(d)||(d==null?void 0:d.code)==="Select All"&&(j==null?void 0:j.length)===((A=i==null?void 0:i.ETLocationSearchData)==null?void 0:A.length)}),label:l(T,{children:l("strong",{children:d.code})})})})})},renderInput:a=>l(F,{title:Ct.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:Ct.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:bt.length===0?"Select Location":"",onChange:d=>{jl(d)}})})})})]})}):e==="Created By"?l(T,{children:x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Created By")}),l(z,{fullWidth:!0,size:"small",sx:{paddingBottom:"0.7rem"},children:l(X,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:Nt,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){Ne([]),ct([]);return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?handleSelectAllCreatedBy():Ne(d)},limitTags:1,options:(S=i==null?void 0:i.ETCreatedBySearchData)!=null&&S.length?[{code:"Select All"},...i==null?void 0:i.ETCreatedBySearchData]:(i==null?void 0:i.ETCreatedBySearchData)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,d,{selected:y})=>{var A;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:os(d)||(d==null?void 0:d.code)==="Select All"&&(Y==null?void 0:Y.length)===((A=i==null?void 0:i.ETCreatedBySearchData)==null?void 0:A.length)}),label:l(T,{children:l("strong",{children:d.code})})})})})},renderInput:a=>l(F,{title:St.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:St.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:Nt.length===0?"Select Created By":"",onChange:d=>{wl(d)}})})})})]})}):e==="Profit Center"?l(T,{children:x(E,{item:!0,md:2,children:[l($,{sx:N,children:b("Profit Center")}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:_t,noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){_e([]),ot([]);return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?handleSelectAllProfitCenter():_e(d)},limitTags:1,options:(f=i==null?void 0:i.ETPCSearchData)!=null&&f.length?[{code:"Select All",desc:"Select All"},...i==null?void 0:i.ETPCSearchData]:(i==null?void 0:i.ETPCSearchData)??[],getOptionLabel:a=>a!=null&&a.code?`${a==null?void 0:a.code}`??"":"",renderOption:(a,d,{selected:y})=>{var A;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:us(d)||(d==null?void 0:d.code)==="Select All"&&(W==null?void 0:W.length)===((A=i==null?void 0:i.ETPCSearchData)==null?void 0:A.length)}),label:x(T,{children:[l("strong",{children:d.code})," ","- ",d.desc]})})})})},renderInput:a=>l(F,{title:gt.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:gt.length>=4,placement:"top",children:l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:_t.length===0?"Select Profit Center":"",onChange:d=>{Fl(d)}})})})})]})}):x(E,{item:!0,md:2,children:[l($,{sx:N,children:e}),l(z,{fullWidth:!0,size:"small",children:l(X,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",noOptionsText:_?l(R,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(Z,{size:20})}):"No Data Available",value:((c=v[e])==null?void 0:c.length)>0?v[e]:((o=ht[e])==null?void 0:o.length)>0?ht[e]:[],renderTags:(a,d)=>a.length>0?x(T,{children:[l(M,{label:a[0].code,...d({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),a.length>1&&l(M,{label:`+${a.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,onChange:(a,d,y)=>{var A;if(y==="clear"||(d==null?void 0:d.length)===0){be(P=>({...P,[e]:[]})),dl(P=>({...P,[e]:[]}));return}d.length>0&&((A=d[d.length-1])==null?void 0:A.code)==="Select All"?handleSelectAllOptions(e):be(P=>({...P,[e]:d}))},limitTags:1,options:(C=w==null?void 0:w[e])!=null&&C.length?[{code:"Select All"},...w==null?void 0:w[e]]:(w==null?void 0:w[e])??[],getOptionLabel:a=>a!=null&&a.code?`${a.code}`:"",renderOption:(a,d,{selected:y})=>{var A,P;return l("li",{...a,children:l(ee,{children:l(te,{control:l(m,{checked:gs(e,d)||(d==null?void 0:d.code)==="Select All"&&((A=v[e])==null?void 0:A.length)===((P=w==null?void 0:w[e])==null?void 0:P.length)}),label:l(T,{children:l("strong",{children:d==null?void 0:d.code})})})})})},renderInput:a=>l(O,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:`Select ${e}`})})})]})})})})]}),l(E,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:x(E,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:[l(E,{children:l(tt,{variant:"outlined",sx:xn,onClick:Ds,children:b("Clear")})}),l(E,{sx:{...qt},children:l(Pn,{moduleName:"CostCenter",handleSearch:()=>et()})}),l(E,{children:l(tt,{variant:"contained",sx:{...Qt,...qt},onClick:()=>et(),children:b("Search")})})]})})]})]})})}),l(E,{item:!0,sx:{position:"relative"},children:l(Wt,{children:l(An,{isLoading:zl,module:"CostCenter",width:"100%",title:"List of Cost Centers",rows:Q,columns:Cl??[],showSearch:!0,page:Ze,pageSize:me,showExport:!0,showRefresh:!0,rowCount:pl??(Q==null?void 0:Q.length)??0,onPageChange:_l,onPageSizeChange:Pl,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowDoubleClick:!0,onRowsSelectionHandler:zs,callback_onRowDoubleClick:e=>{const n=e.row.costCenter;Xe(`/masterDataCockpit/costCenter/displayCostCenter/${n}`,{state:e.row})},stopPropagation_Column:"action",showCustomNavigation:!0})})}),l(pn,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:l(En,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Ll,onChange:e=>{kl(e)},children:l(tt,{className:"container_BottomNav createRequestButtonCC",onClick:()=>{Xe("/requestBench/CostCenterRequestTab")},variant:"contained",size:"small",sx:{...Qt},children:b("Create Request")})})})]})})]})})};export{mn as default};
