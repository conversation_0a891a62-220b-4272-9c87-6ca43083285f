import{a as N,r as s,n as P,j as e,c as r,aj as G,d as c,a6 as E,ak as U,al as j,B as h,$ as W,dA as $,a4 as _,a8 as n,aO as l,aZ as q,an as R,ai as H}from"./index-226a1e75.js";import{d as K}from"./CloudUpload-17ed0189.js";import{d as Y}from"./Delete-3f2fc9ef.js";import{i as Z}from"./utilityImages-067c3dc2.js";const ne=({artifactId:J="",artifactName:Q="",poNumber:V,isAnAttachment:X,setOpen:D,handleUpload:A,showModuleDropdown:p=!1,selectedOption:g="",handleChanged:S=()=>{},dialogTitle:B="Add New Attachment"})=>{const{t:a}=N(),[O,ee]=s.useState("Other"),[f,u]=s.useState(!1),[i,m]=s.useState([]);s.useState([]),s.useState(!1),s.useState(["Others"]);const[x,b]=s.useState(!1);s.useState({attachments:[],comments:[]}),P(t=>t.userManagement.userData);let v=()=>{m([]),D(!1)};const I=t=>{t.preventDefault(),u(!0)},T=()=>{u(!1)},k=t=>{t.preventDefault(),u(!1);const o=Array.from(t.dataTransfer.files);C(o)},w=()=>{document.getElementById("fileButton").click()},C=t=>{let o=[];t.forEach(d=>{d.metaData=O,o.push(d)}),m(d=>[...d,...o])};let L=()=>{if(!x&&i[0]){[...i],A(i);return}},y=()=>{v()},M=t=>{let o=i.filter(d=>d.id!==t);m(o)},z=()=>{let t=0;i.forEach(o=>{t+=o.size}),t>5e8?(promptAction_Functions.handleOpenPromptBox("ERROR",{title:"Warning",message:"Files size excceded",severity:"warning",cancelButton:!1}),b(!0)):b(!1)};return s.useEffect(()=>{z()},[i]),e("div",{style:{display:"flex",flexDirection:"row"},children:r(H,{fullWidth:!0,maxWidth:"sm",sx:{"& .MuiDialog-paper":{borderRadius:"12px",padding:"1rem"},overflow:"hidden"},open:!0,onClose:v,children:[r(G,{sx:{padding:"1rem"},children:[e(c,{variant:"h5",sx:{fontWeight:500},children:a(B)}),e(E,{"aria-label":"close",onClick:y,sx:t=>({position:"absolute",right:12,top:10,color:t.palette.grey[500]}),children:e(U,{})})]}),r(j,{sx:{padding:"1rem",height:"max-content"},dividers:!0,children:[p&&e(h,{sx:{mb:3},children:r(W,{sx:{mb:2,mt:1,minWidth:200,maxWidth:300},children:[e($,{id:"dropdown-label",children:a("Module Type")}),r(_,{labelId:"dropdown-label",value:g,label:a("Module Type"),onChange:S,MenuProps:{PaperProps:{sx:{maxHeight:200}}},children:[e(n,{value:l.MAT,children:a("MATERIAL")}),e(n,{value:l.ART,children:a("ARTICLE")}),e(n,{value:l.PC,children:a("PROFIT CENTER")}),e(n,{value:l.CC,children:a("COST CENTER")}),e(n,{value:l.BK,children:a("BANK KEY")}),e(n,{value:l.GL,children:a("GENERAL LEDGER")}),e(n,{value:l.CCG,children:a("COST CENTER GROUP")}),e(n,{value:l.PCG,children:a("PROFIT CENTER GROUP")}),e(n,{value:l.CEG,children:a("COST ELEMENT GROUP")}),e(n,{value:l.BOM,children:a("BILL OF MATERIAL")}),e(n,{value:l.IO,children:a("INTERNAL ORDER")})]})]})}),r(h,{className:`dropzone ${f?"dragover":""}`,sx:{width:"100%",border:`2px dashed ${f?"#3b30c8":"#d0d5dd"}`,borderRadius:"8px",padding:"2rem",backgroundColor:f?"#f8f9ff":"#fafbff",cursor:"pointer",minHeight:"200px",display:"flex",alignItems:"center",justifyContent:"center"},onDragOver:I,onDragLeave:T,onDrop:k,children:[!i[0]&&r(h,{sx:{padding:"2rem",display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column"},children:[e(K,{sx:{fontSize:48,color:"#3b30c8"}}),e(c,{children:a("Drag and drop file here")}),e(c,{children:a("or")}),e(c,{children:e("a",{onClick:w,children:a("Browse file")})})]}),i.length>0&&r(h,{sx:{padding:"0rem",display:"flex",flexDirection:"column",gap:"1.5rem",width:"100%"},children:[i.map((t,o)=>{var d;return r(h,{sx:{display:"flex",alignItems:"center",padding:"1rem",borderRadius:"10px",backgroundColor:"#fff",border:"1px solid #ddd",boxShadow:"0 2px 6px rgba(0, 0, 0, 0.1)",transition:"background 0.2s ease-in-out, transform 0.1s ease-in-out","&:hover":{backgroundColor:"#f1f5f9"},width:"100%"},children:[e("img",{style:{width:"32px",height:"32px",marginRight:"1rem"},src:Z[(d=t.name)==null?void 0:d.split(".")[1]],alt:"file-icon"}),e(c,{variant:"body1",sx:{flexGrow:1,fontWeight:500,fontSize:"1rem"},children:t.name}),r(c,{sx:{marginLeft:"auto",marginRight:"10%",color:x?"error.main":"gray",fontWeight:500,fontSize:"0.9rem"},children:[parseFloat(t.size/1e6).toFixed(2)," MB"]}),e(E,{id:`closeBtn-${t.id}`,size:"small",onClick:F=>{F.stopPropagation(),M(t.id)},sx:{marginLeft:"0.5rem",opacity:.8,"&:hover":{opacity:1}},children:e(Y,{fontSize:"small",color:"error"})})]},o)}),e(c,{component:"div",sx:{padding:"",background:"#f9f9f9",borderRadius:"10px",fontSize:"0.95rem",lineHeight:"1.6"},children:r("ul",{style:{margin:"0",paddingLeft:"1.2rem"},children:[e("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:a("Mass Upload Process will start in the background. Once file is uploaded, you will receive a notification and an email containing the request ID number.")}),r("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:[a("You can visit the")," ",e("strong",{children:a("Request Bench")})," ",a("tab, search for the request ID, and perform further actions on it.")]}),r("li",{style:{width:"100%",marginBottom:"0.2rem",padding:"0.5rem",background:"#f5f5f5",borderRadius:"4px"},children:[e("strong",{children:a("Note:")})," ",a("All request IDs generated in the background will initially have the status")," ",e("strong",{children:a("Draft")})," ",a("and will be")," ",e("strong",{children:a("Upload Successful")})," ",a("or")," ",e("strong",{children:a("Upload Failed")})," ",a("after Uploading the Excel based on it's validation.")]})]})})]}),e("input",{id:"fileButton",multiple:!1,accept:".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",type:"file",name:"files",onChange:t=>C([...t.target.files]),style:{display:"none"}})]}),r(q,{direction:"row",sx:{justifyContent:"end",marginTop:"1rem",position:"relative"},children:[e(c,{sx:t=>({color:t.palette.grey,position:"absolute",left:0,top:0}),children:a("*Max file size 500 MB")}),e(R,{className:"btn-mr",variant:"contained",onClick:L,disabled:!(i!=null&&i.length)||p&&!g,sx:{mr:1},children:a("Upload")}),e(R,{variant:"outlined",onClick:y,children:a("Cancel")})]})]})]})})};export{ne as A};
