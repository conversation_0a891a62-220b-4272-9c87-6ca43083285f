import{r as o,f5 as Oe,ef as Y,t as le,f6 as yt,n as Ce,f7 as Ee,aJ as Ge,bU as Te,bH as Le,cd as ze,s as ke,f8 as W,f9 as J,c as q,j as g,F as $t,fa as xt,aK as wt,fb as St}from"./index-226a1e75.js";import{N as Ot,u as kt,a as It,A as Ft}from"./context-5b1a8b0b.js";import{g as Rt,m as Bt,K as je,r as Dt,C as Et,a as Me,u as Ue,I as Gt,b as Tt,c as Lt,E as zt,L as Nt,d as Ht,e as At,f as Vt,h as Mt,M as Fe,T as Ke,i as Pt,D as Ye,B as ue,F as me,S as Je,j as Wt,k as Xt,l as xe}from"./EditOutlined-5e4d9326.js";import{C as et,A as ee}from"./EyeOutlined-6bec9589.js";import{A as jt}from"./ArrowLeftOutlined-cbc675ea.js";import{F as Jt,a as qt,I as Zt,A as Qt,T as _t}from"./index-a591cf5c.js";const Ut=s=>{const{componentCls:e,iconCls:n,boxShadow:t,colorText:a,colorSuccess:c,colorError:v,colorWarning:d,colorInfo:m,fontSizeLG:u,motionEaseInOutCirc:r,motionDurationSlow:i,marginXS:O,paddingXS:S,borderRadiusLG:x,zIndexPopup:F,contentPadding:B,contentBg:f}=s,y=`${e}-notice`,k=new je("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:S,transform:"translateY(0)",opacity:1}}),h=new je("MessageMoveOut",{"0%":{maxHeight:s.height,padding:S,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),p={padding:S,textAlign:"center",[`${e}-custom-content`]:{display:"flex",alignItems:"center"},[`${e}-custom-content > ${n}`]:{marginInlineEnd:O,fontSize:u},[`${y}-content`]:{display:"inline-block",padding:B,background:f,borderRadius:x,boxShadow:t,pointerEvents:"all"},[`${e}-success > ${n}`]:{color:c},[`${e}-error > ${n}`]:{color:v},[`${e}-warning > ${n}`]:{color:d},[`${e}-info > ${n},
      ${e}-loading > ${n}`]:{color:m}};return[{[e]:Object.assign(Object.assign({},Dt(s)),{color:a,position:"fixed",top:O,width:"100%",pointerEvents:"none",zIndex:F,[`${e}-move-up`]:{animationFillMode:"forwards"},[`
        ${e}-move-up-appear,
        ${e}-move-up-enter
      `]:{animationName:k,animationDuration:i,animationPlayState:"paused",animationTimingFunction:r},[`
        ${e}-move-up-appear${e}-move-up-appear-active,
        ${e}-move-up-enter${e}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${e}-move-up-leave`]:{animationName:h,animationDuration:i,animationPlayState:"paused",animationTimingFunction:r},[`${e}-move-up-leave${e}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[e]:{[`${y}-wrapper`]:Object.assign({},p)}},{[`${e}-notice-pure-panel`]:Object.assign(Object.assign({},p),{padding:0,textAlign:"start"})}]},Kt=s=>({zIndexPopup:s.zIndexPopupBase+Et+10,contentBg:s.colorBgElevated,contentPadding:`${(s.controlHeightLG-s.fontSize*s.lineHeight)/2}px ${s.paddingSM}px`}),tt=Rt("Message",s=>{const e=Bt(s,{height:150});return Ut(e)},Kt);var Yt=globalThis&&globalThis.__rest||function(s,e){var n={};for(var t in s)Object.prototype.hasOwnProperty.call(s,t)&&e.indexOf(t)<0&&(n[t]=s[t]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(s);a<t.length;a++)e.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(s,t[a])&&(n[t[a]]=s[t[a]]);return n};const es={info:o.createElement(Gt,null),success:o.createElement(Tt,null),error:o.createElement(Lt,null),warning:o.createElement(zt,null),loading:o.createElement(Nt,null)},st=({prefixCls:s,type:e,icon:n,children:t})=>o.createElement("div",{className:Oe(`${s}-custom-content`,`${s}-${e}`)},n||es[e],o.createElement("span",null,t)),ts=s=>{const{prefixCls:e,className:n,type:t,icon:a,content:c}=s,v=Yt(s,["prefixCls","className","type","icon","content"]),{getPrefixCls:d}=o.useContext(Me),m=e||d("message"),u=Ue(m),[r,i,O]=tt(m,u);return r(o.createElement(Ot,Object.assign({},v,{prefixCls:m,className:Oe(n,i,`${m}-notice-pure-panel`,O,u),eventKey:"pure",duration:null,content:o.createElement(st,{prefixCls:m,type:t,icon:a},c)})))},ss=ts;function ns(s,e){return{motionName:e??`${s}-move-up`}}function Pe(s){let e;const n=new Promise(a=>{e=s(()=>{a(!0)})}),t=()=>{e==null||e()};return t.then=(a,c)=>n.then(a,c),t.promise=n,t}var as=globalThis&&globalThis.__rest||function(s,e){var n={};for(var t in s)Object.prototype.hasOwnProperty.call(s,t)&&e.indexOf(t)<0&&(n[t]=s[t]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,t=Object.getOwnPropertySymbols(s);a<t.length;a++)e.indexOf(t[a])<0&&Object.prototype.propertyIsEnumerable.call(s,t[a])&&(n[t[a]]=s[t[a]]);return n};const rs=8,ls=3,cs=({children:s,prefixCls:e})=>{const n=Ue(e),[t,a,c]=tt(e,n);return t(o.createElement(It,{classNames:{list:Oe(a,c,n)}},s))},is=(s,{prefixCls:e,key:n})=>o.createElement(cs,{prefixCls:e,key:n},s),os=o.forwardRef((s,e)=>{const{top:n,prefixCls:t,getContainer:a,maxCount:c,duration:v=ls,rtl:d,transitionName:m,onAllRemoved:u}=s,{getPrefixCls:r,getPopupContainer:i,message:O,direction:S}=o.useContext(Me),x=t||r("message"),F=()=>({left:"50%",transform:"translateX(-50%)",top:n??rs}),B=()=>Oe({[`${x}-rtl`]:d??S==="rtl"}),f=()=>ns(x,m),y=o.createElement("span",{className:`${x}-close-x`},o.createElement(et,{className:`${x}-close-icon`})),[k,h]=kt({prefixCls:x,style:F,className:B,motion:f,closable:!1,closeIcon:y,duration:v,getContainer:()=>(a==null?void 0:a())||(i==null?void 0:i())||document.body,maxCount:c,onAllRemoved:u,renderNotifications:is});return o.useImperativeHandle(e,()=>Object.assign(Object.assign({},k),{prefixCls:x,message:O})),h});let qe=0;function nt(s){const e=o.useRef(null);return Ht(),[o.useMemo(()=>{const t=m=>{var u;(u=e.current)===null||u===void 0||u.close(m)},a=m=>{if(!e.current){const D=()=>{};return D.then=()=>{},D}const{open:u,prefixCls:r,message:i}=e.current,O=`${r}-notice`,{content:S,icon:x,type:F,key:B,className:f,style:y,onClose:k}=m,h=as(m,["content","icon","type","key","className","style","onClose"]);let p=B;return p==null&&(qe+=1,p=`antd-message-${qe}`),Pe(D=>(u(Object.assign(Object.assign({},h),{key:p,content:o.createElement(st,{prefixCls:r,type:F,icon:x},S),placement:"top",className:Oe(F&&`${O}-${F}`,f,i==null?void 0:i.className),style:Object.assign(Object.assign({},i==null?void 0:i.style),y),onClose:()=>{k==null||k(),D()}})),()=>{t(p)}))},v={open:a,destroy:m=>{var u;m!==void 0?t(m):(u=e.current)===null||u===void 0||u.destroy()}};return["info","success","warning","error","loading"].forEach(m=>{const u=(r,i,O)=>{let S;r&&typeof r=="object"&&"content"in r?S=r:S={content:r};let x,F;typeof i=="function"?F=i:(x=i,F=O);const B=Object.assign(Object.assign({onClose:F,duration:x},S),{type:m});return a(B)};v[m]=u}),v},[]),o.createElement(os,Object.assign({key:"message-holder"},s,{ref:e}))]}function us(s){return nt(s)}var ds={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};const fs=ds;var ps=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:fs}))},hs=o.forwardRef(ps);const He=hs;let j=null,ve=s=>s(),we=[],Se={};function Ze(){const{getContainer:s,duration:e,rtl:n,maxCount:t,top:a}=Se,c=(s==null?void 0:s())||document.body;return{getContainer:()=>c,duration:e,rtl:n,maxCount:t,top:a}}const gs=le.forwardRef((s,e)=>{const{messageConfig:n,sync:t}=s,{getPrefixCls:a}=o.useContext(Me),c=Se.prefixCls||a("message"),v=o.useContext(Ft),[d,m]=nt(Object.assign(Object.assign(Object.assign({},n),{prefixCls:c}),v.message));return le.useImperativeHandle(e,()=>{const u=Object.assign({},d);return Object.keys(u).forEach(r=>{u[r]=(...i)=>(t(),d[r].apply(d,i))}),{instance:u,sync:t}}),m}),ms=le.forwardRef((s,e)=>{const[n,t]=le.useState(Ze),a=()=>{t(Ze)};le.useEffect(a,[]);const c=Mt(),v=c.getRootPrefixCls(),d=c.getIconPrefixCls(),m=c.getTheme(),u=le.createElement(gs,{ref:e,sync:a,messageConfig:n});return le.createElement(Vt,{prefixCls:v,iconPrefixCls:d,theme:m},c.holderRender?c.holderRender(u):u)}),Re=()=>{if(!j){const s=document.createDocumentFragment(),e={fragment:s};j=e,ve(()=>{At()(le.createElement(ms,{ref:t=>{const{instance:a,sync:c}=t||{};Promise.resolve().then(()=>{!e.instance&&a&&(e.instance=a,e.sync=c,Re())})}}),s)});return}j.instance&&(we.forEach(s=>{const{type:e,skipped:n}=s;if(!n)switch(e){case"open":{ve(()=>{const t=j.instance.open(Object.assign(Object.assign({},Se),s.config));t==null||t.then(s.resolve),s.setCloseFn(t)});break}case"destroy":ve(()=>{j==null||j.instance.destroy(s.key)});break;default:ve(()=>{var t;const a=(t=j.instance)[e].apply(t,yt(s.args));a==null||a.then(s.resolve),s.setCloseFn(a)})}}),we=[])};function Cs(s){Se=Object.assign(Object.assign({},Se),s),ve(()=>{var e;(e=j==null?void 0:j.sync)===null||e===void 0||e.call(j)})}function vs(s){const e=Pe(n=>{let t;const a={type:"open",config:s,resolve:n,setCloseFn:c=>{t=c}};return we.push(a),()=>{t?ve(()=>{t()}):a.skipped=!0}});return Re(),e}function bs(s,e){const n=Pe(t=>{let a;const c={type:s,args:e,resolve:t,setCloseFn:v=>{a=v}};return we.push(c),()=>{a?ve(()=>{a()}):c.skipped=!0}});return Re(),n}const ys=s=>{we.push({type:"destroy",key:s}),Re()},$s=["success","info","warning","error","loading"],xs={open:vs,destroy:ys,config:Cs,useMessage:us,_InternalPanelDoNotUseOrYouWillBeFired:ss},at=xs;$s.forEach(s=>{at[s]=(...e)=>bs(s,e)});const z=at;var ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M740 161c-61.8 0-112 50.2-112 112 0 50.1 33.1 92.6 78.5 106.9v95.9L320 602.4V318.1c44.2-15 76-56.9 76-106.1 0-61.8-50.2-112-112-112s-112 50.2-112 112c0 49.2 31.8 91 76 106.1V706c-44.2 15-76 56.9-76 106.1 0 61.8 50.2 112 112 112s112-50.2 112-112c0-49.2-31.8-91-76-106.1v-27.8l423.5-138.7a50.52 50.52 0 0034.9-48.2V378.2c42.9-15.8 73.6-57 73.6-105.2 0-61.8-50.2-112-112-112zm-504 51a48.01 48.01 0 0196 0 48.01 48.01 0 01-96 0zm96 600a48.01 48.01 0 01-96 0 48.01 48.01 0 0196 0zm408-491a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"branches",theme:"outlined"};const Ss=ws;var Os=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:Ss}))},ks=o.forwardRef(Os);const Is=ks;var Fs={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};const Rs=Fs;var Bs=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:Rs}))},Ds=o.forwardRef(Bs);const Es=Ds;var Gs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M326 664H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V696c0-17.7-14.3-32-32-32zm16-576h-48c-8.8 0-16 7.2-16 16v176H104c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V104c0-8.8-7.2-16-16-16zm578 576H698c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V744h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm0-384H746V104c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16z"}}]},name:"compress",theme:"outlined"};const Ts=Gs;var Ls=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:Ts}))},zs=o.forwardRef(Ls);const Ns=zs;var Hs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M342 88H120c-17.7 0-32 14.3-32 32v224c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V168h174c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zm578 576h-48c-8.8 0-16 7.2-16 16v176H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h222c17.7 0 32-14.3 32-32V680c0-8.8-7.2-16-16-16zM342 856H168V680c0-8.8-7.2-16-16-16h-48c-8.8 0-16 7.2-16 16v224c0 17.7 14.3 32 32 32h222c8.8 0 16-7.2 16-16v-48c0-8.8-7.2-16-16-16zM904 88H682c-8.8 0-16 7.2-16 16v48c0 8.8 7.2 16 16 16h174v176c0 8.8 7.2 16 16 16h48c8.8 0 16-7.2 16-16V120c0-17.7-14.3-32-32-32z"}}]},name:"expand",theme:"outlined"};const As=Hs;var Vs=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:As}))},Ms=o.forwardRef(Vs);const Ps=Ms;var Ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M365.3 518.5l246 178c5.3 3.8 12.7 0 12.7-6.5v-46.9c0-10.2-4.9-19.9-13.2-25.9L465.4 512l145.4-105.2c8.3-6 13.2-15.6 13.2-25.9V334c0-6.5-7.4-10.3-12.7-6.5l-246 178a8.05 8.05 0 000 13z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"left-square",theme:"outlined"};const Xs=Ws;var js=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:Xs}))},Js=o.forwardRef(js);const qs=Js;var Zs={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M696 480H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"minus-circle",theme:"outlined"};const Qs=Zs;var _s=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:Qs}))},Us=o.forwardRef(_s);const Ks=Us;var Ys={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M843.5 737.4c-12.4-75.2-79.2-129.1-155.3-125.4S550.9 676 546 752c-153.5-4.8-208-40.7-199.1-113.7 3.3-27.3 19.8-41.9 50.1-49 18.4-4.3 38.8-4.9 57.3-3.2 1.7.2 3.5.3 5.2.5 11.3 2.7 22.8 5 34.3 6.8 34.1 5.6 68.8 8.4 101.8 6.6 92.8-5 156-45.9 159.2-132.7 3.1-84.1-54.7-143.7-147.9-183.6-29.9-12.8-61.6-22.7-93.3-30.2-14.3-3.4-26.3-5.7-35.2-7.2-7.9-75.9-71.5-133.8-147.8-134.4-76.3-.6-140.9 56.1-150.1 131.9s40 146.3 114.2 163.9c74.2 17.6 149.9-23.3 175.7-95.1 9.4 1.7 18.7 3.6 28 5.8 28.2 6.6 56.4 15.4 82.4 26.6 70.7 30.2 109.3 70.1 107.5 119.9-1.6 44.6-33.6 65.2-96.2 68.6-27.5 1.5-57.6-.9-87.3-5.8-8.3-1.4-15.9-2.8-22.6-4.3-3.9-.8-6.6-1.5-7.8-1.8l-3.1-.6c-2.2-.3-5.9-.8-10.7-1.3-25-2.3-52.1-1.5-78.5 4.6-55.2 12.9-93.9 47.2-101.1 105.8-15.7 126.2 78.6 184.7 276 188.9 29.1 70.4 106.4 107.9 179.6 87 73.3-20.9 119.3-93.4 106.9-168.6zM329.1 345.2a83.3 83.3 0 11.01-166.61 83.3 83.3 0 01-.01 166.61zM695.6 845a83.3 83.3 0 11.01-166.61A83.3 83.3 0 01695.6 845z"}}]},name:"node-index",theme:"outlined"};const en=Ys;var tn=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:en}))},sn=o.forwardRef(tn);const nn=sn;var an={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M696 480H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"plus-circle",theme:"outlined"};const rn=an;var ln=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:rn}))},cn=o.forwardRef(ln);const rt=cn;var on={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M938 458.8l-29.6-312.6c-1.5-16.2-14.4-29-30.6-30.6L565.2 86h-.4c-3.2 0-5.7 1-7.6 2.9L88.9 557.2a9.96 9.96 0 000 14.1l363.8 363.8c1.9 1.9 4.4 2.9 7.1 2.9s5.2-1 7.1-2.9l468.3-468.3c2-2.1 3-5 2.8-8zM459.7 834.7L189.3 564.3 589 164.6 836 188l23.4 247-399.7 399.7zM680 256c-48.5 0-88 39.5-88 88s39.5 88 88 88 88-39.5 88-88-39.5-88-88-88zm0 120c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z"}}]},name:"tag",theme:"outlined"};const un=on;var dn=function(e,n){return o.createElement(ee,Y({},e,{ref:n,icon:un}))},fn=o.forwardRef(dn);const Qe=fn,pn=()=>{const[s,e]=o.useState(!0),[n,t]=o.useState(null);Ce(r=>r.payload.requestorPayload),Ce(r=>r.hierarchyData);const a=async(r,i,O,S="",x,F)=>{var B={requestId:"",controllingArea:O,hierarchyGrp:S,node:r},f={requestId:F,controllingArea:O,hierarchyGrp:S,node:r},y={requestId:F,chartOfAccount:x,hierarchyGrp:S,node:r},k={PCG:`/${Te}/node/nodeDuplicacyCheckForPCG`,CCG:`/${Le}/node/nodeDuplicacyCheckForCCG`,CEG:`/${ze}/node/nodeDuplicacyCheckForCEG`},h={PCG:B,CCG:f,CEG:y};try{e(!0);const D=await(await Ee(k[i],"post",h[i])).json();if(D.statusCode!==Ge.STATUS_200)throw new Error(D.message||"Duplicate check failed");return D}catch(p){throw t(p),p}finally{e(!1)}},c=async(r,i,O,S,x)=>{var F={requestId:"",classValue:"0106",controllingArea:O,desc:r},B={requestId:x,classValue:"0101",controllingArea:O,desc:r},f={requestId:x,classValue:"0102",chartOfAccount:S,desc:r},y={PCG:`/${Te}/node/descDuplicacyCheckForPCG`,CCG:`/${Le}/node/descDuplicacyCheckForCCG`,CEG:`/${ze}/node/descDuplicacyCheckForCEG`},k={PCG:F,CCG:B,CEG:f};try{e(!0);const p=await(await Ee(y[i],"post",k[i])).json();if(p.statusCode!==Ge.STATUS_200)throw new Error(p.message||"Duplicate check failed");return p}catch(h){throw t(h),h}finally{e(!1)}},v=async(r,i,O,S,x)=>{var F={classValue:"0106",controllingArea:O,hierarchyGrp:x,pc:r},B={classValue:"0101",controllingArea:O,hierarchyGrp:x,cc:r},f={classValue:"0102",chartOfAccount:S,hierarchyGrp:x,gl:r},y={PCG:`/${Te}/node/pcDuplicacyCheckForPCG`,CCG:`/${Le}/node/ccDuplicacyCheckForCCG`,CEG:`/${ze}/node/glDuplicacyCheckForCEG`},k={PCG:F,CCG:B,CEG:f};try{e(!0);const p=await(await Ee(y[i],"post",k[i])).json();if(p.statusCode!==Ge.STATUS_200)throw new Error(p.message||"Duplicate check failed");return p}catch(h){throw t(h),h}finally{e(!1)}};return{checkForNodeDuplicacy:async(r,i,O,S="",x="",F="")=>await a(r,i,O,S,x,F),checkForDescriptionDuplicacy:async(r,i,O,S,x)=>await c(r,i,O,S,x),checkForObjectDuplicacy:async(r,i,O,S,x)=>await v(r,i,O,S,x)}},hn=({form:s,editForm:e,tagForm:n,rawTreeData:t,currentParent:a,currentEditNode:c,currentTagParent:v,object:d,moduleObject:m,requestorPayload:u,reduxPayload:r,MODULE_KEY_MAP:i,setIsModalVisible:O,setIsEditModalVisible:S,setIsTagModalVisible:x,setExpandedKeys:F,addToChangeLog:B,treeChanges:f,setIsAddingNode:y,setIsAddingTag:k,setIsEditingDescription:h})=>{const p=ke(),{checkForNodeDuplicacy:D,checkForDescriptionDuplicacy:Z,checkForObjectDuplicacy:l}=pn(),te=($,C)=>{if($.label===C)return!0;if($.child&&$.child.length>0){for(let I=0;I<$.child.length;I++)if(te($.child[I],C))return!0}return!1},de=$=>{if(!$.child||$.child.length===0)return`${$.id}_0`;const C=$.child.map(w=>{const A=w.id.split("_");return parseInt(A[A.length-1],10)}),I=Math.max(...C);return`${$.id}_${I+1}`},ce=($,C,I)=>$.map(w=>w.id===C?{...w,child:[...w.child||[],I]}:w.child?{...w,child:ce(w.child,C,I)}:w),Q=o.useCallback(async()=>{var $,C,I,w,A,E,ne,X;y(!0);try{const P=await s.validateFields(),{label:H,description:_}=P;if(te(t[0],H)){z.error(`Node "${H}" already exists in the hierarchy!`),y(!1);return}if(U(t[0],_)){z.error(`Description "${_}" already exists in the hierarchy!`),y(!1);return}const pe=((I=(C=u[($=i==null?void 0:i[d])==null?void 0:$.CTRL_AREA])==null?void 0:C[0])==null?void 0:I.code)||(r==null?void 0:r.ControllingArea)||"",G=await D(H,d,pe,"","");if((w=G==null?void 0:G.body)!=null&&w.isDbDuplicate){z.error(`Node "${H}" already exists in some ongoing request!`),y(!1);return}if(((A=G==null?void 0:G.body)==null?void 0:A.PresentInHier)==="X"||((E=G==null?void 0:G.body)==null?void 0:E.PresentInCA)==="X"||((ne=G==null?void 0:G.body)==null?void 0:ne.PresentInCOA)==="X"){z.error(`Node "${H}" already exists in the hierarchy!`),y(!1);return}const V=await Z(_,d,pe,"","");if(Object.keys(V.body).length!==0&&((X=V==null?void 0:V.body)==null?void 0:X.isDbDuplicate)===!0){z.error(`Description "${_}" already present in some ongoing request!`),y(!1);return}p(W({nodeLabel:H,changes:{isNewNode:!0,isMoved:!1,tags:[],oldParentNode:a==null?void 0:a.label,newParentNode:null,description:_}}));const ye={id:a?de(a):`0_${t.length}`,label:H,description:_,isParent:!0,child:[]},$e=a?ce(t,a.id,ye):[...t,ye];B("ADD NODE",`${H} added under ${a==null?void 0:a.label}`),p(J($e)),O(!1),s.resetFields(),a&&F(ae=>[...ae,a.id]),y(!1),z.success(`${H} added successfully`)}catch(P){y(!1),console.error("Validation failed:",P)}},[s,t,a,d,u,r,i,O,F,B,p,y,k,h]),U=($,C)=>{if($.description===C)return!0;if($.child&&$.child.length>0){for(let I=0;I<$.child.length;I++)if(U($.child[I],C))return!0}return!1},ie=($,C,I)=>$.map(w=>w.id===C?{...w,description:I}:w.child?{...w,child:ie(w.child,C,I)}:w),be=o.useCallback(async()=>{var $,C,I,w;h(!0);try{const E=(await e.validateFields()).description;if(U(t[0],E)){z.error(`Description "${E}" already exists in the hierarchy!`),h(!1);return}const ne=((I=(C=u[($=i==null?void 0:i[d])==null?void 0:$.CTRL_AREA])==null?void 0:C[0])==null?void 0:I.code)||(r==null?void 0:r.ControllingArea)||"",X=await Z(c.label,d,ne,"","");if(Object.keys(X.body).length!==0&&((w=X==null?void 0:X.body)==null?void 0:w.isDbDuplicate)===!0){z.error(`Description "${E}" already present in some ongoing request!`),h(!1);return}const P=f==null?void 0:f[c==null?void 0:c.label];p(P?W({nodeLabel:c.label,changes:{description:E}}):W({nodeLabel:c.label,changes:{description:E}}));const H=ie(t,c.id,E);B("CHANGED DESCRIPTION",`${c.label} Node description changed from ${c.description} to ${E}`),p(J(H)),S(!1),e.resetFields(),h(!1),z.success("Description updated successfully")}catch(A){h(!1),console.error("Validation failed:",A)}},[e,t,c,d,m,u,r,i,f,p,S,B]),se=($,C)=>{if($.tags&&$.tags.includes(C))return!0;if($.child&&$.child.length>0){for(let I=0;I<$.child.length;I++)if(se($.child[I],C))return!0}return!1},fe=($,C,I)=>$.map(w=>w.id===C?{...w,tags:[...w.tags||[],I]}:w.child?{...w,child:fe(w.child,C,I)}:w),oe=o.useCallback(async()=>{var $,C,I,w,A,E,ne,X,P,H,_;k(!0);try{const G=(await n.validateFields()).tag;if(se(t[0],G)){z.error(`${m} "${G}" already exists in the hierarchy!`),k(!1);return}const V=await l(G,d,((I=(C=u[($=i==null?void 0:i[d])==null?void 0:$.CTRL_AREA])==null?void 0:C[0])==null?void 0:I.code)||(r==null?void 0:r.ControllingArea)||"",((E=(A=u[(w=i==null?void 0:i[d])==null?void 0:w.COA])==null?void 0:A[0])==null?void 0:E.code)||(r==null?void 0:r.ChartOfAccount)||"",((P=(X=u==null?void 0:u[(ne=i==null?void 0:i[d])==null?void 0:ne.CTR_GRP])==null?void 0:X[0])==null?void 0:P.code)||(u==null?void 0:u[(H=i==null?void 0:i[d])==null?void 0:H.CTR_GRP])||(r==null?void 0:r.ParentNode)||"");if((V==null?void 0:V.body.PresentInCA)!=="X"&&(V==null?void 0:V.body.PresentInCOA)!=="X"){z.error(`Invalid ${m}`),k(!1);return}if((_=V==null?void 0:V.body)!=null&&_.isDuplicate){z.error(`${m} "${G}" already exists in the database!`),k(!1);return}if(V.body.PresentInHier==="X"){z.error(`${m} "${G}" already exists in the hierarchy!`),k(!1);return}const ye=fe(t,v.id,G);B(`ADD ${m}`,`${G} added to ${v==null?void 0:v.label}`);const $e=(f==null?void 0:f[v==null?void 0:v.label])||{},ae=Array.isArray($e.tags)?$e.tags:[],Be=ae!=null&&ae.includes(G)?ae:[...ae,G];p(W({nodeLabel:v==null?void 0:v.label,changes:{tags:Be}})),p(J(ye)),x(!1),n.resetFields(),F(De=>[...De,v.id]),k(!1),z.success(`${G} added successfully`)}catch(pe){k(!1),console.error("Validation failed:",pe)}},[n,t,v,d,m,u,r,i,B,f,p,x,F]);return{handleAddNode:Q,handleEditDescription:be,handleAddTag:oe}},lt=(s,e)=>{var n;for(const t of s){if((n=t.child)!=null&&n.some(a=>a.id===e))return t;if(t.child){const a=lt(t.child,e);if(a)return a}}return null},Ae=(s,e)=>s.map(n=>n.child?{...n,child:Ae(n.child,e)}:n).filter(n=>n.id!==e),ct=(s,e,n)=>s.map(t=>t.id===e?{...t,child:[...t.child||[],n]}:t.child?{...t,child:ct(t.child,e,n)}:t),it=(s,e="")=>s.map((n,t)=>{const a=e===""?`${t}`:`${e}_${t}`,c=n.child?it(n.child,a):[];return{...n,id:a,child:c}}),ot=(s,e,n)=>s.map(t=>{var a;return t.id===e?{...t,tags:((a=t.tags)==null?void 0:a.filter(c=>c!==n))||[]}:t.child?{...t,child:ot(t.child,e,n)}:t}),Ve=(s,e,n)=>s.map(t=>t.id===e?{...t,tags:[...t.tags||[],n]}:t.child?{...t,child:Ve(t.child,e,n)}:t),gn=({rawTreeData:s,treeChanges:e,setSelectedNode:n,setOriginalParent:t,selectedNode:a,addToChangeLog:c,setSelectedTag:v,selectedTag:d,object:m,originalParent:u})=>{const r=ke(),i=o.useCallback(f=>{const y=lt(s,f.id);t(y);const k=Ae([...s],f.id),h=e==null?void 0:e[f==null?void 0:f.label];h!=null&&h.oldParentNode||r(W({nodeLabel:f==null?void 0:f.label,changes:{...h,oldParentNode:y==null?void 0:y.label}})),r(J(k)),n(f),z.info(`Moving ${f.label} - select destination`)},[s,e,r,n,t]),O=o.useCallback(f=>{if(!a)return;const y=Ae([...s],a.id),k=ct(y,f.id,a),h=it(k);c("MOVE NODE",`${a==null?void 0:a.label} Node moved from ${a==null?void 0:a.label} to ${f==null?void 0:f.label}`);const p=e[a==null?void 0:a.label];if(!p)return;const D=f==null?void 0:f.label,Z=p.oldParentNode,l=p.isNewNode;r(l?W({nodeLabel:a.label,changes:{...p,oldParentNode:D,newParentNode:null,isMoved:!0}}):Z===D?W({nodeLabel:a.label,changes:{...p,oldParentNode:null,newParentNode:null,isMoved:!1}}):W({nodeLabel:a.label,changes:{...p,newParentNode:D,isMoved:!0}})),r(J(h)),n(null),t(null),z.success(`Moved ${a.label} to ${f.label}`)},[a,s,e,r,n,t,c]),S=o.useCallback((f,y)=>{const k=ot([...s],y.id,f);t(y);const h=(e==null?void 0:e[y.label])||{},p=(h==null?void 0:h.tags)||[],D=(h==null?void 0:h.replacedTags)||[],Z=p==null?void 0:p.includes(f),l=Z?p.filter(de=>de!==f):p,te=Z?D:[...new Set([...D,f])];r(W({nodeLabel:y.label,changes:{tags:l,replacedTags:te}})),r(J(k)),v({tag:f,sourceNodeId:y.id}),z.info(`Moving tag ${f} - select destination node`)},[s,e,r,t,v]),x=o.useCallback(f=>{if(!d)return;const y=Ve([...s],f.id,d.tag);c(`MOVE ${m}`,`${d==null?void 0:d.tag} ${m} moved to ${f.label}`);const k=e[f.label]||{},h=k.tags||[];r(W({nodeLabel:f.label,changes:{...k,tags:[...new Set([...h,d.tag])]}})),r(J(y)),t(null),v(null),z.success(`Moved ${m} ${d.tag} to ${f.label}`)},[d,s,m,c,e,r,t,v]),F=o.useCallback(()=>{if(!a||!u)return;const f=JSON.parse(JSON.stringify(s)),y=h=>{for(let p=0;p<h.length;p++){if(h[p].id===u.id){h[p].child||(h[p].child=[]);let D=0;for(;D<h[p].child.length&&h[p].child[D].id.localeCompare(a.id)<0;)D++;return h[p].child.splice(D,0,a),!0}if(h[p].child&&y(h[p].child))return!0}return!1};y(f)?(r(J(f)),z.success(`${a.label} restored to exact original position`)):z.error("Could not restore node - parent structure changed"),n(null),t(null)},[s,a,u,r,n,t]),B=o.useCallback(()=>{if(!d)return;const f=Ve([...s],d.sourceNodeId,d.tag);if(u!=null&&u.label){const y=e[u.label]||{},k=(y.replacedTags||[]).filter(p=>p!==d.tag),h=y.tags||[];r(W({nodeLabel:u.label,changes:{...y,tags:[...new Set([...h,d.tag])],replacedTags:k}}))}r(J(f)),v(null),t(null)},[s,d,u,e,r,v,t]);return{handleSelectNode:i,handlePlaceNode:O,handleSelectTag:S,handlePlaceTag:x,handleCancelMoveNode:F,handleCancelMoveTag:B}},ut=(s,e,n)=>s.map(t=>{var a;return t.id===e?{...t,tags:((a=t.tags)==null?void 0:a.filter(c=>c!==n))||[]}:t.child?{...t,child:ut(t.child,e,n)}:t}),mn=({rawTreeData:s,treeChanges:e,addToChangeLog:n,object:t,setIsEditModalVisible:a,editForm:c})=>{const v=ke();return{handleRemoveTag:o.useCallback(async(m,u)=>{try{const r=ut([...s],u.id,m);n(`REMOVE ${t} `,`${m} removed from ${u==null?void 0:u.label}`);const i=(e==null?void 0:e[u.label])||{},O=(i==null?void 0:i.tags)||[],S=(i==null?void 0:i.replacedTags)||[],x=O.includes(m),F=x?O.filter(f=>f!==m):O,B=x?S:[...new Set([...S,m])];v(W({nodeLabel:u.label,changes:{tags:F,replacedTags:B}})),v(J(r)),a(!1),c.resetFields(),z.success(`${t} ${m} Removed Successfully`)}catch(r){console.error("Validation failed:",r)}},[s,e,v,n,t,a,c])}},{confirm:Cn}=Fe,dt=(s,e="")=>s.map((n,t)=>{const a=e===""?`${t}`:`${e}_${t}`;return{...n,id:a,child:n.child?dt(n.child,a):[]}}),ft=(s,e)=>s.map(n=>({...n,child:n.child?ft(n.child,e):[]})).filter(n=>n.id!==e),vn=(s,e,n=null)=>{const t=[],a=(c,v)=>{var d;for(const m of c){if(m.id===e){const u=(r,i)=>{var O;t.push({...r,oldParentNode:i?i.label:null}),(O=r.child)==null||O.forEach(S=>u(S,r))};return u(m,v),!0}if((d=m.child)!=null&&d.length&&a(m.child,m))return!0}return!1};return a(s,n),t},bn=({rawTreeData:s,treeChanges:e,addToChangeLog:n})=>{const t=ke();return{handleDeleteNode:o.useCallback(c=>{Cn({title:`Delete ${c.label}?`,content:"Deleting node will remove all Profit Centers attached to the nodes & Sub-Nodes from this Hierarchy",okText:"Delete",okType:"danger",cancelText:"Cancel",onOk(){const v=vn(s,c.id);let d=ft([...s],c.id);d=dt(d),n("DELETE NODE",`${c==null?void 0:c.label} Node deleted`),v.forEach(r=>{t(W({nodeLabel:r.label,changes:{...e&&e[r.label]||{},isDeleted:!0,oldParentNode:r.oldParentNode}}))}),t(J(d));const m=v.length-1,u=m>0?`Deleted ${c.label} and ${m} child node(s). Updated Hierarchy`:`Deleted ${c.label} and Updated Hierarchy`;z.success(u)}})},[s,e,t,n])}},b={colors:{primary:"#1890ff",success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#13c2c2",parentNode:"#2c3e50",leafNode:"#34495e",tagNode:"#7f8c8d",primaryBg:"#f0f8ff",successBg:"#f6ffed",warningBg:"#fffbe6",errorBg:"#fff2f0",infoBg:"#e6fffb",primaryBorder:"#d9e8fc",successBorder:"#b7eb8f",warningBorder:"#ffe58f",errorBorder:"#ffccc7",infoBorder:"#87e8de",textPrimary:"#262626",textSecondary:"#595959",textDisabled:"#bfbfbf",hoverBg:"#f5f5f5",activeBg:"#e6f7ff",selectedBg:"#bae7ff"},spacing:{xs:"5px",sm:"8px",md:"12px",lg:"16px",xl:"24px",xxl:"32px"},borderRadius:{sm:"4px",md:"6px",lg:"8px"},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},transitions:{default:"all 0.3s ease",fast:"all 0.15s ease",slow:"all 0.5s ease"}},L={container:{fontFamily:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',fontSize:"14px",lineHeight:1.5,padding:"16px",backgroundColor:"#fafafa"},searchContainer:{display:"flex",marginBottom:b.spacing.lg,gap:b.spacing.sm,alignItems:"center"},actionButtonsContainer:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:b.spacing.lg,padding:b.spacing.sm,backgroundColor:b.colors.primaryBg,borderRadius:b.borderRadius.md,border:`1px solid ${b.colors.primaryBorder}`},actionButton:{borderRadius:b.borderRadius.md,boxShadow:b.shadows.sm,transition:b.transitions.default,fontWeight:500},treeContainer:{backgroundColor:"#fff",borderRadius:b.borderRadius.lg,border:`1px solid ${b.colors.primaryBorder}`,boxShadow:b.shadows.md,padding:"16px"},nodeTitle:{display:"flex",justifyContent:"space-between",alignItems:"center",width:"100%",padding:`${b.spacing.xs} ${b.spacing.sm}`,borderRadius:b.borderRadius.sm,transition:b.transitions.default},nodeContent:{display:"flex",gap:b.spacing.md,alignItems:"center",flex:1},nodeIcon:{fontSize:"16px",minWidth:"20px",display:"flex",alignItems:"center",justifyContent:"center"},nodeLabel:{fontWeight:600,minWidth:"80px",color:b.colors.textPrimary,fontSize:"14px"},nodeDescription:{color:b.colors.textSecondary,fontSize:"13px",fontStyle:"italic",flex:1},tagTitle:{display:"flex",alignItems:"center",gap:b.spacing.sm,padding:`${b.spacing.xs} ${b.spacing.sm}`,borderRadius:b.borderRadius.sm,transition:b.transitions.default},tagIcon:{fontSize:"14px",color:b.colors.info},tagLabel:{color:b.colors.info,fontSize:"13px",fontWeight:500},nodeActionButtons:{display:"flex",gap:b.spacing.xs,alignItems:"center",opacity:0,transition:b.transitions.default,paddingLeft:"20px"},actionIconButton:{border:"none",boxShadow:"none",borderRadius:"50%",width:"28px",height:"28px",display:"flex",alignItems:"center",justifyContent:"center",transition:b.transitions.fast},searchHighlight:{backgroundColor:"#ffeb3b",color:"#d32f2f",fontWeight:600,padding:"1px 2px",borderRadius:"2px"},moveAlert:{padding:b.spacing.lg,marginBottom:b.spacing.lg,backgroundColor:b.colors.warningBg,border:`1px solid ${b.colors.warningBorder}`,borderRadius:b.borderRadius.md,display:"flex",alignItems:"center",gap:b.spacing.sm,boxShadow:b.shadows.sm}},yn=`
.hierarchy-tree-container .ant-tree-node-content-wrapper:hover .node-action-buttons {
  opacity: 1 !important;
}
.hierarchy-tree-container .parent-node > .ant-tree-node-content-wrapper {
  font-weight: 600;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.hierarchy-tree-container .tag-node > .ant-tree-node-content-wrapper {
  background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
  border-left: 3px solid ${b.colors.info};
  margin-left: 8px;
}
.hierarchy-tree-container .leaf-node > .ant-tree-node-content-wrapper {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
}
.hierarchy-tree-container .ant-tree-switcher {
  background: none !important;
}
.hierarchy-tree-container .ant-tree-node-selected > .ant-tree-node-content-wrapper {
  background: ${b.colors.selectedBg} !important;
  border-radius: ${b.borderRadius.sm};
}
.hierarchy-tree-container .ant-tree-treenode {
  padding: 2px 0;
}
.hierarchy-tree-container .ant-tree-node-content-wrapper {
  border-radius: ${b.borderRadius.sm};
  transition: ${b.transitions.default};
  min-height: 32px;
  display: flex;
  align-items: center;
}
.hierarchy-tree-container .ant-tree-node-content-wrapper:hover {
  background: ${b.colors.hoverBg} !important;
}
`,_e=s=>{let e=[];const n=t=>{e.push(t.id),t.child&&t.child.forEach(n)};return s.forEach(n),e},$n=(s,e=!1)=>s.child&&s.child.length>0?e?g(Jt,{style:{color:b.colors.warning,...L.nodeIcon}}):g(qt,{style:{color:b.colors.primary,...L.nodeIcon}}):s.tags&&s.tags.length>0?g(Is,{style:{color:b.colors.success,...L.nodeIcon}}):g(nn,{style:{color:b.colors.textSecondary,...L.nodeIcon}}),re=(s,e,n,t="default",a=!1)=>{const c=a?b.colors.error:t==="primary"?"white":b.colors.textPrimary;return g(Ke,{placement:"top",title:n,children:g(ue,{type:t,danger:a,size:"small",icon:le.cloneElement(s,{style:{color:a?"white":c}}),onClick:e,style:{...L.actionIconButton,...t==="primary"&&{backgroundColor:b.colors.primary},...a&&{backgroundColor:b.colors.error,color:"white"}}})})},Ne=(s,e)=>e?g("span",{children:s.split(new RegExp(`(${e})`,"i")).map((n,t)=>n.toLowerCase()===e.toLowerCase()?g("span",{style:L.searchHighlight,children:n},t):n)}):s,pt=(s,e,{searchValue:n,expandedKeys:t,editmode:a,activeAction:c,selectedNode:v,selectedTag:d,object:m,moduleObject:u,handlers:r})=>{const{handleRemoveTag:i,handleSelectTag:O,handleSelectNode:S,handlePlaceNode:x,handlePlaceTag:F,handleDeleteNode:B,setCurrentParent:f,setIsModalVisible:y,setCurrentTagParent:k,setIsTagModalVisible:h,setCurrentEditNode:p,setIsEditModalVisible:D,editForm:Z}=r;return s.map(l=>{var be,se,fe,oe,$;const te=((be=l==null?void 0:l.tags)==null?void 0:be.map((C,I)=>{const w=`${l.id}-tag-${I}`,A=n&&(C.toLowerCase().includes(n.toLowerCase())||`${l.label} Tag`.toLowerCase().includes(n.toLowerCase()));return{title:q("div",{style:L.tagTitle,children:[g(Qe,{style:L.tagIcon}),g("div",{style:L.tagLabel,children:Ne(C,n)||g(Ke,{placement:"bottomLeft",title:`${C} under ${l==null?void 0:l.label}`,children:C})}),a&&q("div",{className:"node-action-buttons",style:L.nodeActionButtons,children:[c==="remove"&&re(g(Es,{}),E=>{E.stopPropagation(),i(C,l)},`Remove ${u} ${C}`,"default",!0),c==="move"&&!v&&!d&&re(g(He,{}),E=>{E.stopPropagation(),O(C,l)},`Move ${u}`,"default")]})]}),key:w,isLeaf:!0,className:"tag-node",style:A?{backgroundColor:b.colors.warningBg}:{}}}))||[],de=l!=null&&l.child?pt(l==null?void 0:l.child,l,{searchValue:n,expandedKeys:t,editmode:a,activeAction:c,selectedNode:v,selectedTag:d,object:m,moduleObject:u,handlers:r}):[],ce=n&&(l==null?void 0:l.label.toLowerCase().includes(n.toLowerCase())),Q=n&&(l==null?void 0:l.description.toLowerCase().includes(n.toLowerCase())),U=ce||Q,ie=t.includes(l==null?void 0:l.id);return{title:q("div",{className:"node-title-container",style:L.nodeTitle,children:[q("div",{style:L.nodeContent,children:[$n(l,ie),g("div",{style:L.nodeLabel,children:Ne(l.label,n)||l.label}),g("div",{style:L.nodeDescription,children:Ne(l.description,n)||l.description})]}),a&&q("div",{className:"node-action-buttons",style:L.nodeActionButtons,children:[c==="add"&&q($t,{children:[!((se=l==null?void 0:l.tags)!=null&&se.length)&&re(g(rt,{}),C=>{C.stopPropagation(),f(l),y(!0)},`Add New Node under ${l==null?void 0:l.label}`,"default"),((fe=l==null?void 0:l.child)==null?void 0:fe.length)===0&&re(g(Qe,{}),C=>{C.stopPropagation(),k(l),h(!0)},`Add New ${u} under ${l==null?void 0:l.label}`,"default"),re(g(Pt,{}),C=>{C.stopPropagation(),p(l),Z.setFieldsValue({description:l.description}),D(!0)},`Change Description for ${l==null?void 0:l.label}`,"default")]}),c==="move"&&l.id!=="1"&&!v&&!d&&re(g(He,{}),C=>{C.stopPropagation(),S(l)},`Move ${l==null?void 0:l.label}`,"default"),v&&(l==null?void 0:l.isParent)&&((oe=l==null?void 0:l.tags)==null?void 0:oe.length)===0&&re(g(jt,{}),C=>{C.stopPropagation(),x(l)},`Put moved Node back to ${l==null?void 0:l.label}`,"primary"),d&&l.isParent&&(($=l==null?void 0:l.child)==null?void 0:$.length)===0&&re(g(qs,{}),C=>{C.stopPropagation(),F(l)},`Put moved ${u} back to ${l==null?void 0:l.label}`,"primary"),c==="delete"&&(l==null?void 0:l.id)!=="1"&&re(g(Ye,{}),C=>{C.stopPropagation(),B(l,e)},`Delete node ${l==null?void 0:l.label}`,"default",!0)]})]}),key:l.id,children:[...de,...te],isLeaf:!l.isParent&&te.length===0,className:l.isParent?"parent-node":"leaf-node",style:U?{backgroundColor:b.colors.warningBg}:{},dataRef:l}})},{Search:xn}=xe,{Item:Ie}=me,Rn=({initialRawTreeData:s=[],editmode:e=!1,object:n="Tag",moduleObject:t=""})=>{const[a,c]=o.useState(""),[v,d]=o.useState([]),[m,u]=o.useState(!0),[r,i]=o.useState(!1),[O,S]=o.useState(!1),[x,F]=o.useState(null),[B,f]=o.useState(null),[y,k]=o.useState(null),[h,p]=o.useState(null),[D,Z]=o.useState(null),[l,te]=o.useState(null),[de,ce]=o.useState(!1),[Q,U]=o.useState("add"),[ie,be]=o.useState(!1),[se,fe]=o.useState(!1),[oe,$]=o.useState(!1),[C]=me.useForm(),[I]=me.useForm(),[w]=me.useForm(),A=ke(),E=Ce(R=>{var N;return(N=R==null?void 0:R.hierarchyData)==null?void 0:N.treeData}),ne=Ce(R=>R.payload.requestorPayload),X=Ce(R=>R.hierarchyData),P=Ce(R=>R.hierarchyData.TreeChanges),H=Ce(R=>R.userManagement.userData);o.useEffect(()=>{A(J(s))},[s,A]),o.useEffect(()=>{d(_e(E))},[E]);const _=R=>{const N=[],he=T=>{N.push({key:T==null?void 0:T.id,label:T==null?void 0:T.label,description:T==null?void 0:T.description,isTag:!1}),T!=null&&T.tags&&(T==null||T.tags.forEach((M,K)=>{N.push({key:`${T.id}-tag-${K}`,label:M,description:`${T.label} Tag`,isTag:!0,parentKey:T.id})})),T!=null&&T.child&&T.child.forEach(he)};return R.forEach(he),N},pe=o.useMemo(()=>_(E),[E]),G=(R,N)=>{const he=wt(),T=(H==null?void 0:H.emailId)||"",M=`/Date(${new Date().getTime()})/`||"";A(St({id:he,type:R,description:N,updatedBy:T,updatedOn:M}))},{handleAddNode:V,handleEditDescription:ye,handleAddTag:$e}=hn({form:C,editForm:w,tagForm:I,rawTreeData:E,currentParent:x,currentEditNode:y,object:n,moduleObject:t,requestorPayload:ne,reduxPayload:X,MODULE_KEY_MAP:xt,currentTagParent:B,setIsTagModalVisible:i,setIsModalVisible:ce,setIsEditModalVisible:S,setExpandedKeys:d,addToChangeLog:G,treeChanges:P,isAddingNode:ie,setIsAddingNode:be,isAddingTag:se,setIsAddingTag:fe,isEditingDescription:oe,setIsEditingDescription:$}),{handleSelectNode:ae,handlePlaceNode:Be,handleSelectTag:De,handlePlaceTag:ht,handleCancelMoveNode:gt,handleCancelMoveTag:mt}=gn({rawTreeData:E,treeChanges:P,selectedNode:h,setSelectedNode:p,setOriginalParent:te,addToChangeLog:G,setSelectedTag:Z,selectedTag:D,object:n,originalParent:l}),{handleRemoveTag:Ct}=mn({rawTreeData:E,treeChanges:P,addToChangeLog:G,object:n,setIsEditModalVisible:S,editForm:w}),{handleDeleteNode:vt}=bn({rawTreeData:E,treeChanges:P,addToChangeLog:G}),We={handleRemoveTag:Ct,handleSelectTag:De,handleSelectNode:ae,handlePlaceNode:Be,handlePlaceTag:ht,handleDeleteNode:vt,setCurrentParent:F,setIsModalVisible:ce,setCurrentTagParent:f,setIsTagModalVisible:i,setCurrentEditNode:k,setIsEditModalVisible:S,editForm:w},Xe=R=>{if(c(R),!R){d([]);return}const N=pe.filter(M=>{const K=M.label.toLowerCase().includes(R.toLowerCase()),ge=M.description.toLowerCase().includes(R.toLowerCase());return K||ge}).map(M=>M.key),he=new Set,T=(M,K)=>{for(const ge of M){if(ge.id===K||K.startsWith(ge.id)&&K.includes("-tag-"))return!0;if(ge.child&&T(ge.child,K))return he.add(ge.id),!0}return!1};N.forEach(M=>{const K=M.includes("-tag-")?M.split("-tag-")[0]:M;T(E,K)}),d([...N,...he]),u(!0)},bt=o.useMemo(()=>pt(E,null,{searchValue:a,expandedKeys:v,editmode:e,activeAction:Q,selectedNode:h,selectedTag:D,object:n,moduleObject:t,handlers:We}),[E,a,h,Q,v,e,D,n,We]);return q("div",{style:L.container,children:[g("style",{children:yn}),q("div",{style:L.actionButtonsContainer,children:[g("div",{style:{display:"flex",gap:"8px",alignItems:"center"},children:e&&q(Je,{children:[g(ue,{type:Q==="add"?"primary":"default",onClick:()=>U("add"),icon:g(rt,{}),style:L.actionButton,children:"Add"}),g(ue,{type:Q==="move"?"primary":"default",onClick:()=>U("move"),icon:g(He,{}),style:L.actionButton,children:"Move"}),g(ue,{type:Q==="remove"?"primary":"default",onClick:()=>U("remove"),icon:g(Ks,{}),style:L.actionButton,children:"Remove"}),g(ue,{type:Q==="delete"?"primary":"default",onClick:()=>U("delete"),icon:g(Ye,{}),danger:Q==="delete",style:L.actionButton,children:"Delete"})]})}),q(Je,{children:[g(ue,{icon:g(Ps,{}),onClick:()=>d(_e(E)),style:L.actionButton,children:"Expand All"}),g(ue,{icon:g(Ns,{}),onClick:()=>d([]),style:L.actionButton,children:"Collapse All"})]})]}),(h||D)&&g(Qt,{message:q("div",{children:[g(Zt,{style:{marginRight:8}}),h?`Moving node "${h.label}" - Click on a parent node to place it`:`Moving tag "${D.tag}" - Click on a parent node to place it`]}),type:"warning",showIcon:!1,style:L.moveAlert,action:g(ue,{size:"small",type:"text",onClick:()=>h?gt():mt(),icon:g(et,{}),children:"Cancel"})}),g("div",{style:L.searchContainer,children:g(xn,{placeholder:`Search Node, Descriptions or ${t}...`,allowClear:!0,onSearch:Xe,onChange:R=>Xe(R.target.value),style:{flex:1},prefix:g(Wt,{})})}),g("div",{className:"hierarchy-tree-container",style:L.treeContainer,children:g(_t,{showLine:{showLeafIcon:!1},showIcon:!1,switcherIcon:g(Xt,{}),treeData:bt,onExpand:R=>{d(R),u(!1)},expandedKeys:v,autoExpandParent:m,height:600,virtual:!0})}),g(Fe,{title:`Add New Node under "${x==null?void 0:x.label}"`,open:de,onOk:()=>C.submit(),onCancel:()=>{ce(!1),C.resetFields(),F(null)},okText:"Add Node",cancelText:"Cancel",confirmLoading:ie,okButtonProps:{disabled:ie},children:q(me,{form:C,layout:"vertical",onFinish:V,initialValues:{isParent:!1},children:[g(Ie,{name:"label",label:"Node Label",rules:[{required:!0,message:"Please enter node label"},{max:10,message:"Label cannot exceed 10 characters"}],children:g(xe,{placeholder:"Enter Node Label",maxLength:10,showCount:!0,style:{textTransform:"uppercase"},onChange:R=>{const N=R.target.value.replace(/[^a-zA-Z0-9_\/-]/g,"");C.setFieldsValue({label:N==null?void 0:N.toUpperCase()})}})}),g(Ie,{name:"description",label:"Description",rules:[{required:!0,message:"Please Enter Description"},{max:40,message:"Description cannot exceed 40 characters"}],children:g(xe,{placeholder:"Enter Node Description",style:{textTransform:"uppercase"},maxLength:40,showCount:!0,onChange:R=>{const N=R.target.value.replace(/[^a-zA-Z0-9-&()#, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#,])\s*/g,"$1").trimStart().toUpperCase();C.setFieldsValue({description:N})}})})]})}),g(Fe,{title:`Add New ${t} to "${(B==null?void 0:B.label)||"node"}"`,open:r,onOk:()=>I.submit(),onCancel:()=>{i(!1),I.resetFields(),f(null)},okText:"Add",cancelText:"Cancel",confirmLoading:se,okButtonProps:{disabled:se},children:g(me,{form:I,layout:"vertical",onFinish:$e,children:g(Ie,{name:"tag",label:`${t}`,rules:[{required:!0,message:`Please enter ${t}`},{max:10,message:`${t} name cannot exceed 10 characters`}],children:g(xe,{placeholder:`Enter ${t}`,style:{textTransform:"uppercase"},maxLength:10,showCount:!0,onChange:R=>{const N=R.target.value.replace(/[^a-zA-Z0-9-&()#, ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#,])\s*/g,"$1").trimStart().toUpperCase();I.setFieldsValue({tag:N})}})})})}),g(Fe,{title:`Edit Description for "${y==null?void 0:y.label}"`,open:O,onOk:()=>w.submit(),onCancel:()=>{S(!1),w.resetFields(),k(null)},okText:"Update Description",cancelText:"Cancel",confirmLoading:oe,okButtonProps:{disabled:oe},children:g(me,{form:w,layout:"vertical",onFinish:ye,children:g(Ie,{name:"description",label:"Description",rules:[{required:!0,message:"Please Enter Description"},{max:40,message:"Description cannot exceed 40 characters"}],children:g(xe,{showCount:!0,maxLength:40,placeholder:"Enter Node Description",onChange:R=>{const N=R.target.value.toUpperCase();w.setFieldsValue({description:N})}})})})})]})};export{Rn as R,pn as u};
