import React, { useEffect, useRef, useState } from "react";
import {
    Typography,
    IconButton,
    Grid,
    Box,
    Stack,
    Tabs,
    Tab,
    Paper,
    Card,
    CardContent,
    Divider,
} from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
    iconButton_SpacingSmall,
    outermostContainer_Information,
} from "@components/Common/commonStyles";
import { doAjax } from "@components/Common/fetchService";
import {
    destination_BankKey,
} from "../../destinationVariables";
import { makeStyles } from "@mui/styles";
import InventoryIcon from "@mui/icons-material/Inventory";
import BusinessIcon from "@mui/icons-material/Business";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import { useDispatch, useSelector } from "react-redux";
import {
    pushMaterialDisplayData,
    setAdditionalData,
    setUOmData,
} from "@app/payloadslice";
import { ERROR_MESSAGES, LOCAL_STORAGE_KEYS, MODULE_MAP } from "@constant/enum";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useBankKeyFieldConfig from "./hooks/useBankKeyFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { clearLocalStorageItem, convertSAPDateForCalendar, setLocalStorage } from "@helper/helper";
import useLogger from "@hooks/useLogger";
import useLang from "@hooks/useLang";
import { setBankKeyTabs, setDropDownDataBNKY, setOdataApiCall } from "./bnkySlice";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData";

const RenderRow = ({ label, value, icon }) => (
    <Grid item xs={6}>
        <Stack flexDirection="row" alignItems="center" spacing={1}>
            {icon && <Box>{icon}</Box>}
            <Typography variant="body2" color={colors.secondary.grey}>
                {label}
            </Typography>
            <Typography variant="body2" fontWeight="bold">
                : {value || ""}
            </Typography>
        </Stack>
    </Grid>
);

const DisplayBankKeySAPView = () => {
    const useStyles = makeStyles(() => ({
        customTabs: {
            "& .MuiTabs-scroller": {
                overflowX: "auto !important",
                overflowY: "hidden !important",
            },
        },
    }));

    const bankKeyTabs = useSelector((state) => {
        const tabsObject = state.bankKey.bankKeyTabs || {};
        const allTabs = Object.values(tabsObject).flat(); 
        const filteredTabs = allTabs.filter((tab) => tab?.tab !== "Initial Screen" 
        );
  return filteredTabs;
});

    const { loading, error, fetchBankKeyFieldConfig } = useBankKeyFieldConfig();
    const navigate = useNavigate();
    const classes = useStyles();
    const location = useLocation();
    const dispatch = useDispatch();
    const structureData = location.state;
    const { customError } = useLogger();
    const { t } = useLang();

    const [tabNames, setTabNames] = useState([]);
    const [tabContentData, setTabContentData] = useState([]);
    const [blurLoading, setBlurLoading] = useState(false);
    const [loaderMessage, setLoaderMessage] = useState("");
    const [selectedTab, setSelectedTab] = useState(0);
    const [selectedRow, setSelectedRow] = useState(null);
    const [selectedRowId, setSelectedRowId] = useState("");
    const hasFetchedData = useRef(false); // NEW: For fetching only once
    const hasMappedTabs = useRef(false);
    const { fetchAllDropdownFMD } = useDropdownFMDData(destination_BankKey, setDropDownDataBNKY);
    const isBankKeyApiCalled = useSelector((state) => state.bankKey?.isOdataApiCalled)

    useEffect(() => {
        if (!isBankKeyApiCalled) {
          fetchAllDropdownFMD("bankKey")
          dispatch(setOdataApiCall(true))
        }
        setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BK)
        return () => {
          clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
          clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
          clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
        }
      }, []);

    useEffect(() => {
    
        if (!bankKeyTabs|| bankKeyTabs.length === 0) {
            fetchBankKeyFieldConfig(structureData?.BankKey, );
        }
    }, []);

    useEffect(() => {
        if (!hasFetchedData.current && structureData?.BankCtry && structureData?.BankKey) {
            hasFetchedData.current = true; // prevent re-fetching
            fetchBankKeyData();
        }
    }, [structureData?.BankCtry, structureData?.BankKey]);

    const handleTabChange = (event, newValue) => {
        setSelectedTab(newValue);
    };

    const mapTabValuesToConfig = (tabs, tabContentData) => {
        
        if (!tabs || !Array.isArray(tabs) || !tabContentData) {
            return [];
        }
        const dtoMap = {
            "Control Data":tabContentData?.toControlData,
            "Address Data":tabContentData?.Tobankaddress,
        };

        return tabs.map((tab) => {
            const tabLabel = tab?.tab;
            if (!tabLabel || !tab?.data) return tab;
            const dto = dtoMap[tabLabel];
            if (!dto) return tab;

            const updatedData = {};
            for (const cardKey in tab.data) {
                if (!tab.data[cardKey] || !Array.isArray(tab.data[cardKey])) {
                    updatedData[cardKey] = tab.data[cardKey];
                    continue;
                }
                updatedData[cardKey] = tab.data[cardKey].map((field) => {
                    if (!field || typeof field !== 'object') return field;
                    const jsonKey = field.jsonName;
                    let value = dto?.[jsonKey];

                    if (field.dataType === "Date" && typeof value === "string" && value.includes("/Date")) {
                        try {
                            value = convertSAPDateForCalendar(value);
                        } catch (customError) {
                            customError("Error converting SAP date:", error);
                            value = field.value; // fallback to original value
                        }
                    }

                    if (typeof value === "boolean") {
                        value = value ? "TRUE" : "FALSE";
                    }

                    return {
                        ...field,
                        value: value ?? field.value,
                    };
                });
            }

            return {
                ...tab,
                data: updatedData,
            };
        });
    };

    // Ref to avoid re-execution

    useEffect(() => {
         const tabsReady = bankKeyTabs && Array.isArray(bankKeyTabs) && bankKeyTabs.length > 0;
        const dataReady = tabContentData && typeof tabContentData === 'object' && Object.keys(tabContentData).length > 0;

        if (tabsReady && dataReady && !hasMappedTabs.current) {
            hasMappedTabs.current = true;

           try {
                const updatedTabs = mapTabValuesToConfig(bankKeyTabs, tabContentData);
                setTabNames(updatedTabs);
            } catch (error) {
                console.error("Error mapping tabs to config:", error);
                // Fallback: set original tabs if mapping fails
                setTabNames(bankKeyTabs);
            }
        }
    }, [bankKeyTabs, tabContentData]);

    const fetchBankKeyData = () => {
          if (!structureData?.BankCtry || !structureData?.BankKey) {
            console.error("Missing required parameters: BankCtry or BankKey");
            return;
        }
        setBlurLoading(true);
        setLoaderMessage("Fetching bank key data...");

        const payload = {
            bankCtry: structureData?.BankCtry,
            bankKey: structureData?.BankKey
        };

        const hSuccess = (data) => {
            setBlurLoading(false);
        setLoaderMessage("");
            
            try {
                const rawData = data?.body?.[0] || {};
                const displayData = {...rawData,
                    toControlData :{
                        SwiftCode:rawData?.Tobankaddress?.SwiftCode,
                        IbanRule:rawData?.IbanRule,
                        BankNo:rawData?.Tobankaddress?.BankNo,
                    }
                }
                setTabContentData(displayData);
            } catch (customError) {
                customError("Error processing response data:", error);
                setTabContentData({});
            }
        };

        const hError = (error) => {
            console.error("Error fetching bank key data", error);
            setBlurLoading(false);
            setLoaderMessage("");
            if (customError) {
                customError("Failed to fetch bank key data. Please try again.");
            }
        };

        doAjax(
            `/${destination_BankKey}/data/displayBankKeySAPData`,
            "post",
            hSuccess,
            hError,
            payload
        );
    };



    return (
        <div style={{ backgroundColor: "#FAFCFF" }}>
            {/* Header Section */}
            <Grid container sx={outermostContainer_Information}>
                <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
                    <Grid md={9} sx={{ display: "flex" }}>
                        <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
                            <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
                        </IconButton>
                        <Grid item md={12}>
                            <Typography variant="h3">
                                <strong>{t("Display Bank Key")}</strong>
                            </Typography>
                            <Typography variant="body2" color="#777">
                                {t("This view displays the details of the Bank Key")}
                            </Typography>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            {/* Top Info Card (mimicking the two-column layout) */}
            <Grid
                container
                display="flex"
                flexDirection="row"
                flexWrap="nowrap"
                sx={{
                    justifyContent: "space-between",
                    alignItems: "center",
                    paddingLeft: "29px",
                    backgroundColor: colors.basic.lighterGrey,
                    borderRadius: "10px",
                    boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
                }}
            >
                {/* Left Side */}
                <Stack
                    width="48%"
                    spacing={1}
                    sx={{
                        padding: "10px 15px",
                        borderRight: "1px solid #eaedf0",
                    }}
                >
                    <Grid item>
                        <RenderRow
                            label={t("Bank Country")}
                            value={structureData?.BankCtry || ""}
                            labelWidth="35%"
                            icon={<InventoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
                        />
                    </Grid>
                    <Grid item>
                        <RenderRow
                            label={t("Bank Key Number")}
                            value={structureData?.BankKey || ""}
                            labelWidth="35%"
                            icon={<BusinessIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
                        />
                    </Grid>
                </Stack>

                {/* Right Side */}
                <Stack
                    width="48%"
                    spacing={1}
                    marginRight={"-10%"}
                    sx={{
                        padding: "10px 15px",
                    }}
                >
                    <Grid item>
                        <RenderRow
                            label={t("Bank Name")}
                            value={structureData?.BankName || ""}
                            labelWidth="35%"
                            icon={<CategoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
                        />
                    </Grid>
                    <Grid item>
                        <RenderRow
                            label={t("Bank Branch")}
                            value={structureData?.BankBranch || ""}
                            labelWidth="35%"
                            icon={<DescriptionIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
                        />
                    </Grid>
                </Stack>
            </Grid>

            {/* Tabs Section */}
            <Grid>
                {tabNames.length > 0 ? (
                    <Box sx={{ mt: 3 }}>
                        <Tabs
                            value={selectedTab}
                            onChange={handleTabChange}
                            indicatorColor="primary"
                            textColor="primary"
                            variant="scrollable"
                            scrollButtons="auto"
                            sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
                        >
                            {tabNames.map((tab, index) => (
                                <Tab key={index} label={tab.tab} />
                            ))}
                        </Tabs>

                        <Paper elevation={2} sx={{ p: 3, borderRadius: 8 }}>
                            {tabNames[selectedTab] && (
                                <GenericTabsGlobal
                                    disabled={true}
                                    basicDataTabDetails={tabNames[selectedTab].data}
                                    dropDownData={""}
                                    activeViewTab={tabNames[selectedTab].tab}
                                    uniqueId={selectedRowId}
                                    selectedRow={selectedRow || {}}
                                    module={MODULE_MAP?.BK}
                                />
                            )}
                        </Paper>
                    </Box>
                ) : (
                    <Box
                        sx={{
                            marginTop: "30px",
                            border: `1px solid ${colors.secondary.grey}`,
                            padding: "16px",
                            background: `${colors.primary.white}`,
                            textAlign: "center",
                        }}
                    >
                        <span>{ERROR_MESSAGES.NO_DATA_AVAILABLE}</span>
                    </Box>
                )}
            </Grid>

            {/* Loader */}
            <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
        </div>
    );

};

export default DisplayBankKeySAPView;
