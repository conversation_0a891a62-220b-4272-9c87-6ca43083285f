import ErrorHistory from "@components/RequestBench/ErrorHistory";
import { Close } from "@mui/icons-material";
import {
  Dialog,
  DialogContent,
  Grid,
  IconButton,
  Box,
  Fade,
  Slide,
} from "@mui/material";
import React from "react";

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const ErrorReportDialog = ({
  closeReusableDialog,
  dialogState,
  onClose,
  isHierarchyCheck = false,
  module,
}) => {
  return (
    <Dialog
      fullWidth
      maxWidth="xl"
      TransitionComponent={Transition}
      transitionDuration={400}
      PaperProps={{
        sx: {
          width: "92vw",
          height: "85vh",
          maxWidth: "none",
          borderRadius: "16px",
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          // backdropFilter: "blur(20px)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
          overflow: "hidden",
          position: "relative",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            // background: "rgba(255, 255, 255, 0.05)",
            // backdropFilter: "blur(10px)",
            zIndex: -1,
          },
        },
      }}
      open={dialogState}
      onClose={onClose ? onClose : closeReusableDialog}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: "absolute",
          top: -50,
          right: -50,
          width: 200,
          height: 200,
          borderRadius: "50%",
          // background: "rgba(255, 255, 255, 0.1)",
          animation: "pulse 4s ease-in-out infinite",
          "@keyframes pulse": {
            "0%, 100%": { transform: "scale(1)", opacity: 0.3 },
            "50%": { transform: "scale(1.1)", opacity: 0.1 },
          },
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: -30,
          left: -30,
          width: 150,
          height: 150,
          borderRadius: "50%",
          // background: "rgba(255, 255, 255, 0.08)",
          animation: "float 6s ease-in-out infinite",
          "@keyframes float": {
            "0%, 100%": { transform: "translateY(0px)" },
            "50%": { transform: "translateY(-20px)" },
          },
        }}
      />

      {/* Close Button */}
      <IconButton
        aria-label="close"
        onClick={onClose ? onClose : closeReusableDialog}
        sx={{
          position: "absolute",
          right: 16,
          top: 16,
          zIndex: 1000,
          backgroundColor: "rgba(0, 0, 0, 0.1)",
          backdropFilter: "blur(10px)",
          border: "1px solid rgba(0, 0, 0, 0.15)",
          color: "#374151",
          width: 44,
          height: 44,
          transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
          "&:hover": {
            backgroundColor: "rgba(0, 0, 0, 0.15)",
            transform: "scale(1.05)",
            color: "#1f2937",
            boxShadow: "0 8px 16px rgba(0, 0, 0, 0.15)",
          },
          "&:active": {
            transform: "scale(0.95)",
          },
        }}
      >
        <Close />
      </IconButton>

      <DialogContent
        sx={{
          padding: 0,
          height: "100%",
          position: "relative",
          overflow: "hidden",
        }}
      >
        <Box
          sx={{
            height: "100%",
            background: "rgba(255, 255, 255, 0.95)",
            backdropFilter: "blur(20px)",
            position: "relative",
            overflow: "hidden",
          }}
        >
          {/* Content Container */}
          <Box
            sx={{
              height: "100%",
              padding: "32px",
              overflow: "auto",
              position: "relative",
              "&::-webkit-scrollbar": {
                width: "8px",
              },
              "&::-webkit-scrollbar-track": {
                background: "rgba(0, 0, 0, 0.1)",
                borderRadius: "4px",
              },
              "&::-webkit-scrollbar-thumb": {
                background: "linear-gradient(135deg, #667eea, #764ba2)",
                borderRadius: "4px",
                "&:hover": {
                  background: "linear-gradient(135deg, #5a6fd8, #6a4190)",
                },
              },
            }}
          >
            <Fade in={dialogState} timeout={600}>
              <Box
                sx={{
                  background: "rgba(255, 255, 255, 0.8)",
                  backdropFilter: "blur(10px)",
                  borderRadius: "16px",
                  border: "1px solid rgba(255, 255, 255, 0.2)",
                  padding: "24px",
                  minHeight: "100%",
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1)",
                  position: "relative",
                  overflow: "hidden",
                  "&::before": {
                    content: '""',
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    height: "4px",
                    background:
                      "linear-gradient(90deg, #667eea, #764ba2, #667eea)",
                    backgroundSize: "200% 100%",
                    animation: "shimmer 3s ease-in-out infinite",
                    "@keyframes shimmer": {
                      "0%": { backgroundPosition: "-200% 0" },
                      "100%": { backgroundPosition: "200% 0" },
                    },
                  },
                }}
              >
                <Grid container>
                  <Grid item xs={12}>
                    <ErrorHistory
                      isHierarchyCheck={isHierarchyCheck}
                      module={module}
                    />
                  </Grid>
                </Grid>
              </Box>
            </Fade>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorReportDialog;
