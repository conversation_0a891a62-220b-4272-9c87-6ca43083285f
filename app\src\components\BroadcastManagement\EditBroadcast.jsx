import {
  <PERSON>rid,
  IconButton,
  <PERSON>ack,
  Typography,
  Box,
  Card,
  FormControl,
  Select,
  TextField,
  Button,
  MenuItem,
  Paper,
  BottomNavigation,
  Chip,
  Tooltip,
  Modal,
} from "@mui/material";
import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import CampaignOutlinedIcon from "@mui/icons-material/CampaignOutlined";
import SlideshowOutlinedIcon from "@mui/icons-material/SlideshowOutlined";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { styled } from "@mui/material/styles";
import moment from "moment";
import { DatePicker } from "rsuite";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { doAjax } from "../Common/fetchService";
import ReusableDialog from "../Common/ReusableDialog";
import ReusableSnackBar from "../Common/ReusableSnackBar";
import { destination_Admin } from "../../destinationVariables";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ReactPlayer from "react-player";
import { MatView } from "../DocumentManagement/UtilDoc";
import { ViewDetailsIcon } from "../Common/icons";
import { useSelector } from "react-redux";
import { getUrlByName } from "@utils/dynamicUrl";
import { END_POINTS } from "@constant/apiEndPoints";

// Google OAuth constants
const CLIENT_ID = "569576829775-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com";
const SCOPES = "https://www.googleapis.com/auth/youtube.upload";

// Styled components matching NewBroadcast
const StyledCard = styled(Card)(({ theme }) => ({
  padding: theme.spacing(3),
  marginBottom: theme.spacing(2),
  borderRadius: theme.spacing(2),
  boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: "18px",
  fontWeight: 600,
  color: theme.palette.text.primary,
  marginBottom: theme.spacing(2),
  borderBottom: `2px solid ${theme.palette.primary.main}`,
  paddingBottom: theme.spacing(1),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius: theme.spacing(1),
  },
}));

const StyledSelect = styled(Select)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  backgroundColor: theme.palette.background.paper,
}));

const UploadBox = styled(Box)(({ theme }) => ({
  border: `2px dashed ${theme.palette.divider}`,
  borderRadius: theme.spacing(2),
  padding: theme.spacing(3),
  textAlign: "center",
  cursor: "pointer",
  transition: "all 0.3s ease",
  "&:hover": {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.hover,
  },
}));

const StatusChip = styled(Chip)(({ status }) => ({
  fontSize: "14px",
  borderRadius: "4px",
  fontWeight: 500,
  backgroundColor:
    status === "Active"
      ? "#cdefd6"
      : status === "Draft"
      ? "#FFC88787"
      : status === "Archived"
      ? "#FAFFC0"
      : "#cddcef",
}));

const EditBroadcast = () => {
  const userData = useSelector((state) => state.userManagement.userData);
  const location = useLocation();
  const navigate = useNavigate();
  const queryParams = new URLSearchParams(location.search);
  const BroadcastId = queryParams.get("BroadcastId");
  
  const presentDate = new Date();
  const futureDate = new Date();
  futureDate.setDate(presentDate.getDate() + 7);

  // Form state
  const [broadcastForm, setBroadcastForm] = useState({
    title: "",
    description: "",
    category: "",
    startDate: presentDate,
    endDate: futureDate,
    module: "",
    files: null,
    file: "",
    status: "",
    link: "",
  });

  // UI state
  const [openSubmit, setOpenSubmit] = useState(false);
  const [openSnackbarSubmit, setOpenSnackbarSubmit] = useState(false);
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [openVideoModal, setOpenVideoModal] = useState(false);
  const [isPublish, setIsPublish] = useState(false);
  const [fieldError, setFieldError] = useState(false);
  const [formErrors, setFormErrors] = useState([]);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  
  // Authentication state
  const [accessToken, setAccessToken] = useState(null);

  // Load Google API on component mount
  useEffect(() => {
    const loadGoogleAPI = () => {
      if (!window.google) {
        const script = document.createElement('script');
        script.src = 'https://accounts.google.com/gsi/client';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
      }
    };

    loadGoogleAPI();
    getBroadcastDetails();
  }, []);

  // Google OAuth authentication function
  const authenticateUser = async () => {
    return new Promise((resolve, reject) => {
      if (window.google) {
        window.google.accounts.oauth2.initTokenClient({
          client_id: CLIENT_ID,
          scope: SCOPES,
          callback: (resp) => {
            if (resp.error) {
              console.error('Authentication error:', resp.error);
              reject(resp.error);
            } else {
              setAccessToken(resp.access_token);
              resolve(resp.access_token);
            }
          },
        }).requestAccessToken();
      } else {
        reject("Google API not loaded");
      }
    });
  };

  // Get authentication token
  const getAuthToken = async () => {
    try {
      const token = accessToken || (await authenticateUser());
      return token;
    } catch (error) {
      console.error('Failed to get authentication token:', error);
      throw error;
    }
  };

  // Get broadcast details
  const getBroadcastDetails = () => {
    const handleSuccess = (data) => {
      const details = data.broadcastDetailsDto;
      setBroadcastForm({
        title: details.broadcastTitle || "",
        description: details.description || "",
        category: details.broadcastCategory || "",
        startDate: details.createdDate ? new Date(details.createdDate) : presentDate,
        endDate: details.endDate ? new Date(details.endDate) : futureDate,
        createdBy: details.createdBy || "",
        module: details.module || "",
        file: details.fileName || "",
        status: details.status || "",
        link: details.externalUrl || "",
        files: null,
      });
    };

    const handleError = (error) => {
      console.error('Error fetching broadcast details:', error);
    };

    doAjax(
      `/${destination_Admin}/broadcastManagement/getBroadcastDetailsById/${BroadcastId}`,
      'get',
      handleSuccess,
      handleError
    );
  };

  // Form handlers
  const handleInputChange = (field, value) => {
    setBroadcastForm(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (formErrors.includes(field)) {
      setFormErrors(prev => prev.filter(error => error !== field));
    }
  };

  const handleFileChange = (e) => {
    setBroadcastForm(prev => ({ ...prev, files: e.target.files }));
  };

  // Validation
  const validateForm = () => {
    const errors = [];
    const required = ['category', 'title', 'description'];
    
    if (broadcastForm.module) {
      required.push('module');
    }
    
    required.forEach(field => {
      if (!broadcastForm[field] || broadcastForm[field].trim() === '') {
        errors.push(field);
      }
    });
    
    if (broadcastForm.startDate >= broadcastForm.endDate) {
      errors.push('dateRange');
    }
    
    setFormErrors(errors);
    return errors.length === 0;
  };

  // Check if form is disabled
  const isFormDisabled = () => {
    return ["Inactive", "Archived"].includes(broadcastForm.status);
  };

  // Video upload API call
  const uploadFileToBackend = async (file, authToken) => {
    const baseUrl = getUrlByName(destination_Admin);
    setBlurLoading(true);
    const formData = new FormData();
    formData.append("file", file);
    
    if (authToken) {
      formData.append("accessToken", authToken);
    }

    const response = await fetch(`${baseUrl}${END_POINTS?.API?.UPLOAD_VIDEO}`, {
        method: "POST",
        body: formData,
    });

    if (!response.ok) {
        const text = await response.text();
        throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${text}`);
    }

    return await response.json();
  };

  // Create form data for submission
  const createFormData = async (isDraft = false) => {
    const formData = new FormData();
    
    if (broadcastForm.files) {
      [...broadcastForm.files].forEach(file => formData.append("files", file));
    }
    
    const broadcastDetails = {
      broadcastId: BroadcastId,
      broadcastCategory: broadcastForm.category,
      broadcastTitle: broadcastForm.title,
      createdBy: userData?.displayName || "",
      startDate: moment(broadcastForm.startDate).format("YYYY-MM-DD HH:mm:ss.000"),
      endDate: moment(broadcastForm.endDate).format("YYYY-MM-DD HH:mm:ss.000"),
      description: broadcastForm.description,
      module: broadcastForm.module,
      createdDate: moment(presentDate).format("YYYY-MM-DD HH:mm:ss.000"),
      externalUrl: broadcastForm.link,
      ...(isDraft && { status: "Draft" })
    };
    
    formData.append("broadcastDetails", JSON.stringify(broadcastDetails));
    return formData;
  };

  // Handle form submission
  const handleSubmit = async (isDraft = false) => {
    if (!validateForm()) {
      setFieldError(true);
      setOpenMessageDialog(true);
      return;
    }

    try {
      let authToken = null;

      // Get authentication token for video uploads
      if (broadcastForm.category === "Videos" && !isDraft && broadcastForm.files && broadcastForm.files.length > 0) {
        setIsAuthenticating(true);
        
        try {
          authToken = await getAuthToken();
        } catch (error) {
          setIsAuthenticating(false);
          setFieldError(true);
          setOpenMessageDialog(true);
          return;
        }
        
        setIsAuthenticating(false);

        const videoUploadPromises = [...broadcastForm.files].map(file => 
          uploadFileToBackend(file, authToken)
        );
        
        try {
          const videoUploadResults = await Promise.all(videoUploadPromises);
        } catch (uploadError) {
          console.error('Video upload failed:', uploadError);
          setFieldError(true);
          setOpenMessageDialog(true);
          return;
        }
      }
      else {
        setBlurLoading(true);
      }
      
      const formData = await createFormData(isDraft);
      
      const handleSuccess = (data) => {
        setOpenSubmit(false);
        setBlurLoading(false);
        setMessageDialogMessage("Broadcast updated successfully!")
        setOpenSnackbarSubmit(true);
      };
      
      const handleError = (error) => {
        console.error('Error updating broadcast:', error);
        setFieldError(true);
        setOpenMessageDialog(true);
        setBlurLoading(false);
        setMessageDialogMessage("Failed Updating Broadcast!")
        setOpenSnackbarSubmit(true);
      };

      doAjax(
        `/${destination_Admin}/broadcastManagement/updateBroadcastDetails`,
        'putformdata',
        handleSuccess,
        handleError,
        formData
      );

    } catch (error) {
      console.error('Error in submission process:', error);
      setFieldError(true);
      setOpenMessageDialog(true);
      setBlurLoading(false);
    }
  };

  // Event handlers
  const handleFormSubmit = () => {
    setOpenSubmit(true);
  };

  const handleConfirmSubmit = () => {
    handleSubmit(isPublish ? false : true);
  };

  const handleOpenVideoModal = () => {
    setOpenVideoModal(true);
  };

  const getFileAcceptTypes = () => {
    return broadcastForm.category === "Videos" ? ".mp4" : ".jpeg,.jpg,.png";
  };

  const getFileTypeText = () => {
    return broadcastForm.category === "Videos" 
      ? "Only MP4 format supported" 
      : "Only PNG, JPEG, JPG formats supported";
  };

  const source = `/${destination_Admin}/broadcastManagement/showBroadcastById/${BroadcastId}`;

  return (
    <Box sx={{ padding: 2, paddingBottom: 10 }}>
      {/* Header */}
      <Grid container sx={{ borderRadius: 2, marginBottom: 2 }}>
        <Grid container>
          <Grid item md={7} style={{ padding: "16px", paddingLeft: "" }}>
            <Stack direction="row">
              <IconButton
                onClick={() => navigate("/configCockpit/broadcastConfigurations")}
                color="primary"
                aria-label="back"
              >
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    color: "#000000",
                  }}
                />
              </IconButton>
              <Box>
                <Typography variant="h5" paddingTop="0.3rem" fontSize="20px">
                  <strong>Edit Broadcast: {BroadcastId}</strong>
                </Typography>
                <Typography variant="body2" color="#777" fontSize="12px">
                  This view displays the details of the broadcast and allows you to edit it
                </Typography>
              </Box>
            </Stack>
          </Grid>
        </Grid>
      </Grid>

      {/* Main Form */}
      <StyledCard>
        <SectionTitle>Broadcast Configuration</SectionTitle>
        
        <Grid container spacing={3}>
          {/* First Row */}
          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Category <span style={{ color: "red" }}>*</span>
            </Typography>
            <FormControl fullWidth size="small">
              <StyledSelect
                value={broadcastForm.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                displayEmpty
                disabled={isFormDisabled()}
                error={formErrors.includes('category')}
                renderValue={(selected) => {
                  if (!selected) {
                    return <Typography color="text.secondary">Select Category</Typography>;
                  }
                  return selected;
                }}
              >
                <MenuItem value="">
                  <Typography color="text.secondary">Select Category</Typography>
                </MenuItem>
                <MenuItem value="Announcements">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <CampaignOutlinedIcon color="primary" />
                    <Typography>Announcements</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem value="Videos">
                  <Stack direction="row" spacing={1} alignItems="center">
                    <SlideshowOutlinedIcon color="primary" />
                    <Typography>Videos</Typography>
                  </Stack>
                </MenuItem>
              </StyledSelect>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Module {broadcastForm.module && <span style={{ color: "red" }}>*</span>}
            </Typography>
            <FormControl fullWidth size="small">
              <SingleSelectDropdown
                options={[
                  { code: "Material", desc: "" },
                  { code: "Cost Center", desc: "" }
                ]}
                value={broadcastForm.module}
                onChange={(newValue) => handleInputChange('module', newValue?.code)}
                placeholder="Select Module"
                disabled={isFormDisabled()}
                minWidth="100%"
                error={formErrors.includes('module')}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Start Date <span style={{ color: "red" }}>*</span>
            </Typography>
            <DatePicker
              size="sm"
              placeholder="Select Start Date"
              value={broadcastForm.startDate}
              onChange={(date) => handleInputChange('startDate', date)}
              format="dd MMM yyyy"
              disabled={isFormDisabled()}
              style={{ width: '100%', height: '40px' }}
            />
          </Grid>

          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              End Date <span style={{ color: "red" }}>*</span>
            </Typography>
            <DatePicker
              size="sm"
              placeholder="Select End Date"
              value={broadcastForm.endDate}
              onChange={(date) => handleInputChange('endDate', date)}
              format="dd MMM yyyy"
              disabled={isFormDisabled()}
              style={{ width: '100%', height: '40px' }}
            />
          </Grid>

          {/* Status Row */}
          <Grid item xs={12} md={6} lg={3}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Status
            </Typography>
            <StatusChip
              status={broadcastForm.status}
              label={broadcastForm.status || "Draft"}
              sx={{ marginTop: 1 }}
            />
          </Grid>

          {/* Second Row */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Title <span style={{ color: "red" }}>*</span>
              <Typography component="span" variant="caption" color="text.secondary">
                {" "}(Max 100 characters)
              </Typography>
            </Typography>
            <StyledTextField
              fullWidth
              placeholder="Enter broadcast title"
              value={broadcastForm.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              disabled={isFormDisabled()}
              error={formErrors.includes('title')}
              inputProps={{ maxLength: 100 }}
              helperText={`${broadcastForm.title.length}/100`}
            />
          </Grid>

          <Grid item xs={12}>
            <Typography variant="subtitle2" gutterBottom>
              Broadcast Description <span style={{ color: "red" }}>*</span>
              <Typography component="span" variant="caption" color="text.secondary">
                {" "}(Max 300 characters)
              </Typography>
            </Typography>
            <StyledTextField
              fullWidth
              multiline
              rows={4}
              placeholder="Enter broadcast description"
              value={broadcastForm.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              disabled={isFormDisabled()}
              error={formErrors.includes('description')}
              inputProps={{ maxLength: 300 }}
              helperText={`${broadcastForm.description.length}/300`}
            />
          </Grid>

          {/* Current File Display */}
          {broadcastForm.file && (
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                Current Document
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Typography variant="body2">
                  {broadcastForm.file}
                </Typography>
                {broadcastForm.category === "Videos" ? (
                  <Tooltip title="View Video">
                    <IconButton onClick={handleOpenVideoModal} size="small">
                      {ViewDetailsIcon}
                    </IconButton>
                  </Tooltip>
                ) : (
                  <MatView
                    index={BroadcastId}
                    name={broadcastForm.file}
                    isBroadcast={true}
                  />
                )}
              </Box>
            </Grid>
          )}

          {/* File Upload and URL Row */}
          {!isFormDisabled() && (
            <>
              <Grid item xs={12} md={8}>
                <Typography variant="subtitle2" gutterBottom>
                  Upload New Document
                </Typography>
                <UploadBox>
                  <input
                    accept={getFileAcceptTypes()}
                    style={{ display: 'none' }}
                    id="file-upload"
                    multiple
                    type="file"
                    onChange={handleFileChange}
                  />
                  <label htmlFor="file-upload">
                    <Stack spacing={1} alignItems="center">
                      <CloudUploadIcon color="primary" sx={{ fontSize: 40 }} />
                      <Typography variant="body2">
                        Click to upload new files
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getFileTypeText()}
                      </Typography>
                      {broadcastForm.files && (
                        <Typography variant="caption" color="primary">
                          {broadcastForm.files.length} file(s) selected
                        </Typography>
                      )}
                    </Stack>
                  </label>
                </UploadBox>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography variant="subtitle2" gutterBottom>
                  External URL
                </Typography>
                <StyledTextField
                  fullWidth
                  placeholder="Enter URL (optional)"
                  value={broadcastForm.link}
                  onChange={(e) => handleInputChange('link', e.target.value)}
                  type="url"
                />
              </Grid>
            </>
          )}
        </Grid>
      </StyledCard>

      {/* Fixed Bottom Actions */}
      {!isFormDisabled() && (
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 5 }}
          elevation={2}
        >
          <BottomNavigation
            showLabels
            className="container_BottomNav"
            sx={{
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setIsPublish(false);
                handleFormSubmit();
              }}
              className='btn-mr'
              sx={{ marginRight: 1 }}
              disabled={isAuthenticating}
            >
              Save As Draft
            </Button>
            <Button
              size="small"
              variant="contained"
              onClick={() => {
                setIsPublish(true);
                handleFormSubmit();
              }}
              disabled={isAuthenticating}
            >
              {isAuthenticating ? "Authenticating..." : "Publish"}
            </Button>
          </BottomNavigation>
        </Paper>
      )}

      {/* Video Modal */}
      <Modal
        open={openVideoModal}
        onClose={() => setOpenVideoModal(false)}
        aria-labelledby="video-modal"
      >
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            bgcolor: "background.paper",
            borderRadius: 2,
            boxShadow: 24,
            p: 4,
            maxWidth: '80vw',
            maxHeight: '80vh',
          }}
        >
          <ReactPlayer url={source} controls width="100%" height="400px" />
        </Box>
      </Modal>

      {/* Dialogs */}
      <ReusableDialog
        dialogState={openSubmit}
        closeReusableDialog={() => setOpenSubmit(false)}
        dialogTitle="Confirm Broadcast Update"
        dialogMessage={`Are you sure you want to ${isPublish ? 'publish' : 'save as draft'} this broadcast update?${
          broadcastForm.category === "Videos" && isPublish ? ' You will need to authenticate with Google for video upload.' : ''
        }`}
        handleDialogConfirm={handleConfirmSubmit}
        handleDialogReject={() => setOpenSubmit(false)}
        showCancelButton={true}
        dialogCancelText="Cancel"
        dialogOkText={isPublish ? "Publish" : "Save Draft"}
        dialogSeverity="success"
      />

      <ReusableSnackBar
        openSnackBar={openSnackbarSubmit}
        alertMsg={messageDialogMessage}
        alertType={alertType}
        handleSnackBarClose={() => {
          setOpenSnackbarSubmit(false);
          navigate("/configCockpit/broadcastConfigurations");
        }}
      />

      <ReusableDialog
        dialogState={openMessageDialog}
        closeReusableDialog={() => setOpenMessageDialog(false)}
        dialogTitle="Validation Error"
        dialogMessage="Please fill in all required fields correctly."
        handleDialogConfirm={() => setOpenMessageDialog(false)}
        dialogOkText="OK"
        dialogSeverity="error"
      />

      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </Box>
  );
};

export default EditBroadcast;