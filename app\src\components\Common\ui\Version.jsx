import { Container, Typography, Paper } from '@mui/material';
import versionData from '../../../../version.json';
const {VITE_ENV} = import.meta.env;

const Version = () => {
  const { buildMajor, buildMinor, buildRevision, commitHash, buildDate, branchName, developerName } = versionData;
  const version = `${buildMajor}.${buildMinor}.${buildRevision} `;

  return (
    <Container maxWidth="sm" style={{ padding: '20px' }}>
      <Paper elevation={3} style={{ padding: '20px' }}>
        <Typography variant="h5" gutterBottom>
          Version Information
        </Typography>
        <Typography variant="body1">
          <strong>Version:</strong> {version}{VITE_ENV}
        </Typography>
        <Typography variant="body1">
          <strong>Branch:</strong> {branchName}
        </Typography>
        <Typography variant="body1">
          <strong>Commit Hash:</strong> {commitHash}
        </Typography>
        <Typography variant="body1">
          <strong>Name:</strong> {developerName}
        </Typography>
        <Typography variant="body1">
          <strong>Build Date/Time:</strong> {new Date(buildDate).toLocaleString()}
        </Typography>
      </Paper>
    </Container>
  );
};

export default Version;