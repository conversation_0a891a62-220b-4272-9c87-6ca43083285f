import React, { useState, useEffect } from "react";
import {
  BottomNavigation,
  Button,
  Grid,
  Paper,
  Typography,
  Box,
  Tooltip,
  Checkbox,
  IconButton,
} from "@mui/material";
import { Stack } from "@mui/system";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import InfoIcon from '@mui/icons-material/Info';
import ReusableTable from '@components/Common/ReusableTable';
import BankKeyFilters from '@bankKey/BankKeyFilters';
import {
  outermostContainer,
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import { 
  clearPayload, 
  clearRequiredFields, 
  setChangeFieldRows, 
  setChangeFieldRowsDisplay 
} from '@app/payloadSlice';
import { clearHeaderFieldsBnky } from "./bnkySlice";
import { commonFilterClear } from "@app/commonFilterSlice";
import { clearPaginationData } from "@app/paginationSlice";
import { setTaskData } from "@app/userManagementSlice";
import { commonSearchBarClear } from "@app/commonSearchBarSlice";
import useLang from "@hooks/useLang";
import useLogger from "@hooks/useLogger";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { 
  API_CODE, 
  DECISION_TABLE_NAME, 
  MODULE_MAP, 
  PAGESIZE, 
  VISIBILITY_TYPE 
} from "@constant/enum";
import { doAjax } from "@components/Common/fetchService";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { destination_BankKey } from "../../destinationVariables";
import HistoryIcon from '@mui/icons-material/History';
import { v4 as uuidv4 } from "uuid";

const BankKey = () => {
  const { customError } = useLogger();
  const { getDtCall, dtData } = useGenericDtCall();
  const { getDtCall: getMasterDataColumn, dtData: masterDataDtResponse } = useGenericDtCall();
  const { getDtCall: getSearchParams, dtData: dtSearchParamsResponse } = useGenericDtCall();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useLang();

  // State management
  const [displayFlag, setDisplayFlag] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [IsFilterDropDownLoading, setIsFilterDropDownLoading] = useState(false);
  const [clearClicked, setClearClicked] = useState(false);
  const [value, setValue] = useState(null);
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [pageSize, setPageSize] = useState(PAGESIZE?.TOP_SKIP);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const [searchParameters, setSearchParameters] = useState([]);
  const [statusOfSelectAllFirstData, setStatusOfSelectAllFirstData] = useState(false);
  const [items, setItem] = useState();
  const [roCount, setroCount] = useState(0);
  const [Count, setCount] = useState(0);
  const [page, setPage] = useState(0);
  const [isCheckboxSelected, setIsCheckboxSelected] = useState(true);

   const bkSearchForm = useSelector(
      (state) => state.commonFilter["BankKey"]
    );

  let dynamicDataApis = {};

  // API and data fetching functions
  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    let payload = {

    };
    setIsFilterDropDownLoading(true);
    
    const hSuccess = (data) => {
      setIsFilterDropDownLoading(false);
      const newOptions = data.body;
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
    };
    
    const hError = (error) => {
      setIsFilterDropDownLoading(false);
    };
    
    if (selectedItem === MODULE_MAP?.BK) { 
      doAjax(apiEndpoint, "post", hSuccess, hError, payload); 
    } else { 
      doAjax(apiEndpoint, "get", hSuccess, hError); 
    }
  };

  const fetchMasterDataColumns = (region) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": MODULE_MAP?.BK,
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
        },
      ],
    };
    getMasterDataColumn(payload);
  };

  const fetchSearchParameterFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": MODULE_MAP?.BK,
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
        },
      ],
    };
    getSearchParams(payload);
  };

  // Table column creation functions
  const createMultiValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
      const displayCount = values.length - 1;

      if (values.length === 0) return "-";

      const formatText = (text) => {
        const [code, ...rest] = text.split('-');
        return (
          <>
            <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
          </>
        );
      };

      return (
        <Box sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          minWidth: 0
        }}>
          <Tooltip title={values[0]} placement="top" arrow>
            <Typography
              variant="body2"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                flex: 1,
                minWidth: 0,
              }}
            >
              {formatText(values[0])}
            </Typography>
          </Tooltip>
          {displayCount > 0 && (
            <Box sx={{
              display: "flex",
              alignItems: "center",
              ml: 1,
              flexShrink: 0
            }}>
              <Tooltip
                arrow
                placement="right"
                title={
                  <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Additional {displayName}s ({displayCount})
                    </Typography>
                    {values.slice(1).map((value, idx) => (
                      <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                        {formatText(value)}
                      </Typography>
                    ))}
                  </Box>
                }
              >
                <Box sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer"
                }}>
                  <InfoIcon
                    sx={{
                      fontSize: "1rem",
                      color: "primary.main",
                      "&:hover": { color: "primary.dark" }
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      ml: 0.5,
                      color: "primary.main",
                      fontSize: "11px"
                    }}
                  >
                    +{displayCount}
                  </Typography>
                </Box>
              </Tooltip>
            </Box>
          )}
        </Box>
      );
    }
  });

  const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: t(displayName),
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const [firstPart, ...rest] = params.value?.split(" - ") || [];
      return (
        <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
          <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
        </span>
      );
    },
  });

  const displayCell = () => (
    {
      field: "dataValidation",
      headerName: t("Audit History"),
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const handleChangelogClick = (event) => {
          event.stopPropagation();
          navigate(APP_END_POINTS?.AUDIT_LOG, {
          state: {
              materialNumber: params.row.BankKey,
              module: MODULE_MAP?.BK
            }
          });
        };
  
        return (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <Tooltip title="View Audit Log" placement="top">
              <IconButton
                onClick={handleChangelogClick}
                size="small"
                sx={{
                  color: 'primary.main',
                  marginLeft:'20px',
                  '&:hover': {
                    color: 'primary.dark',
                    backgroundColor: 'rgba(25, 118, 210, 0.04)',
                    transform: 'scale(1.05)',
                    marginLeft:'20px'
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <HistoryIcon sx={{ fontSize: '1.5rem' }} />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  );

  const createMasterDataColums = (data) => {
    const columns = [];
    let sortedData = data?.sort(
      (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
    ) || [];
    
    if (sortedData) {
      sortedData?.forEach((item) => {
        if (item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY) {
          if (item?.MDG_MAT_UI_FIELD_NAME) {
            const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
            const headerName = item.MDG_MAT_UI_FIELD_NAME;
            
            if(fieldName==="DataValidation"){
              columns.push(displayCell());
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
              columns.push(createMultiValueCell(fieldName, headerName));
            } else if (item.MDG_MAT_FIELD_TYPE === "Single") {
              columns.push(createSingleValueCell(fieldName, headerName));
            }
          }
        }
      });
    }
    return columns;
  };

  // Event handlers
  const handleSearchAction = (value) => {
    if (!value) {
      setroCount(Count);
      setPage(0);
      setTableData([...rmDataRows]);
      return;
    }
    
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      for (let k = 0; k < keys.length; k++) {
        rowMatched = !row[keys[k]]
          ? false
          : row?.[keys?.[k]] &&
          row?.[keys?.[k]]
            .toString()
            .toLowerCase()
            ?.indexOf(value?.toLowerCase()) != -1;

        if (rowMatched) break;
      }
      return rowMatched;
    });

    setTableData([...selected]);
    setroCount(selected?.length);
  };

  const getFilter = (fetchSkip) => {
  setTableLoading(true);
  //might remove later
  // if (!fetchSkip) {
  setPage(0);
  //   setPageSize(10);
  //   setSkip(0);
  // }
  
  let payload = {
    bankCountry: bkSearchForm?.BankCtry?? "",
    bankKey: bkSearchForm?.BankKey?? "",
    bankName: bkSearchForm?.BankName?? "",
    swiftCode: bkSearchForm?.swiftCode ?? "",
    bankNumber: bkSearchForm?.BankNo ?? "",
    createdBy: bkSearchForm?.createdBy ?? "",
    region: bkSearchForm?.Region ?? "",
    branch: bkSearchForm?.BankBranch ?? "",
    street: bkSearchForm?.StreetLng ?? "",
    city: bkSearchForm?.City ?? "",
    top: 100,
    skip: 0,
  };

  const hSuccess = (data) => {
    if (data?.statusCode === API_CODE.STATUS_200) {
    var rows = [];
    for (let index = 0; index < data?.body?.list?.length; index++) {
      var tempObj = data?.body?.list[index];
      var tempRow = {
          id: uuidv4(),
          BankName: tempObj["BankName"] !== "" ? `${tempObj["BankName"]}` : "Not Available",
          BankKey: tempObj["BankKey"] !== "" ? `${tempObj["BankKey"]}` : "Not Available",
          BankNo: tempObj["BankNumber"] !== "" ? `${tempObj["BankNumber"]}` : "Not Available",
          BankBranch: tempObj["BankBranch"] !== "" ? `${tempObj["BankBranch"]}` : "Not Available",
          BankCtry: tempObj["BankCtry"] !== "" ? `${tempObj["BankCtry"]}` : "Not Available",
          Region: tempObj["region"] !== "" ? `${tempObj["Region"]}` : "Not Available",
          StreetLng: tempObj["Street"] !== "" ? `${tempObj["Street"]}` : "Not Available",
          City: tempObj["City"] !== "" ? `${tempObj["City"]}` : "Not Available",
          //might remove later
          // If you have additional fields in response, add them here
          // swiftCode: tempObj?.SWIFTCode ?? "Not Available",
          // createdOn: tempObj["CreatedOn"] !== ""
          //   ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
          //   : "Not Available",
          // createdBy: tempObj["CreatedBy"] !== ""
          //   ? `${tempObj["CreatedBy"]}`
          //   : "Not Available",
        };
        rows.push(tempRow);
      }

      setRmDataRows(rows);
      setTableLoading(false);
      setroCount(data.body?.count);
      setCount(data.count);
      dispatch(commonSearchBarClear({ module: "BankKey" }));
    } else if (data.statusCode === 400) {
      setSearchDialogTitle("Warning");
      setSearchDialogMessage(
        "Please Select Lesser Fields as the URL is getting too long !!"
      );
      handleSearchDialogClickOpen();
    }
  };

  const hError = (error) => {
    console.log(error);
    setTableLoading(false); // Make sure to stop loading on error
  };

  doAjax(
    `/${destination_BankKey}/data/getBankKeysBasedOnAdditionalParams`, // Updated endpoint
    "post",
    hSuccess,
    hError,
    payload
  );
};

  const getFilterBasedOnPagination = () => {
    setTableLoading(true);
    //might remove later
    // let payload = {
    //   top: pageSize,
    //   skip: pageSize * (page) ?? 0,
    //   fetchCount: false,
    // };
    // Add your pagination logic here
    let payload = {
    bankCountry: bkSearchForm?.bankCountry?? "",
    bankKey: bkSearchForm?.bankKey?? "",
    bankName: bkSearchForm?.bankName?? "",
    swiftCode: bkSearchForm?.swiftCode ?? "",
    bankNumber: bkSearchForm?.bankNumber ?? "",
    createdBy: bkSearchForm?.createdBy?.code ?? "",
    region: bkSearchForm?.Region ?? "",
    branch: bkSearchForm?.branch ?? "",
    street: bkSearchForm?.street ?? "",
    city: bkSearchForm?.city ?? "",
    top: 100,
    // skip: 100 * page ?? 0,
    skip: rmDataRows?.length ?? 0,
  };

  const hSuccess = (data) => {
    // let data = demoData;
      var rows = [];
      for (let index = 0; index < data?.body?.list?.length; index++) {
        var tempObj = data?.body.list[index];

        var tempRow = {
          id: uuidv4(),
          bankName: tempObj?.BankName,
          bankKey: tempObj?.BankKey,
          bankNumber: tempObj?.BankNumber,
          bankBranch: tempObj?.BankBranch,
          bankCountry: tempObj?.BankCtry,
          region: tempObj?.Region,
          street: tempObj?.Street,
          city: tempObj?.City,
          //might remove later
          // If you have additional fields in response, add them here
          // swiftCode: tempObj?.SWIFTCode ?? "Not Available",
          // createdOn: tempObj["CreatedOn"] !== ""
          //   ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
          //   : "Not Available",
          // createdBy: tempObj["CreatedBy"] !== ""
          //   ? `${tempObj["CreatedBy"]}`
          //   : "Not Available",
        };
        rows.push(tempRow);
      }

      setRmDataRows((prevRows) => [...prevRows, ...rows]); // Assuming you have setBkDataRows or similar state setter
      setTableLoading(false);
      setroCount(rows.length);
      setCount(data?.body?.count);
    }
  const hError = (error) => {
    console.log(error);
    // setTableLoading(false); // Make sure to stop loading on error
  };

  doAjax(
    `/${destination_BankKey}/data/getBankKeysBasedOnAdditionalParams`, // Updated endpoint
    "post",
    hSuccess,
    hError,
    payload
  );
  };

  useEffect(() => {
    //might remove later
      // if(page!==0) {
      //   if ((parseInt(page) + 1) * parseInt(pageSize) >= parseInt(skip) + 100) {
      //     getFilterBasedOnPagination();
      //     // setSkip((prev) => prev + 500);
      //   }
      // }
      if (page * pageSize >= rmDataRows?.length) {
        getFilterBasedOnPagination();
        // setSkip((prev) => prev + 500);
      }
    }, [page, pageSize]);

  const onRowsSelectionHandler = (ids) => {
    const selectedRowsData = ids.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    // Handle selection logic
  };

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  const handleSelectAllData = () => {
    setStatusOfSelectAllFirstData(true);
    getFilterAfterSelectAllOptions();
  };

  const handleFirstPageOptions = () => {
    setStatusOfSelectAllFirstData(true);
    setPage(0);
  };

  const getFilterAfterSelectAllOptions = () => {
    setPage(0);
    setTableLoading(true);
    let payload = {
      top: Count,
      skip: 0,
    };
    
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        // setRmDataRows(rows.reverse());
        setTableLoading(false);
        // setPage(Math.floor(rows?.length / pageSize));
        setroCount(data.count);
        setCount(data.count);
        dispatch(commonSearchBarClear({ module: "MaterialMgmt" }));
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        // showToast(data?.message, "error");
        setTableLoading(false);
      }
    };
    
    const hError = (error) => {
      customError(error);
    };
    
    // doAjax(apiEndpoint, "post", hSuccess, hError, payload);
  };

  const refreshPage = () => {
    getFilter();
  };

  const resetRedux = () => {
    dispatch(clearPayload());
    dispatch(clearRequiredFields());
    dispatch(clearPaginationData());
    dispatch(setChangeFieldRows([]));
    dispatch(setChangeFieldRowsDisplay({}));
    dispatch(setTaskData({}));
  };

  // Effects
  useEffect(() => {
    if (clearClicked) {
      getFilter();
      setClearClicked(false);
    }
  }, [clearClicked]);

  useEffect(() => {
    resetRedux();
    fetchOptionsForDynamicFilter([dynamicDataApis]);
    return () => {
      dispatch(commonFilterClear({
        module: "BankKey",
        days: 7
      }));
    };
  }, []);

  useEffect(() => setTableData([...rmDataRows]), [rmDataRows]);

  useEffect(() => {
    if (masterDataDtResponse) {
      const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
      setDynamicColumns(columnsGlobal);
    }
    if (dtSearchParamsResponse) {
      const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
      const additionalData = response?.filter((item) => {
        return item.MDG_MAT_FILTER_TYPE === "Additional";
      }).map((item) => {
        return { title: t(item.MDG_MAT_UI_FIELD_NAME) };
      });
      setSearchParameters(response);
      setItem(additionalData);
    }
  }, [masterDataDtResponse, dtSearchParamsResponse]);

  useEffect(() => {
    getFilter();
  }, [pageSize]);

  useEffect(() => {
    fetchMasterDataColumns("US");
    fetchSearchParameterFromDt();
  }, []);

  useEffect(() => {
    if (!statusOfSelectAllFirstData) {
      if (page != 0 && page * pageSize >= rmDataRows?.length) {
        getFilterBasedOnPagination();
      }
    }
  }, [page, pageSize]);

  return (
    <>
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          <Grid container mt={0} sx={outermostContainer_Information}>
            <Grid item md={5}>
              <Typography variant="h3">
                <strong>{t("Bank Key")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the Bank Key")}
              </Typography>
            </Grid>
            
            <BankKeyFilters
              searchParameters={searchParameters}
              onSearch={getFilter}
              onClear={() => setClearClicked(true)}
              filterFieldData={filterFieldData}
              setFilterFieldData={setFilterFieldData}
              items={items}
            />
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={tableLoading}
                paginationLoading={tableLoading}
                module={"Bank Key"}
                width="100%"
                title={t("List of Bank Key") + " (" + roCount + ")"}
                rows={tableData ?? []}
                columns={dynamicColumns ?? []}
                showSearch={true}
                showRefresh={true}
                showSelectedCount={true}
                showExport={true}
                onSearch={(value) => handleSearchAction(value)}
                onRefresh={refreshPage}
                pageSize={pageSize}
                page={page}
                onPageSizeChange={handlePageSizeChange}
                rowCount={roCount ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                tempheight={'calc(100vh - 320px)'}
                onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  const bankKey = params.row.BankKey;
                  // const matlType = params?.row?.materialType?.split(" - ")[0];
                  setDisplayFlag(true);

                  navigate(
                    `/masterDataCockpit/bankKey/displayBankKeySAPData/${bankKey}`,
                    {
                      state: params.row,
                    }
                  );
                }}
                showCustomNavigation={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
                showFirstPageoptions={true}
                showSelectAllOptions={true}
                onSelectAllOptions={handleSelectAllData}
                onSelectFirstPageOptions={handleFirstPageOptions}
              />
            </Stack>
          </Grid>

          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >
              <Button
                size="small"
                variant="contained"
                onClick={() => {
                  navigate(`/${APP_END_POINTS?.CREATE_BK}`);
                  dispatch(clearHeaderFieldsBnky());
                }}
                className="createRequestButtonBK"
              >
                {t("Create Request")}
              </Button>
            </BottomNavigation>
          </Paper>
        </Stack>
      </div>
    </>
  );
};

export default BankKey;