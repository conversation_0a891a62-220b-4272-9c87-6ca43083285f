import React, { useEffect, useState } from "react";
import ReusableTable from "../ReusableTable";
import { Box, Button, Grid, TextField, Typography } from "@mui/material";
import { button_Outlined, container_Padding } from "../../Common/commonStyles";
import { useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setTaxData } from "../../../app/payloadSlice";
import AutoCompleteType from "./AutoCompleteType";
const TableType = (props) => {
  const dispatch = useDispatch();
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  let taxTableRows = useSelector((state) => state.payload.taxData);
  const [rows, setrows] = useState([
    { id:1,
      TaxCategory: "",
      TaxCategoryName: "",
      TaxClassification: "",
      TaxClassificationDescription: "",
    },
  ]);
  const [consumptionData, setconsumptionData] = useState({});
  let taxFields = props.columns;
  // console.log("tax", taxTableRows);

  // useEffect(() => {
  //   if (taxTableRows.length === 0) {
  //     dispatch(
  //       setTaxData([
  //         {
  //           id: 1,
  //           TaxCategory: "",
  //           TaxCategoryName: "",
  //           TaxClassification: "",
  //           TaxClassificationDescription: "",
  //         },
  //       ])
  //     );
  //   }
  // }, []);

  useEffect(() => {
    if (consumptionData) {
      let temp = [];
      for (let index = 0; index < consumptionData.length; index++) {
        var tempObj = consumptionData[index];

        var tempRow = {
          // id: taxTableRows.length + 1,
          TaxCategory: "",
          TaxCategoryName: "",
          TaxClassification: "",
          TaxClassificationDescription: "",
        };

        temp.push(tempRow);
        // console.log(tempRow, "tr");
      }

      // console.log("fetching...2");
      setrows(temp);
    }
  }, [consumptionData]);

  const handleAddNewRow = () => {
    const newRow = [
      ...taxTableRows,
      {
        id: taxTableRows.length + 1,
        TaxCategory: "",
        TaxCategoryName: "",
        TaxClassification: "",
        TaxClassificationDescription: "",
      },
    ];
    dispatch(setTaxData(newRow));
  };

  // const columns = taxFields.map((item) => {
  //   // console.log('tsxcast',item.fieldName,item.fieldName.split(" ").join(""));
  //   return {
  //     field: item.fieldName.split(" ").join(""),
  //     headerName: item.fieldName,
  //     width: 280,
  //     valueOptions: item.fieldName.split(" ").join("") === 'TaxCategory' ?[...new Set(dropDownData?.TaxCategory.map((item) => item.code))]: [],
  //     type: item.fieldType === "Drop Down" ? "singleSelect" : "text",
  //     align: "left",
  //     headerAlign: "left",
  //     editable: true,
  //     preProcessEditCellProps: (params) => {
  //       let rowsData = taxTableRows?.map((x) => {
  //         if (x.id === params.id) {
  //           return { ...x, [item.fieldName.split(" ").join("")]: params?.props?.value };
  //         }
  //         return x;
  //       });
  //       dispatch(setTaxData(rowsData));
  //     },
  //   };
  // });

  const handleBatch = (event) => {
    const rowsData = rows.map((x) => {
      console.log(event, "params");

      if (x.id === event.target.name.id) {
        x["batch"] = event.target.value;
        x["batchQuantity"] = event.target.name.Batch?.filter(
          (item) => item.batchNumber == event.target.value
        )[0]?.batchQuantity;
      }

      return x;
    });

    setrows(rowsData);
  };
  const editData = (params, event) => {
    const rowsData = rows.map((x) => {
      // console.log(params, "params");

      if (x.sequenceNo === params.id) {
        if (params.field === "TaxCategory") {
          x[params.field] = params.props.value;
        }
        if (params.field === "TaxCategoryName") {
          // console.log('hey')
          x[params.field] = event.target.value;
        }
        if (params.field === "TaxClassification") {
          // console.log('hey')
          x[params.field] = event.target.value;
        }
        if (params.field === "TaxClassificationDescription") {
          // console.log('hey')
          x[params.field] = event.target.value;
        }
      }
      return x;
    });

    setrows(rowsData);
  };

  const columns = [
    {
      field: "id",
      headerName: "ID",
      type: "text",
      hide: "true",
    },
    {
      field: "TaxCategory",
      headerName: "Tax Category",
      // type: "singleSelect",
      width: 150,
      // valueOptions: dropDownData?.TaxCategory?.map((item) => item.code),
      renderCell: (params) => {
        return (
          <AutoCompleteType
            sx={{ height: "31px" }}
            fullWidth
            size="small"
            // value={valueFromPayload[props?.keyName]}
            onChange={handleBatch}
            options={dropDownData?.TaxCategory ?? []}
            getOptionLabel={(option) => `${option?.code} - ${option?.desc}`}
            renderOption={(props, option) => (
              <li {...props}>
                <Typography style={{ fontSize: 12 }}>
                  {option?.code} - {option?.desc}
                </Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField {...params} variant="outlined" />
            )}
          />
        );
      },
    },
    {
      field: "TaxCategoryName",
      headerName: "Tax Category Name",
      type: "text",
      width: 150,
    },
    {
      field: "TaxClassification",
      headerName: "Tax Classification",
      type: "singleSelect",
      width: 150,
      valueOptions: dropDownData?.TaxCategory?.map((item) => item.code),
      renderCell: (params) => {
        return (
          <AutoCompleteType
            sx={{ height: "31px" }}
            fullWidth
            size="small"
            value={valueFromPayload[props?.keyName]}
            onChange={(e, value) => {
              dispatch(setPayload({ keyName: props.keyName, data: value }));
            }}
            options={dropDownData[props.keyName] ?? []}
            required={
              props.details.visibility === "0" ||
              props.details.visibility === "Required"
            }
            getOptionLabel={(option) => `${option?.code} - ${option?.desc}`}
            renderOption={(props, option) => (
              <li {...props}>
                <Typography style={{ fontSize: 12 }}>
                  {option?.code} - {option?.desc}
                </Typography>
              </li>
            )}
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                placeholder={`Select ${props.details.fieldName}`}
                error={errorFields.includes(props?.keyName)}
              />
            )}
          />
        );
      },
    },
    {
      field: "TaxClassificationDescription",
      headerName: "Tax Classification Description",
      type: "text",
      width: 150,
    },
  ];
  return (
    <div>
      <Grid
        item
        md={12}
        sx={{
          backgroundColor: "white",
          maxHeight: "max-content",
          height: "max-content",
          borderRadius: "8px",
          border: "1px solid #E0E0E0",
          mt: 0.25,
          boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
          ...container_Padding,
          // ...container_columnGap,
        }}
      >
        <Grid container>
          <Typography
            sx={{
              fontSize: "12px",
              fontWeight: "700",
              color: "#3B30C8",
            }}
          >
            {props.cardName}
          </Typography>
        </Grid>
        <Box width="100%" className="confirmOrder-lineItem" mt={0}>
          <ReusableTable
            width="100%"
            rows={rows ?? []}
            columns={columns}
            getRowIdValue={"id"}
            hideFooter={true}
            checkboxSelection={false}
            onEditCellPropsChange={editData}
            // experimentalFeatures={{ newEditingApi: true }}
          />
        </Box>

        <Button
          variant="outlined"
          sx={{ button_Outlined }}
          onClick={handleAddNewRow}
        >
          Add Row
        </Button>
      </Grid>
    </div>
  );
};

export default TableType;
