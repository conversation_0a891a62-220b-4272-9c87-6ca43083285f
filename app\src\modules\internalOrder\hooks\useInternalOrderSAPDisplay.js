import { useEffect, useState } from "react";
import { doAjax } from "@components/Common/fetchService";
import { destination_InternalOrder } from "../../../destinationVariables";
import { END_POINTS } from "@constant/apiEndPoints";

const useInternalOrderSAPDisplay = (internalOrder) => {
  const [fieldValues, setFieldValues] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!internalOrder) return;
    setLoading(true);
    setError(null);

    doAjax(
      `/${destination_InternalOrder}${END_POINTS.DISPLAY_INTERNAL_ORDER.DISPLAY_MASS_DTO}`,
      "post",
      (data) => {
        const sapData = data?.body?.[0] || {};
        setFieldValues(sapData);
        setLoading(false);
      },
      (err) => {
        setFieldValues({});
        setError(err);
        setLoading(false);
      },
      { internalOrder }
    );
  }, [internalOrder]);

  return { fieldValues, loading, error };
};

export default useInternalOrderSAPDisplay;