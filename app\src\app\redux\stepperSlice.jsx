import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  activeStep: 0,
  requestBenchTab: 0,
};

const stepperSlice = createSlice({
  name: 'stepper',
  initialState,
  reducers: {
    setActiveStep: (state, action) => {
      state.activeStep = action.payload;
    },
    setRequestBenchTab: (state, action) => {
      state.requestBenchTab = action.payload;
    },
    resetStepper: (state) => {
      state.activeStep = 0;
    },
  },
});

export const { setActiveStep,setRequestBenchTab, resetStepper } = stepperSlice.actions;
export default stepperSlice.reducer;
