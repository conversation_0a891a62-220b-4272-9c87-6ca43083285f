import { Box, Grid, Typography, Button, useTheme } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import useLogger from '@hooks/useLogger';
import { doAjax } from "../../../components/Common/fetchService";
import { destination_MaterialMgmt } from "../../../../src/destinationVariables";
import { END_POINTS } from '@constant/apiEndPoints';
import ReusableSnackBar from "@components/Common/ReusableSnackBar";
import { useState } from 'react';
import CustomCheckbox from "@components/Common/ui/CustomCheckbox"
import  useLang  from "@hooks/useLang";
import { filterNavigation } from '@helper/helper';
import { MODULE_MAP } from '@constant/enum';

const PreviewDownloadCard = ({ payloadForDownloadExcel,module,payloadForPreviewDownloadExcel }) => {
  const {destination} = filterNavigation(module)
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [alertType, setAlertType] = useState("success");
  const [reviewed,setReviewed] = useState(false)
  const { t } = useLang();
  const theme = useTheme()
  const { customError } = useLogger();

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const payload = module === MODULE_MAP?.MAT || module === MODULE_MAP?.ART
  ? payloadForDownloadExcel
  : payloadForPreviewDownloadExcel;

  const DownloadExcelPreview = () => {
    const urlMap = {
  [MODULE_MAP?.PCG]: END_POINTS.EXCEL.EXPORT_PCG_EXCEL,
  [MODULE_MAP?.CCG]: END_POINTS.EXCEL.EXPORT_CCG_EXCEL,
  [MODULE_MAP?.CEG]: END_POINTS.EXCEL.EXPORT_CEG_EXCEL,
  default: END_POINTS.EXCEL.EXPORT_PREVIEW_EXCEL,
};

let url = urlMap[module] || urlMap.default;
    const hSuccess = (data) => {
      if (data?.size > 0) {
        const href = URL.createObjectURL(data);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `Request_Preview_${new Date().getTime()}.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
      }
    }
    const hError = (error) => {
      customError(error)
      setMessageDialogMessage(error?.message);
      setAlertType("error");
      setOpenSnackbar(true);
    }
    doAjax(`/${destination}${url}`, "postandgetblob", hSuccess, hError, payload);
  }
  return (
    <Grid
      item
      md={12}
      sx={{
        backgroundColor: "white",
        borderRadius: "8px",
        border: "1px solid #E0E0E0",
        boxShadow: "0px 1px 4px rgba(0, 0, 0, 0.1)",
        p: '10px'
      }}
    >
      <Typography
        sx={{
          fontWeight: "bold",
          mb: "6px",
        }}
      >
        {t("Master data details")}
      </Typography>

      <Box
        sx={{
          backgroundColor: "#FAFAFA",
          borderRadius: "8px",
          boxShadow: "none",
        }}
      >
        <Box sx={{ padding: '8px' }}>
          <Typography align="left" variant="h6" component="h2">
            {t("Please download the excel sheet to view all data.")}
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'flex-start',
              gap: 1,
              mt: 2,
            }}
          >
            <Button
              variant="contained"
              startIcon={
                <DownloadIcon
                  sx={{
                    fontSize: 28,
                    animation: 'downloadBounce 2s ease-in-out infinite',
                    filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))',
                  }}
                />
              }
              onClick={DownloadExcelPreview}
              sx={{
                // background: theme.palette.gradient.default,
                position: 'relative',
                overflow: 'hidden',
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: '-100%',
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                  transition: 'left 0.5s',
                },
                '&:hover::before': {
                  left: '100%',
                },
                '&:hover': {
                  transform: 'translateY(-3px) scale(1.02)',
                  boxShadow: '0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)',
                },
                '&:active': {
                  transform: 'translateY(-1px) scale(0.98)',
                  boxShadow: '0 6px 15px rgba(102, 126, 234, 0.3)',
                },
                transition: 'all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
                borderRadius: 3,
                py: 1.8,
                px: 3,
                textTransform: 'none',
                fontSize: '1.1rem',
                fontWeight: 600,
                boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)',
                display: 'flex',
                alignItems: 'center',
                gap: 1.5,
                border: '1px solid rgba(255, 255, 255, 0.1)',
                backdropFilter: 'blur(10px)',
                color: '#ffffff',
                letterSpacing: '0.5px',
                minWidth: '180px',
                '@keyframes downloadBounce': {
                  '0%, 100%': {
                    transform: 'translateY(0) rotate(0deg)',
                    filter: 'drop-shadow(0 2px 4px rgba(255,255,255,0.3))',
                  },
                  '25%': {
                    transform: 'translateY(-3px) rotate(-2deg)',
                    filter: 'drop-shadow(0 4px 8px rgba(255,255,255,0.4))',
                  },
                  '50%': {
                    transform: 'translateY(-6px) rotate(0deg)',
                    filter: 'drop-shadow(0 6px 12px rgba(255,255,255,0.5))',
                  },
                  '75%': {
                    transform: 'translateY(-3px) rotate(2deg)',
                    filter: 'drop-shadow(0 4px 8px rgba(255,255,255,0.4))',
                  },
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  width: '0',
                  height: '0',
                  background: 'radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)',
                  borderRadius: '50%',
                  transform: 'translate(-50%, -50%)',
                  transition: 'width 0.6s, height 0.6s',
                  pointerEvents: 'none',
                },
                '&:active::after': {
                  width: '300px',
                  height: '300px',
                },
              }}
            >
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                position: 'relative',
                zIndex: 1,
              }}>
                {t("Download Excel")}
                <Box
                  component="span"
                  sx={{
                    fontSize: '0.8rem',
                    opacity: 0.8,
                    fontWeight: 400,
                    ml: 0.5,
                  }}
                >
                  (.xlsx)
                </Box>
              </Box>
            </Button>
            <CustomCheckbox label={t("I have reviewed all request details.")} checked={reviewed} onChange={() => setReviewed(!reviewed)}/>
          </Box>
        </Box>
      </Box>
      {<ReusableSnackBar openSnackBar={openSnackbar} alertMsg={messageDialogMessage} alertType={alertType} handleSnackBarClose={handleSnackBarClose} />}
    </Grid>
  );
};

export default PreviewDownloadCard;
