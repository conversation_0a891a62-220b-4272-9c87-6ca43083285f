import { <PERSON><PERSON>, <PERSON><PERSON>, TextField, Typography } from "@mui/material";
import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { updateModuleFieldData } from "../../../app/profitCenterTabsSlice";
import { updateModuleFieldDataCC } from "../../../app/costCenterTabsSlice";
import { colors } from "@constant/colors";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { useChangeLogCreation } from "@hooks/useChangeLogCreation";
import { CHANGE_LOG_STATUSES, MODULE_MAP, MODULE_PAYLOAD_DATA, OBJECT_MUMBER_KEY } from "@constant/enum";

import { setRequestHeaderPayloadDataPCG } from "@app/hierarchyDataSlice";
import { setBOMpayloadData } from "@BillOfMaterial/bomSlice";
import { setBankKeyPayload, setRowsHeaderData, updateModuleFieldDataBK } from "@bankKey/bnkyslice";
import { MODULE } from "../../../constant/enum";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";
import SkeletonWithFallback from "@components/Common/ui/SkeletonWithFallback";
import useLang from "@hooks/useLang";
import { useLocation } from "react-router-dom";

const InputTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange, module } = props;
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const rowsBodyData = MODULE_PAYLOAD_DATA[module] || (() => ({}));
  const objectKey = OBJECT_MUMBER_KEY[module] || (() => ({}));
  const rowsObjectNumber = rowsBodyData?.[uniqueId]?.[objectKey];
  let requestStatus = rowsBodyData?.[uniqueId]?.RequestStatus;

  const valueFromPayloadPC = useSelector((state) => state.profitCenter.payload || {});
  const valueFromPayloadCC = useSelector((state) => state.costCenter.payload || {});
  const valueFromPayloadGL = useSelector((state) => state.generalLedger.payload || {});
  const BOMpayloadData = useSelector((state) => state.bom.BOMpayloadData || {});
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData || {});
  const BKpayloadData = useSelector((state) => state.bankKey.payload || {});
  const { updateChangeLogGl } = useChangeLogUpdateGl();
  const { updateChangeLogGlobal } = useChangeLogCreation();
  const SAPview = ["displayGeneralLedgerMasterdata", "displayCostCenter", "displayProfitCenter", "displayBankKeySAPData"].some((path) => location?.pathname?.includes(path));
  const { t } = useLang();
  const valueFromPayloadHierarchy = useSelector((state) => state.hierarchyData || {});
  const initialFieldValue =
    module === MODULE?.CC
      ? valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadCC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.GL
      ? valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE?.PCG || module === MODULE?.CCG || module === MODULE?.CEG
      ? valueFromPayloadHierarchy?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.BOM
      ? BOMpayloadData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE.IO
      ? IOpayloadData?.rowsBodyData?.[uniqueId]?.payload?.[field?.jsonName] ?? IOpayloadData?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : module === MODULE_MAP?.BK
      ? BKpayloadData?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? BKpayloadData?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? ""
      : valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ?? valueFromPayloadPC?.requestHeaderData?.[field?.jsonName] ?? field?.value ?? "";

  const [localValue, setLocalValue] = useState(initialFieldValue);
  const [isFocused, setIsFocused] = useState(false);
  const [charCount, setCharCount] = useState({});
  const [isClicked, setIsClicked] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    setLocalValue(initialFieldValue);
    setCharCount((prevCount) => ({
      ...prevCount,
      [field?.jsonName]: initialFieldValue?.length || 0,
    }));
  }, [initialFieldValue]);
  const handleInputChange = (e) => {
    let updatedValue = e.target.value
    if (field.dataType === "QUAN") {
        // Enforce maxLength manually for numbers
        if (updatedValue.length > field.maxLength) {
          updatedValue = updatedValue.slice(0, field.maxLength);
        }
      }else{
        updatedValue = updatedValue
      .replace(/[^a-zA-Z0-9\-&()#,. ]/g, "")
      .replace(/\s{2,}/g, " ")
      .replace(/\s*([-&()#,.])\s*/g, "$1")
      .trimStart();

    setCharCount((prevCount) => ({
      ...prevCount,
      [field?.jsonName]: updatedValue.length,
    }));}

    setLocalValue(updatedValue);

    if (module === MODULE?.CC) {
      dispatch(
        updateModuleFieldDataCC({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            objectNumber: rowsObjectNumber,
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: updatedValue,
            childRequestId: requestId,
          });
      }
    } else if (module === MODULE?.GL) {
      dispatch(
        updateModuleFieldDataGL({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            objectNumber: rowsObjectNumber,
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: updatedValue,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    } else if (module === MODULE?.BOM) {
      dispatch(
        setBOMpayloadData({
          keyName: field?.jsonName,
          data: updatedValue,
        })
      );
    } else if (module === MODULE?.IO) {
      dispatch(
        updateModuleFieldDataIO({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName,
          data: updatedValue,
        })
      );
      {
        requestId &&
          updateChangeLogGlobal({
            uniqueId: uniqueId || "",
            viewName: field?.viewName,
            fieldName: field?.fieldName,
            jsonName: field?.jsonName,
            currentValue: updatedValue || "",
            requestId: requestId,
            childRequestId: requestId,
            module: MODULE_MAP?.IO
          });
      }
    } else if (module === MODULE_MAP?.BK) {
      dispatch(
        updateModuleFieldDataBK({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGlobal({
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: updatedValue,
            requestId: requestId,
            childRequestId: requestId,
          });
      }
      const updatedRows = BKpayloadData?.rowsHeaderData?.map((row) => (row.id === uniqueId ? { ...row, validated: "default" } : row));
      dispatch(setRowsHeaderData(updatedRows));
    } else if (module === MODULE?.PCG || module === MODULE?.CCG || module === MODULE?.CEG) {
      dispatch(
        setRequestHeaderPayloadDataPCG({
          keyName: field?.jsonName || "",
          data: updatedValue,
        })
      );
    } else {
      dispatch(
        updateModuleFieldData({
          uniqueId: uniqueId || "",
          keyName: field?.jsonName || "",
          data: updatedValue,
          viewID: field?.viewName,
        })
      );
      {
        requestId &&
          !CHANGE_LOG_STATUSES.includes(requestStatus) &&
          updateChangeLogGl({
            objectNumber: rowsObjectNumber,
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: updatedValue,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
      }
    }
  };
  return (
    <>
      <Grid item md={2} mb={1.5}>
        <Stack>
          {SAPview ? (
            <div
              style={{
                padding: "16px",
                backgroundColor: colors.primary.white,
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                margin: "16px 0",
                transition: "all 0.3s ease",
              }}
            >
              <Typography
                variant="body1"
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                  fontWeight: 600,
                  fontSize: "12px",
                  marginBottom: "4px",

                  display: "flex",
                  alignItems: "center",
                }}
                title={t(field?.fieldName)}
              >
                {t(field?.fieldName) || "Field Name"}
                {(field?.visibility === "Required" || field?.visibility === "MANDATORY") && <span style={{ color: colors.error.darkRed, marginLeft: "2px" }}>*</span>}
              </Typography>

              <div
                style={{
                  fontSize: "0.8rem",
                  color: colors.black.dark,
                  marginTop: "4px",
                }}
              >
                <span
                  style={{
                    fontWeight: 500,
                    color: colors.secondary.grey,
                    letterSpacing: "0.5px",
                    wordSpacing: "1px",
                  }}
                >
                  {localValue}
                  {!localValue && <SkeletonWithFallback fallback="--" />}
                </span>
              </div>
            </div>
          ) : (
            <>
              <Typography
                variant="body2"
                color="#777"
                sx={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                }}
                title={field?.fieldName}
              >
                {field.fieldName}
                {(field.visibility === "Mandatory" || field.visibility === "0") && <span style={{ color: "red" }}>*</span>}
              </Typography>

              <TextField
                size="small"
                type={field.dataType === "QUAN" ? "number" : "text"}
                placeholder={disabled ? "" : `Enter ${field.fieldName}`}
                error={props?.isError}
                value={localValue}
                title={localValue}
                onBlur={() => setIsFocused(false)}
                inputProps={{
                  style: { textTransform: "uppercase", fontSize: "12px" },
                  maxLength: field.maxLength,
                }}
                onFocus={() => setIsFocused(true)}
                onClick={() => setIsClicked(true)}
                helperText={props?.isError ? "This field is required" : isFocused && (charCount[field?.jsonName] === field.maxLength ? `Max Length Reached` : `${charCount[field?.jsonName]}/${field.maxLength}`)}
                FormHelperTextProps={{
                  sx: {
                    color: props?.isError ? "red" : isFocused && charCount[field?.jsonName] === field.maxLength ? "red" : "blue",
                    position: "absolute",
                    bottom: "-20px",
                  },
                }}
                sx={{
                  "& .MuiInputBase-root.Mui-disabled": {
                    "& > input": {
                      WebkitTextFillColor: colors.black.dark,
                      color: colors.black.dark,
                    },
                    backgroundColor: colors.hover.light,
                  },
                  "& .MuiInputBase-root": {
                    height: "34px",
                  },
                  "& .MuiOutlinedInput-root": {
                    "&.Mui-focused fieldset": {
                      borderColor: props?.isError || (isFocused && charCount[field?.jsonName] >= field.maxLength) ? "red" : "",
                    },
                    "& fieldset": {
                      borderColor: props?.isError || (isFocused && charCount[field?.jsonName] >= field.maxLength) ? "red" : "",
                    },
                  },
                }}
                onChange={handleInputChange}
                disabled={disabled}
                required={field.visibility === "Mandatory" || field.visibility === "0"}
              />
            </>
          )}
        </Stack>
      </Grid>
    </>
  );
};

export default InputTypeGlobal;
