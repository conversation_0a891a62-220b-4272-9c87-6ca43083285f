import { setBank<PERSON><PERSON>Tabs, setBankKeyConfig, setBankKeyData, setMandatoryFieldsBK } from "../bnkySlice";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getLocalStorage, groupBy, removeHiddenAndEmptyObjects, transformStructureForAllTabsData } from "@helper/helper";
import { destination_BankKey, destination_IDM } from "../../../destinationVariables";
import { doAjax } from "@components/Common/fetchService";
import { API_CODE, LOCAL_STORAGE_KEYS, REQUEST_TYPE, TASK_NAME, VISIBILITY_TYPE } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import useLogger from "@hooks/useLogger";

const transformBankKeyFieldConfigData = (responseData, keyToCheck) => {
  let mandatoryFields = {};
  
  responseData?.forEach((item) => {
    if (item.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.MANDATORY && item.MDG_MAT_VIEW_NAME !== "Header") {
      if (!mandatoryFields[keyToCheck]) {
        mandatoryFields[keyToCheck] = {};
      }
      if (!mandatoryFields[keyToCheck][item.MDG_MAT_VIEW_NAME]) {
        mandatoryFields[keyToCheck][item.MDG_MAT_VIEW_NAME] = [];
      }
      mandatoryFields[keyToCheck][item.MDG_MAT_VIEW_NAME].push({
        jsonName: item.MDG_MAT_JSON_FIELD_NAME, 
        fieldName: item.MDG_MAT_UI_FIELD_NAME,
      });
    }
  });
  
  let sortedData = responseData
  ?.filter(item => item.MDG_MAT_VIEW_NAME !== "Header")
  ?.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);
  
  const groupedFields = groupBy(sortedData, "MDG_MAT_VIEW_NAME");
  let view_data_array = [];
  
  Object.entries(groupedFields)?.forEach(([viewName, fields]) => {
    let groupedFieldsDataCardNameWise = groupBy(fields, "MDG_MAT_CARD_NAME");
    let cards = [];

    Object.entries(groupedFieldsDataCardNameWise).forEach(([cardName, cardFields]) => {
      cardFields.sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO);

      let cardDetails = cardFields?.map((item) => ({
        fieldName: item.MDG_MAT_UI_FIELD_NAME,
        sequenceNo: item.MDG_MAT_SEQUENCE_NO,
        fieldType: item.MDG_MAT_FIELD_TYPE,
        maxLength: item.MDG_MAT_MAX_LENGTH,
        dataType: item.MDG_MAT_DATA_TYPE,
        viewName: item.MDG_MAT_VIEW_NAME,
        cardName: item.MDG_MAT_CARD_NAME,
        cardSeq: item.MDG_MAT_CARD_SEQUENCE,
        viewSeq: item.MDG_MAT_VIEW_SEQUENCE,
        value: item.MDG_MAT_DEFAULT_VALUE,
        visibility: item.MDG_MAT_VISIBILITY,
        jsonName: item.MDG_MAT_JSON_FIELD_NAME,
      }));

      cards.push({
        cardName,
        cardSeq: cardFields[0].MDG_MAT_CARD_SEQUENCE,
        cardDetails,
      });
    });

    cards.sort((a, b) => a.cardSeq - b.cardSeq);
    view_data_array.push({
      viewName,
      viewSeq: fields[0].MDG_MAT_VIEW_SEQUENCE,
      cards,
    });
  });

  view_data_array.sort((a, b) => a.viewSeq - b.viewSeq);

  let filteredData = removeHiddenAndEmptyObjects(view_data_array);
  let transformedData = {};
  
  filteredData.forEach((view) => {
    let cardData = {};
    view.cards.forEach((card) => {
      cardData[card.cardName] = card.cardDetails;
    });
    transformedData[view.viewName] = cardData;
  });

  return { transformedData, mandatoryFields };
};

const useBankKeyFieldConfig = () => {
  const dispatch = useDispatch();
  const { customError } = useLogger();
  const initialPayload = useSelector((state) => state.bankKey?.payload?.requestHeaderData);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const { taskData } = useSelector((state) => state.userManagement);
  const userData = useSelector((state) => state.userManagement.userData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const isDisplaySAPViewPresent = window.location.href.includes("displayBankKeySAPData");

  const taskDataString = getLocalStorage(LOCAL_STORAGE_KEYS.CURRENT_TASK);
    let finalTaskData = null;
    finalTaskData = typeof taskDataString === "string" ? JSON.parse(taskDataString) : taskDataString;
    let workflowGroup = finalTaskData?.ATTRIBUTE_5;
  const getBankKeyTemplateData = async (keyToCheck, bkCountry) => {
    setLoading(true);
    if (initialPayload?.RequestType || isDisplaySAPViewPresent) {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_BNKY_FIELD_CONFIG",
      version: "v2",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": REQUEST_TYPE.CREATE,
          "MDG_CONDITIONS.MDG_MAT_COUNTRY": bkCountry || "US",
          "MDG_CONDITIONS.MDG_MAT_GROUP_ROLE": isDisplaySAPViewPresent ? TASK_NAME?.REQ_DISPLAY_FIN : taskData.ATTRIBUTE_5 ? taskData.ATTRIBUTE_5 : workflowGroup ? workflowGroup : TASK_NAME?.REQ_INITIATE_FIN,      
          "MDG_CONDITIONS.MDG_MAT_REGION": initialPayload?.Region || "US",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        if (Array.isArray(data?.data?.result) && data?.data?.result.every(item => Object.keys(item).length !== 0)) {
          //CHECK 
          let responseData = data?.data?.result[0]?.MDG_BOM_MATERIAL_FIELD_CONFIG_ACTION_TYPE;
          const { transformedData, mandatoryFields } = transformBankKeyFieldConfigData(responseData, keyToCheck);
          let bankKeyTabsData = Object.keys(transformedData);

          const allTabsData = bankKeyTabsData.map((tab) => ({
            keyName: keyToCheck,
            tab,
            data: transformedData[tab],
          }));
          
          dispatch(setBankKeyTabs(allTabsData));
          dispatch(setMandatoryFieldsBK(mandatoryFields));

          dispatch(setBankKeyConfig({ 
            BankKey: { 
              allfields: transformStructureForAllTabsData(bankKeyTabsData), 
              mandatoryFields 
            } 
          }));
        } else {
          dispatch(setBankKeyData({ BankKey: {} }));
        }
        setLoading(false);
      }
    };

    const hError = (error) => {
      customError(error);
      setError(error);
      setLoading(false);
    };

    const url =
      applicationConfig.environment === "localhost"
        ? `/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`
        : `/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`;

    doAjax(url, "post", hSuccess, hError, payload);
  };
}

  const fetchBankKeyFieldConfig = (keyToCheck, bkCountry) => {
    try {
      getBankKeyTemplateData(keyToCheck, bkCountry);
    } catch (err) {
      setError(err);
      setLoading(false);
    }
  };

  return { loading, error, fetchBankKeyFieldConfig };
};

export default useBankKeyFieldConfig;