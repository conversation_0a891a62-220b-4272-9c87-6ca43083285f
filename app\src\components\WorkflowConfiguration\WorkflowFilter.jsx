import { Grid } from '@mui/material'
import React from 'react'
import { container_filter } from '../common/commonStyles'
import FilterFieldWorkflow from '../common/ReusableFilterBox/FilterFieldWorkflow';
import { WORKFLOW_FILTERS } from '@constant/enum';

const WorkflowFilter = ({setWfSearchForm, wfSearchForm, activeTab, getAllWorflows}) => {
 
  return (
    <Grid container sx={container_filter}>
      <Grid item md={12}>
       <FilterFieldWorkflow
        searchParameters={WORKFLOW_FILTERS}
        setWfSearchForm={setWfSearchForm} 
        wfSearchForm={wfSearchForm}
        activeTab={activeTab}
        getAllWorflows={getAllWorflows}
      />
      </Grid>
    </Grid>
  )
}

export default WorkflowFilter;