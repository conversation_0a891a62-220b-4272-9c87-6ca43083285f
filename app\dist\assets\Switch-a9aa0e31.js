import{q8 as i,qA as t,cb as a}from"./index-226a1e75.js";const o=i(t)({width:"2.125rem",height:"1.125rem",padding:0,margin:"0.56rem 0.5rem",display:"flex","& .<PERSON>iSwitch-switchBase":{height:"100%",padding:"0.05625rem 0.0625rem","&.Mui-checked":{transform:"translateX(1rem)",color:"var(--primary-main)","& + .MuiSwitch-track":{opacity:1,backgroundColor:"var(--primary-light)"}},"&:not(.Mui-checked)":{color:"var(--contrast-text)","& + .MuiSwitch-track":{opacity:.4,backgroundColor:"var(--text-secondary)"}},"&.Mui-disabled":{"&.Mui-checked":{opacity:.4,color:"var(--primary-main)","& + .<PERSON>iSwitch-track":{backgroundColor:"var(--primary-light)"}},"&:not(.Mui-checked)":{opacity:.7,color:"var(--contrast-text)","& + .MuiSwitch-track":{opacity:.4,backgroundColor:"var(--text-disabled)"}}}},"& .MuiSwitch-thumb":{alignSelf:"center",borderRadius:"50%",width:"1rem",height:"1rem",boxShadow:"none"},"& .MuiSwitch-track":{height:"1.125rem",borderRadius:"1rem",opacity:1}}),e=r=>a.jsx(o,{...r});export{e as h};
