import{r as g,j as s,bF as F,bG as O,a9 as a,d as b,B as f,c as d,a6 as I,a7 as K,bD as Q,dM as X,dN as Y,aa as Z,dO as u,ag as p,dP as M,bE as S,F as G}from"./index-226a1e75.js";const on=({matGroup:i,selectedMaterialGroup:n,setSelectedMaterialGroup:c,isDropDownLoading:$,placeholder:C="Select Option",onInputChange:k,minCharacters:r=0})=>{const[m,z]=g.useState(!1),[R,T]=g.useState(null),[V,_]=g.useState(""),[j,v]=g.useState(!1),[l,w]=g.useState(""),B=g.useRef(null),P=g.useRef(null),t=g.useMemo(()=>{const o=Array.isArray(i)?i.map(h=>({value:h.code,label:h.desc||h.code})):[];return l?o.filter(h=>h.value.toLowerCase().includes(l.toLowerCase())||h.label.toLowerCase().includes(l.toLowerCase())):o},[i,l]),D=o=>{const h=o.target.value;w(h),k&&k(o)},y=g.useCallback(o=>{const h=n.some(x=>x.code===o.value);c(h?n.filter(x=>x.code!==o.value):[...n,{code:o.value,desc:o.label}])},[n,c]),E=g.useCallback(()=>{(n==null?void 0:n.length)===i.length?c([]):c(i.map(o=>({code:o.code,desc:o.desc})))},[i,n==null?void 0:n.length,c]),H=g.useCallback(()=>{c([])},[c]),N=(o,h)=>{T(o.currentTarget),_(h),v(!0)},A=()=>{v(!1)},W=()=>{v(!0)},U=()=>{v(!1)},q=g.useCallback(({index:o,style:h})=>{if(o===0)return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:E,children:s(F,{sx:{width:"100%",py:.5},children:s(O,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(a,{size:"small",checked:(n==null?void 0:n.length)===i.length,indeterminate:(n==null?void 0:n.length)>0&&(n==null?void 0:n.length)<(i==null?void 0:i.length),sx:{py:.5}}),label:s(b,{sx:{fontSize:13},children:"Select All"})})})});const x=t[o-1],L=n.some(e=>e.code===x.value);return s(f,{component:"div",sx:{...h,padding:"4px 8px",cursor:"pointer","&:hover":{backgroundColor:"action.hover"}},onClick:()=>y(x),children:s(F,{sx:{width:"100%",py:.5},children:s(O,{sx:{margin:0,"& .MuiFormControlLabel-label":{flex:1}},control:s(a,{size:"small",checked:L,sx:{py:.5}}),label:d(b,{sx:{fontSize:13},children:[s("strong",{children:x.value})," - ",x.label]})})})})},[t,n,y,E,i==null?void 0:i.length]);g.useEffect(()=>{const o=h=>{P.current&&!P.current.contains(h.target)&&z(!1)};return document.addEventListener("mousedown",o),()=>{document.removeEventListener("mousedown",o)}},[]);const J=()=>{var o,h;return(n==null?void 0:n.length)===0?null:(n==null?void 0:n.length)>1?d(G,{children:[s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(o=n==null?void 0:n[0])==null?void 0:o.code}),s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"},ml:.5},label:`+${(n==null?void 0:n.length)-1}`,onMouseEnter:x=>{const L=n.slice(1).map(e=>`<strong>${e.code}</strong> - ${e.desc}`).join("<br />");N(x,L)},onMouseLeave:A})]}):s(S,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:(h=n==null?void 0:n[0])==null?void 0:h.code})};return d(f,{ref:P,sx:{position:"relative",width:"100%"},children:[s(Z,{fullWidth:!0,size:"small",placeholder:C==null?void 0:C.toUpperCase(),value:l,onChange:D,onFocus:()=>z(!0),InputProps:{startAdornment:J(),endAdornment:d(f,{sx:{display:"flex",alignItems:"center"},children:[(n==null?void 0:n.length)>0&&s(I,{size:"small",onClick:o=>{o.stopPropagation(),H(),w("")},sx:{padding:"4px",mr:.5,"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.04)"}},children:s(K,{sx:{fontSize:"16px"}})}),$?s(Q,{size:20,sx:{mr:1}}):s(I,{size:"small",onClick:o=>{o.stopPropagation(),z(!m)},sx:{padding:"4px"},children:m?s(X,{sx:{fontSize:"20px"}}):s(Y,{sx:{fontSize:"20px"}})})]})}}),m&&s(p,{sx:{position:"absolute",top:"100%",left:0,right:0,mt:1,maxHeight:300,zIndex:1e3},children:t.length===0?s(f,{sx:{p:2,textAlign:"center"},children:s(b,{variant:"body2",color:"text.secondary",children:l.length<r?`Please enter at least ${r} characters`:"No options found"})}):s(u,{height:Math.min(t.length*45+45,300),itemCount:t.length+1,itemSize:45,width:"100%",ref:B,children:q})}),s(M,{open:j,anchorEl:R,onClose:A,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},onMouseEnter:W,onMouseLeave:U,PaperProps:{sx:{p:1,maxWidth:300}},children:s(b,{variant:"body2",dangerouslySetInnerHTML:{__html:V}})})]})};export{on as L};
