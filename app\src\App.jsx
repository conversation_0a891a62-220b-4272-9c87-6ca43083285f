import "./App.css";
import "./theme-light.css"
import { Route, Routes, useLocation } from "react-router-dom";
import ApplicationRouter from "./screens/ApplicationRouter";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { Suspense, useEffect, useRef, useState } from "react";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import { setToken, setIdmToken ,setIwaToken } from "./app/applicationConfigReducer";
import { destination_Admin, destination_MaterialMgmt, destination_IDM, destination_Websocket } from "./destinationVariables";
import { doAjax } from "./components/Common/fetchService";
import { setCurrentSAPSystem, setThemePreference } from "./app/userManagementSlice";
import ErrorBoundary from "./screens/ErrorBoundary";
import InvalidUser from "./screens/InvalidUser";
import LoadingComponent from "./components/Common/LoadingComponent";
import { setNotificationData, setNotificationsDirect, setNotificationPreference, setEmailPreference } from "./app/notificationSlice";
import { API_CODE, ERROR_MESSAGES } from "@constant/enum";
import SockJS from "sockjs-client";
import { Client } from "@stomp/stompjs";
import { baseUrl_Websocket } from "@data/baseUrl";
import ReduxSnackbar from "./components/Common/ReduxSnackbar";
import ReusableDialog from "./components/Common/ReusableDialog";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import FloatingChatbot from "@components/Common/FloatingChatbot";
import { getFilter } from "@components/RequestBench/RequestBench";
import { APP_END_POINTS } from "@constant/appEndPoints";
import NetworkStatus from "./screens/NetworkStatus";
import { ToastContainer } from "react-toastify";
import { showToast } from "./functions";
import { setLangTranslation } from "./app/applicationConfigReducer";
import { fetchGlobalAppSettings } from "./components/Common/ApplicationSettings";
import useUserBootstrap from "@hooks/useUserBootstrap";
import useModuleAccess from "@hooks/useModuleAccess";
import SessionExpiredScreen from "./screens/SessionExpiredScreen";
import { customTheme,themesPalette } from "./theme";
import { useSnackbar } from "@hooks/useSnackbar";
import "../node_modules/@cw/quickadduser/dist/assets/style.css"
import "../node_modules/@cw/usersummary/dist/assets/style.css"
import "../node_modules/@cw/adduser/dist/assets/style.css"
import "../node_modules/@cw/viewuser/dist/assets/style.css"
import "../node_modules/@cw/edituser/dist/assets/style.css"
import "../node_modules/@cw/rolesummary/dist/assets/style.css"
import "../node_modules/@cw/createrole/dist/assets/style.css"
import "../node_modules/@cw/viewandeditrole/dist/assets/style.css"
import "../node_modules/@cw/mfviewandedit/dist/assets/style.css"
import "../node_modules/@cw/groupsummary/dist/assets/style.css"
import "../node_modules/@cw/creategroup/dist/assets/style.css"
const {
    VITE_URL_AUTH_TOKEN,
    VITE_URL_AUTH_TOKEN_CAF
} = import.meta.env;
import useModuleOdataHandler from "@modules/modulesHooks/useModuleOdataHandler";
import AppTour from "@components/Common/AppTour";

const NPITheme = createTheme({
  palette: {
    primary: {
      main: "#3b30c8",
    },
    secondary: {
      main: "#2cbc34",
    },
    danger: {
      main: "#DA2C2C",
    },
    neutral: {
      main: "#808080",
    },
    success: {
      main: "#2cbc34",
    },
  },
  background: {
    default: "#FAFCFF",
  },
  typography: {
    h3: {
      fontSize: "20px",
      fontWeight: "600",
      color: "#1d1d1d",
    },
    h4: {
      fontSize: "18px",
      fontWeight: "600",
      color: "#1d1d1d",
    },
    h5: {
      fontSize: "16px",
      fontWeight: "600",
      color: "#1d1d1d",
    },
    h6: {
      fontSize: "14px",
      fontWeight: "600",
      color: "#1d1d1d",
    },
    body1: {
      fontSize: "14px",
    },
    body2: {
      fontSize: "12px",
    },
    caption: {
      fontSize: "10px",
    },
  },
});

function App() {
  const dispatch = useDispatch();
  const { customError, log } = useLogger()
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const {userData} = useSelector((state) => state.userManagement);
  const ThemePef = useSelector((state) => state.userManagement.themePreference);
  const idmToken = useSelector((state) => state.applicationConfig.idmToken);
  const iwaToken = useSelector((state) => state.applicationConfig.iwaToken);
  const langSelected = useSelector((state) => state.appSettings.language);
  const reqBenchActiveTab = useSelector((state) => state.CommonStepper.requestBenchTab);
  useModuleOdataHandler()
  const { showSnackbar } = useSnackbar();

  const [isAppReady, setIsAppReady] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isSessionExpired,setSessionExpired] = useState(false)
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const emailId = userData?.emailId;
  const location = useLocation();

  const fetchToken = async () => {
    try {
      const response = await axios(VITE_URL_AUTH_TOKEN);
      setIsAppReady(true);
      dispatch(setToken({ token: response.data }));
    } catch (e) {
      customError(e)
      if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
        setIsAppReady(true);
      }
    }
  };

  const fetchIDMToken = async () => {
    try {
      const response = await axios(VITE_URL_AUTH_TOKEN_CAF);
      dispatch(
        setIdmToken({
          idmToken:response.data
        })
      );
    } catch (e) {
      customError("Failed to fetch IDM token:", e)
    }
  };

  
const fetchIWAToken = async () => {
  try {
    await doAjax(
      `/${destination_MaterialMgmt}/authenticate/tokenIWA`, // relative URL
      "get",
      (res) => {
        dispatch(setIwaToken({ iwaToken: res.body }));
      },
      (err) => {
        customError("Error fetching IWA token", err);
      }
    );
  } catch (err) {
    customError("Unexpected error in fetchIWAToken", err);
  }
};

 

  const fetchCurrentSAPSystem = () => {
    const hSuccess = (data) => {
      dispatch(setCurrentSAPSystem(data?.SapSystem));
    };
    const hError = (error) => {
      showToast(ERROR_MESSAGES.FAILED_FETCH_SAP_SYSTEM, "error");
    };
    doAjax(
       `/${destination_MaterialMgmt}${END_POINTS.SYSTEM_CONFIG.GET_CURRENT_SAP_SYSTEM}`,
      "get",
      hSuccess,
      hError
    );
  };

  const { fetchAndDispatchUser, moduleAccessStatus, isLoggedOut } = useUserBootstrap();
 
   const { fetchModuleAccess } = useModuleAccess();

  useEffect(() => {
    const getApirefresh = () => {
      const hSuccess=()=>{}
      const hError=()=>{
        setSessionExpired(true)
      }
      doAjax(`/${destination_Admin}${END_POINTS?.DUMMY_API}`,'get',hSuccess,hError)
    }
    const intervalId = setInterval(() => {
      getApirefresh();
    }, 120000); // Call the API every 2 minutes
    return () => clearInterval(intervalId);
  }, []);

  const emailRef = useRef(emailId);
  const urlRef = useRef(false);
  const reqBenchRef = useRef(reqBenchActiveTab);

  useEffect(() => {
    const isRequestBenchPage = 
    location?.pathname?.endsWith(APP_END_POINTS?.REQUEST_BENCH) || location?.hash?.endsWith(APP_END_POINTS?.REQUEST_BENCH);
    urlRef.current = isRequestBenchPage;
  }, [location]);

  useEffect(() => {
    if ((urlRef.current === location?.pathname?.endsWith(APP_END_POINTS?.REQUEST_BENCH)) || (urlRef.current === location?.hash?.endsWith(APP_END_POINTS?.REQUEST_BENCH))) {
      reqBenchRef.current = reqBenchActiveTab
    }
  }, [reqBenchActiveTab]);

  useEffect(() => {
    emailRef.current = emailId;
    if(emailId) {
      fetchAllNotifications()
    }
  }, [emailId]);

  const fetchLangTranslation = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_LANGUAGE_CONFIG",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_LANGUAGE_NAME": langSelected
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE.STATUS_200) {
        dispatch(setLangTranslation(data.data.result[0].MDG_LANGUAGE_CONFIG_ACTION_TYPE));
      }
    };

    const hError = (error) => {
      console.log(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.LOCAL}`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}${END_POINTS.INVOKE_RULES.PROD}`, "post", hSuccess, hError, payload);
    }
  };
  const fetchAsyncResponse = async () => {
    const client = new Client({
      webSocketFactory: () => new SockJS(`${baseUrl_Websocket}/ws`),
      debug: (msg) => log("[STOMP DEBUG]", msg),
      reconnectDelay: 5000,
      onConnect: () => {
        log("Connected to STOMP WebSocket!");
        client.subscribe("/topic/dbUpdate", (message) => {
          let jSonMsg = JSON.parse(message?.body)
          log(jSonMsg, "jSonMsg");
          if(emailRef.current === jSonMsg?.user){
            showSnackbar(jSonMsg?.message,jSonMsg?.statusCode === API_CODE.STATUS_200 ? "success" : "error")
            dispatch(setNotificationData(jSonMsg));
            if(urlRef.current) {
              getFilter(reqBenchRef.current, 0, true)
            }
          }
          
        });
      },
      onStompError: (frame) => {
        customError(frame.headers["message"]);
      },
    });

    client.activate();

    return () => {
      client.deactivate();
    };
  };
const selectedThemeKey = ThemePef || "blueTheme";
const themeConfig = themesPalette.get(selectedThemeKey);
const dynamicTheme = customTheme(themeConfig);

  const fetchAllNotifications = () => {
    doAjax(`/${destination_Websocket}/notifications/fetch/unread/${emailId}`,
      "get",
      (data) => {
        if(data?.length) {
          dispatch(setNotificationsDirect([...data].reverse()));
        }
      },
      () => {}
    )
  }
  const setupApplication = () => {
    if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
      fetchToken();
      fetchIDMToken();
      fetchIWAToken()
    } else {
      setIsAppReady(true);
    }
  };
  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
  };

  useEffect(() => {
    if (applicationConfig.environment === "localhost" || applicationConfig.environment === "127.0.0.1") {
      fetchAndDispatchUser(); 
    }
  }, [idmToken,iwaToken]);

  useEffect(() => {
    fetchLangTranslation();
  }, [langSelected]);

  useEffect(() => {
    setupApplication();
    fetchAndDispatchUser();
    fetchModuleAccess();
    fetchAsyncResponse();
    fetchCurrentSAPSystem();
  }, []);

  useEffect(() => {
    if (emailId) {
      fetchGlobalAppSettings(userData, dispatch);
      fetchNotificationPreferences()
    }
  }, [emailId]);

  const fetchNotificationPreferences = () => {
    doAjax(`/${destination_Websocket}/notification-preferences/${emailId}`,
      "get",
      (data) => {
        dispatch(setNotificationPreference(data?.inAppNotification))
        dispatch(setEmailPreference(data?.emailNotification))
        dispatch(setThemePreference(data?.theme));
      },
      () => {},
    );
  }

  if (isSessionExpired || isLoggedOut) {
    return <SessionExpiredScreen />;
  }

  return (
    <ErrorBoundary>
      <>
        {isAppReady && (
          <div className="App">
              <ThemeProvider theme={dynamicTheme}>
                  <Suspense fallback={<LoadingComponent />}>
                    <Routes>
                      {moduleAccessStatus ? (
                        <Route path="*" element={<ApplicationRouter/>} />
                      ) : (
                        <Route path="*" element={<InvalidUser  />} />
                      )}
                    </Routes>
                  </Suspense>
              </ThemeProvider>
          </div>
        )}
        
        <ReduxSnackbar/>
        <ReusableDialog
          dialogState={openMessageDialog}
          openReusableDialog={handleMessageDialogClickOpen}
          closeReusableDialog={handleMessageDialogClose}
          dialogTitle={messageDialogTitle}
          dialogMessage={messageDialogMessage}
          handleDialogConfirm={handleMessageDialogClose}
          dialogOkText={"OK"}
          dialogSeverity={messageDialogSeverity}
        />
        <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
        <NetworkStatus/>
        {isAppReady && 
        <ThemeProvider theme={dynamicTheme}>
        <FloatingChatbot
          emailId={emailId}
        />
        <AppTour dynamicTheme={dynamicTheme} />
        </ThemeProvider>}
        <ToastContainer/>
        
      </>
     </ErrorBoundary>
  );
}

export default App;