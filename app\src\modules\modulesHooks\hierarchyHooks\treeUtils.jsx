import React from "react";
import { <PERSON><PERSON>, <PERSON>lt<PERSON> } from "antd";
import {
  FolderOpenOutlined,
  FolderOutlined,
  BranchesOutlined,
  NodeIndexOutlined,
  TagOutlined,
  PlusCircleOutlined,
  EditOutlined,
  SwapOutlined,
  CloseCircleOutlined,
  <PERSON><PERSON><PERSON>tOutlined,
  LeftSquareOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import { treeTheme, treeStyles } from "../../../constant/treeStyle";

export const getAllNodeKeys = (nodes) => {
  let keys = [];
  const traverse = (node) => {
    keys.push(node.id);
    if (node.child) {
      node.child.forEach(traverse);
    }
  };
  nodes.forEach(traverse);
  return keys;
};

export const getNodeIcon = (node, isExpanded = false) => {
  if (node.child && node.child.length > 0) {
    return isExpanded ? (
      <FolderOpenOutlined style={{ color: treeTheme.colors.warning, ...treeStyles.nodeIcon }} />
    ) : (
      <FolderOutlined style={{ color: treeTheme.colors.primary, ...treeStyles.nodeIcon }} />
    );
  } else if (node.tags && node.tags.length > 0) {
    return <BranchesOutlined style={{ color: treeTheme.colors.success, ...treeStyles.nodeIcon }} />;
  } else {
    return <NodeIndexOutlined style={{ color: treeTheme.colors.textSecondary, ...treeStyles.nodeIcon }} />;
  }
};

export const getActionButton = (icon, onClick, tooltip, type = "default", danger = false) => {
  const iconColor = danger ? treeTheme.colors.error : type === "primary" ? "white" : treeTheme.colors.textPrimary;
  return (
    <Tooltip placement="top" title={tooltip}>
      <Button
        type={type}
        danger={danger}
        size="small"
        icon={React.cloneElement(icon, { style: { color: danger ? "white" : iconColor } })}
        onClick={onClick}
        style={{
          ...treeStyles.actionIconButton,
          ...(type === "primary" && { backgroundColor: treeTheme.colors.primary }),
          ...(danger && { backgroundColor: treeTheme.colors.error, color: "white" }),
        }}
      />
    </Tooltip>
  );
};

const createHighlightedText = (text, searchValue) => {
  if (!searchValue) return text;
  return (
    <span>
      {text.split(new RegExp(`(${searchValue})`, "i")).map((part, i) =>
        part.toLowerCase() === searchValue.toLowerCase() ? (
          <span key={i} style={treeStyles.searchHighlight}>{part}</span>
        ) : (
          part
        )
      )}
    </span>
  );
};

export const convertTreeData = (
  nodes,
  parentNode,
  {
    searchValue,
    expandedKeys,
    editmode,
    activeAction,
    selectedNode,
    selectedTag,
    object,
    moduleObject,
    handlers,
  }
) => {
  const {
    handleRemoveTag,
    handleSelectTag,
    handleSelectNode,
    handlePlaceNode,
    handlePlaceTag,
    handleDeleteNode,
    setCurrentParent,
    setIsModalVisible,
    setCurrentTagParent,
    setIsTagModalVisible,
    setCurrentEditNode,
    setIsEditModalVisible,
    editForm,
  } = handlers;

  return nodes.map((node) => {
    const tagNodes = node?.tags?.map((tag, index) => {
      const tagKey = `${node.id}-tag-${index}`;
      const tagMatches = searchValue && 
        (tag.toLowerCase().includes(searchValue.toLowerCase()) ||
         `${node.label} Tag`.toLowerCase().includes(searchValue.toLowerCase()));

      return {
        title: (
          <div style={treeStyles.tagTitle}>
            <TagOutlined style={treeStyles.tagIcon} />
            <div style={treeStyles.tagLabel}>
              {createHighlightedText(tag, searchValue) || (
                <Tooltip placement="bottomLeft" title={`${tag} under ${node?.label}`}>
                  {tag}
                </Tooltip>
              )}
            </div>
            {editmode && (
              <div className="node-action-buttons" style={treeStyles.nodeActionButtons}>
                {activeAction === "remove" &&
                  getActionButton(
                    <CloseCircleOutlined />,
                    (e) => { e.stopPropagation(); handleRemoveTag(tag, node); },
                    `Remove ${moduleObject} ${tag}`,
                    "default",
                    true
                  )}
                {activeAction === "move" && !selectedNode && !selectedTag &&
                  getActionButton(
                    <SwapOutlined />,
                    (e) => { e.stopPropagation(); handleSelectTag(tag, node); },
                    `Move ${moduleObject}`,
                    "default"
                  )}
              </div>
            )}
          </div>
        ),
        key: tagKey,
        isLeaf: true,
        className: "tag-node",
        style: tagMatches ? { backgroundColor: treeTheme.colors.warningBg } : {},
      };
    }) || [];

    const childNodes = node?.child ? convertTreeData(node?.child, node, {
      searchValue, expandedKeys, editmode, activeAction, selectedNode, selectedTag, object,moduleObject, handlers
    }) : [];

    const labelMatch = searchValue && node?.label.toLowerCase().includes(searchValue.toLowerCase());
    const descMatch = searchValue && node?.description.toLowerCase().includes(searchValue.toLowerCase());
    const isMatched = labelMatch || descMatch;
    const isExpanded = expandedKeys.includes(node?.id);

    return {
      title: (
        <div className="node-title-container" style={treeStyles.nodeTitle}>
          <div style={treeStyles.nodeContent}>
            {getNodeIcon(node, isExpanded)}
            <div style={treeStyles.nodeLabel}>
              {createHighlightedText(node.label, searchValue) || node.label}
            </div>
            <div style={treeStyles.nodeDescription}>
              {createHighlightedText(node.description, searchValue) || node.description}
            </div>
          </div>
          {editmode && (
            <div className="node-action-buttons" style={treeStyles.nodeActionButtons}>
              {activeAction === "add" && (
                <>
                  {!node?.tags?.length &&
                    getActionButton(
                      <PlusCircleOutlined />,
                      (e) => { e.stopPropagation(); setCurrentParent(node); setIsModalVisible(true); },
                      `Add New Node under ${node?.label}`,
                      "default"
                    )}
                  {node?.child?.length === 0 &&
                    getActionButton(
                      <TagOutlined />,
                      (e) => { e.stopPropagation(); setCurrentTagParent(node); setIsTagModalVisible(true); },
                      `Add New ${moduleObject} under ${node?.label}`,
                      "default"
                    )}
                  {getActionButton(
                    <EditOutlined />,
                    (e) => {
                      e.stopPropagation();
                      setCurrentEditNode(node);
                      editForm.setFieldsValue({ description: node.description });
                      setIsEditModalVisible(true);
                    },
                    `Change Description for ${node?.label}`,
                    "default"
                  )}
                </>
              )}
              {activeAction === "move" && node.id !== "1" && !selectedNode && !selectedTag &&
                getActionButton(
                  <SwapOutlined />,
                  (e) => { e.stopPropagation(); handleSelectNode(node); },
                  `Move ${node?.label}`,
                  "default"
                )}
              {selectedNode && node?.isParent && node?.tags?.length === 0 &&
                getActionButton(
                  <ArrowLeftOutlined />,
                  (e) => { e.stopPropagation(); handlePlaceNode(node); },
                  `Put moved Node back to ${node?.label}`,
                  "primary"
                )}
              {selectedTag && node.isParent && node?.child?.length === 0 &&
                getActionButton(
                  <LeftSquareOutlined />,
                  (e) => { e.stopPropagation(); handlePlaceTag(node); },
                  `Put moved ${moduleObject} back to ${node?.label}`,
                  "primary"
                )}
              {activeAction === "delete" && node?.id !== "1" &&
                getActionButton(
                  <DeleteOutlined />,
                  (e) => { e.stopPropagation(); handleDeleteNode(node, parentNode); },
                  `Delete node ${node?.label}`,
                  "default",
                  true
                )}
            </div>
          )}
        </div>
      ),
      key: node.id,
      children: [...childNodes, ...tagNodes],
      isLeaf: !node.isParent && tagNodes.length === 0,
      className: node.isParent ? "parent-node" : "leaf-node",
      style: isMatched ? { backgroundColor: treeTheme.colors.warningBg } : {},
      dataRef: node,
    };
  });
};