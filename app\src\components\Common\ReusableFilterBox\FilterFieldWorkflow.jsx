import React, { useState, useRef, useEffect } from 'react';
import {
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  Box,
  Grid,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Autocomplete,
  Chip,
  Popover,
  Tooltip,
  CircularProgress,
  FormGroup,
  FormControlLabel,
  styled,
  useTheme
} from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import ClearIcon from '@mui/icons-material/Clear';
import SearchIcon from '@mui/icons-material/Search';
import useLang from '../../../hooks/useLang';
import { colors } from '../../../constant/colors';
import { button_Outlined, button_Primary, font_Small, outerContainer_Information, outermostContainer, outermostContainer_Information, iconButton_SpacingSmall, button_Marginleft } from "../../Common/commonStyles";
import MaterialDropdown from "@components/Common/ui/dropdown/MaterialDropdown";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import DateRange from "../../Common/DateRangePicker";
import { WF_FILTER_OPTIONS } from '@constant/enum';
import { destination_Admin } from '../../../destinationVariables'
import { doAjax } from "@components/Common/fetchService";
import { END_POINTS } from '@constant/apiEndPoints';

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));

const FilterContainer = styled(Grid)({
  padding: '0.75rem',
  gap: '0.5rem',
});

const ButtonContainer = styled(Grid)({
  display: 'flex',
  justifyContent: 'flex-end',
  paddingRight: '0.75rem',
  paddingBottom: '0.75rem',
  paddingTop: '0rem',
  gap: '0.5rem',

});

const ActionButton = styled(Button)({
  borderRadius: '4px',
  padding: '4px 12px',
  textTransform: 'none',
  fontSize: '0.875rem',
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));

const FilterFieldWorkflow = ({ searchParameters, setWfSearchForm, wfSearchForm, activeTab, getAllWorflows}) => {
  const [workFlowOptions, setWorkFlowOptions] = useState([]);
  const [userOptions, setUserOptions] = useState([]);
  const [timerId, setTimerId] = useState(null);
  const [selectedMaterial, setSelectedMaterial] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const { t } = useLang();
  const theme = useTheme();

  const handleInputChange = (name, newValue) => {
    let tempData;
    tempData = newValue || "";

    let tempFilterData = {
      ...wfSearchForm,
      [name]: tempData,
    };
    setWfSearchForm(tempFilterData);
  };

  const handleDate = (e) => {
    if (e !== null) {
      var createdOn = e;
      setWfSearchForm((prev) => ({ ...prev, createdOn: createdOn }));
    }
  };

  useEffect(() => {
    if (selectedMaterial?.length) {
      handleInputChange("workflowName", selectedMaterial?.map(item => item.code));
    } else {
      handleInputChange("workflowName", []);
    }
  }, [selectedMaterial]);

  useEffect(() => {
    getAllUsers();
  }, [])

  const getMaterialSearch = (inputValue) => {
    setIsDropDownLoading(true);
    let payload = {
      workflowName: inputValue,
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setWorkFlowOptions(data?.data?.map(item => ({ code: item, desc: "" })) || []);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      console.log(error);
    };
    doAjax(
      `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.WF_NAMES}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getAllUsers = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setUserOptions(data?.data || []);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
    };
    doAjax(`/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.WF_CREATEDBY}`, "get", hSuccess, hError);
  };

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(inputValue);
      }, 500);

      setTimerId(newTimerId);
    }
  };
  return (
    <Grid container>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
          <StyledAccordionSummary
            expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: theme.palette.primary.dark }} />}
            aria-controls="panel1a-content"
            id="panel1a-header"
            className="filterWorkFlow"
          >
            <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark }} />
            <Typography
              sx={{
                fontSize: '0.875rem',
                fontWeight: 600,
                color: theme.palette.primary.dark,
              }}
            >
              {t("Filter Workflows ")}
            </Typography>
          </StyledAccordionSummary>

          <AccordionDetails sx={{ padding: 0 }}>
            <FilterContainer container>
              <Grid
                container
                rowSpacing={1}
                spacing={2}
                alignItems="center"
                sx={{ padding: "0rem 1rem 0.5rem" }}
              >
                {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                  .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                  .map((item, index) => {
                    return (
                      <React.Fragment key={index}>
                        {item?.MDG_MAT_JSON_FIELD_NAME === "region" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <AutoCompleteSimpleDropDown
                              options={WF_FILTER_OPTIONS?.REGION}
                              value={wfSearchForm?.region?.length ? wfSearchForm.region : []}
                              onChange={(newValue) => {
                                handleInputChange("region", newValue);
                              }}
                              placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                            />
                          </Grid>
                        }
                        {item?.MDG_MAT_JSON_FIELD_NAME === "scenario" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <AutoCompleteSimpleDropDown
                              options={WF_FILTER_OPTIONS?.SCENARIO}
                              value={wfSearchForm?.scenario?.length ? wfSearchForm.scenario : []}
                              onChange={(newValue) => {
                                handleInputChange("scenario", newValue);
                              }}
                              placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                            />
                          </Grid>
                        }
                        {item?.MDG_MAT_JSON_FIELD_NAME === "createdBy" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <AutoCompleteSimpleDropDown
                              options={userOptions || null}
                              value={wfSearchForm?.createdBy?.length ? wfSearchForm.createdBy : []}
                              onChange={(newValue) => {
                                handleInputChange("createdBy", newValue);
                              }}
                              placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                            />
                          </Grid>
                        }
                        {item?.MDG_MAT_JSON_FIELD_NAME === "bifurcationGroup" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <AutoCompleteSimpleDropDown
                              options={WF_FILTER_OPTIONS?.BIFURCATION_GROUP}
                              value={wfSearchForm?.bifurcationGroup?.length ? wfSearchForm.bifurcationGroup : []}
                              onChange={(newValue) => {
                                handleInputChange("bifurcationGroup", newValue);
                              }}
                              placeholder={t(`SELECT ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()}
                            />
                          </Grid>
                        }
                        {item?.MDG_MAT_JSON_FIELD_NAME === "dateRange" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
                              <LocalizationProvider dateAdapter={AdapterDateFns}>
                                <DateRange
                                  handleDate={handleDate}
                                  date={wfSearchForm?.createdOn}
                                />
                              </LocalizationProvider>
                            </FormControl>
                          </Grid>
                        }
                        {item?.MDG_MAT_JSON_FIELD_NAME === "workflowName" &&
                          <Grid item md={2}>
                            <LabelTypography sx={font_Small}>{t(item?.MDG_MAT_UI_FIELD_NAME)}</LabelTypography>
                            <FormControl size="small" fullWidth>
                              <MaterialDropdown matGroup={workFlowOptions} selectedMaterialGroup={selectedMaterial} setSelectedMaterialGroup={setSelectedMaterial} isDropDownLoading={isDropDownLoading} placeholder={t(`ENTER ${item?.MDG_MAT_UI_FIELD_NAME}`).toUpperCase()} onInputChange={handleMatInputChange} minCharacters={4} />
                            </FormControl>
                          </Grid>
                        }
                      </React.Fragment>
                    )
                  })
                }
              </Grid>
            </FilterContainer>
            <ButtonContainer>
              <ActionButton
                variant="outlined"
                size="small"
                startIcon={<ClearIcon sx={{ fontSize: "1rem" }} />}
                onClick={() => setWfSearchForm({})}>
                {t("Clear")}
              </ActionButton>

              <ActionButton
                variant="contained"
                size="small"
                startIcon={<SearchIcon sx={{ fontSize: '1rem' }} />}
                onClick={() => {
                  getAllWorflows(activeTab)
                  setSelectedMaterial([])
                }}
              >
                {t("Search")}
              </ActionButton>
            </ButtonContainer>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </Grid>
  );
};

export default FilterFieldWorkflow;