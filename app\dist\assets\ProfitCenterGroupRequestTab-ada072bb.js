import{s as ot,n as q,u as Xe,r as n,g as qt,eW as Et,aX as O,j as e,c as Q,O as Ge,b1 as Cs,d as st,B as Z,an as Ue,aG as mt,aF as kt,Ae as Ys,c5 as Lt,y6 as zs,aZ as Ks,aD as Xs,C as Be,bK as Fe,bU as et,aT as Ke,cz as Qs,z9 as Vs,ap as ms,aO as Ae,aY as Zs,ae as wt,f7 as It,A5 as _t,fI as us,fP as yt,fQ as ht,Af as Js,Ag as eo,Ah as to,Ai as so,Aj as oo,Ak as ro,Al as no,Am as Dt,An as Ct,Ao as ao,aa as io,T as $e,bD as lo,cH as Tt,cq as xt,Ap as Ts,a6 as ve,cw as co,d5 as gs,d3 as Ps,d4 as Ss,af as uo,fb as Rs,aH as fo,Z as tt,Aa as po,a as ho,aP as No,Aq as Eo,f9 as Do,xY as Co,xZ as mo,g4 as fs,wZ as To,w_ as go,y0 as ps,y1 as Po,wY as So,aA as Ro,aB as Oo,Ar as bo,bd as hs,As as Ao,er as Io,bJ as _o,cA as yo,y3 as Lo,bc as wo,F as Ns,cI as ze,d6 as xo,d7 as qo,am as ko,br as Fo,ad as Bo,g3 as Ze,bf as Nt,bI as Es,d8 as Ho,a$ as Mo,aE as $o,d9 as vo,fY as Uo,al as Go,cZ as Wo,gA as jo,At as Yo,aJ as zo}from"./index-226a1e75.js";import{D as Ko,A as Xo,P as Qo,S as Vo,B as Zo,g as Jo}from"./PreviewPage-262cf4cb.js";import{u as er,R as tr,a as sr,b as or,C as rr,d as nr}from"./useDynamicWorkflowDTHierarchy-d433cb98.js";import{d as ar}from"./PermIdentityOutlined-842d404f.js";import{d as ir}from"./TrackChangesTwoTone-f7d7fb26.js";import{d as lr}from"./FileUploadOutlined-3ff8ee58.js";import{E as cr}from"./ExcelOperationsCard-3cc40005.js";import{F as dr}from"./FilterFieldGlobal-b5a561ef.js";import{u as ur}from"./useProfitCenterChangeFieldConfig-8887057c.js";import{R as fr}from"./ReusableHIerarchyTree-e69bb363.js";import{u as pr,E as hr}from"./ErrorReportDialog-e2a11116.js";import{d as We}from"./DeleteOutline-259b9549.js";import"./AttachFile-fd8e4fbe.js";import"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";import"./CloudUpload-17ed0189.js";import"./utilityImages-067c3dc2.js";import"./Delete-3f2fc9ef.js";import"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";import"./Description-d98685cc.js";import"./DataObject-2e0c0294.js";import"./Download-f2e7dedd.js";import"./useFinanceCostingRows-699f667f.js";import"./CheckCircleOutline-70edf41f.js";import"./FeedOutlined-2c089703.js";import"./useChangeMaterialRowsRequestor-9caa254c.js";import"./FilterChangeDropdown-2d228e28.js";import"./CloudDownload-23cede9e.js";import"./AttachmentUploadDialog-5b2112e0.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./context-5b1a8b0b.js";import"./EyeOutlined-6bec9589.js";import"./EditOutlined-5e4d9326.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-cbc675ea.js";import"./index-a591cf5c.js";import"./lz-string-127b8448.js";import"./ErrorHistory-e3f4447c.js";const Nr=({downloadClicked:u,setDownloadClicked:i,setIsSecondTabEnabled:D,setIsAttachmentTabEnabled:R})=>{var nt,at,it,Pt;const C=window.location.hash.split("/");C[C.length-1],new Date().toLocaleDateString("en-GB");const s=ot(),t=q(g=>g.hierarchyData.requestHeaderData),y=q(g=>g.request.requestHeader),M=q(g=>g.userManagement.userData);q(g=>g.hierarchyData);const L=q(g=>g.tabsData.requestHeaderData);q(g=>g.AllDropDown.dropDown.FieldName||[]);const B=q(g=>g.AllDropDown.dropDown),j=Xe(),z=new URLSearchParams(j.search),Ee=z.get("RequestType"),De=z.get("RequestId");z.get("reqBench");const de=z.get("RequestId"),Ie=`/Date(${Date.now()})/`,[Ce,me]=n.useState(""),[ne,Pe]=n.useState(""),[F,G]=n.useState(""),[W,J]=n.useState(""),[Te,we]=n.useState([]),ue=qt(),[fe,P]=n.useState(!1);n.useState(!1),n.useState("");const[pe,w]=n.useState("success"),[_e,xe]=n.useState(!1),[Se,r]=n.useState("systemGenerated"),[h,a]=n.useState(),[l,d]=n.useState(!1),[H,f]=n.useState(!1),[o,E]=n.useState(!1),[I,T]=n.useState(""),[$,A]=n.useState(""),[_,Re]=n.useState(!1),{getChangeTemplate:v}=ur(),{getRequestHeaderTemplatePCG:qe}=er(),ae=j.state,ke=[{code:"Create",tooltip:"Create New Profit Center Group Directly in Application"},{code:"Change",tooltip:"Modify Existing Profit Center Group Directly in Application"},{code:"Create with Upload",tooltip:"Create New Profit Center Group with Excel Upload"},{code:"Change with Upload",tooltip:"Modify Existing Profit Center Group with Excel Upload"}],je=[{code:"All Other Fields",desc:""},{code:"Address Change",desc:""},{code:"Block",desc:""},{code:"Temporary Block/Unblock",desc:""}],Oe=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];s(Et({keyName:"RequestStatus",data:"DRAFT"})),s(Et({keyName:"ReqCreatedBy",data:M==null?void 0:M.user_id})),n.useEffect(()=>{var g;if(u){if((t==null?void 0:t.RequestType)===O.CREATE_WITH_UPLOAD){Re(!0);return}if((t==null?void 0:t.RequestType)===((g=O)==null?void 0:g.CHANGE_WITH_UPLOAD)){f(!0);return}}},[u]);const S=()=>{var V,ee;let g=!0;return t&&((V=L[Object.keys(L)])!=null&&V.length)?(ee=L[Object.keys(L)[0]])==null||ee.forEach(c=>{var ge;!t[c.jsonName]&&c.visibility===((ge=Xs)==null?void 0:ge.MANDATORY)&&(g=!1)}):g=!1,g};n.useEffect(()=>{t!=null&&t.TemplateName&&v(t==null?void 0:t.TemplateName)},[t==null?void 0:t.TemplateName]),n.useEffect(()=>{qe()},[t==null?void 0:t.RequestType]),n.useEffect(()=>{const g=Ce&&ne&&F.trim()!=="",V=Ce!=="Change"||Ce==="Change with Upload"||W&&Te.length>0;P(g&&V)},[Ce,ne,F,W,Te]),n.useEffect(()=>{var g,V;De&&(Ee===((g=O)==null?void 0:g.CREATE)||Ee===((V=O)==null?void 0:V.CHANGE))&&ae&&(!ae.parentNode||ae.parentNode.length===0)&&f(!0)},[De,Ee,ae]);const ye=()=>{const g=new Date(t==null?void 0:t.ReqCreatedOn).getTime();f(!1);const V={RequestId:"",ReqCreatedBy:(M==null?void 0:M.user_id)||"",ReqCreatedOn:g?`/Date(${g})/`:Ie,ReqUpdatedOn:g?`/Date(${g})/`:Ie,RequestType:(t==null?void 0:t.RequestType)||"",RequestPrefix:"",RequestPriority:(t==null?void 0:t.RequestPriority)||"",RequestDesc:(t==null?void 0:t.RequestDesc)||"",RequestStatus:"DRAFT",FirstProd:"",LaunchDate:"",LeadingCat:"",Division:"",TemplateName:"",FieldName:"",Region:(t==null?void 0:t.Region)||"",FilterDetails:"",IsBifurcated:!0,IsHierarchyGroup:!0},ee=U=>{var He,re;if(d(!0),a(`Request Header Created Successfully! Request ID: ${(He=U==null?void 0:U.body)==null?void 0:He.requestId}`),xe(!1),w("success"),N(),R(!0),s(Et({keyName:Qs.REQUEST_ID,data:(re=U==null?void 0:U.body)==null?void 0:re.requestId})),s(Vs(U==null?void 0:U.body)),s(ms(U.body)),(t==null?void 0:t.RequestType)===O.CREATE||(t==null?void 0:t.RequestType)===O.CHANGE){f(!0);return}if((t==null?void 0:t.RequestType)===O.CREATE_WITH_UPLOAD||(t==null?void 0:t.RequestType)===O.CHANGE_WITH_UPLOAD){Re(!0);return}},c=U=>{console.error("APIError",U),d(!0),w("error"),a("Error occured while saving Request Header"),N()};Be(`/${et}/massAction/createRequestHeader`,"post",ee,c,V)},oe=()=>{var g;i(!1),Re(!1),r("systemGenerated"),de||ue((g=Fe)==null?void 0:g.REQUEST_BENCH)},N=()=>{E(!0)},Qe=()=>{E(!1)},ie=g=>{var V;r((V=g==null?void 0:g.target)==null?void 0:V.value)},Le=()=>{Se==="systemGenerated"&&(gt(),oe()),Se==="mailGenerated"&&(rt(),oe())},gt=()=>{T("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),A(!0);const g=c=>{var He,re,Ne,Me,lt,ct;if((c==null?void 0:c.size)==0){A(!1),T(""),d(!0),a("No data found for the selected criteria."),w("danger"),N();return}const ge=URL.createObjectURL(c),U=document.createElement("a");U.href=ge,U.setAttribute("download",(t==null?void 0:t.RequestType)===((He=O)==null?void 0:He.CHANGE_WITH_UPLOAD)?`${(re=Ae)==null?void 0:re.PCG}_Mass Change.xlsx`:`${(Ne=Ae)==null?void 0:Ne.PCG}_Mass Create.xlsx`),document.body.appendChild(U),U.click(),document.body.removeChild(U),URL.revokeObjectURL(ge),A(!1),T(""),d(!0),a(`${(t==null?void 0:t.RequestType)===((Me=O)==null?void 0:Me.CHANGE_WITH_UPLOAD)?`${(lt=Ae)==null?void 0:lt.PCG}_Mass Change`:`${(ct=Ae)==null?void 0:ct.PCG}_Mass Create`}.xlsx has been downloaded successfully.`),w("success"),N(),setTimeout(()=>{ue("/requestBench")},2600)},V=()=>{A(!1)},ee=`/${et}${(t==null?void 0:t.RequestType)===O.CHANGE_WITH_UPLOAD?Ke.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD:Ke.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD}`;Be(ee,"getblobfile",g,V)},rt=()=>{A(!0);const g=()=>{var c;A(!1),T(""),d(!0),a((c=Zs)==null?void 0:c.DOWNLOAD_MAIL_INITIATED),w("success"),N(),setTimeout(()=>{var ge;ue((ge=Fe)==null?void 0:ge.REQUEST_BENCH)},2600)},V=()=>{var c;A(!1),d(!0),a((c=wt)==null?void 0:c.ERR_DOWNLOADING_EXCEL),w("danger"),N(),setTimeout(()=>{var ge;ue((ge=Fe)==null?void 0:ge.REQUEST_BENCH)},2600)},ee=`/${destination_MaterialMgmt}${(t==null?void 0:t.RequestType)===O.CHANGE_WITH_UPLOAD?Ke.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL:Ke.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL}`;Be(ee,"get",g,V)};let he={[(nt=O)==null?void 0:nt.CREATE]:"CREATE_PCG",[(at=O)==null?void 0:at.CHANGE]:"CHANGE_PCG"};return e("div",{children:Q(Ks,{spacing:2,children:[Object.entries(L).map(([g,V])=>Q(Ge,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Cs},children:[e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:g}),e(Z,{children:e(Ge,{container:!0,spacing:1,children:V.filter(ee=>ee.visibility!=="Hidden").sort((ee,c)=>ee.sequenceNo-c.sequenceNo).map(ee=>e(dr,{isHeader:!0,field:ee,dropDownData:{RequestType:ke,RequestPriority:Oe,TemplateName:je},disabled:de||!!(y!=null&&y.requestId),requestHeader:!0,module:"PCG"},ee.id))})}),!de&&!(y!=null&&y.requestId)&&e(Z,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(Ue,{variant:"contained",color:"primary",disabled:!S(),onClick:ye,children:"Save Request Header"})})]},g)),e(mt,{blurLoading:$,loaderMessage:I}),l&&e(kt,{openSnackBar:o,alertMsg:h,alertType:pe,handleSnackBarClose:Qe}),H&&e(tr,{open:H,onClose:()=>{f(!1)},parameters:Ys[he==null?void 0:he[t==null?void 0:t.RequestType]],setShowTable:!1,allDropDownData:B,setIsSecondTabEnabled:D,module:(it=Lt)==null?void 0:it.PCG,mandatoryFields:(Pt=zs)==null?void 0:Pt[he==null?void 0:he[t==null?void 0:t.RequestType]]}),e(Ko,{onDownloadTypeChange:Le,open:_,downloadType:Se,handleDownloadTypeChange:ie,onClose:oe})]})})},Ft=({setSuccessDialogOpen:u,setDialogData:i,selectedLevel:D}={})=>{var f;const R=ot();qt();const p=Xe(),C=new URLSearchParams(p.search),m=C.get("RequestType"),s=C.get("RequestId"),{getDynamicWorkflowDT:t}=sr(),[y,M]=n.useState(!1),[L,B]=n.useState(!1),[j,z]=n.useState(!1),[Ee,De]=n.useState(""),[de,Ie]=n.useState("success"),[Ce,me]=n.useState(!1),[ne,Pe]=n.useState([]),F=q(o=>o.userManagement.taskData),G=q(o=>o.payload.filteredButtons),W=q(o=>o.hierarchyData.requestHeaderData),J=q(o=>o.hierarchyData.treeData),Te=q(o=>o.payload.requestorPayload),we=q(o=>o.payload.dataLoading),ue=q(o=>o.payload.dynamicKeyValues),fe=q(o=>o.request.requestHeader),P=q(o=>o.hierarchyData),pe=q(o=>o.hierarchyData.TreeChanges),w=q(o=>o.hierarchyData.DisplayRecords),{getButtonsDisplayGlobal:_e,showWfLevels:xe}=pr(),{preparePayload:Se}=or();n.useEffect(()=>{(F!=null&&F.ATTRIBUTE_1||m)&&_e("Hierarchy Node (Profit Center)","MDG_DYN_BTN_DT","v3")},[F]),n.useEffect(()=>{var E;const o=async()=>{var I,T;try{let $={"MDG_CONDITIONS.MDG_PCG_REQUEST_TYPE":m||"","MDG_CONDITIONS.MDG_HIERARCHY_REGION":((I=P==null?void 0:P.GeneralInformation)==null?void 0:I["Hierarchy Region"])||""};const A=await t(F==null?void 0:F.ATTRIBUTE_3,"v5","MDG_DYNAMIC_WF_PCG_DT",(T=Ae)==null?void 0:T.PCG,$);Pe(A)}catch{}};m&&((E=P==null?void 0:P.GeneralInformation)!=null&&E["Hierarchy Region"])&&(F!=null&&F.ATTRIBUTE_3)&&o()},[m,(f=P==null?void 0:P.GeneralInformation)==null?void 0:f["Hierarchy Region"],F==null?void 0:F.ATTRIBUTE_3]),n.useEffect(()=>{(J==null?void 0:J.length)!==0&&M(!0)},[J]);const r=async o=>{var E,I;try{const T={controllingArea:(P==null?void 0:P.ControllingArea)??"",nodes:[o==null?void 0:o.toUpperCase()]},A=await(await It(`/${_t}/node/fetchDescriptionForNode`,"post",T)).json();return((I=(E=A==null?void 0:A.body)==null?void 0:E[0])==null?void 0:I.Description)||""}catch(T){return console.error("Error fetching old description:",T),""}},h=async o=>{var E,I;try{const T={controllingArea:(P==null?void 0:P.ControllingArea)??"",nodes:[o==null?void 0:o.toUpperCase()]},A=await(await It(`/${_t}/node/fetchParentNodeForSubNode`,"post",T)).json();return((I=(E=A==null?void 0:A.body)==null?void 0:E[0])==null?void 0:I.ParentNode)||""}catch(T){return console.error("Error fetching old parent node:",T),""}},a=async o=>{var E,I;try{const T={controllingArea:(P==null?void 0:P.ControllingArea)??"",hierarchy:(P==null?void 0:P.ParentNode)??"",pcList:[o==null?void 0:o.toUpperCase()]},A=await(await It(`/${_t}/node/fetchParentNodeForObject`,"post",T)).json();return((I=(E=A==null?void 0:A.body)==null?void 0:E[0])==null?void 0:I.Node)||""}catch(T){return console.error("Error fetching old parent for profit center:",T),""}},l=()=>{const o=[],E=[],I=[],T=[],$=[],A=[],_=[],Re=[],v=[],qe=[],ae=(w==null?void 0:w["NEW NODES"])||[],ke=(w==null?void 0:w["MOVE NODE"])||[],je=(w==null?void 0:w["PROFIT CENTERS"])||[],Oe=(w==null?void 0:w["MOVE PROFIT CENTER"])||[],S=(w==null?void 0:w.DESCRIPTIONS)||[],ye=(w==null?void 0:w["DELETE NODE"])||[];ae.forEach(N=>{N["Parent Node"]&&N["New Node"]&&(o.push(`${N["Parent Node"]}$$${N["New Node"]}`),Re.push(`${N["New Node"]}`))}),ke.forEach(N=>{N["New Parent Node"]&&N["Selected Node"]&&o.push(`${N["New Parent Node"]}$$${N["Selected Node"]}`)}),ke.forEach(N=>{N["Selected Node"]&&N["Old Parent Node"]&&E.push(`${N["Selected Node"]}$$${N["Old Parent Node"]}`)}),je.forEach(N=>{N.Node&&N["Profit Center"]&&(I.push(`${N.Node}$$${N["Profit Center"]}`),qe.push(`${N["Profit Center"]}`))}),Oe.forEach(N=>{N["New Parent Node"]&&N["Selected Profit Center"]&&I.push(`${N["New Parent Node"]}$$${N["Selected Profit Center"]}`)}),Oe.forEach(N=>{N["Old Parent Node"]&&N["Selected Profit Center"]&&T.push(`${N["Old Parent Node"]}$$${N["Selected Profit Center"]}`)}),ae.forEach(N=>{N["New Node"]&&N.Description&&($.push(`${N["New Node"]}$~$${N.Description}`),v.push(`${N.Description}`))}),S.forEach(N=>{N["Parent Node"]&&N["New Description"]&&(A.push(`${N["Parent Node"]}$~$${N["New Description"]}`),v.push(`${N["New Description"]}`))}),ye.forEach(N=>{N["Parent Node"]&&N["Deleted Node"]&&_.push(`${N["Parent Node"]}$$${N["Deleted Node"]}`)});const oe={NodeList:o,ReplaceNodesList:E,TagList:I,ReplaceTagList:T,DescList:$,EditDescList:A,DeleteNodeList:_,nodesListForDBDuplicateCheck:Re,descListForDBDuplicateCheck:v,tagListForDBDuplicateCheck:qe,success:!0};return R(Js(o)),R(eo(E)),R(to(I)),R(so(T)),R(oo($)),R(ro(A)),R(no(_)),R(Dt(Re)),R(Ct(v)),R(ao(qe)),console.log("Lists populated:",oe),oe};return{showTree:y,buttonsLoading:L,openButtonSnackBar:j,alertButtonMsg:Ee,alertButtonType:de,filteredButtons:G,requestorPayload:Te,loadForFetching:we,initialNodeData:J,showWfLevels:xe,wfLevels:ne,fetchOldDescriptionForNode:r,fetchOldParentForNode:h,fetchOldParentForObject:a,handleButtonClick:async(o,E,I)=>{var T,$,A,_,Re,v,qe;try{let ae=(Re=(_=(A=(T=Ke)==null?void 0:T.MASTER_BUTTON_APIS)==null?void 0:A[($=Lt)==null?void 0:$.PCG])==null?void 0:_[W==null?void 0:W.RequestType])==null?void 0:Re[o],ke={};if(m!=((v=O)==null?void 0:v.CHANGE_WITH_UPLOAD)){const S=Se(pe);if(B(!0),!(S!=null&&S.success))return;ke=us({...P,NodesList:S.NodeList,ReplaceNodesList:S.ReplaceNodesList,TagList:S.TagList,ReplaceTagList:S.ReplaceTagList,DescList:S.DescList,EditDescList:S.EditDescList,DeleteNodeList:S.DeleteNodeList,nodesListForDBDuplicateCheck:S.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:S.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:S.tagListForDBDuplicateCheck},fe,s,F,ue,Te,o,E,I,D)}if(m===((qe=O)==null?void 0:qe.CHANGE_WITH_UPLOAD)){const S=l();B(!0),S!=null&&S.success,ke=us({...P,NodesList:S.NodeList,ReplaceNodesList:S.ReplaceNodesList,TagList:S.TagList,ReplaceTagList:S.ReplaceTagList,DescList:S.DescList,EditDescList:S.EditDescList,DeleteNodeList:S.DeleteNodeList,nodesListForDBDuplicateCheck:S.nodesListForDBDuplicateCheck,descListForDBDuplicateCheck:S.descListForDBDuplicateCheck,tagListForDBDuplicateCheck:S.tagListForDBDuplicateCheck},fe,s,F,ue,Te,E,I,D)}const je=S=>{var ye,oe,N,Qe,ie,Le;S.statusCode>=200&&S.statusCode<300?(B(!1),i({title:yt.TITLE,message:S.message,subText:yt.SUBTEXT,buttonText:yt.BUTTONTEXT,redirectTo:(Le=(ie=(Qe=(N=(ye=Ke)==null?void 0:ye.MASTER_BUTTON_APIS)==null?void 0:N[(oe=Lt)==null?void 0:oe.PCG])==null?void 0:Qe[W==null?void 0:W.RequestType])==null?void 0:ie[o])==null?void 0:Le.NAVIGATE_TO}),u(!0)):(B(!1),i({title:ht.TITLE,message:S.message,subText:ht.SUBTEXT,buttonText:ht.BUTTONTEXT,redirectTo:ht.REDIRECT}),u(!0))},Oe=S=>{B(!1),Ie("error"),De((S==null?void 0:S.error)||"An error occurred"),me(!0),z(!0)};Be(ae==null?void 0:ae.URL,"POST",je,Oe,ke)}catch(ae){console.error("Error in handleButtonClick:",ae),B(!1)}},handleSnackBarButtonClose:()=>{z(!1)}}},se={ADD_NODE:"ADD_NODE",EDIT_DESCRIPTION:"EDIT_DESCRIPTION",ADD_PROFIT_CENTER:"ADD_PROFIT_CENTER",MOVE_NODE:"MOVE_NODE",MOVE_PROFIT_CENTER:"MOVE_PROFIT_CENTER",REMOVE_PROFIT_CENTER:"REMOVE_PROFIT_CENTER",DELETE_NODE:"DELETE_NODE",CHANGE_PERSON_RESPONSIBLE:"CHANGE_PERSON_RESPONSIBLE",DELETE_ROW:"DELETE_ROW",FIELD_UPDATE:"FIELD_UPDATE"},Er=()=>Date.now()+Math.random().toString(36).substr(2,9),Dr=(u,i,D={})=>{switch(u){case se.ADD_NODE:return`New node "${i["New Node"]}" added under parent "${i["Parent Node"]}"`;case se.EDIT_DESCRIPTION:return`Description changed for node "${i["Parent Node"]}" from "${D["Old Description"]||"N/A"}" to "${i["New Description"]}"`;case se.ADD_PROFIT_CENTER:return`Profit Center "${i["Profit Center"]}" added to node "${i.Node}"`;case se.MOVE_NODE:return`Node "${i["Selected Node"]}" moved from "${i["Old Parent Node"]}" to "${i["New Parent Node"]}"`;case se.MOVE_PROFIT_CENTER:return`Profit Center "${i["Selected Profit Center"]}" moved from "${i["Old Parent Node"]}" to "${i["New Parent Node"]}"`;case se.REMOVE_PROFIT_CENTER:return`Profit Center "${i["Selected Profit Center"]}" removed from node "${i["Parent Node"]}"`;case se.DELETE_NODE:return`Node "${i["Deleted Node"]}" deleted from parent "${i["Parent Node"]}"`;case se.CHANGE_PERSON_RESPONSIBLE:return`Person responsible changed for node "${i["Parent Node"]}" from "${D["Old Person Responsible"]||"N/A"}" to "${i["New Person Responsible"]}"`;case se.DELETE_ROW:return`Row deleted: ${i.description}`;case se.FIELD_UPDATE:return`Field updated: ${i.description}`;default:return`Unknown change type: ${u}`}},Cr=(u,i)=>{const D=[],R=["Id","Updated By","Updated On"];return Object.keys(i).forEach(p=>{if(!R.includes(p)){const C=u[p]||"",m=i[p]||"";C!==m&&D.push({field:p,oldValue:C,newValue:m})}}),D},mr=(u,i,D,R,p)=>{const C=i?`"${i}"`:"empty",m=D?`"${D}"`:"empty";return`${u} changed from ${C} to ${m} in row ${p} (${R})`},Tr=(u,i,D,R)=>{const p=[];switch(R){case 0:u==="New Node"?(i&&i.trim()!==""&&p.push({type:"node",value:i,action:"remove"}),D&&D.trim()!==""&&p.push({type:"node",value:D,action:"add"})):u==="Description"&&(i&&i.trim()!==""&&p.push({type:"desc",value:i,action:"remove"}),D&&D.trim()!==""&&p.push({type:"desc",value:D,action:"add"}));break;case 1:u==="New Description"&&(i&&i.trim()!==""&&p.push({type:"desc",value:i,action:"remove"}),D&&D.trim()!==""&&p.push({type:"desc",value:D,action:"add"}));break}return p},gr=(u,i,D,R,p,C=null)=>{const m=R.findIndex(y=>y.key===D);if(m===-1)return;const s=Cr(u,i);let t=[];s.forEach(({field:y,oldValue:M,newValue:L})=>{if(L.trim()!==""){const B=mr(y,M,L,D,i.Id);let j="FIELD_UPDATE";switch(m){case 0:y==="New Node"&&L&&(j=se.ADD_NODE);break;case 1:y==="New Description"&&L&&(j=se.EDIT_DESCRIPTION);break;case 2:y==="Profit Center"&&L&&(j=se.ADD_PROFIT_CENTER);break;case 3:y==="Selected Node"&&L&&(j=se.MOVE_NODE);break;case 4:y==="Selected Profit Center"&&L&&(j=se.MOVE_PROFIT_CENTER);break;case 5:y==="Selected Profit Center"&&L&&(j=se.REMOVE_PROFIT_CENTER);break;case 6:y==="Deleted Node"&&L&&(j=se.DELETE_NODE);break;case 7:y==="New Person Responsible"&&L&&(j=se.CHANGE_PERSON_RESPONSIBLE);break}const z=Tr(y,M,L,m);t=[...t,...z],p(j,B,{rowId:i.Id,fieldName:y,oldValue:M,newValue:L,oldData:{...u},newData:{...i},tableKey:D})}}),t.length>0&&C&&(C.batchUpdateDuplicateLists?C.batchUpdateDuplicateLists(t):t.forEach(y=>{const{type:M,value:L,action:B}=y;M==="node"&&C.updateNodesListForDuplicateCheck?C.updateNodesListForDuplicateCheck(L,B):M==="desc"&&C.updateDescListForDuplicateCheck&&C.updateDescListForDuplicateCheck(L,B)}))},Ds=(u,i,D)=>{if(!u||!D)return{isValid:!0,message:""};if(D(u,i)){const p=i==="node"?"Node":"Description";return{isValid:!1,message:`${p} "${u}" already exists. Please choose a different ${p.toLowerCase()}.`}}return{isValid:!0,message:""}},Pr=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add Profit Centers",value:"3",key:"PROFIT CENTERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move Profit Center",value:"5",key:"MOVE PROFIT CENTER"},{label:"Remove Profit Center",value:"6",key:"REMOVE PROFIT CENTER"},{label:"Delete Node",value:"7",key:"DELETE NODE"},{label:"Change Person Responsible",value:"8",key:"PERSON RESPONSIBLE"}],ce=({value:u,placeholder:i,maxLength:D=10,disabled:R=!1,rowId:p,fieldName:C,tableKey:m,fetchOldData:s=!1,fetchFunction:t=null,oldDataField:y=null,onNodeValueChange:M=null,addToChangeLog:L,updateDuplicateCheckLists:B=null,isDuplicateCheckField:j=!1,duplicateCheckType:z=null})=>{const Ee=ot(),De=q(E=>E.hierarchyData.DisplayRecords),de=Xe(),Ce=new URLSearchParams(de.search).get("reqBench"),me=q(E=>E.userManagement.userData),[ne,Pe]=n.useState(u||""),[F,G]=n.useState((u==null?void 0:u.length)||0),[W,J]=n.useState(!1),[Te,we]=n.useState({}),[ue,fe]=n.useState(""),[P,pe]=n.useState(!1),w=q(E=>{var I;return(I=E.hierarchyData.requestHeaderData)==null?void 0:I.RequestStatus}),_e=Ce&&!Tt.includes(w);n.useEffect(()=>{W||(Pe(u||""),G((u==null?void 0:u.length)||0),fe(""),pe(!1))},[u,W]);const xe=()=>j&&z&&(B==null?void 0:B.checkForDuplicates),Se=()=>z,r=E=>{const I=De[m]||[],T=I.find(A=>A.Id===p),$=I.map(A=>A.Id===p?{...A,...E,"Updated By":me==null?void 0:me.emailId,"Updated On":xt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")}:A);if(Ee(Ts({...De,[m]:$})),L&&T){const A=$.find(_=>_.Id===p);A&&gr(T,A,m,Pr,L,B)}},h=E=>{const I=E.target.value.trimStart();if(Pe(I),G(I.length),xe()&&(B!=null&&B.checkForDuplicates)){const T=Se();if(T&&I.trim()!==""){const $=Ds(I.trim(),T,B.checkForDuplicates);$.isValid?(fe(""),pe(!1)):(fe($.message),pe(!0))}else fe(""),pe(!1)}M&&M(I)},a=async()=>{J(!1);const E=ne.trim(),I=ne;if(xe()&&(B!=null&&B.checkForDuplicates)&&E!==""){const T=Se();if(T){const $=Ds(E,T,B.checkForDuplicates);$.isValid||(fe($.message),pe(!0))}}if(r({[C]:E.toUpperCase()}),s&&E&&t&&y){we(T=>({...T,[p]:!0}));try{const T=await t(E);T!=null&&r({[y]:T,[C]:I.toUpperCase()})}catch(T){console.error(`Error fetching old ${y}:`,T)}finally{we(T=>({...T,[p]:!1}))}}},l=()=>{J(!0),fe(""),pe(!1)},d=()=>P?"#ff9800":W&&F>=D?"red":"",H=()=>ue&&P?ue:W?F===D?"Max Length Reached":`${F}/${D}`:"",f=()=>ue&&P?"#ff9800":W&&F===D?"red":"blue",o=e(io,{variant:"outlined",size:"small",fullWidth:!0,disabled:_e||R||Te[p],value:ne,placeholder:i,error:P,inputProps:{style:{textTransform:"uppercase"},maxLength:D},onChange:h,onFocus:l,onBlur:a,helperText:H(),FormHelperTextProps:{sx:{color:f(),position:"absolute",bottom:"-20px",fontSize:"0.75rem"}},sx:{"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:d()},"& fieldset":{borderColor:d()},"&.Mui-error fieldset":{borderColor:"#ff9800"}}},onKeyDown:E=>E.stopPropagation()});return Q(Z,{sx:{position:"relative"},children:[P?e($e,{title:ue,arrow:!0,placement:"top",open:P,children:o}):o,Te[p]&&e(lo,{size:24,sx:{position:"absolute",top:"50%",right:8,marginTop:"-12px"}}),P&&e(Z,{sx:{position:"absolute",top:-2,right:-2,width:8,height:8,borderRadius:"50%",backgroundColor:"#ff9800",border:"2px solid white",zIndex:1}})]})},Sr=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:s=>e(ce,{value:s.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:s.row.Id,fieldName:"Parent Node",tableKey:"NEW NODES",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"New Node",headerName:"New Node",flex:1,renderCell:s=>e(ce,{value:s.row["New Node"],placeholder:"Enter New Node",maxLength:10,rowId:s.row.Id,fieldName:"New Node",tableKey:"NEW NODES",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!0,duplicateCheckType:"node"})},{field:"Description",headerName:"Description",flex:1,renderCell:s=>e(ce,{value:s.row.Description,placeholder:"Enter Description",maxLength:40,rowId:s.row.Id,fieldName:"Description",tableKey:"NEW NODES",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Person Responsible",headerName:"Person Responsible",flex:1,renderCell:s=>e(ce,{value:s.row["Person Responsible"],placeholder:"Enter Person Responsible",maxLength:40,rowId:s.row.Id,fieldName:"Person Responsible",tableKey:"NEW NODES",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,0),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],Rr=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Parent Node",headerName:"Parent Node",type:"text",flex:1,renderCell:s=>e(ce,{value:s.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:s.row.Id,fieldName:"Parent Node",tableKey:"DESCRIPTIONS",fetchOldData:!0,fetchFunction:i,oldDataField:"Old Description",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Old Description",headerName:"Old Description",flex:1,editable:!1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Old Description"]||""})},{field:"New Description",headerName:"New Description",flex:1,renderCell:s=>e(ce,{value:s.row["New Description"],placeholder:"Enter New Description",maxLength:40,rowId:s.row.Id,fieldName:"New Description",tableKey:"DESCRIPTIONS",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!0,duplicateCheckType:"desc"})},{field:"Action",headerName:"Action",hide:!1,renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,1),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],Or=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",type:"text",hide:!0},{field:"Node",headerName:"Node",type:"text",flex:1,renderCell:s=>e(ce,{value:s.row.Node,placeholder:"Enter Node",maxLength:10,rowId:s.row.Id,fieldName:"Node",tableKey:"PROFIT CENTERS",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Profit Center",headerName:"Profit Center",flex:1,renderCell:s=>e(ce,{value:s.row["Profit Center"],placeholder:"Enter Profit Center",maxLength:10,rowId:s.row.Id,fieldName:"Profit Center",tableKey:"PROFIT CENTERS",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",hide:!1,renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,2),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],br=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:s=>e(ce,{value:s.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:s.row.Id,fieldName:"New Parent Node",tableKey:"MOVE NODE",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Selected Node",headerName:"Selected Node",flex:1,renderCell:s=>e(ce,{value:s.row["Selected Node"],placeholder:"Enter Selected Node",maxLength:10,rowId:s.row.Id,fieldName:"Selected Node",tableKey:"MOVE NODE",fetchOldData:!0,fetchFunction:D,oldDataField:"Old Parent Node",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,3),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],Ar=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Old Parent Node",headerName:"Old Parent Node",flex:1,editable:!1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Old Parent Node"]||""})},{field:"New Parent Node",headerName:"New Parent Node",flex:1,renderCell:s=>e(ce,{value:s.row["New Parent Node"],placeholder:"Enter New Parent Node",maxLength:10,rowId:s.row.Id,fieldName:"New Parent Node",tableKey:"MOVE PROFIT CENTER",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Selected Profit Center",headerName:"Selected Profit Center",flex:1,renderCell:s=>e(ce,{value:s.row["Selected Profit Center"],placeholder:"Enter Profit Center",maxLength:10,rowId:s.row.Id,fieldName:"Selected Profit Center",tableKey:"MOVE PROFIT CENTER",fetchOldData:!0,fetchFunction:R,oldDataField:"Old Parent Node",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,4),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],Ir=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Parent Node"]||""})},{field:"Selected Profit Center",headerName:"Selected Profit Center",flex:1,renderCell:s=>e(ce,{value:s.row["Selected Profit Center"],placeholder:"Enter Profit Center",maxLength:10,rowId:s.row.Id,fieldName:"Selected Profit Center",tableKey:"REMOVE PROFIT CENTER",fetchOldData:!0,fetchFunction:R,oldDataField:"Parent Node",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,5),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],_r=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Parent Node"]||""})},{field:"Deleted Node",headerName:"Deleted Node",flex:1,renderCell:s=>e(ce,{value:s.row["Deleted Node"],placeholder:"Enter Node to Delete",maxLength:10,rowId:s.row.Id,fieldName:"Deleted Node",tableKey:"DELETE NODE",fetchOldData:!0,fetchFunction:D,oldDataField:"Parent Node",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,6),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],yr=(u,i,D,R,p,C,m)=>[{field:"Id",headerName:"ID",hide:!0},{field:"Parent Node",headerName:"Parent Node",flex:1,renderCell:s=>e(ce,{value:s.row["Parent Node"],placeholder:"Enter Parent Node",maxLength:10,rowId:s.row.Id,fieldName:"Parent Node",tableKey:"PERSON RESPONSIBLE",fetchOldData:!0,fetchFunction:i,oldDataField:"Old Person Responsible",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Old Person Responsible",headerName:"Old Person Responsible",flex:1,editable:!1,renderCell:s=>e("div",{style:{padding:"8px",fontSize:"14px",color:"#666"},children:s.row["Old Person Responsible"]||""})},{field:"New Person Responsible",headerName:"New Person Responsible",flex:1,renderCell:s=>e(ce,{value:s.row["New Person Responsible"],placeholder:"Enter New Person Responsible",maxLength:40,rowId:s.row.Id,fieldName:"New Person Responsible",tableKey:"PERSON RESPONSIBLE",addToChangeLog:p,updateDuplicateCheckLists:C,isDuplicateCheckField:!1})},{field:"Action",headerName:"Action",renderCell:s=>e($e,{title:"Delete Row",children:e(ve,{onClick:()=>u(s.row.Id,7),disabled:m,sx:{color:m?"grey.400":"red"},children:e(We,{})})})}],Je=[{label:"New Nodes",value:"1",key:"NEW NODES"},{label:"Description Change",value:"2",key:"DESCRIPTIONS"},{label:"Add Profit Centers",value:"3",key:"PROFIT CENTERS"},{label:"Move Node",value:"4",key:"MOVE NODE"},{label:"Move Profit Center",value:"5",key:"MOVE PROFIT CENTER"},{label:"Remove Profit Center",value:"6",key:"REMOVE PROFIT CENTER"},{label:"Delete Node",value:"7",key:"DELETE NODE"},{label:"Change Person Responsible",value:"8",key:"PERSON RESPONSIBLE"}],Lr=()=>{const u=ot(),[i,D]=n.useState(0),[R,p]=n.useState(0),[C,m]=n.useState(100),{showSnackbar:s}=co(),t=q(r=>r.hierarchyData.DisplayRecords),y=q(r=>r.hierarchyData.nodesListForDBDuplicateCheck||[]),M=q(r=>r.hierarchyData.descListForDBDuplicateCheck||[]),L=Xe(),j=new URLSearchParams(L.search).get("reqBench"),z=q(r=>r.userManagement.userData),Ee=q(r=>{var h;return(h=r.hierarchyData.requestHeaderData)==null?void 0:h.RequestStatus}),De=j&&!Tt.includes(Ee),{fetchOldDescriptionForNode:de,fetchOldParentForNode:Ie,fetchOldParentForObject:Ce}=Ft();n.useEffect(()=>{me()},[]);const me=()=>{const r=new Set,h=new Set;((t==null?void 0:t["NEW NODES"])||[]).forEach(f=>{f["New Node"]&&f["New Node"].trim()!==""&&r.add(f["New Node"].trim().toUpperCase()),f.Description&&f.Description.trim()!==""&&h.add(f.Description.trim().toUpperCase())}),((t==null?void 0:t.DESCRIPTIONS)||[]).forEach(f=>{f["New Description"]&&f["New Description"].trim()!==""&&h.add(f["New Description"].trim().toUpperCase())});const d=Array.from(r),H=Array.from(h);(d.length>0||y.length===0)&&u(Dt(d)),(H.length>0||M.length===0)&&u(Ct(H))},ne=(r,h="add")=>{var d,H;if(!r||r.trim()==="")return;const a=r.trim().toUpperCase();let l=[...y];if(h==="add")l.some(f=>f.toUpperCase()===a)||(l.push(a),console.log(`Added node to duplicate check list: ${a}`));else if(h==="remove"){const f=l.length;l=l.filter(o=>o.toUpperCase()!==a),l.length<f&&console.log(`Removed node from duplicate check list: ${a}`)}else if(h==="replace"){const f=(d=r.old)==null?void 0:d.trim().toUpperCase(),o=(H=r.new)==null?void 0:H.trim().toUpperCase();f&&o&&f!==o&&(l=l.filter(E=>E.toUpperCase()!==f),l.some(E=>E.toUpperCase()===o)||l.push(o),console.log(`Replaced node in duplicate check list: ${f} -> ${o}`))}u(Dt(l))},Pe=(r,h="add")=>{var d,H;if(!r||r.trim()==="")return;const a=r.trim().toUpperCase();let l=[...M];if(h==="add")l.some(f=>f.toUpperCase()===a)||(l.push(a),console.log(`Added description to duplicate check list: ${a}`));else if(h==="remove"){const f=l.length;l=l.filter(o=>o.toUpperCase()!==a),l.length<f&&console.log(`Removed description from duplicate check list: ${a}`)}else if(h==="replace"){const f=(d=r.old)==null?void 0:d.trim().toUpperCase(),o=(H=r.new)==null?void 0:H.trim().toUpperCase();f&&o&&f!==o&&(l=l.filter(E=>E.toUpperCase()!==f),l.some(E=>E.toUpperCase()===o)||l.push(o),console.log(`Replaced description in duplicate check list: ${f} -> ${o}`))}u(Ct(l))},F=r=>{let h=[...y],a=[...M];r.forEach(l=>{const{type:d,value:H,action:f}=l;if(d==="node"&&H&&H.trim()!==""){const o=H.trim().toUpperCase();f==="add"&&!h.some(E=>E.toUpperCase()===o)?h.push(o):f==="remove"&&(h=h.filter(E=>E.toUpperCase()!==o))}else if(d==="desc"&&H&&H.trim()!==""){const o=H.trim().toUpperCase();f==="add"&&!a.some(E=>E.toUpperCase()===o)?a.push(o):f==="remove"&&(a=a.filter(E=>E.toUpperCase()!==o))}}),u(Dt(h)),u(Ct(a))},G=(r,h)=>{if(!r||r.trim()==="")return!1;const a=r.trim().toUpperCase();return h==="node"?y.some(l=>l.toUpperCase()===a):h==="desc"?M.some(l=>l.toUpperCase()===a):!1},W=(r,h,a={})=>{const l={id:Er(),type:r,description:h,updatedBy:(z==null?void 0:z.emailId)||"<EMAIL>",updatedOn:xt().utc().format("YYYY-MM-DDTHH:mm:ss[Z]"),...a};u(Rs(l))},J=(r,h,a=null,l=null,d=null)=>{if(u(Ts({...t,[r]:h})),a&&l){const H=Dr(a,l,d);W(a,H,{tableName:r,rowData:{...l},oldRowData:d?{...d}:null})}},Te=r=>{const h=r.length>0?Math.max(...r.map(a=>parseInt(a.Id))):0;return String(h+1)},we=(r,h)=>{p(h)},ue=r=>{const h=r.target.value;m(h),p(0)},fe=()=>{const r=Je[i],h=(t==null?void 0:t[r.key])||[];let a;const l={Id:Te(h),"Updated By":z==null?void 0:z.emailId,"Updated On":xt().utc().format("YYYY-MM-DD HH:mm:ss.SSS")};switch(i){case 0:a={...l,"Parent Node":"","New Node":"",Description:"","Person Responsible":""};break;case 1:a={...l,"Parent Node":"","Old Description":"","New Description":""};break;case 2:a={...l,Node:"","Profit Center":""};break;case 3:a={...l,"Old Parent Node":"","New Parent Node":"","Selected Node":""};break;case 4:a={...l,"Old Parent Node":"","New Parent Node":"","Selected Profit Center":""};break;case 5:a={...l,"Parent Node":"","Selected Profit Center":""};break;case 6:a={...l,"Parent Node":"","Deleted Node":""};break;case 7:a={...l,"Parent Node":"","Old Person Responsible":"","New Person Responsible":""};break;default:a=l}const d=[...h,a];J(r.key,d),W("ROW_CREATED",`New ${r.label.toLowerCase()} row created`,{stepLabel:r.label,rowId:a.Id})},P=(r,h)=>{const a=Je[h],l=(t==null?void 0:t[a.key])||[],d=l.find(f=>f.Id===r),H=l.filter(f=>f.Id!==r);if(J(a.key,H),d){const f=[];switch(h){case 0:d["New Node"]&&f.push({type:"node",value:d["New Node"],action:"remove"}),d.Description&&f.push({type:"desc",value:d.Description,action:"remove"});break;case 1:d["New Description"]&&f.push({type:"desc",value:d["New Description"],action:"remove"});break}f.length>0&&F(f);let o=`Row deleted from ${a.label}`;switch(h){case 0:d["New Node"]&&d["Parent Node"]&&(o=`Deleted new node "${d["New Node"]}" under "${d["Parent Node"]}"`);break;case 1:d["Parent Node"]&&(o=`Cancelled description change for node "${d["Parent Node"]}"`);break;case 2:d["Profit Center"]&&d.Node&&(o=`Cancelled adding profit center "${d["Profit Center"]}" to node "${d.Node}"`);break;case 3:d["Selected Node"]&&(o=`Cancelled move operation for node "${d["Selected Node"]}"`);break;case 4:d["Selected Profit Center"]&&(o=`Cancelled move operation for profit center "${d["Selected Profit Center"]}"`);break;case 5:d["Selected Profit Center"]&&d["Parent Node"]&&(o=`Cancelled removal of profit center "${d["Selected Profit Center"]}" from "${d["Parent Node"]}"`);break;case 6:d["Deleted Node"]&&d["Parent Node"]&&(o=`Cancelled deletion of node "${d["Deleted Node"]}" from "${d["Parent Node"]}"`);break;case 7:d["Parent Node"]&&(o=`Cancelled person responsible change for node "${d["Parent Node"]}"`);break}W(se.DELETE_ROW,o,{deletedRow:{...d},stepLabel:a.label})}},pe=(r,h)=>{console.log("Selected rows:",r,"for step:",h)},_e=(()=>{const r=Je[i],h=(t==null?void 0:t[r.key])||[];let a=[];const l=[P,de,Ie,Ce,W,{updateNodesListForDuplicateCheck:ne,updateDescListForDuplicateCheck:Pe,checkForDuplicates:G,batchUpdateDuplicateLists:F},De];switch(i){case 0:a=Sr(...l);break;case 1:a=Rr(...l);break;case 2:a=Or(...l);break;case 3:a=br(...l);break;case 4:a=Ar(...l);break;case 5:a=Ir(...l);break;case 6:a=_r(...l);break;case 7:a=yr(...l);break;default:a=[]}return{rows:h,columns:a,title:r.label,key:r.key}})(),xe=r=>{const h=Je[r],a=(t==null?void 0:t[h.key])||[];if(a.length===0)return{isValid:!0,message:""};const d={0:["Parent Node","New Node","Description"],1:["Parent Node","New Description"],2:["Node","Profit Center"],3:["New Parent Node","Selected Node"],4:["New Parent Node","Selected Profit Center"],5:["Selected Profit Center"],6:["Deleted Node"],7:["Parent Node","New Person Responsible"]}[r]||[];for(const H of a)for(const f of d)if(!H[f]||H[f].toString().trim()==="")return{isValid:!1,message:`Please fill all required fields in ${h.label} table. Missing: ${f}`};return{isValid:!0,message:""}},Se=r=>{const h=xe(i);if(!h.isValid){s(`${h.message}`,"error");return}D(r)};return Q(Z,{sx:{display:"flex",flexDirection:"column",height:"100%"},children:[e(Z,{sx:{width:"100%",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(gs,{nonLinear:!0,activeStep:i,sx:{mb:2},alternativeLabel:!0,children:Je.map((r,h)=>e(Ps,{children:e(Ss,{onClick:()=>Se(h),children:r.label})},r.value))})}),e(Z,{sx:{flexGrow:1,overflowY:"auto",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Ge,{container:!0,children:e(Ge,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Cs},children:Q(Z,{sx:{width:"100%",height:"auto",display:"flex",flexDirection:"column",justifyContent:"space-between"},children:[e(Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:1,mb:1},children:e(Z,{children:e(Ue,{variant:"contained",disabled:De,onClick:fe,sx:{ml:1},children:"Add Row"})})}),_e.columns.length>0&&e(uo,{width:"100%",rowHeight:70,rows:_e.rows,columns:_e.columns,title:`This table shows the ${_e.title}`,rowCount:_e.rows.length,page:R,pageSize:C,onPageChange:we,onPageSizeChange:ue,getRowIdValue:"Id",hideFooter:!0,checkboxSelection:!1,callback_onRowDoubleClick:r=>{console.log("params",r.row)},onRowsSelectionHandler:r=>pe(r,i),stopPropagation_Column:"action",status_onRowDoubleClick:!0,showCustomNavigation:!0})]})})})})]})},wr=u=>{var me,ne,Pe,F;const i=Xe(),D=new URLSearchParams(i.search),R=D.get("RequestType"),p=D.get("reqBench"),C=q(G=>{var W;return(W=G.hierarchyData.requestHeaderData)==null?void 0:W.RequestStatus}),m=p&&!Tt.includes(C)||R===((me=O)==null?void 0:me.CHANGE_WITH_UPLOAD),{showTree:s,blurLoading:t,openSnackBar:y,alertMsg:M,alertType:L,filteredButtons:B,requestorPayload:j,loadForFetching:z,initialNodeData:Ee,handleButtonClick:De,handleSnackBarClose:de}=Ft(),Ie=G=>Array.isArray(G)&&G.length===1&&typeof G[0]=="object"&&G[0].code&&G[0].desc?e(st,{component:"span",variant:"body1",children:G[0].code}):typeof G=="string"?G:JSON.stringify(G),Ce=({requestorPayload:G,renderValue:W})=>!G||!Object.keys(G).length?null:e(Z,{sx:{backgroundColor:tt.primary.whiteSmoke,px:2,py:1,borderBottom:`1px solid ${tt.primary.whiteSmoke}`,borderRadius:"5px",mb:2},children:e(Z,{sx:{display:"flex",flexWrap:"wrap",gap:3},children:Object.keys(G).map(J=>Q(Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[Q(st,{variant:"body1",sx:{fontWeight:700,color:tt.primary.grey},children:[J," :"]}),e(Z,{sx:{display:"flex",alignItems:"center"},children:W(G[J])})]},J))})});return Q("div",{children:[Q(Ge,{container:!0,sx:{height:"80vh",overflow:"hidden"},children:[Q(Ge,{item:!0,md:R===((ne=O)==null?void 0:ne.CHANGE_WITH_UPLOAD)?6:12,sx:{overflowY:"auto",height:"100%",p:2,pr:1,borderRight:"1px solid #e0e0e0",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:[e(Ce,{requestorPayload:j,renderValue:Ie}),s&&e(fr,{initialRawTreeData:Ee,editmode:!m,object:"PCG",moduleObject:(Pe=Ae)==null?void 0:Pe.PC})]}),R===((F=O)==null?void 0:F.CHANGE_WITH_UPLOAD)&&e(Ge,{item:!0,md:6,sx:{overflowY:"auto",height:"100%",p:2,pl:1,scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}},children:e(Lr,{})})]}),e(mt,{blurLoading:z||t}),e(kt,{openSnackBar:y,alertMsg:M,handleSnackBarClose:de,alertType:L}),e(fo,{})]})},An=()=>{var Ht,Mt,$t,vt,Ut,Gt,Wt,jt,Yt,zt,Kt,Xt,Qt,Vt,Zt;const{toPDF:u,targetRef:i}=po({filename:"my-component.pdf"}),{t:D}=ho(),{customError:R}=No(),[p,C]=n.useState(!1);n.useState([]);const[m,s]=n.useState(!1),[t,y]=n.useState([]),[M,L]=n.useState(!1),[B,j]=n.useState(!1),[z,Ee]=n.useState(""),[De,de]=n.useState(!1),[Ie,Ce]=n.useState([]),[me,ne]=n.useState(!1),[Pe,F]=n.useState(!1),[G,W]=n.useState(""),[J,Te]=n.useState(),[we,ue]=n.useState(""),[fe,P]=n.useState(!1),[pe,w]=n.useState(""),[_e,xe]=n.useState(!1),[Se,r]=n.useState("success"),[h,a]=n.useState(!1),[l,d]=n.useState(!1),[H,f]=n.useState(!1),[o,E]=n.useState(!1),[I,T]=n.useState(""),[$,A]=n.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),_=ot(),Re=q(b=>b.applicationConfig),v=q(b=>b.payload.payloadData),[qe,ae]=n.useState(!1),[ke,je]=n.useState([]),Oe=q(b=>{var x;return(x=b.request.requestHeader)==null?void 0:x.requestId}),S=q(b=>{var x;return(x=b.userManagement)==null?void 0:x.taskData}),ye=q(b=>b.hierarchyData),oe=qt(),[N,Qe]=n.useState(!0),ie=q(b=>b.request.tabValue),Le=q(b=>b.request.requestHeader),{buttonsLoading:gt,filteredButtons:rt,handleButtonClick:he,showWfLevels:nt,wfLevels:at}=Ft({setSuccessDialogOpen:E,setDialogData:A,selectedLevel:I}),it=["Request Header","Hierarchy Tree","Attachments & Remarks","Preview"],[Pt,g]=n.useState([!1]),V=b=>{_(Ze(b))},ee=Xe(),c=ee.state,U=new URLSearchParams(ee.search.split("?")[1]).get("RequestId"),He=new URLSearchParams(ee.search),re=He.get("RequestId"),Ne=He.get("RequestType"),Me=He.get("reqBench"),lt={requestId:re||"",isChild:!!((Ht=ye==null?void 0:ye.requestHeaderData)!=null&&Ht.childRequestId)},ct=()=>{ne(!0)},St=()=>{ne(!1)},Os=()=>{F(!0)},bs=b=>{F(b)},As=()=>{we==="success"?oe("/requestBench"):St()},Is=()=>{s(!0)},_s=b=>{const x=`/${et}/node/getTreeModificationHistory?requestId=${b}`;return new Promise((K,X)=>{Be(x,"get",Y=>{var le;(Y==null?void 0:Y.statusCode)===zo.STATUS_200?K(((le=Y==null?void 0:Y.body)==null?void 0:le.Records)||[]):K([])},Y=>{R(Y),X(Y)})})},ys=b=>{let x="";Ne===O.CREATE_WITH_UPLOAD?x="getAllHierarchyNodeFromExcel":Ne===O.CHANGE_WITH_UPLOAD&&(x="getAllHierarchyNodeFromExcelForChange"),w("Initiating Excel Upload"),P(!0);const K=new FormData;[...b].forEach(te=>K.append("files",te)),K.append("requestId",U||"");const X=te=>{var Y,le;te.statusCode===200?(de(!1),P(!1),w(""),oe((Y=Fe)==null?void 0:Y.REQUEST_BENCH)):(de(!1),P(!1),w(""),oe((le=Fe)==null?void 0:le.REQUEST_BENCH))},be=te=>{var Y;P(!1),w(""),oe((Y=Fe)==null?void 0:Y.REQUEST_BENCH)};Be(`/${et}/massAction/${x}`,"postformdata",X,be,K)},Ls=async(b=null)=>new Promise((x,K)=>{P(!0);const X=b||re,be=Wo(Nt.CURRENT_TASK,!0,{}),te=Ne||(S==null?void 0:S.ATTRIBUTE_2)||(be==null?void 0:be.ATTRIBUTE_2),Y=(c==null?void 0:c.childRequestIds)!=="Not Available";let le=Me?{parentId:Y?"":c==null?void 0:c.requestId,massChangeId:Y&&(te===O.CHANGE||te===O.CHANGE_WITH_UPLOAD)?X:"",massCreationId:Y&&(te===O.CREATE||te===O.CREATE_WITH_UPLOAD)?X:""}:{parentId:"",massChangeId:te===O.CHANGE||te===O.CHANGE_WITH_UPLOAD?X:"",massCreationId:te===O.CREATE||te===O.CREATE_WITH_UPLOAD?X:""};const Ve=async Ye=>{var Rt,ut,ft,Jt,es,ts,ss,os,rs,ns,as,is,ls,cs;try{P(!1);const k=(Ye==null?void 0:Ye.body)||{},{ControllingArea:Ot,ParentNode:bt,ParentDesc:ds,RequestType:pt,HierarchyTree:Bs,ChangeLogId:Hs,ErrorLogId:Ms,GeneralInformation:$s,Torequestheaderdata:vs,...Us}=k,Gs={ReqCreatedBy:(Rt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Rt.ReqCreatedBy,RequestStatus:(ut=k==null?void 0:k.Torequestheaderdata)==null?void 0:ut.RequestStatus,Region:(ft=k==null?void 0:k.Torequestheaderdata)==null?void 0:ft.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Jt=k==null?void 0:k.Torequestheaderdata)==null?void 0:Jt.RequestType,RequestDesc:(es=k==null?void 0:k.Torequestheaderdata)==null?void 0:es.RequestDesc,RequestPriority:(ts=k==null?void 0:k.Torequestheaderdata)==null?void 0:ts.RequestPriority,LeadingCat:(ss=k==null?void 0:k.Torequestheaderdata)==null?void 0:ss.LeadingCat,RequestId:(os=k==null?void 0:k.Torequestheaderdata)==null?void 0:os.RequestId,TemplateName:(rs=k==null?void 0:k.Torequestheaderdata)==null?void 0:rs.TemplateName};let At={};(pt===((ns=O)==null?void 0:ns.CREATE)||pt===((as=O)==null?void 0:as.CREATE_WITH_UPLOAD))&&(At={"Controlling Area":Ot,"Profit Center Group":bt,"Profit Center Group Description":ds}),(pt===((is=O)==null?void 0:is.CHANGE)||pt===((ls=O)==null?void 0:ls.CHANGE_WITH_UPLOAD))&&(At={"Controlling Area":Ot,"Profit Center Group":bt}),_(ps(At)),_(fs(Gs));const Ws={ControllingArea:Ot,ParentNode:bt,ParentDesc:ds,ChangeLogId:Hs,ErrorLogId:Ms,GeneralInformation:$s,treeData:[Bs],requestHeaderData:{...vs,childRequestId:(cs=k==null?void 0:k.ToChildHeaderdata)==null?void 0:cs.RequestId},...Us};_(Yo({data:Ws}));const js=await _s(re);_(Rs(js||[])),x()}catch(k){P(!1),R(wt.ERROR_GET_DISPLAY_DATA),K(k)}},dt=Ye=>{P(!1),R(wt.ERROR_FETCHING_DATA),K(Ye)};Be(`/${et}/data/displayHierarchyTreeNodeStructureFromDb`,"post",Ve,dt,le)});n.useEffect(()=>{var K;let b=(K=Jo)==null?void 0:K[ie];const x=Eo(rt,b);je(x)},[ie,rt]),n.useEffect(()=>((async()=>{var x,K,X;re?(await Ls(re),(Ne===O.CHANGE_WITH_UPLOAD&&!((x=c==null?void 0:c.parentNode)!=null&&x.length)||Ne===O.CREATE_WITH_UPLOAD&&!((K=c==null?void 0:c.parentNode)!=null&&K.length))&&((c==null?void 0:c.reqStatus)===ze.DRAFT||(c==null?void 0:c.reqStatus)===ze.UPLOAD_FAILED)?(_(Ze(0)),L(!1),j(!1)):(Ne===O.CREATE||Ne===O.CHANGE)&&!((X=c==null?void 0:c.parentNode)!=null&&X.length)&&Me?(_(Ze(0)),L(!1),j(!0)):(_(Ze(1)),L(!0),j(!0)),d(!0)):_(Ze(0))})(),()=>{_(Do([])),_(Et({})),_(Co()),_(mo()),_(fs({data:{}})),_(To([])),_(go([])),_(ps({})),_(Po()),_(So([])),_(Ro([])),_(Oo({})),_(bo()),hs(Nt.CURRENT_TASK),hs(Nt.ROLE),_(Ao())}),[U,_]),n.useEffect(()=>(ws(),_(Io([])),_(_o({keyName:"Region",data:yo})),()=>{_(ms({}))}),[]),n.useEffect(()=>{M&&g([!0])},[M]),n.useEffect(()=>{Ee(Lo("PCG")),wo(Nt.MODULE,Ae.PCG)},[]);const ws=()=>{let b={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};C(!0);const x=X=>{var be,te,Y,le;if(C(!1),X.statusCode===200){let Ve=(te=(be=X==null?void 0:X.data)==null?void 0:be.result[0])==null?void 0:te.MDG_ATTACHMENTS_ACTION_TYPE,dt=[];Ve==null||Ve.map((Rt,ut)=>{var ft={id:ut};dt.push(ft)}),y(dt);const Ye=((le=(Y=X==null?void 0:X.data)==null?void 0:Y.result[0])==null?void 0:le.MDG_ATTACHMENTS_ACTION_TYPE)||[];Ce(Ye)}},K=X=>{};Re.environment==="localhost"?Be(`/${Es}/rest/v1/invoke-rules`,"post",x,K,b):Be(`/${Es}/v1/invoke-rules`,"post",x,K,b)},xs=()=>{var X,be;const b=(be=(X=Ke)==null?void 0:X.EXCEL)==null?void 0:be.EXPORT_HIERARCHY_EXCEL;w("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),P(!0);const x=te=>{const Y=URL.createObjectURL(te),le=document.createElement("a");le.href=Y,le.setAttribute("download",`${re}_Data Export.xlsx`),document.body.appendChild(le),le.click(),document.body.removeChild(le),URL.revokeObjectURL(Y),P(!1),w(""),xe(!0),Te(`${re}_Data Export.xlsx has been exported successfully.`),r("success"),qs()},K=()=>{};Be(`/${jo}${b}?reqId=${re}&attachmentType=Export_Excel`,"getblobfile",x,K)},qs=()=>{a(!0)},ks=()=>{a(!1)},Fs=()=>{var b,x,K;U&&!Me?oe((b=Fe)==null?void 0:b.MY_TASK):Me?oe((x=Fe)==null?void 0:x.REQUEST_BENCH):!U&&!Me&&oe((K=Fe)==null?void 0:K.MASTER_DATA_PCG)},Bt=()=>{f(!1)};return Q(Ns,{children:[N&&e(mt,{blurLoading:gt||fe,loaderMessage:pe}),Q(Z,{sx:{padding:2},children:[Q(Ge,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Oe||U?Q(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(ar,{sx:{fontSize:"1.5rem"}}),D("Request Header ID"),":"," ",e("span",{children:Oe?(Le==null?void 0:Le.requestPrefix)+""+(Le==null?void 0:Le.requestId):U})]}):e("div",{style:{flex:1}}),ie===1&&Q(Z,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(Ue,{variant:"outlined",size:"small",title:"Download Error Report",disabled:!re,onClick:()=>ae(!0),color:"primary",children:e(Ho,{sx:{padding:"2px"}})}),e(Ue,{variant:"outlined",disabled:!1,size:"small",onClick:Os,title:"Change Log",children:e(ir,{sx:{padding:"2px"}})}),(Ne===((Mt=O)==null?void 0:Mt.CREATE_WITH_UPLOAD)||Ne===(($t=O)==null?void 0:$t.CHANGE_WITH_UPLOAD))&&e(Ue,{variant:"outlined",disabled:!re,size:"small",onClick:xs,title:"Export Excel",children:e(lr,{sx:{padding:"2px"}})})]}),Pe&&e(rr,{open:!0,closeModal:bs,requestId:Oe||U,requestType:v==null?void 0:v.RequestType,module:(vt=Ae)==null?void 0:vt.PCG}),ie===3&&e(Z,{sx:{display:"flex",justifyContent:"flex-end"},children:e(Ue,{variant:"outlined",color:"primary",startIcon:e(nr,{}),onClick:u,children:D("Export Preview")})})]}),e(ve,{onClick:()=>{var b,x;if(Me&&!((b=Tt)!=null&&b.includes(v==null?void 0:v.RequestStatus))){oe((x=Fe)==null?void 0:x.REQUEST_BENCH);return}f(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:"Back",children:e(Mo,{sx:{fontSize:"25px",color:"#000000"}})}),e(gs,{nonLinear:!0,activeStep:ie,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:it.map((b,x)=>e(Ps,{children:e(Ss,{color:"error",disabled:x===1&&!M||x===2&&!B||x===3&&!M&&!B,onClick:()=>V(x),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:b})})},b))}),e($o,{dialogState:me,openReusableDialog:ct,closeReusableDialog:St,dialogTitle:G,dialogMessage:J,handleDialogConfirm:St,dialogOkText:"OK",handleOk:As,dialogSeverity:we}),e(hr,{dialogState:qe,closeReusableDialog:()=>ae(!1),module:(Ut=Ae)==null?void 0:Ut.PCG,isHierarchyCheck:!0}),e(mt,{blurLoading:fe,loaderMessage:pe}),ie===0&&Q(Ns,{children:[e(Nr,{setIsSecondTabEnabled:L,setIsAttachmentTabEnabled:j,requestStatus:c!=null&&c.reqStatus?c==null?void 0:c.reqStatus:ze.ENABLE_FOR_FIRST_TIME,downloadClicked:m,setDownloadClicked:s}),(Ne===O.CHANGE_WITH_UPLOAD||Ne===O.CREATE_WITH_UPLOAD)&&((c==null?void 0:c.reqStatus)==ze.DRAFT&&!((Gt=c==null?void 0:c.material)!=null&&Gt.length)||(c==null?void 0:c.reqStatus)==ze.UPLOAD_FAILED)&&e(cr,{handleDownload:Is,setEnableDocumentUpload:de,enableDocumentUpload:De,handleUploadMaterial:ys}),((v==null?void 0:v.RequestType)===((Wt=O)==null?void 0:Wt.CHANGE)||(v==null?void 0:v.RequestType)===((jt=O)==null?void 0:jt.CHANGE_WITH_UPLOAD))&&!re&&(v==null?void 0:v.DirectAllowed)!=="X"&&(v==null?void 0:v.DirectAllowed)!==void 0&&Q(st,{sx:{fontSize:"13px",fontWeight:"500",color:(zt=(Yt=tt)==null?void 0:Yt.error)==null?void 0:zt.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[Q(Z,{component:"span",sx:{fontWeight:"bold"},children:[D("Note"),":"]})," ","You are not authorized to Tcode"," ",Q(Z,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),ie===1&&e(wr,{setIsAttachmentTabEnabled:!0,setCompleted:g,downloadClicked:m,setDownloadClicked:s}),ie===2&&e(Xo,{requestStatus:c!=null&&c.reqStatus?c==null?void 0:c.reqStatus:ze.ENABLE_FOR_FIRST_TIME,attachmentsData:Ie,requestIdHeader:Oe||U,pcNumber:z,module:(Kt=Ae)==null?void 0:Kt.PCG,artifactName:xo.PCG}),ie===3&&e(Z,{ref:i,sx:{width:"100%",overflow:"auto"},children:e(Qo,{requestStatus:c!=null&&c.reqStatus?c==null?void 0:c.reqStatus:ze.ENABLE_FOR_FIRST_TIME,module:(Xt=Ae)==null?void 0:Xt.PCG,payloadData:ye,payloadForPreviewDownloadExcel:lt})}),ie!=0&&e(Zo,{handleSaveAsDraft:he,handleSubmitForReview:he,handleSubmitForApprove:he,handleSendBack:he,handleCorrection:he,handleRejectAndCancel:he,handleValidateAndSyndicate:he,filteredButtons:ke,moduleName:(Qt=Ae)==null?void 0:Qt.PCG,isHierarchy:!0,activeTab:ie,showWfLevels:nt,selectedLevel:I,workFlowLevels:at,setSelectedLevel:T})]}),e(kt,{openSnackBar:h,alertMsg:J,alertType:Se,handleSnackBarClose:ks}),e(Vo,{open:o,onClose:()=>E(!1),title:$.title,message:$.message,subText:$.subText,buttonText:$.buttonText,redirectTo:$.redirectTo}),H&&Q(qo,{isOpen:H,titleIcon:e(vo,{size:"small",sx:{color:(Zt=(Vt=tt)==null?void 0:Vt.secondary)==null?void 0:Zt.amber,fontSize:"20px"}}),Title:"Warning",handleClose:Bt,children:[e(Go,{sx:{mt:2},children:Uo.LEAVE_PAGE_MESSAGE}),Q(ko,{children:[e(Ue,{variant:"outlined",size:"small",sx:{...Fo},onClick:Bt,children:D("No")}),e(Ue,{variant:"contained",size:"small",sx:{...Bo},onClick:Fs,children:D("Yes")})]})]})]})};export{An as default};
