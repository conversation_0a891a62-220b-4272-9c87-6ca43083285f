import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Chip, IconButton, CircularProgress, Switch, FormControlLabel, Button } from "@mui/material";
import {
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  InsertDriveFile as FileIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Delete as DeleteIcon,
} from "@mui/icons-material";
import TableViewIcon from '@mui/icons-material/TableView';
import AttachFileIcon from "@mui/icons-material/AttachFile";
import AddIcon from "@mui/icons-material/Add";
import RefreshIcon from '@mui/icons-material/Refresh';
import ReusableTable from "../../Common/ReusableTable";
import ReusableSnackBar from "../../Common/ReusableSnackBar";
import UploadDialog from "../../Common/ReusableAttachmentAndComments/UploadDialog";
import useLang from "@hooks/useLang";
import {
  outermostContainer,
  outermostContainer_Information,
} from "../../Common/commonStyles";
import { colors } from "@constant/colors";
import { destination_AI } from "../../../destinationVariables";
import { END_POINTS } from "../../../constant/apiEndPoints"
import { doAjax } from "../../Common/fetchService";
import { useSelector } from "react-redux";
import { MatDownload, MatView } from "@components/DocumentManagement/UtilDoc";
import moment from "moment";
import { API_CODE, DOC_SNACKBAR_MESSAGES } from "@constant/enum";

const getFileIcon = (fileName) => {
  const extension = fileName?.split(".")?.pop()?.toLowerCase() || "";
  const iconProps = { fontSize: "small", sx: { mr: 1 } };

  switch (extension) {
    case "xlsx":
    case "xls":
    case "csv":
      return <TableViewIcon sx={{ color: colors?.secondary?.dark }} />;
    case "pdf":
      return <PdfIcon {...iconProps} sx={{ color: colors?.error?.dark }} />;
    case "doc":
    case "docx":
      return <DocumentIcon {...iconProps} sx={{ color: colors?.primary?.lightPlus }} />;
    case "ppt":
    case "pptx":
      return <DocumentIcon {...iconProps} sx={{ color: colors?.secondary?.amber }} />;
    default:
      return <FileIcon {...iconProps} sx={{ color: colors?.secondary?.grey }} />;
  }
};

const Upload = ({
  onFilesChange = () => {},
  maxFiles = 5,
  maxFileSize = 500, // MB
  acceptedTypes = ".jpeg, .jpg, .xls, .xlsx, .docx, .pdf",
  disabled = false,
  title = ""
}) => {
  const [attachments, setAttachments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [DownloadLoader, setDownloadLoader] = useState({});
  const userData = useSelector((state) => state.userManagement.userData);
  const appSettings = useSelector((state) => state.appSettings);
  const { t } = useLang();

  const getAllFiles = async () => {
    return new Promise((resolve, reject) => {
      const hSuccess=(data) => {
        resolve(data);
        return;
      };
      const hError=(error) => {
        resolve(error);
        return;
      };
      doAjax(
        `/${destination_AI}${END_POINTS?.DOCUMENT_CONFIGURATION_APIS?.GET_FILES_LIST_API}?adminEmail=${userData?.emailId}`, 
        "get", 
        hSuccess, 
        hError,
      );
    })
  }

  const uploadFiles = async (files) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append(`file`, file);
      });
      const hSuccess=(data) => {
        resolve(data)
        return;
      };
      const hError=(error) => {
        resolve(data)
        return;
      };
      doAjax(
        `/${destination_AI}${END_POINTS?.DOCUMENT_CONFIGURATION_APIS?.UPLOAD_FILES_API}`, 
        "postformdata",
        hSuccess,
        hError,
        formData
      );
    })
  }

  const deleteFile = async (fileId) => {
    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        resolve(data)
        return;
      }
      const hError = (error) => {
        resolve(error)
        return;
      }
      doAjax(`/${destination_AI}${END_POINTS?.DOCUMENT_CONFIGURATION_APIS?.DELETE_FILE_API}?documentId=${fileId}`,
          "get",
          hSuccess, 
          hError
      );
    })
  }

  const updateFileVisibility = async (fileId, isVisible) => {
    return new Promise((resolve, reject) => {
      const hSuccess = (data) => {
        resolve(data)
        return;
      }
      const hError = (error) => {
        resolve(error)
        return;
      }
      doAjax(`/${destination_AI}${END_POINTS?.DOCUMENT_CONFIGURATION_APIS?.UPDATE_VISIBILITY_API}/${fileId}?visibility=${isVisible}`,
      "post",
        hSuccess,
        hError,
      );
    })
  }

  const handleSnackbarOpen = (message, type = "success") => {
    setMessageDialogMessage(message);
    setAlertType(type);
    setShowSnackbar(true);
  };

  const handleSnackbarClose = () => {
    setShowSnackbar(false);
  };

  // Fetch files on component mount
  useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    setLoading(true);
    try {
      const result = await getAllFiles();
      if (result?.responseMessage?.status === "Success") {
        const transformedFiles = result.documentDetailDtoList.map(doc => ({
          id: doc.documentId,
          docType: doc.fileType,
          docName: doc.fileName,
          uploadedOn: moment(doc.docCreationDate).format(appSettings.dateFormat),
          uploadedBy: doc.createdBy,
          attachmentType: doc.attachmentType,
          documentViewUrl: doc.documentViewUrl,
          visibility: doc.visibility || false,
        }));
        setAttachments(transformedFiles);
        onFilesChange(transformedFiles);
      } else {
        handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.FETCH_ERROR, "error");
      }
    } catch (error) {
      handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.FETCH_ERROR, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleFilesAdded = async (newFiles) => {
    setLoading(true);
    try {
      const uploadResult = await uploadFiles(newFiles);
      
      if (uploadResult?.statusCode === API_CODE?.STATUS_202) {
        // await fetchFiles();
        handleSnackbarOpen(
          DOC_SNACKBAR_MESSAGES.FILES.UPLOAD_SUCCESS(newFiles.length), 
          "success"
        );
      } else {
        handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.UPLOAD_FAILED, "error");
      }
    } catch (error) {
      handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.UPLOAD_ERROR, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleFileDelete = async (fileId) => {
    setLoading(true);
    try {
      const result = await deleteFile(fileId);
      
      if (result.statusCode === API_CODE?.STATUS_200) {
        const updatedAttachments = attachments.filter(att => att.id !== fileId);
        setAttachments(updatedAttachments);
        onFilesChange(updatedAttachments);
        handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.DELETE_SUCCESS, "success");
      } else {
        handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.DELETE_FAILED, "error");
      }
    } catch (error) {
      handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.DELETE_ERROR, "error");
    } finally {
      setLoading(false);
    }
  };

  const handleVisibilityToggle = async (fileId, currentVisibility) => {
    const newVisibility = !currentVisibility;
    
    try {
      const result = await updateFileVisibility(fileId, newVisibility);
      
      if (result?.statusCode === API_CODE?.STATUS_200) {
        const updatedAttachments = attachments.map(att => 
          att.id === fileId ? { ...att, visibility: newVisibility } : att
        );
        setAttachments(updatedAttachments);
        onFilesChange(updatedAttachments);
        handleSnackbarOpen(
          DOC_SNACKBAR_MESSAGES.FILES.VISIBILITY_SUCCESS(newVisibility),
          "success"
        );
      } else {
        handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.VISIBILITY_FAILED, "error");
      }
    } catch (error) {
      handleSnackbarOpen(DOC_SNACKBAR_MESSAGES.FILES.VISIBILITY_ERROR, "error");
    }
  };

  const attachmentColumns = [
    {
      field: "id",
      headerName: "Document ID",
      flex: 1.2,
      hideable: false,
      hidden: true,
    },
    {
      field: "attachmentType",
      headerName: t("Attachment Type"),
      flex: 1.5,
      renderCell: (params) => (
        <Chip
          label={params.value}
          size="small"
          sx={{
            backgroundColor: colors?.reportTile.lightBlue, 
            color: colors.primary.lightPlus,
            fontWeight: "medium",
          }}
        />
      ),
    },
    {
      field: "docName",
      headerName: t("Document Name"),
      flex: 2,
      renderCell: (params) => (
        <Stack direction="row" spacing={1} alignItems="center">
        {getFileIcon(params.value)} 
        <Typography variant="body2">{params.value}</Typography>
      </Stack>
      ),
    },
    {
      field: "uploadedOn",
      headerName: t("Uploaded On"),
      flex: 1,
      align: "center",
      headerAlign: "center",
    },
    {
      field: "visibility",
      headerName: "Visibility",
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (cellValues) => (
        <FormControlLabel
          control={
            <Switch
              checked={cellValues.value}
              onChange={() => handleVisibilityToggle(cellValues.row.id, cellValues.value)}
              disabled={disabled || loading}
              size="small"
              color="primary"
            />
          }
          label={
            <Stack direction="row" alignItems="center" spacing={0.5}>
              {cellValues.value ? (
                <VisibilityIcon fontSize="small" sx={{ color: colors?.secondary?.green }} />
              ) : (
                <VisibilityOffIcon fontSize="small" sx={{ color: colors?.error?.dark }} />
              )}
              <Typography variant="caption">
                {cellValues.value ? "Visible" : "Hidden"}
              </Typography>
            </Stack>
          }
          sx={{ margin: 0 }}
        />
      ),
    },
    {
      field: "action",
      headerName: t("Action"),
      sortable: false,
      filterable: false,
      align: "center",
      headerAlign: "center",
      flex: 1,
      renderCell: (
        cellValues
      ) => (
        <Stack direction="row" spacing={0}>
          <IconButton
          size="small"
          sx={{
            color: colors.icon.matView,
            "&:hover": { backgroundColor: "rgba(2, 136, 209, 0.1)" },
          }}
        >
          <MatView 
              index={cellValues.row.id} 
              name={cellValues?.row?.docName || cellValues?.row?.fileName}
              documentViewUrl={cellValues.row.documentViewUrl}
            />
        </IconButton>
          <IconButton
            size="small"
            sx={{
              color: colors?.icon.matDownload,
              "&:hover": { backgroundColor: "rgba(46, 125, 50, 0.1)" },
            }}
          >
              <MatDownload
              index={cellValues.row.id}
              name={cellValues?.row?.docName || cellValues?.row?.fileName}

            />
        
          </IconButton>
          <IconButton
            size="small"
            onClick={() => handleFileDelete(cellValues.row.id)}
            disabled={disabled || loading}
            sx={{
              color: colors?.error?.dark,
              "&:hover": { backgroundColor: "rgba(211, 47, 47, 0.1)" },
            }}
          >            
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Stack>
      ),
    },
  ]

  return (
    <div>
      <Grid container spacing={2} sx={{ padding: '35px', pt: "40px" }}>
        <Grid container sx={outermostContainer_Information}>
          <Grid item md={5} xs={12}>
            <Typography variant="h3">
              <strong>{t("Documents Configuration")}</strong>
            </Typography>
            <Typography variant="body2" color={colors.secondary.grey}>
              {t("This view displays the list of Documents uploaded for AI")}
            </Typography>
          </Grid>
          <Grid
            item
            md={7}
            xs={12}
            sx={{
              display: "flex",
            }}
          >
            <Grid
              container
              direction="row"
              justifyContent="flex-end"
              alignItems="center"
              spacing={0}
              mt={0}
            >
            </Grid>
          </Grid>
        </Grid>
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: colors?.primary?.white,
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: `1px solid ${colors?.primary?.white}`,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            paddingRight: "16px",
            mt: "20px"
          }}
        >
          <Grid container sx={{ display: "flex", justifyContent: "space-between", flexDirection: 'row', alignItems: 'center' }}>
            <Button
              className='refreshButtonCD'
              variant="outlined"
              startIcon={<RefreshIcon />}
              sx={{
                textTransform: "capitalize",
                borderRadius: "5px",
                marginLeft: "15px"
              }}
              onClick={fetchFiles}
            >
              {"Refresh"}
            </Button>
            {!disabled && (
              <UploadDialog
                onFilesAdded={handleFilesAdded}
                maxFiles={maxFiles}
                maxFileSize={maxFileSize}
                acceptedTypes={acceptedTypes}
                currentFileCount={attachments.length}
                loading={loading}
                
              />
            )}
          </Grid>

          {loading && attachments.length === 0 ? (
            <Stack alignItems="center" spacing={2} sx={{ py: 4 }}>
              <CircularProgress />
              <Typography variant="body2" color={colors?.secondary?.grey}>
                Loading files...
              </Typography>
            </Stack>
          ) : attachments.length > 0 ? (
            <Grid sx={{padding: "15px" }}>
            <ReusableTable
              width="100%"
              rows={attachments}
              columns={attachmentColumns}
              hideFooter={false}
              getRowIdValue="id"
              autoHeight={true}
              disableSelectionOnClick={true}
              stopPropagation_Column="action"
              title={t("Documents Uploaded")}
            />
            </Grid>
          ) : (
            <Stack alignItems="center" spacing={2} sx={{ py: "25vh" }}>
              <AttachFileIcon sx={{ fontSize: 40, color: colors?.primary?.whiteSmoke, transform: 'rotate(90deg)' }} />
              <Typography variant="body2" color={colors?.secondary?.grey}>
                {t("No Files Added")}
              </Typography>
            </Stack>
          )}
        </Grid>
      </Grid>

      {showSnackbar && (
        <ReusableSnackBar
          openSnackBar={showSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackbarClose}
        />
      )}
    </div>
  );
};

export default Upload;