import { Tooltip, Icon<PERSON>utton } from "@mui/material";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import EditableTextField from "@profitCenterGroup/EditableTextFieldPCG";

// Column definitions for different tables
export const getColumnsNewNodes = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Parent Node",
    headerName: "Parent Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Parent Node"]}
        placeholder="Enter Parent Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Parent Node"
        tableKey="NEW NODES"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "New Node",
    headerName: "New Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["New Node"]}
        placeholder="Enter New Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="New Node"
        tableKey="NEW NODES"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={true}
        duplicateCheckType="node"
      />
    ),
  },
  {
    field: "Description",
    headerName: "Description",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Description"]}
        placeholder="Enter Description"
        maxLength={40}
        rowId={params.row.Id}
        fieldName="Description"
        tableKey="NEW NODES"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={true}
        duplicateCheckType="desc"
      />
    ),
  },
  {
    field: "Person Responsible",
    headerName: "Person Responsible",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Person Responsible"]}
        placeholder="Enter Person Responsible"
        maxLength={40}
        rowId={params.row.Id}
        fieldName="Person Responsible"
        tableKey="NEW NODES"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 0)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsDescChange = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", type: "text", hide: true },
  {
    field: "Parent Node",
    headerName: "Parent Node",
    type: "text",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Parent Node"]}
        placeholder="Enter Parent Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Parent Node"
        tableKey="DESCRIPTIONS"
        fetchOldData={true}
        fetchFunction={fetchOldDescriptionForNode}
        oldDataField="Old Description"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Old Description",
    headerName: "Old Description",
    flex: 1,
    editable: false,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Old Description"] || ""}
      </div>
    ),
  },
  {
    field: "New Description",
    headerName: "New Description",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["New Description"]}
        placeholder="Enter New Description"
        maxLength={40}
        rowId={params.row.Id}
        fieldName="New Description"
        tableKey="DESCRIPTIONS"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={true}
        duplicateCheckType="desc"
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    hide: false,
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 1)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsAddPC = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", type: "text", hide: true },
  {
    field: "Node",
    headerName: "Node",
    type: "text",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Node"]}
        placeholder="Enter Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Node"
        tableKey="PROFIT CENTERS"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Profit Center",
    headerName: "Profit Center",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Profit Center"]}
        placeholder="Enter Profit Center"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Profit Center"
        tableKey="PROFIT CENTERS"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    hide: false,
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 2)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsMoveNode = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Old Parent Node",
    headerName: "Old Parent Node",
    flex: 1,
    editable: false,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Old Parent Node"] || ""}
      </div>
    ),
  },
  {
    field: "New Parent Node",
    headerName: "New Parent Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["New Parent Node"]}
        placeholder="Enter New Parent Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="New Parent Node"
        tableKey="MOVE NODE"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Selected Node",
    headerName: "Selected Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Selected Node"]}
        placeholder="Enter Selected Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Selected Node"
        tableKey="MOVE NODE"
        fetchOldData={true}
        fetchFunction={fetchOldParentForNode}
        oldDataField="Old Parent Node"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 3)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsMovePC = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Old Parent Node",
    headerName: "Old Parent Node",
    flex: 1,
    editable: false,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Old Parent Node"] || ""}
      </div>
    ),
  },
  {
    field: "New Parent Node",
    headerName: "New Parent Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["New Parent Node"]}
        placeholder="Enter New Parent Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="New Parent Node"
        tableKey="MOVE PROFIT CENTER"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Selected Profit Center",
    headerName: "Selected Profit Center",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Selected Profit Center"]}
        placeholder="Enter Profit Center"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Selected Profit Center"
        tableKey="MOVE PROFIT CENTER"
        fetchOldData={true}
        fetchFunction={fetchOldParentForObject}
        oldDataField="Old Parent Node"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 4)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsRemovePC = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Parent Node",
    headerName: "Parent Node",
    flex: 1,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Parent Node"] || ""}
      </div>
    ),
  },
  {
    field: "Selected Profit Center",
    headerName: "Selected Profit Center",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Selected Profit Center"]}
        placeholder="Enter Profit Center"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Selected Profit Center"
        tableKey="REMOVE PROFIT CENTER"
        fetchOldData={true}
        fetchFunction={fetchOldParentForObject}
        oldDataField="Parent Node"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 5)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsDeleteNode = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Parent Node",
    headerName: "Parent Node",
    flex: 1,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Parent Node"] || ""}
      </div>
    ),
  },
  {
    field: "Deleted Node",
    headerName: "Deleted Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Deleted Node"]}
        placeholder="Enter Node to Delete"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Deleted Node"
        tableKey="DELETE NODE"
        fetchOldData={true}
        fetchFunction={fetchOldParentForNode}
        oldDataField="Parent Node"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 6)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];

export const getColumnsChangePR = (
  handleDeleteRow,
  fetchOldDescriptionForNode,
  fetchOldParentForNode,
  fetchOldParentForObject,
  addToChangeLog,
  updateDuplicateCheckLists,
  disableCheck
) => [
  { field: "Id", headerName: "ID", hide: true },
  {
    field: "Parent Node",
    headerName: "Parent Node",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["Parent Node"]}
        placeholder="Enter Parent Node"
        maxLength={10}
        rowId={params.row.Id}
        fieldName="Parent Node"
        tableKey="PERSON RESPONSIBLE"
        fetchOldData={true}
        fetchFunction={fetchOldDescriptionForNode}
        oldDataField="Old Person Responsible"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Old Person Responsible",
    headerName: "Old Person Responsible",
    flex: 1,
    editable: false,
    renderCell: (params) => (
      <div style={{ padding: "8px", fontSize: "14px", color: "#666" }}>
        {params.row["Old Person Responsible"] || ""}
      </div>
    ),
  },
  {
    field: "New Person Responsible",
    headerName: "New Person Responsible",
    flex: 1,
    renderCell: (params) => (
      <EditableTextField
        value={params.row["New Person Responsible"]}
        placeholder="Enter New Person Responsible"
        maxLength={40}
        rowId={params.row.Id}
        fieldName="New Person Responsible"
        tableKey="PERSON RESPONSIBLE"
        addToChangeLog={addToChangeLog}
        updateDuplicateCheckLists={updateDuplicateCheckLists}
        isDuplicateCheckField={false}
      />
    ),
  },
  {
    field: "Action",
    headerName: "Action",
    renderCell: (params) => (
      <Tooltip title="Delete Row">
        <IconButton
          onClick={() => handleDeleteRow(params.row.Id, 7)}
          disabled={disableCheck}
          sx={{
            color: disableCheck ? "grey.400" : "red",
          }}
        >
          <DeleteOutlineIcon />
        </IconButton>
      </Tooltip>
    ),
  },
];
