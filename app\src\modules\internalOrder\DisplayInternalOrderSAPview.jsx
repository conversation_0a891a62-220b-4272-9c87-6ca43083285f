import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import useInternalOrderFieldConfig from "@hooks/useInternalOrderFieldConfig";
import { Grid, IconButton, Stack, Typography, Box, Tabs, Tab, Paper } from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useTranslation } from "react-i18next";
import InventoryIcon from '@mui/icons-material/Inventory';
import BusinessIcon from '@mui/icons-material/Business';
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import GenericTabsForChange from "@components/MasterDataCockpit/GenericTabsForChange";
import useInternalOrderSAPDisplay from "./hooks/useInternalOrderSAPDisplay";
import {
  outermostContainer_Information,
  iconButton_SpacingSmall,
} from "@components/Common/commonStyles";
import { colors } from "../../constant/colors";

const DisplayInternalOrderSAPview = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const rowData = location.state;
  const { fetchInternalOrderFieldConfig, fieldConfigByOrderType } =
    useInternalOrderFieldConfig();
  const { fieldValues, loading: fieldValuesLoading, error: fieldValuesError } = useInternalOrderSAPDisplay(rowData?.Order);
  const RenderRow = ({
    label,
    value,
    labelWidth = "25%",
    centerWidth = "5%",
    icon,
  }) => (
    <Stack flexDirection="row" alignItems="center">
      {icon && <div style={{ marginRight: "10px" }}>{icon}</div>}
      <Typography
        variant="body2"
        color={colors.secondary.grey}
        style={{ width: labelWidth }}
      >
        {label}
      </Typography>
      <Typography
        variant="body2"
        fontWeight="bold"
        sx={{ width: centerWidth, textAlign: "center" }}
      >
        :
      </Typography>
      <Typography variant="body2" fontWeight="bold" justifyContent="flex-start">
        {value || ""}
      </Typography>
    </Stack>
  );

  useEffect(() => {
    if (rowData?.OrderType) {
      fetchInternalOrderFieldConfig(rowData?.OrderType);
    }
  }, []);

  useEffect(() => {}, [fieldConfigByOrderType]);

  const [activeTab, setActiveTab] = React.useState(0);

  const orderType = rowData?.OrderType;
  const allTabsData = (fieldConfigByOrderType?.[orderType]?.allTabsData || []).filter(tab => tab.tab.toLowerCase() !== 'header');

  const tabNames = allTabsData.map((tab) => tab.tab);

  return (
    <div>
      <Grid container sx={outermostContainer_Information}>
        <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <IconButton
              color="primary"
              sx={iconButton_SpacingSmall}
              onClick={() => navigate(-1)}
            >
              <ArrowCircleLeftOutlinedIcon
                sx={{ fontSize: "25px", color: "#000000" }}
              />
            </IconButton>
            <Grid item md={12}>
              <Typography variant="h3">
                <strong>{t("Display Internal Order")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the details of the Internal Order")}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Grid
        container
        display="flex"
        flexDirection="row"
        flexWrap="nowrap"
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
          paddingLeft: "29px",
          backgroundColor: colors.basic.lighterGrey,
          borderRadius: "10px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
        }}
      >
        <Stack
          width="48%"
          spacing={1}
          sx={{
            padding: "10px 15px",
            borderRight: "1px solid #eaedf0",
          }}
        >
          <Grid item>
            <RenderRow
              label={t("Internal Order")}
              value={fieldValues?.Order || rowData?.Order || ""}
              labelWidth="35%"
              icon={
                <InventoryIcon
                  sx={{ color: colors.blue.indigo, fontSize: "20px" }}
                />
              }
            />
          </Grid>
          <Grid item>
            <RenderRow
              label={t("Company Code")}
              value={fieldValues?.CompCode || rowData?.CompCode || ""}
              labelWidth="35%"
              icon={
                <BusinessIcon
                  sx={{ color: colors.blue.indigo, fontSize: "20px" }}
                />
              }
            />
          </Grid>
        </Stack>

        <Stack
          width="48%"
          spacing={1}
          marginRight={"-10%"}
          sx={{
            padding: "10px 15px",
          }}
        >
          <Grid item>
            <RenderRow
              label={t("Order Type")}
              value={fieldValues?.OrderType || rowData?.OrderType || ""}
              labelWidth="35%"
              icon={
                <CategoryIcon
                  sx={{ color: colors.blue.indigo, fontSize: "20px" }}
                />
              }
            />
          </Grid>
          <Grid item>
            <RenderRow
              label={t("Order Description")}
              value={fieldValues?.OrderName || rowData?.OrderName || ""}
              labelWidth="35%"
              icon={
                <DescriptionIcon
                  sx={{ color: colors.blue.indigo, fontSize: "20px" }}
                />
              }
            />
          </Grid>
        </Stack>
      </Grid>

      {allTabsData.length > 0 && (
        <Box
          sx={{
            marginTop: "30px",
            border: "1px solid #e0e0e0",
            padding: "16px",
            background: colors.primary.white,
            borderRadius: "8px",
          }}
        >
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            aria-label="internal order tabs"
            sx={{
              marginBottom: 2,
              borderBottom: 1,
              borderColor: "divider",
              "& .MuiTabs-indicator": {
                backgroundColor: colors.primary.main,
                height: "3px",
              },
            }}
            variant="scrollable"
            scrollButtons="auto"
          >
            {tabNames.map((name, idx) => (
              <Tab label={t(name)} key={idx} />
            ))}
          </Tabs>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 4, marginTop: 2 }}>
            <GenericTabsForChange
              disabled={true}
              basicDataTabDetails={allTabsData[activeTab]?.data}
              dropDownData={{}}
              activeViewTab={tabNames[activeTab]}
            />
          </Paper>
        </Box>
      )}
    </div>
  );
};

export default DisplayInternalOrderSAPview;
