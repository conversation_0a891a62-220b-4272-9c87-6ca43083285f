import{l,m as p}from"./index-226a1e75.js";var Y={exports:{}};(function(m,L){(function(n,o){m.exports=o()})(l,function(){var n={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(o,a,M){var f=a.prototype,t=f.format;M.en.formats=n,f.format=function(e){e===void 0&&(e="YYYY-MM-DDTHH:mm:ssZ");var r=this.$locale().formats,i=function(c,s){return c.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(g,d,u){var x=u&&u.toUpperCase();return d||s[u]||n[u]||s[x].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(w,v,D){return v||D.slice(1)})})}(e,r===void 0?{}:r);return t.call(this,i)}}})})(Y);var A=Y.exports;const T=p(A);var h={exports:{}};(function(m,L){(function(n,o){m.exports=o()})(l,function(){return function(n,o,a){o.prototype.isBetween=function(M,f,t,e){var r=a(M),i=a(f),c=(e=e||"()")[0]==="(",s=e[1]===")";return(c?this.isAfter(r,t):!this.isBefore(r,t))&&(s?this.isBefore(i,t):!this.isAfter(i,t))||(c?this.isBefore(r,t):!this.isAfter(r,t))&&(s?this.isAfter(i,t):!this.isBefore(i,t))}}})})(h);var B=h.exports;const z=p(B);export{z as i,T as l};
