import React from 'react';
import { Box, Typography, Chip, IconButton } from '@mui/material';
import { Message as MessageIcon, Circle, Settings, Close } from '@mui/icons-material';

const ChatHeader = ({ 
  connectionStatus, 
  getStatusColor, 
  getStatusText, 
  menuAnchor, 
  setMenuAnchor, 
  onClose 
}) => {
  return (
    <Box sx={{
      backgroundColor:(theme) =>theme.palette.primary.dark,
      color: 'white',
      px: 2,
      py: 1,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      borderBottom: '1px solid #5a5a7d'
    }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <MessageIcon />
        <Typography variant="h6" sx={{ fontWeight: 500, color: "white" }}>
          Messaging Platform
        </Typography>
        <Chip
          icon={<Circle sx={{ fontSize: 8 }} />}
          label={getStatusText()}
          size="small"
          sx={{
            backgroundColor: getStatusColor(),
            color: 'white',
            height: '20px',
            fontSize: '11px'
          }}
        />
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <IconButton
          size="small"
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              transform: 'scale(1.1)',
              transition: 'all 0.2s ease-in-out'
            }
          }}
          onClick={(e) => setMenuAnchor(e.currentTarget)}
        >
          <Settings />
        </IconButton>
        <IconButton
          size="small"
          sx={{
            color: 'white',
            '&:hover': {
              backgroundColor: 'rgba(255, 0, 0, 0.2)',
              color: '#ff4444',
              transform: 'scale(1.1)',
              transition: 'all 0.2s ease-in-out'
            }
          }}
          onClick={onClose}
        >
          <Close />
        </IconButton>
      </Box>
    </Box>
  );
};

export default ChatHeader;