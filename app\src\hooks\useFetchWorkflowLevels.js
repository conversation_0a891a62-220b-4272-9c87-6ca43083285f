import { useEffect, useState } from 'react';
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import useLogger from './useLogger';
import { REQUEST_TYPE } from '@constant/enum';
export const useFetchWorkflowLevels = ({
  requestType,
  initialPayload,
  dynamicData,
  taskData,
  singlePayloadData,
  version,
  dtName,
  module
}) => {
  const [wfLevels, setWfLevels] = useState([]);
  const [loading, setLoading] = useState(false);
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  const { customError } = useLogger();
  useEffect(() => {
    const fetchWorkflowLevels = async () => {
      try {
        setLoading(true);
        const workflowLevelsDtData = 
        initialPayload?.RequestType === REQUEST_TYPE.CHANGE || initialPayload?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD 
        ? await getDynamicWorkflowDT(
          initialPayload?.RequestType,
          initialPayload?.Region,
          initialPayload?.TemplateName,
          dynamicData?.childRequestHeaderData?.MaterialGroupType,
          taskData?.ATTRIBUTE_3,
          version,
          dtName,
          module
        )
        : await getDynamicWorkflowDT(
          initialPayload?.RequestType,
          initialPayload?.Region,
          '',
          singlePayloadData?.[dynamicData]?.Tochildrequestheaderdata?.MaterialGroupType,
          taskData?.ATTRIBUTE_3,
          version,
          dtName,
          module
        )
        setWfLevels(workflowLevelsDtData);
      } catch (err) {
        customError(err);
      } finally {
        setLoading(false);
      }
    };

    if (initialPayload?.RequestType && initialPayload?.Region && dynamicData && taskData?.ATTRIBUTE_3) {
      fetchWorkflowLevels();
    }
  }, [initialPayload?.Region, dynamicData, taskData?.ATTRIBUTE_3]);

  return { wfLevels, loading };
};
