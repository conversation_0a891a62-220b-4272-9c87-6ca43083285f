import {
  Button,
  FormControl,
  Grid,
  MenuItem,
  Select,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { font_Small } from "./commonStyles";
import { Box, Stack } from "@mui/system";
import { useSelector, useDispatch } from "react-redux";
import { destination_Admin } from "../../destinationVariables";
import { doAjax } from "./fetchService";
import { appSettingsUpdate } from "@app/appSettingsSlice";
import { formValidator } from "../../functions";
import SystemConfiguration from "./ReusableAttachmentAndComments/SystemConfiguration";
import useLang from "@hooks/useLang";
import { showToast } from "../../functions"
import { END_POINTS } from "@constant/apiEndPoints";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@constant/enum";

export function fetchGlobalAppSettings(userData, dispatch, setSettingsObj = null) {
  let hSuccess = (data) => {
    if (!data) {
      dispatch(appSettingsUpdate());
    } else {
      if (
        data.data.dateFormat ||
        data.data.language ||
        data.data.dateRangeValue ||
        data.data.timeFormat
      ) {
        const appSettingsData = {
          dateFormat: data.data.dateFormat,
          range: data.data.dateRangeValue,
          timeFormat: data.data.timeFormat,
          language: data.data.language,
        };
        dispatch(appSettingsUpdate(appSettingsData));

        if (setSettingsObj) {
          setSettingsObj(prevState => ({
            ...prevState,
            dateFormat: data.data.dateFormat,
            language: data.data.language,
            range: data.data.dateRangeValue,
            timeFormat: data.data.timeFormat,
          }));
        }
      }
    }
  };
  let hError = () => { };
  doAjax(
    `/${destination_Admin}${END_POINTS?.USER_ACCESS?.GET_APP_SETTINGS}?email=${userData?.emailId}`,
    "get",
    hSuccess,
    hError
  );
}

export default function ApplicationSettings() {
  const appSettings = useSelector((state) => state.appSettings);
  let userData = useSelector((state) => state.userManagement.userData);
  const { t } = useLang();

  const [SettingsObj, setSettingsObj] = useState({
    dateFormat: appSettings.dateFormat,
    range: appSettings.range,
    timeFormat: appSettings.timeFormat,
    language: appSettings.language,
  });
  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);
  const dispatch = useDispatch();

  const handleSelect = (e) => {
    setSettingsObj({ ...SettingsObj, [e.target.name]: e.target.value });
  };
  const handleFormat = (e) => {
    setSettingsObj({ ...SettingsObj, dateFormat: e.target.value });
  };
  const handleRange = (e) => {
    setSettingsObj({ ...SettingsObj, range: e.target.value });
  };
  const handleClear = () => {
    setSettingsObj({
      ...SettingsObj,
      dateFormat: "",
      range: "",
      timeFormat: "",
    });
    setFormValidationErrorItems([]);
  };

  const handleSave = () => {
    let payload = {
      dateRangeValue: SettingsObj.range,
      dateFormat: SettingsObj.dateFormat,
      email: userData?.emailId,
      timeFormat: SettingsObj.timeFormat,
      landingPage: "",
    };

    let hSuccess = (data) => {
      const Data = {
        ...appSettings,
        dateFormat: SettingsObj.dateFormat,
        range: SettingsObj.range,
        timeFormat: SettingsObj.timeFormat,
      };
      dispatch(appSettingsUpdate(Data));
      if (data.status === "Success") {
        showToast(SUCCESS_MESSAGES?.APP_SET_SUCCESS, "success");
      } else {
        showToast(ERROR_MESSAGES?.APP_SET_ERROR, "error");
      }
    };

    let hError = () => {
      showToast(ERROR_MESSAGES?.APP_SET_ERROR, "error");
    };

    doAjax(
      `/${destination_Admin}${END_POINTS?.USER_ACCESS?.SAVE_APP_SETTINGS}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  return (
      <>
        <Box sx={{ p: 3, position: 'relative', minHeight: '400px' }}>
          <Typography variant="h5" sx={{ mb: 4 }}>
            {t("System Settings")}
          </Typography>
          <Stack spacing={2}>
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Date Format</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Date Format"
                    select
                    sx={font_Small}
                    size="small"
                    value={SettingsObj.dateFormat}
                    name={"dateFormat"}
                    onChange={handleFormat}
                    displayEmpty={true}
                    error={formValidationErrorItems.includes("dateFormat")}
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Date Format{" "}
                      </div>
                    </MenuItem>

                    <MenuItem value={"DD MMM YYYY"}>
                      DD MMM YYYY (01 Apr 2023)
                    </MenuItem>
                    <MenuItem value={"MMM DD, YYYY"}>
                      MMM DD, YYYY (Apr 01, 2023)
                    </MenuItem>
                    <MenuItem value={"YYYY MMM DD"}>
                      YYYY MMM DD (2023 Apr 01)
                    </MenuItem>

                    <MenuItem value={"DD-MM-YYYY"}>
                      DD-MM-YYYY (01-04-2023)
                    </MenuItem>
                    <MenuItem value={"MM-DD-YYYY"}>
                      MM-DD-YYYY (04-01-2023)
                    </MenuItem>
                    <MenuItem value={"YYYY-MM-DD"}>
                      YYYY-MM-DD (2023-04-01)
                    </MenuItem>

                    <MenuItem value={"DD/MM/YYYY"}>
                      DD/MM/YYYY (01/04/2023)
                    </MenuItem>
                    <MenuItem value={"MM/DD/YYYY"}>
                      MM/DD/YYYY (04/01/2023)
                    </MenuItem>
                    <MenuItem value={"YYYY/MM/DD"}>
                      YYYY/MM/DD (2023/04/01)
                    </MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Time Format</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Time Format"
                    select
                    size="small"
                    value={SettingsObj.timeFormat}
                    name={"timeFormat"}
                    onChange={handleSelect}
                    displayEmpty={true}
                    sx={font_Small}
                    error={formValidationErrorItems.includes("timeFormat")}
                  >
                    <MenuItem sx={font_Small} value={""}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Time Format{" "}
                      </div>
                    </MenuItem>

                    <MenuItem value={"hh:mm A"}>12-hour (01:34 AM)</MenuItem>
                    <MenuItem value={"HH:mm"}>24-hour (13:34)</MenuItem>
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item md={12}>
              <Box>
                <Typography sx={font_Small}>Default Date Range</Typography>
                <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                  <Select
                    placeholder="Select Default Date Range"
                    select
                    size="small"
                    value={SettingsObj.range}
                    name={"range"}
                    onChange={handleRange}
                    displayEmpty={true}
                    sx={font_Small}
                    error={formValidationErrorItems.includes("range")}
                  >
                    <MenuItem sx={font_Small} value={0}>
                      <div style={{ color: "#C1C1C1" }}>
                        Select Default Date Range
                      </div>
                    </MenuItem>

                    {[
                      "Last Week",
                      "Last Month",
                      "Current Month",
                      "Current Quarter",
                      "Year To Date",
                    ]?.map((rangeName) => (
                      <MenuItem
                        key={rangeName}
                        value={
                          rangeName === "Last Week"
                            ? 7
                            : rangeName === "Last Month"
                              ? 50
                              : rangeName === "Current Month"
                                ? 100
                                : rangeName === "Current Quarter"
                                  ? 150
                                  : rangeName === "Year To Date"
                                    ? 200
                                    : 0
                        }
                      >
                        {rangeName}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
            <Grid item md={12} alignItems={"center"}>
              <Box>
                <SystemConfiguration />
              </Box>
            </Grid>
          </Stack>
        </Box>
        <Box
          sx={{
            position: 'absolute',
            bottom: 16,
            right: 24,
            display: 'flex',
            gap: 1
          }}
        >
          <Grid item md={12}>
            <Box sx={{ display: "flex", justifyContent: "end", gap: 1 }}>
              <Button
                sx={{ width: "max-content", textTransform: "capitalize" }}
                onClick={handleClear}
                variant="outlined"
              >
                {t("Clear")}
              </Button>
              <Button
                type="save"
                variant="contained"
                onClick={() => {
                  if (
                    formValidator(
                      SettingsObj,
                      ["dateFormat", "range", "timeFormat"],
                      setFormValidationErrorItems
                    )
                  ) {
                    handleSave();
                  }
                }}
              >
                {t("Save")}
              </Button>
            </Box>
          </Grid>
        </Box>
      </>
  );
}