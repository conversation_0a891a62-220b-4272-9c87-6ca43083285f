import fs from 'fs';
import { execSync } from 'child_process';

const versionFile = 'version.json';
let versionData;
try {
  versionData = JSON.parse(fs.readFileSync(versionFile));
} catch (err) {
  console.error('Error reading version.json:', err);
  process.exit(1);
}

versionData.buildRevision += 1;

try {
  versionData.commitHash = execSync('git rev-parse --short HEAD').toString().trim();
} catch (err) {
  console.error('Error retrieving Git commit hash:', err);
  versionData.commitHash = 'unknown';
}

try {
  versionData.branchName = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
} catch (err) {
  console.error('Error retrieving Git branch name:', err);
  versionData.branchName = 'unknown';
}

try {
  versionData.developerName = execSync('git config user.name').toString().trim();
} catch (err) {
  console.error('Error retrieving Git user name:', err);
  versionData.developerName = 'unknown';
}

versionData.buildDate = new Date().toISOString();

// Write updated version data back to version.json
try {
  fs.writeFileSync(versionFile, JSON.stringify(versionData, null, 2));
} catch (err) {
  console.error('Error writing to version.json:', err);
  process.exit(1);
}