import * as icon from "@mui/icons-material";
import { createElement } from "react";
import { useTheme } from "@mui/material/styles";

function ReusableIcon(props) {
  const theme = useTheme();

  return createElement(
    icon[
      props?.isSelected ? `${props?.iconName}` : `${props?.iconName}Outlined`
    ],
    {
      sx: {
        color: props?.isSelected
          ? theme.palette.primary.main
          : props?.iconColor ?? theme.palette.text.secondary,
        fontSize: `${props?.iconSize ?? "24px"}`,
      },
      className: "iconClass"
    }
  );
};

 

export default ReusableIcon;