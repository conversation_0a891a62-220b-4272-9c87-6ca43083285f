import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  requestHeaderDTIO: {},
  tabValue: 0,
  dropDownDataIO: {},
  IOpayloadData: {
    requestHeaderData: {},
    rowsHeaderData: [],
    rowsBodyData: {},
  },
  IOPayloadFields: {}, // Direct payload fields like BOM
  requestHeaderID: "",
  savedReqData: {},
  fieldConfigByOrderType: {}, // Store field config by order type
  activeRowId: null, // Store active row ID
  selectedOrderType: null, // Store selected order type
  validatedRows: {}, // Track validation status of each row
  validatedRowsStatus: {}, // Track validation status (error/success) of each row
  originalRowData: {}, // Store original row data for dirty checking
  originalTabData: {}, // Store original tab data for dirty checking
  isOpenDialog: true, // Dialog opens by default like bankKey
};

const internalOrderSlice = createSlice({
  name: "internalOrder",
  initialState,
  reducers: {
    setRequestHeaderDTIO: (state, action) => {
      state.requestHeaderDTIO = action.payload;
    },

    setDropDownDataIO: (state, action) => {
      const { keyName, data } = action.payload;
      state.dropDownDataIO[keyName] = data;
    },
    setIOpayloadData: (state, action) => {
      const { keyName, data } = action.payload;
      state.IOpayloadData[keyName] = data;
      return state;
    },
    setMultipleIOpayloadData: (state, action) => {
      const updates = action.payload;
      Object.entries(updates).forEach(([keyName, data]) => {
        state.IOpayloadData[keyName] = data;
      });
      return state;
    },

    updateModuleFieldDataIO: (state, action) => {
      const { uniqueId, keyName, data } = action.payload;
      const value = data?.code ?? data ?? "";
      if (uniqueId) {
        if (!state.IOpayloadData.rowsBodyData[uniqueId]) {
          state.IOpayloadData.rowsBodyData[uniqueId] = {};
        }
        if (!state.IOpayloadData.rowsBodyData[uniqueId].payload) {
          state.IOpayloadData.rowsBodyData[uniqueId].payload = {};
        }
        state.IOpayloadData.rowsBodyData[uniqueId].payload[keyName] = value;
      } else {
        state.IOpayloadData.requestHeaderData[keyName] = value;
      }
    },
    updateTableColumnDataIO: (state, action) => {
      const { uniqueId, keyName, data } = action.payload;
      const value = data?.code ?? data ?? "";
      if (uniqueId) {
        if (!state.IOpayloadData.rowsBodyData[uniqueId]) {
          state.IOpayloadData.rowsBodyData[uniqueId] = {};
        }
        // Store table column data directly under uniqueId (not in payload)
        state.IOpayloadData.rowsBodyData[uniqueId][keyName] = value;
      }
    },

    setInternalOrderFieldConfig: (state, action) => {
      const { orderType, fieldData } = action.payload;
      if (!state.fieldConfigByOrderType) {
        state.fieldConfigByOrderType = {};
      }
      state.fieldConfigByOrderType[orderType] = fieldData;
    },

    setSavedReqData: (state, action) => {
      state.savedReqData = action.payload;
    },
    setTabValue: (state, action) => {
      state.tabValue = action.payload;
    },
    setActiveRowIdIO: (state, action) => {
      state.activeRowId = action.payload;
    },
    setSelectedOrderTypeIO: (state, action) => {
      state.selectedOrderType = action.payload;
    },

    setIOPayloadFields: (state, action) => {
      const { keyName, data } = action.payload;
      state.IOPayloadFields[keyName] = typeof data === "object" && data !== null && "code" in data ? data.code : data;
    },

    deleteRowDataIO: (state, action) => {
      const { rowId } = action.payload;
      state.IOpayloadData.rowsHeaderData = state.IOpayloadData.rowsHeaderData.filter((row) => row.id !== rowId);
      if (state.IOpayloadData.rowsBodyData[rowId]) {
        delete state.IOpayloadData.rowsBodyData[rowId];
      }
      // Clean up validation data when row is deleted
      delete state.validatedRows[rowId];
      delete state.validatedRowsStatus[rowId];
      delete state.originalRowData[rowId];
      delete state.originalTabData[rowId];
    },
    setValidatedRowsIO: (state, action) => {
      const { rowId, isValid } = action.payload;
      state.validatedRows[rowId] = isValid;
    },
    setValidatedStatusIO: (state, action) => {
      const { rowId, status } = action.payload;
      state.validatedRowsStatus[rowId] = status;
    },

    setOriginalRowDataIO: (state, action) => {
      const { rowId, data } = action.payload;
      state.originalRowData[rowId] = data;
    },
    setOriginalTabDataIO: (state, action) => {
      const { rowId, data } = action.payload;
      state.originalTabData[rowId] = data;
    },
    setIORows: (state, action) => {
      state.IOpayloadData.rowsHeaderData = action.payload;
    },
    setOpenDialogIO: (state, action) => {
      state.isOpenDialog = action.payload;
    },
    resetInternalOrderState: (state) => {
      // Reset all state to initial values
      state.requestHeaderDTIO = {};
      state.tabValue = 0;
      state.dropDownDataIO = {};
      state.IOpayloadData = {
        requestHeaderData: {},
        rowsHeaderData: [],
        rowsBodyData: {},
      };
      state.IOPayloadFields = {};
      state.requestHeaderID = "";
      state.savedReqData = {};
      state.fieldConfigByOrderType = {};
      state.activeRowId = null;
      state.selectedOrderType = null;
      state.validatedRows = {};
      state.validatedRowsStatus = {};
      state.originalRowData = {};
      state.originalTabData = {};
      state.isOpenDialog = true; // Reset to default open state
    },
  },
});

export const { setMultipleIOpayloadData, setRequestHeaderDTIO, setDropDownDataIO, setIOpayloadData, updateModuleFieldDataIO, updateTableColumnDataIO, setInternalOrderFieldConfig, setSavedReqData, setTabValue, deleteRowDataIO, setActiveRowIdIO, setSelectedOrderTypeIO, setIOPayloadFields, setValidatedRowsIO, setValidatedStatusIO, setOriginalRowDataIO, setOriginalTabDataIO, setIORows, setOpenDialogIO, resetInternalOrderState } = internalOrderSlice.actions;

export default internalOrderSlice.reducer;
