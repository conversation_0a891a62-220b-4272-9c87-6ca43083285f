import React, { useEffect, useRef, useState } from "react";
import {
  Typography,
  IconButton,
  Grid,
  Box,
  Stack,
  Tabs,
  Tab,
  Paper,
  Card,
  CardContent,
  Divider,
} from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import {
  destination_CostCenter_Mass,
  destination_ProfitCenter_Mass
} from "../../destinationVariables";
import {
  iconButton_SpacingSmall,
  outermostContainer_Information,
} from "@components/Common/commonStyles";
import { doAjax } from "@components/Common/fetchService";
import { makeStyles } from "@mui/styles";
import InventoryIcon from "@mui/icons-material/Inventory";
import BusinessIcon from "@mui/icons-material/Business";
import CategoryIcon from "@mui/icons-material/Category";
import DescriptionIcon from "@mui/icons-material/Description";
import { useDispatch, useSelector } from "react-redux";
import { ERROR_MESSAGES, LOCAL_STORAGE_KEYS, MODULE, MODULE_MAP } from "@constant/enum";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useCostCenterFieldConfig from "@hooks/useCostCenterFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { convertSAPDateForCalendar, setLocalStorage } from "@helper/helper";
import useLang from "@hooks/useLang";
import { END_POINTS } from "@constant/apiEndPoints";
import { setDropDown as  setDropDownAction} from "@costCenter/slice/costCenterDropDownSlice";
import { fetchCurrencyBasedOnCompCode } from "../../functions";
import { v4 as uuidv4 } from "uuid";
import {setCCPayload} from "@app/costCenterTabsSlice";
 
const RenderRow = ({ label, value, icon }) => (
  <Grid item xs={6}>
    <Stack flexDirection="row" alignItems="center" spacing={1}>
      {icon && <Box>{icon}</Box>}
      <Typography variant="body2" color={colors.secondary.grey}>
        {label}
      </Typography>
      <Typography variant="body2" fontWeight="bold">
        : {value || ""}
      </Typography>
    </Stack>
  </Grid>
);
 
const DisplayCostCenter = () => {

  const dispatch = useDispatch();
  const useStyles = makeStyles(() => ({
    customTabs: {
      "& .MuiTabs-scroller": {
        overflowX: "auto !important",
        overflowY: "hidden !important",
      },
    },
  }));
 
  const costCenterTabs = useSelector((state) => {
    const tabs = state.costCenter.costCenterTabs || [];
    return tabs.filter((tab) => tab.tab !== "Initial Screen");
  });
 
  const { loading, error, fetchCostCenterFieldConfig } = useCostCenterFieldConfig();
  const navigate = useNavigate();
  const classes = useStyles();
  const location = useLocation();
  const structureData = location.state;
  const { t } = useLang();
 
  const [tabNames, setTabNames] = useState([]);
  const [tabContentData, setTabContentData] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedRowId, setSelectedRowId] = useState("");
  const fetchedDropdownData = useSelector((state)=>state.costCenterDropDownData?.dropDown);
  useEffect(() => {
    if (!costCenterTabs?.length) {
      fetchCostCenterFieldConfig();
    }
  }, []);
 
  useEffect(() => {
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.CC)
    fetchCostCenterData();
  }, []);
  const getCostCenterCategory = (id) => {
      const hSuccess = (data) => {
              dispatch(setDropDownAction({
                    keyName:"CostcenterType",
                    data: data?.body || [],
                    keyName2: id,
              }))
      };
      const hError = (error) => {
        console.error(error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
        "get",
        hSuccess,
        hError
      );
    };
    const getFunctionalArea = (id) => {
      const hSuccess = (data) => {
        dispatch(setDropDownAction({
                    keyName:"FuncAreaLong",
                    data: data?.body,
                    keyName2: id,
              }))
      };
      const hError = (error) => {
        console.error(error);
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
        "get",
        hSuccess,
        hError
      );
    };
    const getCompanyCodeBasedOnControllingArea = (CA,id) => {
        const hSuccess = (data) => {
          dispatch(setDropDownAction({
                keyName:"CompCode",
                data: data?.body || [],
                keyName2: id,
          }))
        };
        const hError = (error) => {
          console.log(error);
        };
        doAjax(
          `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${CA}&rolePrefix=ETP`,
          "get",
          hSuccess,
          hError
        );
      };
    const getProfitCenter = (compcode,id) => {
          const payload = {
            controllingArea: "ETCA",
            companyCode: compcode,
            top: "100",
            skip: "0",
          };
          const hSuccess = (data) => {
            dispatch(setDropDownAction({
                  keyName:"ProfitCtr",
                  data: data?.body?.list || [],
                  keyName2: id,
            }))
          };
          const hError = (error) => {
            console.log(error);
          };
          doAjax(
            `/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`,
            "post",
            hSuccess,
            hError,
            payload
          );
        };
    const getCountryData = () => {
            const hSuccess = (data) => {
            dispatch(setDropDownAction({
                  keyName:"AddrCountry",
                  data: data?.body,
            }))
            };
            const hError = (error) => {
              console.log(error);
            };
            doAjax(
              `/${destination_CostCenter_Mass}/data/getCountry`,
              "get",
              hSuccess,
              hError
            );
          };
        
    const getRegionBasedOnCountry = (countryCode,id) => {
            const hSuccess = (data) => {
              dispatch(setDropDownAction({
                  keyName:"AddrRegion",
                  data: data?.body,
                  keyName2: id,
            }))
            };
            const hError = (error) => {
              console.log(error);
            };
            doAjax(
              `/${destination_CostCenter_Mass}/data/getRegionBasedOnCountry?country=${countryCode}`,
              "get",
              hSuccess,
              hError
            );
          };
 
  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
 
  const mapTabValuesToConfig = (tabs, tabContentData) => {
    const dtoMap = {
      "Basic Data": tabContentData?.basicDataTabDto,
      "Additional Data": tabContentData?.additionalDataTabDto,
      Indicators: tabContentData?.indicatorsTabDto,
      Address: tabContentData?.addressTabDto,
      Communication: tabContentData?.communicationTabDto,
      Control: tabContentData?.controlTabDto,
      Template: tabContentData?.templatesTabDto,
    };
 
    return tabs.map((tab) => {
      const tabLabel = tab.tab;
      const dto = dtoMap[tabLabel];
      if (!dto) return tab;
 
      const updatedData = {};
      for (const cardKey in tab.data) {
        updatedData[cardKey] = tab.data[cardKey].map((field) => {
          const jsonKey = field.jsonName;
          let value = dto?.[jsonKey]; 
          if (typeof value === "boolean") {
            value = value ? "TRUE" : "FALSE";
          }
          return {
            ...field,
            value: value ?? field.value,
          };
        });
      }
 
      return {
        ...tab,
        data: updatedData,
      };
    });
  };
 

const hasMappedTabs = useRef(false); // Ref to avoid re-execution

useEffect(() => {
  const tabsReady = costCenterTabs?.length > 0;
  const dataReady = tabContentData && Object.keys(tabContentData).length > 0;

  if (tabsReady && dataReady && !hasMappedTabs.current) {
    hasMappedTabs.current = true; // ✅ Prevent future calls

    const updatedTabs = mapTabValuesToConfig(costCenterTabs, tabContentData);
    setTabNames(updatedTabs);

    // Now run these APIs only once
    getCostCenterCategory();
    getFunctionalArea();
    getCountryData();
    // Note: Might use for reference later
    // if (tabContentData?.controllingArea) {
    //   getCompanyCodeBasedOnControllingArea(tabContentData?.controllingArea);
    // }
    // if (tabContentData?.basicDataTabDto?.CompCode) {
    //   fetchCurrencyBasedOnCompCode(
    //     tabContentData?.basicDataTabDto?.CompCode,
    //     dispatch,
    //     selectedRowId || selectedRow?.id
    //   );
    //   getProfitCenter(tabContentData?.basicDataTabDto?.CompCode);
    // }
    // if (tabContentData?.addressTabDto?.AddrCountry) {
    //   getRegionBasedOnCountry(tabContentData?.addressTabDto?.AddrCountry);
    // }
  }
}, [costCenterTabs, tabContentData]);


  const fetchCostCenterData = () => {
    setBlurLoading(true);
 
    const payload = {
      coAreaCCs: [
        {
          controllingArea: structureData?.controllingArea,
          costCenter: structureData?.costCenter,
        },
      ],
    };
 
    const hSuccess = async (data) => {
      const rawData = data?.body?.[0] || {};
      setTabContentData(rawData);
      const id = uuidv4();
      const flat = data?.body.reduce((acc, obj) => ({
        ...acc,
        ...obj.additionalDataTabDto,
        ...obj.addressTabDto,
        ...obj.basicDataTabDto,
        ...obj.communicationTabDto,
        ...obj.controlTabDto,
        ...obj.templatesTabDto,
        controllingArea : obj.controllingArea,
        costCenter: obj.costCenter,
        fromValid: obj.fromValid,
        toValid: obj.toValid,
      }), {});
      setSelectedRowId(id);
      const promises = [];
        if (flat?.controllingArea) {
           promises.push(
          getCompanyCodeBasedOnControllingArea(flat.controllingArea, id),
          );
        }
        if (flat?.CompCode) {
           promises.push(
          fetchCurrencyBasedOnCompCode(flat?.CompCode, dispatch, id),
          getProfitCenter(flat?.CompCode,id),
          );
        }
        if (flat?.AddrCountry) {
           promises.push(
          getRegionBasedOnCountry(flat?.AddrCountry,id),
          );
        }
  const dropDownObjects   = await Promise.all(promises);   // Array of objects
  const dropDownCombined  = Object.assign({}, ...dropDownObjects);
    const rowsBodyData = {
    [id]: {
      ...flat,               // base data from first API
      ...dropDownCombined,   // every look‑up result
    },
  };
    const payload = {
    requestHeaderData: {},   // you can still build these…
    rowsHeaderData:   {},
    rowsBodyData,
  };
    dispatch(setCCPayload(payload));
  setTimeout(() => {
      setBlurLoading(false);
}, 1000);

      // Note: Might use for display response mapping
      // const rawData = data?.body?.[0] || {};
      // setTabContentData(rawData);
      // const updatedTabs = mapTabValuesToConfig(costCenterTabs, rawData);
      // console.log("updatedTabs",updatedTabs)
      // setTabNames(updatedTabs);
      // getCostCenterCategory();
      // getFunctionalArea();
      // getCountryData();
      // if(rawData?.controllingArea){
      //     getCompanyCodeBasedOnControllingArea(rawData?.controllingArea);
      // }
      // if(rawData?.basicDataTabDto?.CompCode){
      //     fetchCurrencyBasedOnCompCode(rawData?.basicDataTabDto?.CompCode,dispatch,selectedRowId || selectedRow?.id);
      //     getProfitCenter(rawData?.basicDataTabDto?.CompCode);
      // }
      // if(rawData?.addressTabDto?.AddrCountry){
      //   getRegionBasedOnCountry(rawData?.addressTabDto?.AddrCountry);
      // }      
    };
 
    const hError = (error) => {
      console.error("Error fetching cost center data", error);
      setBlurLoading(false);
    };
 
    doAjax(
      `/${destination_CostCenter_Mass}${END_POINTS?.DATA?.GET_COSTCENTER_DATA}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
 
 
  return (
  <div style={{ backgroundColor: "#FAFCFF" }}>
    {/* Header Section */}
    <Grid container sx={outermostContainer_Information}>
      <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
        <Grid md={9} sx={{ display: "flex" }}>
          <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
            <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
          </IconButton>
          <Grid item md={12}>
            <Typography variant="h3">
              <strong>{t("Display Cost Center")}</strong>
            </Typography>
            <Typography variant="body2" color="#777">
              {t("This view displays the details of the Cost Centers")}
            </Typography>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
 
    {/* Top Info Card (mimicking the two-column layout) */}
    <Grid
      container
      display="flex"
      flexDirection="row"
      flexWrap="nowrap"
      sx={{
        justifyContent: "space-between",
        alignItems: "center",
        paddingLeft: "29px",
        backgroundColor: colors.basic.lighterGrey,
        borderRadius: "10px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
      }}
    >
      {/* Left Side */}
      <Stack
        width="48%"
        spacing={1}
        sx={{
          padding: "10px 15px",
          borderRight: "1px solid #eaedf0",
        }}
      >
        <Grid item>
          <RenderRow
            label={t("Controlling Area")}
            value={structureData?.controllingArea || ""}
            labelWidth="35%"
            icon={<InventoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px", marginTop:"10px" }} />}
          />
        </Grid>
        <Grid item>
          <RenderRow
            label={t("CostCenter Number")}
            value={structureData?.costCenter || ""}
            labelWidth="35%"
            icon={<BusinessIcon sx={{ color: colors.blue.indigo, fontSize: "20px", marginTop:"10px" }} />}
          />
        </Grid>
      </Stack>
 
      {/* Right Side */}
      <Stack
        width="48%"
        spacing={1}
        marginRight={"-10%"}
        sx={{
          padding: "10px 15px",
        }}
      >
        <Grid item>
          <RenderRow
            label={t("Short Description")}
            value={structureData?.CostCenterName || ""}
            labelWidth="35%"
            icon={<CategoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px", marginTop:"10px" }} />}
          />
        </Grid>
        <Grid item>
          <RenderRow
            label={t("Long Description")}
            value={structureData?.description || ""}
            labelWidth="35%"
            icon={<DescriptionIcon sx={{ color: colors.blue.indigo, fontSize: "20px", marginTop:"10px" }} />}
          />
        </Grid>
      </Stack>
    </Grid>
 
    {/* Tabs Section */}
    <Grid>
      {tabNames.length > 0 ? (
        <Box sx={{ mt: 3 }}>
          <Tabs
            value={selectedTab}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="scrollable"
            scrollButtons="auto"
            sx={{ borderBottom: 1, borderColor: "divider", mb: 2 }}
          >
            {tabNames.map((tab, index) => (
              <Tab key={index} label={tab.tab} />
            ))}
          </Tabs>
         
          <Paper elevation={2} sx={{ p: 3, borderRadius: 8 }}>
            {tabNames[selectedTab] && fetchedDropdownData?.ProfitCtr && fetchedDropdownData?.CompCode ? (
              <GenericTabsGlobal
                disabled={true}
                basicDataTabDetails={tabNames[selectedTab].data}
                dropDownData={fetchedDropdownData}
                activeViewTab={tabNames[selectedTab].tab}
                uniqueId={selectedRowId}
                selectedRow={selectedRow || {}}
                module={MODULE?.CC}
              />
            ):""}
          </Paper>
        </Box>
      ) : (
        <Box
          sx={{
            marginTop: "30px",
            border: `1px solid ${colors.secondary.grey}`,
            padding: "16px",
            background: `${colors.primary.white}`,
            textAlign: "center",
          }}
        >
          <span>{ERROR_MESSAGES.NO_DATA_AVAILABLE}</span>
        </Box>
      )}
    </Grid>
    <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
  </div>
);
 
};
 
export default DisplayCostCenter;