import React, { useState, useEffect, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { doAjax } from "../../../components/Common/fetchService";
import { Grid, IconButton, Tooltip, Typography } from "@mui/material";

import {
  Box,
  Container,
  TextField,
  InputAdornment,
  Skeleton,
  Fade,
  Paper,
  useTheme,
} from "@mui/material";
import {
  Search as SearchIcon,
  Add as AddIcon,
  Edit as EditIcon,
  TrendingUp as ExtendIcon,
  Download as DownloadIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
} from "@mui/icons-material";
import { END_POINTS } from "../../../constant/apiEndPoints";
import useLang from "../../../hooks/useLang";
import { KPICard } from "@cw/rds";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import ChangeLogRequestHistory from "./ChangeLogRequestHistory";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { useSnackbar } from "@hooks/useSnackbar";
import { CONSOLIDATED_REQUEST_HISTORY } from "@constant/enum";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { FilterChip, FloatingBackground, StyledDownloadButton, } from "./ConsolidatedRequestHistoryStyles";
import { MODULE_CONFIG, KPI_CARD_COLOR } from "./ConsolidatedRequestHistoryUtils";

// Import the timeline renderer component
import ConsolidatedRequestTimeline from "./ConsolidatedRequestTimeline";
import { colors } from "@constant/colors";
import { iconButton_SpacingSmall } from "../../common/commonStyles";
import { REQUEST_TYPE } from "../../../constant/enum";

const ConsolidatedRequestHistory = (props) => {
  const location = useLocation();
  const navigate = useNavigate()
  const materialNumber = location.state?.materialNumber || props?.materialNumber;
  const module = location.state?.module || props?.module || "Material";
  const theme = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("ALL");
  const [expandedRequest, setExpandedRequest] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [animateCards, setAnimateCards] = useState(false);
  const [changeLogModalOpen, setChangeLogModalOpen] = useState(false);
  const [responseData, setResponseData] = useState([]);
  const [selectedRequestForChangelog, setSelectedRequestForChangelog] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [KpiCards, setKpiCards] = useState([
    { requestType: "Create", requestCount: 0 },
    { requestType: "Create With Upload", requestCount: 0 },
    { requestType: "Change", requestCount: 0 },
    { requestType: "Change With Upload", requestCount: 0 },
    { requestType: "Extend", requestCount: 0 },
    { requestType: "Extend With Upload", requestCount: 0},
  ]);
  const { t } = useLang();
  const [loaderMessage, setLoaderMessage] = useState("");
  const { showSnackbar } = useSnackbar();

  const handleDownloadPdf = (request) => {
    setBlurLoading(true);

    const moduleConfig = MODULE_CONFIG[module] || "";
    const isChangeRequest = request.massChangeId || request.massChildChangeId;
    const fileIdentifier = request[moduleConfig.filePrefix] || materialNumber;

    let payload = {
      massCreationId: request.massCreationId,
      massChildCreationId: request.massChildCreationId,
      massChangeId: request.massChangeId,
      massExtendId: request.massExtendId,
      massSchedulingId: request.massSchedulingId,
      screenName: request.requestType,
      dtName: isChangeRequest
        ? moduleConfig.changeDtName
        : moduleConfig.defaultDtName,
      version: isChangeRequest
        ? moduleConfig.changeVersion
        : moduleConfig.defaultVersion,
      page: 0,
      size: 50,
      sort: "",
      Region: request.region,
      massChildSchedulingId: request.massChildSchedulingId,
      massChildExtendId: request.massChildExtendId,
      massChildChangeId: request.massChildChangeId,
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `${materialNumber}_Consolidated Request History.pdf`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(
        `${materialNumber} ${CONSOLIDATED_REQUEST_HISTORY.CONSOLIDATED_PDF_SUCCESS}`,
        "success"
      );
    };

    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(
        `Failed exporting ${request.requestId}_Consolidated Request History.pdf`,
        "error"
      );
    };

    doAjax(
      `/${moduleConfig?.destination}${END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF}/${request.requestId}/${fileIdentifier}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const handleTimelineDownload = (request) => {
    setBlurLoading(true);

    const moduleConfig = MODULE_CONFIG[module] || MODULE_CONFIG.Material;

    let payload = {
      materialNumber: materialNumber || "",
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      const fileIdentifier = request[moduleConfig.filePrefix] || materialNumber;

      link.href = href;
      link.setAttribute("download", `${module}_${fileIdentifier}_Timeline.pdf`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(
        `${module}_${fileIdentifier}_${CONSOLIDATED_REQUEST_HISTORY.TIMELINE_SUCCESS}`,
        "success"
      );
    };

    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`${CONSOLIDATED_REQUEST_HISTORY.TIMELINE_FAILURE}`, "error");
    };

    const dynamicUrl = `/${moduleConfig.destination}/${moduleConfig.timelineEndpoint}`;
    doAjax(dynamicUrl, "postandgetblob", hSuccess, hError, payload);
  };

  const handleApiCall = () => {
    const moduleConfig = MODULE_CONFIG[module] || MODULE_CONFIG.Material;
    const destination = moduleConfig.destination || destination_MaterialMgmt;

    const hSuccess = (data) => {
      const kpiData = [
        { requestType: "Create", requestCount: data?.body?.create || 0 },
        { requestType: "Create With Upload", requestCount: data?.body?.createWithUpload || 0 },
        { requestType: "Change", requestCount: data?.body?.change || 0 },
        { requestType: "Change With Upload", requestCount: data?.body?.changeWithUpload || 0 },
        { requestType: "Extend", requestCount: data?.body?.extend || 0 },
        { requestType: "Extend With Upload", requestCount: data?.body?.extendWithUpload || 0},
      ];
      setKpiCards(kpiData);
      setResponseData(data?.body?.body || []);
    };
    const hError = () => {
    };
    const payloadMaterialNumber = {
      materialNumber: materialNumber,
    };
    doAjax(
      `/${destination}/${END_POINTS.TASK_ACTION_DETAIL.CONSOLIDATED_REQUEST_HISTORY}`,
      "post",
      hSuccess,
      hError,
      payloadMaterialNumber
    );
  };

  useEffect(() => {
    handleApiCall();
  }, []);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
      setAnimateCards(true);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  // Filter and search logic
  const filteredRequests = useMemo(() => {
    if (!responseData || !Array.isArray(responseData)) return [];

    return responseData.filter((request) => {
      const normalizeRequestType = (type) => {
        if (type?.includes("Extend")) return "EXTEND";
        if (type?.includes("Change")) return "CHANGE";
        if (type?.includes("Create")) return "CREATE";
        return type?.toUpperCase() || "";
      };

      const normalizedRequestType = normalizeRequestType(request.requestType);
      const matchesFilter =
        selectedFilter === "ALL" || normalizedRequestType === selectedFilter;

      const matchesSearch =
        searchTerm === "" ||
        request.requestId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.childRequestId
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        request.requestCreatedBy
          ?.toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        request.requestType?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.requestStatus?.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesFilter && matchesSearch;
    });
  }, [responseData, searchTerm, selectedFilter]);

  // Loading Component
  const LoadingSkeleton = () => (
    <Container maxWidth="lg">
      <Box display="flex" flexDirection="column" alignItems="center" py={8}>
        <Box position="relative" mb={4}>
          <Box
            position="absolute"
            top="50%"
            left="50%"
            sx={{ transform: "translate(-50%, -50%)" }}
          ></Box>
        </Box>

        {[1, 2, 3, 4].map((i) => (
          <Skeleton
            key={i}
            variant="rectangular"
            width="100%"
            height={120}
            sx={{ mb: 3, borderRadius: 2 }}
          />
        ))}
      </Box>
    </Container>
  );

  if (isLoading) {
    return (
      <Box minHeight="100vh" bgcolor="grey.50">
        <LoadingSkeleton />
      </Box>
    );
  }

  return (
    <Box minHeight="90vh" bgcolor="grey.50" position="relative">
      {/* Floating Background */}
      <FloatingBackground>
        <svg viewBox="0 0 400 400">
          <defs>
            <linearGradient
              id="circleGradient1"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="#667EEA" stopOpacity="0.1" />
              <stop offset="100%" stopColor="#764BA2" stopOpacity="0.05" />
            </linearGradient>
            <linearGradient
              id="circleGradient2"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="#10B981" stopOpacity="0.1" />
              <stop offset="100%" stopColor="#059669" stopOpacity="0.05" />
            </linearGradient>
          </defs>
          <circle cx="80" cy="80" r="40" fill="url(#circleGradient1)">
            <animateTransform
              attributeName="transform"
              type="translate"
              values="0,0; 20,10; 0,0"
              dur="6s"
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="320" cy="120" r="30" fill="url(#circleGradient2)">
            <animateTransform
              attributeName="transform"
              type="translate"
              values="0,0; -15,20; 0,0"
              dur="8s"
              repeatCount="indefinite"
            />
          </circle>
          <circle cx="60" cy="300" r="25" fill="url(#circleGradient1)">
            <animateTransform
              attributeName="transform"
              type="translate"
              values="0,0; 10,-15; 0,0"
              dur="7s"
              repeatCount="indefinite"
            />
          </circle>
        </svg>
      </FloatingBackground>

      <Container
        maxWidth="100vw"
        sx={{ py: 3, position: "relative", zIndex: 1 }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
            <Grid>
              <IconButton color="primary" aria-label="upload picture" component="label" sx={iconButton_SpacingSmall}>
                <ArrowCircleLeftOutlinedIcon
                  sx={{
                    fontSize: "25px",
                    marginRight: "2px",
                    color: colors?.basic?.black,
                  }}
                  onClick={() => {
                    navigate(-1);
                  }}
                />
              </IconButton>
            </Grid>
            <Typography variant="h3" fontWeight="bold">
              {t("Audit Log")} - {module} - {materialNumber}
            </Typography>
          </Box>
          <Box>
            {filteredRequests?.length > 0 && (
                <StyledDownloadButton
                  onClick={handleTimelineDownload}
                  text="Export Timeline"
                  sx={{ minWidth: "200px" }}
                />
              )}
          </Box>
        </Box>

        <Typography variant="body2" color="text.secondary" mb={3} mt={-4}>
          {t("This view displays all the audits related to Master Data made in the Application")}
        </Typography>


        {/* KPI Cards Section */}
        <Fade in timeout={600}>
          <Grid
            container
            spacing={2}
            sx={{
              width: "100%",
              margin: 0,
              justifyContent: "space-between",
              marginBottom: 2,
            }}
          >
            {KpiCards?.map((tile, index) => {
              return (
                <Grid
                  key={index}
                  sx={{
                    flex: `1 1 ${100 / (KpiCards?.length || 1)}%`,
                    minWidth: 200,
                    maxWidth: 215,
                  }}
                >
                  <KPICard
                    events={{
                      allow: true,
                      type: "option",
                    }}
                    value={tile?.requestCount}
                    graphName={t(tile?.requestType)}
                    KPIColor={KPI_CARD_COLOR[tile.requestType]}
                  />
                </Grid>
              );
            })}
          </Grid>
        </Fade>

        {/* Controls Section */}
        <Fade in timeout={800}>
          <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
            <Box mb={2}>
              <TextField
                fullWidth
                placeholder="Search by request ID, requester, or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon color="action" />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    "&:hover fieldset": {
                      borderColor: "primary.main",
                    },
                    "&.Mui-focused fieldset": {
                      boxShadow: "0 0 0 3px rgba(59, 130, 246, 0.1)",
                    },
                  },
                }}
              />
            </Box>

            <Box display="flex" gap={1} flexWrap="wrap">
              {["ALL", "CREATE", "CHANGE", "EXTEND"].map((filter) => (
                <FilterChip
                  key={filter}
                  label={filter}
                  onClick={() => setSelectedFilter(filter)}
                  selected={selectedFilter === filter}
                  requestType={filter}
                  variant={selectedFilter === filter ? "filled" : "outlined"}
                />
              ))}
            </Box>
          </Paper>
        </Fade>

        {/* Timeline Section */}
        <ConsolidatedRequestTimeline
          filteredRequests={filteredRequests}
          selectedFilter={selectedFilter}
          materialNumber={materialNumber}
          expandedRequest={expandedRequest}
          setExpandedRequest={setExpandedRequest}
          animateCards={animateCards}
          handleDownloadPdf={handleDownloadPdf}
          setChangeLogModalOpen={setChangeLogModalOpen}
          setSelectedRequestForChangelog={setSelectedRequestForChangelog}
          module={module}
        />

        <ChangeLogRequestHistory
          open={changeLogModalOpen}
          closeModal={setChangeLogModalOpen}
          request={selectedRequestForChangelog}
        />
      </Container>
      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />
    </Box>
  );
};
export default ConsolidatedRequestHistory;
