import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { styled, Typography, Switch, FormControlLabel } from "@mui/material";
import { font_Small } from "../commonStyles";
import { setCurrentSAPSystem } from "../../../app/userManagementSlice";
import { destination_MaterialMgmt } from "../../../destinationVariables";
import { doAjax } from "../../Common/fetchService";
import { showToast } from "../../../functions";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { END_POINTS } from "../../../constant/apiEndPoints";
import { ERROR_MESSAGES } from "../../../constant/enum";

// Styled MaterialUISwitch
const MaterialUISwitch = styled(Switch)(({ theme }) => ({
    width: 62,
    height: 34,
    padding: 7,
    "& .MuiSwitch-switchBase": {
      margin: 1,
      padding: 0,
      transform: "translateX(6px)",
      "&.Mui-checked": {
        color: "#fff",
        transform: "translateX(22px)",
        "& .MuiSwitch-thumb:before": {
          content: '"S4"', // Text for toggled state
          fontSize: "14px",
          fontWeight: "bold",
          color: "#fff",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        },
        "& + .MuiSwitch-track": {
          opacity: 1,
          backgroundColor: "#8796A5",
          display: "flex",
          justifyContent: "flex-end", // Align text to the right
          alignItems: "center",
          padding: "0 10px",
        },
      },
    },
    "& .MuiSwitch-thumb": {
      backgroundColor: "#001e3c",
      width: 32,
      height: 32,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      "&::before": {
        content: '"ECC"', // Text for default state
        fontSize: "14px",
        fontWeight: "bold",
        color: "#fff",
      },
    },
    "& .MuiSwitch-track": {
      opacity: 1,
      backgroundColor: "#aab4be",
      borderRadius: 20 / 2,
      display: "flex",
      justifyContent: "flex-start", // Align text to the left
      alignItems: "center",
      padding: "0 10px",
    },
  }));
  

const SystemConfiguration = () => {
  const selectedSystem = useSelector((state) => state.userManagement.currentSAPSystem);
  const dispatch = useDispatch();

  useEffect(() => {
    const hSuccess = (data) => {
      if (data && data.SapSystem) {
        dispatch(setCurrentSAPSystem(data.SapSystem));
      }
    };
    
    const hError = (error) => {
      showToast(ERROR_MESSAGES.FAILED_FETCH_SAP_SYSTEM, "error");
    };
    
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.SYSTEM_CONFIG.GET_CURRENT_SAP_SYSTEM}`,
      "get",
      hSuccess,
      hError
    );
  }, [dispatch]);

  const handleChange = (event) => {
    const newSystem = event.target.checked ? "S4" : "ECC";

    dispatch(setCurrentSAPSystem(newSystem));
    
    const hSuccess = (data) => {
      showToast(data?.SapSystem, "success");
    };
    
    const hError = (error) => {
      showToast(ERROR_MESSAGES.FAILED_UPDATE_SAP_SYSTEM(newSystem), "error");
    };
    
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS.SYSTEM_CONFIG.UPDATE_SAP_SYSTEM}?system=${newSystem}`,
      "post",
      hSuccess,
      hError
    );
  };

  return (
    <div>
      <Typography sx={font_Small} >
        System Configuration (ECC/S4)
      </Typography>
      <FormControlLabel
        sx={{ margin: ".5em 0px" }}
        control={
          <MaterialUISwitch
            checked={selectedSystem === "S4"}
            onChange={handleChange}
          />
        }
      />
      <ToastContainer/>
    </div>
  );
};

export default SystemConfiguration;
