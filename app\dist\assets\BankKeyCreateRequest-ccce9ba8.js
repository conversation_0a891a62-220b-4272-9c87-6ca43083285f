import{o as vs,a as ds,cw as cs,s as us,u as ps,n as m,r as o,dq as st,cy as Ys,yO as zs,cA as Qs,cz as Ss,eU as jt,Au as Us,xi as Vs,aX as R,aO as F,j as t,c as g,O as ie,b1 as Xs,d as ve,aD as _s,B as W,an as he,aG as Gs,aH as Zs,aZ as Js,c5 as ea,C as Ae,ae as ta,Av as sa,Aw as aa,Ax as Je,dg as $e,aT as Le,bK as et,g as Hs,Ay as na,ge as bs,cI as be,Az as Ze,AA as Ft,T as Kt,a6 as mt,af as oa,ai as ra,aj as ia,al as rs,$ as la,bP as da,F as is,am as ls,d7 as Ks,fY as Ps,br as ws,qT as As,ad as $s,Z as ee,b6 as ca,b5 as ua,ag as pa,cH as ha,eb as fa,AB as Ws,AC as Ds,eV as tt,AD as ma,a9 as ga,t as as,aa as ns,xG as Ta,xH as Ea,aK as Rs,bO as Ca,bQ as xs,bG as Bs,AE as Pt,dn as Sa,fK as Is,da as Ns,bc as ys,y3 as _a,dp as ba,bd as os,AF as Aa,ap as Da,AG as Ra,AH as xa,d0 as ks,x$ as Ba,Aq as Ia,d2 as Na,d6 as ya,bf as ht,bI as Ms,J as ka,d8 as Ma,d3 as Oa,d4 as qa,d5 as La,d9 as va,aJ as ft,aW as Ua,AI as Ga,AJ as Ha,AK as Ka,fS as Pa,AL as wa,fP as wt,fQ as $t}from"./index-226a1e75.js";import{d as $a}from"./PermIdentityOutlined-842d404f.js";import{d as Wa}from"./FeedOutlined-2c089703.js";import{F as Fa}from"./FilterFieldGlobal-b5a561ef.js";import{D as ja,A as Ya,P as za,S as Qa,B as Va,g as Xa}from"./PreviewPage-262cf4cb.js";import{u as Fs}from"./useDownloadExcel-1a49cad3.js";import{F as Yt,C as Za}from"./ChangeLogGlobal-bc3b2dcd.js";import{S as Wt}from"./SingleSelectDropdown-ee61a6b7.js";import{d as Ja}from"./TaskAlt-9d2cabf1.js";import{G as en}from"./GenericTabsGlobal-6faba7da.js";import{d as Os}from"./DeleteOutlineOutlined-d41ebb56.js";import{d as qs,a as Ls}from"./CloseFullscreen-e3b32947.js";import{u as tn}from"./useBankKeyFieldConfig-85a40c89.js";import{u as js}from"./useDynamicWorkflowDT-7ae52689.js";import{d as sn}from"./LibraryAdd-cbd81e59.js";import{E as an}from"./ExcelOperationsCard-3cc40005.js";import{d as nn}from"./FileUploadOutlined-3ff8ee58.js";import{d as on}from"./TrackChangesTwoTone-f7d7fb26.js";import{u as rn,E as ln}from"./ErrorReportDialog-e2a11116.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./DatePicker-e5574363.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./AttachFile-fd8e4fbe.js";import"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";import"./CloudUpload-17ed0189.js";import"./utilityImages-067c3dc2.js";import"./Delete-3f2fc9ef.js";import"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";import"./Description-d98685cc.js";import"./DataObject-2e0c0294.js";import"./Download-f2e7dedd.js";import"./useFinanceCostingRows-699f667f.js";import"./CheckCircleOutline-70edf41f.js";import"./createChangeLogTemplate-774d7b1c.js";import"./CloudDownload-23cede9e.js";import"./AttachmentUploadDialog-5b2112e0.js";import"./lz-string-127b8448.js";import"./ErrorHistory-e3f4447c.js";const dn=({downloadClicked:ge,setIsSecondTabEnabled:Ue,setIsAttachmentTabEnabled:x,setDownloadClicked:at})=>{var Ke;const C=[{code:"Create"},{code:"Change"},{code:"Create with Upload"},{code:"Change with Upload"}],{getDtCall:T,dtData:V}=vs(),{t:U}=ds(),{showSnackbar:B}=cs(),{handleDownload:i,handleEmailDownload:De}=Fs((Ke=ea)==null?void 0:Ke.BK),M=us(),Re=ps(),a=new URLSearchParams(Re.search),O=a.get("reqBench"),Q=a.get("RequestId"),w=m(c=>c.bankKey.headerFieldsBnky||{}),le=m(c=>c.bankKey.requestHeaderID),q=m(c=>c.userManagement.userData),s=m(c=>c.bankKey.payload.requestHeaderData||{});m(c=>c.bankKey.requestHeaderResponse||{});const[te,I]=o.useState("systemGenerated"),[X,p]=o.useState(!1),[Te,xe]=o.useState(""),[Ge,Ee]=o.useState(!1);o.useEffect(()=>{M(st({keyName:"RequestPriority",data:Ys})),M(st({keyName:"TemplateName",data:zs})),M(st({keyName:"Region",data:Qs})),M(st({keyName:Ss.REQUEST_TYPE,data:C})),!Q&&!O&&(q!=null&&q.user_id)&&(M(jt({keyName:"ReqCreatedBy",data:q.user_id})),M(jt({keyName:"RequestStatus",data:"DRAFT"})))},[M,Q,O,q]),o.useEffect(()=>{We()},[s==null?void 0:s.RequestType]),o.useEffect(()=>{var c;if(V){const N={"Header Data":((c=V.result[0])==null?void 0:c.MDG_MAT_REQUEST_HEADER_CONFIG).sort((_,J)=>_.MDG_MAT_SEQUENCE_NO-J.MDG_MAT_SEQUENCE_NO).map(_=>({fieldName:_.MDG_MAT_UI_FIELD_NAME,sequenceNo:_.MDG_MAT_SEQUENCE_NO,fieldType:_.MDG_MAT_FIELD_TYPE,maxLength:_.MDG_MAT_MAX_LENGTH,visibility:_.MDG_MAT_VISIBILITY,jsonName:_.MDG_MAT_JSON_FIELD_NAME,value:_.MDG_MAT_DEFAULT_VALUE}))};M(Us(N)),M(Vs(N))}},[V]),o.useEffect(()=>{var c;if(ge){if((s==null?void 0:s.RequestType)===R.CREATE_WITH_UPLOAD){p(!0);return}if((s==null?void 0:s.RequestType)===((c=R)==null?void 0:c.CHANGE_WITH_UPLOAD)){setDialogOpen(!0);return}}},[ge]);const Be=()=>{var h,Z;let c=!0;return s&&((h=w[Object.keys(w)])!=null&&h.length)?(Z=w[Object.keys(w)[0]])==null||Z.forEach(N=>{var _;!s[N.jsonName]&&N.visibility===((_=_s)==null?void 0:_.MANDATORY)&&(c=!1)}):c=!1,c},We=()=>{var c,h,Z;T({decisionTableId:null,decisionTableName:"MDG_FMD_REQUEST_HEADER_CONFIG",version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":((c=s==null?void 0:s.RequestType)==null?void 0:c.code)||((h=R)==null?void 0:h.CREATE),"MDG_CONDITIONS.MDG_MAT_MODULE_NAME":(Z=F)==null?void 0:Z.BK}]})},Ie=()=>{var N;const c=(N=new Date(s==null?void 0:s.ReqCreatedOn))==null?void 0:N.getTime(),h=`/Date(${Date.now()})/`,Z={RequestId:le||"",ReqCreatedBy:(q==null?void 0:q.user_id)||"",ReqCreatedOn:c?`/Date(${c})/`:h,ReqUpdatedOn:c?`/Date(${c})/`:h,RequestType:(s==null?void 0:s.RequestType)||"",RequestPriority:(s==null?void 0:s.RequestPriority)||"",RequestDesc:(s==null?void 0:s.RequestDesc)||"",RequestStatus:(s==null?void 0:s.RequestStatus)||"DRAFT",TemplateName:(s==null?void 0:s.TemplateName)||"",FieldName:Array.isArray(s==null?void 0:s.FieldName)?s.FieldName.join("$^$"):"",Region:(s==null?void 0:s.Region)||"US",IsBifurcated:!1};Ae(`/${$e}${Le.BANKKEY_APIS.CREATE_BANKKEY_REQUEST_HEADER}`,"post",_=>{var Ce,Se,se;const J=(Ce=_==null?void 0:_.body)==null?void 0:Ce.requestId;if(B(`Request Header Created Successfully with request ID ${J}`,"success"),M(sa(_==null?void 0:_.body)),x(!0),M(aa({keyName:Ss.REQUEST_ID,data:(Se=_==null?void 0:_.body)==null?void 0:Se.requestId})),(s==null?void 0:s.RequestType)===R.CREATE_WITH_UPLOAD){p(!0);return}if((s==null?void 0:s.RequestType)===((se=R)==null?void 0:se.CHANGE_WITH_UPLOAD)){setDialogOpen(!0);return}M(Je(1)),Ue(!0)},_=>{var J;B((J=ta)==null?void 0:J.ERROR_REQUEST_HEADER,"error")},Z)},nt=()=>{var c,h;te==="systemGenerated"&&(i(xe,Ee,s,(c=F)==null?void 0:c.BK),je()),te==="mailGenerated"&&(De(xe,Ee,s,(h=F)==null?void 0:h.BK),je())},Fe=c=>{var h;I((h=c==null?void 0:c.target)==null?void 0:h.value)},je=()=>{var c;at(!1),p(!1),I("systemGenerated"),Q||navigate((c=et)==null?void 0:c.REQUEST_BENCH)},He=w["Header Data"]||[];return t("div",{children:g(Js,{spacing:2,children:[g(ie,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...Xs},children:[t(ve,{sx:{fontSize:12,fontWeight:"700",pb:1},children:U("Header Data")}),t(ie,{container:!0,spacing:1,children:He.filter(c=>{var h;return c.visibility!==((h=_s)==null?void 0:h.HIDDEN)}).sort((c,h)=>c.sequenceNo-h.sequenceNo).map(c=>t(Fa,{isHeader:!0,field:c,dropDownData:{},module:F.BK,disabled:Q||!!le,requestHeader:!0},c.jsonName))}),!Q&&!le&&t(W,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:t(he,{variant:"contained",color:"primary",onClick:Ie,disabled:!Be(),children:U("Save Request Header")})}),t(Gs,{blurLoading:Ge,loaderMessage:Te})]}),t(Zs,{}),t(ja,{onDownloadTypeChange:nt,open:X,downloadType:te,handleDownloadTypeChange:Fe,onClose:je})]})})},cn=o.forwardRef(({setIsAttachmentTabEnabled:ge,setCompleted:Ue,requestStatus:x},at)=>{var ss,It,ye,dt,Nt,Xe,yt,kt,Mt,Ot,qt,Lt,ct,vt,Ut,Gt,Ht;const C=us();Hs();const{t:T}=ds();m(e=>e==null?void 0:e.userManagement.taskData);const{selectedRowID:V,mandatoryFields:U}=m(e=>e.bankKey),B=m(e=>e.bankKey),i=m(e=>e.bankKey.bankKeyTabs||[]),De=ps(),M=new URLSearchParams(De.search),Re=M.get("RequestId"),a=M.get("reqBench"),O=(De==null?void 0:De.state)||{},Q=m(e=>e.bankKey.isOpenDialog),{loading:w,error:le,fetchBankKeyFieldConfig:q}=tn(),s=m(e=>e.bankKey.payload.rowsHeaderData),te=m(e=>{var n;return((n=e.bankKey.payload)==null?void 0:n.rowsBodyData)||{}}),I=m(e=>{var n;return((n=e.bankKey.payload)==null?void 0:n.requestHeaderData)||{}}),X=m(e=>e.bankKey.payload);(ss=X==null?void 0:X.requestHeaderData)==null||ss.RequestType,m(e=>e.changeLog.createChangeLogDataBK||{}),m(e=>e.payload.filteredButtons);const[p,Te]=o.useState(null),[xe,Ge]=o.useState(0);o.useState([]);const[Ee,Be]=o.useState(!1),[We,Ie]=o.useState([]),[nt,Fe]=o.useState(!1),[je,He]=o.useState(!1),[Ke,c]=o.useState(""),[h,Z]=o.useState(!1),[N,_]=o.useState(!1),[J,Ce]=o.useState("yes"),[Se,se]=o.useState(""),[ot,gt]=o.useState("");o.useState(null),o.useState({});const[j,Ye]=o.useState({data:{},isVisible:!1}),[ze,Tt]=o.useState(),[Et,Ct]=o.useState({open:!1,message:"",title:""}),[hs,Qe]=o.useState(!1),[zt,rt]=o.useState(!1),{showSnackbar:St}=cs();M.get("reqBench");const de=a&&!ha.includes(x);m(e=>e.userManagement.taskData),m(e=>e.applicationConfig),o.useState([]),js();const[_t,ae]=o.useState({}),bt=na(),[Ne,Qt]=o.useState();o.useState(""),o.useState(null),o.useEffect(()=>{if(s){const e=bs(s);Fe(e)}},[s]);const fe=e=>{if(e){const l=`${(I==null?void 0:I.Region)||fa.US}-${e}`;l in i||(q(l,e),Ws(e,C))}};o.useEffect(()=>{var e,n;(Re&&!a||a&&(O==null?void 0:O.reqStatus)!==((e=be)==null?void 0:e.DRAFT)||a&&(O==null?void 0:O.reqStatus)===((n=be)==null?void 0:n.DRAFT)&&(O==null?void 0:O.objectNumbers)!=="Not Available")&&C(Ze(!1))},[]),o.useEffect(()=>{var e,n;(e=s==null?void 0:s[0])!=null&&e.id&&fe((n=s==null?void 0:s[0])==null?void 0:n.BankCtry)},[s]);const[Vt,ce]=o.useState(!1);o.useEffect(()=>{Q&&!Vt&&ce(!0)},[Q]);const At=async e=>{var f;const n=[],l=[];let u=null,E=null;for(const L of Ds||[]){let b=e[L.jsonName];(b==null||typeof b=="string"&&b.trim()==="")&&(n.push(L.fieldName),u||(u=L.jsonName,E=0))}const y=(U==null?void 0:U[`${I==null?void 0:I.Region}-${e==null?void 0:e.BankCtry}`])||{};let P=0;for(const L of Object.keys(y)){const b=y[L];for(const Y of b){let z=(f=te==null?void 0:te[e.id])==null?void 0:f[Y.jsonName];(z==null||typeof z=="string"&&z.trim()==="")&&(l.push(Y.jsonName),n.push(Y.fieldName),u||(u=Y.jsonName,E=P))}P++}if(n.length>0){const L=s.map(b=>b.id===e.id?{...b,validated:!1}:b);return C(tt(L)),Ge(E),Te(e),C(Ft(e==null?void 0:e.id)),Ie(n),Be(!0),ae(b=>({...b,[e.id]:l})),{validated:!1,rowId:e.id,missing:n,errorTabIndex:E,errorField:u}}try{const L=e.BankCtry,b=e.BankKey,Y=e.BankName;if(L&&b){const $=await Xt(L,b,Y);if($.isDuplicate){const ne=s.map(ke=>ke.id===e.id?{...ke,validated:!1}:ke);C(tt(ne)),Ct({open:!0,message:$.message,title:"Duplicate Bank Details Found"});return}}const z=s.map($=>$.id===e.id?{...$,validated:!0}:$);C(tt(z)),ae($=>{const ne={...$};return delete ne[e.id],ne}),Ue([!0,!0]),ge(!0);const A=bs(z);Fe(A)}catch{St("Validation failed due to an error","error")}return{validated:!0,row:e}};o.useImperativeHandle(at,()=>({validateRow:At}));const Xt=(e,n,l)=>new Promise((u,E)=>{var Y,z;const y=[{bankCtry:e,bankKey:n,requestNo:((Y=B==null?void 0:B.requestHeaderResponse)==null?void 0:Y.requestId)||(I==null?void 0:I.RequestId)||"",bankName:l}],P=A=>{var $;if(($=A==null?void 0:A.body)!=null&&$.length){const ne=`Duplicate bank details found: ${A.body[0].split("$^$")[0]} (${A.body[0].split("$^$")[1]})`;u({isDuplicate:!0,message:ne})}else u({isDuplicate:!1,message:""})},f=A=>{customError(A),u({isDuplicate:!1,message:""})};let L=0,b="";s==null||s.forEach(A=>{A!=null&&A.BankCtry&&(A!=null&&A.BankKey)&&(A==null?void 0:A.BankCtry)===e&&(A==null?void 0:A.BankKey)===n&&L++}),L>1?(b=`Duplicate bank details found locally: ${e} - ${n}`,u({isDuplicate:!0,message:b})):Ae(`/${$e}${(z=Le.MASS_ACTION)==null?void 0:z.BANK_DUPLICATE_CHECK}`,"post",P,f,y)}),Dt=()=>{var l;const e=j==null?void 0:j.data;C(tt(s==null?void 0:s.filter(u=>{var E;return u.id!==((E=e==null?void 0:e.row)==null?void 0:E.id)})));let n={...te};delete n[(l=e==null?void 0:e.row)==null?void 0:l.id],C(ma(n)),Te(s==null?void 0:s[0]),Ye({...j,isVisible:!1})},Pe=e=>{var n;return(n=Ds)==null?void 0:n.some(l=>l.jsonName===e)},Rt=e=>{rt(!0);const n=`/${$e}/data/getBankKeyBasedOnCountry`;Ae(n,"post",l=>{rt(!1),C(st({keyName:"BankKey",data:(l==null?void 0:l.body)||[]}))},()=>{rt(!1)},{bankCtry:e})},Ve=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:e=>t(ga,{checked:e.row.included,disabled:de,onChange:n=>we(n.target.checked,e.row.id,"included")})},{field:"lineNumber",headerName:"Line Number",flex:.5,align:"center",headerAlign:"center",renderCell:e=>{const n=s.findIndex(l=>l.id===e.row.id);return t("div",{children:(n+1)*10})}},{field:"BankCtry",flex:1,headerAlign:"center",renderHeader:()=>g("span",{children:[T("Bank Ctry/Reg."),Pe("BankCtry")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{var n,l,u;return t(Wt,{options:((n=B==null?void 0:B.dropDownData)==null?void 0:n.BankCtry)||[],value:((u=(l=B==null?void 0:B.dropDownData)==null?void 0:l.BankCtry)==null?void 0:u.find(E=>E.code===e.row.BankCtry))||null,onChange:E=>we(E==null?void 0:E.code,e.row.id,"BankCtry",e),placeholder:T("Select Bank Country"),disabled:de,minWidth:"90%",listWidth:235})}},{field:"BankKey",flex:1,headerAlign:"center",renderHeader:()=>g("span",{children:[T("Bank Key"),Pe("BankKey")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{const n=e.row.BankKey||"",l=9,u=n.length,[E,y]=as.useState(!1);return t(W,{sx:{position:"relative",width:"100%"},children:t(ns,{value:n,onChange:P=>{const f=P.target.value.toUpperCase();/^\d*$/.test(f)&&f.length<=l&&we(f,e.row.id,"BankKey")},onFocus:()=>y(!0),onBlur:()=>y(!1),inputProps:{maxLength:l},variant:"outlined",size:"small",placeholder:"Enter Bank Key",disabled:de,fullWidth:!0,helperText:E&&(u===l?"Max Length Reached":`${u}/${l}`),FormHelperTextProps:{sx:{color:E&&u===l?"red":"blue",position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-input":{padding:"10px 14px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ee.black.dark,color:ee.black.dark}}}})})}},{field:"BankName",flex:1,headerAlign:"center",renderHeader:()=>g("span",{children:[T("Bank Name"),Pe("BankName")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{const n=e.row.BankName||"",l=60,u=n.length,[E,y]=as.useState(!1);return t(W,{sx:{position:"relative",width:"100%"},children:t(ns,{value:n,onChange:P=>{const f=P.target.value.toUpperCase();f.length<=l&&we(f,e.row.id,"BankName")},onFocus:()=>y(!0),onBlur:()=>y(!1),inputProps:{maxLength:l},variant:"outlined",size:"small",placeholder:"Enter Bank Name",disabled:de,fullWidth:!0,helperText:E&&(u===l?"Max Length Reached":`${u}/${l}`),FormHelperTextProps:{sx:{color:E&&u===l?"red":"blue",position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-input":{padding:"10px 14px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ee.black.dark,color:ee.black.dark}}}})})}},{field:"BankBranch",flex:1,headerAlign:"center",renderHeader:()=>g("span",{children:[T("Bank Branch"),Pe("BankBranch")&&t("span",{style:{color:"red"},children:" *"})]}),renderCell:e=>{const n=e.row.BankBranch||"",l=40,u=n.length,[E,y]=as.useState(!1);return t(W,{sx:{position:"relative",width:"100%"},children:t(ns,{value:n,onChange:P=>{const f=P.target.value.toUpperCase();f.length<=l&&we(f,e.row.id,"BankBranch")},onFocus:()=>y(!0),onBlur:()=>y(!1),inputProps:{maxLength:l},variant:"outlined",size:"small",placeholder:"Enter Bank Branch",disabled:de,fullWidth:!0,helperText:E&&(u===l?"Max Length Reached":`${u}/${l}`),FormHelperTextProps:{sx:{color:E&&u===l?"red":"blue",position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-input":{padding:"10px 14px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ee.black.dark,color:ee.black.dark}}}})})}},{field:"action",headerName:"Action",flex:.5,headerAlign:"center",align:"center",renderHeader:()=>t("span",{style:{fontWeight:"bold"},children:T("Action")}),renderCell:e=>{let n=e.row.id,l=Ta(e==null?void 0:e.row);return g(W,{children:[t(Kt,{title:l==="success"?"Validated Successfully":l==="error"?"Validation Failed":"Click to Validate",children:t(mt,{disabled:de,onClick:u=>{u.stopPropagation(),At(e.row)},color:l==="success"?"success":l==="error"?"error":"default",children:l==="error"?t(Ea,{}):t(Ja,{})})}),t(Kt,{title:T("Delete Row"),disabled:de||/^\d+$/.test(String(n)),children:t(mt,{onClick:()=>{Ye({...j,data:e,isVisible:!0})},color:"error",children:t(Os,{})})})]})}}],xt=()=>{C(Ze(!1))},Zt=()=>{Z(!h),N&&_(!1)},fs=()=>{_(!N),h&&Z(!1)},we=(e,n,l,u)=>{l==="BankCtry"&&fe(e),l==="BankName"?C(jt({uniqueId:V||(p==null?void 0:p.id),keyName:"Name",data:e})):l==="BankKey"&&C(jt({uniqueId:V||(p==null?void 0:p.id),keyName:"BankKey",data:e}));const E=s.map(y=>y.id===n?{...y,[l]:e,validated:"default"}:y);if(C(tt(E)),u!=null&&u.row){let y=JSON.parse(JSON.stringify(u==null?void 0:u.row));y[l]=e,Bt({row:y})}},Jt=()=>{const e=Rs();Tt(e),Qe(!0),Ce("yes"),C(Ze(!0))},it=(e,n)=>{Ge(n)};o.useEffect(()=>{var e,n;p!=null&&p.BankCtry||(Te(s[0]),C(Ft((e=s[0])==null?void 0:e.id)),Tt((n=s[0])==null?void 0:n.id))},[s]),o.useState({});const lt=async(e,n)=>{He(!0);const l=`/${$e}/data/displayBankKeySAPData`;var u={bankCtry:e,bankKey:n};Ae(l,"post",P=>{var z,A;He(!1);const f=(P==null?void 0:P.body[0])||{};let L={...te},b=[];const Y=ze||"";if(Y){if(Y==="first_request_id_requestor")b==null||b.push({id:ze||"",BankCtry:(f==null?void 0:f.BankCtry)||Se,BankKey:"",BankName:"",BankBranch:((z=f==null?void 0:f.Tobankaddress)==null?void 0:z.BankBranch)||"",included:!0,validated:"default"});else{b=[...s];const $={id:ze||"",BankCtry:(f==null?void 0:f.BankCtry)||Se,BankKey:"",BankName:"",BankBranch:((A=f==null?void 0:f.Tobankaddress)==null?void 0:A.BankBranch)||"",included:!0,validated:"default"},ne=s.findIndex(ke=>ke.id===ze);ne!==-1?b[ne]=$:b.push($)}L[Y]={...f,...(f==null?void 0:f.Tobankaddress)||{},BankNo:"",Name:""},C(Pt({keyName:"rowsHeaderData",data:b})),C(Pt({keyName:"rowsBodyData",data:L})),se(""),gt("")}},()=>{He(!1)},u)},es=async()=>{J==="no"?(C(Ze(!1)),Qe(!1)):Ne?ts(Ne):(await lt(Se,ot),C(Ze(!1)),Qe(!1))},ts=e=>{const n=Rs(),l={...te},u=l[e]||{},E=s.find(L=>L.id===e)||{},y=u||{};l[n]={...y};const P={id:n,BankCtry:E.BankCtry||"",BankKey:"",BankName:"",BankBranch:E.BankBranch||"",included:!0,validated:"default"},f=[...s,P];C(Pt({keyName:"rowsBodyData",data:l})),C(Pt({keyName:"rowsHeaderData",data:f})),C(Ze(!1)),St("success with copy from line number","success")},Bt=e=>{const n=e.row;fe(n==null?void 0:n.BankCtry),Te(n),C(Ft(n==null?void 0:n.id))};return g("div",{children:[le&&t(ve,{color:"error",children:T("Error loading data")}),t("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:g(W,{sx:{position:h?"fixed":"relative",top:h?0:"auto",left:h?0:"auto",right:h?0:"auto",bottom:h?0:"auto",width:h?"100vw":"100%",height:h?"100vh":"auto",zIndex:h?1004:0,backgroundColor:h?"white":"transparent",padding:h?"20px":"0",display:"flex",flexDirection:"column",boxShadow:h?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[g(W,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[t(ve,{variant:"h6",children:T("List of Bank Keys")}),g(W,{sx:{display:"flex",alignItems:"center",gap:1},children:[t(he,{variant:"contained",color:"primary",size:"small",onClick:Jt,disabled:!nt||de||Re&&!a,children:T("+ Add")}),t(Kt,{title:h?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(mt,{onClick:Zt,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:h?t(qs,{}):t(Ls,{})})})]})]}),t("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:t("div",{style:{height:"100%"},children:t("div",{children:t(oa,{isLoading:w,rows:s,columns:Ve,pageSize:10,rowHeight:70,tempheight:"50vh",getRowIdValue:"id",status_onRowSingleClick:!0,callback_onRowSingleClick:Bt,showFilter:!1,showColumns:!1,rowSelectionModel:V?[V]:[]})})})})]})}),Q&&g(ra,{fullWidth:!0,open:Q,maxWidth:"lg",onClose:xt,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[t(ia,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0.75rem 1rem",background:e=>e.palette.primary.light,borderBottom:"1px solid #d6d6f0"},children:g(W,{sx:{display:"flex",alignItems:"center"},children:[t(sn,{sx:{mr:1,color:bt.palette.primary.dark}}),t(ve,{variant:"h6",sx:{fontWeight:600,color:bt.palette.primary.dark},children:"Add New Bank Key"})]})}),g(rs,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[g(la,{component:"fieldset",sx:{paddingBottom:"2%"},children:[t(Ca,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:"Do You Want To Continue"}),g(da,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:J,onChange:e=>Ce(e.target.value),children:[t(Bs,{value:"yes",control:t(xs,{}),label:"With Reference"}),t(Bs,{value:"no",control:t(xs,{}),label:"Without Reference"})]})]}),g(ie,{container:!0,spacing:2,children:[t(ie,{item:!0,xs:12,children:g(ie,{container:!0,spacing:2,children:[t(ie,{item:!0,xs:3,children:t(Wt,{options:((It=B==null?void 0:B.dropDownData)==null?void 0:It.BankCtry)||[],value:((dt=(ye=B==null?void 0:B.dropDownData)==null?void 0:ye.BankCtry)==null?void 0:dt.find(e=>e.code===Se))||null,onChange:e=>{se((e==null?void 0:e.code)||""),Rt(e==null?void 0:e.code)},placeholder:"Select Bank Country",minWidth:"90%",listWidth:235,disabled:J==="no"})}),t(ie,{item:!0,xs:3,children:t(Wt,{options:((Nt=B==null?void 0:B.dropDownData)==null?void 0:Nt.BankKey)||[],value:((yt=(Xe=B==null?void 0:B.dropDownData)==null?void 0:Xe.BankKey)==null?void 0:yt.find(e=>e.code===ot))||null,onChange:e=>{gt((e==null?void 0:e.code)||"")},isLoading:zt,placeholder:"Select Bank Key",disabled:J==="no",minWidth:"90%",listWidth:235})})]})}),s[0].validated===!0&&g(is,{children:[t(ie,{item:!0,xs:12,children:t(W,{sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"10px 0"},children:t(ve,{variant:"body2",sx:{color:"#666",fontWeight:"bold"},children:"OR"})})}),t(ie,{item:!0,xs:12,children:t(ie,{container:!0,spacing:2,children:t(ie,{item:!0,xs:6,children:t(Wt,{options:s.map((e,n)=>{const l=(n+1)*10;return{code:l.toString(),desc:`${l}`}}),value:Ne?(()=>s.find(n=>n.id===Ne)?{code:((s.findIndex(u=>u.id===Ne)+1)*10).toString()}:null)():null,onChange:e=>{const l=parseInt((e==null?void 0:e.code)||"0")/10-1,u=s[l];Qt((u==null?void 0:u.id)||"")},placeholder:"SELECT BANK KEY LINE NUMBER",minWidth:"90%",listWidth:450})})})})]})]})]}),g(ls,{sx:{display:"flex",justifyContent:"end"},children:[t(he,{onClick:xt,variant:"outlined",children:"Cancel"}),t(he,{type:"save",disabled:!(Se&&ot)&&J==="yes"&&!Ne,onClick:es,variant:"contained",children:"Proceed"})]})]}),t(Yt,{open:Ee,onClose:()=>Be(!1),missingFields:We,t:T}),t(Yt,{open:Et.open,onClose:()=>Ct({open:!1,message:"",title:""}),customMessage:Et.message,title:Et.title,t:T}),(j==null?void 0:j.isVisible)&&g(Ks,{isOpen:j==null?void 0:j.isVisible,titleIcon:t(Os,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:T("Delete Row")+"!",handleClose:()=>Ye({...j,isVisible:!1}),children:[t(rs,{sx:{mt:2},children:T(Ps.DELETE_MESSAGE)}),g(ls,{children:[t(he,{variant:"outlined",size:"small",sx:{...ws},onClick:()=>Ye({...j,isVisible:!1}),children:T(As.CANCEL)}),t(he,{variant:"contained",size:"small",sx:{...$s},onClick:Dt,children:T(As.DELETE)})]})]}),p&&g(W,{sx:{position:N?"fixed":"relative",top:N?0:"auto",left:N?0:"auto",right:N?0:"auto",bottom:N?0:"auto",width:N?"100vw":"100%",height:N?"100vh":"auto",zIndex:N?1004:0,backgroundColor:N?"white":"transparent",padding:N?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:N?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[g(W,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[t(ve,{variant:"h6",children:T("View Details")}),t(Kt,{title:N?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:t(mt,{onClick:fs,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:N?t(qs,{}):t(Ls,{})})})]}),g(W,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[t(ua,{value:xe,onChange:it,indicatorColor:"primary",textColor:"primary","aria-label":"Request tabs",variant:"scrollable",scrollButtons:"auto",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:ee.background.container,borderBottom:`1px solid ${ee.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:ee.black.graphite,"&.Mui-selected":{color:ee.primary.main,fontWeight:700},"&:hover":{color:ee.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:ee.primary.main,height:"3px"}},children:(kt=i==null?void 0:i[`${I==null?void 0:I.Region}-${p==null?void 0:p.BankCtry}`])==null?void 0:kt.map((e,n)=>t(ca,{label:e.tab},n))}),t(pa,{elevation:2,sx:{p:3,borderRadius:4},children:((Mt=i==null?void 0:i[`${I==null?void 0:I.Region}-${p==null?void 0:p.BankCtry}`])==null?void 0:Mt[xe])&&t(en,{disabled:de,basicDataTabDetails:(Ot=i==null?void 0:i[`${I==null?void 0:I.Region}-${p==null?void 0:p.BankCtry}`])==null?void 0:Ot[xe].data,activeViewTab:(qt=i==null?void 0:i[`${I==null?void 0:I.Region}-${p==null?void 0:p.BankCtry}`])==null?void 0:qt[xe].tab,uniqueId:(p==null?void 0:p.id)||V||((Lt=s[0])==null?void 0:Lt.id),selectedRow:p||{},module:(ct=F)==null?void 0:ct.BK,fieldErrors:_t[(p==null?void 0:p.id)||V||((vt=s[0])==null?void 0:vt.id)]||[],missingFieldsDialogOpen:Ee},((p==null?void 0:p.id)||V||((Ut=s[0])==null?void 0:Ut.id))+(((Ht=_t[(p==null?void 0:p.id)||V||((Gt=s[0])==null?void 0:Gt.id)])==null?void 0:Ht.join(","))||""))}),t(W,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"}})]})]}),t(Gs,{blurLoading:je,loaderMessage:Ke})]})}),ao=()=>{var E,y,P,f,L,b,Y,z,A,$,ne,ke,ms,gs,Ts,Es,Cs;const ge=Hs(),Ue=ps(),x=us(),{getDtCall:at,dtData:C}=vs(),{t:T}=ds(),U=new URLSearchParams(Ue.search.split("?")[1]).get("RequestId"),B=[T("Request Header"),T("Bank Key List"),T("Attachments & Remarks"),T("Preview")],i=Ue.state,De=m(r=>r.bankKey.requestHeaderResponse),M=m(r=>{var d;return((d=r.bankKey.requestHeaderResponse)==null?void 0:d.requestId)||""}),Re=m(r=>{var d;return((d=r.bankKey.requestHeaderResponse)==null?void 0:d.requestType)||""}),a=m(r=>r.bankKey.payload),O=m(r=>r.bankKey.tabValue),Q=new URLSearchParams(Ue.search),w=Q.get("reqBench"),le=Q.get("RequestId"),q=Q.get("RequestType"),s=m(r=>{var d;return(d=r.bankKey)==null?void 0:d.isOdataApiCalled}),{fetchAllDropdownFMD:te}=Sa($e,st),I=m(r=>r.applicationConfig),X=m(r=>r.bankKey),[p,Te]=o.useState(!1),[xe,Ge]=o.useState([!1]),[Ee,Be]=o.useState(!1),[We,Ie]=o.useState(!1),[nt,Fe]=o.useState([]),[je,He]=o.useState([]),[Ke,c]=o.useState(!1),[h,Z]=o.useState(!1),[N,_]=o.useState(!1),[J,Ce]=o.useState(""),[Se,se]=o.useState(!1),{handleUploadMaterial:ot}=Fs((E=F)==null?void 0:E.BK),[gt,j]=o.useState(!1),[Ye,ze]=o.useState(""),[Tt,Et]=o.useState(!1),{getButtonsDisplayGlobal:Ct,showWfLevels:hs}=rn(),[Qe,zt]=o.useState(""),[rt,St]=o.useState(""),[de,_t]=o.useState([]),ae=m(r=>{var d;return((d=r.bankKey.payload)==null?void 0:d.requestHeaderData)||{}}),bt=Q.get("RequestId");let Ne=m(r=>r==null?void 0:r.userManagement.taskData);const Qt=m(r=>r.changeLog.createChangeLogDataBK||{}),fe=m(r=>r.bankKey),{getDynamicWorkflowDT:Vt}=js();let ce=m(r=>r.userManagement.taskData);const[At,Xt]=o.useState([]),Dt=m(r=>r.payload.filteredButtons),{showSnackbar:Pe}=cs(),Rt=m(r=>r.bankKey.payload.rowsHeaderData);m(r=>{var d;return((d=r.bankKey.payload)==null?void 0:d.rowsBodyData)||{}}),m(r=>r.bankKey);const[Ve,xt]=o.useState(!1),[Zt,fs]=o.useState([]),[we,Jt]=o.useState(0),it=o.useRef(null),[lt,es]=o.useState({open:!1,message:"",title:""});o.useState(null),o.useState({});const ts=(i==null?void 0:i.childRequestIds)!=="Not Available",Bt=Is(X==null?void 0:X.payload,De),[ss,It]=o.useState(""),[ye,dt]=o.useState({title:"",message:"",subText:"",buttonText:"",redirectTo:""}),[Nt,Xe]=o.useState(!1),yt={region:(a==null?void 0:a.Region)||"US",scenario:(a==null?void 0:a.RequestType)===((y=R)==null?void 0:y.CHANGE)||(a==null?void 0:a.RequestType)===((P=R)==null?void 0:P.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",bankCtry:(a==null?void 0:a.BankCtry)||"US",dtName:((a==null?void 0:a.RequestType)===((f=R)==null?void 0:f.CHANGE)||(a==null?void 0:a.RequestType)===((L=R)==null?void 0:L.CHANGE_WITH_UPLOAD)||(a==null?void 0:a.RequestType)===((b=R)==null?void 0:b.CREATE)||(a==null?void 0:a.RequestType)===((Y=R)==null?void 0:Y.CREATE_WITH_UPLOAD),"MDG_BNKY_FIELD_CONFIG"),version:((a==null?void 0:a.RequestType)===((z=R)==null?void 0:z.CREATE)||(a==null?void 0:a.RequestType)===((A=R)==null?void 0:A.CREATE_WITH_UPLOAD),"v2"),rolePrefix:($=Ns)==null?void 0:$.REQ_INITIATE_DOWNLOAD,requestId:w&&!ts?U:"",templateName:(a==null?void 0:a.TemplateName)||"",bankKeyDetails:Bt};o.useEffect(()=>{Ee&&Ge([!0])},[Ee]),o.useEffect(()=>{U||x(Je(0)),Ht(),ys(ht.MODULE,F.BK),vt(),ze(_a("BK"))},[]),o.useEffect(()=>(s||(te("bankKey"),x(ba(!0))),ys(ht.MODULE,F.BK),()=>{os(ht.MODULE),os(ht.CURRENT_TASK),os(ht.ROLE)}),[]),o.useEffect(()=>{var r;if(C){const G={"Header Data":((r=C==null?void 0:C.result[0])==null?void 0:r.MDG_MAT_REQUEST_HEADER_CONFIG).sort((H,oe)=>H.MDG_MAT_SEQUENCE_NO-oe.MDG_MAT_SEQUENCE_NO).map(H=>({fieldName:H.MDG_MAT_UI_FIELD_NAME,sequenceNo:H.MDG_MAT_SEQUENCE_NO,fieldType:H.MDG_MAT_FIELD_TYPE,maxLength:H.MDG_MAT_MAX_LENGTH,value:H.MDG_MAT_DEFAULT_VALUE,visibility:H.MDG_MAT_VISIBILITY,jsonName:H.MDG_MAT_JSON_FIELD_NAME}))};x(Us(G))}},[C]);const kt=r=>{x(Je(r))},Mt=()=>{var r,d,D;U&&!w?ge((r=et)==null?void 0:r.MY_TASK):w?ge((d=et)==null?void 0:d.REQUEST_BENCH):!U&&!w&&ge((D=et)==null?void 0:D.BANKKEY)},Ot=()=>{_(!0)},qt=()=>{c(!0)},Lt=r=>{var d;ot(r,Ce,se,a,(d=F)==null?void 0:d.BK,q,U,i)},ct=()=>{Te(!1)},vt=()=>{var G,H,oe,ue;let r={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};const d=v=>{var re,S,me;if(v.statusCode===((re=ft)==null?void 0:re.STATUS_200)){const K=((me=(S=v==null?void 0:v.data)==null?void 0:S.result[0])==null?void 0:me.MDG_ATTACHMENTS_ACTION_TYPE)||[];Fe(K)}},D=v=>{};I.environment==="localhost"?Ae(`/${Ms}${(H=(G=Le)==null?void 0:G.INVOKE_RULES)==null?void 0:H.LOCAL}`,"post",d,D,r):Ae(`/${Ms}${(ue=(oe=Le)==null?void 0:oe.INVOKE_RULES)==null?void 0:ue.PROD}`,"post",d,D,r)},Ut=()=>{var oe,ue,v,re,S,me,_e,K,k;se(!0),Ce((oe=Ua)==null?void 0:oe.REPORT_LOADING);const r=le?Le.EXCEL.EXPORT_EXCEL_BK:Le.EXCEL.EXPORT_EXCEL_BK,d=(i==null?void 0:i.childRequestIds)!=="Not Available";let D={dtName:((a==null?void 0:a.RequestType)===((ue=R)==null?void 0:ue.CHANGE)||(a==null?void 0:a.RequestType)===((v=R)==null?void 0:v.CHANGE_WITH_UPLOAD),"MDG_BNKY_FIELD_CONFIG"),version:((a==null?void 0:a.RequestType)===((re=R)==null?void 0:re.CHANGE)||(a==null?void 0:a.RequestType)===((S=R)==null?void 0:S.CHANGE_WITH_UPLOAD),"v2"),parentRequestId:w&&!d?U:"",childRequestId:!w&&U||w&&d?U:"",scenario:(a==null?void 0:a.RequestType)===((me=R)==null?void 0:me.CHANGE)||(a==null?void 0:a.RequestType)===((_e=R)==null?void 0:_e.CHANGE_WITH_UPLOAD)||(a==null?void 0:a.RequestType)===((K=R)==null?void 0:K.CREATE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(a==null?void 0:a.TemplateName)||"",region:(a==null?void 0:a.Region)||"US",bankCtry:(a==null?void 0:a.BankCtry)||"US",rolePrefix:(k=Ns)==null?void 0:k.REQ_INITIATE_DOWNLOAD,templateName:""};const G=Me=>{const Oe=URL.createObjectURL(Me),pe=document.createElement("a");pe.href=Oe,pe.setAttribute("download","Bank Key_Data Export.xlsx"),document.body.appendChild(pe),pe.click(),document.body.removeChild(pe),URL.revokeObjectURL(Oe),se(!1),Ce(""),setSuccessMsg(!0),setAlertType("success"),handleSnackBarOpen()},H=()=>{};Ae(`/${$e}${r}`,"postandgetblob",G,H,D)},Gt=async r=>{var oe,ue;const d=(i==null?void 0:i.childRequestIds)!=="Not Available",D=w?{sort:"id,asc",childRequestId:d?r:"",parentRequestId:d?"":r,page:0,size:10}:{sort:"id,asc",childRequestId:d?r:"",parentRequestId:d?"":r,page:0,size:10},G=v=>{var k,Me,Oe,pe,ut,pt;const re=(v==null?void 0:v.body)||[];let S=(k=v==null?void 0:v.body[0])==null?void 0:k.Torequestheaderdata,me=(Me=v==null?void 0:v.body[0])==null?void 0:Me.TotalIntermediateTasks;x(Ga({RequestId:S.RequestId,RequestPrefix:S.RequestPrefix,ReqCreatedBy:S.ReqCreatedBy,ReqCreatedOn:S.ReqCreatedOn,ReqUpdatedOn:S.ReqUpdatedOn,RequestType:S.RequestType,RequestDesc:S.RequestDesc,RequestStatus:S.RequestStatus,RequestPriority:S.RequestPriority,FieldName:S.FieldName,TemplateName:S.TemplateName,Division:S.Division,region:S.region,leadingCat:S.leadingCat,firstProd:S.firstProd,launchDate:S.launchDate,isBifurcated:S.isBifurcated,screenName:S.screenName,TotalIntermediateTasks:me})),He(re);const _e=S==null?void 0:S.RequestType;(_e===R.CHANGE||_e===R.CHANGE_WITH_UPLOAD)&&getChangeTemplate();const K=Ha(re);x(Ft((pe=(Oe=K==null?void 0:K.payload)==null?void 0:Oe.rowsHeaderData[0])==null?void 0:pe.id)),x(Ka(K==null?void 0:K.payload)),x(Pa({keyName:"childRequestHeaderData",data:(ut=K==null?void 0:K.payload)==null?void 0:ut.childRequestHeaderData})),x(ks((pt=K==null?void 0:K.payload)==null?void 0:pt.rowsBodyData)),re.forEach(qe=>{qe.Tobankaddress.Country&&(wa(qe.Tobankaddress.Country,x,qe.Tobankaddress.AddrRegion,qe.Tobankaddress.Transpzone),Ws(qe.Tobankaddress.Country,x))})},H=v=>{};Ae(`/${$e}/${(ue=(oe=Le)==null?void 0:oe.CHG_DISPLAY_REQUESTOR)==null?void 0:ue.DISPLAY_BK}`,"post",G,H,D)};o.useEffect(()=>((async()=>{le?(await Gt(le),(q===R.CHANGE_WITH_UPLOAD&&!(i!=null&&i.length)||q===R.CREATE_WITH_UPLOAD)&&((i==null?void 0:i.reqStatus)===be.DRAFT||(i==null?void 0:i.reqStatus)===be.UPLOAD_FAILED)?(x(Je(0)),Be(!1),Ie(!1)):(x(Je(1)),Be(!0),Ie(!0))):x(Je(0))})(),()=>{x(Aa()),x(Da({})),x(Ra()),x(xa()),x(ks({})),x(Ba())}),[U,x]);const Ht=()=>{var d,D;let r={decisionTableId:null,decisionTableName:ka.MDG_FMD_REQUEST_HEADER_CONFIG,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(d=R)==null?void 0:d.CREATE,"MDG_CONDITIONS.MDG_MAT_MODULE_NAME":(D=F)==null?void 0:D.BK}]};at(r)},e=async(r,d)=>{var ue,v,re,S,me,_e,K;if(r==="VALIDATE"&&!await u())return;se(!0);let D=(K=(_e=(re=(ue=Le)==null?void 0:ue.MASTER_BUTTON_APIS)==null?void 0:re[(v=F)==null?void 0:v.BK])==null?void 0:_e[(me=(S=fe==null?void 0:fe.payload)==null?void 0:S.requestHeaderData)==null?void 0:me.RequestType])==null?void 0:K[r];const G=Is(a,fe==null?void 0:fe.requestHeaderResponse,bt,Ne,d,Qt,Qe),H=k=>{var Oe,pe,ut,pt,qe;se(!1);let Me;U&&!w?Me=(Oe=et)==null?void 0:Oe.MY_TASK:Me=wt.REDIRECT,(k==null?void 0:k.statusCode)===((pe=ft)==null?void 0:pe.STATUS_200)||(k==null?void 0:k.statusCode)===((ut=ft)==null?void 0:ut.STATUS_201)?(dt({title:wt.TITLE,message:k.message,subText:wt.SUBTEXT,buttonText:wt.BUTTONTEXT,redirectTo:Me}),Xe(!0)):(k==null?void 0:k.statusCode)===((pt=ft)==null?void 0:pt.STATUS_500)||(k==null?void 0:k.statusCode)===((qe=ft)==null?void 0:qe.STATUS_501)?(dt({title:$t.TITLE,message:k.message,subText:$t.SUBTEXT,buttonText:$t.BUTTONTEXT,redirectTo:$t.REDIRECT}),Xe(!0)):It("Unexpected response received.")},oe=k=>{se(!1),Pe(k==null?void 0:k.error,"error")};Ae(D==null?void 0:D.URL,"POST",H,oe,G)},[n,l]=o.useState(null);o.useEffect(()=>{n&&Ve&&Jt(n.errorTabIndex)},[n,Ve]);const u=async()=>{if(!it.current)return;let r=!0;for(const d of Rt){const D=await it.current.validateRow(d);x(tt(Rt.map(G=>G.id===d.id?{...G,validated:D.validated}:G))),D.validated||(r=!1,l(D),await new Promise(G=>{const H=setInterval(()=>{Ve||(clearInterval(H),G())},100)}))}return r&&Pe("All rows validated successfully","success"),r};return o.useEffect(()=>{const r=async()=>{var d,D;try{const G=await Vt(q,ae==null?void 0:ae.Region,"",(d=X==null?void 0:X.Tochildrequestheaderdata)==null?void 0:d.BankKeyGroupType,ce==null?void 0:ce.ATTRIBUTE_3,"v1","MDG_BNKY_DYNAMIC_WORKFLOW_DT",(D=F)==null?void 0:D.BK);_t(G)}catch(G){customError(G)}};q&&(ae!=null&&ae.Region)&&(ce!=null&&ce.ATTRIBUTE_3)&&r()},[q,ae==null?void 0:ae.Region,ce==null?void 0:ce.ATTRIBUTE_3]),o.useEffect(()=>{var D;let r=(D=Xa)==null?void 0:D[O];const d=Ia(Dt,r);Xt(d)},[O,Dt]),o.useEffect(()=>{Ct("Bank Key","MDG_DYN_BTN_DT","v3",Re||q)},[Re,q]),g(is,{children:[g(W,{sx:{padding:2},children:[g(ie,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[M||U?g(ve,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[t($a,{sx:{fontSize:"1.5rem"}}),T("Request Header ID"),":"," ",t("span",{children:M||U})]}):t("div",{style:{flex:1}}),O===1&&g(W,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[t(he,{variant:"outlined",size:"small",title:"Download Error Report",disabled:!le,onClick:()=>j(!0),color:"primary",children:t(Ma,{sx:{padding:"2px"}})}),t(he,{variant:"outlined",disabled:!1,size:"small",onClick:Ot,title:"Change Log",children:t(on,{sx:{padding:"2px"}})}),t(he,{variant:"outlined",disabled:!le,size:"small",onClick:Ut,title:"Export Excel",children:t(nn,{sx:{padding:"2px"}})})]})]}),N&&t(Za,{open:!0,closeModal:()=>_(!1),requestId:M||U,requestType:q||Re,module:(ne=F)==null?void 0:ne.BK}),(a==null?void 0:a.TemplateName)&&g(ve,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[t(Wa,{sx:{fontSize:"1.5rem"}}),T("Template Name"),": ",t("span",{children:a==null?void 0:a.TemplateName})]}),t(mt,{onClick:()=>{var r;if(w){ge((r=et)==null?void 0:r.REQUEST_BENCH);return}Te(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:T("Back"),children:t(Na,{sx:{fontSize:"25px",color:"#000000"}})}),t(La,{nonLinear:!0,activeStep:O,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:B.map((r,d)=>t(Oa,{children:t(qa,{color:"error",disabled:d===1&&!Ee||d===2&&!We||d===3&&!We,onClick:()=>kt(d),sx:{fontSize:"50px",fontWeight:"bold"},children:t("span",{style:{fontSize:"15px",fontWeight:"bold"},children:r})})},r))}),t(ln,{dialogState:gt,closeReusableDialog:()=>j(!1),module:(ke=F)==null?void 0:ke.BK}),O===0&&g(is,{children:[t(dn,{setIsSecondTabEnabled:Be,setIsAttachmentTabEnabled:Ie,downloadClicked:Ke,setDownloadClicked:c}),(q===R.CHANGE_WITH_UPLOAD||q===R.CREATE_WITH_UPLOAD)&&((i==null?void 0:i.reqStatus)==be.DRAFT&&!(i!=null&&i.objectNumbers)!=="Not Available"||(i==null?void 0:i.reqStatus)==be.UPLOAD_FAILED)&&t(an,{handleDownload:qt,setEnableDocumentUpload:Z,enableDocumentUpload:h,handleUploadMaterial:Lt})]}),O===1&&t(cn,{ref:it,setIsAttachmentTabEnabled:Ie,setCompleted:Ge,selectedTabParent:we,downloadClicked:Ke,setDownloadClicked:c,requestStatus:i!=null&&i.reqStatus?i==null?void 0:i.reqStatus:be.ENABLE_FOR_FIRST_TIME}),O===2&&t(Ya,{requestStatus:i!=null&&i.reqStatus?i==null?void 0:i.reqStatus:be.ENABLE_FOR_FIRST_TIME,attachmentsData:nt,requestIdHeader:M||U,pcNumber:Ye,childRequestIds:i!=null&&i.childRequestIds?i==null?void 0:i.childRequestIds:"Not Available",module:(ms=F)==null?void 0:ms.BK,artifactName:ya.BK}),O===3&&t(W,{sx:{width:"100%",overflow:"auto"},children:t(za,{requestStatus:i!=null&&i.reqStatus?i==null?void 0:i.reqStatus:be.ENABLE_FOR_FIRST_TIME,module:(gs=F)==null?void 0:gs.BK,payloadData:X,payloadForDownloadExcel:"",payloadForPreviewDownloadExcel:yt})})]}),p&&g(Ks,{isOpen:p,titleIcon:t(va,{size:"small",sx:{color:(Es=(Ts=ee)==null?void 0:Ts.secondary)==null?void 0:Es.amber,fontSize:"20px"}}),Title:T("Warning"),handleClose:ct,children:[t(rs,{sx:{mt:2},children:T(Ps.LEAVE_PAGE_MESSAGE)}),g(ls,{children:[t(he,{variant:"outlined",size:"small",sx:{...ws},onClick:ct,children:T("No")}),t(he,{variant:"contained",size:"small",sx:{...$s},onClick:Mt,children:T("Yes")})]})]}),O!=0&&t(Va,{handleSaveAsDraft:e,handleSubmitForReview:e,handleSubmitForApprove:e,handleSendBack:e,handleCorrection:e,handleRejectAndCancel:e,handleValidateAndSyndicate:e,validateAllRows:e,isSaveAsDraftEnabled:Tt,validateEnabled:!0,filteredButtons:At,moduleName:(Cs=F)==null?void 0:Cs.BK,showWfLevels:hs,selectedLevel:Qe,workFlowLevels:de,setSelectedLevel:zt,activeTab:O,selectedLevelName:rt,setSelectedLevelName:St}),t(Yt,{open:Ve,onClose:()=>xt(!1),missingFields:Zt,t:T}),t(Yt,{open:lt.open,onClose:()=>es({open:!1,message:"",title:""}),customMessage:lt.message,title:lt.title,t:T}),t(Qa,{open:Nt,onClose:()=>Xe(!1),title:ye.title,message:ye.message,subText:ye.subText,buttonText:ye.buttonText,redirectTo:ye.redirectTo})]})};export{ao as default};
