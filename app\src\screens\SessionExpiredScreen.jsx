import { <PERSON>, Typo<PERSON>, <PERSON><PERSON>, Divider, <PERSON><PERSON><PERSON><PERSON>on } from '@mui/material';
import { keyframes } from '@emotion/react';
import { MdTimerOff } from 'react-icons/md';
import 'react-toastify/dist/ReactToastify.css';
import RefreshIcon from '@mui/icons-material/Refresh';

const fadeIn = keyframes`
  0% { opacity: 0; transform: scale(0.95); }
  100% { opacity: 1; transform: scale(1); }
`;

const pulse = keyframes`
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
`;

const SessionExpiredScreen = () => {
  const handleReload = () => {
    window.location.reload()
  };

  return (
    <Box
      sx={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(220, 220, 220, 0.9)',
        zIndex: 9999,
        animation: `${fadeIn} 0.5s ease-out`,
        ariaLive: 'assertive',
      }}
    >

      <Box
        sx={{
          textAlign: 'center',
          backgroundColor: 'white',
          padding: 5,
          borderRadius: 3,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          maxWidth: '580px',
          width: '90%',
          animation: `${fadeIn} 0.7s ease-out`,
        }}
      >
        <Box
          sx={{
            marginBottom: '20px',
            animation: `${pulse} 2s infinite`,
            display: 'inline-flex',
          }}
        >
          <MdTimerOff size={60} color="#FF6F61" />
        </Box>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 500 }}>
          Session Expired
        </Typography>
        <Divider
          sx={{
            margin: '20px auto',
            width: '60%',
            backgroundColor: '#1976d2',
            height: '2px',
          }}
        />
        <Typography variant="body1" gutterBottom sx={{ color: '#555', marginBottom: 3 }}>
          Your session has expired due to security protocols or network issues. For your protection, please re-login to continue.
        </Typography>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Button
            onClick={handleReload}
            variant='contained'
            sx={{
              marginTop: 3,
              backgroundColor: '#1976d2',
              color: 'white',
              fontSize: '16px',
              padding: '12px 24px',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1.5,
              transition: 'background-color 0.3s ease',
              '&:hover': {
                backgroundColor: '#115293',
              },
            }}
          >
            <IconButton
              sx={{
                color: 'inherit',
              }}
            >
              <RefreshIcon />
            </IconButton>
            Re-login
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default SessionExpiredScreen;