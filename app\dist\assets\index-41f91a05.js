import{r as a,ef as ye,f5 as k,t as Ze,fc as Wt,fi as le,fn as ga,_ as ce,f6 as Qe,fe as zo,ff as Lo,fg as Bo,fh as jo,m as Ao}from"./index-226a1e75.js";import{A as Lt,c as pa,_ as G,f as Ke,a as Ho,w as ur,F as vn,C as ba,k as Wo}from"./EyeOutlined-6bec9589.js";import{K as ft,aV as ha,A as ae,ab as en,Q as zt,P as dr,y as Ht,s as bn,p as _n,a8 as fr,aW as Vo,aX as ya,U as mr,a4 as Ct,aI as Ca,z as Ko,w as Mt,v as hn,N as vr,au as yn,g as Tt,m as Rt,o as rn,a as St,r as Bt,X as tn,n as N,_ as Sa,ap as Fo,c as Go,L as $a,j as Uo,k as Xo,W as qo,a7 as Yo,as as ko,at as xa,u as wa,ac as Qo,ai as Ln,ad as Zo,aw as Jo,$ as ei,aY as gr,ax as ti,Y as ni,a0 as ri,aZ as ai,a3 as oi,a2 as ii,a_ as li,T as Bn,H as Ea,V as Ia,aH as Oa,aG as Ra,aa as si,q as Pa,a$ as ci,b0 as ui,a9 as di,x as Ma,a1 as er,Z as jn,b1 as fi,aS as mi,B as vi,b2 as gi,b3 as pi,E as Ta,aE as _a,b4 as bi,b5 as hi,b6 as yi,b7 as Ci,t as Si,b8 as _r,ae as $i,aR as Na,b9 as xi,i as wi}from"./EditOutlined-5e4d9326.js";var Ei={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};const Ii=Ei;var Oi=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Ii}))},Ri=a.forwardRef(Oi);const Nr=Ri,Pi=new ft("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Mi=new ft("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),Ti=new ft("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),_i=new ft("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),Ni=new ft("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Di=new ft("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),zi=new ft("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),Li=new ft("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}}),Bi={"move-up":{inKeyframes:zi,outKeyframes:Li},"move-down":{inKeyframes:Pi,outKeyframes:Mi},"move-left":{inKeyframes:Ti,outKeyframes:_i},"move-right":{inKeyframes:Ni,outKeyframes:Di}},Dr=(e,t)=>{const{antCls:r}=e,n=`${r}-${t}`,{inKeyframes:o,outKeyframes:i}=Bi[t];return[ha(n,o,i,e.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]},Da=new ft("antSlideUpIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1}}),za=new ft("antSlideUpOut",{"0%":{transform:"scaleY(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"0% 0%",opacity:0}}),La=new ft("antSlideDownIn",{"0%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0},"100%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1}}),Ba=new ft("antSlideDownOut",{"0%":{transform:"scaleY(1)",transformOrigin:"100% 100%",opacity:1},"100%":{transform:"scaleY(0.8)",transformOrigin:"100% 100%",opacity:0}}),ji=new ft("antSlideLeftIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1}}),Ai=new ft("antSlideLeftOut",{"0%":{transform:"scaleX(1)",transformOrigin:"0% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"0% 0%",opacity:0}}),Hi=new ft("antSlideRightIn",{"0%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0},"100%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1}}),Wi=new ft("antSlideRightOut",{"0%":{transform:"scaleX(1)",transformOrigin:"100% 0%",opacity:1},"100%":{transform:"scaleX(0.8)",transformOrigin:"100% 0%",opacity:0}}),Vi={"slide-up":{inKeyframes:Da,outKeyframes:za},"slide-down":{inKeyframes:La,outKeyframes:Ba},"slide-left":{inKeyframes:ji,outKeyframes:Ai},"slide-right":{inKeyframes:Hi,outKeyframes:Wi}},Nn=(e,t)=>{const{antCls:r}=e,n=`${r}-${t}`,{inKeyframes:o,outKeyframes:i}=Vi[t];return[ha(n,o,i,e.motionDurationMid),{[`
      ${n}-enter,
      ${n}-appear
    `]:{transform:"scale(0)",transformOrigin:"0% 0%",opacity:0,animationTimingFunction:e.motionEaseOutQuint,"&-prepare":{transform:"scale(1)"}},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInQuint}}]};var ja=function(t){if(pa()&&window.document.documentElement){var r=Array.isArray(t)?t:[t],n=window.document.documentElement;return r.some(function(o){return o in n.style})}return!1},Ki=function(t,r){if(!ja(t))return!1;var n=document.createElement("div"),o=n.style[t];return n.style[t]=r,n.style[t]!==o};function zr(e,t){return!Array.isArray(e)&&t!==void 0?Ki(e,t):ja(e)}var An=function(t){var r=t.className,n=t.customizeIcon,o=t.customizeIconProps,i=t.children,l=t.onMouseDown,s=t.onClick,c=typeof n=="function"?n(o):n;return a.createElement("span",{className:r,onMouseDown:function(d){d.preventDefault(),l==null||l(d)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:s,"aria-hidden":!0},c!==void 0?c:a.createElement("span",{className:k(r.split(/\s+/).map(function(u){return"".concat(u,"-icon")}))},i))},Fi=function(t,r,n,o,i){var l=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,s=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,u=Ze.useMemo(function(){if(Wt(o)==="object")return o.clearIcon;if(i)return i},[o,i]),d=Ze.useMemo(function(){return!!(!l&&o&&(n.length||s)&&!(c==="combobox"&&s===""))},[o,l,n.length,s,c]);return{allowClear:d,clearIcon:Ze.createElement(An,{className:"".concat(t,"-clear"),onMouseDown:r,customizeIcon:u},"×")}},Aa=a.createContext(null);function Gi(){return a.useContext(Aa)}function Ui(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=a.useState(!1),r=G(t,2),n=r[0],o=r[1],i=a.useRef(null),l=function(){window.clearTimeout(i.current)};a.useEffect(function(){return l},[]);var s=function(u,d){l(),i.current=window.setTimeout(function(){o(u),d&&d()},e)};return[n,s,l]}function Ha(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=a.useRef(null),r=a.useRef(null);a.useEffect(function(){return function(){window.clearTimeout(r.current)}},[]);function n(o){(o||t.current===null)&&(t.current=o),window.clearTimeout(r.current),r.current=window.setTimeout(function(){t.current=null},e)}return[function(){return t.current},n]}function Xi(e,t,r,n){var o=a.useRef(null);o.current={open:t,triggerOpen:r,customizedTrigger:n},a.useEffect(function(){function i(l){var s;if(!((s=o.current)!==null&&s!==void 0&&s.customizedTrigger)){var c=l.target;c.shadowRoot&&l.composed&&(c=l.composedPath()[0]||c),o.current.open&&e().filter(function(u){return u}).every(function(u){return!u.contains(c)&&u!==c})&&o.current.triggerOpen(!1)}}return window.addEventListener("mousedown",i),function(){return window.removeEventListener("mousedown",i)}},[])}function qi(e){return e&&![ae.ESC,ae.SHIFT,ae.BACKSPACE,ae.TAB,ae.WIN_KEY,ae.ALT,ae.META,ae.WIN_KEY_RIGHT,ae.CTRL,ae.SEMICOLON,ae.EQUALS,ae.CAPS_LOCK,ae.CONTEXT_MENU,ae.F1,ae.F2,ae.F3,ae.F4,ae.F5,ae.F6,ae.F7,ae.F8,ae.F9,ae.F10,ae.F11,ae.F12].includes(e)}var Yi=["prefixCls","invalidate","item","renderItem","responsive","responsiveDisabled","registerSize","itemKey","className","style","children","display","order","component"],kt=void 0;function ki(e,t){var r=e.prefixCls,n=e.invalidate,o=e.item,i=e.renderItem,l=e.responsive,s=e.responsiveDisabled,c=e.registerSize,u=e.itemKey,d=e.className,f=e.style,m=e.children,g=e.display,p=e.order,v=e.component,h=v===void 0?"div":v,b=Ke(e,Yi),y=l&&!g;function S(E){c(u,E)}a.useEffect(function(){return function(){S(null)}},[]);var C=i&&o!==kt?i(o,{index:p}):m,$;n||($={opacity:y?0:1,height:y?0:kt,overflowY:y?"hidden":kt,order:l?p:kt,pointerEvents:y?"none":kt,position:y?"absolute":kt});var x={};y&&(x["aria-hidden"]=!0);var I=a.createElement(h,ye({className:k(!n&&r,d),style:le(le({},$),f)},x,b,{ref:t}),C);return l&&(I=a.createElement(en,{onResize:function(P){var T=P.offsetWidth;S(T)},disabled:s},I)),I}var gn=a.forwardRef(ki);gn.displayName="Item";function Qi(e){if(typeof MessageChannel>"u")zt(e);else{var t=new MessageChannel;t.port1.onmessage=function(){return e()},t.port2.postMessage(void 0)}}function Zi(){var e=a.useRef(null),t=function(n){e.current||(e.current=[],Qi(function(){ga.unstable_batchedUpdates(function(){e.current.forEach(function(o){o()}),e.current=null})})),e.current.push(n)};return t}function cn(e,t){var r=a.useState(t),n=G(r,2),o=n[0],i=n[1],l=dr(function(s){e(function(){i(s)})});return[o,l]}var Dn=Ze.createContext(null),Ji=["component"],el=["className"],tl=["className"],nl=function(t,r){var n=a.useContext(Dn);if(!n){var o=t.component,i=o===void 0?"div":o,l=Ke(t,Ji);return a.createElement(i,ye({},l,{ref:r}))}var s=n.className,c=Ke(n,el),u=t.className,d=Ke(t,tl);return a.createElement(Dn.Provider,{value:null},a.createElement(gn,ye({ref:r,className:k(s,u)},c,d)))},Wa=a.forwardRef(nl);Wa.displayName="RawItem";var rl=["prefixCls","data","renderItem","renderRawItem","itemKey","itemWidth","ssr","style","className","maxCount","renderRest","renderRawRest","suffix","component","itemComponent","onVisibleChange"],Va="responsive",Ka="invalidate";function al(e){return"+ ".concat(e.length," ...")}function ol(e,t){var r=e.prefixCls,n=r===void 0?"rc-overflow":r,o=e.data,i=o===void 0?[]:o,l=e.renderItem,s=e.renderRawItem,c=e.itemKey,u=e.itemWidth,d=u===void 0?10:u,f=e.ssr,m=e.style,g=e.className,p=e.maxCount,v=e.renderRest,h=e.renderRawRest,b=e.suffix,y=e.component,S=y===void 0?"div":y,C=e.itemComponent,$=e.onVisibleChange,x=Ke(e,rl),I=f==="full",E=Zi(),P=cn(E,null),T=G(P,2),w=T[0],_=T[1],R=w||0,O=cn(E,new Map),j=G(O,2),A=j[0],D=j[1],z=cn(E,0),L=G(z,2),M=L[0],B=L[1],ee=cn(E,0),K=G(ee,2),X=K[0],q=K[1],ge=cn(E,0),te=G(ge,2),Q=te[0],ie=te[1],$e=a.useState(null),V=G($e,2),W=V[0],Y=V[1],F=a.useState(null),H=G(F,2),ne=H[0],be=H[1],Z=a.useMemo(function(){return ne===null&&I?Number.MAX_SAFE_INTEGER:ne||0},[ne,w]),xe=a.useState(!1),de=G(xe,2),Se=de[0],je=de[1],Oe="".concat(n,"-item"),Te=Math.max(M,X),Ye=p===Va,Re=i.length&&Ye,Ae=p===Ka,_e=Re||typeof p=="number"&&i.length>p,Ce=a.useMemo(function(){var he=i;return Re?w===null&&I?he=i:he=i.slice(0,Math.min(i.length,R/d)):typeof p=="number"&&(he=i.slice(0,p)),he},[i,d,w,p,Re]),Me=a.useMemo(function(){return Re?i.slice(Z+1):i.slice(Ce.length)},[i,Ce,Re,Z]),Le=a.useCallback(function(he,Ee){var ve;return typeof c=="function"?c(he):(ve=c&&(he==null?void 0:he[c]))!==null&&ve!==void 0?ve:Ee},[c]),ot=a.useCallback(l||function(he){return he},[l]);function Be(he,Ee,ve){ne===he&&(Ee===void 0||Ee===W)||(be(he),ve||(je(he<i.length-1),$==null||$(he)),Ee!==void 0&&Y(Ee))}function Ne(he,Ee){_(Ee.clientWidth)}function De(he,Ee){D(function(ve){var Je=new Map(ve);return Ee===null?Je.delete(he):Je.set(he,Ee),Je})}function U(he,Ee){q(Ee),B(X)}function fe(he,Ee){ie(Ee)}function Ue(he){return A.get(Le(Ce[he],he))}Ht(function(){if(R&&typeof Te=="number"&&Ce){var he=Q,Ee=Ce.length,ve=Ee-1;if(!Ee){Be(0,null);return}for(var Je=0;Je<Ee;Je+=1){var Xe=Ue(Je);if(I&&(Xe=Xe||0),Xe===void 0){Be(Je-1,void 0,!0);break}if(he+=Xe,ve===0&&he<=R||Je===ve-1&&he+Ue(ve)<=R){Be(ve,null);break}else if(he+Te>R){Be(Je-1,he-Xe-Q+X);break}}b&&Ue(0)+Q>R&&Y(null)}},[R,A,X,Q,Le,Ce]);var Fe=Se&&!!Me.length,mt={};W!==null&&Re&&(mt={position:"absolute",left:W,top:0});var tt={prefixCls:Oe,responsive:Re,component:C,invalidate:Ae},pt=s?function(he,Ee){var ve=Le(he,Ee);return a.createElement(Dn.Provider,{key:ve,value:le(le({},tt),{},{order:Ee,item:he,itemKey:ve,registerSize:De,display:Ee<=Z})},s(he,Ee))}:function(he,Ee){var ve=Le(he,Ee);return a.createElement(gn,ye({},tt,{order:Ee,key:ve,item:he,renderItem:ot,itemKey:ve,registerSize:De,display:Ee<=Z}))},bt={order:Fe?Z:Number.MAX_SAFE_INTEGER,className:"".concat(Oe,"-rest"),registerSize:U,display:Fe},st=v||al,it=h?a.createElement(Dn.Provider,{value:le(le({},tt),bt)},h(Me)):a.createElement(gn,ye({},tt,bt),typeof st=="function"?st(Me):st),nt=a.createElement(S,ye({className:k(!Ae&&n,g),style:m,ref:t},x),Ce.map(pt),_e?it:null,b&&a.createElement(gn,ye({},tt,{responsive:Ye,responsiveDisabled:!Re,order:Z,className:"".concat(Oe,"-suffix"),registerSize:fe,display:!0,style:mt}),b));return Ye?a.createElement(en,{onResize:Ne,disabled:!Re},nt):nt}var Nt=a.forwardRef(ol);Nt.displayName="Overflow";Nt.Item=Wa;Nt.RESPONSIVE=Va;Nt.INVALIDATE=Ka;function il(e,t,r){var n=le(le({},e),r?t:{});return Object.keys(t).forEach(function(o){var i=t[o];typeof i=="function"&&(n[o]=function(){for(var l,s=arguments.length,c=new Array(s),u=0;u<s;u++)c[u]=arguments[u];return i.apply(void 0,c),(l=e[o])===null||l===void 0?void 0:l.call.apply(l,[e].concat(c))})}),n}var ll=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],sl=function(t,r){var n=t.prefixCls,o=t.id,i=t.inputElement,l=t.autoFocus,s=t.autoComplete,c=t.editable,u=t.activeDescendantId,d=t.value,f=t.open,m=t.attrs,g=Ke(t,ll),p=i||a.createElement("input",null),v=p,h=v.ref,b=v.props;return Ho(!("maxLength"in p.props)),p=a.cloneElement(p,le(le(le({type:"search"},il(g,b,!0)),{},{id:o,ref:bn(r,h),autoComplete:s||"off",autoFocus:l,className:k("".concat(n,"-selection-search-input"),b==null?void 0:b.className),role:"combobox","aria-expanded":f||!1,"aria-haspopup":"listbox","aria-owns":"".concat(o,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(o,"_list"),"aria-activedescendant":f?u:void 0},m),{},{value:c?d:"",readOnly:!c,unselectable:c?null:"on",style:le(le({},b.style),{},{opacity:c?null:0})})),p},Fa=a.forwardRef(sl);function Ga(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}var cl=typeof window<"u"&&window.document&&window.document.documentElement,ul=cl;function dl(e){return e!=null}function fl(e){return!e&&e!==0}function Lr(e){return["string","number"].includes(Wt(e))}function Ua(e){var t=void 0;return e&&(Lr(e.title)?t=e.title.toString():Lr(e.label)&&(t=e.label.toString())),t}function ml(e,t){ul?a.useLayoutEffect(e,t):a.useEffect(e,t)}function vl(e){var t;return(t=e.key)!==null&&t!==void 0?t:e.value}var Br=function(t){t.preventDefault(),t.stopPropagation()},gl=function(t){var r=t.id,n=t.prefixCls,o=t.values,i=t.open,l=t.searchValue,s=t.autoClearSearchValue,c=t.inputRef,u=t.placeholder,d=t.disabled,f=t.mode,m=t.showSearch,g=t.autoFocus,p=t.autoComplete,v=t.activeDescendantId,h=t.tabIndex,b=t.removeIcon,y=t.maxTagCount,S=t.maxTagTextLength,C=t.maxTagPlaceholder,$=C===void 0?function(F){return"+ ".concat(F.length," ...")}:C,x=t.tagRender,I=t.onToggleOpen,E=t.onRemove,P=t.onInputChange,T=t.onInputPaste,w=t.onInputKeyDown,_=t.onInputMouseDown,R=t.onInputCompositionStart,O=t.onInputCompositionEnd,j=t.onInputBlur,A=a.useRef(null),D=a.useState(0),z=G(D,2),L=z[0],M=z[1],B=a.useState(!1),ee=G(B,2),K=ee[0],X=ee[1],q="".concat(n,"-selection"),ge=i||f==="multiple"&&s===!1||f==="tags"?l:"",te=f==="tags"||f==="multiple"&&s===!1||m&&(i||K);ml(function(){M(A.current.scrollWidth)},[ge]);var Q=function(H,ne,be,Z,xe){return a.createElement("span",{title:Ua(H),className:k("".concat(q,"-item"),ce({},"".concat(q,"-item-disabled"),be))},a.createElement("span",{className:"".concat(q,"-item-content")},ne),Z&&a.createElement(An,{className:"".concat(q,"-item-remove"),onMouseDown:Br,onClick:xe,customizeIcon:b},"×"))},ie=function(H,ne,be,Z,xe,de){var Se=function(Oe){Br(Oe),I(!i)};return a.createElement("span",{onMouseDown:Se},x({label:ne,value:H,disabled:be,closable:Z,onClose:xe,isMaxTag:!!de}))},$e=function(H){var ne=H.disabled,be=H.label,Z=H.value,xe=!d&&!ne,de=be;if(typeof S=="number"&&(typeof be=="string"||typeof be=="number")){var Se=String(de);Se.length>S&&(de="".concat(Se.slice(0,S),"..."))}var je=function(Te){Te&&Te.stopPropagation(),E(H)};return typeof x=="function"?ie(Z,de,ne,xe,je):Q(H,de,ne,xe,je)},V=function(H){if(!o.length)return null;var ne=typeof $=="function"?$(H):$;return typeof x=="function"?ie(void 0,ne,!1,!1,void 0,!0):Q({title:ne},ne,!1)},W=a.createElement("div",{className:"".concat(q,"-search"),style:{width:L},onFocus:function(){X(!0)},onBlur:function(){X(!1)}},a.createElement(Fa,{ref:c,open:i,prefixCls:n,id:r,inputElement:null,disabled:d,autoFocus:g,autoComplete:p,editable:te,activeDescendantId:v,value:ge,onKeyDown:w,onMouseDown:_,onChange:P,onPaste:T,onCompositionStart:R,onCompositionEnd:O,onBlur:j,tabIndex:h,attrs:_n(t,!0)}),a.createElement("span",{ref:A,className:"".concat(q,"-search-mirror"),"aria-hidden":!0},ge," ")),Y=a.createElement(Nt,{prefixCls:"".concat(q,"-overflow"),data:o,renderItem:$e,renderRest:V,suffix:W,itemKey:vl,maxCount:y});return a.createElement("span",{className:"".concat(q,"-wrap")},Y,!o.length&&!ge&&a.createElement("span",{className:"".concat(q,"-placeholder")},u))},pl=function(t){var r=t.inputElement,n=t.prefixCls,o=t.id,i=t.inputRef,l=t.disabled,s=t.autoFocus,c=t.autoComplete,u=t.activeDescendantId,d=t.mode,f=t.open,m=t.values,g=t.placeholder,p=t.tabIndex,v=t.showSearch,h=t.searchValue,b=t.activeValue,y=t.maxLength,S=t.onInputKeyDown,C=t.onInputMouseDown,$=t.onInputChange,x=t.onInputPaste,I=t.onInputCompositionStart,E=t.onInputCompositionEnd,P=t.onInputBlur,T=t.title,w=a.useState(!1),_=G(w,2),R=_[0],O=_[1],j=d==="combobox",A=j||v,D=m[0],z=h||"";j&&b&&!R&&(z=b),a.useEffect(function(){j&&O(!1)},[j,b]);var L=d!=="combobox"&&!f&&!v?!1:!!z,M=T===void 0?Ua(D):T,B=a.useMemo(function(){return D?null:a.createElement("span",{className:"".concat(n,"-selection-placeholder"),style:L?{visibility:"hidden"}:void 0},g)},[D,L,g,n]);return a.createElement("span",{className:"".concat(n,"-selection-wrap")},a.createElement("span",{className:"".concat(n,"-selection-search")},a.createElement(Fa,{ref:i,prefixCls:n,id:o,open:f,inputElement:r,disabled:l,autoFocus:s,autoComplete:c,editable:A,activeDescendantId:u,value:z,onKeyDown:S,onMouseDown:C,onChange:function(K){O(!0),$(K)},onPaste:x,onCompositionStart:I,onCompositionEnd:E,onBlur:P,tabIndex:p,attrs:_n(t,!0),maxLength:j?y:void 0})),!j&&D?a.createElement("span",{className:"".concat(n,"-selection-item"),title:M,style:L?{visibility:"hidden"}:void 0},D.label):null,B)},bl=function(t,r){var n=a.useRef(null),o=a.useRef(!1),i=t.prefixCls,l=t.open,s=t.mode,c=t.showSearch,u=t.tokenWithEnter,d=t.disabled,f=t.prefix,m=t.autoClearSearchValue,g=t.onSearch,p=t.onSearchSubmit,v=t.onToggleOpen,h=t.onInputKeyDown,b=t.onInputBlur,y=t.domRef;a.useImperativeHandle(r,function(){return{focus:function(M){n.current.focus(M)},blur:function(){n.current.blur()}}});var S=Ha(0),C=G(S,2),$=C[0],x=C[1],I=function(M){var B=M.which,ee=n.current instanceof HTMLTextAreaElement;!ee&&l&&(B===ae.UP||B===ae.DOWN)&&M.preventDefault(),h&&h(M),B===ae.ENTER&&s==="tags"&&!o.current&&!l&&(p==null||p(M.target.value)),!(ee&&!l&&~[ae.UP,ae.DOWN,ae.LEFT,ae.RIGHT].indexOf(B))&&qi(B)&&v(!0)},E=function(){x(!0)},P=a.useRef(null),T=function(M){g(M,!0,o.current)!==!1&&v(!0)},w=function(){o.current=!0},_=function(M){o.current=!1,s!=="combobox"&&T(M.target.value)},R=function(M){var B=M.target.value;if(u&&P.current&&/[\r\n]/.test(P.current)){var ee=P.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");B=B.replace(ee,P.current)}P.current=null,T(B)},O=function(M){var B=M.clipboardData,ee=B==null?void 0:B.getData("text");P.current=ee||""},j=function(M){var B=M.target;if(B!==n.current){var ee=document.body.style.msTouchAction!==void 0;ee?setTimeout(function(){n.current.focus()}):n.current.focus()}},A=function(M){var B=$();M.target!==n.current&&!B&&!(s==="combobox"&&d)&&M.preventDefault(),(s!=="combobox"&&(!c||!B)||!l)&&(l&&m!==!1&&g("",!0,!1),v())},D={inputRef:n,onInputKeyDown:I,onInputMouseDown:E,onInputChange:R,onInputPaste:O,onInputCompositionStart:w,onInputCompositionEnd:_,onInputBlur:b},z=s==="multiple"||s==="tags"?a.createElement(gl,ye({},t,D)):a.createElement(pl,ye({},t,D));return a.createElement("div",{ref:y,className:"".concat(i,"-selector"),onClick:j,onMouseDown:A},f&&a.createElement("div",{className:"".concat(i,"-prefix")},f),z)},hl=a.forwardRef(bl),yl=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],Cl=function(t){var r=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:r,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:r,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:r,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:r,adjustY:1},htmlRegion:"scroll"}}},Sl=function(t,r){var n=t.prefixCls;t.disabled;var o=t.visible,i=t.children,l=t.popupElement,s=t.animation,c=t.transitionName,u=t.dropdownStyle,d=t.dropdownClassName,f=t.direction,m=f===void 0?"ltr":f,g=t.placement,p=t.builtinPlacements,v=t.dropdownMatchSelectWidth,h=t.dropdownRender,b=t.dropdownAlign,y=t.getPopupContainer,S=t.empty,C=t.getTriggerDOMNode,$=t.onPopupVisibleChange,x=t.onPopupMouseEnter,I=Ke(t,yl),E="".concat(n,"-dropdown"),P=l;h&&(P=h(l));var T=a.useMemo(function(){return p||Cl(v)},[p,v]),w=s?"".concat(E,"-").concat(s):c,_=typeof v=="number",R=a.useMemo(function(){return _?null:v===!1?"minWidth":"width"},[v,_]),O=u;_&&(O=le(le({},O),{},{width:v}));var j=a.useRef(null);return a.useImperativeHandle(r,function(){return{getPopupElement:function(){var D;return(D=j.current)===null||D===void 0?void 0:D.popupElement}}}),a.createElement(fr,ye({},I,{showAction:$?["click"]:[],hideAction:$?["click"]:[],popupPlacement:g||(m==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:T,prefixCls:E,popupTransitionName:w,popup:a.createElement("div",{onMouseEnter:x},P),ref:j,stretch:R,popupAlign:b,popupVisible:o,getPopupContainer:y,popupClassName:k(d,ce({},"".concat(E,"-empty"),S)),popupStyle:O,getTriggerDOMNode:C,onPopupVisibleChange:$}),i)},$l=a.forwardRef(Sl);function jr(e,t){var r=e.key,n;return"value"in e&&(n=e.value),r??(n!==void 0?n:"rc-index-key-".concat(t))}function tr(e){return typeof e<"u"&&!Number.isNaN(e)}function Xa(e,t){var r=e||{},n=r.label,o=r.value,i=r.options,l=r.groupLabel,s=n||(t?"children":"label");return{label:s,value:o||"value",options:i||"options",groupLabel:l||s}}function xl(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.fieldNames,n=t.childrenAsData,o=[],i=Xa(r,!1),l=i.label,s=i.value,c=i.options,u=i.groupLabel;function d(f,m){Array.isArray(f)&&f.forEach(function(g){if(m||!(c in g)){var p=g[s];o.push({key:jr(g,o.length),groupOption:m,data:g,label:g[l],value:p})}else{var v=g[u];v===void 0&&n&&(v=g.label),o.push({key:jr(g,o.length),group:!0,data:g,label:v}),d(g[c],!0)}})}return d(e,!1),o}function nr(e){var t=le({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return ur(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var wl=function(t,r,n){if(!r||!r.length)return null;var o=!1,i=function s(c,u){var d=Vo(u),f=d[0],m=d.slice(1);if(!f)return[c];var g=c.split(f);return o=o||g.length>1,g.reduce(function(p,v){return[].concat(Qe(p),Qe(s(v,m)))},[]).filter(Boolean)},l=i(t,r);return o?typeof n<"u"?l.slice(0,n):l:null},pr=a.createContext(null);function El(e){var t=e.visible,r=e.values;if(!t)return null;var n=50;return a.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(r.slice(0,n).map(function(o){var i=o.label,l=o.value;return["number","string"].includes(Wt(i))?i:l}).join(", ")),r.length>n?", ...":null)}var Il=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],Ol=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],rr=function(t){return t==="tags"||t==="multiple"},Rl=a.forwardRef(function(e,t){var r,n=e.id,o=e.prefixCls,i=e.className,l=e.showSearch,s=e.tagRender,c=e.direction,u=e.omitDomProps,d=e.displayValues,f=e.onDisplayValuesChange,m=e.emptyOptions,g=e.notFoundContent,p=g===void 0?"Not Found":g,v=e.onClear,h=e.mode,b=e.disabled,y=e.loading,S=e.getInputElement,C=e.getRawInputElement,$=e.open,x=e.defaultOpen,I=e.onDropdownVisibleChange,E=e.activeValue,P=e.onActiveValueChange,T=e.activeDescendantId,w=e.searchValue,_=e.autoClearSearchValue,R=e.onSearch,O=e.onSearchSplit,j=e.tokenSeparators,A=e.allowClear,D=e.prefix,z=e.suffixIcon,L=e.clearIcon,M=e.OptionList,B=e.animation,ee=e.transitionName,K=e.dropdownStyle,X=e.dropdownClassName,q=e.dropdownMatchSelectWidth,ge=e.dropdownRender,te=e.dropdownAlign,Q=e.placement,ie=e.builtinPlacements,$e=e.getPopupContainer,V=e.showAction,W=V===void 0?[]:V,Y=e.onFocus,F=e.onBlur,H=e.onKeyUp,ne=e.onKeyDown,be=e.onMouseDown,Z=Ke(e,Il),xe=rr(h),de=(l!==void 0?l:xe)||h==="combobox",Se=le({},Z);Ol.forEach(function(oe){delete Se[oe]}),u==null||u.forEach(function(oe){delete Se[oe]});var je=a.useState(!1),Oe=G(je,2),Te=Oe[0],Ye=Oe[1];a.useEffect(function(){Ye(ya())},[]);var Re=a.useRef(null),Ae=a.useRef(null),_e=a.useRef(null),Ce=a.useRef(null),Me=a.useRef(null),Le=a.useRef(!1),ot=Ui(),Be=G(ot,3),Ne=Be[0],De=Be[1],U=Be[2];a.useImperativeHandle(t,function(){var oe,J;return{focus:(oe=Ce.current)===null||oe===void 0?void 0:oe.focus,blur:(J=Ce.current)===null||J===void 0?void 0:J.blur,scrollTo:function(Ve){var ke;return(ke=Me.current)===null||ke===void 0?void 0:ke.scrollTo(Ve)},nativeElement:Re.current||Ae.current}});var fe=a.useMemo(function(){var oe;if(h!=="combobox")return w;var J=(oe=d[0])===null||oe===void 0?void 0:oe.value;return typeof J=="string"||typeof J=="number"?String(J):""},[w,h,d]),Ue=h==="combobox"&&typeof S=="function"&&S()||null,Fe=typeof C=="function"&&C(),mt=mr(Ae,Fe==null||(r=Fe.props)===null||r===void 0?void 0:r.ref),tt=a.useState(!1),pt=G(tt,2),bt=pt[0],st=pt[1];Ht(function(){st(!0)},[]);var it=Ct(!1,{defaultValue:x,value:$}),nt=G(it,2),he=nt[0],Ee=nt[1],ve=bt?he:!1,Je=!p&&m;(b||Je&&ve&&h==="combobox")&&(ve=!1);var Xe=Je?!1:ve,re=a.useCallback(function(oe){var J=oe!==void 0?oe:!ve;b||(Ee(J),ve!==J&&(I==null||I(J)))},[b,ve,Ee,I]),pe=a.useMemo(function(){return(j||[]).some(function(oe){return[`
`,`\r
`].includes(oe)})},[j]),me=a.useContext(pr)||{},se=me.maxCount,we=me.rawValues,ze=function(J,We,Ve){if(!(xe&&tr(se)&&(we==null?void 0:we.size)>=se)){var ke=!0,at=J;P==null||P(null);var It=wl(J,j,tr(se)?se-we.size:void 0),yt=Ve?null:It;return h!=="combobox"&&yt&&(at="",O==null||O(yt),re(!1),ke=!1),R&&fe!==at&&R(at,{source:We?"typing":"effect"}),ke}},vt=function(J){!J||!J.trim()||R(J,{source:"submit"})};a.useEffect(function(){!ve&&!xe&&h!=="combobox"&&ze("",!1,!1)},[ve]),a.useEffect(function(){he&&b&&Ee(!1),b&&!Le.current&&De(!1)},[b]);var lt=Ha(),ct=G(lt,2),et=ct[0],gt=ct[1],wt=a.useRef(!1),jt=function(J){var We=et(),Ve=J.key,ke=Ve==="Enter";if(ke&&(h!=="combobox"&&J.preventDefault(),ve||re(!0)),gt(!!fe),Ve==="Backspace"&&!We&&xe&&!fe&&d.length){for(var at=Qe(d),It=null,yt=at.length-1;yt>=0;yt-=1){var _t=at[yt];if(!_t.disabled){at.splice(yt,1),It=_t;break}}It&&f(at,{type:"remove",values:[It]})}for(var xt=arguments.length,Ot=new Array(xt>1?xt-1:0),Yt=1;Yt<xt;Yt++)Ot[Yt-1]=arguments[Yt];if(ve&&(!ke||!wt.current)){var wn;ke&&(wt.current=!0),(wn=Me.current)===null||wn===void 0||wn.onKeyDown.apply(wn,[J].concat(Ot))}ne==null||ne.apply(void 0,[J].concat(Ot))},Ut=function(J){for(var We=arguments.length,Ve=new Array(We>1?We-1:0),ke=1;ke<We;ke++)Ve[ke-1]=arguments[ke];if(ve){var at;(at=Me.current)===null||at===void 0||at.onKeyUp.apply(at,[J].concat(Ve))}J.key==="Enter"&&(wt.current=!1),H==null||H.apply(void 0,[J].concat(Ve))},Et=function(J){var We=d.filter(function(Ve){return Ve!==J});f(We,{type:"remove",values:[J]})},ut=function(){wt.current=!1},Dt=a.useRef(!1),Vt=function(){De(!0),b||(Y&&!Dt.current&&Y.apply(void 0,arguments),W.includes("focus")&&re(!0)),Dt.current=!0},Xt=function(){Le.current=!0,De(!1,function(){Dt.current=!1,Le.current=!1,re(!1)}),!b&&(fe&&(h==="tags"?R(fe,{source:"submit"}):h==="multiple"&&R("",{source:"blur"})),F&&F.apply(void 0,arguments))},$t=[];a.useEffect(function(){return function(){$t.forEach(function(oe){return clearTimeout(oe)}),$t.splice(0,$t.length)}},[]);var Ie=function(J){var We,Ve=J.target,ke=(We=_e.current)===null||We===void 0?void 0:We.getPopupElement();if(ke&&ke.contains(Ve)){var at=setTimeout(function(){var xt=$t.indexOf(at);if(xt!==-1&&$t.splice(xt,1),U(),!Te&&!ke.contains(document.activeElement)){var Ot;(Ot=Ce.current)===null||Ot===void 0||Ot.focus()}});$t.push(at)}for(var It=arguments.length,yt=new Array(It>1?It-1:0),_t=1;_t<It;_t++)yt[_t-1]=arguments[_t];be==null||be.apply(void 0,[J].concat(yt))},ue=a.useState({}),Pe=G(ue,2),He=Pe[1];function Ge(){He({})}var rt;Fe&&(rt=function(J){re(J)}),Xi(function(){var oe;return[Re.current,(oe=_e.current)===null||oe===void 0?void 0:oe.getPopupElement()]},Xe,re,!!Fe);var ht=a.useMemo(function(){return le(le({},e),{},{notFoundContent:p,open:ve,triggerOpen:Xe,id:n,showSearch:de,multiple:xe,toggleOpen:re})},[e,p,Xe,ve,n,de,xe,re]),Kt=!!z||y,qt;Kt&&(qt=a.createElement(An,{className:k("".concat(o,"-arrow"),ce({},"".concat(o,"-arrow-loading"),y)),customizeIcon:z,customizeIconProps:{loading:y,searchValue:fe,open:ve,focused:Ne,showSearch:de}}));var At=function(){var J;v==null||v(),(J=Ce.current)===null||J===void 0||J.focus(),f([],{type:"clear",values:d}),ze("",!1,!1)},Ft=Fi(o,At,d,A,L,b,fe,h),on=Ft.allowClear,ln=Ft.clearIcon,sn=a.createElement(M,{ref:Me}),Fn=k(o,i,ce(ce(ce(ce(ce(ce(ce(ce(ce(ce({},"".concat(o,"-focused"),Ne),"".concat(o,"-multiple"),xe),"".concat(o,"-single"),!xe),"".concat(o,"-allow-clear"),A),"".concat(o,"-show-arrow"),Kt),"".concat(o,"-disabled"),b),"".concat(o,"-loading"),y),"".concat(o,"-open"),ve),"".concat(o,"-customize-input"),Ue),"".concat(o,"-show-search"),de)),xn=a.createElement($l,{ref:_e,disabled:b,prefixCls:o,visible:Xe,popupElement:sn,animation:B,transitionName:ee,dropdownStyle:K,dropdownClassName:X,direction:c,dropdownMatchSelectWidth:q,dropdownRender:ge,dropdownAlign:te,placement:Q,builtinPlacements:ie,getPopupContainer:$e,empty:m,getTriggerDOMNode:function(J){return Ae.current||J},onPopupVisibleChange:rt,onPopupMouseEnter:Ge},Fe?a.cloneElement(Fe,{ref:mt}):a.createElement(hl,ye({},e,{domRef:Ae,prefixCls:o,inputElement:Ue,ref:Ce,id:n,prefix:D,showSearch:de,autoClearSearchValue:_,mode:h,activeDescendantId:T,tagRender:s,values:d,open:ve,onToggleOpen:re,activeValue:E,searchValue:fe,onSearch:ze,onSearchSubmit:vt,onRemove:Et,tokenWithEnter:pe,onInputBlur:ut}))),qe;return Fe?qe=xn:qe=a.createElement("div",ye({className:Fn},Se,{ref:Re,onMouseDown:Ie,onKeyDown:jt,onKeyUp:Ut,onFocus:Vt,onBlur:Xt}),a.createElement(El,{visible:Ne&&!ve,values:d}),xn,qt,on&&ln),a.createElement(Aa.Provider,{value:ht},qe)}),br=function(){return null};br.isSelectOptGroup=!0;var hr=function(){return null};hr.isSelectOption=!0;function Pl(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var Ml=["disabled","title","children","style","className"];function Ar(e){return typeof e=="string"||typeof e=="number"}var Tl=function(t,r){var n=Gi(),o=n.prefixCls,i=n.id,l=n.open,s=n.multiple,c=n.mode,u=n.searchValue,d=n.toggleOpen,f=n.notFoundContent,m=n.onPopupScroll,g=a.useContext(pr),p=g.maxCount,v=g.flattenOptions,h=g.onActiveValue,b=g.defaultActiveFirstOption,y=g.onSelect,S=g.menuItemSelectedIcon,C=g.rawValues,$=g.fieldNames,x=g.virtual,I=g.direction,E=g.listHeight,P=g.listItemHeight,T=g.optionRender,w="".concat(o,"-item"),_=Ca(function(){return v},[l,v],function(V,W){return W[0]&&V[1]!==W[1]}),R=a.useRef(null),O=a.useMemo(function(){return s&&tr(p)&&(C==null?void 0:C.size)>=p},[s,p,C==null?void 0:C.size]),j=function(W){W.preventDefault()},A=function(W){var Y;(Y=R.current)===null||Y===void 0||Y.scrollTo(typeof W=="number"?{index:W}:W)},D=a.useCallback(function(V){return c==="combobox"?!1:C.has(V)},[c,Qe(C).toString(),C.size]),z=function(W){for(var Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,F=_.length,H=0;H<F;H+=1){var ne=(W+H*Y+F)%F,be=_[ne]||{},Z=be.group,xe=be.data;if(!Z&&!(xe!=null&&xe.disabled)&&(D(xe.value)||!O))return ne}return-1},L=a.useState(function(){return z(0)}),M=G(L,2),B=M[0],ee=M[1],K=function(W){var Y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;ee(W);var F={source:Y?"keyboard":"mouse"},H=_[W];if(!H){h(null,-1,F);return}h(H.value,W,F)};a.useEffect(function(){K(b!==!1?z(0):-1)},[_.length,u]);var X=a.useCallback(function(V){return c==="combobox"?String(V).toLowerCase()===u.toLowerCase():C.has(V)},[c,u,Qe(C).toString(),C.size]);a.useEffect(function(){var V=setTimeout(function(){if(!s&&l&&C.size===1){var Y=Array.from(C)[0],F=_.findIndex(function(H){var ne=H.data;return u?String(ne.value).startsWith(u):ne.value===Y});F!==-1&&(K(F),A(F))}});if(l){var W;(W=R.current)===null||W===void 0||W.scrollTo(void 0)}return function(){return clearTimeout(V)}},[l,u]);var q=function(W){W!==void 0&&y(W,{selected:!C.has(W)}),s||d(!1)};if(a.useImperativeHandle(r,function(){return{onKeyDown:function(W){var Y=W.which,F=W.ctrlKey;switch(Y){case ae.N:case ae.P:case ae.UP:case ae.DOWN:{var H=0;if(Y===ae.UP?H=-1:Y===ae.DOWN?H=1:Pl()&&F&&(Y===ae.N?H=1:Y===ae.P&&(H=-1)),H!==0){var ne=z(B+H,H);A(ne),K(ne,!0)}break}case ae.TAB:case ae.ENTER:{var be,Z=_[B];Z&&!(Z!=null&&(be=Z.data)!==null&&be!==void 0&&be.disabled)&&!O?q(Z.value):q(void 0),l&&W.preventDefault();break}case ae.ESC:d(!1),l&&W.stopPropagation()}},onKeyUp:function(){},scrollTo:function(W){A(W)}}}),_.length===0)return a.createElement("div",{role:"listbox",id:"".concat(i,"_list"),className:"".concat(w,"-empty"),onMouseDown:j},f);var ge=Object.keys($).map(function(V){return $[V]}),te=function(W){return W.label};function Q(V,W){var Y=V.group;return{role:Y?"presentation":"option",id:"".concat(i,"_list_").concat(W)}}var ie=function(W){var Y=_[W];if(!Y)return null;var F=Y.data||{},H=F.value,ne=Y.group,be=_n(F,!0),Z=te(Y);return Y?a.createElement("div",ye({"aria-label":typeof Z=="string"&&!ne?Z:null},be,{key:W},Q(Y,W),{"aria-selected":X(H)}),H):null},$e={role:"listbox",id:"".concat(i,"_list")};return a.createElement(a.Fragment,null,x&&a.createElement("div",ye({},$e,{style:{height:0,width:0,overflow:"hidden"}}),ie(B-1),ie(B),ie(B+1)),a.createElement(Ko,{itemKey:"key",ref:R,data:_,height:E,itemHeight:P,fullHeight:!1,onMouseDown:j,onScroll:m,virtual:x,direction:I,innerProps:x?null:$e},function(V,W){var Y=V.group,F=V.groupOption,H=V.data,ne=V.label,be=V.value,Z=H.key;if(Y){var xe,de=(xe=H.title)!==null&&xe!==void 0?xe:Ar(ne)?ne.toString():void 0;return a.createElement("div",{className:k(w,"".concat(w,"-group"),H.className),title:de},ne!==void 0?ne:Z)}var Se=H.disabled,je=H.title;H.children;var Oe=H.style,Te=H.className,Ye=Ke(H,Ml),Re=Mt(Ye,ge),Ae=D(be),_e=Se||!Ae&&O,Ce="".concat(w,"-option"),Me=k(w,Ce,Te,ce(ce(ce(ce({},"".concat(Ce,"-grouped"),F),"".concat(Ce,"-active"),B===W&&!_e),"".concat(Ce,"-disabled"),_e),"".concat(Ce,"-selected"),Ae)),Le=te(V),ot=!S||typeof S=="function"||Ae,Be=typeof Le=="number"?Le:Le||be,Ne=Ar(Be)?Be.toString():void 0;return je!==void 0&&(Ne=je),a.createElement("div",ye({},_n(Re),x?{}:Q(V,W),{"aria-selected":X(be),className:Me,title:Ne,onMouseMove:function(){B===W||_e||K(W)},onClick:function(){_e||q(be)},style:Oe}),a.createElement("div",{className:"".concat(Ce,"-content")},typeof T=="function"?T(V,{index:W}):Be),a.isValidElement(S)||Ae,ot&&a.createElement(An,{className:"".concat(w,"-option-state"),customizeIcon:S,customizeIconProps:{value:be,disabled:_e,isSelected:Ae}},Ae?"✓":null))}))},_l=a.forwardRef(Tl);const Nl=function(e,t){var r=a.useRef({values:new Map,options:new Map}),n=a.useMemo(function(){var i=r.current,l=i.values,s=i.options,c=e.map(function(f){if(f.label===void 0){var m;return le(le({},f),{},{label:(m=l.get(f.value))===null||m===void 0?void 0:m.label})}return f}),u=new Map,d=new Map;return c.forEach(function(f){u.set(f.value,f),d.set(f.value,t.get(f.value)||s.get(f.value))}),r.current.values=u,r.current.options=d,c},[e,t]),o=a.useCallback(function(i){return t.get(i)||r.current.options.get(i)},[t]);return[n,o]};function Gn(e,t){return Ga(e).join("").toUpperCase().includes(t)}const Dl=function(e,t,r,n,o){return a.useMemo(function(){if(!r||n===!1)return e;var i=t.options,l=t.label,s=t.value,c=[],u=typeof n=="function",d=r.toUpperCase(),f=u?n:function(g,p){return o?Gn(p[o],d):p[i]?Gn(p[l!=="children"?l:"label"],d):Gn(p[s],d)},m=u?function(g){return nr(g)}:function(g){return g};return e.forEach(function(g){if(g[i]){var p=f(r,m(g));if(p)c.push(g);else{var v=g[i].filter(function(h){return f(r,m(h))});v.length&&c.push(le(le({},g),{},ce({},i,v)))}return}f(r,m(g))&&c.push(g)}),c},[e,n,o,r,t])};var Hr=0,zl=pa();function Ll(){var e;return zl?(e=Hr,Hr+=1):e="TEST_OR_SSR",e}function Bl(e){var t=a.useState(),r=G(t,2),n=r[0],o=r[1];return a.useEffect(function(){o("rc_select_".concat(Ll()))},[]),e||n}var jl=["children","value"],Al=["children"];function Hl(e){var t=e,r=t.key,n=t.props,o=n.children,i=n.value,l=Ke(n,jl);return le({key:r,value:i!==void 0?i:r,children:o},l)}function qa(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return hn(e).map(function(r,n){if(!a.isValidElement(r)||!r.type)return null;var o=r,i=o.type.isSelectOptGroup,l=o.key,s=o.props,c=s.children,u=Ke(s,Al);return t||!i?Hl(r):le(le({key:"__RC_SELECT_GRP__".concat(l===null?n:l,"__"),label:l},u),{},{options:qa(c)})}).filter(function(r){return r})}var Wl=function(t,r,n,o,i){return a.useMemo(function(){var l=t,s=!t;s&&(l=qa(r));var c=new Map,u=new Map,d=function(g,p,v){v&&typeof v=="string"&&g.set(p[v],p)},f=function m(g){for(var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,v=0;v<g.length;v+=1){var h=g[v];!h[n.options]||p?(c.set(h[n.value],h),d(u,h,n.label),d(u,h,o),d(u,h,i)):m(h[n.options],!0)}};return f(l),{options:l,valueOptions:c,labelOptions:u}},[t,r,n,o,i])};function Wr(e){var t=a.useRef();t.current=e;var r=a.useCallback(function(){return t.current.apply(t,arguments)},[]);return r}var Vl=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],Kl=["inputValue"];function Fl(e){return!e||Wt(e)!=="object"}var Gl=a.forwardRef(function(e,t){var r=e.id,n=e.mode,o=e.prefixCls,i=o===void 0?"rc-select":o,l=e.backfill,s=e.fieldNames,c=e.inputValue,u=e.searchValue,d=e.onSearch,f=e.autoClearSearchValue,m=f===void 0?!0:f,g=e.onSelect,p=e.onDeselect,v=e.dropdownMatchSelectWidth,h=v===void 0?!0:v,b=e.filterOption,y=e.filterSort,S=e.optionFilterProp,C=e.optionLabelProp,$=e.options,x=e.optionRender,I=e.children,E=e.defaultActiveFirstOption,P=e.menuItemSelectedIcon,T=e.virtual,w=e.direction,_=e.listHeight,R=_===void 0?200:_,O=e.listItemHeight,j=O===void 0?20:O,A=e.labelRender,D=e.value,z=e.defaultValue,L=e.labelInValue,M=e.onChange,B=e.maxCount,ee=Ke(e,Vl),K=Bl(r),X=rr(n),q=!!(!$&&I),ge=a.useMemo(function(){return b===void 0&&n==="combobox"?!1:b},[b,n]),te=a.useMemo(function(){return Xa(s,q)},[JSON.stringify(s),q]),Q=Ct("",{value:u!==void 0?u:c,postState:function(pe){return pe||""}}),ie=G(Q,2),$e=ie[0],V=ie[1],W=Wl($,I,te,S,C),Y=W.valueOptions,F=W.labelOptions,H=W.options,ne=a.useCallback(function(re){var pe=Ga(re);return pe.map(function(me){var se,we,ze,vt,lt;if(Fl(me))se=me;else{var ct;ze=me.key,we=me.label,se=(ct=me.value)!==null&&ct!==void 0?ct:ze}var et=Y.get(se);if(et){var gt;we===void 0&&(we=et==null?void 0:et[C||te.label]),ze===void 0&&(ze=(gt=et==null?void 0:et.key)!==null&&gt!==void 0?gt:se),vt=et==null?void 0:et.disabled,lt=et==null?void 0:et.title}return{label:we,value:se,key:ze,disabled:vt,title:lt}})},[te,C,Y]),be=Ct(z,{value:D}),Z=G(be,2),xe=Z[0],de=Z[1],Se=a.useMemo(function(){var re,pe=X&&xe===null?[]:xe,me=ne(pe);return n==="combobox"&&fl((re=me[0])===null||re===void 0?void 0:re.value)?[]:me},[xe,ne,n,X]),je=Nl(Se,Y),Oe=G(je,2),Te=Oe[0],Ye=Oe[1],Re=a.useMemo(function(){if(!n&&Te.length===1){var re=Te[0];if(re.value===null&&(re.label===null||re.label===void 0))return[]}return Te.map(function(pe){var me;return le(le({},pe),{},{label:(me=typeof A=="function"?A(pe):pe.label)!==null&&me!==void 0?me:pe.value})})},[n,Te,A]),Ae=a.useMemo(function(){return new Set(Te.map(function(re){return re.value}))},[Te]);a.useEffect(function(){if(n==="combobox"){var re,pe=(re=Te[0])===null||re===void 0?void 0:re.value;V(dl(pe)?String(pe):"")}},[Te]);var _e=Wr(function(re,pe){var me=pe??re;return ce(ce({},te.value,re),te.label,me)}),Ce=a.useMemo(function(){if(n!=="tags")return H;var re=Qe(H),pe=function(se){return Y.has(se)};return Qe(Te).sort(function(me,se){return me.value<se.value?-1:1}).forEach(function(me){var se=me.value;pe(se)||re.push(_e(se,me.label))}),re},[_e,H,Y,Te,n]),Me=Dl(Ce,te,$e,ge,S),Le=a.useMemo(function(){return n!=="tags"||!$e||Me.some(function(re){return re[S||"value"]===$e})||Me.some(function(re){return re[te.value]===$e})?Me:[_e($e)].concat(Qe(Me))},[_e,S,n,Me,$e,te]),ot=function re(pe){var me=Qe(pe).sort(function(se,we){return y(se,we,{searchValue:$e})});return me.map(function(se){return Array.isArray(se.options)?le(le({},se),{},{options:se.options.length>0?re(se.options):se.options}):se})},Be=a.useMemo(function(){return y?ot(Le):Le},[Le,y,$e]),Ne=a.useMemo(function(){return xl(Be,{fieldNames:te,childrenAsData:q})},[Be,te,q]),De=function(pe){var me=ne(pe);if(de(me),M&&(me.length!==Te.length||me.some(function(ze,vt){var lt;return((lt=Te[vt])===null||lt===void 0?void 0:lt.value)!==(ze==null?void 0:ze.value)}))){var se=L?me:me.map(function(ze){return ze.value}),we=me.map(function(ze){return nr(Ye(ze.value))});M(X?se:se[0],X?we:we[0])}},U=a.useState(null),fe=G(U,2),Ue=fe[0],Fe=fe[1],mt=a.useState(0),tt=G(mt,2),pt=tt[0],bt=tt[1],st=E!==void 0?E:n!=="combobox",it=a.useCallback(function(re,pe){var me=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},se=me.source,we=se===void 0?"keyboard":se;bt(pe),l&&n==="combobox"&&re!==null&&we==="keyboard"&&Fe(String(re))},[l,n]),nt=function(pe,me,se){var we=function(){var Et,ut=Ye(pe);return[L?{label:ut==null?void 0:ut[te.label],value:pe,key:(Et=ut==null?void 0:ut.key)!==null&&Et!==void 0?Et:pe}:pe,nr(ut)]};if(me&&g){var ze=we(),vt=G(ze,2),lt=vt[0],ct=vt[1];g(lt,ct)}else if(!me&&p&&se!=="clear"){var et=we(),gt=G(et,2),wt=gt[0],jt=gt[1];p(wt,jt)}},he=Wr(function(re,pe){var me,se=X?pe.selected:!0;se?me=X?[].concat(Qe(Te),[re]):[re]:me=Te.filter(function(we){return we.value!==re}),De(me),nt(re,se),n==="combobox"?Fe(""):(!rr||m)&&(V(""),Fe(""))}),Ee=function(pe,me){De(pe);var se=me.type,we=me.values;(se==="remove"||se==="clear")&&we.forEach(function(ze){nt(ze.value,!1,se)})},ve=function(pe,me){if(V(pe),Fe(null),me.source==="submit"){var se=(pe||"").trim();if(se){var we=Array.from(new Set([].concat(Qe(Ae),[se])));De(we),nt(se,!0),V("")}return}me.source!=="blur"&&(n==="combobox"&&De(pe),d==null||d(pe))},Je=function(pe){var me=pe;n!=="tags"&&(me=pe.map(function(we){var ze=F.get(we);return ze==null?void 0:ze.value}).filter(function(we){return we!==void 0}));var se=Array.from(new Set([].concat(Qe(Ae),Qe(me))));De(se),se.forEach(function(we){nt(we,!0)})},Xe=a.useMemo(function(){var re=T!==!1&&h!==!1;return le(le({},W),{},{flattenOptions:Ne,onActiveValue:it,defaultActiveFirstOption:st,onSelect:he,menuItemSelectedIcon:P,rawValues:Ae,fieldNames:te,virtual:re,direction:w,listHeight:R,listItemHeight:j,childrenAsData:q,maxCount:B,optionRender:x})},[B,W,Ne,it,st,he,P,Ae,te,T,h,w,R,j,q,x]);return a.createElement(pr.Provider,{value:Xe},a.createElement(Rl,ye({},ee,{id:K,prefixCls:i,ref:t,omitDomProps:Kl,mode:n,displayValues:Re,onDisplayValuesChange:Ee,direction:w,searchValue:$e,onSearch:ve,autoClearSearchValue:m,onSearchSplit:Je,dropdownMatchSelectWidth:h,OptionList:_l,emptyOptions:!Ne.length,activeValue:Ue,activeDescendantId:"".concat(K,"_list_").concat(pt)})))}),yr=Gl;yr.Option=hr;yr.OptGroup=br;const Ul=()=>{const[,e]=vr(),[t]=yn("Empty"),n=new vn(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return a.createElement("svg",{style:n,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},a.createElement("title",null,(t==null?void 0:t.description)||"Empty"),a.createElement("g",{fill:"none",fillRule:"evenodd"},a.createElement("g",{transform:"translate(24 31.67)"},a.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),a.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),a.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),a.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),a.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),a.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),a.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},a.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),a.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},Xl=Ul,ql=()=>{const[,e]=vr(),[t]=yn("Empty"),{colorFill:r,colorFillTertiary:n,colorFillQuaternary:o,colorBgContainer:i}=e,{borderColor:l,shadowColor:s,contentColor:c}=a.useMemo(()=>({borderColor:new vn(r).onBackground(i).toHexString(),shadowColor:new vn(n).onBackground(i).toHexString(),contentColor:new vn(o).onBackground(i).toHexString()}),[r,n,o,i]);return a.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},a.createElement("title",null,(t==null?void 0:t.description)||"Empty"),a.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},a.createElement("ellipse",{fill:s,cx:"32",cy:"33",rx:"32",ry:"7"}),a.createElement("g",{fillRule:"nonzero",stroke:l},a.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),a.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:c}))))},Yl=ql,kl=e=>{const{componentCls:t,margin:r,marginXS:n,marginXL:o,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:n,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:n,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:r},"&-normal":{marginBlock:o,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:n,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},Ql=Tt("Empty",e=>{const{componentCls:t,controlHeightLG:r,calc:n}=e,o=Rt(e,{emptyImgCls:`${t}-img`,emptyImgHeight:n(r).mul(2.5).equal(),emptyImgHeightMD:r,emptyImgHeightSM:n(r).mul(.875).equal()});return kl(o)});var Zl=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ya=a.createElement(Xl,null),ka=a.createElement(Yl,null),Cr=e=>{var t;const{className:r,rootClassName:n,prefixCls:o,image:i,description:l,children:s,imageStyle:c,style:u,classNames:d,styles:f}=e,m=Zl(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:g,direction:p,className:v,style:h,classNames:b,styles:y,image:S}=rn("empty"),C=g("empty",o),[$,x,I]=Ql(C),[E]=yn("Empty"),P=typeof l<"u"?l:E==null?void 0:E.description,T=typeof P=="string"?P:"empty",w=(t=i??S)!==null&&t!==void 0?t:Ya;let _=null;return typeof w=="string"?_=a.createElement("img",{alt:T,src:w}):_=w,$(a.createElement("div",Object.assign({className:k(x,I,C,v,{[`${C}-normal`]:w===ka,[`${C}-rtl`]:p==="rtl"},r,n,b.root,d==null?void 0:d.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},y.root),h),f==null?void 0:f.root),u)},m),a.createElement("div",{className:k(`${C}-image`,b.image,d==null?void 0:d.image),style:Object.assign(Object.assign(Object.assign({},c),y.image),f==null?void 0:f.image)},_),P&&a.createElement("div",{className:k(`${C}-description`,b.description,d==null?void 0:d.description),style:Object.assign(Object.assign({},y.description),f==null?void 0:f.description)},P),s&&a.createElement("div",{className:k(`${C}-footer`,b.footer,d==null?void 0:d.footer),style:Object.assign(Object.assign({},y.footer),f==null?void 0:f.footer)},s)))};Cr.PRESENTED_IMAGE_DEFAULT=Ya;Cr.PRESENTED_IMAGE_SIMPLE=ka;const un=Cr,Jl=e=>{const{componentName:t}=e,{getPrefixCls:r}=a.useContext(St),n=r("empty");switch(t){case"Table":case"List":return Ze.createElement(un,{image:un.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return Ze.createElement(un,{image:un.PRESENTED_IMAGE_SIMPLE,className:`${n}-small`});case"Table.filter":return null;default:return Ze.createElement(un,null)}},es=Jl,ts=e=>{const r={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:e==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},r),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},r),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},r),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},r),{points:["br","tr"],offset:[0,-4]})}};function ns(e,t){return e||ts(t)}const Vr=e=>{const{optionHeight:t,optionFontSize:r,optionLineHeight:n,optionPadding:o}=e;return{position:"relative",display:"block",minHeight:t,padding:o,color:e.colorText,fontWeight:"normal",fontSize:r,lineHeight:n,boxSizing:"border-box"}},rs=e=>{const{antCls:t,componentCls:r}=e,n=`${r}-item`,o=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,s=`${r}-dropdown-placement-`,c=`${n}-option-selected`;return[{[`${r}-dropdown`]:Object.assign(Object.assign({},Bt(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${o}${s}bottomLeft,
          ${i}${s}bottomLeft
        `]:{animationName:Da},[`
          ${o}${s}topLeft,
          ${i}${s}topLeft,
          ${o}${s}topRight,
          ${i}${s}topRight
        `]:{animationName:La},[`${l}${s}bottomLeft`]:{animationName:za},[`
          ${l}${s}topLeft,
          ${l}${s}topRight
        `]:{animationName:Ba},"&-hidden":{display:"none"},[n]:Object.assign(Object.assign({},Vr(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},tn),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${n}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${n}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${n}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${n}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},Vr(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},Nn(e,"slide-up"),Nn(e,"slide-down"),Dr(e,"move-up"),Dr(e,"move-down")]},as=rs,os=e=>{const{multipleSelectItemHeight:t,paddingXXS:r,lineWidth:n,INTERNAL_FIXED_ITEM_MARGIN:o}=e,i=e.max(e.calc(r).sub(n).equal(),0),l=e.max(e.calc(i).sub(o).equal(),0);return{basePadding:i,containerPadding:l,itemHeight:N(t),itemLineHeight:N(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},is=e=>{const{multipleSelectItemHeight:t,selectHeight:r,lineWidth:n}=e;return e.calc(r).sub(t).div(2).sub(n).equal()},ls=e=>{const{componentCls:t,iconCls:r,borderRadiusSM:n,motionDurationSlow:o,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:s,colorIcon:c,colorIconHover:u,INTERNAL_FIXED_ITEM_MARGIN:d}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"calc(100% - 4px)",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:d,borderRadius:n,cursor:"default",transition:`font-size ${o}, line-height ${o}, height ${o}`,marginInlineEnd:e.calc(d).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${t}-disabled&`]:{color:l,borderColor:s,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},Sa()),{display:"inline-flex",alignItems:"center",color:c,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${r}`]:{verticalAlign:"-0.2em"},"&:hover":{color:u}})}}}},ss=(e,t)=>{const{componentCls:r,INTERNAL_FIXED_ITEM_MARGIN:n}=e,o=`${r}-selection-overflow`,i=e.multipleSelectItemHeight,l=is(e),s=t?`${r}-${t}`:"",c=os(e);return{[`${r}-multiple${s}`]:Object.assign(Object.assign({},ls(e)),{[`${r}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:c.basePadding,paddingBlock:c.containerPadding,borderRadius:e.borderRadius,[`${r}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${N(n)} 0`,lineHeight:N(i),visibility:"hidden",content:'"\\a0"'}},[`${r}-selection-item`]:{height:c.itemHeight,lineHeight:N(c.itemLineHeight)},[`${r}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:N(i),marginBlock:n}},[`${r}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal()},[`${o}-item + ${o}-item,
        ${r}-prefix + ${r}-selection-wrap
      `]:{[`${r}-selection-search`]:{marginInlineStart:0},[`${r}-selection-placeholder`]:{insetInlineStart:0}},[`${o}-item-suffix`]:{minHeight:c.itemHeight,marginBlock:n},[`${r}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:N(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${r}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function Un(e,t){const{componentCls:r}=e,n=t?`${r}-${t}`:"",o={[`${r}-multiple${n}`]:{fontSize:e.fontSize,[`${r}-selector`]:{[`${r}-show-search&`]:{cursor:"text"}},[`
        &${r}-show-arrow ${r}-selector,
        &${r}-allow-clear ${r}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[ss(e,t),o]}const cs=e=>{const{componentCls:t}=e,r=Rt(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),n=Rt(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[Un(e),Un(r,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},Un(n,"lg")]},us=cs;function Xn(e,t){const{componentCls:r,inputPaddingHorizontalBase:n,borderRadius:o}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${r}-${t}`:"";return{[`${r}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${r}-selector`]:Object.assign(Object.assign({},Bt(e,!0)),{display:"flex",borderRadius:o,flex:"1 1 auto",[`${r}-selection-wrap:after`]:{lineHeight:N(i)},[`${r}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${r}-selection-item,
          ${r}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:N(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${r}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${r}-selection-item:empty:after`,`${r}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${r}-show-arrow ${r}-selection-item,
        &${r}-show-arrow ${r}-selection-search,
        &${r}-show-arrow ${r}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${r}-open ${r}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${r}-customize-input)`]:{[`${r}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${N(n)}`,[`${r}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:N(i)}}},[`&${r}-customize-input`]:{[`${r}-selector`]:{"&:after":{display:"none"},[`${r}-selection-search`]:{position:"static",width:"100%"},[`${r}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${N(n)}`,"&:after":{display:"none"}}}}}}}function ds(e){const{componentCls:t}=e,r=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[Xn(e),Xn(Rt(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${N(r)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(r).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},Xn(Rt(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const fs=e=>{const{fontSize:t,lineHeight:r,lineWidth:n,controlHeight:o,controlHeightSM:i,controlHeightLG:l,paddingXXS:s,controlPaddingHorizontal:c,zIndexPopupBase:u,colorText:d,fontWeightStrong:f,controlItemBgActive:m,controlItemBgHover:g,colorBgContainer:p,colorFillSecondary:v,colorBgContainerDisabled:h,colorTextDisabled:b,colorPrimaryHover:y,colorPrimary:S,controlOutline:C}=e,$=s*2,x=n*2,I=Math.min(o-$,o-x),E=Math.min(i-$,i-x),P=Math.min(l-$,l-x);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(s/2),zIndexPopup:u+50,optionSelectedColor:d,optionSelectedFontWeight:f,optionSelectedBg:m,optionActiveBg:g,optionPadding:`${(o-t*r)/2}px ${c}px`,optionFontSize:t,optionLineHeight:r,optionHeight:o,selectorBg:p,clearBg:p,singleItemHeightLG:l,multipleItemBg:v,multipleItemBorderColor:"transparent",multipleItemHeight:I,multipleItemHeightSM:E,multipleItemHeightLG:P,multipleSelectorBgDisabled:h,multipleItemColorDisabled:b,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:y,activeBorderColor:S,activeOutlineColor:C,selectAffixPadding:s}},Qa=(e,t)=>{const{componentCls:r,antCls:n,controlOutlineWidth:o}=e;return{[`&:not(${r}-customize-input) ${r}-selector`]:{border:`${N(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${r}-disabled):not(${r}-customize-input):not(${n}-pagination-size-changer)`]:{[`&:hover ${r}-selector`]:{borderColor:t.hoverBorderHover},[`${r}-focused& ${r}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${N(o)} ${t.activeOutlineColor}`,outline:0},[`${r}-prefix`]:{color:t.color}}}},Kr=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Qa(e,t))}),ms=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},Qa(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Kr(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Kr(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${N(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Za=(e,t)=>{const{componentCls:r,antCls:n}=e;return{[`&:not(${r}-customize-input) ${r}-selector`]:{background:t.bg,border:`${N(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${r}-disabled):not(${r}-customize-input):not(${n}-pagination-size-changer)`]:{[`&:hover ${r}-selector`]:{background:t.hoverBg},[`${r}-focused& ${r}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},Fr=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Za(e,t))}),vs=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Za(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Fr(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Fr(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${N(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),gs=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${N(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${N(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),Ja=(e,t)=>{const{componentCls:r,antCls:n}=e;return{[`&:not(${r}-customize-input) ${r}-selector`]:{borderWidth:`0 0 ${N(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${r}-disabled):not(${r}-customize-input):not(${n}-pagination-size-changer)`]:{[`&:hover ${r}-selector`]:{borderColor:t.hoverBorderHover},[`${r}-focused& ${r}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${r}-prefix`]:{color:t.color}}}},Gr=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Ja(e,t))}),ps=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},Ja(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Gr(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Gr(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${N(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),bs=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},ms(e)),vs(e)),gs(e)),ps(e))}),hs=bs,ys=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},Cs=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},Ss=e=>{const{antCls:t,componentCls:r,inputPaddingHorizontalBase:n,iconCls:o}=e,i={[`${r}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[r]:Object.assign(Object.assign({},Bt(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${r}-customize-input) ${r}-selector`]:Object.assign(Object.assign({},ys(e)),Cs(e)),[`${r}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},tn),{[`> ${t}-typography`]:{display:"inline"}}),[`${r}-selection-placeholder`]:Object.assign(Object.assign({},tn),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${r}-arrow`]:Object.assign(Object.assign({},Sa()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:n,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[o]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${r}-suffix)`]:{pointerEvents:"auto"}},[`${r}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${r}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${r}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${r}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:n,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto",transform:"translateZ(0)","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":i,"&:hover":i}),[`${r}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${r}-has-feedback`]:{[`${r}-clear`]:{insetInlineEnd:e.calc(n).add(e.fontSize).add(e.paddingXS).equal()}}}}}},$s=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},Ss(e),ds(e),us(e),as(e),{[`${t}-rtl`]:{direction:"rtl"}},Fo(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},xs=Tt("Select",(e,{rootPrefixCls:t})=>{const r=Rt(e,{rootPrefixCls:t,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[$s(r),hs(r)]},fs,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var ws={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 00-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"}}]},name:"check",theme:"outlined"};const Es=ws;var Is=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Es}))},Os=a.forwardRef(Is);const eo=Os;function Rs({suffixIcon:e,clearIcon:t,menuItemSelectedIcon:r,removeIcon:n,loading:o,multiple:i,hasFeedback:l,prefixCls:s,showSuffixIcon:c,feedbackIcon:u,showArrow:d,componentName:f}){const m=t??a.createElement(Go,null),g=b=>e===null&&!l&&!d?null:a.createElement(a.Fragment,null,c!==!1&&b,l&&u);let p=null;if(e!==void 0)p=g(e);else if(o)p=g(a.createElement($a,{spin:!0}));else{const b=`${s}-suffix`;p=({open:y,showSearch:S})=>g(y&&S?a.createElement(Uo,{className:b}):a.createElement(Xo,{className:b}))}let v=null;r!==void 0?v=r:i?v=a.createElement(eo,null):v=null;let h=null;return n!==void 0?h=n:h=a.createElement(ba,null),{clearIcon:m,suffixIcon:p,itemIcon:v,removeIcon:h}}function Ps(e){return Ze.useMemo(()=>{if(e)return(...t)=>Ze.createElement(qo,{space:!0},e.apply(void 0,t))},[e])}function Ms(e,t){return t!==void 0?t:e!==null}var Ts=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const to="SECRET_COMBOBOX_MODE_DO_NOT_USE",_s=(e,t)=>{var r,n,o,i,l;const{prefixCls:s,bordered:c,className:u,rootClassName:d,getPopupContainer:f,popupClassName:m,dropdownClassName:g,listHeight:p=256,placement:v,listItemHeight:h,size:b,disabled:y,notFoundContent:S,status:C,builtinPlacements:$,dropdownMatchSelectWidth:x,popupMatchSelectWidth:I,direction:E,style:P,allowClear:T,variant:w,dropdownStyle:_,transitionName:R,tagRender:O,maxCount:j,prefix:A,dropdownRender:D,popupRender:z,onDropdownVisibleChange:L,onOpenChange:M,styles:B,classNames:ee}=e,K=Ts(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:X,getPrefixCls:q,renderEmpty:ge,direction:te,virtual:Q,popupMatchSelectWidth:ie,popupOverflow:$e}=a.useContext(St),{showSearch:V,style:W,styles:Y,className:F,classNames:H}=rn("select"),[,ne]=vr(),be=h??(ne==null?void 0:ne.controlHeight),Z=q("select",s),xe=q(),de=E??te,{compactSize:Se,compactItemClassnames:je}=ko(Z,de),[Oe,Te]=xa("select",w,c),Ye=wa(Z),[Re,Ae,_e]=xs(Z,Ye),Ce=a.useMemo(()=>{const{mode:se}=e;if(se!=="combobox")return se===to?"combobox":se},[e.mode]),Me=Ce==="multiple"||Ce==="tags",Le=Ms(e.suffixIcon,e.showArrow),ot=(r=I??x)!==null&&r!==void 0?r:ie,Be=((n=B==null?void 0:B.popup)===null||n===void 0?void 0:n.root)||((o=Y.popup)===null||o===void 0?void 0:o.root)||_,Ne=Ps(z||D),De=M||L,{status:U,hasFeedback:fe,isFormItemInput:Ue,feedbackIcon:Fe}=a.useContext(Qo),mt=ti(U,C);let tt;S!==void 0?tt=S:Ce==="combobox"?tt=null:tt=(ge==null?void 0:ge("Select"))||a.createElement(es,{componentName:"Select"});const{suffixIcon:pt,itemIcon:bt,removeIcon:st,clearIcon:it}=Rs(Object.assign(Object.assign({},K),{multiple:Me,hasFeedback:fe,feedbackIcon:Fe,showSuffixIcon:Le,prefixCls:Z,componentName:"Select"})),nt=T===!0?{clearIcon:it}:T,he=Mt(K,["suffixIcon","itemIcon"]),Ee=k(((i=ee==null?void 0:ee.popup)===null||i===void 0?void 0:i.root)||((l=H==null?void 0:H.popup)===null||l===void 0?void 0:l.root)||m||g,{[`${Z}-dropdown-${de}`]:de==="rtl"},d,H.root,ee==null?void 0:ee.root,_e,Ye,Ae),ve=Ln(se=>{var we;return(we=b??Se)!==null&&we!==void 0?we:se}),Je=a.useContext(Zo),Xe=y??Je,re=k({[`${Z}-lg`]:ve==="large",[`${Z}-sm`]:ve==="small",[`${Z}-rtl`]:de==="rtl",[`${Z}-${Oe}`]:Te,[`${Z}-in-form-item`]:Ue},Jo(Z,mt,fe),je,F,u,H.root,ee==null?void 0:ee.root,d,_e,Ye,Ae),pe=a.useMemo(()=>v!==void 0?v:de==="rtl"?"bottomRight":"bottomLeft",[v,de]),[me]=ei("SelectLike",Be==null?void 0:Be.zIndex);return Re(a.createElement(yr,Object.assign({ref:t,virtual:Q,showSearch:V},he,{style:Object.assign(Object.assign(Object.assign(Object.assign({},Y.root),B==null?void 0:B.root),W),P),dropdownMatchSelectWidth:ot,transitionName:gr(xe,"slide-up",R),builtinPlacements:ns($,$e),listHeight:p,listItemHeight:be,mode:Ce,prefixCls:Z,placement:pe,direction:de,prefix:A,suffixIcon:pt,menuItemSelectedIcon:bt,removeIcon:st,allowClear:nt,notFoundContent:tt,className:re,getPopupContainer:f||X,dropdownClassName:Ee,disabled:Xe,dropdownStyle:Object.assign(Object.assign({},Be),{zIndex:me}),maxCount:Me?j:void 0,tagRender:Me?O:void 0,dropdownRender:Ne,onDropdownVisibleChange:De})))},an=a.forwardRef(_s),Ns=Yo(an,"dropdownAlign");an.SECRET_COMBOBOX_MODE_DO_NOT_USE=to;an.Option=hr;an.OptGroup=br;an._InternalPanelDoNotUseOrYouWillBeFired=Ns;const kf=an,nn=e=>e?typeof e=="function"?e():e:null,Ds=e=>{const{componentCls:t,popoverColor:r,titleMinWidth:n,fontWeightStrong:o,innerPadding:i,boxShadowSecondary:l,colorTextHeading:s,borderRadiusLG:c,zIndexPopup:u,titleMarginBottom:d,colorBgElevated:f,popoverBg:m,titleBorderBottom:g,innerContentPadding:p,titlePadding:v}=e;return[{[t]:Object.assign(Object.assign({},Bt(e)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:u,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":f,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${t}-content`]:{position:"relative"},[`${t}-inner`]:{backgroundColor:m,backgroundClip:"padding-box",borderRadius:c,boxShadow:l,padding:i},[`${t}-title`]:{minWidth:n,marginBottom:d,color:s,fontWeight:o,borderBottom:g,padding:v},[`${t}-inner-content`]:{color:r,padding:p}})},ri(e,"var(--antd-arrow-background-color)"),{[`${t}-pure`]:{position:"relative",maxWidth:"none",margin:e.sizePopupArrow,display:"inline-block",[`${t}-content`]:{display:"inline-block"}}}]},zs=e=>{const{componentCls:t}=e;return{[t]:ai.map(r=>{const n=e[`${r}6`];return{[`&${t}-${r}`]:{"--antd-arrow-background-color":n,[`${t}-inner`]:{backgroundColor:n},[`${t}-arrow`]:{background:"transparent"}}}})}},Ls=e=>{const{lineWidth:t,controlHeight:r,fontHeight:n,padding:o,wireframe:i,zIndexPopupBase:l,borderRadiusLG:s,marginXS:c,lineType:u,colorSplit:d,paddingSM:f}=e,m=r-n,g=m/2,p=m/2-t,v=o;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:l+30},oi(e)),ii({contentRadius:s,limitVerticalRadius:!0})),{innerPadding:i?0:12,titleMarginBottom:i?0:c,titlePadding:i?`${g}px ${v}px ${p}px`:0,titleBorderBottom:i?`${t}px ${u} ${d}`:"none",innerContentPadding:i?`${f}px ${v}px`:0})},no=Tt("Popover",e=>{const{colorBgElevated:t,colorText:r}=e,n=Rt(e,{popoverBg:t,popoverColor:r});return[Ds(n),zs(n),ni(n,"zoom-big")]},Ls,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]});var Bs=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ro=({title:e,content:t,prefixCls:r})=>!e&&!t?null:a.createElement(a.Fragment,null,e&&a.createElement("div",{className:`${r}-title`},e),t&&a.createElement("div",{className:`${r}-inner-content`},t)),js=e=>{const{hashId:t,prefixCls:r,className:n,style:o,placement:i="top",title:l,content:s,children:c}=e,u=nn(l),d=nn(s),f=k(t,r,`${r}-pure`,`${r}-placement-${i}`,n);return a.createElement("div",{className:f,style:o},a.createElement("div",{className:`${r}-arrow`}),a.createElement(li,Object.assign({},e,{className:t,prefixCls:r}),c||a.createElement(ro,{prefixCls:r,title:u,content:d})))},As=e=>{const{prefixCls:t,className:r}=e,n=Bs(e,["prefixCls","className"]),{getPrefixCls:o}=a.useContext(St),i=o("popover",t),[l,s,c]=no(i);return l(a.createElement(js,Object.assign({},n,{prefixCls:i,hashId:s,className:k(r,c)})))},ao=As;var Hs=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ws=a.forwardRef((e,t)=>{var r,n;const{prefixCls:o,title:i,content:l,overlayClassName:s,placement:c="top",trigger:u="hover",children:d,mouseEnterDelay:f=.1,mouseLeaveDelay:m=.1,onOpenChange:g,overlayStyle:p={},styles:v,classNames:h}=e,b=Hs(e,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle","styles","classNames"]),{getPrefixCls:y,className:S,style:C,classNames:$,styles:x}=rn("popover"),I=y("popover",o),[E,P,T]=no(I),w=y(),_=k(s,P,T,S,$.root,h==null?void 0:h.root),R=k($.body,h==null?void 0:h.body),[O,j]=Ct(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(n=e.defaultOpen)!==null&&n!==void 0?n:e.defaultVisible}),A=(B,ee)=>{j(B,!0),g==null||g(B,ee)},D=B=>{B.keyCode===ae.ESC&&A(!1,B)},z=B=>{A(B)},L=nn(i),M=nn(l);return E(a.createElement(Bn,Object.assign({placement:c,trigger:u,mouseEnterDelay:f,mouseLeaveDelay:m},b,{prefixCls:I,classNames:{root:_,body:R},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},x.root),C),p),v==null?void 0:v.root),body:Object.assign(Object.assign({},x.body),v==null?void 0:v.body)},ref:t,open:O,onOpenChange:z,overlay:L||M?a.createElement(ro,{prefixCls:I,title:L,content:M}):null,transitionName:gr(w,"zoom-big",b.transitionName),"data-popover-inject":!0}),Ea(d,{onKeyDown:B=>{var ee,K;a.isValidElement(d)&&((K=d==null?void 0:(ee=d.props).onKeyDown)===null||K===void 0||K.call(ee,B)),D(B)}})))}),oo=Ws;oo._InternalPanelDoNotUseOrYouWillBeFired=ao;const Vs=oo;var Ks={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};const Fs=Ks;var Gs=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Fs}))},Us=a.forwardRef(Gs);const Ur=Us;var Xs=ae.ESC,qs=ae.TAB;function Ys(e){var t=e.visible,r=e.triggerRef,n=e.onVisibleChange,o=e.autoFocus,i=e.overlayRef,l=a.useRef(!1),s=function(){if(t){var f,m;(f=r.current)===null||f===void 0||(m=f.focus)===null||m===void 0||m.call(f),n==null||n(!1)}},c=function(){var f;return(f=i.current)!==null&&f!==void 0&&f.focus?(i.current.focus(),l.current=!0,!0):!1},u=function(f){switch(f.keyCode){case Xs:s();break;case qs:{var m=!1;l.current||(m=c()),m?f.preventDefault():s();break}}};a.useEffect(function(){return t?(window.addEventListener("keydown",u),o&&zt(c,3),function(){window.removeEventListener("keydown",u),l.current=!1}):function(){l.current=!1}},[t])}var ks=a.forwardRef(function(e,t){var r=e.overlay,n=e.arrow,o=e.prefixCls,i=a.useMemo(function(){var s;return typeof r=="function"?s=r():s=r,s},[r]),l=bn(t,Ia(i));return Ze.createElement(Ze.Fragment,null,n&&Ze.createElement("div",{className:"".concat(o,"-arrow")}),Ze.cloneElement(i,{ref:Oa(i)?l:void 0}))}),Qt={adjustX:1,adjustY:1},Zt=[0,0],Qs={topLeft:{points:["bl","tl"],overflow:Qt,offset:[0,-4],targetOffset:Zt},top:{points:["bc","tc"],overflow:Qt,offset:[0,-4],targetOffset:Zt},topRight:{points:["br","tr"],overflow:Qt,offset:[0,-4],targetOffset:Zt},bottomLeft:{points:["tl","bl"],overflow:Qt,offset:[0,4],targetOffset:Zt},bottom:{points:["tc","bc"],overflow:Qt,offset:[0,4],targetOffset:Zt},bottomRight:{points:["tr","br"],overflow:Qt,offset:[0,4],targetOffset:Zt}},Zs=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function Js(e,t){var r,n=e.arrow,o=n===void 0?!1:n,i=e.prefixCls,l=i===void 0?"rc-dropdown":i,s=e.transitionName,c=e.animation,u=e.align,d=e.placement,f=d===void 0?"bottomLeft":d,m=e.placements,g=m===void 0?Qs:m,p=e.getPopupContainer,v=e.showAction,h=e.hideAction,b=e.overlayClassName,y=e.overlayStyle,S=e.visible,C=e.trigger,$=C===void 0?["hover"]:C,x=e.autoFocus,I=e.overlay,E=e.children,P=e.onVisibleChange,T=Ke(e,Zs),w=Ze.useState(),_=G(w,2),R=_[0],O=_[1],j="visible"in e?S:R,A=Ze.useRef(null),D=Ze.useRef(null),z=Ze.useRef(null);Ze.useImperativeHandle(t,function(){return A.current});var L=function(Q){O(Q),P==null||P(Q)};Ys({visible:j,triggerRef:z,onVisibleChange:L,autoFocus:x,overlayRef:D});var M=function(Q){var ie=e.onOverlayClick;O(!1),ie&&ie(Q)},B=function(){return Ze.createElement(ks,{ref:D,overlay:I,prefixCls:l,arrow:o})},ee=function(){return typeof I=="function"?B:B()},K=function(){var Q=e.minOverlayWidthMatchTrigger,ie=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?Q:!ie},X=function(){var Q=e.openClassName;return Q!==void 0?Q:"".concat(l,"-open")},q=Ze.cloneElement(E,{className:k((r=E.props)===null||r===void 0?void 0:r.className,j&&X()),ref:Oa(E)?bn(z,Ia(E)):void 0}),ge=h;return!ge&&$.indexOf("contextMenu")!==-1&&(ge=["click"]),Ze.createElement(fr,ye({builtinPlacements:g},T,{prefixCls:l,ref:A,popupClassName:k(b,ce({},"".concat(l,"-show-arrow"),o)),popupStyle:y,action:$,showAction:v,hideAction:ge,popupPlacement:f,popupAlign:u,popupTransitionName:s,popupAnimation:c,popupVisible:j,stretch:K()?"minWidth":"",popup:ee(),onPopupVisibleChange:L,onPopupClick:M,getPopupContainer:p}),q)}const ec=Ze.forwardRef(Js);var io=a.createContext(null);function lo(e,t){return e===void 0?null:"".concat(e,"-").concat(t)}function so(e){var t=a.useContext(io);return lo(t,e)}var tc=["children","locked"],Pt=a.createContext(null);function nc(e,t){var r=le({},e);return Object.keys(t).forEach(function(n){var o=t[n];o!==void 0&&(r[n]=o)}),r}function pn(e){var t=e.children,r=e.locked,n=Ke(e,tc),o=a.useContext(Pt),i=Ca(function(){return nc(o,n)},[o,n],function(l,s){return!r&&(l[0]!==s[0]||!Ra(l[1],s[1],!0))});return a.createElement(Pt.Provider,{value:i},t)}var rc=[],co=a.createContext(null);function Hn(){return a.useContext(co)}var uo=a.createContext(rc);function Cn(e){var t=a.useContext(uo);return a.useMemo(function(){return e!==void 0?[].concat(Qe(t),[e]):t},[t,e])}var fo=a.createContext(null),Sr=a.createContext({});function Xr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(si(e)){var r=e.nodeName.toLowerCase(),n=["input","select","textarea","button"].includes(r)||e.isContentEditable||r==="a"&&!!e.getAttribute("href"),o=e.getAttribute("tabindex"),i=Number(o),l=null;return o&&!Number.isNaN(i)?l=i:n&&l===null&&(l=0),n&&e.disabled&&(l=null),l!==null&&(l>=0||t&&l<0)}return!1}function ac(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=Qe(e.querySelectorAll("*")).filter(function(n){return Xr(n,t)});return Xr(e,t)&&r.unshift(e),r}var ar=ae.LEFT,or=ae.RIGHT,ir=ae.UP,Pn=ae.DOWN,Mn=ae.ENTER,mo=ae.ESC,dn=ae.HOME,fn=ae.END,qr=[ir,Pn,ar,or];function oc(e,t,r,n){var o,i="prev",l="next",s="children",c="parent";if(e==="inline"&&n===Mn)return{inlineTrigger:!0};var u=ce(ce({},ir,i),Pn,l),d=ce(ce(ce(ce({},ar,r?l:i),or,r?i:l),Pn,s),Mn,s),f=ce(ce(ce(ce(ce(ce({},ir,i),Pn,l),Mn,s),mo,c),ar,r?s:c),or,r?c:s),m={inline:u,horizontal:d,vertical:f,inlineSub:u,horizontalSub:f,verticalSub:f},g=(o=m["".concat(e).concat(t?"":"Sub")])===null||o===void 0?void 0:o[n];switch(g){case i:return{offset:-1,sibling:!0};case l:return{offset:1,sibling:!0};case c:return{offset:-1,sibling:!1};case s:return{offset:1,sibling:!1};default:return null}}function ic(e){for(var t=e;t;){if(t.getAttribute("data-menu-list"))return t;t=t.parentElement}return null}function lc(e,t){for(var r=e||document.activeElement;r;){if(t.has(r))return r;r=r.parentElement}return null}function $r(e,t){var r=ac(e,!0);return r.filter(function(n){return t.has(n)})}function Yr(e,t,r){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:1;if(!e)return null;var o=$r(e,t),i=o.length,l=o.findIndex(function(s){return r===s});return n<0?l===-1?l=i-1:l-=1:n>0&&(l+=1),l=(l+i)%i,o[l]}var lr=function(t,r){var n=new Set,o=new Map,i=new Map;return t.forEach(function(l){var s=document.querySelector("[data-menu-id='".concat(lo(r,l),"']"));s&&(n.add(s),i.set(s,l),o.set(l,s))}),{elements:n,key2element:o,element2key:i}};function sc(e,t,r,n,o,i,l,s,c,u){var d=a.useRef(),f=a.useRef();f.current=t;var m=function(){zt.cancel(d.current)};return a.useEffect(function(){return function(){m()}},[]),function(g){var p=g.which;if([].concat(qr,[Mn,mo,dn,fn]).includes(p)){var v=i(),h=lr(v,n),b=h,y=b.elements,S=b.key2element,C=b.element2key,$=S.get(t),x=lc($,y),I=C.get(x),E=oc(e,l(I,!0).length===1,r,p);if(!E&&p!==dn&&p!==fn)return;(qr.includes(p)||[dn,fn].includes(p))&&g.preventDefault();var P=function(D){if(D){var z=D,L=D.querySelector("a");L!=null&&L.getAttribute("href")&&(z=L);var M=C.get(D);s(M),m(),d.current=zt(function(){f.current===M&&z.focus()})}};if([dn,fn].includes(p)||E.sibling||!x){var T;!x||e==="inline"?T=o.current:T=ic(x);var w,_=$r(T,y);p===dn?w=_[0]:p===fn?w=_[_.length-1]:w=Yr(T,y,x,E.offset),P(w)}else if(E.inlineTrigger)c(I);else if(E.offset>0)c(I,!0),m(),d.current=zt(function(){h=lr(v,n);var A=x.getAttribute("aria-controls"),D=document.getElementById(A),z=Yr(D,h.elements);P(z)},5);else if(E.offset<0){var R=l(I,!0),O=R[R.length-2],j=S.get(O);c(O,!1),P(j)}}u==null||u(g)}}function cc(e){Promise.resolve().then(e)}var xr="__RC_UTIL_PATH_SPLIT__",kr=function(t){return t.join(xr)},uc=function(t){return t.split(xr)},sr="rc-menu-more";function dc(){var e=a.useState({}),t=G(e,2),r=t[1],n=a.useRef(new Map),o=a.useRef(new Map),i=a.useState([]),l=G(i,2),s=l[0],c=l[1],u=a.useRef(0),d=a.useRef(!1),f=function(){d.current||r({})},m=a.useCallback(function(S,C){var $=kr(C);o.current.set($,S),n.current.set(S,$),u.current+=1;var x=u.current;cc(function(){x===u.current&&f()})},[]),g=a.useCallback(function(S,C){var $=kr(C);o.current.delete($),n.current.delete(S)},[]),p=a.useCallback(function(S){c(S)},[]),v=a.useCallback(function(S,C){var $=n.current.get(S)||"",x=uc($);return C&&s.includes(x[0])&&x.unshift(sr),x},[s]),h=a.useCallback(function(S,C){return S.filter(function($){return $!==void 0}).some(function($){var x=v($,!0);return x.includes(C)})},[v]),b=function(){var C=Qe(n.current.keys());return s.length&&C.push(sr),C},y=a.useCallback(function(S){var C="".concat(n.current.get(S)).concat(xr),$=new Set;return Qe(o.current.keys()).forEach(function(x){x.startsWith(C)&&$.add(o.current.get(x))}),$},[]);return a.useEffect(function(){return function(){d.current=!0}},[]),{registerPath:m,unregisterPath:g,refreshOverflowKeys:p,isSubPathKey:h,getKeyPath:v,getKeys:b,getSubPathKeys:y}}function mn(e){var t=a.useRef(e);t.current=e;var r=a.useCallback(function(){for(var n,o=arguments.length,i=new Array(o),l=0;l<o;l++)i[l]=arguments[l];return(n=t.current)===null||n===void 0?void 0:n.call.apply(n,[t].concat(i))},[]);return e?r:void 0}var fc=Math.random().toFixed(5).toString().slice(2),Qr=0;function mc(e){var t=Ct(e,{value:e}),r=G(t,2),n=r[0],o=r[1];return a.useEffect(function(){Qr+=1;var i="".concat(fc,"-").concat(Qr);o("rc-menu-uuid-".concat(i))},[]),n}function vo(e,t,r,n){var o=a.useContext(Pt),i=o.activeKey,l=o.onActive,s=o.onInactive,c={active:i===e};return t||(c.onMouseEnter=function(u){r==null||r({key:e,domEvent:u}),l(e)},c.onMouseLeave=function(u){n==null||n({key:e,domEvent:u}),s(e)}),c}function go(e){var t=a.useContext(Pt),r=t.mode,n=t.rtl,o=t.inlineIndent;if(r!=="inline")return null;var i=e;return n?{paddingRight:i*o}:{paddingLeft:i*o}}function po(e){var t=e.icon,r=e.props,n=e.children,o;return t===null||t===!1?null:(typeof t=="function"?o=a.createElement(t,le({},r)):typeof t!="boolean"&&(o=t),o||n||null)}var vc=["item"];function zn(e){var t=e.item,r=Ke(e,vc);return Object.defineProperty(r,"item",{get:function(){return ur(!1,"`info.item` is deprecated since we will move to function component that not provides React Node instance in future."),t}}),r}var gc=["title","attribute","elementRef"],pc=["style","className","eventKey","warnKey","disabled","itemIcon","children","role","onMouseEnter","onMouseLeave","onClick","onKeyDown","onFocus"],bc=["active"],hc=function(e){zo(r,e);var t=Lo(r);function r(){return Bo(this,r),t.apply(this,arguments)}return jo(r,[{key:"render",value:function(){var o=this.props,i=o.title,l=o.attribute,s=o.elementRef,c=Ke(o,gc),u=Mt(c,["eventKey","popupClassName","popupOffset","onTitleClick"]);return ur(!l,"`attribute` of Menu.Item is deprecated. Please pass attribute directly."),a.createElement(Nt.Item,ye({},l,{title:typeof i=="string"?i:void 0},u,{ref:s}))}}]),r}(a.Component),yc=a.forwardRef(function(e,t){var r=e.style,n=e.className,o=e.eventKey;e.warnKey;var i=e.disabled,l=e.itemIcon,s=e.children,c=e.role,u=e.onMouseEnter,d=e.onMouseLeave,f=e.onClick,m=e.onKeyDown,g=e.onFocus,p=Ke(e,pc),v=so(o),h=a.useContext(Pt),b=h.prefixCls,y=h.onItemClick,S=h.disabled,C=h.overflowDisabled,$=h.itemIcon,x=h.selectedKeys,I=h.onActive,E=a.useContext(Sr),P=E._internalRenderMenuItem,T="".concat(b,"-item"),w=a.useRef(),_=a.useRef(),R=S||i,O=mr(t,_),j=Cn(o),A=function(ie){return{key:o,keyPath:Qe(j).reverse(),item:w.current,domEvent:ie}},D=l||$,z=vo(o,R,u,d),L=z.active,M=Ke(z,bc),B=x.includes(o),ee=go(j.length),K=function(ie){if(!R){var $e=A(ie);f==null||f(zn($e)),y($e)}},X=function(ie){if(m==null||m(ie),ie.which===ae.ENTER){var $e=A(ie);f==null||f(zn($e)),y($e)}},q=function(ie){I(o),g==null||g(ie)},ge={};e.role==="option"&&(ge["aria-selected"]=B);var te=a.createElement(hc,ye({ref:w,elementRef:O,role:c===null?"none":c||"menuitem",tabIndex:i?null:-1,"data-menu-id":C&&v?null:v},Mt(p,["extra"]),M,ge,{component:"li","aria-disabled":i,style:le(le({},ee),r),className:k(T,ce(ce(ce({},"".concat(T,"-active"),L),"".concat(T,"-selected"),B),"".concat(T,"-disabled"),R),n),onClick:K,onKeyDown:X,onFocus:q}),s,a.createElement(po,{props:le(le({},e),{},{isSelected:B}),icon:D}));return P&&(te=P(te,e,{selected:B})),te});function Cc(e,t){var r=e.eventKey,n=Hn(),o=Cn(r);return a.useEffect(function(){if(n)return n.registerPath(r,o),function(){n.unregisterPath(r,o)}},[o]),n?null:a.createElement(yc,ye({},e,{ref:t}))}const Wn=a.forwardRef(Cc);var Sc=["className","children"],$c=function(t,r){var n=t.className,o=t.children,i=Ke(t,Sc),l=a.useContext(Pt),s=l.prefixCls,c=l.mode,u=l.rtl;return a.createElement("ul",ye({className:k(s,u&&"".concat(s,"-rtl"),"".concat(s,"-sub"),"".concat(s,"-").concat(c==="inline"?"inline":"vertical"),n),role:"menu"},i,{"data-menu-list":!0,ref:r}),o)},wr=a.forwardRef($c);wr.displayName="SubMenuList";function Er(e,t){return hn(e).map(function(r,n){if(a.isValidElement(r)){var o,i,l=r.key,s=(o=(i=r.props)===null||i===void 0?void 0:i.eventKey)!==null&&o!==void 0?o:l,c=s==null;c&&(s="tmp_key-".concat([].concat(Qe(t),[n]).join("-")));var u={key:s,eventKey:s};return a.cloneElement(r,u)}return r})}var dt={adjustX:1,adjustY:1},xc={topLeft:{points:["bl","tl"],overflow:dt},topRight:{points:["br","tr"],overflow:dt},bottomLeft:{points:["tl","bl"],overflow:dt},bottomRight:{points:["tr","br"],overflow:dt},leftTop:{points:["tr","tl"],overflow:dt},leftBottom:{points:["br","bl"],overflow:dt},rightTop:{points:["tl","tr"],overflow:dt},rightBottom:{points:["bl","br"],overflow:dt}},wc={topLeft:{points:["bl","tl"],overflow:dt},topRight:{points:["br","tr"],overflow:dt},bottomLeft:{points:["tl","bl"],overflow:dt},bottomRight:{points:["tr","br"],overflow:dt},rightTop:{points:["tr","tl"],overflow:dt},rightBottom:{points:["br","bl"],overflow:dt},leftTop:{points:["tl","tr"],overflow:dt},leftBottom:{points:["bl","br"],overflow:dt}};function bo(e,t,r){if(t)return t;if(r)return r[e]||r.other}var Ec={horizontal:"bottomLeft",vertical:"rightTop","vertical-left":"rightTop","vertical-right":"leftTop"};function Ic(e){var t=e.prefixCls,r=e.visible,n=e.children,o=e.popup,i=e.popupStyle,l=e.popupClassName,s=e.popupOffset,c=e.disabled,u=e.mode,d=e.onVisibleChange,f=a.useContext(Pt),m=f.getPopupContainer,g=f.rtl,p=f.subMenuOpenDelay,v=f.subMenuCloseDelay,h=f.builtinPlacements,b=f.triggerSubMenuAction,y=f.forceSubMenuRender,S=f.rootClassName,C=f.motion,$=f.defaultMotions,x=a.useState(!1),I=G(x,2),E=I[0],P=I[1],T=g?le(le({},wc),h):le(le({},xc),h),w=Ec[u],_=bo(u,C,$),R=a.useRef(_);u!=="inline"&&(R.current=_);var O=le(le({},R.current),{},{leavedClassName:"".concat(t,"-hidden"),removeOnLeave:!1,motionAppear:!0}),j=a.useRef();return a.useEffect(function(){return j.current=zt(function(){P(r)}),function(){zt.cancel(j.current)}},[r]),a.createElement(fr,{prefixCls:t,popupClassName:k("".concat(t,"-popup"),ce({},"".concat(t,"-rtl"),g),l,S),stretch:u==="horizontal"?"minWidth":null,getPopupContainer:m,builtinPlacements:T,popupPlacement:w,popupVisible:E,popup:o,popupStyle:i,popupAlign:s&&{offset:s},action:c?[]:[b],mouseEnterDelay:p,mouseLeaveDelay:v,onPopupVisibleChange:d,forceRender:y,popupMotion:O,fresh:!0},n)}function Oc(e){var t=e.id,r=e.open,n=e.keyPath,o=e.children,i="inline",l=a.useContext(Pt),s=l.prefixCls,c=l.forceSubMenuRender,u=l.motion,d=l.defaultMotions,f=l.mode,m=a.useRef(!1);m.current=f===i;var g=a.useState(!m.current),p=G(g,2),v=p[0],h=p[1],b=m.current?r:!1;a.useEffect(function(){m.current&&h(!1)},[f]);var y=le({},bo(i,u,d));n.length>1&&(y.motionAppear=!1);var S=y.onVisibleChanged;return y.onVisibleChanged=function(C){return!m.current&&!C&&h(!0),S==null?void 0:S(C)},v?null:a.createElement(pn,{mode:i,locked:!m.current},a.createElement(Pa,ye({visible:b},y,{forceRender:c,removeOnLeave:!1,leavedClassName:"".concat(s,"-hidden")}),function(C){var $=C.className,x=C.style;return a.createElement(wr,{id:t,className:$,style:x},o)}))}var Rc=["style","className","title","eventKey","warnKey","disabled","internalPopupClose","children","itemIcon","expandIcon","popupClassName","popupOffset","popupStyle","onClick","onMouseEnter","onMouseLeave","onTitleClick","onTitleMouseEnter","onTitleMouseLeave"],Pc=["active"],Mc=a.forwardRef(function(e,t){var r=e.style,n=e.className,o=e.title,i=e.eventKey;e.warnKey;var l=e.disabled,s=e.internalPopupClose,c=e.children,u=e.itemIcon,d=e.expandIcon,f=e.popupClassName,m=e.popupOffset,g=e.popupStyle,p=e.onClick,v=e.onMouseEnter,h=e.onMouseLeave,b=e.onTitleClick,y=e.onTitleMouseEnter,S=e.onTitleMouseLeave,C=Ke(e,Rc),$=so(i),x=a.useContext(Pt),I=x.prefixCls,E=x.mode,P=x.openKeys,T=x.disabled,w=x.overflowDisabled,_=x.activeKey,R=x.selectedKeys,O=x.itemIcon,j=x.expandIcon,A=x.onItemClick,D=x.onOpenChange,z=x.onActive,L=a.useContext(Sr),M=L._internalRenderSubMenuItem,B=a.useContext(fo),ee=B.isSubPathKey,K=Cn(),X="".concat(I,"-submenu"),q=T||l,ge=a.useRef(),te=a.useRef(),Q=u??O,ie=d??j,$e=P.includes(i),V=!w&&$e,W=ee(R,i),Y=vo(i,q,y,S),F=Y.active,H=Ke(Y,Pc),ne=a.useState(!1),be=G(ne,2),Z=be[0],xe=be[1],de=function(U){q||xe(U)},Se=function(U){de(!0),v==null||v({key:i,domEvent:U})},je=function(U){de(!1),h==null||h({key:i,domEvent:U})},Oe=a.useMemo(function(){return F||(E!=="inline"?Z||ee([_],i):!1)},[E,F,_,Z,i,ee]),Te=go(K.length),Ye=function(U){q||(b==null||b({key:i,domEvent:U}),E==="inline"&&D(i,!$e))},Re=mn(function(De){p==null||p(zn(De)),A(De)}),Ae=function(U){E!=="inline"&&D(i,U)},_e=function(){z(i)},Ce=$&&"".concat($,"-popup"),Me=a.useMemo(function(){return a.createElement(po,{icon:E!=="horizontal"?ie:void 0,props:le(le({},e),{},{isOpen:V,isSubMenu:!0})},a.createElement("i",{className:"".concat(X,"-arrow")}))},[E,ie,e,V,X]),Le=a.createElement("div",ye({role:"menuitem",style:Te,className:"".concat(X,"-title"),tabIndex:q?null:-1,ref:ge,title:typeof o=="string"?o:null,"data-menu-id":w&&$?null:$,"aria-expanded":V,"aria-haspopup":!0,"aria-controls":Ce,"aria-disabled":q,onClick:Ye,onFocus:_e},H),o,Me),ot=a.useRef(E);if(E!=="inline"&&K.length>1?ot.current="vertical":ot.current=E,!w){var Be=ot.current;Le=a.createElement(Ic,{mode:Be,prefixCls:X,visible:!s&&V&&E!=="inline",popupClassName:f,popupOffset:m,popupStyle:g,popup:a.createElement(pn,{mode:Be==="horizontal"?"vertical":Be},a.createElement(wr,{id:Ce,ref:te},c)),disabled:q,onVisibleChange:Ae},Le)}var Ne=a.createElement(Nt.Item,ye({ref:t,role:"none"},C,{component:"li",style:r,className:k(X,"".concat(X,"-").concat(E),n,ce(ce(ce(ce({},"".concat(X,"-open"),V),"".concat(X,"-active"),Oe),"".concat(X,"-selected"),W),"".concat(X,"-disabled"),q)),onMouseEnter:Se,onMouseLeave:je}),Le,!w&&a.createElement(Oc,{id:Ce,open:V,keyPath:K},c));return M&&(Ne=M(Ne,e,{selected:W,active:Oe,open:V,disabled:q})),a.createElement(pn,{onItemClick:Re,mode:E==="horizontal"?"vertical":E,itemIcon:Q,expandIcon:ie},Ne)}),Ir=a.forwardRef(function(e,t){var r=e.eventKey,n=e.children,o=Cn(r),i=Er(n,o),l=Hn();a.useEffect(function(){if(l)return l.registerPath(r,o),function(){l.unregisterPath(r,o)}},[o]);var s;return l?s=i:s=a.createElement(Mc,ye({ref:t},e),i),a.createElement(uo.Provider,{value:o},s)});function ho(e){var t=e.className,r=e.style,n=a.useContext(Pt),o=n.prefixCls,i=Hn();return i?null:a.createElement("li",{role:"separator",className:k("".concat(o,"-item-divider"),t),style:r})}var Tc=["className","title","eventKey","children"],_c=a.forwardRef(function(e,t){var r=e.className,n=e.title;e.eventKey;var o=e.children,i=Ke(e,Tc),l=a.useContext(Pt),s=l.prefixCls,c="".concat(s,"-item-group");return a.createElement("li",ye({ref:t,role:"presentation"},i,{onClick:function(d){return d.stopPropagation()},className:k(c,r)}),a.createElement("div",{role:"presentation",className:"".concat(c,"-title"),title:typeof n=="string"?n:void 0},n),a.createElement("ul",{role:"group",className:"".concat(c,"-list")},o))}),yo=a.forwardRef(function(e,t){var r=e.eventKey,n=e.children,o=Cn(r),i=Er(n,o),l=Hn();return l?i:a.createElement(_c,ye({ref:t},Mt(e,["warnKey"])),i)}),Nc=["label","children","key","type","extra"];function cr(e,t,r){var n=t.item,o=t.group,i=t.submenu,l=t.divider;return(e||[]).map(function(s,c){if(s&&Wt(s)==="object"){var u=s,d=u.label,f=u.children,m=u.key,g=u.type,p=u.extra,v=Ke(u,Nc),h=m??"tmp-".concat(c);return f||g==="group"?g==="group"?a.createElement(o,ye({key:h},v,{title:d}),cr(f,t,r)):a.createElement(i,ye({key:h},v,{title:d}),cr(f,t,r)):g==="divider"?a.createElement(l,ye({key:h},v)):a.createElement(n,ye({key:h},v,{extra:p}),d,(!!p||p===0)&&a.createElement("span",{className:"".concat(r,"-item-extra")},p))}return null}).filter(function(s){return s})}function Zr(e,t,r,n,o){var i=e,l=le({divider:ho,item:Wn,group:yo,submenu:Ir},n);return t&&(i=cr(t,l,o)),Er(i,r)}var Dc=["prefixCls","rootClassName","style","className","tabIndex","items","children","direction","id","mode","inlineCollapsed","disabled","disabledOverflow","subMenuOpenDelay","subMenuCloseDelay","forceSubMenuRender","defaultOpenKeys","openKeys","activeKey","defaultActiveFirst","selectable","multiple","defaultSelectedKeys","selectedKeys","onSelect","onDeselect","inlineIndent","motion","defaultMotions","triggerSubMenuAction","builtinPlacements","itemIcon","expandIcon","overflowedIndicator","overflowedIndicatorPopupClassName","getPopupContainer","onClick","onOpenChange","onKeyDown","openAnimation","openTransitionName","_internalRenderMenuItem","_internalRenderSubMenuItem","_internalComponents"],Gt=[],zc=a.forwardRef(function(e,t){var r,n=e,o=n.prefixCls,i=o===void 0?"rc-menu":o,l=n.rootClassName,s=n.style,c=n.className,u=n.tabIndex,d=u===void 0?0:u,f=n.items,m=n.children,g=n.direction,p=n.id,v=n.mode,h=v===void 0?"vertical":v,b=n.inlineCollapsed,y=n.disabled,S=n.disabledOverflow,C=n.subMenuOpenDelay,$=C===void 0?.1:C,x=n.subMenuCloseDelay,I=x===void 0?.1:x,E=n.forceSubMenuRender,P=n.defaultOpenKeys,T=n.openKeys,w=n.activeKey,_=n.defaultActiveFirst,R=n.selectable,O=R===void 0?!0:R,j=n.multiple,A=j===void 0?!1:j,D=n.defaultSelectedKeys,z=n.selectedKeys,L=n.onSelect,M=n.onDeselect,B=n.inlineIndent,ee=B===void 0?24:B,K=n.motion,X=n.defaultMotions,q=n.triggerSubMenuAction,ge=q===void 0?"hover":q,te=n.builtinPlacements,Q=n.itemIcon,ie=n.expandIcon,$e=n.overflowedIndicator,V=$e===void 0?"...":$e,W=n.overflowedIndicatorPopupClassName,Y=n.getPopupContainer,F=n.onClick,H=n.onOpenChange,ne=n.onKeyDown;n.openAnimation,n.openTransitionName;var be=n._internalRenderMenuItem,Z=n._internalRenderSubMenuItem,xe=n._internalComponents,de=Ke(n,Dc),Se=a.useMemo(function(){return[Zr(m,f,Gt,xe,i),Zr(m,f,Gt,{},i)]},[m,f,xe]),je=G(Se,2),Oe=je[0],Te=je[1],Ye=a.useState(!1),Re=G(Ye,2),Ae=Re[0],_e=Re[1],Ce=a.useRef(),Me=mc(p),Le=g==="rtl",ot=Ct(P,{value:T,postState:function(oe){return oe||Gt}}),Be=G(ot,2),Ne=Be[0],De=Be[1],U=function(oe){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;function We(){De(oe),H==null||H(oe)}J?ga.flushSync(We):We()},fe=a.useState(Ne),Ue=G(fe,2),Fe=Ue[0],mt=Ue[1],tt=a.useRef(!1),pt=a.useMemo(function(){return(h==="inline"||h==="vertical")&&b?["vertical",b]:[h,!1]},[h,b]),bt=G(pt,2),st=bt[0],it=bt[1],nt=st==="inline",he=a.useState(st),Ee=G(he,2),ve=Ee[0],Je=Ee[1],Xe=a.useState(it),re=G(Xe,2),pe=re[0],me=re[1];a.useEffect(function(){Je(st),me(it),tt.current&&(nt?De(Fe):U(Gt))},[st,it]);var se=a.useState(0),we=G(se,2),ze=we[0],vt=we[1],lt=ze>=Oe.length-1||ve!=="horizontal"||S;a.useEffect(function(){nt&&mt(Ne)},[Ne]),a.useEffect(function(){return tt.current=!0,function(){tt.current=!1}},[]);var ct=dc(),et=ct.registerPath,gt=ct.unregisterPath,wt=ct.refreshOverflowKeys,jt=ct.isSubPathKey,Ut=ct.getKeyPath,Et=ct.getKeys,ut=ct.getSubPathKeys,Dt=a.useMemo(function(){return{registerPath:et,unregisterPath:gt}},[et,gt]),Vt=a.useMemo(function(){return{isSubPathKey:jt}},[jt]);a.useEffect(function(){wt(lt?Gt:Oe.slice(ze+1).map(function(qe){return qe.key}))},[ze,lt]);var Xt=Ct(w||_&&((r=Oe[0])===null||r===void 0?void 0:r.key),{value:w}),$t=G(Xt,2),Ie=$t[0],ue=$t[1],Pe=mn(function(qe){ue(qe)}),He=mn(function(){ue(void 0)});a.useImperativeHandle(t,function(){return{list:Ce.current,focus:function(oe){var J,We=Et(),Ve=lr(We,Me),ke=Ve.elements,at=Ve.key2element,It=Ve.element2key,yt=$r(Ce.current,ke),_t=Ie??(yt[0]?It.get(yt[0]):(J=Oe.find(function(Yt){return!Yt.props.disabled}))===null||J===void 0?void 0:J.key),xt=at.get(_t);if(_t&&xt){var Ot;xt==null||(Ot=xt.focus)===null||Ot===void 0||Ot.call(xt,oe)}}}});var Ge=Ct(D||[],{value:z,postState:function(oe){return Array.isArray(oe)?oe:oe==null?Gt:[oe]}}),rt=G(Ge,2),ht=rt[0],Kt=rt[1],qt=function(oe){if(O){var J=oe.key,We=ht.includes(J),Ve;A?We?Ve=ht.filter(function(at){return at!==J}):Ve=[].concat(Qe(ht),[J]):Ve=[J],Kt(Ve);var ke=le(le({},oe),{},{selectedKeys:Ve});We?M==null||M(ke):L==null||L(ke)}!A&&Ne.length&&ve!=="inline"&&U(Gt)},At=mn(function(qe){F==null||F(zn(qe)),qt(qe)}),Ft=mn(function(qe,oe){var J=Ne.filter(function(Ve){return Ve!==qe});if(oe)J.push(qe);else if(ve!=="inline"){var We=ut(qe);J=J.filter(function(Ve){return!We.has(Ve)})}Ra(Ne,J,!0)||U(J,!0)}),on=function(oe,J){var We=J??!Ne.includes(oe);Ft(oe,We)},ln=sc(ve,Ie,Le,Me,Ce,Et,Ut,ue,on,ne);a.useEffect(function(){_e(!0)},[]);var sn=a.useMemo(function(){return{_internalRenderMenuItem:be,_internalRenderSubMenuItem:Z}},[be,Z]),Fn=ve!=="horizontal"||S?Oe:Oe.map(function(qe,oe){return a.createElement(pn,{key:qe.key,overflowDisabled:oe>ze},qe)}),xn=a.createElement(Nt,ye({id:p,ref:Ce,prefixCls:"".concat(i,"-overflow"),component:"ul",itemComponent:Wn,className:k(i,"".concat(i,"-root"),"".concat(i,"-").concat(ve),c,ce(ce({},"".concat(i,"-inline-collapsed"),pe),"".concat(i,"-rtl"),Le),l),dir:g,style:s,role:"menu",tabIndex:d,data:Fn,renderRawItem:function(oe){return oe},renderRawRest:function(oe){var J=oe.length,We=J?Oe.slice(-J):null;return a.createElement(Ir,{eventKey:sr,title:V,disabled:lt,internalPopupClose:J===0,popupClassName:W},We)},maxCount:ve!=="horizontal"||S?Nt.INVALIDATE:Nt.RESPONSIVE,ssr:"full","data-menu-list":!0,onVisibleChange:function(oe){vt(oe)},onKeyDown:ln},de));return a.createElement(Sr.Provider,{value:sn},a.createElement(io.Provider,{value:Me},a.createElement(pn,{prefixCls:i,rootClassName:l,mode:ve,openKeys:Ne,rtl:Le,disabled:y,motion:Ae?K:null,defaultMotions:Ae?X:null,activeKey:Ie,onActive:Pe,onInactive:He,selectedKeys:ht,inlineIndent:ee,subMenuOpenDelay:$,subMenuCloseDelay:I,forceSubMenuRender:E,builtinPlacements:te,triggerSubMenuAction:ge,getPopupContainer:Y,itemIcon:Q,expandIcon:ie,onItemClick:At,onOpenChange:Ft},a.createElement(fo.Provider,{value:Vt},xn),a.createElement("div",{style:{display:"none"},"aria-hidden":!0},a.createElement(co.Provider,{value:Dt},Te)))))}),Sn=zc;Sn.Item=Wn;Sn.SubMenu=Ir;Sn.ItemGroup=yo;Sn.Divider=ho;var Lc={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};const Bc=Lc;var jc=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Bc}))},Ac=a.forwardRef(jc);const Hc=Ac,Wc=a.createContext({siderHook:{addSider:()=>null,removeSider:()=>null}}),Vc=e=>{const{antCls:t,componentCls:r,colorText:n,footerBg:o,headerHeight:i,headerPadding:l,headerColor:s,footerPadding:c,fontSize:u,bodyBg:d,headerBg:f}=e;return{[r]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:d,"&, *":{boxSizing:"border-box"},[`&${r}-has-sider`]:{flexDirection:"row",[`> ${r}, > ${r}-content`]:{width:0}},[`${r}-header, &${r}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${r}-header`]:{height:i,padding:l,color:s,lineHeight:N(i),background:f,[`${t}-menu`]:{lineHeight:"inherit"}},[`${r}-footer`]:{padding:c,color:n,fontSize:u,background:o},[`${r}-content`]:{flex:"auto",color:n,minHeight:0}}},Co=e=>{const{colorBgLayout:t,controlHeight:r,controlHeightLG:n,colorText:o,controlHeightSM:i,marginXXS:l,colorTextLightSolid:s,colorBgContainer:c}=e,u=n*1.25;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:r*2,headerPadding:`0 ${u}px`,headerColor:o,footerPadding:`${i}px ${u}px`,footerBg:t,siderBg:"#001529",triggerHeight:n+l*2,triggerBg:"#002140",triggerColor:s,zeroTriggerWidth:n,zeroTriggerHeight:n,lightSiderBg:c,lightTriggerBg:c,lightTriggerColor:o}},So=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],Qf=Tt("Layout",Vc,Co,{deprecatedTokens:So}),Kc=e=>{const{componentCls:t,siderBg:r,motionDurationMid:n,motionDurationSlow:o,antCls:i,triggerHeight:l,triggerColor:s,triggerBg:c,headerHeight:u,zeroTriggerWidth:d,zeroTriggerHeight:f,borderRadiusLG:m,lightSiderBg:g,lightTriggerColor:p,lightTriggerBg:v,bodyBg:h}=e;return{[t]:{position:"relative",minWidth:0,background:r,transition:`all ${n}, background 0s`,"&-has-trigger":{paddingBottom:l},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:l,color:s,lineHeight:N(l),textAlign:"center",background:c,cursor:"pointer",transition:`all ${n}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:u,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:f,color:s,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:r,borderRadius:`0 ${N(m)} ${N(m)} 0`,cursor:"pointer",transition:`background ${o} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${o}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:`${N(m)} 0 0 ${N(m)}`}},"&-light":{background:g,[`${t}-trigger`]:{color:p,background:v},[`${t}-zero-width-trigger`]:{color:p,background:v,border:`1px solid ${h}`,borderInlineStart:0}}}}},Fc=Tt(["Layout","Sider"],Kc,Co,{deprecatedTokens:So});var Gc=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Jr={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},Uc=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),Xc=a.createContext({}),qc=(()=>{let e=0;return(t="")=>(e+=1,`${t}${e}`)})(),Yc=a.forwardRef((e,t)=>{const{prefixCls:r,className:n,trigger:o,children:i,defaultCollapsed:l=!1,theme:s="dark",style:c={},collapsible:u=!1,reverseArrow:d=!1,width:f=200,collapsedWidth:m=80,zeroWidthTriggerStyle:g,breakpoint:p,onCollapse:v,onBreakpoint:h}=e,b=Gc(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:y}=a.useContext(Wc),[S,C]=a.useState("collapsed"in e?e.collapsed:l),[$,x]=a.useState(!1);a.useEffect(()=>{"collapsed"in e&&C(e.collapsed)},[e.collapsed]);const I=(Q,ie)=>{"collapsed"in e||C(Q),v==null||v(Q,ie)},{getPrefixCls:E,direction:P}=a.useContext(St),T=E("layout-sider",r),[w,_,R]=Fc(T),O=a.useRef(null);O.current=Q=>{x(Q.matches),h==null||h(Q.matches),S!==Q.matches&&I(Q.matches,"responsive")},a.useEffect(()=>{function Q($e){var V;return(V=O.current)===null||V===void 0?void 0:V.call(O,$e)}let ie;return typeof(window==null?void 0:window.matchMedia)<"u"&&p&&p in Jr&&(ie=window.matchMedia(`screen and (max-width: ${Jr[p]})`),ci(ie,Q),Q(ie)),()=>{ui(ie,Q)}},[p]),a.useEffect(()=>{const Q=qc("ant-sider-");return y.addSider(Q),()=>y.removeSider(Q)},[]);const j=()=>{I(!S,"clickTrigger")},A=Mt(b,["collapsed"]),D=S?m:f,z=Uc(D)?`${D}px`:String(D),L=parseFloat(String(m||0))===0?a.createElement("span",{onClick:j,className:k(`${T}-zero-width-trigger`,`${T}-zero-width-trigger-${d?"right":"left"}`),style:g},o||a.createElement(Hc,null)):null,M=P==="rtl"==!d,K={expanded:M?a.createElement(Nr,null):a.createElement(Ur,null),collapsed:M?a.createElement(Ur,null):a.createElement(Nr,null)}[S?"collapsed":"expanded"],X=o!==null?L||a.createElement("div",{className:`${T}-trigger`,onClick:j,style:{width:z}},o||K):null,q=Object.assign(Object.assign({},c),{flex:`0 0 ${z}`,maxWidth:z,minWidth:z,width:z}),ge=k(T,`${T}-${s}`,{[`${T}-collapsed`]:!!S,[`${T}-has-trigger`]:u&&o!==null&&!L,[`${T}-below`]:!!$,[`${T}-zero-width`]:parseFloat(z)===0},n,_,R),te=a.useMemo(()=>({siderCollapsed:S}),[S]);return w(a.createElement(Xc.Provider,{value:te},a.createElement("aside",Object.assign({className:ge},A,{style:q,ref:t}),a.createElement("div",{className:`${T}-children`},i),u||$&&L?X:null)))}),Zf=Yc;var kc={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M176 511a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0zm280 0a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"ellipsis",theme:"outlined"};const Qc=kc;var Zc=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Qc}))},Jc=a.forwardRef(Zc);const eu=Jc;var tu={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};const nu=tu;var ru=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:nu}))},au=a.forwardRef(ru);const ou=au,Vn=a.createContext(null);var iu=function(t){var r=t.activeTabOffset,n=t.horizontal,o=t.rtl,i=t.indicator,l=i===void 0?{}:i,s=l.size,c=l.align,u=c===void 0?"center":c,d=a.useState(),f=G(d,2),m=f[0],g=f[1],p=a.useRef(),v=Ze.useCallback(function(b){return typeof s=="function"?s(b):typeof s=="number"?s:b},[s]);function h(){zt.cancel(p.current)}return a.useEffect(function(){var b={};if(r)if(n){b.width=v(r.width);var y=o?"right":"left";u==="start"&&(b[y]=r[y]),u==="center"&&(b[y]=r[y]+r.width/2,b.transform=o?"translateX(50%)":"translateX(-50%)"),u==="end"&&(b[y]=r[y]+r.width,b.transform="translateX(-100%)")}else b.height=v(r.height),u==="start"&&(b.top=r.top),u==="center"&&(b.top=r.top+r.height/2,b.transform="translateY(-50%)"),u==="end"&&(b.top=r.top+r.height,b.transform="translateY(-100%)");return h(),p.current=zt(function(){var S=m&&b&&Object.keys(b).every(function(C){var $=b[C],x=m[C];return typeof $=="number"&&typeof x=="number"?Math.round($)===Math.round(x):$===x});S||g(b)}),h},[JSON.stringify(r),n,o,u,v]),{style:m}},ea={width:0,height:0,left:0,top:0};function lu(e,t,r){return a.useMemo(function(){for(var n,o=new Map,i=t.get((n=e[0])===null||n===void 0?void 0:n.key)||ea,l=i.left+i.width,s=0;s<e.length;s+=1){var c=e[s].key,u=t.get(c);if(!u){var d;u=t.get((d=e[s-1])===null||d===void 0?void 0:d.key)||ea}var f=o.get(c)||le({},u);f.right=l-f.left-f.width,o.set(c,f)}return o},[e.map(function(n){return n.key}).join("_"),t,r])}function ta(e,t){var r=a.useRef(e),n=a.useState({}),o=G(n,2),i=o[1];function l(s){var c=typeof s=="function"?s(r.current):s;c!==r.current&&t(c,r.current),r.current=c,i({})}return[r.current,l]}var su=.1,na=.01,Tn=20,ra=Math.pow(.995,Tn);function cu(e,t){var r=a.useState(),n=G(r,2),o=n[0],i=n[1],l=a.useState(0),s=G(l,2),c=s[0],u=s[1],d=a.useState(0),f=G(d,2),m=f[0],g=f[1],p=a.useState(),v=G(p,2),h=v[0],b=v[1],y=a.useRef();function S(P){var T=P.touches[0],w=T.screenX,_=T.screenY;i({x:w,y:_}),window.clearInterval(y.current)}function C(P){if(o){var T=P.touches[0],w=T.screenX,_=T.screenY;i({x:w,y:_});var R=w-o.x,O=_-o.y;t(R,O);var j=Date.now();u(j),g(j-c),b({x:R,y:O})}}function $(){if(o&&(i(null),b(null),h)){var P=h.x/m,T=h.y/m,w=Math.abs(P),_=Math.abs(T);if(Math.max(w,_)<su)return;var R=P,O=T;y.current=window.setInterval(function(){if(Math.abs(R)<na&&Math.abs(O)<na){window.clearInterval(y.current);return}R*=ra,O*=ra,t(R*Tn,O*Tn)},Tn)}}var x=a.useRef();function I(P){var T=P.deltaX,w=P.deltaY,_=0,R=Math.abs(T),O=Math.abs(w);R===O?_=x.current==="x"?T:w:R>O?(_=T,x.current="x"):(_=w,x.current="y"),t(-_,-_)&&P.preventDefault()}var E=a.useRef(null);E.current={onTouchStart:S,onTouchMove:C,onTouchEnd:$,onWheel:I},a.useEffect(function(){function P(R){E.current.onTouchStart(R)}function T(R){E.current.onTouchMove(R)}function w(R){E.current.onTouchEnd(R)}function _(R){E.current.onWheel(R)}return document.addEventListener("touchmove",T,{passive:!1}),document.addEventListener("touchend",w,{passive:!0}),e.current.addEventListener("touchstart",P,{passive:!0}),e.current.addEventListener("wheel",_,{passive:!1}),function(){document.removeEventListener("touchmove",T),document.removeEventListener("touchend",w)}},[])}function $o(e){var t=a.useState(0),r=G(t,2),n=r[0],o=r[1],i=a.useRef(0),l=a.useRef();return l.current=e,di(function(){var s;(s=l.current)===null||s===void 0||s.call(l)},[n]),function(){i.current===n&&(i.current+=1,o(i.current))}}function uu(e){var t=a.useRef([]),r=a.useState({}),n=G(r,2),o=n[1],i=a.useRef(typeof e=="function"?e():e),l=$o(function(){var c=i.current;t.current.forEach(function(u){c=u(c)}),t.current=[],i.current=c,o({})});function s(c){t.current.push(c),l()}return[i.current,s]}var aa={width:0,height:0,left:0,top:0,right:0};function du(e,t,r,n,o,i,l){var s=l.tabs,c=l.tabPosition,u=l.rtl,d,f,m;return["top","bottom"].includes(c)?(d="width",f=u?"right":"left",m=Math.abs(r)):(d="height",f="top",m=-r),a.useMemo(function(){if(!s.length)return[0,0];for(var g=s.length,p=g,v=0;v<g;v+=1){var h=e.get(s[v].key)||aa;if(Math.floor(h[f]+h[d])>Math.floor(m+t)){p=v-1;break}}for(var b=0,y=g-1;y>=0;y-=1){var S=e.get(s[y].key)||aa;if(S[f]<m){b=y+1;break}}return b>p?[0,-1]:[b,p]},[e,t,n,o,i,m,c,s.map(function(g){return g.key}).join("_"),u])}function oa(e){var t;return e instanceof Map?(t={},e.forEach(function(r,n){t[n]=r})):t=e,JSON.stringify(t)}var fu="TABS_DQ";function xo(e){return String(e).replace(/"/g,fu)}function Or(e,t,r,n){return!(!r||n||e===!1||e===void 0&&(t===!1||t===null))}var wo=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.editable,o=e.locale,i=e.style;return!n||n.showAdd===!1?null:a.createElement("button",{ref:t,type:"button",className:"".concat(r,"-nav-add"),style:i,"aria-label":(o==null?void 0:o.addAriaLabel)||"Add tab",onClick:function(s){n.onEdit("add",{event:s})}},n.addIcon||"+")}),ia=a.forwardRef(function(e,t){var r=e.position,n=e.prefixCls,o=e.extra;if(!o)return null;var i,l={};return Wt(o)==="object"&&!a.isValidElement(o)?l=o:l.right=o,r==="right"&&(i=l.right),r==="left"&&(i=l.left),i?a.createElement("div",{className:"".concat(n,"-extra-content"),ref:t},i):null}),mu=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.id,o=e.tabs,i=e.locale,l=e.mobile,s=e.more,c=s===void 0?{}:s,u=e.style,d=e.className,f=e.editable,m=e.tabBarGutter,g=e.rtl,p=e.removeAriaLabel,v=e.onTabClick,h=e.getPopupContainer,b=e.popupClassName,y=a.useState(!1),S=G(y,2),C=S[0],$=S[1],x=a.useState(null),I=G(x,2),E=I[0],P=I[1],T=c.icon,w=T===void 0?"More":T,_="".concat(n,"-more-popup"),R="".concat(r,"-dropdown"),O=E!==null?"".concat(_,"-").concat(E):null,j=i==null?void 0:i.dropdownAriaLabel;function A(K,X){K.preventDefault(),K.stopPropagation(),f.onEdit("remove",{key:X,event:K})}var D=a.createElement(Sn,{onClick:function(X){var q=X.key,ge=X.domEvent;v(q,ge),$(!1)},prefixCls:"".concat(R,"-menu"),id:_,tabIndex:-1,role:"listbox","aria-activedescendant":O,selectedKeys:[E],"aria-label":j!==void 0?j:"expanded dropdown"},o.map(function(K){var X=K.closable,q=K.disabled,ge=K.closeIcon,te=K.key,Q=K.label,ie=Or(X,ge,f,q);return a.createElement(Wn,{key:te,id:"".concat(_,"-").concat(te),role:"option","aria-controls":n&&"".concat(n,"-panel-").concat(te),disabled:q},a.createElement("span",null,Q),ie&&a.createElement("button",{type:"button","aria-label":p||"remove",tabIndex:0,className:"".concat(R,"-menu-item-remove"),onClick:function(V){V.stopPropagation(),A(V,te)}},ge||f.removeIcon||"×"))}));function z(K){for(var X=o.filter(function(ie){return!ie.disabled}),q=X.findIndex(function(ie){return ie.key===E})||0,ge=X.length,te=0;te<ge;te+=1){q=(q+K+ge)%ge;var Q=X[q];if(!Q.disabled){P(Q.key);return}}}function L(K){var X=K.which;if(!C){[ae.DOWN,ae.SPACE,ae.ENTER].includes(X)&&($(!0),K.preventDefault());return}switch(X){case ae.UP:z(-1),K.preventDefault();break;case ae.DOWN:z(1),K.preventDefault();break;case ae.ESC:$(!1);break;case ae.SPACE:case ae.ENTER:E!==null&&v(E,K);break}}a.useEffect(function(){var K=document.getElementById(O);K&&K.scrollIntoView&&K.scrollIntoView(!1)},[E]),a.useEffect(function(){C||P(null)},[C]);var M=ce({},g?"marginRight":"marginLeft",m);o.length||(M.visibility="hidden",M.order=1);var B=k(ce({},"".concat(R,"-rtl"),g)),ee=l?null:a.createElement(ec,ye({prefixCls:R,overlay:D,visible:o.length?C:!1,onVisibleChange:$,overlayClassName:k(B,b),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:h},c),a.createElement("button",{type:"button",className:"".concat(r,"-nav-more"),style:M,"aria-haspopup":"listbox","aria-controls":_,id:"".concat(n,"-more"),"aria-expanded":C,onKeyDown:L},w));return a.createElement("div",{className:k("".concat(r,"-nav-operations"),d),style:u,ref:t},ee,a.createElement(wo,{prefixCls:r,locale:i,editable:f}))});const vu=a.memo(mu,function(e,t){return t.tabMoving});var gu=function(t){var r=t.prefixCls,n=t.id,o=t.active,i=t.focus,l=t.tab,s=l.key,c=l.label,u=l.disabled,d=l.closeIcon,f=l.icon,m=t.closable,g=t.renderWrapper,p=t.removeAriaLabel,v=t.editable,h=t.onClick,b=t.onFocus,y=t.onBlur,S=t.onKeyDown,C=t.onMouseDown,$=t.onMouseUp,x=t.style,I=t.tabCount,E=t.currentPosition,P="".concat(r,"-tab"),T=Or(m,d,v,u);function w(A){u||h(A)}function _(A){A.preventDefault(),A.stopPropagation(),v.onEdit("remove",{key:s,event:A})}var R=a.useMemo(function(){return f&&typeof c=="string"?a.createElement("span",null,c):c},[c,f]),O=a.useRef(null);a.useEffect(function(){i&&O.current&&O.current.focus()},[i]);var j=a.createElement("div",{key:s,"data-node-key":xo(s),className:k(P,ce(ce(ce(ce({},"".concat(P,"-with-remove"),T),"".concat(P,"-active"),o),"".concat(P,"-disabled"),u),"".concat(P,"-focus"),i)),style:x,onClick:w},a.createElement("div",{ref:O,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(s),className:"".concat(P,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(s),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(D){D.stopPropagation(),w(D)},onKeyDown:S,onMouseDown:C,onMouseUp:$,onFocus:b,onBlur:y},i&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(E," of ").concat(I)),f&&a.createElement("span",{className:"".concat(P,"-icon")},f),c&&R),T&&a.createElement("button",{type:"button",role:"tab","aria-label":p||"remove",tabIndex:o?0:-1,className:"".concat(P,"-remove"),onClick:function(D){D.stopPropagation(),_(D)}},d||v.removeIcon||"×"));return g?g(j):j},pu=function(t,r){var n=t.offsetWidth,o=t.offsetHeight,i=t.offsetTop,l=t.offsetLeft,s=t.getBoundingClientRect(),c=s.width,u=s.height,d=s.left,f=s.top;return Math.abs(c-n)<1?[c,u,d-r.left,f-r.top]:[n,o,l,i]},Jt=function(t){var r=t.current||{},n=r.offsetWidth,o=n===void 0?0:n,i=r.offsetHeight,l=i===void 0?0:i;if(t.current){var s=t.current.getBoundingClientRect(),c=s.width,u=s.height;if(Math.abs(c-o)<1)return[c,u]}return[o,l]},En=function(t,r){return t[r?0:1]},la=a.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.id,i=e.animated,l=e.activeKey,s=e.rtl,c=e.extra,u=e.editable,d=e.locale,f=e.tabPosition,m=e.tabBarGutter,g=e.children,p=e.onTabClick,v=e.onTabScroll,h=e.indicator,b=a.useContext(Vn),y=b.prefixCls,S=b.tabs,C=a.useRef(null),$=a.useRef(null),x=a.useRef(null),I=a.useRef(null),E=a.useRef(null),P=a.useRef(null),T=a.useRef(null),w=f==="top"||f==="bottom",_=ta(0,function(Ie,ue){w&&v&&v({direction:Ie>ue?"left":"right"})}),R=G(_,2),O=R[0],j=R[1],A=ta(0,function(Ie,ue){!w&&v&&v({direction:Ie>ue?"top":"bottom"})}),D=G(A,2),z=D[0],L=D[1],M=a.useState([0,0]),B=G(M,2),ee=B[0],K=B[1],X=a.useState([0,0]),q=G(X,2),ge=q[0],te=q[1],Q=a.useState([0,0]),ie=G(Q,2),$e=ie[0],V=ie[1],W=a.useState([0,0]),Y=G(W,2),F=Y[0],H=Y[1],ne=uu(new Map),be=G(ne,2),Z=be[0],xe=be[1],de=lu(S,Z,ge[0]),Se=En(ee,w),je=En(ge,w),Oe=En($e,w),Te=En(F,w),Ye=Math.floor(Se)<Math.floor(je+Oe),Re=Ye?Se-Te:Se-Oe,Ae="".concat(y,"-nav-operations-hidden"),_e=0,Ce=0;w&&s?(_e=0,Ce=Math.max(0,je-Re)):(_e=Math.min(0,Re-je),Ce=0);function Me(Ie){return Ie<_e?_e:Ie>Ce?Ce:Ie}var Le=a.useRef(null),ot=a.useState(),Be=G(ot,2),Ne=Be[0],De=Be[1];function U(){De(Date.now())}function fe(){Le.current&&clearTimeout(Le.current)}cu(I,function(Ie,ue){function Pe(He,Ge){He(function(rt){var ht=Me(rt+Ge);return ht})}return Ye?(w?Pe(j,Ie):Pe(L,ue),fe(),U(),!0):!1}),a.useEffect(function(){return fe(),Ne&&(Le.current=setTimeout(function(){De(0)},100)),fe},[Ne]);var Ue=du(de,Re,w?O:z,je,Oe,Te,le(le({},e),{},{tabs:S})),Fe=G(Ue,2),mt=Fe[0],tt=Fe[1],pt=dr(function(){var Ie=arguments.length>0&&arguments[0]!==void 0?arguments[0]:l,ue=de.get(Ie)||{width:0,height:0,left:0,right:0,top:0};if(w){var Pe=O;s?ue.right<O?Pe=ue.right:ue.right+ue.width>O+Re&&(Pe=ue.right+ue.width-Re):ue.left<-O?Pe=-ue.left:ue.left+ue.width>-O+Re&&(Pe=-(ue.left+ue.width-Re)),L(0),j(Me(Pe))}else{var He=z;ue.top<-z?He=-ue.top:ue.top+ue.height>-z+Re&&(He=-(ue.top+ue.height-Re)),j(0),L(Me(He))}}),bt=a.useState(),st=G(bt,2),it=st[0],nt=st[1],he=a.useState(!1),Ee=G(he,2),ve=Ee[0],Je=Ee[1],Xe=S.filter(function(Ie){return!Ie.disabled}).map(function(Ie){return Ie.key}),re=function(ue){var Pe=Xe.indexOf(it||l),He=Xe.length,Ge=(Pe+ue+He)%He,rt=Xe[Ge];nt(rt)},pe=function(ue,Pe){var He=Xe.indexOf(ue),Ge=S.find(function(ht){return ht.key===ue}),rt=Or(Ge==null?void 0:Ge.closable,Ge==null?void 0:Ge.closeIcon,u,Ge==null?void 0:Ge.disabled);rt&&(Pe.preventDefault(),Pe.stopPropagation(),u.onEdit("remove",{key:ue,event:Pe}),He===Xe.length-1?re(-1):re(1))},me=function(ue,Pe){Je(!0),Pe.button===1&&pe(ue,Pe)},se=function(ue){var Pe=ue.code,He=s&&w,Ge=Xe[0],rt=Xe[Xe.length-1];switch(Pe){case"ArrowLeft":{w&&re(He?1:-1);break}case"ArrowRight":{w&&re(He?-1:1);break}case"ArrowUp":{ue.preventDefault(),w||re(-1);break}case"ArrowDown":{ue.preventDefault(),w||re(1);break}case"Home":{ue.preventDefault(),nt(Ge);break}case"End":{ue.preventDefault(),nt(rt);break}case"Enter":case"Space":{ue.preventDefault(),p(it??l,ue);break}case"Backspace":case"Delete":{pe(it,ue);break}}},we={};w?we[s?"marginRight":"marginLeft"]=m:we.marginTop=m;var ze=S.map(function(Ie,ue){var Pe=Ie.key;return a.createElement(gu,{id:o,prefixCls:y,key:Pe,tab:Ie,style:ue===0?void 0:we,closable:Ie.closable,editable:u,active:Pe===l,focus:Pe===it,renderWrapper:g,removeAriaLabel:d==null?void 0:d.removeAriaLabel,tabCount:Xe.length,currentPosition:ue+1,onClick:function(Ge){p(Pe,Ge)},onKeyDown:se,onFocus:function(){ve||nt(Pe),pt(Pe),U(),I.current&&(s||(I.current.scrollLeft=0),I.current.scrollTop=0)},onBlur:function(){nt(void 0)},onMouseDown:function(Ge){return me(Pe,Ge)},onMouseUp:function(){Je(!1)}})}),vt=function(){return xe(function(){var ue,Pe=new Map,He=(ue=E.current)===null||ue===void 0?void 0:ue.getBoundingClientRect();return S.forEach(function(Ge){var rt,ht=Ge.key,Kt=(rt=E.current)===null||rt===void 0?void 0:rt.querySelector('[data-node-key="'.concat(xo(ht),'"]'));if(Kt){var qt=pu(Kt,He),At=G(qt,4),Ft=At[0],on=At[1],ln=At[2],sn=At[3];Pe.set(ht,{width:Ft,height:on,left:ln,top:sn})}}),Pe})};a.useEffect(function(){vt()},[S.map(function(Ie){return Ie.key}).join("_")]);var lt=$o(function(){var Ie=Jt(C),ue=Jt($),Pe=Jt(x);K([Ie[0]-ue[0]-Pe[0],Ie[1]-ue[1]-Pe[1]]);var He=Jt(T);V(He);var Ge=Jt(P);H(Ge);var rt=Jt(E);te([rt[0]-He[0],rt[1]-He[1]]),vt()}),ct=S.slice(0,mt),et=S.slice(tt+1),gt=[].concat(Qe(ct),Qe(et)),wt=de.get(l),jt=iu({activeTabOffset:wt,horizontal:w,indicator:h,rtl:s}),Ut=jt.style;a.useEffect(function(){pt()},[l,_e,Ce,oa(wt),oa(de),w]),a.useEffect(function(){lt()},[s]);var Et=!!gt.length,ut="".concat(y,"-nav-wrap"),Dt,Vt,Xt,$t;return w?s?(Vt=O>0,Dt=O!==Ce):(Dt=O<0,Vt=O!==_e):(Xt=z<0,$t=z!==_e),a.createElement(en,{onResize:lt},a.createElement("div",{ref:mr(t,C),role:"tablist","aria-orientation":w?"horizontal":"vertical",className:k("".concat(y,"-nav"),r),style:n,onKeyDown:function(){U()}},a.createElement(ia,{ref:$,position:"left",extra:c,prefixCls:y}),a.createElement(en,{onResize:lt},a.createElement("div",{className:k(ut,ce(ce(ce(ce({},"".concat(ut,"-ping-left"),Dt),"".concat(ut,"-ping-right"),Vt),"".concat(ut,"-ping-top"),Xt),"".concat(ut,"-ping-bottom"),$t)),ref:I},a.createElement(en,{onResize:lt},a.createElement("div",{ref:E,className:"".concat(y,"-nav-list"),style:{transform:"translate(".concat(O,"px, ").concat(z,"px)"),transition:Ne?"none":void 0}},ze,a.createElement(wo,{ref:T,prefixCls:y,locale:d,editable:u,style:le(le({},ze.length===0?void 0:we),{},{visibility:Et?"hidden":null})}),a.createElement("div",{className:k("".concat(y,"-ink-bar"),ce({},"".concat(y,"-ink-bar-animated"),i.inkBar)),style:Ut}))))),a.createElement(vu,ye({},e,{removeAriaLabel:d==null?void 0:d.removeAriaLabel,ref:P,prefixCls:y,tabs:gt,className:!Et&&Ae,tabMoving:!!Ne})),a.createElement(ia,{ref:x,position:"right",extra:c,prefixCls:y})))}),Eo=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.className,o=e.style,i=e.id,l=e.active,s=e.tabKey,c=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(s),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(s),"aria-hidden":!l,style:o,className:k(r,l&&"".concat(r,"-active"),n),ref:t},c)}),bu=["renderTabBar"],hu=["label","key"],yu=function(t){var r=t.renderTabBar,n=Ke(t,bu),o=a.useContext(Vn),i=o.tabs;if(r){var l=le(le({},n),{},{panes:i.map(function(s){var c=s.label,u=s.key,d=Ke(s,hu);return a.createElement(Eo,ye({tab:c,key:u,tabKey:u},d))})});return r(l,la)}return a.createElement(la,n)},Cu=["key","forceRender","style","className","destroyInactiveTabPane"],Su=function(t){var r=t.id,n=t.activeKey,o=t.animated,i=t.tabPosition,l=t.destroyInactiveTabPane,s=a.useContext(Vn),c=s.prefixCls,u=s.tabs,d=o.tabPane,f="".concat(c,"-tabpane");return a.createElement("div",{className:k("".concat(c,"-content-holder"))},a.createElement("div",{className:k("".concat(c,"-content"),"".concat(c,"-content-").concat(i),ce({},"".concat(c,"-content-animated"),d))},u.map(function(m){var g=m.key,p=m.forceRender,v=m.style,h=m.className,b=m.destroyInactiveTabPane,y=Ke(m,Cu),S=g===n;return a.createElement(Pa,ye({key:g,visible:S,forceRender:p,removeOnLeave:!!(l||b),leavedClassName:"".concat(f,"-hidden")},o.tabPaneMotion),function(C,$){var x=C.style,I=C.className;return a.createElement(Eo,ye({},y,{prefixCls:f,id:r,tabKey:g,animated:d,active:S,style:le(le({},v),x),className:k(h,I),ref:$}))})})))};function $u(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=le({inkBar:!0},Wt(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var xu=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],sa=0,wu=a.forwardRef(function(e,t){var r=e.id,n=e.prefixCls,o=n===void 0?"rc-tabs":n,i=e.className,l=e.items,s=e.direction,c=e.activeKey,u=e.defaultActiveKey,d=e.editable,f=e.animated,m=e.tabPosition,g=m===void 0?"top":m,p=e.tabBarGutter,v=e.tabBarStyle,h=e.tabBarExtraContent,b=e.locale,y=e.more,S=e.destroyInactiveTabPane,C=e.renderTabBar,$=e.onChange,x=e.onTabClick,I=e.onTabScroll,E=e.getPopupContainer,P=e.popupClassName,T=e.indicator,w=Ke(e,xu),_=a.useMemo(function(){return(l||[]).filter(function(F){return F&&Wt(F)==="object"&&"key"in F})},[l]),R=s==="rtl",O=$u(f),j=a.useState(!1),A=G(j,2),D=A[0],z=A[1];a.useEffect(function(){z(ya())},[]);var L=Ct(function(){var F;return(F=_[0])===null||F===void 0?void 0:F.key},{value:c,defaultValue:u}),M=G(L,2),B=M[0],ee=M[1],K=a.useState(function(){return _.findIndex(function(F){return F.key===B})}),X=G(K,2),q=X[0],ge=X[1];a.useEffect(function(){var F=_.findIndex(function(ne){return ne.key===B});if(F===-1){var H;F=Math.max(0,Math.min(q,_.length-1)),ee((H=_[F])===null||H===void 0?void 0:H.key)}ge(F)},[_.map(function(F){return F.key}).join("_"),B,q]);var te=Ct(null,{value:r}),Q=G(te,2),ie=Q[0],$e=Q[1];a.useEffect(function(){r||($e("rc-tabs-".concat(sa)),sa+=1)},[]);function V(F,H){x==null||x(F,H);var ne=F!==B;ee(F),ne&&($==null||$(F))}var W={id:ie,activeKey:B,animated:O,tabPosition:g,rtl:R,mobile:D},Y=le(le({},W),{},{editable:d,locale:b,more:y,tabBarGutter:p,onTabClick:V,onTabScroll:I,extra:h,style:v,panes:null,getPopupContainer:E,popupClassName:P,indicator:T});return a.createElement(Vn.Provider,{value:{tabs:_,prefixCls:o}},a.createElement("div",ye({ref:t,id:r,className:k(o,"".concat(o,"-").concat(g),ce(ce(ce({},"".concat(o,"-mobile"),D),"".concat(o,"-editable"),d),"".concat(o,"-rtl"),R),i)},w),a.createElement(yu,ye({},Y,{renderTabBar:C})),a.createElement(Su,ye({destroyInactiveTabPane:S},W,{animated:O}))))});const Eu={motionAppear:!1,motionEnter:!0,motionLeave:!0};function Iu(e,t={inkBar:!0,tabPane:!1}){let r;return t===!1?r={inkBar:!1,tabPane:!1}:t===!0?r={inkBar:!0,tabPane:!0}:r=Object.assign({inkBar:!0},typeof t=="object"?t:{}),r.tabPane&&(r.tabPaneMotion=Object.assign(Object.assign({},Eu),{motionName:gr(e,"switch")})),r}var Ou=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Ru(e){return e.filter(t=>t)}function Pu(e,t){if(e)return e.map(n=>{var o;const i=(o=n.destroyOnHidden)!==null&&o!==void 0?o:n.destroyInactiveTabPane;return Object.assign(Object.assign({},n),{destroyInactiveTabPane:i})});const r=hn(t).map(n=>{if(a.isValidElement(n)){const{key:o,props:i}=n,l=i||{},{tab:s}=l,c=Ou(l,["tab"]);return Object.assign(Object.assign({key:String(o)},c),{label:s})}return null});return Ru(r)}const Mu=e=>{const{componentCls:t,motionDurationSlow:r}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${r}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${r}`}}}}},[Nn(e,"slide-up"),Nn(e,"slide-down")]]},Tu=Mu,_u=e=>{const{componentCls:t,tabsCardPadding:r,cardBg:n,cardGutter:o,colorBorderSecondary:i,itemSelectedColor:l}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:r,background:n,border:`${N(e.lineWidth)} ${e.lineType} ${i}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:l,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:Ma(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:N(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:N(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${N(e.borderRadiusLG)} 0 0 ${N(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Nu=e=>{const{componentCls:t,itemHoverColor:r,dropdownEdgeChildVerticalPadding:n}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},Bt(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${N(n)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tn),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${N(e.paddingXXS)} ${N(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:r}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},Du=e=>{const{componentCls:t,margin:r,colorBorderSecondary:n,horizontalMargin:o,verticalItemPadding:i,verticalItemMargin:l,calc:s}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${N(e.lineWidth)} ${e.lineType} ${n}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:r,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:s(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:i,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:l},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:N(s(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${N(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:s(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${N(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},zu=e=>{const{componentCls:t,cardPaddingSM:r,cardPaddingLG:n,cardHeightSM:o,cardHeightLG:i,horizontalItemPaddingSM:l,horizontalItemPaddingLG:s}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:s,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:r},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${N(e.borderRadius)} ${N(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${N(e.borderRadius)} ${N(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${N(e.borderRadius)} ${N(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${N(e.borderRadius)} 0 0 ${N(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:i,minHeight:i}}}}}},Lu=e=>{const{componentCls:t,itemActiveColor:r,itemHoverColor:n,iconCls:o,tabsHorizontalItemMargin:i,horizontalItemPadding:l,itemSelectedColor:s,itemColor:c}=e,u=`${t}-tab`;return{[u]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:l,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:r}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${u}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},er(e)),"&:hover":{color:n},[`&${u}-active ${u}-btn`]:{color:s,textShadow:e.tabsActiveTextShadow},[`&${u}-focus ${u}-btn:focus-visible`]:Ma(e),[`&${u}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${u}-disabled ${u}-btn, &${u}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${u}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${u} + ${u}`]:{margin:{_skip_check_:!0,value:i}}}},Bu=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:r,iconCls:n,cardGutter:o,calc:i}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:r},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:N(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:N(e.marginXS)},marginLeft:{_skip_check_:!0,value:N(i(e.marginXXS).mul(-1).equal())},[n]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ju=e=>{const{componentCls:t,tabsCardPadding:r,cardHeight:n,cardGutter:o,itemHoverColor:i,itemActiveColor:l,colorBorderSecondary:s}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Bt(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:r,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:n,minHeight:n,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${N(e.lineWidth)} ${e.lineType} ${s}`,borderRadius:`${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:i},"&:active, &:focus:not(:focus-visible)":{color:l}},er(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),Lu(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},er(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},Au=e=>{const{cardHeight:t,cardHeightSM:r,cardHeightLG:n,controlHeight:o,controlHeightLG:i}=e,l=t||i,s=r||o,c=n||i+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:l,cardHeightSM:s,cardHeightLG:c,cardPadding:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(s-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}},Hu=Tt("Tabs",e=>{const t=Rt(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${N(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${N(e.horizontalItemGutter)}`});return[zu(t),Bu(t),Du(t),Nu(t),_u(t),ju(t),Tu(t)]},Au),Wu=()=>null,Vu=Wu;var Ku=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Fu=a.forwardRef((e,t)=>{var r,n,o,i,l,s,c,u,d,f,m;const{type:g,className:p,rootClassName:v,size:h,onEdit:b,hideAdd:y,centered:S,addIcon:C,removeIcon:$,moreIcon:x,more:I,popupClassName:E,children:P,items:T,animated:w,style:_,indicatorSize:R,indicator:O,destroyInactiveTabPane:j,destroyOnHidden:A}=e,D=Ku(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:z}=D,{direction:L,tabs:M,getPrefixCls:B,getPopupContainer:ee}=a.useContext(St),K=B("tabs",z),X=wa(K),[q,ge,te]=Hu(K,X),Q=a.useRef(null);a.useImperativeHandle(t,()=>({nativeElement:Q.current}));let ie;g==="editable-card"&&(ie={onEdit:(ne,{key:be,event:Z})=>{b==null||b(ne==="add"?Z:be,ne)},removeIcon:(r=$??(M==null?void 0:M.removeIcon))!==null&&r!==void 0?r:a.createElement(ba,null),addIcon:(C??(M==null?void 0:M.addIcon))||a.createElement(ou,null),showAdd:y!==!0});const $e=B(),V=Ln(h),W=Pu(T,P),Y=Iu(K,w),F=Object.assign(Object.assign({},M==null?void 0:M.style),_),H={align:(n=O==null?void 0:O.align)!==null&&n!==void 0?n:(o=M==null?void 0:M.indicator)===null||o===void 0?void 0:o.align,size:(c=(l=(i=O==null?void 0:O.size)!==null&&i!==void 0?i:R)!==null&&l!==void 0?l:(s=M==null?void 0:M.indicator)===null||s===void 0?void 0:s.size)!==null&&c!==void 0?c:M==null?void 0:M.indicatorSize};return q(a.createElement(wu,Object.assign({ref:Q,direction:L,getPopupContainer:ee},D,{items:W,className:k({[`${K}-${V}`]:V,[`${K}-card`]:["card","editable-card"].includes(g),[`${K}-editable-card`]:g==="editable-card",[`${K}-centered`]:S},M==null?void 0:M.className,p,v,ge,te,X),popupClassName:k(E,ge,te,X),style:F,editable:ie,more:Object.assign({icon:(m=(f=(d=(u=M==null?void 0:M.more)===null||u===void 0?void 0:u.icon)!==null&&d!==void 0?d:M==null?void 0:M.moreIcon)!==null&&f!==void 0?f:x)!==null&&m!==void 0?m:a.createElement(eu,null),transitionName:`${$e}-slide-up`},I),prefixCls:K,animated:Y,indicator:H,destroyInactiveTabPane:A??j})))}),Io=Fu;Io.TabPane=Vu;const Gu=Io;var Uu=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Xu=e=>{var{prefixCls:t,className:r,hoverable:n=!0}=e,o=Uu(e,["prefixCls","className","hoverable"]);const{getPrefixCls:i}=a.useContext(St),l=i("card",t),s=k(`${l}-grid`,r,{[`${l}-grid-hoverable`]:n});return a.createElement("div",Object.assign({},o,{className:s}))},Oo=Xu,qu=e=>{const{antCls:t,componentCls:r,headerHeight:n,headerPadding:o,tabsMarginBottom:i}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${N(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${N(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0 0`},jn()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},tn),{[`
          > ${r}-typography,
          > ${r}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:i,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${N(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},Yu=e=>{const{cardPaddingBase:t,colorBorderSecondary:r,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${N(o)} 0 0 0 ${r},
      0 ${N(o)} 0 0 ${r},
      ${N(o)} ${N(o)} 0 0 ${r},
      ${N(o)} 0 0 0 ${r} inset,
      0 ${N(o)} 0 0 ${r} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},ku=e=>{const{componentCls:t,iconCls:r,actionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:i,actionsBg:l}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:l,borderTop:`${N(e.lineWidth)} ${e.lineType} ${i}`,display:"flex",borderRadius:`0 0 ${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)}`},jn()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${r}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:N(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${r}`]:{fontSize:o,lineHeight:N(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${N(e.lineWidth)} ${e.lineType} ${i}`}}})},Qu=e=>Object.assign(Object.assign({margin:`${N(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},jn()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},tn),"&-description":{color:e.colorTextDescription}}),Zu=e=>{const{componentCls:t,colorFillAlter:r,headerPadding:n,bodyPadding:o}=e;return{[`${t}-head`]:{padding:`0 ${N(n)}`,background:r,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${N(e.padding)} ${N(o)}`}}},Ju=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},ed=e=>{const{componentCls:t,cardShadow:r,cardHeadPadding:n,colorBorderSecondary:o,boxShadowTertiary:i,bodyPadding:l,extraColor:s}=e;return{[t]:Object.assign(Object.assign({},Bt(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:i},[`${t}-head`]:qu(e),[`${t}-extra`]:{marginInlineStart:"auto",color:s,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:l,borderRadius:`0 0 ${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)}`},jn()),[`${t}-grid`]:Yu(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:ku(e),[`${t}-meta`]:Qu(e)}),[`${t}-bordered`]:{border:`${N(e.lineWidth)} ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:r}},[`${t}-contain-grid`]:{borderRadius:`${N(e.borderRadiusLG)} ${N(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:Zu(e),[`${t}-loading`]:Ju(e),[`${t}-rtl`]:{direction:"rtl"}}},td=e=>{const{componentCls:t,bodyPaddingSM:r,headerPaddingSM:n,headerHeightSM:o,headerFontSizeSM:i}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:o,padding:`0 ${N(n)}`,fontSize:i,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:r}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},nd=e=>{var t,r;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,headerHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:(t=e.bodyPadding)!==null&&t!==void 0?t:e.paddingLG,headerPadding:(r=e.headerPadding)!==null&&r!==void 0?r:e.paddingLG}},rd=Tt("Card",e=>{const t=Rt(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[ed(t),td(t)]},nd);var ca=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const ad=e=>{const{actionClasses:t,actions:r=[],actionStyle:n}=e;return a.createElement("ul",{className:t,style:n},r.map((o,i)=>{const l=`action-${i}`;return a.createElement("li",{style:{width:`${100/r.length}%`},key:l},a.createElement("span",null,o))}))},od=a.forwardRef((e,t)=>{const{prefixCls:r,className:n,rootClassName:o,style:i,extra:l,headStyle:s={},bodyStyle:c={},title:u,loading:d,bordered:f,variant:m,size:g,type:p,cover:v,actions:h,tabList:b,children:y,activeTabKey:S,defaultActiveTabKey:C,tabBarExtraContent:$,hoverable:x,tabProps:I={},classNames:E,styles:P}=e,T=ca(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:w,direction:_,card:R}=a.useContext(St),[O]=xa("card",m,f),j=de=>{var Se;(Se=e.onTabChange)===null||Se===void 0||Se.call(e,de)},A=de=>{var Se;return k((Se=R==null?void 0:R.classNames)===null||Se===void 0?void 0:Se[de],E==null?void 0:E[de])},D=de=>{var Se;return Object.assign(Object.assign({},(Se=R==null?void 0:R.styles)===null||Se===void 0?void 0:Se[de]),P==null?void 0:P[de])},z=a.useMemo(()=>{let de=!1;return a.Children.forEach(y,Se=>{(Se==null?void 0:Se.type)===Oo&&(de=!0)}),de},[y]),L=w("card",r),[M,B,ee]=rd(L),K=a.createElement(fi,{loading:!0,active:!0,paragraph:{rows:4},title:!1},y),X=S!==void 0,q=Object.assign(Object.assign({},I),{[X?"activeKey":"defaultActiveKey"]:X?S:C,tabBarExtraContent:$});let ge;const te=Ln(g),Q=!te||te==="default"?"large":te,ie=b?a.createElement(Gu,Object.assign({size:Q},q,{className:`${L}-head-tabs`,onChange:j,items:b.map(de=>{var{tab:Se}=de,je=ca(de,["tab"]);return Object.assign({label:Se},je)})})):null;if(u||l||ie){const de=k(`${L}-head`,A("header")),Se=k(`${L}-head-title`,A("title")),je=k(`${L}-extra`,A("extra")),Oe=Object.assign(Object.assign({},s),D("header"));ge=a.createElement("div",{className:de,style:Oe},a.createElement("div",{className:`${L}-head-wrapper`},u&&a.createElement("div",{className:Se,style:D("title")},u),l&&a.createElement("div",{className:je,style:D("extra")},l)),ie)}const $e=k(`${L}-cover`,A("cover")),V=v?a.createElement("div",{className:$e,style:D("cover")},v):null,W=k(`${L}-body`,A("body")),Y=Object.assign(Object.assign({},c),D("body")),F=a.createElement("div",{className:W,style:Y},d?K:y),H=k(`${L}-actions`,A("actions")),ne=h!=null&&h.length?a.createElement(ad,{actionClasses:H,actionStyle:D("actions"),actions:h}):null,be=Mt(T,["onTabChange"]),Z=k(L,R==null?void 0:R.className,{[`${L}-loading`]:d,[`${L}-bordered`]:O!=="borderless",[`${L}-hoverable`]:x,[`${L}-contain-grid`]:z,[`${L}-contain-tabs`]:b==null?void 0:b.length,[`${L}-${te}`]:te,[`${L}-type-${p}`]:!!p,[`${L}-rtl`]:_==="rtl"},n,o,B,ee),xe=Object.assign(Object.assign({},R==null?void 0:R.style),i);return M(a.createElement("div",Object.assign({ref:t},be,{className:Z,style:xe}),ge,V,F,ne))}),id=od;var ld=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const sd=e=>{const{prefixCls:t,className:r,avatar:n,title:o,description:i}=e,l=ld(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=a.useContext(St),c=s("card",t),u=k(`${c}-meta`,r),d=n?a.createElement("div",{className:`${c}-meta-avatar`},n):null,f=o?a.createElement("div",{className:`${c}-meta-title`},o):null,m=i?a.createElement("div",{className:`${c}-meta-description`},i):null,g=f||m?a.createElement("div",{className:`${c}-meta-detail`},f,m):null;return a.createElement("div",Object.assign({},l,{className:u}),d,g)},cd=sd,Rr=id;Rr.Grid=Oo;Rr.Meta=cd;const Jf=Rr,ud=e=>{const{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},dd=e=>{const{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:n,lineWidth:o,textPaddingInline:i,orientationMargin:l,verticalMarginInline:s}=e;return{[t]:Object.assign(Object.assign({},Bt(e)),{borderBlockStart:`${N(o)} solid ${n}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:s,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${N(o)} solid ${n}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${N(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${N(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${n}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${N(o)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${l} * 100%)`},"&::after":{width:`calc(100% - ${l} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${l} * 100%)`},"&::after":{width:`calc(${l} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:`${N(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:`${N(o)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:o,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},fd=e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),md=Tt("Divider",e=>{const t=Rt(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[dd(t),ud(t)]},fd,{unitless:{orientationMargin:!0}});var vd=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const gd={small:"sm",middle:"md"},pd=e=>{const{getPrefixCls:t,direction:r,className:n,style:o}=rn("divider"),{prefixCls:i,type:l="horizontal",orientation:s="center",orientationMargin:c,className:u,rootClassName:d,children:f,dashed:m,variant:g="solid",plain:p,style:v,size:h}=e,b=vd(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),y=t("divider",i),[S,C,$]=md(y),x=Ln(h),I=gd[x],E=!!f,P=a.useMemo(()=>s==="left"?r==="rtl"?"end":"start":s==="right"?r==="rtl"?"start":"end":s,[r,s]),T=P==="start"&&c!=null,w=P==="end"&&c!=null,_=k(y,n,C,$,`${y}-${l}`,{[`${y}-with-text`]:E,[`${y}-with-text-${P}`]:E,[`${y}-dashed`]:!!m,[`${y}-${g}`]:g!=="solid",[`${y}-plain`]:!!p,[`${y}-rtl`]:r==="rtl",[`${y}-no-default-orientation-margin-start`]:T,[`${y}-no-default-orientation-margin-end`]:w,[`${y}-${I}`]:!!I},u,d),R=a.useMemo(()=>typeof c=="number"?c:/^\d+$/.test(c)?Number(c):c,[c]),O={marginInlineStart:T?R:void 0,marginInlineEnd:w?R:void 0};return S(a.createElement("div",Object.assign({className:_,style:Object.assign(Object.assign({},o),v)},b,{role:"separator"}),f&&l!=="vertical"&&a.createElement("span",{className:`${y}-inner-text`,style:O},f)))},em=pd,bd=(e,t=!1)=>t&&e==null?[]:Array.isArray(e)?e:[e],hd=bd,yd=e=>{const{componentCls:t,iconCls:r,antCls:n,zIndexPopup:o,colorText:i,colorWarning:l,marginXXS:s,marginXS:c,fontSize:u,fontWeightStrong:d,colorTextHeading:f}=e;return{[t]:{zIndex:o,[`&${n}-popover`]:{fontSize:u},[`${t}-message`]:{marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${r}`]:{color:l,fontSize:u,lineHeight:1,marginInlineEnd:c},[`${t}-title`]:{fontWeight:d,color:f,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:s,color:i}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}}}},Cd=e=>{const{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}},Ro=Tt("Popconfirm",e=>yd(e),Cd,{resetStyle:!1});var Sd=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Po=e=>{const{prefixCls:t,okButtonProps:r,cancelButtonProps:n,title:o,description:i,cancelText:l,okText:s,okType:c="primary",icon:u=a.createElement(Ta,null),showCancel:d=!0,close:f,onConfirm:m,onCancel:g,onPopupClick:p}=e,{getPrefixCls:v}=a.useContext(St),[h]=yn("Popconfirm",mi.Popconfirm),b=nn(o),y=nn(i);return a.createElement("div",{className:`${t}-inner-content`,onClick:p},a.createElement("div",{className:`${t}-message`},u&&a.createElement("span",{className:`${t}-message-icon`},u),a.createElement("div",{className:`${t}-message-text`},b&&a.createElement("div",{className:`${t}-title`},b),y&&a.createElement("div",{className:`${t}-description`},y))),a.createElement("div",{className:`${t}-buttons`},d&&a.createElement(vi,Object.assign({onClick:g,size:"small"},n),l||(h==null?void 0:h.cancelText)),a.createElement(gi,{buttonProps:Object.assign(Object.assign({size:"small"},pi(c)),r),actionFn:m,close:f,prefixCls:v("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},s||(h==null?void 0:h.okText))))},$d=e=>{const{prefixCls:t,placement:r,className:n,style:o}=e,i=Sd(e,["prefixCls","placement","className","style"]),{getPrefixCls:l}=a.useContext(St),s=l("popconfirm",t),[c]=Ro(s);return c(a.createElement(ao,{placement:r,className:k(s,n),style:o,content:a.createElement(Po,Object.assign({prefixCls:s},i))}))},xd=$d;var wd=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ed=a.forwardRef((e,t)=>{var r,n;const{prefixCls:o,placement:i="top",trigger:l="click",okType:s="primary",icon:c=a.createElement(Ta,null),children:u,overlayClassName:d,onOpenChange:f,onVisibleChange:m,overlayStyle:g,styles:p,classNames:v}=e,h=wd(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:b,className:y,style:S,classNames:C,styles:$}=rn("popconfirm"),[x,I]=Ct(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(n=e.defaultOpen)!==null&&n!==void 0?n:e.defaultVisible}),E=(D,z)=>{I(D,!0),m==null||m(D),f==null||f(D,z)},P=D=>{E(!1,D)},T=D=>{var z;return(z=e.onConfirm)===null||z===void 0?void 0:z.call(globalThis,D)},w=D=>{var z;E(!1,D),(z=e.onCancel)===null||z===void 0||z.call(globalThis,D)},_=(D,z)=>{const{disabled:L=!1}=e;L||E(D,z)},R=b("popconfirm",o),O=k(R,y,d,C.root,v==null?void 0:v.root),j=k(C.body,v==null?void 0:v.body),[A]=Ro(R);return A(a.createElement(Vs,Object.assign({},Mt(h,["title"]),{trigger:l,placement:i,onOpenChange:_,open:x,ref:t,classNames:{root:O,body:j},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},$.root),S),g),p==null?void 0:p.root),body:Object.assign(Object.assign({},$.body),p==null?void 0:p.body)},content:a.createElement(Po,Object.assign({okType:s,icon:c},e,{prefixCls:R,close:P,onConfirm:T,onCancel:w})),"data-popover-inject":!0}),u))}),Mo=Ed;Mo._InternalPanelDoNotUseOrYouWillBeFired=xd;const tm=Mo;var Id={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};const Od=Id;var Rd=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Od}))},Pd=a.forwardRef(Rd);const Md=Pd;var Td={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};const _d=Td;var Nd=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:_d}))},Dd=a.forwardRef(Nd);const zd=Dd;var Ld={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};const Bd=Ld;var jd=function(t,r){return a.createElement(Lt,ye({},t,{ref:r,icon:Bd}))},Ad=a.forwardRef(jd);const nm=Ad,Hd=e=>{const{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:i}=e,l=i(n).sub(r).equal(),s=i(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},Bt(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${N(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},Pr=e=>{const{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return Rt(e,{tagFontSize:o,tagLineHeight:N(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},Mr=e=>({defaultBg:new vn(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),To=Tt("Tag",e=>{const t=Pr(e);return Hd(t)},Mr);var Wd=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Vd=a.forwardRef((e,t)=>{const{prefixCls:r,style:n,className:o,checked:i,children:l,icon:s,onChange:c,onClick:u}=e,d=Wd(e,["prefixCls","style","className","checked","children","icon","onChange","onClick"]),{getPrefixCls:f,tag:m}=a.useContext(St),g=S=>{c==null||c(!i),u==null||u(S)},p=f("tag",r),[v,h,b]=To(p),y=k(p,`${p}-checkable`,{[`${p}-checkable-checked`]:i},m==null?void 0:m.className,o,h,b);return v(a.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},n),m==null?void 0:m.style),className:y,onClick:g}),s,a.createElement("span",null,l)))}),Kd=Vd,Fd=e=>bi(e,(t,{textColor:r,lightBorderColor:n,lightColor:o,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),Gd=_a(["Tag","preset"],e=>{const t=Pr(e);return Fd(t)},Mr);function Ud(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const In=(e,t,r)=>{const n=Ud(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},Xd=_a(["Tag","status"],e=>{const t=Pr(e);return[In(t,"success","Success"),In(t,"processing","Info"),In(t,"error","Error"),In(t,"warning","Warning")]},Mr);var qd=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Yd=a.forwardRef((e,t)=>{const{prefixCls:r,className:n,rootClassName:o,style:i,children:l,icon:s,color:c,onClose:u,bordered:d=!0,visible:f}=e,m=qd(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:g,direction:p,tag:v}=a.useContext(St),[h,b]=a.useState(!0),y=Mt(m,["closeIcon","closable"]);a.useEffect(()=>{f!==void 0&&b(f)},[f]);const S=hi(c),C=yi(c),$=S||C,x=Object.assign(Object.assign({backgroundColor:c&&!$?c:void 0},v==null?void 0:v.style),i),I=g("tag",r),[E,P,T]=To(I),w=k(I,v==null?void 0:v.className,{[`${I}-${c}`]:$,[`${I}-has-color`]:c&&!$,[`${I}-hidden`]:!h,[`${I}-rtl`]:p==="rtl",[`${I}-borderless`]:!d},n,o,P,T),_=z=>{z.stopPropagation(),u==null||u(z),!z.defaultPrevented&&b(!1)},[,R]=Ci(_r(e),_r(v),{closable:!1,closeIconRender:z=>{const L=a.createElement("span",{className:`${I}-close-icon`,onClick:_},z);return Si(z,L,M=>({onClick:B=>{var ee;(ee=M==null?void 0:M.onClick)===null||ee===void 0||ee.call(M,B),_(B)},className:k(M==null?void 0:M.className,`${I}-close-icon`)}))}}),O=typeof m.onClick=="function"||l&&l.type==="a",j=s||null,A=j?a.createElement(a.Fragment,null,j,l&&a.createElement("span",null,l)):l,D=a.createElement("span",Object.assign({},y,{ref:t,className:w,style:x}),A,R,S&&a.createElement(Gd,{key:"preset",prefixCls:I}),C&&a.createElement(Xd,{key:"status",prefixCls:I}));return E(O?a.createElement($i,{component:"Tag"},D):D)}),_o=Yd;_o.CheckableTag=Kd;const rm=_o,kd=(e,t,r,n)=>{const{titleMarginBottom:o,fontWeightStrong:i}=n;return{marginBottom:o,color:r,fontWeight:i,fontSize:e,lineHeight:t}},Qd=e=>{const t=[1,2,3,4,5],r={};return t.forEach(n=>{r[`
      h${n}&,
      div&-h${n},
      div&-h${n} > textarea,
      h${n}
    `]=kd(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)}),r},Zd=e=>{const{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},Na(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},Jd=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:Wo[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:e.fontWeightStrong},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),ef=e=>{const{componentCls:t,paddingSM:r}=e,n=r;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:`calc(1em - ${N(n)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},tf=e=>({[`${e.componentCls}-copy-success`]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),nf=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),rf=e=>{const{componentCls:t,titleMarginTop:r}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},Qd(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:r},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:r}}}),Jd(e)),Zd(e)),{[`
        ${t}-expand,
        ${t}-collapse,
        ${t}-edit,
        ${t}-copy
      `]:Object.assign(Object.assign({},Na(e)),{marginInlineStart:e.marginXXS})}),ef(e)),tf(e)),nf()),{"&-rtl":{direction:"rtl"}})}},af=()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"}),No=Tt("Typography",rf,af),of=e=>{const{prefixCls:t,"aria-label":r,className:n,style:o,direction:i,maxLength:l,autoSize:s=!0,value:c,onSave:u,onCancel:d,onEnd:f,component:m,enterIcon:g=a.createElement(zd,null)}=e,p=a.useRef(null),v=a.useRef(!1),h=a.useRef(null),[b,y]=a.useState(c);a.useEffect(()=>{y(c)},[c]),a.useEffect(()=>{var O;if(!((O=p.current)===null||O===void 0)&&O.resizableTextArea){const{textArea:j}=p.current.resizableTextArea;j.focus();const{length:A}=j.value;j.setSelectionRange(A,A)}},[]);const S=({target:O})=>{y(O.value.replace(/[\n\r]/g,""))},C=()=>{v.current=!0},$=()=>{v.current=!1},x=({keyCode:O})=>{v.current||(h.current=O)},I=()=>{u(b.trim())},E=({keyCode:O,ctrlKey:j,altKey:A,metaKey:D,shiftKey:z})=>{h.current!==O||v.current||j||A||D||z||(O===ae.ENTER?(I(),f==null||f()):O===ae.ESC&&d())},P=()=>{I()},[T,w,_]=No(t),R=k(t,`${t}-edit-content`,{[`${t}-rtl`]:i==="rtl",[`${t}-${m}`]:!!m},n,w,_);return T(a.createElement("div",{className:R,style:o},a.createElement(xi,{ref:p,maxLength:l,value:b,onChange:S,onKeyDown:x,onKeyUp:E,onCompositionStart:C,onCompositionEnd:$,onBlur:P,"aria-label":r,rows:1,autoSize:s}),g!==null?Ea(g,{className:`${t}-edit-content-confirm`}):null))},lf=of;var sf=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,r=[],n=0;n<e.rangeCount;n++)r.push(e.getRangeAt(n));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null;break}return e.removeAllRanges(),function(){e.type==="Caret"&&e.removeAllRanges(),e.rangeCount||r.forEach(function(o){e.addRange(o)}),t&&t.focus()}},cf=sf,ua={"text/plain":"Text","text/html":"Url",default:"Text"},uf="Copy to clipboard: #{key}, Enter";function df(e){var t=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C";return e.replace(/#{\s*key\s*}/g,t)}function ff(e,t){var r,n,o,i,l,s,c=!1;t||(t={}),r=t.debug||!1;try{o=cf(),i=document.createRange(),l=document.getSelection(),s=document.createElement("span"),s.textContent=e,s.ariaHidden="true",s.style.all="unset",s.style.position="fixed",s.style.top=0,s.style.clip="rect(0, 0, 0, 0)",s.style.whiteSpace="pre",s.style.webkitUserSelect="text",s.style.MozUserSelect="text",s.style.msUserSelect="text",s.style.userSelect="text",s.addEventListener("copy",function(d){if(d.stopPropagation(),t.format)if(d.preventDefault(),typeof d.clipboardData>"u"){r&&console.warn("unable to use e.clipboardData"),r&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var f=ua[t.format]||ua.default;window.clipboardData.setData(f,e)}else d.clipboardData.clearData(),d.clipboardData.setData(t.format,e);t.onCopy&&(d.preventDefault(),t.onCopy(d.clipboardData))}),document.body.appendChild(s),i.selectNodeContents(s),l.addRange(i);var u=document.execCommand("copy");if(!u)throw new Error("copy command was unsuccessful");c=!0}catch(d){r&&console.error("unable to copy using execCommand: ",d),r&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),c=!0}catch(f){r&&console.error("unable to copy using clipboardData: ",f),r&&console.error("falling back to prompt"),n=df("message"in t?t.message:uf),window.prompt(n,e)}}finally{l&&(typeof l.removeRange=="function"?l.removeRange(i):l.removeAllRanges()),s&&document.body.removeChild(s),o()}return c}var mf=ff;const vf=Ao(mf);var gf=globalThis&&globalThis.__awaiter||function(e,t,r,n){function o(i){return i instanceof r?i:new r(function(l){l(i)})}return new(r||(r=Promise))(function(i,l){function s(d){try{u(n.next(d))}catch(f){l(f)}}function c(d){try{u(n.throw(d))}catch(f){l(f)}}function u(d){d.done?i(d.value):o(d.value).then(s,c)}u((n=n.apply(e,t||[])).next())})};const pf=({copyConfig:e,children:t})=>{const[r,n]=a.useState(!1),[o,i]=a.useState(!1),l=a.useRef(null),s=()=>{l.current&&clearTimeout(l.current)},c={};e.format&&(c.format=e.format),a.useEffect(()=>s,[]);const u=dr(d=>gf(void 0,void 0,void 0,function*(){var f;d==null||d.preventDefault(),d==null||d.stopPropagation(),i(!0);try{const m=typeof e.text=="function"?yield e.text():e.text;vf(m||hd(t,!0).join("")||"",c),i(!1),n(!0),s(),l.current=setTimeout(()=>{n(!1)},3e3),(f=e.onCopy)===null||f===void 0||f.call(e,d)}catch(m){throw i(!1),m}}));return{copied:r,copyLoading:o,onClick:u}},bf=pf;function qn(e,t){return a.useMemo(()=>{const r=!!e;return[r,Object.assign(Object.assign({},t),r&&typeof e=="object"?e:null)]},[e])}const hf=e=>{const t=a.useRef(void 0);return a.useEffect(()=>{t.current=e}),t.current},yf=hf,Cf=(e,t,r)=>a.useMemo(()=>e===!0?{title:t??r}:a.isValidElement(e)?{title:e}:typeof e=="object"?Object.assign({title:t??r},e):{title:e},[e,t,r]),Sf=Cf;var $f=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const xf=a.forwardRef((e,t)=>{const{prefixCls:r,component:n="article",className:o,rootClassName:i,setContentRef:l,children:s,direction:c,style:u}=e,d=$f(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:f,direction:m,className:g,style:p}=rn("typography"),v=c??m,h=l?bn(t,l):t,b=f("typography",r),[y,S,C]=No(b),$=k(b,g,{[`${b}-rtl`]:v==="rtl"},o,i,S,C),x=Object.assign(Object.assign({},p),u);return y(a.createElement(n,Object.assign({className:$,style:x,ref:h},d),s))}),Do=xf;function da(e){return e===!1?[!1,!1]:Array.isArray(e)?e:[e]}function Yn(e,t,r){return e===!0||e===void 0?t:e||r&&t}function wf(e){const t=document.createElement("em");e.appendChild(t);const r=e.getBoundingClientRect(),n=t.getBoundingClientRect();return e.removeChild(t),r.left>n.left||n.right>r.right||r.top>n.top||n.bottom>r.bottom}const Tr=e=>["string","number"].includes(typeof e),Ef=({prefixCls:e,copied:t,locale:r,iconOnly:n,tooltips:o,icon:i,tabIndex:l,onCopy:s,loading:c})=>{const u=da(o),d=da(i),{copied:f,copy:m}=r??{},g=t?f:m,p=Yn(u[t?1:0],g),v=typeof p=="string"?p:g;return a.createElement(Bn,{title:p,getPopupContainer:h=>h.parentNode},a.createElement("button",{type:"button",className:k(`${e}-copy`,{[`${e}-copy-success`]:t,[`${e}-copy-icon-only`]:n}),onClick:s,"aria-label":v,tabIndex:l},t?Yn(d[1],a.createElement(eo,null),!0):Yn(d[0],c?a.createElement($a,null):a.createElement(Md,null),!0)))},If=Ef,On=a.forwardRef(({style:e,children:t},r)=>{const n=a.useRef(null);return a.useImperativeHandle(r,()=>({isExceed:()=>{const o=n.current;return o.scrollHeight>o.clientHeight},getHeight:()=>n.current.clientHeight})),a.createElement("span",{"aria-hidden":!0,ref:n,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},t)}),Of=e=>e.reduce((t,r)=>t+(Tr(r)?String(r).length:1),0);function fa(e,t){let r=0;const n=[];for(let o=0;o<e.length;o+=1){if(r===t)return n;const i=e[o],s=Tr(i)?String(i).length:1,c=r+s;if(c>t){const u=t-r;return n.push(String(i).slice(0,u)),n}n.push(i),r=c}return e}const kn=0,Qn=1,Zn=2,Jn=3,ma=4,Rn={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function Rf(e){const{enableMeasure:t,width:r,text:n,children:o,rows:i,expanded:l,miscDeps:s,onEllipsis:c}=e,u=a.useMemo(()=>hn(n),[n]),d=a.useMemo(()=>Of(u),[n]),f=a.useMemo(()=>o(u,!1),[n]),[m,g]=a.useState(null),p=a.useRef(null),v=a.useRef(null),h=a.useRef(null),b=a.useRef(null),y=a.useRef(null),[S,C]=a.useState(!1),[$,x]=a.useState(kn),[I,E]=a.useState(0),[P,T]=a.useState(null);Ht(()=>{x(t&&r&&d?Qn:kn)},[r,n,i,t,u]),Ht(()=>{var O,j,A,D;if($===Qn){x(Zn);const z=v.current&&getComputedStyle(v.current).whiteSpace;T(z)}else if($===Zn){const z=!!(!((O=h.current)===null||O===void 0)&&O.isExceed());x(z?Jn:ma),g(z?[0,d]:null),C(z);const L=((j=h.current)===null||j===void 0?void 0:j.getHeight())||0,M=i===1?0:((A=b.current)===null||A===void 0?void 0:A.getHeight())||0,B=((D=y.current)===null||D===void 0?void 0:D.getHeight())||0,ee=Math.max(L,M+B);E(ee+1),c(z)}},[$]);const w=m?Math.ceil((m[0]+m[1])/2):0;Ht(()=>{var O;const[j,A]=m||[0,0];if(j!==A){const z=(((O=p.current)===null||O===void 0?void 0:O.getHeight())||0)>I;let L=w;A-j===1&&(L=z?j:A),g(z?[j,L]:[L,A])}},[m,w]);const _=a.useMemo(()=>{if(!t)return o(u,!1);if($!==Jn||!m||m[0]!==m[1]){const O=o(u,!1);return[ma,kn].includes($)?O:a.createElement("span",{style:Object.assign(Object.assign({},Rn),{WebkitLineClamp:i})},O)}return o(l?u:fa(u,m[0]),S)},[l,$,m,u].concat(Qe(s))),R={width:r,margin:0,padding:0,whiteSpace:P==="nowrap"?"normal":"inherit"};return a.createElement(a.Fragment,null,_,$===Zn&&a.createElement(a.Fragment,null,a.createElement(On,{style:Object.assign(Object.assign(Object.assign({},R),Rn),{WebkitLineClamp:i}),ref:h},f),a.createElement(On,{style:Object.assign(Object.assign(Object.assign({},R),Rn),{WebkitLineClamp:i-1}),ref:b},f),a.createElement(On,{style:Object.assign(Object.assign(Object.assign({},R),Rn),{WebkitLineClamp:1}),ref:y},o([],!0))),$===Jn&&m&&m[0]!==m[1]&&a.createElement(On,{style:Object.assign(Object.assign({},R),{top:400}),ref:p},o(fa(u,w),!0)),$===Qn&&a.createElement("span",{style:{whiteSpace:"inherit"},ref:v}))}const Pf=({enableEllipsis:e,isEllipsis:t,children:r,tooltipProps:n})=>!(n!=null&&n.title)||!e?r:a.createElement(Bn,Object.assign({open:t?void 0:!1},n),r),Mf=Pf;var Tf=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function _f({mark:e,code:t,underline:r,delete:n,strong:o,keyboard:i,italic:l},s){let c=s;function u(d,f){f&&(c=a.createElement(d,{},c))}return u("strong",o),u("u",r),u("del",n),u("code",t),u("mark",e),u("kbd",i),u("i",l),c}const Nf="...",va=["delete","mark","code","underline","strong","keyboard","italic"],Df=a.forwardRef((e,t)=>{var r;const{prefixCls:n,className:o,style:i,type:l,disabled:s,children:c,ellipsis:u,editable:d,copyable:f,component:m,title:g}=e,p=Tf(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:v,direction:h}=a.useContext(St),[b]=yn("Text"),y=a.useRef(null),S=a.useRef(null),C=v("typography",n),$=Mt(p,va),[x,I]=qn(d),[E,P]=Ct(!1,{value:I.editing}),{triggerType:T=["icon"]}=I,w=U=>{var fe;U&&((fe=I.onStart)===null||fe===void 0||fe.call(I)),P(U)},_=yf(E);Ht(()=>{var U;!E&&_&&((U=S.current)===null||U===void 0||U.focus())},[E]);const R=U=>{U==null||U.preventDefault(),w(!0)},O=U=>{var fe;(fe=I.onChange)===null||fe===void 0||fe.call(I,U),w(!1)},j=()=>{var U;(U=I.onCancel)===null||U===void 0||U.call(I),w(!1)},[A,D]=qn(f),{copied:z,copyLoading:L,onClick:M}=bf({copyConfig:D,children:c}),[B,ee]=a.useState(!1),[K,X]=a.useState(!1),[q,ge]=a.useState(!1),[te,Q]=a.useState(!1),[ie,$e]=a.useState(!0),[V,W]=qn(u,{expandable:!1,symbol:U=>U?b==null?void 0:b.collapse:b==null?void 0:b.expand}),[Y,F]=Ct(W.defaultExpanded||!1,{value:W.expanded}),H=V&&(!Y||W.expandable==="collapsible"),{rows:ne=1}=W,be=a.useMemo(()=>H&&(W.suffix!==void 0||W.onEllipsis||W.expandable||x||A),[H,W,x,A]);Ht(()=>{V&&!be&&(ee(zr("webkitLineClamp")),X(zr("textOverflow")))},[be,V]);const[Z,xe]=a.useState(H),de=a.useMemo(()=>be?!1:ne===1?K:B,[be,K,B]);Ht(()=>{xe(de&&H)},[de,H]);const Se=H&&(Z?te:q),je=H&&ne===1&&Z,Oe=H&&ne>1&&Z,Te=(U,fe)=>{var Ue;F(fe.expanded),(Ue=W.onExpand)===null||Ue===void 0||Ue.call(W,U,fe)},[Ye,Re]=a.useState(0),Ae=({offsetWidth:U})=>{Re(U)},_e=U=>{var fe;ge(U),q!==U&&((fe=W.onEllipsis)===null||fe===void 0||fe.call(W,U))};a.useEffect(()=>{const U=y.current;if(V&&Z&&U){const fe=wf(U);te!==fe&&Q(fe)}},[V,Z,c,Oe,ie,Ye]),a.useEffect(()=>{const U=y.current;if(typeof IntersectionObserver>"u"||!U||!Z||!H)return;const fe=new IntersectionObserver(()=>{$e(!!U.offsetParent)});return fe.observe(U),()=>{fe.disconnect()}},[Z,H]);const Ce=Sf(W.tooltip,I.text,c),Me=a.useMemo(()=>{if(!(!V||Z))return[I.text,c,g,Ce.title].find(Tr)},[V,Z,g,Ce.title,Se]);if(E)return a.createElement(lf,{value:(r=I.text)!==null&&r!==void 0?r:typeof c=="string"?c:"",onSave:O,onCancel:j,onEnd:I.onEnd,prefixCls:C,className:o,style:i,direction:h,component:m,maxLength:I.maxLength,autoSize:I.autoSize,enterIcon:I.enterIcon});const Le=()=>{const{expandable:U,symbol:fe}=W;return U?a.createElement("button",{type:"button",key:"expand",className:`${C}-${Y?"collapse":"expand"}`,onClick:Ue=>Te(Ue,{expanded:!Y}),"aria-label":Y?b.collapse:b==null?void 0:b.expand},typeof fe=="function"?fe(Y):fe):null},ot=()=>{if(!x)return;const{icon:U,tooltip:fe,tabIndex:Ue}=I,Fe=hn(fe)[0]||(b==null?void 0:b.edit),mt=typeof Fe=="string"?Fe:"";return T.includes("icon")?a.createElement(Bn,{key:"edit",title:fe===!1?"":Fe},a.createElement("button",{type:"button",ref:S,className:`${C}-edit`,onClick:R,"aria-label":mt,tabIndex:Ue},U||a.createElement(wi,{role:"button"}))):null},Be=()=>A?a.createElement(If,Object.assign({key:"copy"},D,{prefixCls:C,copied:z,locale:b,onCopy:M,loading:L,iconOnly:c==null})):null,Ne=U=>[U&&Le(),ot(),Be()],De=U=>[U&&!Y&&a.createElement("span",{"aria-hidden":!0,key:"ellipsis"},Nf),W.suffix,Ne(U)];return a.createElement(en,{onResize:Ae,disabled:!H},U=>a.createElement(Mf,{tooltipProps:Ce,enableEllipsis:H,isEllipsis:Se},a.createElement(Do,Object.assign({className:k({[`${C}-${l}`]:l,[`${C}-disabled`]:s,[`${C}-ellipsis`]:V,[`${C}-ellipsis-single-line`]:je,[`${C}-ellipsis-multiple-line`]:Oe},o),prefixCls:n,style:Object.assign(Object.assign({},i),{WebkitLineClamp:Oe?ne:void 0}),component:m,ref:bn(U,y,t),direction:h,onClick:T.includes("text")?R:void 0,"aria-label":Me==null?void 0:Me.toString(),title:g},$),a.createElement(Rf,{enableMeasure:H&&!Z,text:c,rows:ne,width:Ye,onEllipsis:_e,expanded:Y,miscDeps:[z,Y,L,x,A,b].concat(Qe(va.map(fe=>e[fe])))},(fe,Ue)=>_f(e,a.createElement(a.Fragment,null,fe.length>0&&Ue&&!Y&&Me?a.createElement("span",{key:"show-content","aria-hidden":!0},fe):fe,De(Ue)))))))}),Kn=Df;var zf=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Lf=a.forwardRef((e,t)=>{var{ellipsis:r,rel:n}=e,o=zf(e,["ellipsis","rel"]);const i=Object.assign(Object.assign({},o),{rel:n===void 0&&o.target==="_blank"?"noopener noreferrer":n});return delete i.navigate,a.createElement(Kn,Object.assign({},i,{ref:t,ellipsis:!!r,component:"a"}))}),Bf=Lf,jf=a.forwardRef((e,t)=>a.createElement(Kn,Object.assign({ref:t},e,{component:"div"}))),Af=jf;var Hf=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Wf=(e,t)=>{var{ellipsis:r}=e,n=Hf(e,["ellipsis"]);const o=a.useMemo(()=>r&&typeof r=="object"?Mt(r,["expandable","rows"]):r,[r]);return a.createElement(Kn,Object.assign({ref:t},n,{ellipsis:o,component:"span"}))},Vf=a.forwardRef(Wf);var Kf=globalThis&&globalThis.__rest||function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ff=[1,2,3,4,5],Gf=a.forwardRef((e,t)=>{const{level:r=1}=e,n=Kf(e,["level"]),o=Ff.includes(r)?`h${r}`:"h1";return a.createElement(Kn,Object.assign({ref:t},n,{component:o}))}),Uf=Gf,$n=Do;$n.Text=Vf;$n.Link=Bf;$n.Title=Uf;$n.Paragraph=Af;const am=$n;export{Jf as C,ho as D,Sn as E,Nt as F,Ur as L,Wn as M,ou as P,Nr as R,Xc as S,am as T,Ir as a,eu as b,yo as c,La as d,za as e,Ba as f,Dr as g,ec as h,Nn as i,ls as j,os as k,Rs as l,kf as m,zr as n,un as o,es as p,em as q,rm as r,Da as s,Vs as t,Cn as u,nm as v,tm as w,Zf as x,Qf as y,Wc as z};
