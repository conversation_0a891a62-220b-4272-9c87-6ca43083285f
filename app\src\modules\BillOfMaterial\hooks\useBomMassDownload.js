import { useCallback} from "react";
import { doAjax } from "@components/Common/fetchService";
import { destination_BOM } from "../../../destinationVariables";
import { useLocation, useNavigate } from "react-router-dom";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useSelector } from "react-redux";
import { END_POINTS } from "@constant/apiEndPoints";
import { useSnackbar } from "@hooks/useSnackbar";
import { ERROR_MESSAGES, TASK_NAME } from "../../../constant/enum";


const useBomMassDownload = (setLoaderMessage, setBlurLoading) => {
  const navigate = useNavigate();
  const { showSnackbar } = useSnackbar();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const CreatedRequestId = useSelector((state) => state.bom.requestHeaderID);
  const payloadData = useSelector((state) => state.bom.BOMpayloadData);

  const handleDownloadBOM = useCallback(() => {
    setLoaderMessage("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.");
    setBlurLoading(true);
    let payload = {
      "region": payloadData?.Region || "US",
      "scenario": payloadData?.RequestType || RequestType,
      "dtName": "MDG_BOM_MATERIAL_FIELD_CONFIG",
      "version": "v3",
      "role": TASK_NAME?.REQ_INITIATE_DOWNLOAD_MAT,
      "requestId": payloadData?.RequestId ? payloadData?.RequestId : RequestId || CreatedRequestId,
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showSnackbar(ERROR_MESSAGES?.NO_DATA_FOUND, "error");
        return
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", "Mass_Create.xlsx");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      showSnackbar(`${payloadData?.TemplateName ? `${payloadData.TemplateName}_Mass Change` : "Mass_Create"}.xlsx has been downloaded successfully.`, "success");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    const hError = () => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error");
    }

    const downloadUrl = `/${destination_BOM}${END_POINTS.EXCEL.DOWNLOAD_EXCEL_BOM}`;
    doAjax(downloadUrl, "postandgetblob", hSuccess, hError, payload);
  }, [setBlurLoading, setLoaderMessage, payloadData]);

  return { handleDownloadBOM };
};

export default useBomMassDownload;
