import React, { useState, forwardRef, useRef, useEffect, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tooltip,
  Box,
  Typography,
  Slide,
  FormControl,
  FormControlLabel,
  tooltipClasses,
  RadioGroup,
  IconButton,
  Radio,
  Tabs,
  Tab,
  useTheme,
} from "@mui/material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import styled from "@emotion/styled";
import { doAjax } from "./fetchService";
import { DataGrid } from "@mui/x-data-grid";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import { setChangeFieldRows, setRequestorPayload, updateMaterialData } from "../../app/payloadslice";
import useChangeMaterialRowsRequestor from "../../hooks/useChangeMaterialRowsRequestor";
import ReusableBackDrop from "./ReusableBackDrop";
import ReusableSnackBar from "./ReusableSnackBar";
import DownloadDialog from "./DownloadDialog";
import { useNavigate } from "react-router-dom";
import { MANDATORY_FILTERS, TEMPLATE_KEYS, Templates } from "@constant/changeTemplates";
import ReusableIcon from "./ReusableIcon";
import { saveExcel, showToast } from "../../functions";
import { setDropDown } from "../../app/dropDownDataSlice"
import { CHANGE_KEYS, ERROR_MESSAGES, REQUEST_TYPE, API_CODE } from "@constant/enum";
import { colors } from "@constant/colors";
import FilterChangeDropdown from './ui/dropdown/FilterChangeDropdown';
import { filterAndMapPlantData, filterAndMapWarehouseData, readExcelFile } from "@helper/helper";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { END_POINTS } from "@constant/apiEndPoints";
import HandleScrollDropdown from "./ui/dropdown/HandleScrollDropdown";

// Slide transition component
const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ReusableDialogForDropdown = ({ open, onClose, parameters, templateName, setShowTable, allDropDownData }) => {
  const [selectedValues, setSelectedValues] = useState({});
  const [convertedValues, setConvertedValues] = useState({});
  const [errors, setErrors] = useState({});
  const [blurLoading, setBlurLoading] = useState("");
  const [successMsg, setSuccessMsg] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [materialOptions, setMaterialOptions] = useState([]);
  const [errorTextMessage, setErrorTextMessage] = useState("");
  const [errorText, setErrorText] = useState(false);
  const initialPayload = useSelector((state) => state.payload.payloadData?.data || state.payload.payloadData );
  const RequestId = useSelector((state) => state.request.requestHeader.requestId);
  const loadForFetching = useSelector((state) => state.payload.dataLoading);
  const regionBasedSalesOrgData = useSelector((state) => state.request.salesOrgDTData);
  const [dropDownData, setDropDownData] = useState({});
  const [isFilterLoading, setIsFilterLoading] = useState({
      [CHANGE_KEYS.MATERIAL_NUM]: false,
      [CHANGE_KEYS.PLANT]: false,
      [CHANGE_KEYS.SALES_ORG]: false,
      [CHANGE_KEYS.DIVISION]: false,
      [CHANGE_KEYS.DIST_CHNL]: false,
      [CHANGE_KEYS.WAREHOUSE]: false,
      [CHANGE_KEYS.STORAGE_LOC]: false,
      [CHANGE_KEYS.MRP_CTRLER]: false
    });

  const [skip, setSkip] = useState(0); // Skip value for API call
  const [inputState, setInputState] = useState({ code: "", desc: "" }); // Skip value for API call
  const [timerId, setTimerId] = useState(null);
  const dropdownRef = useRef(null); // Ref for the dropdown
  const [isLoading, setIsLoading] = useState(false); // Loading state

  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { fetchDisplayDataRequestor } = useChangeMaterialRowsRequestor();
  const [activeTab, setActiveTab] = useState(0); 
  const [activeHandler, setActiveHandler] = useState(null);
  const [rowsOfMaterialData, setRowsOfMaterialData] = useState([]);
  const [totalMaterialCount, setTotalMaterialCount] = useState(0);
  const [isCopyFileDownloaded, setIsCopyFileDownloaded] = useState(0);
  const fileInputRef = useRef();
  const theme = useTheme()
  const handleIconClick = () => {
    fileInputRef?.current?.click(); 
  };
  const columnsOfMaterialData = Templates[initialPayload?.TemplateName]?.map((item) => ({
    field: item.key, 
    headerName: item.key,
    editable: true,
    flex: 2,
  }));
  const limit = 200

  const handlePasteMaterialData = useCallback((event) => {
    event.preventDefault();
    const clipboardData = event.clipboardData || window.clipboardData;
    const pastedData = clipboardData.getData("Text");

    const newRows = pastedData
      .trim()
      .split("\n")
      .map((row, rowIndex) => {
        const values = row.split("\t");
        const rowData = { id: rowIndex + 1 }; 
        columnsOfMaterialData.forEach((col, colIndex) => {
          rowData[col.field] = values[colIndex] || "";
        });
        return rowData;
      });
    setRowsOfMaterialData(newRows);
  }, []);

  useEffect(() => {
     if (activeTab===1) {
      document.addEventListener("paste", handlePasteMaterialData);
      return () => {
        document.removeEventListener("paste", handlePasteMaterialData);
      };
    }
  }, [activeTab, handlePasteMaterialData]);
 
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    if(activeTab === 1) {
      setActiveHandler("handlePasteMaterialData")
    }
    
  };

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const functions_ExportAsExcelMaterialData = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      columnsOfMaterialData?.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Material Data`,
        columns: excelColumns,
        rows: rowsOfMaterialData,
      })
      setIsCopyFileDownloaded(1);
    },
    
  };

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const handleSelectionChange = (key, newValue) => {
    setSelectedValues((prev) => ({
      ...prev,
      [key]: newValue,
    }));
    if (newValue.length > 0) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  useEffect(() => {
    setConvertedValues(convertedData(selectedValues));
    dispatch(setRequestorPayload(convertedData(selectedValues)));
  },[selectedValues])

  useEffect(() => {
    if(rowsOfMaterialData) {
      let result = convertRowDataToSelectedValues(rowsOfMaterialData);
      setSelectedValues(result);
    }
  }, [rowsOfMaterialData])

  const handleSelectAll = (key, allOptions) => {
    const allSelected = selectedValues[key]?.length === allOptions.length;
    setSelectedValues((prev) => ({
      ...prev,
      [key]: allSelected ? [] : allOptions,
    }));
    if (!allSelected) {
      setErrors((prev) => ({
        ...prev,
        [key]: "",
      }));
    }
  };

  const validateFields = () => {
    const newErrors = {};
    parameters?.forEach((param) => {
      if (!selectedValues[param.key] || selectedValues[param.key].length === 0) {
        newErrors[param.key] = `${param.key} is required.`;
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const convertedData = (data) => {
    const result = {};
  
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        // Map the code values and join them with commas
        result[key] = data[key].map((item) => item.code).join(",");
      }
    }
  
    return result;
  }

  const convertRowDataToSelectedValues = (data) => {
    const result = {};
  
    data.forEach((row) => {
      Object.keys(row).forEach((key) => {
        if (key !== "id" && row[key].trim() !== "") { // Exclude 'id' and empty values
          if (!result[key]) {
            result[key] = [];
          }
          result[key].push({ code: row[key].trim() });
        }
      });
    });
  
    return result;
  };

  const validateMandatoryFields = (templateName) => {
    const mandatoryFields = MANDATORY_FILTERS[templateName] || [];
    const emptyMandatoryFields = mandatoryFields?.filter(
      (fieldName) => !convertedValues[fieldName] || convertedValues[fieldName].trim() === ""
    );
  
    if (emptyMandatoryFields.length > 0) {
      setErrorText(true);
      setErrorTextMessage(ERROR_MESSAGES.MANDATORY_FILTER_MD(emptyMandatoryFields.join(', ')));
      return false;
    }
  
    return true;
  };

  const handleOkClick = async () => {
    if (!validateMandatoryFields(templateName)) {
      return; // Stop execution if validation fails
    }

    try {
      const result = await fetchDisplayDataRequestor(templateName, convertedValues);

      if (result && result.length > 0) {
        setErrorText(false);
        setShowTable(true);
      } else {
        setErrorText(true);
        setErrorTextMessage('No data found for the selected criteria.');
      }
    } catch (error) {
      setErrorText(true);
      setErrorTextMessage('Error fetching data.');
    }
  };


  const handleDownload = () => {
    setLoaderMessage("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.");
    setBlurLoading(true);
    onClose();
    let templateKeys = Templates[initialPayload?.TemplateName]?.map(item => item.key) || [];
    let payload = {};
    if(activeTab===0){
      payload = {
      materialDetails : [templateKeys.reduce((acc, key) => {
        acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
        return acc;
      }, {})],
      templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
      requestId: RequestId || initialPayload?.RequestId || "",
      templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
      dtName : "MDG_MAT_CHANGE_TEMPLATE",
      version : "v5",
      rolePrefix : ""
    }
    }
    else{
      payload = {
        materialDetails : [templateKeys.reduce((acc, key) => {
          acc[key] = rowsOfMaterialData.map(row => row[key]?.trim()).filter(value => value !== "")
          .join(",") || "";
          return acc;
        }, {})],
        templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
        dtName : "MDG_MAT_CHANGE_TEMPLATE",
        version : "v5",
        rolePrefix : ""
      }
    }
    const hSuccess = (response) => {

      if(response?.size==0){
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {position: "top-center", largeWidth: true,});
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `${initialPayload.TemplateName}_Mass Change.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setMessageDialogMessage(`${initialPayload.TemplateName}_Mass Change.xlsx has been downloaded successfully.`);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {position : "top-center"});
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    doAjax(`/${destination_MaterialMgmt}/excel/downloadExcelWithData`, "postandgetblob", hSuccess, hError, payload)

  };

  const handleEmailDownload = () => {
    setBlurLoading(true);
    onClose();
    let templateKeys = Templates[initialPayload?.TemplateName]?.map(item => item.key) || [];
    let payload = {};
    if(activeTab===0){
      payload = {
      materialDetails : [templateKeys.reduce((acc, key) => {
        acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
        return acc;
      }, {})],
      templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
      requestId: RequestId || initialPayload?.RequestId || "",
      templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
      dtName : "MDG_MAT_CHANGE_TEMPLATE",
      version : "v4",
      rolePrefix : ""
    }
    }
    else{
      payload = {
        materialDetails : [templateKeys.reduce((acc, key) => {
          acc[key] = rowsOfMaterialData.map(row => row[key]?.trim()).filter(value => value !== "")
          .join(",") || "";
          return acc;
        }, {})],
        templateHeaders : initialPayload?.FieldName ? initialPayload.FieldName?.join("$^$"): "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName : initialPayload?.TemplateName ? initialPayload.TemplateName : "",
        dtName : "MDG_MAT_CHANGE_TEMPLATE",
        version : "v4",
        rolePrefix : ""
      }
    }
    const hSuccess = () => {

      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(`Download has been started. You will get the Excel file via email.`);
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    }
    doAjax(`/${destination_MaterialMgmt}/excel/downloadExcelWithDataInMail`, "postandgetblob", hSuccess, hError, payload)

  };

  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };

  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };

  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  }

  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };

  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };

  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };

  useEffect(() => {
    const { [CHANGE_KEYS?.MATERIAL_NUM]: ignored, ...rest } = selectedValues || {};
    if (rest && Object.keys(rest).length > 0) {
      setInputState({ code: "", desc: "" });
      getMaterialNo("", true);
    }
  }, [JSON.stringify({ ...selectedValues, [CHANGE_KEYS.MATERIAL_NUM]: undefined })]);

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value?.toUpperCase();
    setInputState({ code: inputValue, desc: "" });
    setSkip(0);
    if (timerId) {
      clearTimeout(timerId);
    }
    const newTimerId = setTimeout(() => {
      getMaterialNo(inputValue, true);
    }, 500);

    setTimerId(newTimerId);
  };

  const getMaterialNo = (value = "", reset = false) => {
    setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: true }));
  
    const division = selectedValues[CHANGE_KEYS?.DIVISION]?.map((item) => item?.code).join("$^$")  || "";
    const plant = selectedValues[CHANGE_KEYS?.PLANT]?.map((item) => item?.code).join("$^$")  || "";
    const salesOrg = selectedValues[CHANGE_KEYS?.SALES_ORG]?.map((item) => item?.code).join("$^$") || regionBasedSalesOrgData?.uniqueSalesOrgList?.map(item => item.code).join("$^$") || "";
    const distrChan = selectedValues[CHANGE_KEYS?.DIST_CHNL]?.map((item) => item?.code).join("$^$")  || "";
    const mrpCtlr = selectedValues[CHANGE_KEYS?.MRP_CTRLER]?.map((item) => item?.code).join("$^$")  || "";
    const whseNo = selectedValues[CHANGE_KEYS?.WAREHOUSE]?.map((item) => item?.code).join("$^$")  || "";
  
    let endpoint = "";
    let payload = {
      materialNo: value ?? "",
      salesOrg: salesOrg,
      top: limit,
      skip: reset ? 0 : skip,
    };
  
    switch (templateName) {
      case TEMPLATE_KEYS?.LOGISTIC:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.LOGISTIC;
        payload = { ...payload, division, plant };
        break;
      case TEMPLATE_KEYS?.MRP:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.MRP;
        payload = { ...payload, division, plant, mrpCtlr };
        break;
      case TEMPLATE_KEYS?.ITEM_CAT:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.SALES;
        payload = { ...payload, division, salesOrg, distrChan };
        break;
      case TEMPLATE_KEYS?.WARE_VIEW_2:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.WAREHOUSE;
        payload = { ...payload, division, plant, whseNo };
        break;
      case TEMPLATE_KEYS?.CHG_STAT:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.CHG_STATUS;
        payload = { ...payload, division, salesOrg, distrChan };
        break;
      case TEMPLATE_KEYS?.SET_DNU:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.SET_DNU;
        payload = { ...payload, division, salesOrg, distrChan, plant };
        break;
      case TEMPLATE_KEYS?.UPD_DESC:
        endpoint = END_POINTS?.MAT_SEARCH_APIS?.DESC;
        payload = { ...payload, division, plant };
        break;
      default:
        return;
    }
  
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        if (data?.count !== undefined) {
          setTotalMaterialCount(data?.count);
        }
        
        if (reset) {
          setMaterialOptions(data?.body);
          setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.MATERIAL_NUM]: data.body }));
          setSkip(0);
        } else {
          setMaterialOptions((prevOptions) => [...prevOptions, ...data?.body]);
          setDropDownData((prev) => ({
            ...prev,
            [CHANGE_KEYS?.MATERIAL_NUM]: [
              ...(prev[CHANGE_KEYS?.MATERIAL_NUM] || []),
              ...data.body,
            ],
          }));
        }
      } else if (data?.statusCode === API_CODE.STATUS_414) {
        showToast(data?.message, "error");
        setMaterialOptions([]);
        setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.MATERIAL_NUM]: [] }));
        setTotalMaterialCount(0);
      }
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: false }));
      setIsLoading(false);
    };
  
    const hError = () => {
      setIsLoading(false);
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MATERIAL_NUM]: false }));
    };
  
    setIsLoading(true);
    doAjax(`/${destination_MaterialMgmt}${endpoint}`, "post", hSuccess, hError, payload);
  };
  

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target;
    if (scrollTop + clientHeight >= scrollHeight - 10 && 
        !isLoading && 
        !isFilterLoading[CHANGE_KEYS.MATERIAL_NUM] &&
        materialOptions.length < totalMaterialCount) {
      setSkip((prevSkip) => prevSkip + limit);
    }
  };

  useEffect(() => {
    if (skip > 0) {
      getMaterialNo(inputState?.code, false);
    }
  }, [skip]);

  useEffect(() => {
    parameters?.forEach((param) => {
      if (param.key === CHANGE_KEYS?.SALES_ORG) {
        setDropDownData((prev) => ({ ...prev, [param.key]: regionBasedSalesOrgData?.uniqueSalesOrgList || [] }))
      } else if (param.key === CHANGE_KEYS?.PLANT) {
        setDropDownData((prev) => ({ ...prev, [param.key]: regionBasedSalesOrgData?.uniquePlantList || [] }))
      }
    });
  }, [parameters]);

  useEffect(() => {
    if(regionBasedSalesOrgData?.salesOrgData?.length > 0  && !selectedValues[CHANGE_KEYS?.SALES_ORG])  {
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.SALES_ORG]: regionBasedSalesOrgData?.uniqueSalesOrgList || [] }))
      const uniquePlantData = filterAndMapPlantData(regionBasedSalesOrgData?.uniqueSalesOrgList, regionBasedSalesOrgData)
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.PLANT]: uniquePlantData }))
      // setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.PLANT]: regionBasedSalesOrgData?.uniquePlantList || [] }))
    }
  }, [regionBasedSalesOrgData])
  
  useEffect(() => {
    if(selectedValues[CHANGE_KEYS?.SALES_ORG] && selectedValues[CHANGE_KEYS?.SALES_ORG].length === 0) {
      selectedValues[CHANGE_KEYS?.DIST_CHNL] = []
      selectedValues[CHANGE_KEYS?.PLANT] = []
    }
    if(templateName === TEMPLATE_KEYS?.SET_DNU) {
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.PLANT]: [] }))
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.DIST_CHNL]: [] }))
    }
    if(templateName === TEMPLATE_KEYS?.ITEM_CAT || templateName === TEMPLATE_KEYS?.CHG_STAT) {
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.DIST_CHNL]: [] }))
    }
    if(selectedValues[CHANGE_KEYS?.SALES_ORG] && selectedValues[CHANGE_KEYS?.SALES_ORG].length > 0) {
      fetchDistChnlLookupData();
      const uniquePlantData = filterAndMapPlantData(selectedValues[CHANGE_KEYS?.SALES_ORG], regionBasedSalesOrgData)
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.PLANT]: uniquePlantData }))
    }
  }, [selectedValues[CHANGE_KEYS?.SALES_ORG]])

  useEffect(() => {
    if(selectedValues[CHANGE_KEYS?.PLANT] && selectedValues[CHANGE_KEYS?.PLANT].length === 0) {
      selectedValues[CHANGE_KEYS?.MRP_CTRLER] = []
      selectedValues[CHANGE_KEYS?.WAREHOUSE] = []
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.MRP_CTRLER]: [] }))
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.WAREHOUSE]: [] }))
    }
    if(selectedValues[CHANGE_KEYS?.PLANT] && selectedValues[CHANGE_KEYS?.PLANT].length > 0) {
      fetchMRPCtrlerLookupData();
      const uniqueWarehouseData = filterAndMapWarehouseData(selectedValues[CHANGE_KEYS?.PLANT], regionBasedSalesOrgData)
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.WAREHOUSE]: uniqueWarehouseData }))
    }
  }, [selectedValues[CHANGE_KEYS?.PLANT]])

  const fetchDistChnlLookupData = () => {
    setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: true }));
    let payload={
      salesOrg : selectedValues[CHANGE_KEYS?.SALES_ORG] 
      ? selectedValues[CHANGE_KEYS?.SALES_ORG]?.map((item) => item?.code).join("$^$") 
      : ""
    }
    const successHandler = (data) => {
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.DIST_CHNL]: data.body }))
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: false }));
    };
    const errorHandler = (error) => {
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.DIST_CHNL]: false }));
    };

    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_DISTRCHNL}`, "post", successHandler, errorHandler, payload);
  }

  const fetchMRPCtrlerLookupData = () => {
    setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MRP_CTRLER]: true }));
    let payload={
      plant : selectedValues[CHANGE_KEYS?.PLANT] 
      ? selectedValues[CHANGE_KEYS?.PLANT]?.map((item) => item?.code).join("$^$") 
      : ""
    }
    const successHandler = (data) => {
      setDropDownData((prev) => ({ ...prev, [CHANGE_KEYS?.MRP_CTRLER]: data.body }))
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MRP_CTRLER]: false }));
    };
    const errorHandler = (error) => {
      setIsFilterLoading(prev => ({ ...prev, [CHANGE_KEYS.MRP_CTRLER]: false }));
    };

    doAjax(`/${destination_MaterialMgmt}${END_POINTS.DATA.GET_MRP_CONTROLLER_BASED_ON_PLANT}`, "post", successHandler, errorHandler, payload);
  }
  

  const fetchOrgLookupData = (field) => {
    setIsFilterLoading(prev => ({ ...prev, [field]: true }));
    const endpoints = {
      // [CHANGE_KEYS?.PLANT]: "/getPlant",
      // [CHANGE_KEYS?.SALES_ORG]: "/getSalesOrg",
      [CHANGE_KEYS?.WAREHOUSE]: "/getWareHouseNo",
    };
    const successHandler = (data) => {
      setDropDownData((prev) => ({ ...prev, [field]: data.body }))
      dispatch(setDropDown({ keyName: field, data: data?.body }))
      setIsFilterLoading(prev => ({ ...prev, [field]: false }));
    };
    const errorHandler = (error) => {
      setIsFilterLoading(prev => ({ ...prev, [field]: false }));
    };

    doAjax(`/${destination_MaterialMgmt}/data${endpoints[field]}`, "get", successHandler, errorHandler);
  };

  const renderAutocomplete = (param) => {
    const formatOptionLabel = (option) => {
      if (option.code && option.desc) {
        return `${option.code} - ${option.desc}`;
      }
      return option.code || "";
    };

    if(param.key === CHANGE_KEYS?.MATERIAL_NUM) {
      return (
        <HandleScrollDropdown
          param={param}
          mandatory={MANDATORY_FILTERS?.[templateName]?.includes(param?.key)}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          inputState={inputState}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          handleMatInputChange={handleMatInputChange}
          handleScroll={handleScroll}
          dropdownRef={dropdownRef}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isMaterialNum={true}
          isLoading={isFilterLoading[CHANGE_KEYS.MATERIAL_NUM]}
          singleSelect={(templateName === TEMPLATE_KEYS?.LOGISTIC || initialPayload?.TemplateName === TEMPLATE_KEYS?.LOGISTIC) && initialPayload?.RequestType === REQUEST_TYPE?.CHANGE}
          hasMoreItems={materialOptions.length < totalMaterialCount}
          totalCount={totalMaterialCount}
          loadedCount={materialOptions.length}
        />
      );
    }
    else if(
      param.key === CHANGE_KEYS?.PLANT || 
      param.key === CHANGE_KEYS?.SALES_ORG || 
      param.key === CHANGE_KEYS?.MRP_CTRLER || 
      param.key === CHANGE_KEYS?.DIVISION || 
      param.key === CHANGE_KEYS?.WAREHOUSE || 
      param.key === CHANGE_KEYS?.DIST_CHNL
    ) {
      return (
        <FilterChangeDropdown
          param={param}
          mandatory={MANDATORY_FILTERS?.[templateName]?.includes(param?.key)}
          dropDownData={dropDownData}
          allDropDownData={allDropDownData}
          selectedValues={selectedValues}
          handleSelectAll={handleSelectAll}
          handleSelectionChange={handleSelectionChange}
          errors={errors}
          formatOptionLabel={formatOptionLabel}
          handlePopoverOpen={handlePopoverOpen}
          handlePopoverClose={handlePopoverClose}
          handleMouseEnterPopover={handleMouseEnterPopover}
          handleMouseLeavePopover={handleMouseLeavePopover}
          isPopoverVisible={isPopoverVisible}
          popoverId={popoverId}
          popoverAnchorEl={popoverAnchorEl}
          popoverRef={popoverRef}
          popoverContent={popoverContent}
          isMaterialNum={false}
          isLoading={isFilterLoading[param.key]}
          isSelectAll={true}
        />
      );
    }
  };

  const handleFileChange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const excelData = await readExcelFile(file);
    const formatted = excelData.map((row, index) => {
      const rowData = { id: index + 1 }; 
      columnsOfMaterialData.forEach((col, colIndex) => {
        rowData[col.field] = row[col.field] || "";
      });
      return rowData
    });
    setRowsOfMaterialData(formatted);
    e.target.value = null;
  };
  

  return (
    <>
    <Dialog
      open={open}
      TransitionComponent={Transition}
      keepMounted
      onClose={() => {}}
      maxWidth={activeTab === 1 ? "md" : "sm"}
      fullWidth
    >
      <Box
        sx={{
          background: theme.palette.primary.light,
          padding: "1rem 1.5rem",
          display: "flex",
          alignItems: "center",
        }}
      >
        <FeedOutlinedIcon color="primary" sx={{ marginRight: "0.5rem" }} />
        <Typography variant="h6" component="div" color="primary">
          {templateName} Search Filter(s)
        </Typography>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Search Filter" />
          <Tab label="Copy Material" />
        </Tabs>
        {activeTab === 1 && (
          <Box sx={{ display: 'flex', gap: 1, marginLeft: 'auto', pr: 2 }}>
            <Tooltip title="Export Table">
              <IconButton onClick={functions_ExportAsExcelMaterialData.convertJsonToExcel}>
                <ReusableIcon iconName="Download" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Upload Excel" disabled={!isCopyFileDownloaded}>
              <IconButton onClick={handleIconClick} >
                <ReusableIcon iconName="Upload" />
              </IconButton>
            </Tooltip>
            <input
              type="file"
              accept=".xlsx, .xls"
              ref={fileInputRef}
              style={{ display: "none" }}
              onChange={handleFileChange}
            />
          </Box>
        )}
      </Box>


      <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
        {activeTab === 0 && (
          <>
            {parameters?.map((param) => (
              <Box key={param.key} sx={{ marginBottom: "1rem" }}>
                {renderAutocomplete(param)}
              </Box>
            ))}
          </>
        )}
        {activeTab === 1 && (
          <Box>
            <DataGrid style={{ height: 400, width: '100%' }}
                  rows={rowsOfMaterialData}
                  columns={columnsOfMaterialData}
            />
          </Box>
        )}
        {errorText && (
          <Typography variant="h6" color={colors?.error?.dark}>* {errorTextMessage}</Typography>
        )}
        <ReusableBackDrop
          blurLoading={loadForFetching}
        />
      </DialogContent>
      <DialogActions 
        sx={{ 
          padding: "0.5rem 1.5rem", 
          display: "flex", 
          justifyContent: "space-between", 
          alignItems: "center" 
        }}
      >
        <Typography 
          variant="caption"
          sx={{ 
            color: "text.secondary",
            fontWeight: "bold",
          }}
        >
          Note: Please choose other Mandatory fields before selecting Material Number
        </Typography>

        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            onClick={() => {
              if(initialPayload?.RequestType === REQUEST_TYPE?.CHANGE) {
                navigate(APP_END_POINTS?.REQUEST_BENCH)
                onClose();
                return;
              }
              onClose();
            }}
            color="error"
            variant="outlined"
            sx={{
              height: 36,
              minWidth: "3.5rem",
              textTransform: "none",
              borderColor: "#cc3300",
              fontWeight: 500,
            }}
          >
            Cancel
          </Button>
          {initialPayload?.RequestType !== REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
            <Button
              onClick={handleOkClick}
              variant="contained"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                textTransform: "none",
                fontWeight: 500,
              }}
            >
              OK
            </Button>
          )}
          {initialPayload?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
            <Button
              onClick={() => {
                if (!validateMandatoryFields(templateName)) {
                  return;
                }
                handleDownloadDialogOpen()
              }}
              variant="contained"
              sx={{
                height: 36,
                minWidth: "3.5rem",
                backgroundColor: "#3B30C8",
                textTransform: "none",
                fontWeight: 500,
                "&:hover": {
                  backgroundColor: "#2c278f",
                },
              }}
            >
              Download
            </Button>
          )}
        </Box>
      </DialogActions>
    </Dialog>
    {/* <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            Select Download Option
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", 
                      fontSize: "12px",
                      overflow: "hidden",
                      textOverflow: "ellipsis", 
                    }}
                  >
                    Here Excel will be downloaded
                  </span>
                }
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", 
                      fontSize: "12px",
                      
                      overflow: "hidden",
                      textOverflow: "ellipsis", 
                    }}
                  >
                    Here Excel will be sent to your email
                  </span>
                }
              
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onDownloadTypeChange}>
            OK
          </Button>
        </DialogActions>
      </Dialog> */}

    <DownloadDialog
      onDownloadTypeChange={onDownloadTypeChange}
      open={openDownloadDialog}
      downloadType={downloadType}
      handleDownloadTypeChange={handleDownloadTypeChange}
      onClose={handleDownloadDialogClose}
    />
    <ReusableBackDrop
      blurLoading={blurLoading}
      loaderMessage={loaderMessage}
    />
    {successMsg && (
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={messageDialogMessage}
        alertType={alertType}
        handleSnackBarClose={handleSnackBarClose}
      />
    )}
  </>
    
  );
};

export default ReusableDialogForDropdown;
