import {
  Box,
  Grid,
  Typography,
} from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { container_Padding } from "../common/commonStyles";
import FilterFieldGlobal from "./FilterFieldGlobal";
import { colors } from "@constant/colors";

const GenericTabsGlobal = (props) => {
  
  let filterFields = props?.basicDataTabDetails && Object?.entries(props?.basicDataTabDetails);
  const [basicJsx, setbasicJsx] = useState([]);
  const tabsRef = useRef({});
  useEffect(() =>{
    let errorCard = getCardName(filterFields,props.fieldErrors)
    const ref = tabsRef?.current[errorCard];
      if (ref && ref?.scrollIntoView && !props.missingFieldsDialogOpen) {
        setTimeout(() => ref.scrollIntoView({ behavior: "smooth", block: "center" }), 400);
      }
  },[props.fieldErrors,props.missingFieldsDialogOpen])
  
  const mandatoryFailedFields = Array.isArray(props.fieldErrors) ? props.fieldErrors : [];
  const getCardName = (filterFieldsArr) => {
    for (const searchJsonName of mandatoryFailedFields) {
      const foundCard = filterFieldsArr.find(([cardName, fields]) =>
        fields.some(field => field.jsonName === searchJsonName)
      );
      if (foundCard) return foundCard[0]; // cardName
    }
    return null;
  };
  useEffect(() => {
    setbasicJsx(
      filterFields?.map((item) => {
        const cardName = item[0];
        const cardFields = item[1];
        return (
          <Grid
            item
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: `1px solid ${(props?.missingValidationCards?.includes(props?.activeViewTab) && checkForMandatoryFieldCard(cardFields, props?.fieldErrors)) && props?.selectedRow?.validated !== true ? colors.error.dark : "#E0E0E0"}`,
              mt: 0.5,
              mb: 1,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
            key={cardName}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  paddingBottom:"10px",
                }}
              >
                {item[0]}
              </Typography>
            </Grid>
            <Box>
              <Grid container spacing={1} paddingBottom={1} ref={el => { tabsRef.current[item[0]] = el; }}>
                {[...item[1]]
                  .filter((x) => x.visibility != "Hidden")
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  ?.map((innerItem) => {
                    const isIndividuallyDisabled = ["Description", "CompanyCode","GLname","Accounttype","AccountGroup"].includes(innerItem.jsonName);
                    const isFieldError = props?.fieldErrors?.includes(innerItem.fieldName) || props?.fieldErrors?.includes(innerItem.jsonName);
                    return (
                      <FilterFieldGlobal
                          key={innerItem.fieldName}
                          disabled={props?.disabled|| isIndividuallyDisabled}
                          field={innerItem}
                          dropDownData={props.dropDownData}
                          uniqueId={props?.uniqueId}
                          viewName={props?.activeViewTab}
                          selectedRow={props?.selectedRow}
                          module={props?.module}
                          isError={isFieldError}
                          missingFields={Array.isArray(props?.fieldErrors) ? props?.fieldErrors : []}
                      />
                    );
                  })}
              </Grid>
            </Box>
          </Grid>
        );
      })
    );
  }, [props?.basicDataTabDetails, props.activeViewTab, props?.uniqueId, props?.selectedRow?.id, props?.fieldErrors, props?.mandatoryFailedView, props?.missingValidationCards]);

  return <>{basicJsx}</>;
};

export default GenericTabsGlobal;