import{a as ie,s as de,u as re,n as H,r as B,aM as ne,ae,C as we,au as Ee,aT as be,b2 as Q,j as l,a4 as _e,Z as V,a8 as De,$ as Se,a6 as se,T as p,c as ee,ch as ye,B as Te,an as ce,br as Oe,O as k,dX as z,be as P,bi as W,dY as ke,af as Me,aa as xe,aK as Ae,dZ as j,b1 as Le,a9 as qe,d_ as ve,F as le,Q as Ne,b5 as Ge,b6 as Be,d as Fe}from"./index-226a1e75.js";import{d as oe}from"./DeleteOutline-259b9549.js";import{u as ue}from"./useChangeLogUpdate-23c3e0f8.js";import{M as We,u as Ve}from"./useCustomDtCall-f90ca5c1.js";import{S as Re}from"./SingleSelectDropdown-ee61a6b7.js";const He=({materialID:e,selectedMaterialNumber:I,disabled:d})=>{var F;const{t:f}=ie(),s=de(),n=re(),{updateChangeLog:R}=ue(),L=new URLSearchParams(n.search).get("RequestId"),A=H(r=>r.payload.payloadData),J=H(r=>r.payload),C=((F=J[e])==null?void 0:F.additionalData)||[],[o,x]=B.useState([]);B.useEffect(()=>{g(),v()},[e]);const g=async()=>{var r,N,w;try{const a=i=>x((i==null?void 0:i.body)||[]),t=()=>{var i;return ne((i=ae)==null?void 0:i.ERROR_FETCHING_LANGU,"error")};we(`/${Ee}${(N=(r=be)==null?void 0:r.DATA)==null?void 0:N.GET_LANGUAGE}`,"get",a,t)}catch{ne((w=ae)==null?void 0:w.ERROR_FETCHING_LANGU,"error")}},v=()=>{var w,a,t,i,c;const r=((a=(w=J[e])==null?void 0:w.headerData)==null?void 0:a.globalMaterialDescription)||((c=(i=(t=J[e])==null?void 0:t.additionalData)==null?void 0:i[0])==null?void 0:c.materialDescription)||"";let N=[...C];C.length?N[0]={...N[0],materialDescription:r}:N=[{id:1,language:"EN",materialDescription:r}],s(Q({materialID:e,data:N}))},G=(r,N)=>{const w=C.map(a=>a.id===N.id?{...a,language:r.target.value}:a);s(Q({materialID:e,data:w}))},D=(r,N)=>{const w=C.map(a=>a.id===r?{...a,materialDescription:N}:a);if(s(Q({materialID:e,data:w})),L&&!z.includes(A==null?void 0:A.RequestStatus)){const a=C.find(t=>t.id===r);a&&R({materialID:I,viewName:P.DESCRIPTION,plantData:a.language,fieldName:"Material Description",jsonName:"materialDescription",currentValue:N,requestId:A==null?void 0:A.RequestId,childRequestId:L,isDescriptionData:!0,language:a.language})}},K=()=>{const r=Array.isArray(C)?C:[],w={id:r.length>0?Math.max(...r.map(a=>a.id))+1:1,language:"",materialDescription:"",isNew:!0};s(Q({materialID:e,data:[...r,w]}))},Y=r=>{if(r===1)return;const N=C.filter(w=>w.id!==r);s(Q({materialID:e,data:N}))},$=B.useMemo(()=>[{field:"id",headerName:f("ID"),flex:.2,align:"center",headerAlign:"center"},{field:"language",headerName:f("Language"),flex:.4,type:"singleSelect",align:"center",headerAlign:"center",editable:!0,renderCell:r=>{const N=r.id===1,w=C.filter(a=>a.id!==r.id).map(a=>a.language);return l(Se,{fullWidth:!0,children:l(_e,{sx:{height:"31px",width:"100%"},value:r.value||(N?"EN":""),onChange:a=>!N&&G(a,r),displayEmpty:!0,renderValue:a=>{var t;return a?`${a} - ${((t=o.find(i=>i.code===a))==null?void 0:t.desc)||""}`:l("span",{style:{color:V.primary.grey,fontSize:"12px"},children:"Select Language"})},disabled:N||d,children:o.map(a=>l(De,{value:a.code,disabled:w.includes(a.code),children:`${a.code} - ${a.desc}`},a.code))})})}},{field:"materialDescription",headerName:f("Material Description"),flex:1,align:"center",headerAlign:"center",editable:!1,renderCell:r=>l(We,{disabled:d,params:r,handleCellEdit:D})},{field:"actions",headerName:f("Actions"),flex:.3,align:"center",headerAlign:"center",sortable:!1,renderCell:r=>(r.row.id,l(p,{title:r.row.isNew?f("Delete row"):f("Cannot delete existing row"),children:l("span",{children:l(se,{onClick:()=>Y(r.row.id),disabled:!r.row.isNew||d,size:"small",color:"error",children:l(oe,{fontSize:"small"})})})}))}],[C,o,d]);return ee("div",{children:[l(Te,{sx:{width:"50%",height:"50vh"},className:"confirmOrder-lineItem",children:l(ye,{rows:C??[],columns:$,getRowId:r=>r.id,hideFooter:!0,disableSelectionOnClick:!0,style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"}})}),l(k,{children:!d&&l(ce,{variant:"outlined",sx:{...Oe,mt:2},onClick:K,children:f("Add Row")})})]})},$e=e=>{var $,F,r,N,w,a;const I=de(),d=H(t=>{var i;return((i=t.materialDropDownData)==null?void 0:i.dropDown)||{}}),f=H(t=>t.payload),s=(($=f[e.materialID])==null?void 0:$.unitsOfMeasureData)||[];let n=(r=(F=f[e==null?void 0:e.materialID])==null?void 0:F.payloadData)==null?void 0:r["Basic Data"];(N=f[e.materialID])==null||N.headerData;const R=((w=f[e.materialID])==null?void 0:w.UniqueAltUnit)||[],{updateChangeLog:M}=ue(),{getDtCall:L,dtData:A}=Ve(),J=re(),o=new URLSearchParams(J.search).get("RequestId"),x=H(t=>t.payload.payloadData),{t:g}=ie(),v=B.useCallback((t,i,c)=>{var y;const u={...s==null?void 0:s.find(b=>b.id===t),[i]:c};if(i==="aUnit"){const b=(y=d==null?void 0:d.BaseUom)==null?void 0:y.find(q=>q.code===c);u.measureUnitText=(b==null?void 0:b.desc)||""}const S=s.map(b=>b.id===t?u:b);I(W({materialID:e.materialID,data:S})),o&&!z.includes(x==null?void 0:x.RequestStatus)&&M({materialID:e.selectedMaterialNumber,viewName:P.ADDITIONAL_DATA,plantData:u.aUnit||"",fieldName:i,jsonName:i,currentValue:u[i],requestId:x==null?void 0:x.RequestId,childRequestId:e.requestId,isUnitOfMeasure:!0,uomId:u.UomId||null})},[s,e.materialID,e.selectedMaterialNumber,I,M]),G=(t,i)=>{const c=t!=null&&t.value?i==null?void 0:i.find(u=>u.code===t.value):null;return l(p,{title:(c==null?void 0:c.desc)||g("No value selected"),arrow:!0,placement:"top",children:l("div",{style:{width:"100%"},children:l(Re,{options:t.field==="aUnit"?d==null?void 0:d.BaseUom:t.field==="eanCategory"?d==null?void 0:d.CategoryOfInternationalArticleNumberEAN:t.field==="unitsOfDimension"?d==null?void 0:d.BaseUom:t.field==="volumeUnit"?d==null?void 0:d.Volumeunit:t.field==="weightUnit"?d==null?void 0:d.UnitOfWt:[],value:c,onChange:u=>{v(t.row.id,t.field,u==null?void 0:u.code)},disabled:e==null?void 0:e.disabled,placeholder:g("Select Option"),isOptionDisabled:u=>R.includes(u.code)})})})},D=t=>{var S;const i=t.row.aUnit===((S=n==null?void 0:n.basic)==null?void 0:S.BaseUom),c=t.field==="xValue"||t.field==="yValue",u=(e==null?void 0:e.disabled)||i&&c;return l(xe,{fullWidth:!0,size:"small",value:i&&c?"1":t.value||"",onChange:y=>v(t.row.id,t.field,y.target.value),disabled:u,InputProps:{sx:{"&.Mui-disabled":{"& input":{WebkitTextFillColor:V.text.primary,color:V.text.primary}}}}})},K=[{field:"id",headerName:g("ID"),width:80,hide:!0},{field:"xValue",headerName:"X",width:150,editable:!1,renderCell:D},{field:"aUnit",headerName:g("AUn"),width:150,editable:!1,renderCell:t=>G(t,d==null?void 0:d.BaseUom)},{field:"yValue",headerName:g("Y"),width:150,editable:!1,renderCell:D},{field:"eanUpc",headerName:g("EAN/UPC"),width:150,editable:!1,renderCell:D},{field:"eanCategory",headerName:g("EAN Category"),width:160,editable:!1,renderCell:t=>G(t,d==null?void 0:d.CategoryOfInternationalArticleNumberEAN)},{field:"length",headerName:g("Length"),width:120,editable:!1,renderCell:D},{field:"width",headerName:g("Width"),width:120,editable:!1,renderCell:D},{field:"height",headerName:g("Height"),width:120,editable:!1,renderCell:D},{field:"unitsOfDimension",headerName:g("Unit of Dimension"),width:160,editable:!1,renderCell:t=>G(t,d==null?void 0:d.BaseUom)},{field:"volume",headerName:g("Volume"),width:120,editable:!1,renderCell:D},{field:"volumeUnit",headerName:g("Volume Unit"),width:160,editable:!1,renderCell:t=>G(t,d==null?void 0:d.Volumeunit)},{field:"grossWeight",headerName:g("Gross Weight"),width:140,editable:!1,renderCell:D},{field:"netWeight",headerName:g("Net Weight"),width:140,editable:!1,renderCell:D},{field:"weightUnit",headerName:g("Weight Unit"),width:160,editable:!1,renderCell:t=>G(t,d==null?void 0:d.UnitOfWt)},{field:"actions",headerName:g("Actions"),width:100,sortable:!1,renderCell:t=>l(p,{title:t.row.isNew?"Delete row":"Cannot delete existing row",children:l("span",{children:l(se,{onClick:()=>Y(t.row.id),disabled:!t.row.isNew||e.disabled,size:"small",color:"error",children:l(oe,{fontSize:"small"})})})})}];B.useEffect(()=>{var t,i,c,u,S,y,b,q,m,E,U,_,h,T,X,Z,te,he,me,ge,fe,Ue;if(s.length){let O=JSON.parse(JSON.stringify(s));!O[0].aUnit&&!o&&(O[0].aUnit=(_=n==null?void 0:n.basic)==null?void 0:_.BaseUom,O[0].measureUnitText=((T=(h=n==null?void 0:n.basic)==null?void 0:h.BaseUom)==null?void 0:T.desc)??"",O[0].bUnit=(X=n==null?void 0:n.basic)==null?void 0:X.BaseUom,O[0].measurementUnitText=((te=(Z=n==null?void 0:n.basic)==null?void 0:Z.BaseUom)==null?void 0:te.desc)??"",O[0].volume=((he=n==null?void 0:n.basic)==null?void 0:he.Volume)||"",O[0].volumeUnit=(me=n==null?void 0:n.basic)==null?void 0:me.Volumeunit,O[0].grossWeight=(ge=n==null?void 0:n.basic)==null?void 0:ge.GrossWt,O[0].netWeight=(fe=n==null?void 0:n.basic)==null?void 0:fe.NetWeight,O[0].weightUnit=(Ue=n==null?void 0:n.basic)==null?void 0:Ue.UnitOfWt,I(W({materialID:e==null?void 0:e.materialID,data:O})))}else{const O=[...s,{id:s.length+1,xValue:"1",aUnit:(t=n==null?void 0:n.basic)==null?void 0:t.BaseUom,measureUnitText:((c=(i=n==null?void 0:n.basic)==null?void 0:i.BaseUom)==null?void 0:c.desc)??"",yValue:"1",bUnit:(u=n==null?void 0:n.basic)==null?void 0:u.BaseUom,measurementUnitText:((y=(S=n==null?void 0:n.basic)==null?void 0:S.BaseUom)==null?void 0:y.desc)??"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:((b=n==null?void 0:n.basic)==null?void 0:b.Volume)||"",volumeUnit:(q=n==null?void 0:n.basic)==null?void 0:q.Volumeunit,grossWeight:(m=n==null?void 0:n.basic)==null?void 0:m.GrossWt,netWeight:(E=n==null?void 0:n.basic)==null?void 0:E.NetWeight,weightUnit:(U=n==null?void 0:n.basic)==null?void 0:U.UnitOfWt,noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}];I(W({materialID:e==null?void 0:e.materialID,data:O}))}},[(a=n==null?void 0:n.basic)==null?void 0:a.BaseUom]),B.useEffect(()=>{var t,i,c,u,S,y;if((i=(t=A==null?void 0:A.data)==null?void 0:t.result)!=null&&i[0]){let b=(u=(c=A==null?void 0:A.data)==null?void 0:c.result)==null?void 0:u[0].MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT;if(!b||!Array.isArray(b)){ne((S=ae)==null?void 0:S.NO_DATA_AVAILABLE,"error");return}let q=b.map((m,E)=>{var h,T,X,Z;const U=(h=d==null?void 0:d.BaseUom)==null?void 0:h.find(te=>te.code===m.MDG_MAT_UOM),_=m.MDG_MAT_UOM===((T=n==null?void 0:n.basic)==null?void 0:T.BaseUom);return{id:E+1,uomId:null,xValue:"1",aUnit:m.MDG_MAT_UOM||"",measureUnitText:(U==null?void 0:U.desc)||"",yValue:"1",bUnit:m.MDG_MAT_UOM||"",measurementUnitText:((Z=(X=n==null?void 0:n.basic)==null?void 0:X.BaseUom)==null?void 0:Z.desc)||"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:m.MDG_MAT_LENGTH||"",width:m.MDG_MAT_WIDTH||"",height:m.MDG_MAT_HEIGHT||"",unitsOfDimension:m.MDG_MAT_UNIT_DIMENSIONS||"",volume:m.MDG_MAT_VOLUME||"",volumeUnit:m.MDG_MAT_VOLUME_UNIT||"",grossWeight:m.MDG_MAT_GROSS_NET_WEIGHT||"",netWeight:"",weightUnit:m.MDG_MAT_WEIGHT_UNIT||"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""}});q=q.sort((m,E)=>{var U,_;return m.aUnit===((U=n==null?void 0:n.basic)==null?void 0:U.BaseUom)?-1:E.aUnit===((_=n==null?void 0:n.basic)==null?void 0:_.BaseUom)?1:0}),JSON.stringify(q)!==JSON.stringify((y=f[e.materialID])==null?void 0:y.unitsOfMeasureData)&&I(W({materialID:e==null?void 0:e.materialID,data:q}))}},[A]),B.useEffect(()=>{if(s!=null&&s.length){const t=[...new Set(s.map(i=>i.aUnit).filter(Boolean))];I(ke({materialID:e==null?void 0:e.materialID,data:t}))}},[s]);const Y=t=>{const i=s.filter(c=>c.id!==t);I(W({materialID:e==null?void 0:e.materialID,data:i}))};return l("div",{children:l(k,{container:!0,direction:"row",sx:{backgroundColor:"white",padding:2},children:ee(k,{item:!0,xs:12,mt:4,children:[l(Me,{rows:s,columns:K,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,onCellEditCommit:t=>{v(t.id,t.field,t.value)},width:"100%",title:g("Units of Measure/ EANs/ Dimensions"),showSearch:!1,showRefresh:!1,showExport:!1,showFilter:!0,showColumns:!0}),!(e!=null&&e.disabled)&&l(ce,{variant:"outlined",sx:{mt:2},onClick:()=>{const i={id:s.length>0?Math.max(...s.map(c=>c.id))+1:1,isNew:!0,uomId:null,xValue:"1",aUnit:"",measureUnitText:"",yValue:"1",bUnit:"",measurementUnitText:"",eanUpc:"",eanCategory:"",autoCheckDigit:"",addEans:"",length:"",width:"",height:"",unitsOfDimension:"",volume:"",volumeUnit:"",grossWeight:"",netWeight:"",weightUnit:"",noLowerLvlUnits:"",lowerLvlUnits:"",remVolAfterNesting:"",maxStackFactor:"",maxTopLoadFullPkg:"",UomToploadFullPkg:"",capacityUsage:"",UomCategory:""};I(W({materialID:e==null?void 0:e.materialID,data:[...s,i]}))},children:g("Add Row")})]})})})},Ie=({value:e,onChange:I,disabled:d})=>l(qe,{checked:e||!1,onChange:f=>I(f.target.checked),disabled:d}),je=({value:e,onChange:I,disabled:d,maxLength:f})=>l(xe,{variant:"outlined",fullWidth:!0,size:"small",value:e||"",onChange:s=>{const n=s.target.value;/^\d*$/.test(n)&&I(n)},disabled:d,inputProps:{pattern:"[0-9]*",inputMode:"numeric"},sx:{"& .MuiInputBase-input":{color:V.black.dark,fontSize:"12px"},"& .MuiInputBase-input.Mui-disabled":{WebkitTextFillColor:V.black.dark,color:V.black.dark},"& .MuiOutlinedInput-root":{"&.Mui-disabled":{"& > input":{WebkitTextFillColor:V.black.dark,color:V.black.dark}}}}}),Ce=({options:e=[],value:I,onChange:d,disabled:f,placeholder:s,isOptionDisabled:n})=>{const R=e.find(L=>L.code===I),M=R?`${R.code} - ${R.desc||""}`:"";return l(Re,{options:e,value:M,onChange:d,disabled:f,placeholder:s,isOptionDisabled:n})},ze=e=>{var Y,$,F,r,N,w;const I=de(),d=!1,f=H(a=>a.AllDropDown.dropDown),s=H(a=>a.payload),n=((Y=s[e.materialID])==null?void 0:Y.eanData)||[],R=(($=s[e.materialID])==null?void 0:$.UniqueAltUnit)||[],M=((F=s[e.materialID])==null?void 0:F.unitsOfMeasureData)||[];(r=s==null?void 0:s.payloadData)!=null&&r.Region,(w=(N=s[e==null?void 0:e.materialID])==null?void 0:N.payloadData)==null||w["Basic Data"];const L=re(),{updateChangeLog:A}=ue(),C=new URLSearchParams(L.search).get("RequestId"),o=H(a=>a.payload.payloadData),x=a=>{I(ve({materialID:e.materialID,data:a}))},{t:g}=ie(),v=(a,t,i)=>{var c,u,S,y,b,q;if(t===((c=j)==null?void 0:c.EAN_UPC)){const m=n.find(h=>h.id===a),E=m.altunit,U=n.some(h=>h.id!==a&&h.altunit===E&&h.MainEan===!0),_=n.map(h=>h.id===a?{...h,[t]:i,au:!1,MainEan:U?!1:!!i}:h);if(x(_),M.length>0&&i&&!U){const h=M.map(T=>T.aUnit===E?{...T,eanUpc:i,eanCategory:m.eanCategory}:T);JSON.stringify(h)!==JSON.stringify(M)&&I(W({materialID:e.materialID,data:h}))}C&&!z.includes(o==null?void 0:o.RequestStatus)&&A({materialID:e.materialID,viewName:P.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanUpc",jsonName:(u=j)==null?void 0:u.EAN_UPC,currentValue:i,requestId:o==null?void 0:o.RequestId,isAdditionalEAN:!0,eanId:m.EanId||null,childRequestId:C})}else if(t==="MainEan"&&i===!0){const m=n.find(h=>h.id===a),E=m.altunit,_=n.map(h=>h.altunit===E?{...h,MainEan:!1}:h).map(h=>h.id===a?{...h,MainEan:!0}:h);if(x(_),M.length>0){const h=M.map(T=>T.aUnit===E?{...T,eanUpc:m.eanUpc,eanCategory:m.eanCategory}:T);JSON.stringify(h)!==JSON.stringify(M)&&I(W({materialID:e.materialID,data:h}))}C&&!z.includes(o==null?void 0:o.RequestStatus)&&A({materialID:e.materialID,viewName:P.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"MainEan",jsonName:(S=j)==null?void 0:S.MAIN_EAN,currentValue:i,requestId:o==null?void 0:o.RequestId,childRequestId:C,isAdditionalEAN:!0,eanId:m.EanId||null})}else if(t==="eanCategory"){const m=n.find(U=>U.id===a),E=n.map(U=>U.id===a?{...U,[t]:i,MainEan:!1}:U);x(E),C&&!z.includes(o==null?void 0:o.RequestStatus)&&A({materialID:e.materialID,viewName:P.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"EanCat",jsonName:(y=j)==null?void 0:y.EAN_CATEGORY,currentValue:i,requestId:o==null?void 0:o.RequestId,childRequestId:C,isAdditionalEAN:!0,eanId:m.EanId||null})}else if(t==="au"&&i===!0){const m=n.find(E=>E.id===a);if(m.eanUpc){const E=_=>{const h=n.map(T=>T.id===a?{...T,[t]:i,eanUpc:T.eanUpc+_.body,MainEan:!1}:T);x(h),C&&!z.includes(o==null?void 0:o.RequestStatus)&&A({materialID:e.materialID,viewName:P.ADDITIONAL_EAN_DATA,plantData:"",fieldName:"other",jsonName:"au",currentValue:i,requestId:o==null?void 0:o.RequestId,childRequestId:C,isAdditionalEAN:!0,eanId:m.EanId||null})},U=_=>{var h;ne((h=ae)==null?void 0:h.ERROR_FETCHING_DATA,"error")};we(`/${Ee}${(q=(b=be)==null?void 0:b.DATA)==null?void 0:q.GET_CHECK_DIGIT}?number=${m.eanUpc}`,"get",E,U,{eanUpc:m.eanUpc})}}else{const m=n.map(U=>U.id===a?{...U,[t]:i}:U),E=n.find(U=>U.id===a);x(m),C&&!z.includes(o==null?void 0:o.RequestStatus)&&A({materialID:e.materialID,viewName:P.ADDITIONAL_EAN_DATA,plantData:"",fieldName:t,jsonName:t,currentValue:i,requestId:o==null?void 0:o.RequestId,childRequestId:C,isAdditionalEAN:!0,eanId:E.EanId||null})}},G=a=>{const t=n.filter(i=>i.id!==a);x(t)},D=()=>{const a={id:Ae(),EanId:null,altunit:"",MainEan:!1,eanUpc:"",eanCategory:"",au:!1,isNew:!0};x([...n,a])};B.useEffect(()=>{if(R.length){const a=n.filter(u=>R.includes(u.altunit)||u.altunit===""),t=a.map(u=>u.altunit),c=R.filter(u=>!t.includes(u)).map(u=>({id:Ae(),EanId:null,altunit:u,MainEan:!1,eanUpc:"",eanCategory:"",au:!1}));x([...a,...c])}},[R]);const K=[{field:"altunit",headerName:g("Alt. Unit"),width:200,renderCell:a=>l(Ce,{options:(f==null?void 0:f.BaseUom)||[],value:a.row.altunit,onChange:t=>v(a.row.id,"altunit",t==null?void 0:t.code),disabled:e==null?void 0:e.disabled,placeholder:"SELECT Alt. Unit",isOptionDisabled:t=>!R.includes(t.code)})},{field:"eanUpc",headerName:g("EAN/UPC"),width:180,renderCell:a=>{var t,i;return l("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:l(p,{title:a.row[(t=j)==null?void 0:t.EAN_UPC]||"",arrow:!0,children:l("div",{style:{width:"100%"},children:l(je,{value:a.row[(i=j)==null?void 0:i.EAN_UPC],onChange:c=>{var u;return v(a.row.id,(u=j)==null?void 0:u.EAN_UPC,c)},disabled:e==null?void 0:e.disabled,maxLength:13,onClick:c=>c.stopPropagation(),onDoubleClick:c=>c.stopPropagation()})})})})}},{field:"eanCategory",headerName:g("EAN Cat."),width:200,renderCell:a=>l(Ce,{options:(f==null?void 0:f.EanCat)||[],value:a.row.eanCategory,onChange:t=>v(a.row.id,"eanCategory",t==null?void 0:t.code),disabled:e==null?void 0:e.disabled,placeholder:"SELECT EAN Cat.",isOptionDisabled:t=>n.filter(c=>c.altunit===a.row.altunit&&c.id!==a.row.id).some(c=>c.eanCategory===t.code)})},{field:"au",headerName:g("Au"),width:150,renderCell:a=>l(Ie,{value:a.row.au,onChange:t=>v(a.row.id,"au",t),disabled:(e==null?void 0:e.disabled)||a.row.au})},{field:"MainEan",headerName:g("Main EAN"),width:160,renderCell:a=>l(Ie,{value:a.row.MainEan,onChange:t=>v(a.row.id,"MainEan",t),disabled:e==null?void 0:e.disabled})},{field:"actions",headerName:g("Actions"),width:180,renderCell:a=>l(p,{title:a.row.isNew?"Delete row":"Cannot delete existing row",children:l("span",{children:l(se,{onClick:()=>G(a.row.id),disabled:(e==null?void 0:e.disabled)||!a.row.isNew,color:"error",size:"small",children:l(oe,{fontSize:"small"})})})})}];return l("div",{children:l(k,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",mt:.25,...Le},children:ee(k,{container:!0,display:"block",sx:{marginLeft:"-10px"},children:[l(k,{item:!0,xs:4}),ee(k,{item:!0,xs:10,mt:4,children:[l(Me,{title:"Additional EANs/Units of Measure",isLoading:d,rows:n,columns:K,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:["eanUpc","action"],status_onRowDoubleClick:!1,width:"100%"}),!(e!=null&&e.disabled)&&l(ce,{variant:"outlined",sx:{mt:2},onClick:D,disabled:!(M!=null&&M.length),children:g("Add Row")})]})]})})})},Ze=e=>{const{t:I}=ie(),[d,f]=B.useState(0),s=window.location.href.includes("DisplayMaterialSAPView"),n=[I("Description"),I("Units of Measure"),...s?[]:[I("Additional EANs")]],R=[[l(le,{children:l(He,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],[l(le,{children:l($e,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})],...s?[]:[[l(le,{children:l(ze,{materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,disabled:e.disableCheck})})]]],M=(L,A)=>{f(A)};return l("div",{children:l(k,{container:!0,style:{...Ne,backgroundColor:"#FAFCFF"},children:l(k,{sx:{width:"inherit"},children:l(k,{container:!0,style:{padding:"0 1rem 0 1rem"},children:l(k,{container:!0,sx:Ne,children:l(k,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:ee(k,{container:!0,children:[l(Ge,{value:d,onChange:M,variant:"scrollable",sx:{background:"#FFF",borderBottom:"1px solid #BDBDBD",width:"100%"},"aria-label":"mui tabs example",children:n.map((L,A)=>l(Be,{sx:{fontSize:"12px",fontWeight:"700"},label:L},A))}),R[d].map((L,A)=>l(Te,{sx:{mb:2,width:"100%"},children:l(Fe,{variant:"body2",children:L})},A))]})})})})})})})};export{Ze as default};
