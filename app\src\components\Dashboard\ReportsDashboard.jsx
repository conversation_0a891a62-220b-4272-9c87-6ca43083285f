import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import { Accordion, AccordionDetails, AccordionSummary, FormControl, Grid, Typography, Button, Stack, Card, CardContent, Box, styled, CircularProgress } from "@mui/material";
import React, { useState, useEffect } from "react";
import TextSnippetIcon from "@mui/icons-material/TextSnippet";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { destination_Dashboard } from "../../destinationVariables";
import { iconButton_SpacingSmall, button_Outlined, font_Small } from "@components/Common/commonStyles";
import DateRange from "../common/DateRangePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { useDispatch, useSelector } from "react-redux";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice";
import moment from "moment";
import { doAjax } from "@components/Common/fetchService";
import LargeDropdown from "@components/Common/ui/dropdown/LargeDropdown";
import { reportStyle, dialogTitleStyle, downloadButtonStyle } from "@constant/style";
import { barColors1Store } from "./LegendStore";
import { LOADING_MESSAGE, MODULE_OPTIONS } from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { colors } from "@constant/colors";
import FilterListIcon from '@mui/icons-material/FilterList';
import useLang  from "@hooks/useLang";
import {arrayToCommaString} from "@helper/helper"

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${colors.primary.border}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));
const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor:  theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));

const ReportsDashboard = ({ reportConfig = [], kpiReportPrefs = [] ,loading }) => {
  const reportSearchForm = useSelector((state) => state.commonFilter["Reports"]);
  const rbSearchForm = useSelector((state) => state.commonFilter["RequestBench"]);
  const dropDownData = useSelector((state) => state.AllDropDown.dropDown);
  const [selectedDivision, setSelectedDivision] = useState([]);
  const [requestStatus, setRequestStatus] = useState([]);
  const [dateTime, setDateTime] = useState([...reportSearchForm.reportDate]);
  const [requestType, setRequestType] = useState([]);
  const [requestTypeOptions, setRequestTypeOptions] = useState([]);
  const [requestStatusOptions, setRequestStatusOptions] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const dispatch = useDispatch();
  const userData = useSelector((state) => state.userManagement.userData);
  const userId = userData?.user_id || "";
  const { t } = useLang();
  const dashboardSearchForm = useSelector((state) => state.commonFilter["Dashboard"]);

  useEffect(() => {
    if (reportSearchForm?.reportDate) {
      const presentDate = new Date(reportSearchForm?.reportDate[0]);
      const backDate = new Date(reportSearchForm?.reportDate[1]);
      setDateTime([presentDate, backDate]);
    }
  }, [reportSearchForm?.reportDate]);
  useEffect(() => {
    getRequestType();
    getRequestStatus();
  }, []);

  const regionOptions = ["US", "EUR"];
  const [regionName, setRegionName] = useState([]);

  const handleSelectAllRegion = () => {
    setRegionName(regionName.length === regionOptions.length ? [] : regionOptions);
  };

  const handleSelectAllReqType = () => {
    setRequestType(requestType.length === requestTypeOptions.length ? [] : requestTypeOptions);
  };

  const handleSelectAllReqStatus = () => {
    setRequestStatus(requestStatus.length === requestStatusOptions.length ? [] : requestStatusOptions);
  };

  const getRequestStatus = () => {
    doAjax(
      `/${destination_Dashboard}/GraphConfig/getReqStatuses`,
      "get",
      (data) => setRequestStatusOptions(data?.body || []),
      () => {}
    );
  };

  const getRequestType = () => {
    doAjax(
      `/${destination_Dashboard}/GraphConfig/getReqTypes`,
      "get",
      (data) => setRequestTypeOptions(data?.body || []),
      () => {}
    );
  };

  const downloadAPPkpiData = (endpoint, filename, downloadPayload) => {
    setLoaderMessage(LOADING_MESSAGE.REPORT_LOADING);
    setBlurLoading(true);

    doAjax(
      `/${destination_Dashboard}/excel${endpoint}`,
      "postandgetblob",
      (response) => {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        setBlurLoading(false);
        setLoaderMessage("");
      },
      () => {
        setBlurLoading(false);
        setLoaderMessage("");
      },
      downloadPayload
    );
  };

  const handleDate = (e) => {
    dispatch(
      commonFilterUpdate({
        module: "Reports",
        filterData: { ...reportSearchForm, reportDate: e },
      })
    );
  };

  const handleClear = () => {
    setRegionName([]);
    setRequestType([]);
    setSelectedDivision([]);
    setRequestStatus([]);
    const presentDate = new Date();
    const backDate = new Date();
    backDate.setDate(backDate.getDate() - 3650);
    setDateTime([backDate, presentDate]);
    dispatch(commonFilterClear({ module: "Reports" }));
  };

  return (
    <>
      <Grid item md={12}>
        <StyledAccordion
          defaultExpanded={false}
           sx={{
            marginTop: "5px !important",
            marginLeft: "25px !important",
            marginRight: "20px !important",
          }}
        >
          <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: colors.primary.main }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                >
                  <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: colors.primary.main }} />
                  <Typography
                    sx={{
                     fontSize: '0.875rem',
                      fontWeight: 600,
                      color: colors.primary.dark,
                    }}
                  >
                    {t("Filter Reports")}
                  </Typography>
                </StyledAccordionSummary>
          <AccordionDetails>
            <Grid container rowSpacing={1} spacing={2}>
              <Grid item md={2}>
                <Typography sx={font_Small}>{t("Date Range")}</Typography>
                <FormControl fullWidth sx={{ padding: 0, height: "37px" }}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateRange handleDate={handleDate} cleanDate={false} date={dateTime} />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
              <Grid item md={2}>
                <Typography sx={font_Small}>{t("Region")}</Typography>
                <AutoCompleteSimpleDropDown
                  options={[...regionOptions?.filter((name) => name !== "Select All")?.sort((a, b) => (typeof a === "string" && typeof b === "string" ? a.localeCompare(b) : 0))]}
                  value={regionName}
                  onChange={(value) => {
                    if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                      handleSelectAllRegion();
                    } else {
                      setRegionName(value);
                    }
                  }}
                  placeholder="Select Region"
                />
              </Grid>
              {/* <Grid item md={2}>
                <Typography sx={font_Small}>Request Type</Typography>
                <AutoCompleteSimpleDropDown
                  options={[...requestTypeOptions?.filter((name) => name !== "Select All")?.sort((a, b) => (typeof a === "string" && typeof b === "string" ? a.localeCompare(b) : 0))]}
                  value={requestType}
                  onChange={(value) => {
                    if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                      handleSelectAllReqType();
                    } else {
                      setRequestType(value);
                    }
                  }}
                  placeholder="Select Request Type"
                />
              </Grid> */}
              <Grid item md={2}>
                <Typography sx={font_Small}>{t("Division")}</Typography>
                <LargeDropdown
                  matGroup={dropDownData?.Division ?? []}
                  selectedMaterialGroup={selectedDivision}
                  setSelectedMaterialGroup={(value) => {
                    if (!value || value.length === 0) {
                      setSelectedDivision([]);
                      return;
                    }
                    setSelectedDivision(value);
                  }}
                  placeholder="Select Division"
                />
              </Grid>
              {/* <Grid item md={2}>
                <Typography sx={font_Small}>Request Status</Typography>
                <AutoCompleteSimpleDropDown
                  options={[...requestStatusOptions?.filter((name) => name !== "Select All")?.sort((a, b) => (typeof a === "string" && typeof b === "string" ? a.localeCompare(b) : 0))]}
                  value={requestStatus}
                  onChange={(value) => {
                    if (value.length > 0 && value[value.length - 1]?.label === "Select All") {
                      handleSelectAllReqStatus();
                    } else {
                      setRequestStatus(value);
                    }
                  }}
                  placeholder="Select Request Status"
                />
              </Grid> */}
            </Grid>
            <Grid container style={{ display: "flex", justifyContent: "flex-end" }}>
              <Grid item style={{ display: "flex", justifyContent: "space-around" }}>
                <Button variant="outlined" sx={{ ...button_Outlined }} onClick={handleClear}>
                  {t("Clear")}
                </Button>
              </Grid>
            </Grid>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>

      <Grid item md={12}>
        <Stack justifyContent="space-between" direction="row">
          <Grid container spacing={2}>
            <Grid item md={12}>
              <Card sx={{ borderRadius: "10px", boxShadow: 4, height: { xs: "calc(70vh - 100px)", md: "calc(75vh - 100px)", lg: "calc(80vh - 130px)" }, marginLeft: "25px", marginTop: "20px", marginRight: "20px" }}>
                <CardContent>
                  <Typography variant="h6" sx={{ fontWeight: "bold", marginBottom: "20px" }}>
                    {t("Application Report List")}
                  </Typography>
                  <Stack spacing={2} sx={{ height: { xs: "calc(70vh - 160px)", md: "calc(75vh - 160px)", lg: "calc(80vh - 190px)" }, overflowY: "auto", overflowX: "hidden", "&::-webkit-scrollbar": { width: "4px" }, "&::-webkit-scrollbar-thumb": { backgroundColor: "rgba(0,0,0,0.2)", borderRadius: "4px" } }}>
                    {loading && (
                      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
                        <CircularProgress />
                      </Box>
                    )}
                    <Grid container spacing={1}>
                      {reportConfig
                        .filter((report) => {
                          const matchedPref = kpiReportPrefs.find((p) => p.KpiId === report.MDG_KPI_ID);
                          return matchedPref?.KpiVisibility === true && matchedPref?.IsActive === true;
                        })
                        .sort((a, b) => +a.MDG_KPI_GRAPH_SEQUENCE - +b.MDG_KPI_GRAPH_SEQUENCE)
                        .map((report, index) => {
                          const payload = {
                            FromDate: moment(reportSearchForm?.reportDate?.[0] ?? rbSearchForm?.createdOn?.[0]).format("YYYY-MM-DD"),
                            ToDate: moment(reportSearchForm?.reportDate?.[1] ?? rbSearchForm?.createdOn?.[1]).format("YYYY-MM-DD"),
                            Requestor: "",
                            KpiId: report.MDG_KPI_ID,
                            Module: arrayToCommaString(dashboardSearchForm?.dashBoardModuleName) || MODULE_OPTIONS[0],
                            UserId: userId,
                            Priority: "",
                            Region: regionName.join(","),
                            ReqType: requestType.join(","),
                            ReqStatus: requestStatus.join(","),
                            GraphType: report.MDG_KPI_GRAPH_TYPE || "",
                            KpiName: report.MDG_KPI_NAME || "",
                            ColPallet: report.MDG_KPI_COLOR_PALLET || "",
                            GraphColumn: report.MDG_KPI_GRAPH_COLUMN?.toLowerCase() || "",
                            GraphSequence: report.MDG_KPI_GRAPH_SEQUENCE || "",
                          };
                          return (
                            <Grid item xs={6} key={report.MDG_KPI_ID} sx={{ paddingRight: "8px" }}>
                              <Box sx={{ ...reportStyle, width: "100%", boxSizing: "border-box" }}>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    ...dialogTitleStyle,
                                    whiteSpace: "nowrap",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                  }}
                                >
                                  <TextSnippetIcon
                                    sx={{
                                      color: barColors1Store[index % barColors1Store.length],
                                      marginRight: "4px",
                                    }}
                                  />
                                  {t(report.MDG_KPI_NAME)}
                                </Typography>
                                <Button variant="outlined" sx={downloadButtonStyle} onClick={() => downloadAPPkpiData(report.MDG_KPI_ENDPOINT, `${report.MDG_KPI_NAME}.xlsx`, payload)}>
                                  {t("Download")}
                                </Button>
                              </Box>
                            </Grid>
                          );
                        })}
                    </Grid>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Stack>
      </Grid>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </>
  );
};

export default ReportsDashboard;
