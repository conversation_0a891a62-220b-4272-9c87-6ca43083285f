import { useEffect, useState,  } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate} from "react-router-dom";
import ReusableDialogForDropdown from '@components/Common/ReusableDialogForDropdown';
import { Templates } from '@constant/changeTemplates';
import ContentForChange from './ContentForChange';
import BottomNav from "../BottomNav";
import useMaterialButtonDT from '@hooks/useMaterialButtonDT';
import { useFetchWorkflowLevels } from "@hooks/useFetchWorkflowLevels";
import { APP_END_POINTS } from '@constant/appEndPoints';
import { ToastContainer } from 'react-toastify';
import { filterButtonsBasedOnTab } from '@helper/helper';
import { BUTTONS_ACTION_TYPE } from '@constant/buttonPriority';
import {MODULE_MAP} from '@constant/enum'

const RequestDetailsForChange = (props) => {
  const [dialogChangeOpen, setDialogChangeOpen] = useState(true);
  const dropDownDataFromRedux = useSelector((state) => state.materialDropDownData.dropDown)
  const singlePayloadData = useSelector((state) => state.payload);
  const initialPayload = useSelector((state) => state.payload.payloadData?.data || state.payload.payloadData);
  const initialPayloadRequestType = initialPayload?.RequestType;
  const [showTable, setShowTable] = useState(false);
  const [showTableDisplay, setShowTableDisplay] = useState(false);
  const taskData = useSelector((state) => state.userManagement.taskData)
  const filteredButtons = useSelector((state) => state.payload.filteredButtons)
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get('RequestType');
  const RequestId = queryParams.get('RequestId');
  const changeFieldRows = useSelector((state) => state.payload.changeFieldRows)
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const navigate = useNavigate();

  const { getButtonsDisplay, showWfLevels } = useMaterialButtonDT();
  const { wfLevels } = useFetchWorkflowLevels({initialPayloadRequestType, initialPayload, dynamicData, taskData, singlePayloadData, version:'v4',dtName:'MDG_MAT_DYNAMIC_WF_DT',module:MODULE_MAP.MAT});
  const requestDetailsButton = filterButtonsBasedOnTab(filteredButtons,[BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_APPROVAL,BUTTONS_ACTION_TYPE.HANDLE_SAP_SYNDICATION,BUTTONS_ACTION_TYPE.HANDLE_SUBMIT_FOR_REVIEW])
  useEffect(() => {
    if((taskData?.ATTRIBUTE_1 || RequestType)) {
      // setShowTableDisplay(true);
      getButtonsDisplay();
    }
  }, [taskData]);

  useEffect(() => {
    if((changeFieldRows?.length !== 0 && changeFieldRows?.length !== undefined) || !isChangeFieldRowsEmpty()) {
      setShowTableDisplay(true);
      setShowTable(true);
    }
  }, [changeFieldRows])

  const isChangeFieldRowsEmpty = () => {
    return Object?.values(changeFieldRows)?.every(arr => Array?.isArray(arr) && arr?.length === 0);
  };

  useEffect(() => {
    if(props.downloadClicked) {
      setDialogChangeOpen(true);
    }
  },[props.downloadClicked])

  return (
    <div>
      {
        ((initialPayload?.TemplateName && (changeFieldRows && changeFieldRows?.length ===0 || isChangeFieldRowsEmpty())) || props.downloadClicked) && (
          <ReusableDialogForDropdown
            open={dialogChangeOpen}
            onClose={() => {
              setDialogChangeOpen(false)
              props?.setDownloadClicked(false)
              if(!RequestId) {
                navigate(APP_END_POINTS?.REQUEST_BENCH);
              }
            }}
            parameters={Templates[initialPayload?.TemplateName]}
            templateName={initialPayload?.TemplateName}
            setShowTable={setShowTable}
            allDropDownData={dropDownDataFromRedux}
            setDownloadClicked={props?.setDownloadClicked}
          />
        )
      }
      {
        ((showTable || showTableDisplay) && !props?.downloadClicked) && (
          <>
            <ContentForChange setCompleted={props?.setCompleted}
            RequestId={RequestId}
            />
            <BottomNav filteredButtons={requestDetailsButton} setCompleted={props?.setCompleted} showWfLevels={showWfLevels} workFlowLevels={wfLevels}/>
          </>
          
        )
      }
      <ToastContainer />
    </div>
  );
};

export default RequestDetailsForChange;
