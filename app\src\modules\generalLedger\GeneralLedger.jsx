import React, { use<PERSON><PERSON>back, useMemo } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CloseIcon from "@mui/icons-material/Close";
import EditOutlinedIcon from "@mui/icons-material/EditOutlined";
import HistoryIcon from '@mui/icons-material/History';
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import ReusableIcon from "../../components/Common/ReusableIcon";
import {
  Button,
  Checkbox,
  Grid,
  Paper,
  IconButton,
  Typography,
  TextField,
  Box,
  Tooltip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Popper,
  BottomNavigation,
  ListItemText,
  InputLabel,
  tooltipClasses,
  Card,
  CardContent,
  OutlinedInput,
  Autocomplete,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  ButtonGroup,
  ClickAwayListener,
  MenuList,
  Divider,
  FormControlLabel,
  ToggleButtonGroup,
  ToggleButton,
  CircularProgress,
  Backdrop,
  FormGroup,
  RadioGroup,
  Radio,
  useTheme,
} from "@mui/material";

import moment from "moment/moment";
import { Stack } from "@mui/system";
import Select from "@mui/material/Select";
import { FormControl, MenuItem } from "@mui/material";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import ReusableDialog from "../../components/Common/ReusableDialog";
import { useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import TrackChangesIcon from "@mui/icons-material/TrackChanges";
import styled from "@emotion/styled";
import html2canvas from "html2canvas";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import {
  commonFilterClear,
  commonFilterUpdate,
} from "../../app/commonFilterSlice";
import { v4 as uuidv4 } from "uuid";
import {
  button_Marginleft,
  button_Primary,
  container_filter,
  font_Small,
  iconButton_SpacingSmall,
  outerContainer_Information,
  outermostContainer,
  outermostContainer_Information,
} from "../../components/Common/commonStyles";
import CheckCircleOutlinedIcon from "@mui/icons-material/CheckCircleOutlined";
import WarningIcon from "@mui/icons-material/Warning";
import useGenericDtCall from "@hooks/useGenericDtCall";
import {API_CODE, DECISION_TABLE_NAME, ERROR_MESSAGES, MODULE_MAP, PAGESIZE, SEARCH_FIELD_TYPES, VISIBILITY_TYPE} from "@constant/enum";

import {
  destination_CostCenter,
  destination_GeneralLedger_Mass,
  destination_IDM,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import ClearIcon from "@mui/icons-material/Clear";
import ReusableTable from "../../components/Common/ReusableTable";

import { setDropDown } from "../../app/dropDownDataSlice";
import DateRange from "../../components/Common/DateRangePicker";
import AttachmentUploadDialog from "../../components/Common/AttachmentUploadDialog";
import {
  clearGeneralLedger,
  
  resetPayloadDataGL,
  setEditMultipleGlExtend,
  setHandleMassMode,
  setMultipleGLData,
  setMultipleGlDataForExtend,
  setSinglegeneralLedgerPayload,
  setgeneralLedgerControlData,
  setgeneralLedgerCreateBankIntrest,
  setgeneralLedgerInformation,
  setgeneralLedgerKeywordTranslation,
  setgeneralLedgerTypeDescription,
} from "../../app/generalLedgerTabSlice";
import { clearTaskData, setTaskData } from "../../app/userManagementSlice";
import {  saveExcel } from "../../functions";
import {
  clearPayloadForEdit,
  
} from "../../app/editPayloadSlice";

import ReusablePreset from "../../components/Common/ReusablePresetFilter";
import { DataGrid } from "@mui/x-data-grid";
import {
  clearArtifactId,
  
} from "../../app/initialDataSlice";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import { colors } from "@constant/colors";
import FilterListIcon from "@mui/icons-material/FilterList";
import SearchIcon from "@mui/icons-material/Search";
import useLogger from "@hooks/useLogger";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: "8px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.05)",
  "&:not(:last-child)": {
    borderBottom: 0,
  },
  "&:before": {
    display: "none",
  },
}));

const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor: theme.palette.primary.light,
  borderRadius: "8px 8px 0 0",
  transition: "all 0.2s ease-in-out",
  "&:hover": {
    backgroundColor: `${theme.palette.primary.light}20`,
  },
}));

const StyledTextField = styled(TextField)({
  "& .MuiOutlinedInput-root": {
    borderRadius: "4px",
    "&:hover fieldset": {
      borderColor: colors.primary.main,
    },
  },
});

const FilterContainer = styled(Grid)({
  padding: "0.75rem",
  gap: "0.5rem",
});

const ButtonContainer = styled(Grid)({
  display: "flex",
  justifyContent: "flex-end",
  paddingRight: "0.75rem",
  paddingBottom: "0.75rem",
  paddingTop: "0rem",
  gap: "0.5rem",
});

const ActionButton = styled(Button)({
  borderRadius: "4px",
  padding: "4px 12px",
  textTransform: "none",
  fontSize: "0.875rem",
});

const LabelTypography = styled(Typography)(({ theme }) => ({
  fontSize: '0.75rem',
  color: theme.palette.primary.dark,
  marginBottom: '0.25rem',
  fontWeight: 500,
}));


const HtmlTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#f5f5f9",
    color: "rgba(0, 0, 0, 0.87)",
    maxWidth: 250,
    border: "1px solid #dadde9",
  },
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
const exportAsPicture = () => {
  const html = document.getElementsByTagName("HTML")[0];
  const body = document.getElementsByTagName("BODY")[0];
  let htmlWidth = html.clientWidth;
  let bodyWidth = body.clientWidth;

  const data = document.getElementById("e-invoice-export"); 
  const newWidth = data.scrollWidth - data.clientWidth;

  if (newWidth > data.clientWidth) {
    htmlWidth += newWidth;
    bodyWidth += newWidth;
  }

  html.style.width = htmlWidth + "px";
  body.style.width = bodyWidth + "px";

  html2canvas(data)
    .then((canvas) => {
      return canvas.toDataURL("image/png", 1.0);
    })
    .then((image) => {
      saveAs(image, "E-InvoiceReport.png"); 
      html.style.width = null;
      body.style.width = null;
    });
};

const saveAs = (blob, fileName) => {
  const elem = window.document.createElement("a");
  elem.href = blob;
  elem.download = fileName;
  (document.body || document.documentElement).appendChild(elem);
  if (typeof elem.click === "function") {
    elem.click();
  } else {
    elem.target = "_blank";
    elem.dispatchEvent(
      new MouseEvent("click", {
        view: window,
        bubbles: true,
        cancelable: true,
      })
    );
  }
  URL.revokeObjectURL(elem.href);
  elem.remove();
};
const GeneralLedger = () => {
  const{t} = useLang();
  const [snackbar, setSnackbar] = useState(false);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [ccInputValue, setCcInputValue] = useState("");

  const [openSearchDialog, setOpenSearchDialog] = useState(false);
  const [searchDialogTitle, setSearchDialogTitle] = useState("");
  const [searchDialogMessage, setSearchDialogMessage] = useState();

  const [shortTextInputValue, setShortTextInputValue] = useState("");
  const [longTextInputValue, setLongTextInputValue] = useState("");
  const [selectedValues, setSelectedValues] = useState({});
  const [selectedLongText, setselectedLongText] = useState([]);
  const [selectedShortText, setselectedShortText] = useState([]);
  const [selectedTaxCategory, setselectedTaxCategory] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [selectedCreatedBy, setselectedCreatedBy] = useState([]);
  const [messageDialogSeverity, setMessageDialogSeverity] = useState("");
  const [selectedComanyCode, setselectedComanyCode] = useState([]);
  const [selectedGeneralLedger, setselectedGeneralLedger] = useState([]);
  const [dynamicColumns, setDynamicColumns] = useState([]);
  const { getDtCall: getMasterDataColumn, dtData: masterDataDtResponse } = useGenericDtCall();
  const { getDtCall:getSearchParams, dtData:dtSearchParamsResponse } = useGenericDtCall();
  const [searchParameters, setSearchParameters] = useState([]);

  const [selectedPresetValues, setSelectedPresetValues] = useState({});
  const [selectedPresetLongText, setselectedPresetLongText] = useState([]);
  const [selectedPresetShortText, setselectedPresetShortText] = useState([]);
  const [selectedPresetTaxCategory, setselectedPresetTaxCategory] = useState([]);
  const [selectedPresetCreatedBy, setselectedPresetCreatedBy] = useState([]);
  const [selectedPresetComanyCode, setselectedPresetComanyCode] = useState([]);
  const [selectedPresetGeneralLedger, setselectedPresetGeneralLedger] = useState([]);
  const [seletedDateRange, setseletedDateRange] = useState([]);
  const [items,setItem] = useState([]);
  const theme = useTheme();

  const memoizedGLValue = useMemo(() => {
    if (selectedGeneralLedger.length > 0) return selectedGeneralLedger;
    else if (selectedPresetGeneralLedger.length > 0) return selectedPresetGeneralLedger;
    else return [];
  }, [selectedGeneralLedger, selectedPresetGeneralLedger]);

  const memoizedLDValue = useMemo(() => {
    if (selectedLongText.length > 0) return selectedLongText;
    else if (selectedPresetLongText.length > 0) return selectedPresetLongText;
    else return [];
  }, [selectedLongText, selectedPresetLongText]);

  const memoizedSDValue = useMemo(() => {
    if (selectedShortText.length > 0) return selectedShortText;
    else if (selectedPresetShortText.length > 0) return selectedPresetShortText;
    else return [];
  }, [selectedShortText, selectedPresetShortText]);


  const [newChartOfAccount, setNewChartOfAccount] = useState({
    code: "ETCN",
    desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
  });
  const [etCompanyCodesSelected, setETCompanyCodesSelected] = useState(false);
  const [sunocoCompanyCodesSelected, setSunocoCompanyCodesSelected] =
    useState(false);
  const [newCompanyCode, setNewCompanyCode] = useState([]);
  const [newGLAccount, setNewGLAccount] = useState("");
  const appSettings = useSelector((state) => state.appSettings["Format"]);
  const dropdownData = useSelector((state) => state.AllDropDown.dropDown);
 
  const [newGeneralLedgerValid, setNewGeneralLedgerValid] = useState(false);
  const [
    extendETCompanyCodesSelectedWithkey,
    setExtendETCompanyCodesSelectedWithkey,
  ] = useState({});
  const [
    extendSunocoCompanyCodesSelectedWithkey,
    setExtendSunocoCompanyCodesSelectedWithkey,
  ] = useState({});
  const [
    extendETCompaniesDisabledWithKey,
    setExtendETCompaniesDisabledWithKey,
  ] = useState({});
  const [
    extendSunocoCompaniesDisabledWithKey,
    setExtendSunocoCompaniesDisabledWithKey,
  ] = useState({});

  const [isValidationError, setIsValidationError] = useState(false);
  const [checkValidationGeneralLedger, setCheckValidationGeneralLedger] =
    useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [newAccountType, setNewAccountType] = useState("");
  const [newAccountGroup, setNewAccountGroup] = useState([]);
  const [newGeneralLedgerValidWithCopy, setNewGeneralLedgerValidWithCopy] =
    useState(false);
  const [numberRangeError, setNumberRangeError] = useState(false);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [skip, setSkip] = useState(0);
  const [newCompanyCodeToCopyFrom, setNewCompanyCodeToCopyFrom] = useState("");
  const [selectedMassChangeRowData, setSelectedMassChangeRowData] = useState(
    []
  );
  const [openDialog, setOpenDialog] = useState(false);
  const [openDialogExtend, setOpenDialogExtend] = useState(false);
  const [openDialogChangeExtend, setOpenDialogChangeExtend] = useState(false);
  const [openDialogChangeExtendFerc, setOpenDialogChangeExtendFerc] = useState(false);
  const [fieldSelectionFromIdm, setFieldselectionFromIdm] = useState([]);
  const [openDialogHandsonDialog, setOpenDialogHandsonDialog] = useState(false);
  const [noMatchesRow, setNoMatchesRow] = useState([]);
  const [directmatchmassGLWithCopy, setDirectmatchmassGLWithCopy] = useState(
    []
  );
  const [selectedIdForGlMassExtend, setSelectedIdForGlMassExtend] =
    useState("");
  const [openDialogforPopup, setOpenDialogforPopup] = useState(false);

  const [dialogTitle, setDialogTitle] = useState("");
  const [dialogOkText, setDialogOkText] = useState("");
  const [statusOfSelectAllFirstData, setStatusOfSelectAllFirstData] = useState(false)


  const handleMassModePC = useSelector(
    (state) => state.generalLedger.handleMassMode
  );

  let iwaAccessData = useSelector(
    (state) => state.userManagement.entitiesAndActivities?.["Display Material"]
  );
  let userData = useSelector((state) => state.userManagement.userData);
  const [selectAll, setSelectAll] = useState(false);
  const [selectAllExtend, setSelectAllExtend] = useState(false);
  const [selectAllCOA, setSelectAllCOA] = useState(false);

  const [selectedListItems, setSelectedListItems] = useState([]);
  const [selectedListItemsCOA, setSelectedListItemsCOA] = useState([]);
  const [selectedGl, setSelectedGl] = useState([]);
  const [selectedCodes, setSelectedCodes] = useState([]);
  const [downloadMultiple, setDownloadMultiple] = useState(false);
  const [glAccountsWithColor, setGlAccountsWithColor] = useState({});

  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [downloadType, setDownloadType] = useState("systemGenerated");
  useEffect(() => {
  }, [selectedListItems]);


  useEffect(() => {
    setIsValidateAfterSeriesCheck(true)
  }, [newCompanyCode]);

  const NoMaxWidthTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: "none",
    },
  });

  const [openSelectColumnDialog, setOpenSelectColumnDialog] = useState(false);

  const [extendDropDownList, setExtendDropdownList] = useState([]);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [dataList, setDataList] = useState([]);
  const [dataListCOA, setDataListCOA] = useState([]);
  const [dataListBlocked, setDataListBlocked] = useState([]);

  const [selectedOptionBlocked, setSelectedOptionBlocked] = useState([]);
  const [openDownloadChangeDialog, setOpenDownloadChangeDialog] =
    useState(false);
  const [placeholderForGLAccount, setPlaceHolderForGLAccount] = useState([
    "ENTER GL ACCOUNT",
  ]);
  const rmSearchForm = useSelector(
    (state) => state.commonFilter["GeneralLedger"]
  );
    const { customError } = useLogger()
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const ITEM_HEIGHT = 48;
  const ITEM_PADDING_TOP = 8;
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
        width: 250,
      },
    },
  };
  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadChangeDialogClose = () => {
    setOpenDownloadChangeDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const handleMultipleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownloadCreate();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };
  const onMultipleDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownloadChangeDialogClose();
      handleApply();
    }
    if (downloadType === "mailGenerated") {
      handleDownloadChangeDialogClose();
      handleApplyEmail();

    }
  };

  const HtmlTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
  ))(({ theme }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: "#f5f5f9",

      color: "rgba(0, 0, 0, 0.87)",

      maxWidth: 250,

      border: "1px solid #dadde9",
    },
  }));

  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
    setSkip(0);
  };
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const duplicateFieldsColumns = [
    {
      field: "glAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
      width: 150,
    },
    {
      field: "reqId",
      headerName: "Req Id",
      editable: false,
      flex: 1,
      width: 200,
    },
    {
      field: "requestedBy",
      headerName: "Requested By",
      editable: false,
      flex: 1,
      width: 250,
    },
  ];

  useEffect(() => {
    if (rmSearchForm?.["G/L Account"]) {
      const costCenterArray = rmSearchForm?.["G/L Account"].split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetGeneralLedger(formattedCostCenterArray);
    }

    if (rmSearchForm?.["Company Code"]) {
      const costCenterArray = rmSearchForm?.["Company Code"].split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetComanyCode(formattedCostCenterArray);
    }

    if (rmSearchForm?.TaxCategory) {
      const costCenterArray = rmSearchForm?.TaxCategory.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetTaxCategory(formattedCostCenterArray);
    }

    if (rmSearchForm?.createdBy) {
      const costCenterArray = rmSearchForm?.createdBy.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetCreatedBy(formattedCostCenterArray);
    }

    if (rmSearchForm?.glAcctLongText) {
      const costCenterArray = rmSearchForm?.glAcctLongText.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetLongText(formattedCostCenterArray);
    }

    if (rmSearchForm?.shortText) {
      const costCenterArray = rmSearchForm?.shortText.split("$^$");
      const formattedCostCenterArray = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setselectedPresetShortText(formattedCostCenterArray);
    }

    if (rmSearchForm?.createdOn) {
      const presentDate = new Date(rmSearchForm?.createdOn[0]);
      const backDate = new Date(rmSearchForm?.createdOn[1]);
      setseletedDateRange([presentDate, backDate]);
    }

    if (rmSearchForm?.["Account Group"]) {
      const costCenterArray = rmSearchForm?.["Account Group"].split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));

      setSelectedPresetValues((prev) => ({
        ...prev,
        ["Account Group"]: valuesSelected ?? [],
      }));
    }

    if (rmSearchForm?.["G/L Account Type"]) {
      const costCenterArray = rmSearchForm?.["G/L Account Type"].split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));

      setSelectedPresetValues((prev) => ({
        ...prev,
        ["G/L Account Type"]: valuesSelected ?? [], 
      }));
    }

    if (rmSearchForm?.["Field Status Group"]) {
      const costCenterArray = rmSearchForm?.["Field Status Group"].split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        ["Field Status Group"]: valuesSelected ?? [], 
      }));
    }

    if (rmSearchForm?.["Recon Account for Acct Type"]) {
      const costCenterArray = rmSearchForm?.["Recon Account for Acct Type"].split("$^$");
      const valuesSelected = costCenterArray.map((substring) => ({
        code: substring,
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        ["Recon Account for Acct Type"]: valuesSelected ?? [], 
      }));
    }

  }, [rmSearchForm])

  useEffect(() => {
    Object.keys(selectedValues).forEach((option) => {
      const tempSelected = selectedValues[option]
        ?.map((item) => item?.code)
        .join("$^$");
      let tempFilterData = {
        ...rmSearchForm,
        [option]: tempSelected,
      };

      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    });
  }, [selectedValues]);

  useEffect(() => {
    var tempCompanyCode = selectedComanyCode
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      ["Company Code"]: tempCompanyCode,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedComanyCode]);

  useEffect(() => {
    var tempCC = selectedGeneralLedger.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      "G/L Account": tempCC,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedGeneralLedger]);

  useEffect(() => {
    var tempShortText = selectedShortText.map((item) => item?.code).join("$^$");
    let tempFilterData = {
      ...rmSearchForm,
      shortText: tempShortText,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedShortText]);

  useEffect(() => {
    var tempCreatedBy = selectedCreatedBy.map((item) => item?.code).join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      createdBy: tempCreatedBy,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedCreatedBy]);

  useEffect(() => {
    var tempTaxCategory = selectedTaxCategory
      .map((item) => item?.code)
      .join("$^$");

    let tempFilterData = {
      ...rmSearchForm,
      TaxCategory: tempTaxCategory,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedTaxCategory]);

  useEffect(() => {
    var tempGLAccountLongText = selectedLongText
      .map((item) => item?.code)
      .join("$^$");
    let tempFilterData = {
      ...rmSearchForm,
      glAcctLongText: tempGLAccountLongText,
    };
    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  }, [selectedLongText]);

  useEffect(() => {

  }, []);

  useEffect(() => {
    if (page * pageSize >= rmDataRows?.length) {
      getFilterBasedOnPagination();
    }

  }, [page, pageSize]);


  const handleSelectAllData = () => {
    setStatusOfSelectAllFirstData(true)

    getFilterAfterSelectAllOptions()
  }

  const handleFirstPageOptions = () => {
    setStatusOfSelectAllFirstData(true)
    setPage(0)
  }

  const getFilterAfterSelectAllOptions = () => {
    setPage(0);
    setTableLoading(true);
    let payload = {
      glAccount: rmSearchForm?.["G/L Account"] ?? "",
      chartOfAccount: rmSearchForm?.chartOfAccount?.code
        ? rmSearchForm?.chartOfAccount?.code
        : "ETCN",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      taxCategory: rmSearchForm?.TaxCategory ?? "",
      glAcctLongText: rmSearchForm?.glAcctLongText ?? "",
      postingWithoutTaxAllowed:
        rmSearchForm?.postingWithoutTaxAllowed === "Allowed"
          ? "X"
          : rmSearchForm?.postingWithoutTaxAllowed === "Not Allowed"
            ? "Y"
            : "",
      blockedForPostingInCOA:
        rmSearchForm?.blockedForPostingInCOA === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCOA === "Unblocked"
            ? "Y"
            : "",
      blockedForPostingInCompany:
        rmSearchForm?.blockedForPostingInCompany === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCompany === "Unblocked"
          ? "Y"
          : "",
      accountGroup: rmSearchForm?.["Account Group"] ?? "",
      shortText: rmSearchForm?.shortText ?? "",
      glAccountType: rmSearchForm?.["G/L Account Type"] ?? "",
      fieldStatusGroup: rmSearchForm?.["Field Status Group"] ?? "",
      openItemMgmtbyLedgerGroup:
        rmSearchForm?.openItemMgmtByLedgerGroup === "True - X"
          ? "X"
          : rmSearchForm?.openItemMgmtByLedgerGroup === "False"
            ? "Y"
            : "",
      openItemManagement:
        rmSearchForm?.openItemManagement === "True - X"
          ? "X"
          : rmSearchForm?.openItemManagement === "False"
            ? "Y"
            : "",
      postAutoOnly: rmSearchForm?.postAutoOnly === "True - X"
        ? "X"
        : rmSearchForm?.postAutoOnly === "False"
          ? "Y"
          : "",

      reconAccountforAcctType:
        rmSearchForm?.["Recon Account for Acct Type"] ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      top: count,
      skip: 0,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let glAccountArr = [];

        data?.body?.list?.forEach((item) => {
          if (item?.COA == "ETCN") {
            let glAccountHash = {};
            glAccountHash["code"] = item?.GLAccount;
            glAccountHash["desc"] = item?.GLAccount;
            glAccountArr.push(glAccountHash);
          }
        });
        const uniqueArray = Object.values(
          glAccountArr.reduce((acc, item) => {
            acc[item.code] = item;
            return acc;
          }, {})
        );
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body?.list[index];
                    var tempRow = {
            id: uuidv4(),
            chartOfAccount: tempObj?.COA !== "" ? tempObj?.COA : "",
            glAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "",
            glAcctLongText:
              tempObj?.Description !== "" ? tempObj?.Description : "",
            AccountGroup:
              tempObj?.AccountGroup !== "" ? tempObj?.AccountGroup : "",
            compCode: tempObj?.CompanyCode !== "" ? tempObj?.CompanyCode : "",
            glAccount: tempObj?.GLAccount !== "" ? tempObj?.GLAccount : "",
            groupAccountNumber:
              tempObj?.GroupAccNo !== "" ? tempObj?.GroupAccNo : "",
            postingWithoutTaxAllowed:
              tempObj?.Pstnwotax === "X" ? "Allowed" : "Not Allowed",
            blockedForPostingInCOA:
              tempObj?.PostingBlockedCOA === "X" ? "Blocked" : "Unblocked",
            blockedForPostingInCompany:
              tempObj?.PostingBlockedCoCd === "X" ? "Blocked" : "Unblocked",
            PostAutomaticallyOnly: tempObj?.PostAuto === "X" ? "X" : "",
            TaxCategory: tempObj.Taxcategory !== "" ? tempObj?.Taxcategory : "",
            createdBy: tempObj?.CreatedBy !== "" ? tempObj?.CreatedBy : "",
            shortText: tempObj?.GLname !== "" ? tempObj?.GLname : "",
            gLAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "",
            fieldStatusGroup:
              tempObj?.FieldStsGrp !== "" ? tempObj?.FieldStsGrp : "",
            openItemManagement:
              tempObj?.Openitmmanage !== "" ? tempObj?.Openitmmanage : "",
            openItemMgmtByLedgerGroup:
              tempObj?.OIMgmtByLedgerGrp !== ""
                ? tempObj?.OIMgmtByLedgerGrp
                : "",
            reconAccountForAcctType:
              tempObj?.ReconAcc !== "" ? tempObj?.ReconAcc : "",
            createdOn:
              tempObj?.CreatedOn !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "",

          };
          rows.push(tempRow);
        }
        rows.sort(
          (a, b) =>
            moment(a.createdOn, "DD MMM YYYY HH:mm") -
            moment(b.createdOn, "DD MMM YYYY HH:mm")
        );

        setPage(Math.floor(rows?.length / pageSize));
        setRmDataRows(rows);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);


      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  }




  var newDate = new Date();
  var validToDate = new Date("December 31, 9999 01:15:00");
  const [isLoading, setIsLoading] = useState(true);
  const [timerId, setTimerId] = useState(null);
  const [value, setValue] = useState(null);
  const ariaLabel = { "aria-label": "description" };
  const [rmDataRows, setRmDataRows] = useState([]);
  const [tableData, setTableData] = useState([...rmDataRows]);
  const [UserName, setUserName] = React.useState("");
  const [openSnackBaraccept, setOpenSnackBaraccept] = useState(false);
  const [confirmingid, setConfirmingid] = useState("");
  const [materialNumber, setMaterialNumber] = useState("");
  const [confirmStatus, setConfirmStatus] = useState(true);
  const [messageDialogExtra, setMessageDialogExtra] = useState(false);
  const [messageDialogOK, setMessageDialogOK] = useState(true);
  const [vendorDetailsSet, setVendorDetailsSet] = useState([]);
  const [taskstatusSet, setTasksttusSet] = useState([]);
  const [disableButton, setDisableButton] = useState(true);
  const [selectedRow, setSelectedRow] = useState([]);
  const [selectedDetails, setSelectedDetails] = useState([]);
  const [downloadError, setdownloadError] = useState(false);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedOptionsnew, setSelectedOptionsnew] = useState([]);
  const [displayedFields, setDisplayedFields] = useState([]);
  const [filterFieldData, setFilterFieldData] = useState({});
  const [matType, setMatType] = useState([]);
  const [plantCodeSet, setPlantCodeSet] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [viewDetailpage, setViewDetailpage] = useState(false);
  const [matNumber, setMatNumber] = useState([]);
  const [dynamicOptions, setDynamicOptions] = useState([]);
  const [plantForWarehouse, setPlantForWarehouse] = useState([]);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [fullWidth, setFullWidth] = useState(true);
  const [maxWidth, setMaxWidth] = useState("sm");
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [selectedIndexChange, setSelectedIndexChange] = useState(0);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [openButtonChange, setOpenButtonChange] = useState(false);
  const [openButtonChangeExtend, setOpenButtonChangeExtend] = useState(false);
  const [successMsg, setsuccessMsg] = useState(false);
  const [dialogOpenCreate, setDialogOpenCreate] = useState(false);
  const [checkValidationCostCenter, setCheckValidationCostCenter] =
    useState(false);
  const [rmDataRowsExtend, setRmDataRowsExtend] = useState([]);
  const [newGLAccountCopyFrom, setNewGLAccountCopyFrom] = useState("");
  const anchorRef = React.useRef(null);
  const anchorRefChange = React.useRef(null);
  const anchorRefChangeExtend = React.useRef(null);
  const [openButton, setOpenButton] = useState(false);
  const [isValidationErrorwithCopy, setIsValidationErrorwithCopy] =
    useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectedRowsExtend, setSelectedRowsExtend] = useState([]);
  const [selectedRowsExtendData, setSelectedRowsExtendData] = useState([]);
  const [count, setCount] = useState(0);
  const [tableRows, setTableRows] = useState([]);
  const [tableRowsExtend, setTableRowsExtend] = useState([]);
  const [alignment, setAlignment] = useState("Company Code");
  const [selectedListItemsExtend, setSelectedListItemsExtend] = useState([]);
  const [extendSelectedOptionsWithkey, setExtendSelectedOptionsWithkey] =
    useState({});
  const [extendSelectedAllOptionsWithkey, setExtendSelectedAllOptionsWithkey] =
    useState({});
  const [glAccountRangeData, setGLAccountRangeData] = useState([]);
  const [accountTypeWithCopy, setAccountTypeWithCopy] = useState([]);
  const [accountGroupWithCopy, setAccountGroupWithCopy] = useState([]);
  const [accountSubGroupWithCopy, setAccountSubGroupWithCopy] = useState([]);
  const [rangeForGlAccount, setRangeForGlAccount] = useState([]);
  const [accountNumberCopyFrom, setAccountNumberCopyFrom] = useState([]);
  const [companyCodeCopyFrom, setCompanyCodeCopyFrom] = useState([]);
  const [glAccountRangeWithCopy, setGlAccountRangeWithCopy] = useState("");
  const [glAccountWithCopyMinRange, setglAccountWithCopyMinRange] = useState(
    []
  );
  const [glAccountWithCopyMaxRange, setglAccountWithCopyMaxRange] = useState(
    []
  );
  const [accountNumberWithCopy, setAccountNumberWithCopy] = [];
  const [accontTypeWithCopy, setaccontTypeWithCopy] = useState([]);
  const [accontGroupWithCopy, setaccontGroupWithCopy] = useState([]);
  const [accontGroupWithCopyValue, setaccontGroupWithCopyValue] =
    useState(null);
  const [subaccontGroupWithCopy, setsubaccontGroupWithCopy] = useState(null);
  const [copyFormDisable, setcopyFormDisable] = useState(false);
  const [selectedExtendValues, setSelectedExtendValues] = useState([]);
  const [searchInput, setSearchInput] = useState("");
  const [searchInputExtend, setSearchInputExtend] = useState("");
  const [rowsofCopy, setRowsofCopy] = useState([]);
  const [copiedData, setCopiedData] = useState(null);
  const [gLValidationRangeErrors, setGLValidationRangeErrors] = useState([]);
  const [glNamePesentError, setGlNamePesentError] = useState([]);
  const [glValidationRange, setGlValidationRange] = useState(false);
  const [glValidationGlName, setGlValidationGlName] = useState(false);
  const [dialogOpenMassWithCopyError, setdialogOpenMassWithCopyError] =
    useState(false);
  const [dialogOpenGlPresentError, setDialogOpenGlPresentError] =
    useState(false);
  const [glAccountOptionsForExtend, setGlAccountOptionsForExtend] = useState(
    []
  );
  const [openDialogMassExtend, setOpenDialogMassExtend] = useState(false);
  const [openDialogMassFerc, setOpenDialogMassFerc] = useState(false);
  const [tableRowsFerc, setTableRowsFerc] = useState(false);

  const [isValidateAfterSeriesCheck, setIsValidateAfterSeriesCheck] =
    useState(true);
  const [isSunocoChecked, setIsSunocoChecked] = useState(false);
  const [chartOfAccountExtend, setChartOfAccountExtend] = useState([]);

  const [selectedglAccountForMassExtend, setSelectedglAccountForMassExtend] =
    useState([]);
  const [selectedGLAccount, setSelectedGLAccount] = useState([]);
  const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
  const [popoverContent, setPopoverContent] = useState("");
  const [isPopoverVisible, setIsPopoverVisible] = useState(false);
  const popoverRef = useRef(null);

  useEffect(() => {
    dispatch(clearArtifactId());
  }, []);
  useEffect(() => {
    function handleClickOutside(event) {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        setIsPopoverVisible(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handlePopoverOpen = (event, content) => {
    setPopoverAnchorEl(event.currentTarget);
    setPopoverContent(content);
    setIsPopoverVisible(true);
  };

  const handlePopoverClose = () => {
    setIsPopoverVisible(false);
  };

  const handleMouseEnterPopover = () => {
    setIsPopoverVisible(true);
  };

  const handleMouseLeavePopover = () => {
    setIsPopoverVisible(false);
  };

  const popoverOpen = Boolean(popoverAnchorEl);
  const popoverId = popoverOpen ? "custom-popover" : undefined;

  const allOptions = dropdownData?.GlAccountForExtend;
  const [displayOptions, setDisplayOptions] = useState(
    allOptions?.slice(0, 100)
  ); 
  const [inputValue, setInputValue] = useState("");
  const [blurLoadingForInner, setBlurLoadingForInner] = useState(false);

  useEffect(() => { }, [filterFieldData]);

  const optionsk = [
    { title: "Option 1", group: "Group A" },
    { title: "Option 2", group: "Group A" },
    { title: "Option 3", group: "Group B" },
    { title: "Option 4", group: "Group B" },
  ];

  const groupOptions = (options) => {
    return options.reduce((groups, option) => {
      const group = groups[option.group] || [];
      return { ...groups, [option.group]: [...group, option] };
    }, {});
  };

  const groupedOptions = groupOptions(optionsk);

  const handleChangeOK = (event, newValue) => {
    setSelectedOptionsnew(newValue);
  };

  const handleSelectAllToggle = (group, isSelectAllChecked) => {

    const groupOptions = groupedOptions[group];
    if (isSelectAllChecked) {

      setSelectedOptionsnew((prevSelected) => [
        ...prevSelected,
        ...groupOptions.filter((opt) => !prevSelected.includes(opt)),
      ]);
    } else {
      setSelectedOptionsnew((prevSelected) =>
        prevSelected.filter((opt) => !groupOptions.includes(opt))
      );
    }
  };

  const [glSearchField, setGlSearchField] = useState("");

  let editMultipleGlExtendData = useSelector(
    (state) => state.generalLedger.editMultipleGlExtend
  );
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const [value34, setValue34] = useState([]);

  const handleChange45 = (event, newValue) => {
    setValue34(newValue);
  };

  const handleInputChange = (event, newInputValue) => {
    if (newInputValue !== "") {
      setSearchInput(newInputValue);
    } else if (event?.nativeEvent?.inputType === "deleteContentBackward") {
      setSearchInput("");
    } else {
      setSearchInput((prev) => prev);
    }
  };
  const handleInputChangeExtend = (event, newInputValue) => {

    setSearchInputExtend(newInputValue);
  };
  const handleOptionChange = (event, newValue) => {
    setSelectedExtendValues(newValue);
  };

  const handleSearchChange = (event, newInputValue) => {
    setSearchInput(newInputValue);
  };

  const filterOptions = (options, { inputValue }) => {
    const searchValues = inputValue
      .split(",")
      .map((value) => value.trim().toLowerCase());
    if (!inputValue.trim() || !searchValues[0]) {
      return options;
    }
    return options.filter((option) =>
      searchValues.some(
        (searchValue) => option.code.toLowerCase().includes(searchValue)

      )
    );
  };

  let allColumnsinExtend = [
    {
      field: "coa",
      headerName: "Chart Of Account",
      flex: 1.5,
      width: 100,
    },
    {
      field: "GLAccount",
      headerName: "GL Account",
      flex: 1.5,
      width: 100,
      renderCell: (params) => {
        const selectedRowId = selectedIdForGlMassExtend;

        const isSelected = params.id === selectedRowId;
        const isGreen = glAccountsWithColor[params.value] === true;
        return (
          <div
            style={{
              color: isSelected ? "#0033A0" : "#333",
              fontWeight: isSelected ? "bold" : "normal",
              padding: "12px",
              display: "flex",
              alignItems: "center",
            }}
          >
            {params.value}
            {isGreen && (
              <CheckCircleOutlinedIcon
                style={{ color: "green", marginLeft: "15px" }}
              />
            )}
          </div>
        );
      },
    },
    {
      field: "glDesc",
      headerName: "GL Description",
      flex: 2,
      width: 100,
    },
  ];

  const formcontroller_SearchBar = useSelector(
    (state) => state.commonSearchBar["GeneralLedger"]
  );
  const sendNewGeneralLedgerData = {
    chartOfAccounts: { newChartOfAccount },
    companyCode: { newCompanyCode },
    accountType: { newAccountType },
    accountGroup: { newAccountGroup },
    newGLAccount,
    copyFromCompCode: { newCompanyCodeToCopyFrom },
    copyFromGlAccount: { newGLAccountCopyFrom },
  };

  const sendNewGeneralLedgerDataWithCopy = {
    chartOfAccounts: { newChartOfAccount },
    companyCode: { newCompanyCode },
    accountType: { newAccountType },
    accountGroup: { accontGroupWithCopy },
    subAccountGroup: { subaccontGroupWithCopy },
    newGLAccount,
    copyFromCompCode: { companyCodeCopyFrom },
    copyFromGlAccount: { accountNumberCopyFrom },
    requstType: "Display For Create",
  };

  const handleDialogClickOpen = () => {
    setDialogOpen(true);
  };


  const columnCopy = [
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      editable: false, 
      flex: 1,
      width: 100,
    },
    {
      field: "accountType",
      headerName: "SAP G/L ACCOUNT TYPE ",
      editable: false,
      flex: 1,
      width: 100,
    },
    {
      field: "accountGroup",
      headerName: "SAP ACCOUNT GROUP (LEVEL 1)",
      editable: false, 
      flex: 1,
      width: 100,
    },
    {
      field: "subAccountGroup",
      headerName: "SUB-ACCOUNT GROUP",
      editable: false, 
      flex: 1,
      width: 250,

    },

    {
      field: "glAccountNumberCreated",
      headerName: "GL Account Number",
      editable: false, 
      flex: 1,
      width: 100,
      renderCell: (params) => {

        const isDirectMatch = directmatchmassGLWithCopy.find(
          (element) => element.glAccount === params.value
        );

        if (isDirectMatch) {
          return (
            <Typography sx={{ fontSize: "12px", color: "red" }}>
              {params.value}
            </Typography>
          );
        } else {
          return (
            <Typography sx={{ fontSize: "12px" }}>{params.value}</Typography>
          );
        }
      },
    },
    {
      field: "compCodeCreated",
      headerName: "Company Code",
      editable: false, 
      flex: 1,
      width: 100,
    },
  ];

  const checkForNameAndCompCodeDuplicateCheck = () => {
    let result = newGLAccount.concat("$$", newCompanyCode?.code);
    setIsLoading(true);
    setBlurLoading(true);
    let payloadforAccountCheck = {
      glAccount: newGLAccount,
      coa: newChartOfAccount,
    };

    const hSuccess = (data) => {
      setIsLoading(false);
      setBlurLoading(false);
      if (data.body.length > 0) {
        setCheckValidationGeneralLedger(true);
      } else {
        dispatch(
          setSinglegeneralLedgerPayload({
            keyName: "AccountType",
            data: newAccountType,
          })
        );
        dispatch(
          setSinglegeneralLedgerPayload({
            keyName: "AccountGroup",
            data: {
              code: newAccountGroup?.AccountGroup,
              desc: newAccountGroup?.Description,
            },
          })
        );
        navigate(`/masterDataCockpit/generalLedger/newSingleGeneralLedger`, {
          state: sendNewGeneralLedgerData,
        });
      }
    };
    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChk`,
      "get",
      hSuccess,
      hError,
      payloadforAccountCheck
    );
  };

  const PresetMethod = () => {
    let tempFilterData = {
      costCenterName: rmSearchForm?.costCenterName ?? "",
      costCenter: "",
      controllingArea: rmSearchForm?.controllingArea?.code ?? "",
      companyCode: rmSearchForm?.companyCode?.code ?? "",
      profitCenter: rmSearchForm?.profitCenter?.code ?? "",
      hierarchyArea: rmSearchForm?.hierarchyArea?.code ?? "",
      costCenterCategory: rmSearchForm?.costCenterCategory?.code ?? "",
      createdBy: "",
      fromDate: "",
      toDate: "",
      personResponsible: filterFieldData?.["Person Responsible"] ?? "",
      businessArea: filterFieldData?.["Business Area"]?.code ?? "",
      functionalArea: filterFieldData?.["Functional Area"]?.code ?? "",
    };
    dispatch(
      commonFilterUpdate({
        module: "CostCenter",
        filterData: tempFilterData,
      })
    );
  };
  const PresetObj = [{ name: "CostCenterName", value: "TUK1" }];


  const duplicateCheck = () => {
    if (
      newChartOfAccount.code === undefined ||
      newChartOfAccount.code === "" ||
      newCompanyCode?.code === undefined ||
      newCompanyCode?.code === "" ||
      newAccountType?.code === undefined ||
      newAccountType?.code === "" ||
      newAccountGroup?.AccountGroup === undefined ||
      newAccountGroup?.AccountGroup === "" ||
      newGLAccount === undefined ||
      newGLAccount === ""
    ) {
      setNewGeneralLedgerValid(false);
      setIsValidationError(true);
      setNumberRangeError(false);
      return;
    } else {
      if (newGLAccount.length !== 10) {
        setNumberRangeError(false);
        setNewGeneralLedgerValid(true);
        setIsValidationError(false);
        return;
      } else {
        let GlNumber = Number(newGLAccount); 
        let minNumber = Number(newAccountGroup?.FromAcct); 
        let maxNumber = Number(newAccountGroup?.ToAcct); 
        if (
          minNumber == 0 ||
          isNaN(minNumber) ||
          maxNumber == 0 ||
          isNaN(maxNumber)
        ) {
        
          setNumberRangeError(false);
          setIsValidationError(false);
          setNewGeneralLedgerValid(false);
          checkForNameAndCompCodeDuplicateCheck();
        } else {
          if (GlNumber >= minNumber && GlNumber <= maxNumber) {
            setNumberRangeError(false);
            setIsValidationError(false);
            setNewGeneralLedgerValid(false);
            checkForNameAndCompCodeDuplicateCheck();
          } else {
            setNumberRangeError(true);
            setIsValidationError(false);
            setNewGeneralLedgerValid(false);
          }
          setNewGeneralLedgerValid(false);
        }
        setIsValidationError(false);
      }
    }
  };
  const handleDialogProceed = () => {
    duplicateCheck();
  };

  const handleDialogCloseCreate = () => {
    setNewChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    getCompanyCodeBasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    }); 
    getAllGLAccountbasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    setNewCompanyCode([]);
    setIsValidationError(false);
    setIsValidationErrorwithCopy(false);
    setNewGeneralLedgerValidWithCopy(false);
    setNewAccountGroup([]);
    setaccontGroupWithCopyValue(null);
    setsubaccontGroupWithCopy(null);
    setIsValidateAfterSeriesCheck(true);
    setDialogOpenCreate(false);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setNumberRangeError(false);
    setIsValidationError(false);
    setNewGeneralLedgerValid(false);
  };
  const handleToggle = () => {
    setOpenButton((prevOpen) => !prevOpen);
  };
  const handleToggleChange = () => {
    setOpenButtonChange((prevOpen) => !prevOpen);
  };
  const handleToggleChangeExtend = () => {
    setOpenButtonChangeExtend((prevOpen) => !prevOpen);
  };
  const handleCloseButton = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpenButton(false);
  };
  const handleCloseButtonChange = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChange(false);
  };
  const handleCloseButtonChangeExtend = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonChangeExtend(false);
  };

  const handleChange = (event, newAlignment) => {
    setAlignment(newAlignment);

  };
  const handleSelectionListItem = (item) => {

    const selectedIndex = selectedListItems.findIndex(
      (selectedItem) => selectedItem?.id === item?.id
    );
    let newSelected = [];
    if (selectedIndex === -1) {
      newSelected = [...selectedListItems, item];
    } else if (selectedIndex === 0) {
      newSelected = selectedListItems?.slice(1);
    } else if (selectedIndex === selectedListItems?.length - 1) {
      newSelected = selectedListItems?.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...selectedListItems?.slice(0, selectedIndex),
        ...selectedListItems?.slice(selectedIndex + 1),
      ];
    }

    setSelectedListItems(newSelected);
  };

  const getChangedDataExtend = (
    editMultipleGlDataExtend,
    selectedId,
    newValue
  ) => {

    const desiredObject = editMultipleGlDataExtend.find(
      (item) => item.glAccount === selectedId
    );

    if (desiredObject) {
      const updatedCoCodeToExtend =
        desiredObject.coCodeToExtend.concat(newValue);
      const updatedObject = {
        ...desiredObject,
        coCodeToExtend: updatedCoCodeToExtend,
      };
      const updatedJsonData = editMultipleGlDataExtend?.map((item) => {
        if (item?.glAccount === selectedId) {
          return updatedObject;
        }
        return item;
      });

      return updatedJsonData;
    } else {
      return editMultipleGlDataExtend;
    }
  };

  const handleSelectionListItemExtend = (item) => {
    const selectedIndex = selectedListItemsExtend?.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selectedListItemsExtend, item];
    } else if (selectedIndex === 0) {
      newSelected = selectedListItemsExtend?.slice(1);
    } else if (selectedIndex === selectedListItemsExtend.length - 1) {
      newSelected = selectedListItemsExtend?.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...selectedListItemsExtend?.slice(0, selectedIndex),
        ...selectedListItemsExtend?.slice(selectedIndex + 1),
      ];
    }

    setSelectedListItemsExtend(newSelected);

    setExtendSelectedOptionsWithkey((prevState) => ({
      ...prevState,
      [selectedRowsExtend]: newSelected,
    }));
   
    setGlAccountsWithColor((prevState) => ({
      ...prevState,
      [selectedRowsExtend]: newSelected.length > 0,
    }));
  };

  const handleSelectAll = () => {
    if (!selectAll && alignment === "Company Code") {
      setSelectedListItems(dataList);
    } else if (selectAll && alignment === "Company Code") {
      setSelectedListItems([]);
    } else if (!selectAll && alignment === "Chart of Account") {
      setSelectedListItemsCOA(dataListCOA);
    } else {
      setSelectedListItemsCOA([]);
    }
    setSelectAll(!selectAll);
  };
  const handleSelectAllComapnyCodeWithCopy = () => {
    if (newCompanyCode.length === dropdownData.CompanyCodeForWithCopy.length) {
      setNewCompanyCode([]);
    } else {
      setNewCompanyCode(dropdownData.CompanyCodeForWithCopy);
    }
  };
  const handleSelectAllExtend = () => {
    if (!extendSelectedAllOptionsWithkey[selectedRowsExtend]) {
      setSelectedListItemsExtend(dropdownData["CompanyCodeExtend"]);
      setExtendSelectedAllOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: [...dropdownData["CompanyCodeExtend"]],
      }));

      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));
    } else {
      setExtendSelectedAllOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: [],
      }));
      setSelectedListItemsExtend([]);
      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));
    }
    setSelectAllExtend(!selectAllExtend);
  };



  const handleETCompanyCodes = () => {
    const isCurrentlySelected =
      extendETCompanyCodesSelectedWithkey[selectedRowsExtend] || false;

    setExtendETCompanyCodesSelectedWithkey((prevState) => ({
      ...prevState,
      [selectedRowsExtend]: !isCurrentlySelected,
    }));

    if (!isCurrentlySelected) {
      const selectedItems = extendDropDownList.filter(
        (item) => !(parseInt(item.code) >= 7000 && parseInt(item.code) < 8000)
      );

      setSelectedListItemsExtend(selectedItems);
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: selectedItems,
      }));

      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));

      setETCompanyCodesSelected(true);
      setExtendSunocoCompaniesDisabledWithKey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));
    } else {
      setSelectedListItemsExtend([]);
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: [],
      }));

      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));

      setETCompanyCodesSelected(false);
      setExtendSunocoCompaniesDisabledWithKey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));
    }
  };



  const handleSunocoCompanyCodes = () => {
    const isCurrentlySelected =
      extendSunocoCompanyCodesSelectedWithkey[selectedRowsExtend] || false;

    setExtendSunocoCompanyCodesSelectedWithkey((prevState) => ({
      ...prevState,
      [selectedRowsExtend]: !isCurrentlySelected,
    }));

    if (!isCurrentlySelected) {
      const selectedItems = extendDropDownList.filter(
        (item) => parseInt(item.code) >= 7000 && parseInt(item.code) < 8000
      );

      setSelectedListItemsExtend(selectedItems);
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: selectedItems,
      }));

      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));

      setExtendETCompaniesDisabledWithKey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: true,
      }));
    } else {
      setSelectedListItemsExtend([]);
      setExtendSelectedOptionsWithkey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: [],
      }));

      setGlAccountsWithColor((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));

      setExtendETCompaniesDisabledWithKey((prevState) => ({
        ...prevState,
        [selectedRowsExtend]: false,
      }));
    }
  };

  const handleSelectionListItemCOA = (item) => {
    const selectedIndex = selectedListItemsCOA.findIndex(
      (selectedItem) => selectedItem.id === item.id
    );
    let newSelected = [];

    if (selectedIndex === -1) {
      newSelected = [...selectedListItemsCOA, item];
    } else if (selectedIndex === 0) {
      newSelected = selectedListItemsCOA?.slice(1);
    } else if (selectedIndex === selectedListItemsCOA.length - 1) {
      newSelected = selectedListItemsCOA?.slice(0, -1);
    } else if (selectedIndex > 0) {
      newSelected = [
        ...selectedListItemsCOA?.slice(0, selectedIndex),
        ...selectedListItemsCOA?.slice(selectedIndex + 1),
      ];
    }

    setSelectedListItemsCOA(newSelected);
  };


  const options = ["Create Multiple", "Upload Template ", "Download Template "];
  const optionsChange = [
    "Change Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const optionsChangeExtend = [
    "Extend Multiple",
    "Upload Template ",
    "Download Template ",
  ];
  const [selectedIndexCreate, setSelectedIndexCreate] = useState(0);
  const optionsCreateSingle = ["Create Single", "With Copy", "Without Copy"];
  const anchorRefCreate = React.useRef(null);
  const [openButtonCreate, setOpenButtonCreate] = useState(false);
  const handleToggleCreate = () => {
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleDialogProceedWithCopy = () => {
    duplicateCheckWithCopy();
  };
  const duplicateCheckWithCopy = () => {

    if (
      newChartOfAccount.code === undefined ||
      newChartOfAccount.code === "" ||
      newCompanyCode?.length <= 0 ||
      newGLAccount === undefined ||
      newGLAccount === "" ||
      newAccountType?.code === undefined ||
      newAccountType?.code === "" ||
      subaccontGroupWithCopy?.code === undefined ||
      subaccontGroupWithCopy?.code === "" ||
      accontGroupWithCopy?.AccountGroup === undefined ||
      accontGroupWithCopy?.AccountGroup === ""
    ) {
      setNewGeneralLedgerValid(false);
      setIsValidationErrorwithCopy(true);
      return;
    } else {
      if (
        !(
          newGLAccount >= glAccountWithCopyMinRange &&
          newGLAccount <= glAccountWithCopyMaxRange
        )
      ) {
        setNewGeneralLedgerValid(true);
        setIsValidationErrorwithCopy(false);
        return;
      } else {
        setNewGeneralLedgerValid(false);
      }
      setIsValidationErrorwithCopy(false);
    }
    let result = newGLAccount.concat("$$", companyCodeCopyFrom);
    let payloadforAccountCheck = {
      glAccount: newGLAccount,
      coa: newChartOfAccount?.code,
    };
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);

      if (data.body.length > 0) {
        setCheckValidationGeneralLedger(true);
      } else {
        setDialogOpenCreate(false);
        dispatch(clearGeneralLedger());
        dispatch(clearPayloadForEdit());
        navigate(
          `/masterDataCockpitNew/generalLedger/displayCopyGeneralLedger`,
          {
            state: sendNewGeneralLedgerDataWithCopy,
          }
        );
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChk`,
      "post",
      hSuccess,
      hError,
      payloadforAccountCheck
    );
  };
  const handleDialogClickOpenWithCopy = () => {
    setDialogOpenCreate(true);
  };

  const handleDialogCloseCreateSingle = (event) => {
    if (
      anchorRefCreate.current &&
      anchorRefCreate.current.contains(event.target)
    ) {
      return;
    }
    setOpenButtonCreate((prevOpen) => !prevOpen);
  };

  const handleClickCreate = (option, index) => {
    if (index !== 0) {
      setSelectedIndexCreate(index);
      setOpenButtonCreate(false);
      if (index === 1) {
        handleCreateSingleWithCopy();
      } else if (index === 2) {
        handleCreateSingleWithoutCopy();
      }
    }
  };
  const handleCreateSingleWithoutCopy = () => {
    handleDialogClickOpen();
  };
  const handleCreateSingleWithCopy = () => {
    handleDialogClickOpenWithCopy();
  };
  const handleSnackBarOpen = () => {
    setopenSnackbar(true);
  };

  const getAccountGroupForSearch = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroup", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroupCodeDesc?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCompanyCodeForSearch = (value) => {
    const hSuccess = (data) => {
      let newcompCodeArr = [];

      data?.body?.map((item) => {
        let newcompCodehash = {};
        newcompCodehash["code"] = item?.code;
        newcompCodehash["desc"] = item?.desc;
        newcompCodeArr.push(newcompCodehash);
      });

      dispatch(setDropDown({ keyName: "CompanyCode", data: newcompCodeArr }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${value
        ? value
        : rmSearchForm?.chartOfAccount?.code
          ? rmSearchForm?.chartOfAccount?.code
          : "ETCN"
      }`,
      "get",
      hSuccess,
      hError
    );
  };
  const getNewChartofAccounts = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_CUSTOM_DROPDOWN_LIST",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MODULE": "General Ledger",
          "MDG_CONDITIONS.MDG_FIELD_NAME": "Chart Of Accounts",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        const lookupData =
          data?.data?.result[0]?.MDG_CUSTOM_LOOKUP_ACTION_TYPE || [];

        let lookupDataArr = [];
        lookupData?.map((itemData) => {
          let lookupDataHash = {};
          lookupDataHash["code"] = itemData?.MDG_LOOKUP_CODE;
          lookupDataHash["desc"] = itemData?.MDG_LOOKUP_DESC;
          lookupDataArr.push(lookupDataHash);
        });

        const sortedDataasDesc = lookupDataArr.sort((a, b) =>
          a?.desc?.localeCompare(b?.desc)
        );

        dispatch(
          setDropDown({ keyName: "NewChartOfAccounts", data: sortedDataasDesc })
        );
      }
    };

    const hError = (error) => {
      customError(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
  const getGroupAccountNumberForSearch = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "GroupAccountNumberSearch", data: data.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGroupAccountNumber?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLAccountForSearch = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GLAccountForSearch", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLBasedOnCompanyCode?companyCode=${value}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleChartOfAccount = (e, value) => {
    if (true) {
      var tempChartOfAccount = value;

      let tempFilterData = {
        ...rmSearchForm,
        chartOfAccount: tempChartOfAccount,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getAccountGroupLookup(value?.code);
    getAccountGroupForSearch(value?.code);
    getCompanyCodeForSearch(value?.code);
    getTaxCategory(value?.code);
    getCreatedBy(value?.code);
    getGroupAccountNumberForSearch(value?.code);
  };
  const handleGlSearchFilter = (e) => {
    const value = e.target.value;
    setGlSearchField(value);

    let tempFilterData = {
      ...rmSearchForm,
      generalLedgerString: value,
    };

    dispatch(
      commonFilterUpdate({
        module: "GeneralLedger",
        filterData: tempFilterData,
      })
    );
  };

  const handleSelectAllOptions = (option) => {
    if (selectedValues[option]?.length === dynamicOptions[option]?.length) {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: [],
      }));
      setSelectedPresetValues((prev) => ({
        ...prev,
        [option]: [],
      }));
    } else {
      setSelectedValues((prev) => ({
        ...prev,
        [option]: dynamicOptions[option] ?? [],
      }));
    }
  };

  const handleSelectAllCompanyCodes = () => {
    if (selectedComanyCode.length === dropdownData?.CompanyCode.length) {
      setselectedComanyCode([]);
      setselectedPresetComanyCode([]);
    } else {
      setselectedComanyCode(dropdownData?.CompanyCode);
    }
  };

  const handleSelectAllGeneralLedger = () => {
    if (selectedGeneralLedger.length === dropdownData?.GLSearchData?.length) {
      setselectedGeneralLedger([]);
      setselectedPresetGeneralLedger([]);
    } else {
      setselectedGeneralLedger(dropdownData?.GLSearchData);
    }
  };

  const handleSelectAllShortTexts = () => {
    if (selectedShortText.length === dropdownData?.ShortTextSearchGL.length) {
      setselectedShortText([]);
      setselectedPresetShortText([]);
    } else {
      setselectedShortText(dropdownData?.ShortTextSearchGL);
    }
  };
  const handleSelectAllCreatedBy = () => {
    if (selectedCreatedBy.length === dropdownData?.CreatedBySearchGL?.length) {
      setselectedCreatedBy([]);
      setselectedPresetCreatedBy([]);
    } else {
      setselectedCreatedBy(dropdowndata?.CreatedBySearchGL);
    }
  };

  const handleSelectAllTaxCategory = () => {
    if (selectedTaxCategory.length === dropdownData?.TaxCategory.length) {
      setselectedTaxCategory([]);
      setselectedPresetTaxCategory([]);
    } else {
      setselectedTaxCategory(dropdownData?.TaxCategory);
    }
  };

  const handleSelectAllLongTexts = () => {
    if (selectedLongText.length === dropdownData?.GLAcctLongText.length) {
      setselectedLongText([]);
      setselectedPresetLongText([]);
    } else {
      setselectedLongText(dropdownData?.GLAcctLongText);
    }
  };

  const handleCompanyCode = (e, value) => {
    if (true) {
      var tempCompanyCode = value;

      let tempFilterData = {
        ...rmSearchForm,
        companyCode: tempCompanyCode,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getGLAccountForSearch(value?.code);
  };
  const handleGLAcctLongText = (e, value) => {
    if (true) {
      var tempGLAccountLongText = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAcctLongText: tempGLAccountLongText,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleBlockedForPostingInCOA = (e, value) => {
    if (e.target.value !== null) {
      var tempBlockedForPostingInCOA = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        blockedForPostingInCOA: tempBlockedForPostingInCOA,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getGLAccountForSearch(value?.code);
  };
  const handleBlockedForPostingInCompany = (e, value) => {
    if (e.target.value !== null) {
      var tempBlockedForPostingInCompany = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        blockedForPostingInCompany: tempBlockedForPostingInCompany,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getGLAccountForSearch(value?.code);
  };
  const handleOpenItemMgmtByLedgerGroup = (e, value) => {
    if (e.target.value !== null) {
      var tempOpenItemMgmtByLedgerGroup = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        openItemMgmtByLedgerGroup: tempOpenItemMgmtByLedgerGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleOpenItemManagement = (e, value) => {
    if (e.target.value !== null) {
      var tempOpenItemManagement = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        openItemManagement: tempOpenItemManagement,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handlePostingWithoutTaxAllowed = (e, value) => {
    if (e.target.value !== null) {
      var tempPostingWithoutTaxAllowed = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        postingWithoutTaxAllowed: tempPostingWithoutTaxAllowed,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
    getGLAccountForSearch(value?.code);
  };
  const handleGLAccount = (e, value) => {
    if (true) {
      var tempGlAccount = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccount: tempGlAccount,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleGlAccountType = (e, value) => {
    if (true) {
      var tempGlAccountType = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccountType: tempGlAccountType,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleShortText = (e, value) => {
    if (true) {
      var tempShortText = value;
      let tempFilterData = {
        ...rmSearchForm,
        shortText: tempShortText,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleAccountGroup = (e, value) => {
    if (true) {
      var tempAccountGroup = value;

      let tempFilterData = {
        ...rmSearchForm,
        accountGroup: tempAccountGroup,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleGLInputChange = (e) => {
    const inputValue = e.target.value;
    setCcInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getGeneralLedgerSearch(inputValue);
      }, 500);
      setTimerId(newTimerId);
    }
  };

  const handleLongTextInputChange = (e) => {
    const inputValue = e.target.value;
    setLongTextInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getLongText(inputValue);
      }, 500);
      setTimerId(newTimerId);
    }
  };

  const handleShortTextInputChange = (e) => {
    const inputValue = e.target.value;
    setShortTextInputValue(inputValue);
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getShortText(inputValue);
      }, 500);
      setTimerId(newTimerId);
    }
  };

  const handleCellClick = (params) => {
    const { id, field } = params;
    const row = rowsofCopy.find((row) => row.id === id);
    setCopiedData(row[field]);
  };

  const handlePaste = useCallback((event) => {
    const clipboardData = event.clipboardData || window.Clipboard;
    const pastedData = clipboardData.getData("Text");

    const newRows = pastedData
      .trim()
      .split("\n")
      .map((row, rowIndex) => {
        const values = row.split("\t");
        const rowData = { id: rowIndex + 1 };
        columnCopy.forEach((col, colIndex) => {
          rowData[col.field] = values[colIndex] || "";
        });
        return rowData;
      });

    setRowsofCopy(newRows);
  }, []);

  const handlePasteglAccountExtend = useCallback((event) => {

    const clipboardData = event.clipboardData || window.Clipboard;
    const pastedData = clipboardData.getData("Text");

    const newRows = pastedData
      .trim()
      .split("\n")
      .map((row, rowIndex) => {
        const values = row.split("\t");
        const rowData = { id: rowIndex + 1 }; 
        allColumnsinExtend.forEach((col, colIndex) => {
          rowData[col.field] = values[colIndex] || "";
        });
        return rowData;
      });

    setRmDataRowsExtend(newRows);
  }, []);


  useEffect(() => {
    document.addEventListener("paste", handlePaste);

    return () => {
      document.removeEventListener("paste", handlePaste);
    };
  }, [handlePaste]);

  useEffect(() => {
    document.addEventListener("paste", handlePasteglAccountExtend);

    return () => {
      document.removeEventListener("paste", handlePasteglAccountExtend);
    };
  }, [handlePasteglAccountExtend]);

  const handleCreatedOn = (e) => {
    if (e.target.value !== null) {
      var tempCreatedOn = e.target.value;

      let tempFilterData = {
        ...rmSearchForm,
        street: tempCreatedOn,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleFunctionalArea = (e, value) => {
    if (true) {
      var tempFunctionalArea = value;

      let tempFilterData = {
        ...rmSearchForm,
        functionalArea: tempFunctionalArea,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleGlAccountText = (e, value) => {
    if (true) {
      var tempGlAccountText = value;

      let tempFilterData = {
        ...rmSearchForm,
        glAccountText: tempGlAccountText,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleGroupAccountNumber = (e, value) => {
    if (true) {
      var tempGroupAccountNumber = value;

      let tempFilterData = {
        ...rmSearchForm,
        groupAccountNumber: tempGroupAccountNumber,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleAccountCurrency = (e, value) => {
    if (true) {
      var tempAccountCurrency = value;

      let tempFilterData = {
        ...rmSearchForm,
        accountCurrency: tempAccountCurrency,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const handleTaxCategory = (e, value) => {
    if (true) {
      var tempTaxCategory = value;

      let tempFilterData = {
        ...rmSearchForm,
        TaxCategory: tempTaxCategory,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };
  const uploadExcel = (file) => {
    setEnableDocumentUpload(false);

    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    if (handleMassModePC === "Change") {
      var uploadUrl = `/${destination_GeneralLedger_Mass}/massAction/getAllGLFromExcelWithLimitedFieldsForMassChange`;

      const hSuccess = (data) => {
        if (data.statusCode === 200) {
          setIsLoading(false);
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Create");

          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          setIsLoading(false);

        } else if (data.statusCode === 429) {
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            data?.message || "Too many requests. Please try again later."
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
          setBlurLoading(false);
          setLoaderMessage("");
        }
        handleClose();
      };
      const hError = (error) => {
        setBlurLoading(false);
        setLoaderMessage(false);
        setDialogTitle("Error");
        setMessageDialogMessage(error?.message);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");

        handleMessageDialogClickOpen();
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else if (handleMassModePC === "Extend") {
      var uploadUrl = `/${destination_GeneralLedger_Mass}/massAction/getAllGeneralLedgerFromExcelForMassExtend`;
      const hSuccess = (data) => {
        setIsLoading();
        if (data.statusCode === 200) {
          setEnableDocumentUpload(false);
          dispatch(setMultipleGLData(data?.body)); 
          setMessageDialogTitle("Extend");
          setMessageDialogMessage(`${file.name} has been Uploaded Succesfully`);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          handleSnackBarOpen();
          setMessageDialogExtra(true);
          setIsLoading(false);
          navigate(`/masterDataCockpitNew/generalLedger/createMultipleGL`);
        } else if (data.statusCode === 429) {
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            data?.message || "Too many requests. Please try again later."
          );
       
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Create");
          setsuccessMsg(false);
          setMessageDialogMessage("Creation Failed");
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        customError(error);
      };
      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    } else {
      var uploadUrl = `/${destination_GeneralLedger_Mass}/massAction/getAllGLFromExcelWithLimitedFields`;
      const hSuccess = (data) => {

        if (data.statusCode === 200) {
          setIsLoading(false);
          setBlurLoading(false);
          setLoaderMessage("");
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Create");

          const content = (
            <Typography component="div">
              <ul>
                <li>
                  Mass Upload Process has Started in the background. As soon as
                  the request ID is generated, you will receive a notification
                  and mail for it containing the new request ID number.
                </li>
                <li>
                  Then you can visit the Request Bench Tab and search for that
                  request ID and do further actions on it.
                </li>
                <li>
                  Note - All request IDs generated in the background would
                  initially have the status Draft.
                </li>
              </ul>
            </Typography>
          );
          handleMessageDialogClickOpen();
          setMessageDialogTitle("Header - Information");
          setMessageDialogMessage(content);
          setMessageDialogSeverity("success");
          setMessageDialogOK(false);
          setsuccessMsg(true);
          setMessageDialogExtra(true);
          setIsLoading(false);
        } else {
          setEnableDocumentUpload(false);
          setMessageDialogTitle("Error");
          setsuccessMsg(false);
          setMessageDialogMessage(
            "Upload failed. Incorrect template tab name, please recheck upload file"
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setBlurLoading(false);
          setLoaderMessage("");
          setIsLoading(false);
        }
        handleClose();
      };
      const hError = (error) => {
        setDialogTitle("Error");
        setBlurLoading(false);
        setLoaderMessage(false);
        setMessageDialogMessage(error?.message);
        setMessageDialogSeverity("danger");
        setMessageDialogOK(false);
        setMessageDialogExtra(true);
        setDialogOkText("OK");
        setBlurLoading(false);
        setLoaderMessage("");

        handleMessageDialogClickOpen();
      };

      doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
    }
  };

  const validationColumns = [
    {
      field: "coa",
      headerName: "Chart Of Account",
      editable: false,
      flex: 1,
    },
    {
      field: "accountType",
      headerName: "Account Type",
      editable: false,
      flex: 1,
    },
    {
      field: "accountGroup",
      headerName: "Account Group",
      editable: false,
      flex: 1,
    },
    {
      field: "subAccountGroup",
      headerName: "Sub Account Group",
      editable: false,
      flex: 1,
    },
    {
      field: "GLAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
    },
    {
      field: "rangeFrom",
      headerName: "Range From",
      editable: false,
      flex: 1,
    },
    {
      field: "rangeTo",
      headerName: "Range To",
      editable: false,
      flex: 1,
    },
    {
      field: "message",
      headerName: "Message",
      editable: false,
      flex: 1,
    },
  ];

  const validationColumnsOfGLAcocuntPresent = [
    {
      field: "coa",
      headerName: "Chart Of Account",
      editable: false,
      flex: 1,
    },
    {
      field: "glAccount",
      headerName: "GL Account",
      editable: false,
      flex: 1,
    },
    {
      field: "errmessageGlName",
      headerName: "Error",
      editable: false,
      flex: 1,
    },
  ];


  let dynamicDataApis = {
    "Account Group": `/${destination_GeneralLedger_Mass}/data/getSearchParamsAccGroup`,
    "G/L Account Type": `/${destination_GeneralLedger_Mass}/data/getSearchParamsGLAccType`,
    "Field Status Group": `/${destination_GeneralLedger_Mass}/data/getSearchParamsFieldStatusGrp`,
    "Recon Account for Acct Type": `/${destination_GeneralLedger_Mass}/data/getSearchParamsReconAccForAccType`,
   
  };

  const isOptionSelected = (option, dropdownOption) => {
    return selectedValues[option]?.some(
      (selectedOption) => selectedOption?.code === dropdownOption?.code
    );
  };

  const isCompanyCodeSelected = (option) => {
    return selectedComanyCode.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isGeneralLedgerSelected = (option) => {
    return selectedGeneralLedger.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isCreatedBySelected = (option) => {
    return selectedCreatedBy.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isTaxCategorySelected = (option) => {
    return selectedTaxCategory.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isLongTextSelected = (option) => {
    return selectedLongText.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const isShortTextSelected = (option) => {
    return selectedShortText.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const handleSelection = (event) => {
    const selectedItems = event.target.value;
    setSelectedOptions(selectedItems);
    setDisplayedFields([]);

    selectedItems.forEach(async (selectedItem) => {
      const apiEndpoint = dynamicDataApis[selectedItem];
      fetchOptionsForDynamicFilter(apiEndpoint, selectedItem);
    });
  };



  const handleAddFields = () => {
    const numSelected = selectedOptions.length;
    const newFields = Array.from({ length: numSelected }, (_, index) => ({
      id: index,
      value: "",
    }));
    setDisplayedFields(newFields);
  };

  const handleFieldChange = (fieldId, value) => {
    setDisplayedFields(
      (selectedOptions) => selectedOptions?.map((option) => option)
    );
  };
  // const items = [
  //   { title: "Account Group" },
  //   { title: "Short Text" },
  //   { title: "G/L Account Type" },
  //   { title: "Field Status Group" },
  //   { title: "Open Item Mgmt by Ledger Group" },
  //   { title: "Open Item Management" },
  //   { title: "Recon Account for Acct Type" },
  //   { title: "Created On" },
  //   { title: "Created By" },
  // ];
  const titleToFieldMapping = {
    "Account Group": "AccountGroup",
    "Short Text": "shortText",
    "G/L Account Type": "gLAccountType",
    "Field Status Group": "fieldStatusGroup",
    "Open Item Mgmt by Ledger Group": "openItemMgmtByLedgerGroup",
    "Open Item Management": "openItemManagement",
    "Recon Account for Acct Type": "reconAccountForAcctType",
    "Created On": "createdOn",
    "Created By": "createdBy",
  };
  const postingWithoutTaxValues = ["Allowed", "Not Allowed", ""];
  const openItemManagementByLedgerGroupValues = ["True", "False", ""];
  const openItemManagementValues = ["True", "False", ""];
  const blockedForPostingInCOAValues = ["Blocked", "Unblocked", ""];
  const blockedForPostingInCompanyValues = ["Blocked", "Unblocked", ""];
  const postAutoOnlyValues = ["True = X", "False", ""];

  const getGLControlData = () => {
    let viewName = "Control Data";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerControlData(data.body));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLCreateBankInterest = () => {
    let viewName = "Create/Bank/Interest";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerCreateBankIntrest(data.body));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLTypeDescription = () => {
    let viewName = "Type/Description";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerTypeDescription(data.body));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLInformation = () => {
    let viewName = "Information";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerInformation(data.body));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGLKeywordTranslation = () => {
    let viewName = "Keyword/Translation";
    const hSuccess = (data) => {
      dispatch(setgeneralLedgerKeywordTranslation(data.body));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getViewFieldDetails?viewName=${viewName}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getGLAccountType = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountType", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getTradingPartner = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TradingPartner", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTradingPartner`,
      "get",
      hSuccess,
      hError
    );
  };
  const getBusinessArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "BusinessArea", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getBusinessArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "FunctionalArea", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ProfitCenter", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getProfitCenter`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostingSheet = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CostingSheet", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getCostingSheet`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryOrRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CountryReg", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      "get",
      hSuccess,
      hError
    );
  };
  const getJurisdiction = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Jurisdiction", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getJurisdiction`,
      "get",
      hSuccess,
      hError
    );
  };
  const getRegion = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Region", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_CostCenter}/data/getRegion`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguageKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "LanguageKey", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getChartOfAccounts = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ChartOfAccounts", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsCOA`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGeneralLedgerSearch = (gl) => {
    setIsDropDownLoading(true);
    let payload = {
      glAccount: gl,
      coa: rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GLSearchData", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsGL`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getLongText = (longText) => {
    setIsDropDownLoading(true);
    let payload = {
      description: longText,
      coa: rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GLAcctLongText", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsLongText`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getShortText = (shortText) => {
    setIsDropDownLoading(true);
    let payload = {
      name: shortText,
      coa: rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "ShortTextSearchGL", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsShortText`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getCreatedBy = (value) => {
    setIsDropDownLoading(true);
    let payload = {
      coa: value ? value : rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CreatedBySearchGL", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsCreatedBy`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getReconAccountForAccountType = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ReconAccountForAccountType", data: data.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };
  const getSortKey = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "SortKey", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSortKey`,
      "get",
      hSuccess,
      hError
    );
  };
  const getPlanningLevel = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "PlanningLevel", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };
  const getInternalUOM = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "InternalUOM", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getInternalUOM`,
      "get",
      hSuccess,
      hError
    );
  };
  const getLanguage = () => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "Language", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getLanguage`,
      "get",
      hSuccess,
      hError
    );
  };

  const getInterestIndicator = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "InterestIndicator", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getInterestIndicator`,
      "get",
      hSuccess,
      hError
    );
  };
  const getInterestCalculationFrequency = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({
          keyName: "InterestCalculationFrequency",
          data: data.body,
        })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getInterestCalculationFreq`,
      "get",
      hSuccess,
      hError
    );
  };
  const getGlAccountBasedOnCompanyCode = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GlAccountCompCode", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLBasedOnCompanyCode?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAllGLAccountbasedOnChartOfAccount = (value) => {
    setChartOfAccountExtend(value?.code);
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "GlAccountForExtend", data: data?.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountByCOA?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {

  }, [inputValue, allOptions]);

  useEffect(() => {

  }, [selectedRows])

  useEffect(() => {
    getAllGLAccountbasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    getCompanyCodeForSearch();
    getTaxCategory();
    getCreatedBy();
    getBusinessArea();
    getFunctionalArea();
    getProfitCenter();
    getCostingSheet();
    getCountryOrRegion();
    getJurisdiction();
    getRegion();
    getLanguageKey();
    getGLControlData();
    getGLCreateBankInterest();
    getGLTypeDescription();
    getGLInformation();
    getGLKeywordTranslation();
    getTradingPartner();
    getChartOfAccounts();
    getReconAccountForAccountType();
    getSortKey();
    getPlanningLevel();
    getInternalUOM();
    getLanguage();
    getInterestIndicator();
    getInterestCalculationFrequency();
    dispatch(setTaskData({}));
    dispatch(clearGeneralLedger());
    getChangeTemplate();
    getRegulatedCompanyCode();
    getNewChartofAccounts();
    dispatch(clearTaskData());
    getCompanyCodeBasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    getAllGLAccountbasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    }); 
  }, []);

  const getRegulatedCompanyCode = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "RegulatedCompanyCode", data: data?.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getRegulatedCompanyCodes`,
      "post",
      hSuccess,
      hError
    );
  };
  const getChangeTemplate = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ET_CHNG_FIELD_SELECTION_DT",
      version: "v4",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "GENERAL LEDGER",
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData =
          data?.data?.result[0]?.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;
        setFieldselectionFromIdm(responseData);
        let fieldSelectionCompanyCode = [];
        let fieldSelectionCOA = [];
        let fieldSelectionTempBlock = [];
        responseData?.map((element, index) => {
          
          if (element.MDG_FIELD_SELECTION_LVL == "COMPANY CODE") {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCompanyCode.push(COHash);
          } else if (element.MDG_FIELD_SELECTION_LVL == "CHART OF ACCOUNT") {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionCOA.push(COAHash);
          } else {
            let COAHash = {};
            COAHash["id"] = index;
            COAHash["name"] = element.MDG_SELECT_OPTION;
            fieldSelectionTempBlock.push(COAHash);
          }
        });
       
        const uniqueNames = new Set();
        const distinctCompanyCodeData = fieldSelectionCompanyCode.filter(
          (obj) => {
            if (!uniqueNames.has(obj.name)) {
              uniqueNames.add(obj.name);
              return true;
            }
            return false;
          }
        );
        const uniqueNamesCOA = new Set();
        const distinctCartOfAccoutData = fieldSelectionCOA.filter((obj) => {
          if (!uniqueNamesCOA.has(obj.name)) {
            uniqueNamesCOA.add(obj.name);
            return true;
          }
          return false;
        });
        
        setDataList(distinctCompanyCodeData);
        setDataListCOA(distinctCartOfAccoutData);
        setDataListBlocked(fieldSelectionTempBlock);
      } else {
      }
      handleClose();
    };

    const hError = (error) => {
      customError(error);
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };

  const getAccountTypeFromIDMRule = (responseData) => {
   
    let accountTypeArr = [];
    responseData.map((item) => {
      let accountType_hash = {};
      accountType_hash["code"] = item.MDG_GL_ACCOUNT_TYPE_CODE;
      accountType_hash["desc"] = item.MDG_GL_ACCOUNT_TYPE;
      accountTypeArr.push(accountType_hash);
    });
    const result = Object.values(
      accountTypeArr.reduce((acc, obj) => ({ ...acc, [obj.desc]: obj }), {})
    );
    const sortedDatabyAlphabetic = result?.sort((a, b) =>
      a.code.localeCompare(b.code)
    );
    return sortedDatabyAlphabetic;
  };
  const getGlAccountRange = (compCodeType) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_GL_ACCOUNT_NO_RANGE_DT",
      version: "v5",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_SERIAL_NO": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    setIsLoading(true);
    const hSuccess = (data) => {
      setIsLoading(false);
      if (data.statusCode === 200) {
        let responseData = "";
        data?.data?.result[0]?.MDG_GL_ACCOUNT_NO_RANGE_ACTION_TYPE;
        if (compCodeType == "NonSunoco") {
          responseData =
            data?.data?.result[0]?.MDG_GL_ACCOUNT_NO_RANGE_ACTION_TYPE?.filter(
              (item) => item.MDG_IS_AVAIL_FOR_SUNOCO == "False"
            );
        } else {
          responseData =
            data?.data?.result[0]?.MDG_GL_ACCOUNT_NO_RANGE_ACTION_TYPE;
        }

        setGLAccountRangeData(responseData);

        let getAccountTypeFromIDM = getAccountTypeFromIDMRule(responseData);
        setAccountTypeWithCopy(getAccountTypeFromIDM);

      } else {
      }
      handleClose();
    };

    const hError = (error) => {
      customError(error);

    };

    if (applicationConfig.environment === "localhost") {
      doAjax(
        `/${destination_IDM}/rest/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    } else {
      doAjax(
        `/${destination_IDM}/v1/invoke-rules`,
        "post",
        hSuccess,
        hError,
        payload
      );
    }
  };
 
  const fetchOptionsForDynamicFilter = (apiEndpoint, selectedItem) => {
    setIsDropDownLoading(true);
    let payload = {
      coa: rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      const newOptions = data.body;
      setDynamicOptions((prev) => ({ ...prev, [selectedItem]: newOptions }));
      setIsDropDownLoading(false);
    };

    const hError = (error) => {
      customError(error);
    };
    doAjax(apiEndpoint, "post", hSuccess, hError, payload);
  };

  const clearSearchBar = () => {
    setMaterialNumber("");
  };

  const presentDate = new Date();
  const backDate = new Date();
  backDate.setDate(backDate.getDate() - 15);

  const [date, setDate] = useState([backDate, presentDate]);
  const [date1, setDate1] = useState([backDate, presentDate]);

  const handleDate = (e) => {
    if (e !== null) {
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: {
            ...rmSearchForm,
            createdOn: e,
          },
        })
      );
    }
  };
  const handleCreatedBy = (e, value) => {
    if (true) {
      var tempCreatedBy = value;

      let tempFilterData = {
        ...rmSearchForm,
        createdBy: tempCreatedBy,
      };
      dispatch(
        commonFilterUpdate({
          module: "GeneralLedger",
          filterData: tempFilterData,
        })
      );
    }
  };

  const handleDate1 = (e) => {
    if (e !== null) setDate1(e.reverse());
  };

  const handleSnackBarClickaccept = () => {
    setOpenSnackBaraccept(true);
  };

  const handleSnackBarCloseaccept = (event, reason) => {
    if (reason === "clickaway") {
      return;
    }

    setOpenSnackBaraccept(false);
  };

  const handleUserName = (e) => {
    setUserName(e.target.value);
  };
 
  const getFilter = (fetchSkip) => {
    setStatusOfSelectAllFirstData(false);

    setPage(0);
  
    setTableLoading(true);

   
    let payload = {
      glAccount: rmSearchForm?.["G/L Account"] ?? "",
      chartOfAccount: rmSearchForm?.chartOfAccount?.code
        ? rmSearchForm?.chartOfAccount?.code
        : "ETCN",
      postAutoOnly: "",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      taxCategory: rmSearchForm?.TaxCategory ?? "",
      glAcctLongText: rmSearchForm?.glAcctLongText ?? "",
      postingWithoutTaxAllowed:
        rmSearchForm?.postingWithoutTaxAllowed === "Allowed"
          ? "X"
          : rmSearchForm?.postingWithoutTaxAllowed === "Not Allowed"
            ? "Y"
            : "",
      blockedForPostingInCOA:
        rmSearchForm?.blockedForPostingInCOA === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCOA === "Unblocked"
            ? "Y"
            : "",
      blockedForPostingInCompany:
        rmSearchForm?.blockedForPostingInCompany === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCompany === "Unblocked"
          ? "Y"
          : "",
      accountGroup: rmSearchForm?.["Account Group"] ?? "",
      shortText: rmSearchForm?.shortText ?? "",
      glAccountType: rmSearchForm?.["G/L Account Type"] ?? "",
      fieldStatusGroup: rmSearchForm?.["Field Status Group"] ?? "",
      openItemMgmtbyLedgerGroup:
        rmSearchForm?.openItemMgmtByLedgerGroup === "True"
          ? "X"
          : rmSearchForm?.openItemMgmtByLedgerGroup === "False"
            ? "Y"
            : "",
      openItemManagement:
        rmSearchForm?.openItemManagement === "True"
          ? "X"
          : rmSearchForm?.openItemManagement === "False"
          ? "Y"
          : "",
      reconAccountforAcctType:
        rmSearchForm?.["Recon Account for Acct Type"] ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      top: 100,
      skip: 0,
    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let glAccountArr = [];

        data?.body?.list?.forEach((item) => {
          if (item?.COA == "ETCN") {
            let glAccountHash = {};
            glAccountHash["code"] = item?.GLAccount;
            glAccountHash["desc"] = item?.GLAccount;
            glAccountArr.push(glAccountHash);
          }
        });

        const uniqueArray = Object.values(
          glAccountArr.reduce((acc, item) => {
            acc[item.code] = item;
            return acc;
          }, {})
        );

        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body?.list[index];
          
          var tempRow = {
            id: uuidv4(),
            chartOfAccount: tempObj?.COA !== "" ? tempObj?.COA : "NA",
            glAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
            glAcctLongText:
              tempObj?.Description !== "" ? tempObj?.Description : "NA",
            AccountGroup:
              tempObj?.AccountGroup !== "" ? tempObj?.AccountGroup : "NA",
            companyCode: tempObj?.CompanyCode !== "" ? tempObj?.CompanyCode : "NA",
            glAccount: tempObj?.GLAccount !== "" ? tempObj?.GLAccount : "NA",
            groupAccountNumber:
              tempObj?.GroupAccNo !== "" ? tempObj?.GroupAccNo : "NA",
            postingWithoutTaxAllowed:
              tempObj?.Pstnwotax === "X" ? "Allowed" : "Not Allowed",
            blockedForPostingInCOA:
              tempObj?.PostingBlockedCOA === "X" ? "Blocked" : "Unblocked",
            blockedForPostingInCompany:
              tempObj?.PostingBlockedCoCd === "X" ? "Blocked" : "Unblocked",
            postAutoOnly: tempObj?.PostAuto === "X" ? "X" : "",
            taxCategory:
              tempObj.Taxcategory !== "" ? tempObj?.Taxcategory : "NA",
            createdBy: tempObj?.CreatedBy !== "" ? tempObj?.CreatedBy : "NA",
            shortText: tempObj?.GLname !== "" ? tempObj?.GLname : "NA",
            gLAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
            fieldStatusGroup:
              tempObj?.FieldStsGrp !== "" ? tempObj?.FieldStsGrp : "NA",
            openItemManagement:
              tempObj?.Openitmmanage !== "" ? tempObj?.Openitmmanage : "",
            openItemMgmtByLedgerGroup:
              tempObj?.OIMgmtByLedgerGrp !== ""
                ? tempObj?.OIMgmtByLedgerGrp
                : "",
            reconAccountForAcctType:
              tempObj?.ReconAcc !== "" ? tempObj?.ReconAcc : "NA",
            createdOn:
              tempObj?.CreatedOn !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "NA",
            
          };
          rows.push(tempRow);
         
        }
        
        rows.sort(
          (a, b) =>
            moment(a.createdOn, "DD MMM YYYY HH:mm") -
            moment(b.createdOn, "DD MMM YYYY HH:mm")
        );
        setRmDataRows(rows);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getFilterBasedOnPagination = (fetchSkip) => {
  

    setTableLoading(true);
 
    let payload = {
      glAccount: rmSearchForm?.["G/L Account"] ?? "",
      chartOfAccount: rmSearchForm?.chartOfAccount?.code
        ? rmSearchForm?.chartOfAccount?.code
        : "ETCN",
      companyCode: rmSearchForm?.["Company Code"] ?? "",
      postAutoOnly: "",
      taxCategory: rmSearchForm?.TaxCategory ?? "",
      glAcctLongText: rmSearchForm?.glAcctLongText ?? "",
      postingWithoutTaxAllowed:
        rmSearchForm?.postingWithoutTaxAllowed === "Allowed"
          ? "X"
          : rmSearchForm?.postingWithoutTaxAllowed === "Not Allowed"
            ? "Y"
            : "",
      blockedForPostingInCOA:
        rmSearchForm?.blockedForPostingInCOA === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCOA === "Unblocked"
            ? "Y"
            : "",
      blockedForPostingInCompany:
        rmSearchForm?.blockedForPostingInCompany === "Blocked"
          ? "X"
          : rmSearchForm?.blockedForPostingInCompany === "Unblocked"
          ? "Y"
          : "",
      accountGroup: rmSearchForm?.["Account Group"] ?? "",
      shortText: rmSearchForm?.shortText ?? "",
      glAccountType: rmSearchForm?.["G/L Account Type"] ?? "",
      fieldStatusGroup: rmSearchForm?.["Field Status Group"] ?? "",
      openItemMgmtbyLedgerGroup:
        rmSearchForm?.openItemMgmtByLedgerGroup === "True"
          ? "X"
          : rmSearchForm?.openItemMgmtByLedgerGroup === "False"
            ? "Y"
            : "",
      openItemManagement:
        rmSearchForm?.openItemManagement === "True"
          ? "X"
          : rmSearchForm?.openItemManagement === "False"
          ? "Y"
          : "",
      reconAccountforAcctType:
        rmSearchForm?.["Recon Account for Acct Type"] ?? "",
      fromDate:
        moment(rmSearchForm?.createdOn[0]).format("YYYY-MM-DDT00:00:00") ?? "",
      toDate:
        moment(rmSearchForm?.createdOn[1]).format("YYYY-MM-DDT00:00:00") ?? "",
      createdBy: rmSearchForm?.createdBy ?? "",
      top: "100",
      skip: rmDataRows?.length ?? 0,

    };
    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        let glAccountArr = [];

        data?.body?.list?.forEach((item) => {
          if (item?.COA == "ETCN") {
            let glAccountHash = {};
            glAccountHash["code"] = item?.GLAccount;
            glAccountHash["desc"] = item?.GLAccount;
            glAccountArr.push(glAccountHash);
          }
        });
        const uniqueArray = Object.values(
          glAccountArr.reduce((acc, item) => {
            acc[item.code] = item;
            return acc;
          }, {})
        );
        var rows = [];
        for (let index = 0; index < data?.body?.list?.length; index++) {
          var tempObj = data?.body?.list[index];
        
          var tempRow = {
            id: uuidv4(),
            chartOfAccount: tempObj?.COA !== "" ? tempObj?.COA : "NA",
            glAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
            glAcctLongText:
              tempObj?.Description !== "" ? tempObj?.Description : "NA",
            AccountGroup:
              tempObj?.AccountGroup !== "" ? tempObj?.AccountGroup : "NA",
            companyCode: tempObj?.CompanyCode !== "" ? tempObj?.CompanyCode : "NA",
            glAccount: tempObj?.GLAccount !== "" ? tempObj?.GLAccount : "NA",
            groupAccountNumber:
              tempObj?.GroupAccNo !== "" ? tempObj?.GroupAccNo : "NA",
            postingWithoutTaxAllowed:
              tempObj?.Pstnwotax === "X" ? "Allowed" : "Not Allowed",
            blockedForPostingInCOA:
              tempObj?.PostingBlockedCOA === "X" ? "Blocked" : "Unblocked",
            blockedForPostingInCompany:
              tempObj?.PostingBlockedCoCd === "X" ? "Blocked" : "Unblocked",
            postAutoOnly: tempObj?.PostAuto === "X" ? "X" : "",
            taxCategory:
              tempObj.Taxcategory !== "" ? tempObj?.Taxcategory : "NA",
            createdBy: tempObj?.CreatedBy !== "" ? tempObj?.CreatedBy : "NA",
            shortText: tempObj?.GLname !== "" ? tempObj?.GLname : "NA",
            gLAccountType:
              tempObj?.Accounttype !== "" ? tempObj?.Accounttype : "NA",
            fieldStatusGroup:
              tempObj?.FieldStsGrp !== "" ? tempObj?.FieldStsGrp : "NA",
            openItemManagement:
              tempObj?.Openitmmanage !== "" ? tempObj?.Openitmmanage : "NA",
            openItemMgmtByLedgerGroup:
              tempObj?.OIMgmtByLedgerGrp !== ""
                ? tempObj?.OIMgmtByLedgerGrp
                : "NA",
            reconAccountForAcctType:
              tempObj?.ReconAcc !== "" ? tempObj?.ReconAcc : "NA",
            createdOn:
              tempObj?.CreatedOn !== ""
                ? `${moment(tempObj["CreatedOn"]).format("DD MMM YYYY")}`
                : "NA",
         
          };
          rows.push(tempRow);
   
        }
        rows.sort(
          (a, b) =>
            moment(a.createdOn, "DD MMM YYYY HH:mm") -
            moment(b.createdOn, "DD MMM YYYY HH:mm")
        );
        setRmDataRows((prevRows) => [...prevRows, ...rows]);
        setTableLoading(false);
        setroCount(rows.length);
        setCount(data?.body?.count);
      } else if (data.statusCode === 400) {
        setSearchDialogTitle("Warning");
        setSearchDialogMessage(
          "Please Select Lesser Fields as the URL is getting too long !!"
        );
        handleSearchDialogClickOpen();
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersBasedOnAdditionalParams`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const [userList, setUserList] = useState([]);


  useEffect(() => {
  }, [dataList, dataListCOA]);
  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "company",
      filterTitle: "Company",
    },
    {
      type: "singleSelect",
      filterName: "vendor",
      filterData: vendorDetailsSet,
      filterTitle: "Supplier",
    },
    {
      type: "text",
      filterName: "poNum",
      filterTitle: "PO Number",
    },
    {
      type: "autoComplete",
      filterName: "createdBy",
      filterData: userList,
      filterTitle: "Created By",
    },
    {
      type: "singleSelectKV",
      filterName: "returnType",
      filterData: {
        "Debit Note": "Debit Note",
        Replacement: "Replacement",
      },
      filterTitle: "Return Type",
    },
    {
      type: "singleSelect",
      filterName: "plant",
      filterData: plantCodeSet,
      filterTitle: "Plant",
    },
    {
      type: "dateRange",
      filterName: "createdOn",
      filterTitle: "Return Request Date",
    },
  ];
  const [poHeader, setPoHeader] = useState(null);
  const [roCount, setroCount] = useState(0);
  const [showBtmNav, setShowBtmNav] = useState(false);
  const [opendialog, setOpendialog] = useState(false);
  const [openSnackbarDialog, setOpenSnackbarDialog] = useState(false);
  const [opendialog2, setOpendialog2] = useState(false);
  const [opendialog3, setOpendialog3] = useState(false);
  const [openforwarddialog, setOpenforwarddialog] = useState(false);
  const [rejectInputText, setRejectInputText] = useState("");
  const [acceptInputText, setAcceptInputText] = useState("");
  const [openMessageDialog, setOpenMessageDialog] = useState(false);
  const [messageDialogTitle, setMessageDialogTitle] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const handleSnackbarClose = () => {
    setopenSnackbar(false);
  };

  const handleReject = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };
  const handleAccept = () => {
    setMessageDialogTitle("Success");
    setMessageDialogMessage("Comment Posted");
    setMessageDialogSeverity("success");
    handleMessageDialogClickOpen();
  };

  const handleMessageDialogClickOpen = () => {
    setOpenMessageDialog(true);
  };

  const handleMessageDialogClose = () => {
    setOpenMessageDialog(false);
    setMessageDialogSeverity("");
    setMessageDialogMessage("");
    setMessageDialogTitle("");
  };

  const handleSearchDialogClickOpen = () => {
    setOpenSearchDialog(true);
  };

  const handleSearchDialogClose = () => {
    setOpenSearchDialog(false);
  };

  const [anchorEl_Preset, setAnchorEl] = useState(null);
  const openAnchor = Boolean(anchorEl_Preset);

  const handleClose_Preset = () => {
    setPresetName("");
    setAnchorEl(null);
  };

  const [presets, setPresets] = useState(null);
  const [presetName, setPresetName] = useState(null);

  const handleClear = () => {
   
    dispatch(commonFilterClear({ module: "GeneralLedger" }));
    setFilterFieldData((prevState) => {
      const updatedState = { ...prevState };
      Object.keys(updatedState).forEach((key) => {
        Object.keys(updatedState[key]).forEach((innerKey) => {
          updatedState[key][innerKey] = ""; 
        });
      });
      return updatedState;
    });

    setselectedComanyCode([]);
    setselectedGeneralLedger([]);
    setselectedLongText([]);
    setselectedShortText([]);
    setselectedTaxCategory([]);
    setselectedCreatedBy([]);
    setSelectedValues({});

    setselectedPresetComanyCode([]);
    setselectedPresetGeneralLedger([]);
    setselectedPresetLongText([]);
    setselectedPresetShortText([]);
    setselectedPresetTaxCategory([]);
    setselectedPresetCreatedBy([]);
    setSelectedPresetValues({});
  };
  const onRowsSelectionHandler = (ids) => {
    const selectedRowsData = ids?.map((id) =>
      rmDataRows.find((row) => row.id === id)
    );
    var compCodes = selectedRowsData?.map((row) => row.company);
    var companySet = new Set(compCodes);
    var vendors = selectedRowsData?.map((row) => row.vendor);
    var vendorSet = new Set(vendors);
    var paymentTerms = selectedRowsData?.map((row) => row.paymentTerm);
    var paymentTermsSet = new Set(paymentTerms);
    if (selectedRowsData.length > 0) {
      if (companySet.size === 1) {
        if (vendorSet.size === 1) {
          if (paymentTermsSet.size !== 1) {
            setDisableButton(true);
            setMessageDialogTitle("Error");
            setMessageDialogMessage(
              "Invoice cannot be generated for vendors with different payment terms"
            );
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          } else setDisableButton(false);
        } else {
          setDisableButton(true);
          setMessageDialogTitle("Error");
          setMessageDialogMessage(
            "Invoice cannot be generated for multiple suppliers"
          );
          setMessageDialogSeverity("danger");
          handleMessageDialogClickOpen();
        }
      } else {
        setDisableButton(true);
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          "Invoice cannot be generated for multiple companies"
        );
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    } else {
      setDisableButton(true); 
    }
    setSelectedRow(ids); 
    setSelectedDetails(selectedRowsData);
  };
  function refreshPage() {
    dispatch(commonFilterClear({ module: "GeneralLedger" }));
    getFilter();
  }

  const [company, setCompany] = useState([]);
  const [Companyid, setCompanyid] = useState([]);
  const [open, setOpen] = useState(false);
  const [matAnchorEl, setMatAnchorEl] = useState(null);
  const [materialDetails, setMaterialDetails] = useState(null);
  const [itemDataRows, setItemDataRows] = useState([]);

  const matOpen = Boolean(matAnchorEl);
  const popperId = matOpen ? "simple-popper" : undefined;

  const handleClickOpen = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const [poNum, setPONum] = useState(null);
  const fetchPOHeader = (id) => {
    var formData = new FormData();
    if (id) formData.append("extReturnId", id);
    const hSuccess = (data) => {
      if (data) {
        setPoHeader(data);
        setPONum(data[0]["poNumber"] ?? "");
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_Returns}/returnsHeader/getReturnsPreview`,
      "postformdata",
      hSuccess,
      hError,
      formData
    );
  };
  
  const [id, setID] = useState("");
  const createMultiValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: displayName,
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const values = params.value ? params.value.split(",").map(m => m.trim()) : [];
      const displayCount = values.length - 1;

      if (values.length === 0) return "-";

      const formatText = (text) => {
        const [code, ...rest] = text.split('-');
        return (
          <>
            <strong>{code}</strong>{rest.length ? ` - ${rest.join('-')}` : ''}
          </>
        );
      };

      return (
        <Box sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          minWidth: 0
        }}>
          <Tooltip
            title={values[0]}
            placement="top"
            arrow
          >
            <Typography
              variant="body2"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                flex: 1,
                minWidth: 0,
              }}
            >
              {formatText(values[0])}
            </Typography>
          </Tooltip>
          {displayCount > 0 && (
            <Box sx={{
              display: "flex",
              alignItems: "center",
              ml: 1,
              flexShrink: 0
            }}>
              <Tooltip
                arrow
                placement="right"
                title={
                  <Box sx={{ p: 1, maxHeight: 200, overflowY: "auto" }}>
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      Additional {displayName}s ({displayCount})
                    </Typography>
                    {values.slice(1).map((value, idx) => (
                      <Typography key={idx} variant="body2" sx={{ mb: 0.5 }}>
                        {formatText(value)}
                      </Typography>
                    ))}
                  </Box>
                }
              >
                <Box sx={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer"
                }}>
                  <InfoIcon
                    sx={{
                      fontSize: "1rem",
                      color: "primary.main",
                      "&:hover": { color: "primary.dark" }
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      ml: 0.5,
                      color: "primary.main",
                      fontSize: "11px"
                    }}
                  >
                    +{displayCount}
                  </Typography>
                </Box>
              </Tooltip>
            </Box>
          )}
        </Box>
      );
    }
  });
  const createSingleValueCell = (fieldName, displayName) => ({
    field: fieldName,
    headerName: displayName,
    editable: false,
    flex: 1,
    renderCell: (params) => {
      const [firstPart, ...rest] = params.value?.split(" - ") || [];
      return (
        <span style={{ flex: 1, wordBreak: 'break-word', whiteSpace: 'normal' }}>
          <strong>{firstPart}</strong> {rest.length ? `- ${rest.join(" - ")}` : ""}
        </span>
      );
    },
  });
  const displayCell = () => (
    {
      field: "dataValidation",
      headerName: t("Audit History"),
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const handleChangelogClick = (event) => {
          event.stopPropagation();
          navigate(APP_END_POINTS?.AUDIT_LOG, {
          state: {
              materialNumber: params.row.glAccount,
              module: MODULE_MAP?.GL
            }
          });
        };
  
        return (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <Tooltip title="View Audit Log" placement="top">
              <IconButton
                onClick={handleChangelogClick}
                size="small"
                sx={{
                  color: 'primary.main',
                  marginLeft:'20px',
                  '&:hover': {
                    color: 'primary.dark',
                    backgroundColor: 'rgba(25, 118, 210, 0.04)',
                    transform: 'scale(1.05)',
                    marginLeft:'20px'
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <HistoryIcon sx={{ fontSize: '1.5rem' }} />
              </IconButton>
            </Tooltip>
          </Box>
        );
      }
    }
  );
  const fetchMasterDataColumns = () => {
    console.log("Error")
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_COLUMN,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": "US",
          "MDG_CONDITIONS.MDG_MODULE": "General Ledger",
          "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE": "Master Data",
          "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
        },
      ],
    };
    getMasterDataColumn(payload);
  };
  const fetchSearchParameterFromDt = () => {
    let payload = {
          decisionTableId: null,
          decisionTableName: DECISION_TABLE_NAME.MDG_MAT_SEARCHSCREEN_PARAMETER,
          version: "v2",
          conditions: [
            {
              "MDG_CONDITIONS.MDG_MAT_REGION":"US",
              "MDG_CONDITIONS.MDG_MODULE":"General Ledger",
              "MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data",
              "MDG_CONDITIONS.MDG_MAT_VIEW_TYPE": "NA",
            },
          ],
        };
        getSearchParams(payload);
  }
  const columns = [
    {
      field: "chartOfAccount",
      headerName: "Chart Of Account",
      editable: false, 
      flex: 1,
    },
    {
      field: "compCode",
      headerName: "Company Code",
      editable: false, 
      flex: 1,
    },
    {
      field: "glAccount",
      headerName: "G/L Account",
      editable: false, 
      flex: 1,
    },
    {
      field: "glAcctLongText",
      headerName: "Long Text",
      editable: false, 
      flex: 1,
    },
    
    {
      field: "TaxCategory",
      headerName: "Tax Category",
      editable: false, 
      flex: 1,
    },
    {
      field: "postingWithoutTaxAllowed",
      headerName: "Posting Without Tax Allowed",
      editable: false, 
      flex: 1,
    },
    {
      field: "PostAutomaticallyOnly",
      headerName: "Post Automatically Only",
      editable: false, 
      flex: 1,
    },
    {
      field: "blockedForPostingInCOA",
      headerName: "Blocked For Posting In COA",
      editable: false,
      flex: 1,
    },
    {
      field: "blockedForPostingInCompany",
      headerName: "Blocked For Posting In Company", 
      editable: false,
      flex: 1,
    },
  ];
  const actionColumn = [
    {
      field: "actions",
      align: "center",
      flex: 1, 
      headerAlign: "center",
      headerName: "Actions",
      sortable: false,
      renderCell: (params) => (
        <div>
         
          <Tooltip title="Change">
            <IconButton
              aria-label="View Metadata"
              onClick={() => handleOpenDialog(params.row)} 
            >
              <EditOutlinedIcon />
            </IconButton>
          </Tooltip>
        </div>
      ),
    },
  ];
  const dynamicFilterColumns = selectedOptions
    ?.map((option) => {
      const field = titleToFieldMapping[option]; 
      if (!field) {
        return null; 
      }
      return {
        field: field, 
        headerName: option,
        editable: false,
        flex: 1,
      };
    })
    .filter((column) => column !== null); 

  const allColumns = [
    ...columns,
    ...dynamicFilterColumns,
    ...actionColumn,
  ];
  const capitalize = (str) => {
    const arr = str.split(" ");
    for (var i = 0; i < arr.length; i++) {
      arr[i] = arr[i].charAt(0) + arr[i]?.slice(1).toLowerCase();
    }

    const str2 = arr.join(" ");
    return str2;
  };
  const createMasterDataColums = (data) => {
    const columns = [];
    let sortedData = data?.sort(
      (a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO
    ) || [];
    if (sortedData) {
      sortedData?.forEach((item) => {
        if (item?.MDG_MAT_VISIBILITY === VISIBILITY_TYPE.DISPLAY) {

          if (item?.MDG_MAT_UI_FIELD_NAME) {
            const fieldName = item.MDG_MAT_JSON_FIELD_NAME;
            const headerName = item.MDG_MAT_UI_FIELD_NAME;

            if(fieldName==="DataValidation"){
              columns.push(displayCell());
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Multiple") {
              columns.push(createMultiValueCell(fieldName, headerName));
            }
            else if (item.MDG_MAT_FIELD_TYPE === "Single") {
              columns.push(createSingleValueCell(fieldName, headerName));
            }
          }
        }
      });
    }
    return columns;
  }
  useEffect(() => {
    if (masterDataDtResponse) {
      const columnsGlobal = createMasterDataColums(masterDataDtResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);
      setDynamicColumns(columnsGlobal);
      console.log("col",columnsGlobal);

    }
    if(dtSearchParamsResponse){
          const response = dtSearchParamsResponse?.result?.[0]?.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE;
          const additionalData = response?.filter((item) => {
            return item.MDG_MAT_FILTER_TYPE === "Additional";
          }).map((item) => {
            return { title: t(item.MDG_MAT_UI_FIELD_NAME) };
          });
          setSearchParameters(response);
          console.log("res",response,additionalData);
          setItem(additionalData);
        }
  }, [masterDataDtResponse,dtSearchParamsResponse]);

  useEffect(() => {
    getFilter();
  }, []);

  useEffect(() => {
    fetchMasterDataColumns();
    fetchSearchParameterFromDt();
  },[]);


  let ref_elementForExport = useRef(null);
  let exportAsPicture = () => {
    setTimeout(() => {
      captureScreenShot("Material-Single");
    }, 100);
  };

  const handleDownloadTemplate = async () => {
    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        compCode: x.compCode,
        glAccount: x.glAccount,
      };
    });
    let hSuccess = (response) => {
      handleCloseDialog();
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute("download", `GeneraL Ledger_Mass Change.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `General Ledger Mass Change has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      downloadPayload
    );
  };
  const handleCreateMultiple = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Create");
    dispatch(setHandleMassMode("Create"));
  };
  const handleChangeMultipleExtend = () => {
    setEnableDocumentUpload(true);
    setHandleMassMode("Extend");
    dispatch(setHandleMassMode("Extend"));
  };

  const handleClick = (option, index) => {
    if (index !== 0) {
      setSelectedIndex(index);
      setOpenButton(false);
      if (index === 1) {
        handleCreateMultiple();
      } else if (index === 2) {
        setOpenDownloadDialog(true);
      } else if (index === 3) {
        setOpenDownloadDialog(true);
      }
    }
  };

  const handleOpenPopUpTableHandson = () => {
    setOpenDialogHandsonDialog(true);
  };

  const handleCloseHandsonDialog = () => {
    setOpenDialogHandsonDialog(false);
  };

  const handleApplyHandsOn = () => {
    let validedEachGLRowsArePerfectOrNot = getGlRowsValidation();
    if (validedEachGLRowsArePerfectOrNot.length === 0) {
      setBlurLoading(true);
      let duplicateCheckPayload = [];

      rowsofCopy?.map((x) => {
        var idk = {
          glAccount: x?.glAccountNumberCreated,
          coa: x?.chartOfAccount.toUpperCase(),
        };
        duplicateCheckPayload.push(idk);
      });

      const hDuplicateCheckSuccess = (data) => {
        setBlurLoading(false);
        if (data.body.length === 0) {
          let CompanyCodeWithCopyAndGlAccountWithCopyData =
            getCompanyCodeAndGlAccountWithCopy();
          let distinctData = Object.values(
            CompanyCodeWithCopyAndGlAccountWithCopyData.reduce((acc, cur) => {
              acc[cur.id] = cur;
              return acc;
            }, {})
          );
          downloadExcelWithCopyData(distinctData);

        } else {         
          const directMatches = data.body?.map((item) => item.glAccount);
          setDirectmatchmassGLWithCopy(data.body);
          setBlurLoading(false);
          setMessageDialogTitle("Duplicate Check");
          setsuccessMsg(false);
          setMessageDialogMessage(
            `There is a direct match for foowing Gl Account
                ${data.body?.map((item) => item.glAccount).join(",")}`
          );
          setMessageDialogSeverity("danger");
          setMessageDialogOK(false);
          setMessageDialogExtra(true);
          handleMessageDialogClickOpen();
          setSubmitForReviewDisabled(true);
        }
      };

      const hDuplicateCheckError = (error) => {

      };
      doAjax(
        `/${destination_GeneralLedger_Mass}/alter/fetchGlAccountNCoaDupliChkMass`,
        "post",
        hDuplicateCheckSuccess,
        hDuplicateCheckError,
        duplicateCheckPayload
      );
    } else {
      const glAccountNumbers = validedEachGLRowsArePerfectOrNot
        .map((obj) => obj.glAccountNumberCreated)
        .join(",");
      setBlurLoading(false);
      setMessageDialogTitle("Duplicate Check");
      setsuccessMsg(false);
      setMessageDialogMessage(
        `Please Review Following glAccountNumbers  ${glAccountNumbers} , Something is Wrong In That`
      );
      setMessageDialogSeverity("danger");
      setMessageDialogOK(false);
      setMessageDialogExtra(true);
      handleMessageDialogClickOpen();
      setSubmitForReviewDisabled(true);
    }
  };

  const downloadExcelWithCopyData = async (payload) => {
    let hSuccess = (response) => {
      setIsLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `GeneraL Ledger_Mass Create With Copy.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `GeneraL Ledger_Mass Create with Copy.xlsx has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelForMassCreateWithCopy`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const getCompanyCodeAndGlAccountWithCopy = () => {
    const glAccountAndCompanyCodeCopyFrom = [];

    rowsofCopy.forEach((firstObj, index) => {
      let newhash = {};
      glAccountRangeData.forEach((secondObj) => {
        if (
          firstObj.accountType === secondObj.MDG_GL_ACCOUNT_TYPE_CODE &&
          firstObj.accountGroup === secondObj.MDG_GL_ACCOUNT_GRP_CODE &&
          firstObj.subAccountGroup === secondObj.MDG_GL_SUB_ACCOUNT_GRP
        ) {
          newhash["id"] = index;
          newhash["chartOfAccount"] = "ETCN";
          (newhash["accountType"] = firstObj?.accountType),
            (newhash["accountGroup"] = firstObj?.accountGroup);
          (newhash["subAccountGroup"] = firstObj?.subAccountGroup),
            (newhash["glAccountNumberCreated"] =
              firstObj?.glAccountNumberCreated),
            (newhash["compCodeCreated"] = firstObj?.compCodeCreated),
            (newhash["glAccountNumberCopyFrom"] =
              secondObj?.MDG_GL_GLACCOUNT_NO);
          newhash["compCodeCopyFrom"] = secondObj?.MDG_GL_COMPANY_CODE;
          glAccountAndCompanyCodeCopyFrom.push(newhash);
        }
      });
    });
    return glAccountAndCompanyCodeCopyFrom;
  };

  const isOptionSelectedMassExtend = (option) => {
    return selectedglAccountForMassExtend?.some(
      (selectedOption) => selectedOption?.code === option?.code
    );
  };

  const getGlRowsValidation = () => {
    const nonMatches = [];

    rowsofCopy.forEach((firstObj) => {
      let matched = false;

      for (const secondObj of glAccountRangeData) {
        if (
          firstObj.accountType === secondObj.MDG_GL_ACCOUNT_TYPE_CODE &&
          firstObj.accountGroup === secondObj.MDG_GL_ACCOUNT_GRP_CODE &&
          firstObj.subAccountGroup === secondObj.MDG_GL_SUB_ACCOUNT_GRP
        ) {
          const glAccountNumberCreated = parseInt(
            firstObj.glAccountNumberCreated,
            10
          );
          const MDG_GL_L1_RANGE = parseInt(secondObj.MDG_GL_L1_RANGE, 10);
          const MDG_GL_L2_RANGE = parseInt(secondObj.MDG_GL_L2_RANGE, 10);

          if (
            glAccountNumberCreated >= MDG_GL_L1_RANGE &&
            glAccountNumberCreated <= MDG_GL_L2_RANGE
          ) {
            matched = true;
            break;
          }
        }
      }

      if (!matched) {
        nonMatches.push(firstObj);
      }
    });

    setNoMatchesRow(nonMatches);

    return nonMatches;
  };
  const handleSelectedColumn = () => {
    dispatch(setFields(selectedFieldsforCCSunSlice));
    setIsLoading(true);
    setIsCheckboxSelected(false);
    handleChangeDownload();
  };
  const handleClickChange = (option, index) => {
    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChange(false);

      if (index === 1) {
        handleChangeMultiple();
      } else if (index === 2) {
        setDownloadMultiple(true);
        setOpenDialog(true);
      
      }
    }
  };

  const handleClickChangeExtend = (option, index) => {
    setSelectedRowsExtend(selectedMassChangeRowData[0]);
    setSelectedRowsExtendData(selectedMassChangeRowData[0]);
   
    let extendArrayForUpdate = [];
    selectedMassChangeRowData?.map((item, index) => {
      let extendHashForUpdate = {};

      {
        extendHashForUpdate["id"] = index;
        (extendHashForUpdate["compCode"] = item?.compCode),
          (extendHashForUpdate["glAccount"] = item.glAccount),
          (extendHashForUpdate["coCodeToExtend"] = []);
      }
      extendArrayForUpdate.push(extendHashForUpdate);
    });

    dispatch(setEditMultipleGlExtend(extendArrayForUpdate));

    if (index !== 0) {
      setSelectedIndexChange(index);
      setOpenButtonChangeExtend(false);

      if (index === 1) {
        handleChangeMultipleExtend();
      } else if (index === 2) {
        if (selectedRows.length > 0) {
          setDownloadMultiple(true);
          setOpenDialogChangeExtend(true);
        } else {
        }
      }
    }
  };

  const handleChangeMultiple = () => {
    setEnableDocumentUpload(true);
    dispatch(setHandleMassMode("Change"));
  };
  const handleDownloadCreate = async () => {
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Group",
      "Business Segment",
      "Tax Field Check",
    ];
    let downloadPayloadHash = {};

    downloadPayloadHash["headers"] = selectedItemArr;
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    let hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        setBlurLoading(false);
        setLoaderMessage("");
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        link.href = href;
        link.setAttribute("download", `General Ledger Mass Create.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");
        setMessageDialogMessage(
          `General Ledger Mass Create.xlsx has been downloaded successfully`
        );
        setMessageDialogSeverity("success");
      } else {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
      }
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcel`,
      "getblobfile",
      hSuccess,
      hError
    );
  };
  const handleEmailDownload = async () => {
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Group",
      "Business Segment",
      "Tax Field Check",
    ];
    let downloadPayloadHash = {};

    downloadPayloadHash["headers"] = selectedItemArr;
    
    setBlurLoading(true);
    let hSuccess = (response) => {
      setBlurLoading(false);
      setLoaderMessage("");
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(`Download has been started. You will get the Excel file via email`);
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelInMail`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleChangeDownloadEmpty = () => {
    setIsLoading(true);
    let downloadPayloadHashTemporaryBlock = {}
    downloadPayloadHashTemporaryBlock["GlAccount"] = [];
    downloadPayloadHashTemporaryBlock["templateName"] = checkElement(
      selectedOptionBlocked
    );

    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        compCode: x?.compCode,
        glAccount: x?.glAccount,
        coa: x?.chartOfAccount,
      };
    });
    let downloadPayloadHash = {};
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Type",
      "Account Group",
      "Business Segment",
      "OIM/CSL Indicator",
      "Tax Field Check",
    ];
    let filterDataWithSelectedData = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else {
      selectedListItemsCOA.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    }
    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    downloadPayloadHash["GlAccount"] = [];
    downloadPayloadHash["headers"] = selectedItemArr;
    downloadPayloadHash["templateHeaders"] = "";
    downloadPayloadHash["templateName"] = "";
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    let hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        setBlurLoading(false);
        setLoaderMessage("");
        setIsLoading(false);
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");

        link.href = href;
        link.setAttribute("download", `General Ledger Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `General Ledger Mass Change.xlsx has been downloaded successfully`
        );

        setSelectAll(false);

        setMessageDialogSeverity("success");
      } else {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
      }
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setSelectAll(false);
      }
    };
    if (alignment === "Change Temporary Block") {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmptyEmail = () => {
    setIsLoading(true);
    let downloadPayloadHashTemporaryBlock = {}
    downloadPayloadHashTemporaryBlock["GlAccount"] = [];
    downloadPayloadHashTemporaryBlock["templateName"] = checkElement(
      selectedOptionBlocked
    );

    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        compCode: x?.compCode,
        glAccount: x?.glAccount,
        coa: x?.chartOfAccount,
      };
    });
    let downloadPayloadHash = {};
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Type",
      "Account Group",
      "Business Segment",
      "OIM/CSL Indicator",
      "Tax Field Check",
    ];
    let filterDataWithSelectedData = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else {
      selectedListItemsCOA.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    }
    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    downloadPayloadHash["GlAccount"] = [];
    downloadPayloadHash["headers"] = selectedItemArr;
    downloadPayloadHash["templateHeaders"] = "";
    downloadPayloadHash["templateName"] = "";
   
    setBlurLoading(true);
    let hSuccess = (response) => {
      setBlurLoading(false);
      setLoaderMessage("");
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");
      setMessageDialogMessage(`Download has been started. You will get the Excel file via email`);
      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
        setSelectAll(false);
      }
    };
    if (alignment === "Change Temporary Block") {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };

  const checkElement = (inputArray) => {

    if (inputArray.includes("Temp Block/Unblock-Post Automatically Only")) {
      return "poAutoOnly";
    } else if (inputArray.includes("Temp Block/Unblock-Comp Code")) {
      return "blockCCLevel";
    } else if (inputArray.includes("Temp Block/Unblock-COA")) {
      return "blockCoaLevel";
    }
  };
  const handleChangeDownload = () => {
    setIsLoading(true);

    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        compCode: x?.compCode,
        glAccount: x?.glAccount,
        coa: x?.chartOfAccount,
      };
    });
    let downloadPayloadHash = {};
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Type",
      "Account Group",
      "Business Segment",
      "OIM/CSL Indicator",
      "Tax Field Check",
    ];
    let downloadPayloadHashTemporaryBlock = {};
    let filterDataWithSelectedData = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else if (alignment === "Chart of Account") {
      selectedListItemsCOA.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else {
      selectedOptionBlocked.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    }
    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    downloadPayloadHashTemporaryBlock["GlAccount"] = downloadPayload;
    downloadPayloadHashTemporaryBlock["templateName"] = checkElement(
      selectedOptionBlocked
    );
    downloadPayloadHash["GlAccount"] = downloadPayload;
    downloadPayloadHash["headers"] = selectedItemArr;
    downloadPayloadHash["templateHeaders"] = "";
    downloadPayloadHash["templateName"] = "";
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    let hSuccess = (response) => {
      if (response?.size !== 0 || response?.type !== "") {
        setBlurLoading(false);
        setLoaderMessage("");
        setIsLoading(false);
        setSelectAll(false);
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");

        link.href = href;
        link.setAttribute("download", `GeneraL Ledger_Mass Change.xlsx`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Success");

        setMessageDialogMessage(
          `GeneraL Ledger_Mass Change.xlsx has been downloaded successfully`
        );

        setMessageDialogSeverity("success");
      } else {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please try again.");
        setMessageDialogSeverity("danger");
      }

    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };

    if (alignment === "Change Temporary Block") {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataForTempBlock`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithData`,
        "postandgetblob",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const handleChangeDownloadEmail = () => {
    setIsLoading(true);

    var downloadPayload = selectedMassChangeRowData?.map((x) => {
      return {
        compCode: x?.compCode,
        glAccount: x?.glAccount,
        coa: x?.chartOfAccount,
      };
    });
    let downloadPayloadHash = {};
    let selectedItemArr = [
      "GL Account",
      "Chart Of Account",
      "Company Code",
      "Account Type",
      "Account Group",
      "Business Segment",
      "OIM/CSL Indicator",
      "Tax Field Check",
    ];
    let downloadPayloadHashTemporaryBlock = {};
    let filterDataWithSelectedData = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else if (alignment === "Chart of Account") {
      selectedListItemsCOA.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    } else {
      selectedOptionBlocked.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedData.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedData));
    }
    filterDataWithSelectedData?.map((selectedElement) => {
      selectedItemArr?.push(selectedElement.name);
    });
    downloadPayloadHashTemporaryBlock["GlAccount"] = downloadPayload;
    downloadPayloadHashTemporaryBlock["templateName"] = checkElement(
      selectedOptionBlocked
    );
    downloadPayloadHash["GlAccount"] = downloadPayload;
    downloadPayloadHash["headers"] = selectedItemArr;
    downloadPayloadHash["templateHeaders"] = "";
    downloadPayloadHash["templateName"] = "";
   
    setBlurLoading(true);
    let hSuccess = (response) => {
      setBlurLoading(false);
      setLoaderMessage("");
      setIsLoading(false);
      setSelectAll(false);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(`Download has been started. You will get the Excel file via email`);

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        setBlurLoading(false);
        setLoaderMessage(false);
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(
          `Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>.`
        );
        setMessageDialogSeverity("danger");
      }
    };

    if (alignment === "Change Temporary Block") {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataForTempBlockInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHashTemporaryBlock
      );
    } else {
      doAjax(
        `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataInMail`,
        "post",
        hSuccess,
        hError,
        downloadPayloadHash
      );
    }
  };
  const getValueForFieldName = (data, fieldName) => {
    const field = data?.find((field) => field?.fieldName === fieldName);
        return field ? field.value : "";
  };
  const getValueForGLAccount = (data, glAccount) => {
    return data?.filter((item) => item?.glAccount === glAccount);
  };

  const geAllExtendeItems = (extendedItems) => {
    let extendedArr = [];
    extendedItems?.map((item) => {
      extendedArr?.push(item.code);
    });

    return extendedArr;
  };

  const handleChangeDownloadExtend = () => {
    setBlurLoading(true);
    setIsLoading(true);
    let arr_ofData = [];
    Object?.keys(extendSelectedOptionsWithkey)?.map((item) => {
      let getCompCode = getValueForGLAccount(editMultipleGlExtendData, item);
      let pyloadhash = {};
      pyloadhash["glAccount"] = item;
      pyloadhash["coCodeToExtend"] = geAllExtendeItems(
        extendSelectedOptionsWithkey[item]
      )?.join(",");
      arr_ofData.push(pyloadhash);
    });

    var downloadPayload = [
      {
        compCode: "I00X",
        glAccount: "**********",
        coCodeToExtend: "0001,TUK1",
      },
      {
        compCode: "I00X",
        glAccount: "**********",
        coCodeToExtend: "0001,TUK1",
      },
    ];
    let downloadPayloadHash = {};
    let selectedItemArr = ["GL Account", "Chart Of Account", "Company Code"];
    selectedListItems?.map((selectedElement) => {
      selectedItemArr.push(selectedElement.name);
    });
    downloadPayloadHash["GlAccount"] = downloadPayload;
    downloadPayloadHash["headers"] = selectedItemArr;
    let hSuccess = (response) => {
      setIsLoading(false);
      setBlurLoading(false);
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `GeneraL Ledger_Mass Extend.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Success");

      setMessageDialogMessage(
        `GeneraL Ledger_Mass Extend.xlsx has been downloaded successfully`
      );

      setMessageDialogSeverity("success");
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogTitle("Error");
        setMessageDialogMessage(`${error.message}`);
        setMessageDialogSeverity("danger");
      }
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/excel/downloadExcelWithDataForExtend`,
      "postandgetblob",
      hSuccess,
      hError,
      arr_ofData
    );
  };

  const handleChangeDownloadExtendMass = () => {


    let arr_ofData = [];
    Object?.keys(extendSelectedOptionsWithkey)?.map((item) => {
      let getCompCode = getValueForGLAccount(editMultipleGlExtendData, item);
      let pyloadhash = {};
      pyloadhash["glAccount"] = item;
      pyloadhash["longText"] = item;
      pyloadhash["shortText"] = item;
      pyloadhash["coCodeToExtend"] = geAllExtendeItems(
        extendSelectedOptionsWithkey[item]
      )?.join(",");
      arr_ofData.push(pyloadhash);
    });
    dispatch(setMultipleGlDataForExtend(arr_ofData));
    navigate(`/masterDataCockpitNew/generalLedger/extendMultipleGl`);
  };

  const handleChangeDownloadExtendMassFerc = () => {
  
    let arr_ofData = [];
    Object?.keys(extendSelectedOptionsWithkey)?.map((item) => {
      
      let getCompCode = getValueForGLAccount(editMultipleGlExtendData, item);

      let pyloadhash = {};
      pyloadhash["glAccount"] = item;
      pyloadhash["longText"] = item;
      pyloadhash["shortText"] = item;
      pyloadhash["coCodeToExtend"] = geAllExtendeItems(
        extendSelectedOptionsWithkey[item]
      )?.join(",");
      arr_ofData.push(pyloadhash);
    });
    dispatch(setMultipleGlDataForExtend(arr_ofData));
    navigate(`/masterDataCockpitNew/generalLedger/fercGLAccountField`);
  };

  const handleCloseButtonCeateMultiple = (event) => {
    if (
      anchorRefChange.current &&
      anchorRefChange.current.contains(event.target)
    ) {
      return;
    }
    setOpenButton((prevOpen) => !prevOpen);
  };
  const getCompanyCodeBasedOnChartOfAccount = (value) => {
    const hSuccess = (data) => {
      let companyCodeArr = [];

      data.body?.map((item) => {
        let companyCodeHash = {};
        companyCodeHash["code"] = item?.code;
        companyCodeHash["desc"] = item?.desc;
        companyCodeArr.push(companyCodeHash);
      });

      const sortedData = companyCodeArr
        ?.filter((item) => !item?.code.startsWith("7"))
        .concat(companyCodeArr.filter((item) => item?.code?.startsWith("7")));


      const transformData = (data) => {
        return data?.map((item) => {
          return {
            title: `${item?.code} - ${item?.desc}`, 
            group: !item?.code.startsWith("7")
              ? "ET Active Company Code"
              : "SUN Active Company Code",
          };
        });
      };

      const groupedData = [
        { title: "Select All", group: null }, 
        ...transformData(sortedData), 
      ];
      dispatch(
        setDropDown({ keyName: "CompanyCodeForWithCopy", data: groupedData })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCompanyCodeBasedOnChartOfAccountExtend = (value, GlAccount) => {
    setBlurLoadingForInner(true);
    const hSuccesspresentGLAccount = (dataOfpresentGLAccount) => {
      if (dataOfpresentGLAccount?.statusCode === 200) {
        setBlurLoadingForInner(false);
        let arr = [];
        dataOfpresentGLAccount?.body?.map((item, index) => {
          let hash = {};
          hash["code"] = item.code;
          hash["desc"] = item.desc;
          hash["id"] = index + 1;
          arr.push(hash);
        });
       
        setExtendDropdownList(arr);

        dispatch(setDropDown({ keyName: "CompanyCodeExtend", data: arr }));
        setBlurLoadingForInner(false);
      } else {
        setBlurLoadingForInner(false);
        setMessageDialogSeverity("danger");
        setMessageDialogMessage(
          "All The Company Code Already Present in This GL Account"
        );
        setMessageDialogTitle("");
        handleMessageDialogClickOpen();
      }
    };

    const hErrorpresentGlAccount = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAvailableCompCodesToExtend?coa=${value}&glAccount=${GlAccount}`,
      "get",
      hSuccesspresentGLAccount,
      hErrorpresentGlAccount
    );
  };

  const getCompanyCodeBasedOnChartOfAccountExtendFerc = (value, GlAccount) => {
    setBlurLoadingForInner(true);

    const hSuccesspresentGLAccount = (dataOfpresentGLAccount) => {
      if (dataOfpresentGLAccount?.statusCode === 200) {
        setBlurLoadingForInner(false);

        let arr = [];
        dataOfpresentGLAccount?.body?.map((item, index) => {
          let hash = {};
          hash["code"] = item.code;
          hash["desc"] = item.desc;
          hash["id"] = index + 1;
          arr.push(hash);
        });
        setExtendDropdownList(arr);

        dispatch(setDropDown({ keyName: "CompanyCodeExtend", data: arr }));
        setBlurLoadingForInner(false);
      } else {
        setBlurLoadingForInner(false);
        setMessageDialogSeverity("danger");
        setMessageDialogMessage(
          "All The Company Code Already Present in This GL Account"
        );
        setMessageDialogTitle("");
        handleMessageDialogClickOpen();
      }
    };

    const hErrorpresentGlAccount = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getRegCCForFERCOperation?coa=${value}&glAccount=${GlAccount}`,
      "get",
      hSuccesspresentGLAccount,
      hErrorpresentGlAccount
    );
  };

  const getGroupAccountNumber = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "GroupAccountNumber", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGroupAccountNumber?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountCurrency", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getExchangeRateDiffKey = () => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "ExchangeRateDiffKey", data: data.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getExchangeRateDiffKey?ChartOfAccount=${"ETCN"}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (value) => {
    setIsDropDownLoading(true);
    let payload = {
      coa: value ? value : rmSearchForm?.chartOfAccount?.code,
      top: 200,
      skip: 0,
    };
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "TaxCategory", data: data.body }));
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getSearchParamsTaxCategory`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const getAlternativeAccountNumber = (value) => {
    const hSuccess = (data) => {
      dispatch(
        setDropDown({ keyName: "AlternativeAccountNumber", data: data.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAlternativeAccountNumber?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFieldStatusGroup = (value) => {
    const hSuccess = (data) => {
      setDynamicOptions((prev) => ({
        ...prev,
        ["Field Status Group"]: data.body,
      }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountGroup = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "AccountGroupDialog", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountGroupLookup = (value) => {
    const hSuccess = (data) => {
      setDynamicOptions((prev) => ({ ...prev, ["Account Group"]: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroupCodeDesc?chartAccount=${value}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getHouseBank = (value) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "HouseBank", data: data.body }));
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getAccountGroupForWithCopyForm = (value) => {

    if (value !== null) {
      const filteredData = glAccountRangeData.filter(
        (obj) => obj.MDG_GL_ACCOUNT_TYPE == value?.desc
      );
      let accountGroupArr = [];
      filteredData.map((item) => {
        let accountGroup_hash = {};
        accountGroup_hash["AccountGroup"] = item.MDG_GL_ACCOUNT_GRP_CODE;
        accountGroup_hash["Description"] = item.MDG_GL_ACCOUNT_GROUP;
        accountGroupArr.push(accountGroup_hash);
      });

      const result = Object.values(
        accountGroupArr.reduce(
          (acc, obj) => ({ ...acc, [obj.Description]: obj }),
          {}
        )
      );
      const sortedDatabyAlphabetic = result?.sort((a, b) =>
        a?.AccountGroup?.localeCompare(b?.AccountGroup)
      );
    
      setaccontGroupWithCopyValue(null);
      setAccountGroupWithCopy(sortedDatabyAlphabetic)
    } else {
      setsubaccontGroupWithCopy(null);
      setaccontGroupWithCopyValue(null);
      setAccountGroupWithCopy([]);
      setAccountSubGroupWithCopy([]);
    }
  };

  const getSubAccountGroupForWithCopyForm = (value) => {

    if (value !== null) {
      const filteredBasedOnAcountTypeData = glAccountRangeData.filter(
        (obj) => obj.MDG_GL_ACCOUNT_TYPE == newAccountType?.desc
      );
    
      const filteredData = filteredBasedOnAcountTypeData.filter(
        (obj) => obj.MDG_GL_ACCOUNT_GROUP === value?.Description
      );
      let subaccountGroupArr = [];
      filteredData.map((item) => {
        let subaccountGroup_hash = {};
        subaccountGroup_hash["code"] = item.MDG_GL_SUB_ACCOUNT_GRP[0];
        subaccountGroup_hash["desc"] = item.MDG_GL_SUB_ACCOUNT_GRP;
        subaccountGroupArr.push(subaccountGroup_hash);
      });
    
      const result = Object.values(
        subaccountGroupArr.reduce(
          (acc, obj) => ({ ...acc, [obj.desc]: obj }),
          {}
        )
      );

      const sortedDatabyAlphabetic = result?.sort((a, b) =>
        a.code.localeCompare(b.code)
      );

      setAccountSubGroupWithCopy(sortedDatabyAlphabetic);
    } else {
      setsubaccontGroupWithCopy(null);
      setAccountSubGroupWithCopy([]);
    }
  };
  const getAccountNumberRangeForWithCopy = (value) => {
    setcopyFormDisable(true);
    const filteredDataAfterSubGroupSlected = glAccountRangeData.filter(
      (obj) => obj.MDG_GL_SUB_ACCOUNT_GRP === value?.desc
    );
    setglAccountWithCopyMinRange(
      filteredDataAfterSubGroupSlected[0]?.MDG_GL_L1_RANGE
    );
    setglAccountWithCopyMaxRange(
      filteredDataAfterSubGroupSlected[0]?.MDG_GL_L2_RANGE
    );
    setAccountNumberCopyFrom(
      filteredDataAfterSubGroupSlected[0]?.MDG_GL_GLACCOUNT_NO
    );
    setCompanyCodeCopyFrom(
      filteredDataAfterSubGroupSlected[0]?.MDG_GL_COMPANY_CODE
    );


  };

  const getCostElementCategory = (value) => {
   
    const hSuccess = (data) => {
      
      dispatch(
        setDropDown({ keyName: "CostElementCategory", data: data.body })
      );
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${value?.code}`,
      "get",
      hSuccess,
      hError
    );
  };

  const companyCodes = [
    {
      code: "1100",
      desc: "LE MP,LLC",
    },
    {
      code: "1120",
      desc: "ET PARTNERS KP, SP",
    },
    {
      code: "1160",
      desc: "ACTIVE-SUNPRTNRSLEASEACFQ",
    },
    {
      code: "2010",
      desc: "ENERGY TRANSFER SP",
    },
    {
      code: "2012",
      desc: "SUNOCO MP LLC",
    },
    {
      code: "2014",
      desc: "ENABLE  MIDSTREAM PARTNER",
    },
    {
      code: "2020",
      desc: "ET WNG EXPORT, LLC",
    },
    {
      code: "2715",
      desc: "SEC ENERGY PROD & JVCS LP",
    },
    {
      code: "7200",
      desc: "SUNOCO TPC, LLC",
    },
    {
      code: "7305",
      desc: "SUNOCO TQC RETAIL LLC",
    },
  ];


  const handleConfirm = () => {
  };

  const functions_ExportAsExcelErrorProcessNotCompleate = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      duplicateFieldsColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Duplicate Requests -${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: duplicateFieldsData,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() =>
            functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel()
          }
        >
          Download
        </Button>
      );
    },
  };

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "actions" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `General Ledger Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: rmDataRows,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcel.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };
  const handleSelectColumnDialogClose = () => {
    setOpenSelectColumnDialog(false);
  };
  function checkCompCodes() {
    setAccountTypeWithCopy([]);
    setNewAccountType(null);
    setAccountGroupWithCopy([]);
    setAccountSubGroupWithCopy([]);
    setAccountNumberCopyFrom([]);
    setsubaccontGroupWithCopy(null);
    setaccontGroupWithCopyValue(null);
    setNewGLAccount([])
    setCompanyCodeCopyFrom([])
    setglAccountWithCopyMinRange([])
    setglAccountWithCopyMaxRange([])

    const getCode = newCompanyCode?.map((item) => item.title);
    let allStartWith7 = true;
    let noneStartWith7 = true;

    if (getCode?.length > 0) {
      getCode.forEach((item) => {
        if (item?.startsWith("7")) {
          noneStartWith7 = false;
        } else {
          allStartWith7 = false;
        }
      });

      if (allStartWith7) {
        setIsValidateAfterSeriesCheck(false);
        setIsSunocoChecked(true);
        getGlAccountRange("Sunoco");
      } else if (noneStartWith7) {
        setIsValidateAfterSeriesCheck(false);
        getGlAccountRange("NonSunoco");
      } else {
        setIsValidateAfterSeriesCheck(false);
        getGlAccountRange("NonSunoco");
      }
    } else {
      handleMessageDialogClickOpen();
      setMessageDialogTitle("Confirm");
      setMessageDialogMessage("Please Select Company Code");
      setMessageDialogSeverity("danger");
    }
  }

  const handleOpenDialogMassExtend = (rows) => {
    setOpenDialogMassExtend(true);
    setTableRowsExtend(rows);

  };
  const handleOpenDialogMassFerc = (rows) => {
    setOpenDialogMassFerc(true);
    setTableRowsFerc(rows);

  };
  const handleOpenDialogExtend = (rows) => {
    setOpenDialogExtend(true);
    setTableRowsExtend(rows);
    setTableRowsExtend((rows) => ({
      ...rows,
      requestType: "Display For Extend",
    }));
  };

  const handleOpenDialog = (rows) => {
    setOpenDialog(true);
    setTableRows(rows);
  };
  const handleCloseDialog = () => {
    setDownloadMultiple(false);
    setSelectAll(false);
    setOpenDialog(false);
    setOpenDialogExtend(false);
    setSelectedListItems([]);
    setSelectedListItemsCOA([]);
    setSelectAll(false);
  };

  const openNewChangeDialog = () => {
    if (downloadMultiple !== true && selectedRows.length >= 0) {
      handleApply()
    } else {
      setOpenDownloadChangeDialog(true)
    }


  }
  const handleSelectAllMassGLAccount = () => {
    const selectableOptions = allOptions.filter(
      (option) => option.creationBlock !== "X"
    ); 

    if (selectedglAccountForMassExtend?.length === selectableOptions?.length) {
      setSelectedglAccountForMassExtend([]);
    } else {
      setSelectedglAccountForMassExtend(selectableOptions); 
    }
  };

  const handleCloseDialogMassExtend = () => {
    setSearchInput("");
    setNewChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    getCompanyCodeBasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    }); 
    getAllGLAccountbasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    setSelectedglAccountForMassExtend([]);
    setOpenDialogMassExtend(false);
  };

  const handleCloseDialogMassFerc = () => {
    setSearchInput("");
    setNewChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    getCompanyCodeBasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    }); 
    getAllGLAccountbasedOnChartOfAccount({
      code: "ETCN",
      desc: "ET NATURAL / OPERATIONAL CHART OF ACCOUNTS",
    });
    setSelectedglAccountForMassExtend([]);
    setOpenDialogMassExtend(false);
    setOpenDialogMassFerc(false)

  };

  const handleCloseDialogExtend = () => {
    setOpenDialogExtend(false);
  };
  const handleCloseDialogChangeExtend = () => {
    const resetState = rmDataRowsExtend.reduce((acc, row) => {
      acc[row.GLAccount] = false;
      return acc;
    }, {});

    setGlAccountsWithColor(resetState);
    setSelectedGLAccount([]);
    setRmDataRowsExtend([]);
    setExtendDropdownList([]);
    setExtendSelectedOptionsWithkey([]);
    setExtendSelectedAllOptionsWithkey([]);
    setOpenDialogChangeExtend(false);
  };

  const handleCloseDialogChangeExtendFerc = () => {
    const resetState = rmDataRowsExtend.reduce((acc, row) => {
      acc[row.GLAccount] = false;
      return acc;
    }, {});

    setGlAccountsWithColor(resetState);
    setSelectedGLAccount([]);
    setRmDataRowsExtend([]);
    setExtendDropdownList([]);
    setExtendSelectedOptionsWithkey([]);
    setExtendSelectedAllOptionsWithkey([]);
    setOpenDialogChangeExtendFerc(false);
  };

  const handleApply = () => {
    if (downloadMultiple === true && selectedRows.length === 0) {
      if (
        selectedListItems?.length > 0 ||
        selectedListItemsCOA?.length > 0 ||
        selectedOptionBlocked?.length > 0
      ) {
        handleChangeDownloadEmpty();
        setOpenDialog(false);
        setSelectedListItems([]);
        setSelectedListItemsCOA([]);
        selectedOptionBlocked([]);
        setDownloadMultiple(false);

        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    }
    let filterDataForLock = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "Chart of Account") {
      selectedListItemsCOA.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataForLock.push(COHash);
          }
        });
      });
    }
    else {

      let filterDataWithSelectedDataBlocked = [];
      selectedOptionBlocked.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedDataBlocked.push(COHash);
            filterDataForLock.push(COHash);
          }
        });

        
      });
      
      dispatch(setFields(filterDataWithSelectedDataBlocked));
     
    }
    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");
    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      payload = selectedMassChangeRowData.map((row) => ({
        glAccount: row.glAccount,
        compCode: row.compCode,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      payload = [
        {
          glAccount: tableRows?.glAccount,
          compCode: tableRows?.compCode,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }
    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          if (selectedRows.length > 0) {
            if (
              selectedListItems?.length > 0 ||
              selectedListItemsCOA?.length > 0
            ) {
              handleChangeDownload();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setSelectedOptionBlocked([]);
              setDownloadMultiple(false);
            } else if (selectedOptionBlocked?.length > 0) {
              handleChangeDownload();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setSelectedOptionBlocked([]);
              setDownloadMultiple(false);
            } else {
              setMessageDialogTitle("Error");
              setMessageDialogMessage("Please Select Any Field To Proceed?");
              setMessageDialogSeverity("danger");
              handleMessageDialogClickOpen();
            }
          } else if (selectedRows.length === 0) {
            if (
              selectedListItems?.length > 0 ||
              selectedListItemsCOA?.length > 0
            ) {
              handleChangeDownloadEmpty();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setDownloadMultiple(false);
            } else {
              setMessageDialogTitle("Error");
              setMessageDialogMessage("Please Select Any Field To Proceed?");
              setMessageDialogSeverity("danger");
              handleMessageDialogClickOpen();
            }
          }
        } else {
         
          if (alignment === "Company Code") {
            let filterDataWithSelectedData = [];
            selectedListItems.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedData.push(COHash);
                }
              });
            
            });
            dispatch(setFields(filterDataWithSelectedData));
          } else if (alignment === "Chart of Account") {
            let filterDataWithSelectedData = [];
            selectedListItemsCOA.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedData.push(COHash);
                }
              });
            
            });
            dispatch(setFields(filterDataWithSelectedData));

          } else {
            let filterDataWithSelectedDataBlocked = [];
            selectedOptionBlocked.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedDataBlocked.push(COHash);
                }
              });
            });
            dispatch(setFields(filterDataWithSelectedDataBlocked));
            
          }
          if (
            selectedListItems?.length > 0 ||
            selectedListItemsCOA?.length > 0
          ) {
            dispatch(clearGeneralLedger());
            dispatch(clearPayloadForEdit());
            navigate("/masterDataCockpitNew/generalLedger/changeGLField", {
              state: tableRows,
            });
          } else if (selectedOptionBlocked?.length > 0) {
            dispatch(clearGeneralLedger());
            dispatch(clearPayloadForEdit());

            navigate(
              "/masterDataCockpitNew/generalLedger/changeGLFieldTemporaryBlock",
              {
                state: tableRows,
              }
            );
          } else {
            setMessageDialogTitle("Error");
            setMessageDialogMessage("Please Select Any Field To Proceed?");
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const glAccount = item?.message?.match(/GL Account:\s*(\d+)/)?.[1];
          dataHash["id"] = index;
          dataHash["glAccount"] = glAccount;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);
      }
    };
    let hError = (error) => {
      if (error.message) {
        handleMessageDialogClickOpen();
        setMessageDialogSeverity("danger");
      }
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/checkDuplicateGLRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApplyEmail = () => {
    if (downloadMultiple === true && selectedRows.length === 0) {
      if (
        selectedListItems?.length > 0 ||
        selectedListItemsCOA?.length > 0 ||
        selectedOptionBlocked?.length > 0
      ) {
        handleChangeDownloadEmptyEmail();
        setOpenDialog(false);
        setSelectedListItems([]);
        setSelectedListItemsCOA([]);
        selectedOptionBlocked([]);
        setDownloadMultiple(false);

        return;
      } else {
        setMessageDialogTitle("Error");
        setMessageDialogMessage("Please Select Any Field To Proceed?");
        setMessageDialogSeverity("danger");
        handleMessageDialogClickOpen();
      }
    }
    let filterDataForLock = [];
    if (alignment === "Company Code") {
      selectedListItems.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input.name) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataForLock.push(COHash);
          }
        });
      });
    } else if (alignment === "Chart Of Account") {
    } else {
      let filterDataWithSelectedDataBlocked = [];
      selectedOptionBlocked.forEach((input) => {
        fieldSelectionFromIdm.map((item, index) => {
          if (item.MDG_SELECT_OPTION === input) {
            let COHash = {};
            COHash["id"] = index;
            COHash["name"] = item.MDG_FIELD_NAME;
            filterDataWithSelectedDataBlocked.push(COHash);
          }
        });
      });
      dispatch(setFields(filterDataWithSelectedDataBlocked));
   
    }
    let changedFieldsToCheck = filterDataForLock
      ?.map((item) => item?.name)
      ?.join(",");
    let payload = [];
    if (downloadMultiple === true && selectedRows.length > 0) {
      payload = selectedMassChangeRowData.map((row) => ({
        glAccount: row.glAccount,
        compCode: row.compCode,
        changedFieldsToCheck: changedFieldsToCheck,
      }));
    } else {
      payload = [
        {
          glAccount: tableRows?.glAccount,
          compCode: tableRows?.compCode,
          changedFieldsToCheck: changedFieldsToCheck,
        },
      ];
    }
    const hSuccess = (response) => {
      const hasError = response.some((item) => item.statusCode !== 200);
      if (!hasError) {
        if (downloadMultiple === true) {
          if (selectedRows.length > 0) {
            if (
              selectedListItems?.length > 0 ||
              selectedListItemsCOA?.length > 0
            ) {
              handleChangeDownloadEmail();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setSelectedOptionBlocked([]);
              setDownloadMultiple(false);
            } else if (selectedOptionBlocked?.length > 0) {
              handleChangeDownloadEmail();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setSelectedOptionBlocked([]);
              setDownloadMultiple(false);
            } else {
              setMessageDialogTitle("Error");
              setMessageDialogMessage("Please Select Any Field To Proceed?");
              setMessageDialogSeverity("danger");
              handleMessageDialogClickOpen();
            }
          } else if (selectedRows.length === 0) {
            if (
              selectedListItems?.length > 0 ||
              selectedListItemsCOA?.length > 0
            ) {
              handleChangeDownloadEmpty();
              setOpenDialog(false);
              setSelectedListItems([]);
              setSelectedListItemsCOA([]);
              setDownloadMultiple(false);
            } else {
              setMessageDialogTitle("Error");
              setMessageDialogMessage("Please Select Any Field To Proceed?");
              setMessageDialogSeverity("danger");
              handleMessageDialogClickOpen();
            }
          }
        } else {
          if (alignment === "Company Code") {
            let filterDataWithSelectedData = [];
            selectedListItems.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedData.push(COHash);
                }
              });
            });
            dispatch(setFields(filterDataWithSelectedData));
          } else if (alignment === "Chart of Account") {
            let filterDataWithSelectedData = [];
            selectedListItemsCOA.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input.name) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedData.push(COHash);
                }
              });
             
            });
            dispatch(setFields(filterDataWithSelectedData));
           
          } else {
            let filterDataWithSelectedDataBlocked = [];
            selectedOptionBlocked.forEach((input) => {
              fieldSelectionFromIdm.map((item, index) => {
                if (item.MDG_SELECT_OPTION === input) {
                  let COHash = {};
                  COHash["id"] = index;
                  COHash["name"] = item.MDG_FIELD_NAME;
                  filterDataWithSelectedDataBlocked.push(COHash);
                }
              });
             
            });
            dispatch(setFields(filterDataWithSelectedDataBlocked));
           
          }
          if (
            selectedListItems?.length > 0 ||
            selectedListItemsCOA?.length > 0
          ) {
            dispatch(clearGeneralLedger());
            dispatch(clearPayloadForEdit());
            navigate("/masterDataCockpitNew/generalLedger/changeGLField", {
              state: tableRows,
            });
          } else if (selectedOptionBlocked?.length > 0) {
            dispatch(clearGeneralLedger());
            dispatch(clearPayloadForEdit());

            navigate(
              "/masterDataCockpitNew/generalLedger/changeGLFieldTemporaryBlock",
              {
                state: tableRows,
              }
            );
          } else {
            setMessageDialogTitle("Error");
            setMessageDialogMessage("Please Select Any Field To Proceed?");
            setMessageDialogSeverity("danger");
            handleMessageDialogClickOpen();
          }
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        let duplicateFieldsArr = [];

        filteredData?.map((item, index) => {
          let dataHash = {};
          const glAccount = item?.message?.match(/GL Account:\s*(\d+)/)?.[1];
          dataHash["id"] = index;
          dataHash["glAccount"] = glAccount;
          dataHash["reqId"] =
            item?.body?.EditIds?.[0] || item?.body?.MassEditIds?.[0];
          dataHash["requestedBy"] = item?.body?.RequestCreatedBy?.[0];

          duplicateFieldsArr.push(dataHash);
        });

        setDuplicateFieldsData(duplicateFieldsArr);

        setShowTableInDialog(true);
      }
    };
    const hError = (error) => {
      customError(error);
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/alter/checkDuplicateGLRequest`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };
  const handleApplyExtend = () => {
    if (downloadMultiple === true) {
      handleChangeDownloadExtendMass();
      setOpenDialogChangeExtend(false);
      setExtendSelectedOptionsWithkey([]);
      setSelectedListItems([]);
      setSelectedListItemsCOA([]);
    } else {
      {
        alignment === "Company Code"
          ? dispatch(setFields(selectedListItems))
          : dispatch(setFields(selectedListItemsCOA));
      }
      navigate("/masterDataCockpitNew/generalLedger/changeGLField", {
        state: tableRows,
      });
    }
  };
  const handleCloseDialogpopup = () => {
    setOpenDialogforPopup(false);
  };



  const handleDialogCloseErrorRange = () => {
    setdialogOpenMassWithCopyError(false);
  };
  const handleDialogCloseGlPresent = () => {
    setDialogOpenGlPresentError(false);
  };

  const handleSearchAction = (value) => {
    if (!value) {
      setTableData([...rmDataRows]);
      return;
    }
    const selected = rmDataRows.filter((row) => {
      let rowMatched = false;
      let keys = Object.keys(row);

      const displayedKeys = allColumns.map((col) => col.field);
      for (let k = 0; k < keys.length; k++) {
        if (displayedKeys.includes(keys[k])) {
          rowMatched = !row[keys[k]]
            ? false
            : row?.[keys?.[k]] &&
            row?.[keys?.[k]]
              .toString()
              .toLowerCase()
              ?.indexOf(value?.toLowerCase()) != -1;

          if (rowMatched) break;
        }
      }
      return rowMatched;
    });
    setTableData([...selected]);
    setRmDataRows([...selected]);
  };
  

  const handleRadioChangeBlocked = (event) => {
    setSelectedOptionBlocked([event.target.value]);
  };
  const functions_ExportAsExcelErrorRange = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      validationColumns.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Error log Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: gLValidationRangeErrors,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() => functions_ExportAsExcelErrorRange.convertJsonToExcel()}
        >
          Download
        </Button>
      );
    },
  };

  const functions_ExportAsExcelforExtendGlAccount = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      allColumnsinExtend.forEach((item) => {
        if (item.headerName !== "GL Description") {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Extended GL Account Data-${moment(presentDate).format(
          "DD-MMM-YYYY"
        )}`,
        columns: excelColumns,
        rows: gLValidationRangeErrors,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() =>
            functions_ExportAsExcelforExtendGlAccount.convertJsonToExcel()
          }
        >
          Download
        </Button>
      );
    },
  };
  const functions_ExportAsExcelErrorGlname = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      validationColumnsOfGLAcocuntPresent.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });
      saveExcel({
        fileName: `Error log Data-${moment(presentDate).format("DD-MMM-YYYY")}`,
        columns: excelColumns,
        rows: glNamePesentError,
      });
    },
    button: () => {
      return (
        <Button
          sx={{
            textTransform: "capitalize",
            position: "absolute",
            right: 0,
            top: 0,
          }}
          onClick={() =>
            functions_ExportAsExcelErrorGlname.convertJsonToExcel()
          }
        >
          Download
        </Button>
      );
    },
  };
  return (
    <div ref={ref_elementForExport}>
      <Dialog
        open={dialogOpenGlPresentError}
        fullWidth
        onClose={handleDialogCloseGlPresent}
        sx={{
          
          "& .MuiDialog-paper": {
            minWidth: "800px",
            minheight: "500px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6" color="red">
            Following Gl is already Created Or In Workflow in following Chart Of
            Account
          </Typography>

          <Grid md={1}>
            <Tooltip title="Export Table">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={functions_ExportAsExcelErrorGlname.convertJsonToExcel}
              >
                <ReusableIcon iconName={"IosShare"} />
              </IconButton>
            </Tooltip>
            {/* <Typography > */}

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleDialogCloseGlPresent}
              children={<CloseIcon />}
            />
          </Grid>
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          {glValidationGlName && (
            <ReusableTable
              width="100%"
              rows={glNamePesentError}
              columns={validationColumnsOfGLAcocuntPresent}
              pageSize={10}
              getRowIdValue={"glAccount"}
              getRowId={"glAccount"}
              hideFooter={true}
              checkboxSelection={false}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              stopPropagation_Column={"action"}
              status_onRowDoubleClick={true}
            />
          )}
        </DialogContent>

        <DialogActions
          sx={{ display: "flex", justifyContent: "end" }}
        ></DialogActions>
      </Dialog>

      <Dialog
        open={showTableInDialog}
        onClose={() => setShowTableInDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: "#FFDAB9", color: "warning.contrastText" }}>
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <span>
              <WarningIcon sx={{ mr: 1 }} /> Duplicate Requests Alert
            </span>

            <Tooltip title="Export Table">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={
                  functions_ExportAsExcelErrorProcessNotCompleate.convertJsonToExcel
                }
              >
                <ReusableIcon iconName={"IosShare"} />
              </IconButton>
            </Tooltip>
          </Typography>
        </DialogTitle>
        <DialogContent>
          <div style={{ marginTop: "20px" }}>
            <ReusableTable
              height={400}
              rows={duplicateFieldsData}
              columns={duplicateFieldsColumns}
              pageSize={duplicateFieldsData.length}
              getRowIdValue={"id"}
              hideFooter={true}
              checkboxSelection={false}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              stopPropagation_Column={"action"}
              status_onRowDoubleClick={true}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <Button
            variant="contained"
            onClick={() => {
              setShowTableInDialog(false);
              setOpenDialog(false);
            }}
          >
            OK
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog open={openDownloadDialog} onClose={handleDownloadDialogClose}>
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            Select Download Option
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", 
                      fontSize: "12px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be downloaded
                  </span>
                }
           
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", 
                      fontSize: "12px",
                      
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be sent to your email
                  </span>
                }
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onDownloadTypeChange}>
            OK
          </Button>
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDownloadChangeDialog}
        onClose={handleDownloadChangeDialogClose}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography
            variant="h6"
            gutterBottom
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            Select Download Option
          </Typography>
        </DialogTitle>
        <DialogContent>
          <FormControl>
            <RadioGroup
              row
              aria-labelledby="demo-row-radio-buttons-group-label"
              name="row-radio-buttons-group"
              value={downloadType}
              onChange={handleMultipleDownloadTypeChange}
            >
              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap",
                      fontSize: "12px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be downloaded
                  </span>
                }
              >
                <FormControlLabel
                  value="systemGenerated"
                  control={<Radio />}
                  label="System-Generated"
                />
              </NoMaxWidthTooltip>

              <NoMaxWidthTooltip
                arrow
                placement="bottom"
                title={
                  <span
                    style={{
                      whiteSpace: "nowrap", 
                      fontSize: "12px",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    Here Excel will be sent to your email
                  </span>
                }
              >
                <FormControlLabel
                  value="mailGenerated"
                  control={<Radio />}
                  label="Mail-Generated"
                />
              </NoMaxWidthTooltip>
            </RadioGroup>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={onMultipleDownloadTypeChange}>
            OK
          </Button>
        </DialogActions>
      </Dialog>

      <ReusableBackDrop
        blurLoading={blurLoading}
        loaderMessage={loaderMessage}
      />


      <Dialog
        open={dialogOpenMassWithCopyError}
        fullWidth
        onClose={handleDialogCloseErrorRange}
        sx={{
        
          "& .MuiDialog-paper": {
            minWidth: "1000px",
            minheight: "500px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Typography variant="h6" color="red">
            Errors For GL Account Range Is Not Matched
          </Typography>

          <Grid md={1}>
            <Tooltip title="Export Table">
              <IconButton
                sx={iconButton_SpacingSmall}
                onClick={functions_ExportAsExcelErrorRange.convertJsonToExcel}
              >
                <ReusableIcon iconName={"IosShare"} />
              </IconButton>
            </Tooltip>
            {/* <Typography > */}

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleDialogCloseErrorRange}
              children={<CloseIcon />}
            />
          </Grid>
        </DialogTitle>
        <DialogContent sx={{ padding: ".5rem 1rem" }}>
          {glValidationRange && (
            <ReusableTable
              width="100%"
              rows={gLValidationRangeErrors}
              columns={validationColumns}
              pageSize={10}
              getRowIdValue={"GLAccount"}
              hideFooter={true}
              checkboxSelection={false}
              disableSelectionOnClick={true}
              status_onRowSingleClick={true}
              stopPropagation_Column={"action"}
              status_onRowDoubleClick={true}
            />
          )}
        </DialogContent>

        <DialogActions
          sx={{ display: "flex", justifyContent: "end" }}
        ></DialogActions>
      </Dialog>


      <Dialog open={openDialogforPopup}>
        <DialogTitle>{"Error"}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Please select Company Codes to be extended for all selected GLs
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialogpopup} color="primary">
            OK
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={openDialogHandsonDialog}
        onClose={handleCloseHandsonDialog}
        sx={{
       
          "& .MuiDialog-paper": {
            minWidth: "800px",
            minheight: "500px",
          },
        }}
      >
        <DialogTitle
          sx={{
            justifyContent: "space-between",
            alignItems: "center",
            height: "max-content",
            padding: ".5rem",
            paddingLeft: "1rem",
            backgroundColor: "#EAE9FF40",
            display: "flex",
          }}
        >
          <Grid sx={{ display: "block" }}>
            <Typography variant="h7">Mass Create With Copy</Typography>
            <Typography variant="body2" color="#777">
              To view Company Code
            </Typography>
          </Grid>
          <IconButton
            sx={{ width: "max-content" }}
            onClick={handleCloseHandsonDialog}
            children={<CloseIcon />}
          />
        </DialogTitle>

        <DialogContent
          sx={{
            padding: ".5rem 1rem",

            overflowY: "auto",
          }}
        >
          <div style={{ height: 500, width: "100%" }}>
            <DataGrid
              rows={rowsofCopy}
              columns={columnCopy}
              getRowHeight={() => "auto"}
            />
          </div>
        </DialogContent>
        <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
          <Button
            sx={{ width: "max-content", textTransform: "capitalize" }}
            onClick={handleCloseHandsonDialog}
          >
            Cancel
          </Button>
          <Button
            className="button_primary--normal"
            type="save"
            onClick={handleApplyHandsOn}
            variant="contained"
          >
            Apply
          </Button>
        </DialogActions>
      </Dialog>
      <div>
        <Dialog
          open={openDialog}
          onClose={handleCloseDialog}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
            <Grid>
              <Grid sx={{ display: "flex", justifyContent: "space-between" }}>
                <ToggleButtonGroup
                  color="primary"
                  value={alignment}
                  exclusive
                  onChange={handleChange}
                  aria-label="Platform"
                >
                  <ToggleButton
                    value="Company Code"
                    disabled={selectedListItemsCOA?.length !== 0}
                  >
                    Company Code
                  </ToggleButton>
                  <ToggleButton
                    value="Chart of Account"
                    disabled={selectedListItems?.length !== 0}
                  >
                    Chart of Account
                  </ToggleButton>
                  <ToggleButton
                    value="Change Temporary Block"
                    disabled={selectedListItems?.length !== 0}
                  >
                    TEMP BLOCK/UNBLOCK
                  </ToggleButton>
                </ToggleButtonGroup>
               
              </Grid>
              <Grid>
                <Typography variant="h6">
                  Select the field(s) to be changed
                </Typography>
              </Grid>
            </Grid>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              maxHeight: 400,
              
            }}
          >
            <Grid container>
              {alignment === "Company Code" ? (
                <>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={handleSelectAll}
                          checked={selectAll}
                        />
                      }
                      sx={{
                        height: {
                          xs: "3vh",
                        },
                      }}
                      label="SELECT ALL"
                    />
                  </Grid>
                  {dataList?.map((item) => (
                    <Grid item xs={12} key={item?.id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => handleSelectionListItem(item)}
                            checked={selectedListItems?.some(
                              (selectedItem) => selectedItem.id === item.id
                            )}
                          />
                        }
                        label={item.name?.toUpperCase()}
                        sx={{
                          height: {
                            xs: "3vh",
                          },
                        }}
                      />
                    </Grid>
                  ))}
                </>
              ) : alignment === "Chart of Account" ? (
                <>
                  {/* Select All Checkbox for Chart of Account */}
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={handleSelectAll}
                          checked={selectAll}
                        />
                      }
                      sx={{
                        height: {
                          xs: "3vh",
                        },
                      }}
                      label="SELECT ALL"
                    />
                  </Grid>
                  {dataListCOA?.map((item) => (
                    <Grid item xs={12} key={item.id}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            onChange={() => handleSelectionListItemCOA(item)}
                            checked={selectedListItemsCOA?.some(
                              (selectedItem) => selectedItem.id === item.id
                            )}
                          />
                        }
                        sx={{
                          height: {
                            xs: "3vh",
                          },
                        }}
                        label={item.name?.toUpperCase()}
                      />
                    </Grid>
                  ))}
                </>
              ) : (
                <>
                  <RadioGroup
                    value={selectedOptionBlocked}
                    onChange={handleRadioChangeBlocked}
                    aria-label="radio-options"
                  >
                    {dataListBlocked.map((option) => (
                      <FormControlLabel
                        key={option.name}
                        value={option.name.toString()}
                        control={<Radio />}
                        label={option.name?.toUpperCase()}
                        sx={{
                          height: {
                            xs: "3vh",
                          },
                        }}
                      />
                    ))}
                  </RadioGroup>
                </>
              )}
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleCloseDialog}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={openNewChangeDialog}
              variant="contained"
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>
        
        <Dialog
          open={openSelectColumnDialog}
          onClose={handleSelectColumnDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              backgroundColor: "#EAE9FF40",
              display: "flex",
            }}
          >
            <Typography variant="h6">
              Select the field(s) to be changed
            </Typography>

            <IconButton
              sx={{ width: "max-content" }}
              onClick={handleSelectColumnDialogClose}
              children={<CloseIcon />}
            />
          </DialogTitle>
          <DialogContent sx={{ padding: ".5rem 1rem" }}>
            <Grid container>
              {alignment === "Company Code"
                ? dataList?.map((item) => (
                  <Grid item xs={12} key={item.id}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={() => handleSelectionListItem(item)}
                          checked={selectedListItems.some(
                            (selectedItem) => selectedItem.id === item.id
                          )}
                        />
                      }
                      sx={{
                        height: {
                          xs: "3vh",
                        },
                      }}
                      label={item.name}
                    />
                  </Grid>
                ))
                : dataListCOA?.map((item) => (
                  <Grid item xs={12} key={item.id}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          onChange={() => handleSelectionListItemCOA(item)}
                          checked={selectedListItemsCOA.some(
                            (selectedItem) => selectedItem.id === item.id
                          )}
                        />
                      }
                      sx={{
                        height: {
                          xs: "3vh",
                        },
                      }}
                      label={item.name}
                    />
                  </Grid>
                ))}
            </Grid>
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{ width: "max-content", textTransform: "capitalize" }}
              onClick={handleSelectColumnDialogClose}
            >
              Cancel
            </Button>
            <Button
              className="button_primary--normal"
              type="save"
              onClick={handleSelectedColumn}
              variant="contained"
            >
              Apply
            </Button>
          </DialogActions>
        </Dialog>
      </div>
      <ReusableDialog
        dialogState={openMessageDialog}
        openReusableDialog={handleMessageDialogClickOpen}
        closeReusableDialog={handleMessageDialogClose}
        dialogTitle={messageDialogTitle}
        dialogMessage={messageDialogMessage}
        handleDialogConfirm={handleMessageDialogClose}
        dialogOkText={"OK"}
        dialogSeverity={messageDialogSeverity}
      />

      <ReusableDialog
        dialogState={openSearchDialog}
        openReusableDialog={handleSearchDialogClickOpen}
        closeReusableDialog={handleSearchDialogClose}
        dialogTitle={searchDialogTitle}
        dialogMessage={searchDialogMessage}
        handleDialogConfirm={handleSearchDialogClose}
        dialogSeverity={"danger"}
        showCancelButton={false}
        dialogOkText={"OK"}
      />
      <div style={{ ...outermostContainer, backgroundColor: "#FAFCFF" }}>
        <Stack spacing={1}>
          {/* Information */}
          <Grid container sx={outermostContainer_Information}>
            <Grid item md={5} sx={outerContainer_Information}>
              <Typography variant="h3">
                <strong>{t("General Ledger")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the list of General Ledgers")}
              </Typography>
            </Grid>
           
          </Grid>
          <Grid container sx={container_filter}>
            <Grid item md={12}>
              <StyledAccordion defaultExpanded={false} >
                <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: theme.palette.primary.dark }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  className="filterGL"
                >

                  <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark }} />
                  < Typography
                    sx={{
                      fontSize: '0.875rem',
                      fontWeight: 600,
                      color: theme.palette.primary.dark,
                    }}
                  >
                    {t("Filter General Ledger")}
                  </Typography>
                </StyledAccordionSummary>
                < AccordionDetails sx={{ padding: "0.5rem 1rem 0.5rem" }}>
                
                  
                    <Grid
                      container
                      spacing={1}
                      sx={{ padding: "0rem 1rem 0.5rem" }}
                    >
                    {searchParameters?.filter(item => item.MDG_MAT_VISIBILITY !== "Hidden")
                      .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
                      .map((item, index) => {
                        return (
                          <React.Fragment key={index}>
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.CHARTOFACC &&
                              <Grid item md={2} >
                                <LabelTypography>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                </LabelTypography>
                                < FormControl size="small" fullWidth >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    size="small"
                                    value={rmSearchForm?.chartOfAccount}
                                    onChange={handleChartOfAccount}
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    options={dropdownData?.NewChartOfAccounts ?? []}
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return (
                                          `${option?.code} - ${option?.desc}` ?? ""
                                        );
                                      else return "";
                                    }}
                                    renderOption={(props, option) => (
                                      <li {...props} >
                                        <Typography style={{ fontSize: 12 }}>
                                          {option?.desc ? (
                                            <>
                                              <strong>{option?.code} </strong> -{" "}
                                              {option?.desc}
                                            </>
                                          ) : (
                                            <strong>{option?.code} </strong>
                                          )}
                                        </Typography>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <TextField
                                        sx={{ fontSize: "12px !important" }}
                                        {...params}
                                        variant="outlined"
                                        placeholder="Select Chart Of Account"
                                      />
                                    )}
                                  />
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.GLACC &&
                              <Grid item md={2}>
                                <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)} </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    multiple
                                    disableCloseOnSelect
                                    size="small"
                                    value={memoizedGLValue}
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedGeneralLedger([]);
                                        setselectedPresetGeneralLedger([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code === "Select All"
                                      ) {
                                        handleSelectAllGeneralLedger();
                                      } else {
                                        setselectedGeneralLedger(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    limitTags={1}
                                    options={
                                      dropdownData?.GLSearchData?.length
                                        ? [
                                          { code: "Select All", desc: "Select All" },
                                          ...dropdownData?.GLSearchData,
                                        ]
                                        : dropdownData?.GLSearchData ?? []
                                    }
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return `${option?.code}` ?? "";
                                      else return "";
                                    }}
                                    renderOption={(props, option, { selected }) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isGeneralLedgerSelected(option) ||
                                                  (option?.code === "Select All" &&
                                                    selectedGeneralLedger?.length ===
                                                    dropdownData?.GLSearchData
                                                      ?.length)
                                                }
                                              />
                                            }
                                            label={
                                              <>
                                                <strong>{option?.code} </strong> -{" "}
                                                {option?.desc}
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}

                                    renderInput={(params) => {
                                      const originalOnChange = params.inputProps.onChange;

                                      return (
                                        <Tooltip
                                          title={
                                            ccInputValue.length < 4
                                              ? "Enter at least 4 characters"
                                              : ""
                                          }
                                          arrow
                                          disableHoverListener={ccInputValue.length >= 4}
                                          placement="top"
                                        >
                                          <TextField
                                            sx={
                                              {
                                                fontSize: "12px !important",
                                                "& .MuiOutlinedInput-root": {
                                                  height: 35,
                                                },
                                                "& .MuiInputBase-input": {
                                                  padding: "10px 14px",
                                                },
                                              }
                                            }
                                            {...params}
                                            variant="outlined"
                                            placeholder="Select General Ledger"

                                            inputProps={{
                                              ...params.inputProps,
                                              onChange: (event) => {
                                                const cleanedValue = event.target.value.replace(/\s+/g, '');

                                                handleGLInputChange(event);
                                                if (originalOnChange) {
                                                  originalOnChange({
                                                    ...event,
                                                    target: {
                                                      ...event.target,
                                                      value: cleanedValue,
                                                    },
                                                  });
                                                }
                                              },
                                            }}
                                          />
                                        </Tooltip>
                                      )
                                    }}
                                  />
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.COMPANYCODE &&
                              < Grid item md={2} >
                                <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)} </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    size="small"
                                    multiple
                                    limitTags={1}
                                    disableCloseOnSelect
                                    value={
                                      selectedComanyCode?.length > 0
                                        ? selectedComanyCode
                                        : selectedPresetComanyCode?.length > 0
                                          ? selectedPresetComanyCode
                                          : []
                                    }
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedComanyCode([]);
                                        setselectedPresetComanyCode([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code === "Select All"
                                      ) {
                                        handleSelectAllCompanyCodes();
                                      } else {
                                        setselectedComanyCode(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    options={
                                      [
                                        { code: "Select All", desc: "" },
                                        ...(dropdownData?.CompanyCode ?? []),
                                      ]
                                    }
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return (
                                          `${option?.code}` ?? ""
                                        );
                                      else return "";
                                    }}
                                    renderOption={(props, option, { selected }) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isCompanyCodeSelected(option) ||
                                                  (option?.code === "Select All" &&
                                                    selectedComanyCode?.length ===
                                                    dropdownData?.CompanyCode?.length)
                                                }
                                              />
                                            }

                                            label={
                                              <>
                                                <strong>{option?.code} </strong> -{" "}
                                                {option?.desc}
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <TextField
                                        sx={{
                                          fontSize: "12px !important",
                                          "& .MuiOutlinedInput-root": {
                                            height: 35,
                                          },
                                          "& .MuiInputBase-input": {
                                            padding: "10px 14px",
                                          },
                                        }}
                                        {...params}
                                        variant="outlined"
                                        placeholder="Select Company Code"
                                      />
                                    )}
                                  />
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.LONGGLTEXT &&
                              <Grid item md={2}>
                                <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)} </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    size="small"
                                    multiple
                                    disableCloseOnSelect
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedLongText([]);
                                        setselectedPresetLongText([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code === "Select All"
                                      ) {
                                        handleSelectAllLongTexts();
                                      } else {
                                        setselectedLongText(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    limitTags={1}
                                    value={memoizedLDValue}
                                    options={
                                      dropdownData?.GLAcctLongText?.length
                                        ? [
                                          { code: "Select All" },
                                          ...dropdownData?.GLAcctLongText,
                                        ]
                                        : dropdownData?.GLAcctLongText ?? []
                                    }
                                    onInputChange={handleInputChange}
                                    filterOptions={
                                      (options, { inputValue }) =>
                                        options
                                          .filter((option) =>
                                            option?.code
                                              ?.toLowerCase()
                                              ?.startsWith(inputValue?.toLowerCase())
                                          )
                                          .slice(0, 500)
                                    }

                                    getOptionLabel={(option) => {
                                      if (option?.code) return `${option?.code}` ?? "";
                                      else return "";
                                    }}
                                    renderOption={(props, option, { selected }) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isLongTextSelected(option) ||
                                                  (option?.code === "Select All" &&
                                                    selectedLongText?.length ===
                                                    dropdownData?.GLAcctLongText
                                                      ?.length)
                                                }
                                              />
                                            }

                                            label={
                                              <>
                                                <strong>{option?.code} </strong>
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <Tooltip
                                        title={
                                          longTextInputValue?.length < 4
                                            ? "Enter at least 4 characters"
                                            : ""
                                        }
                                        arrow
                                        disableHoverListener={
                                          longTextInputValue?.length >= 4
                                        }
                                        placement="top"
                                      >
                                        <TextField
                                          sx={{
                                            fontSize: "12px !important",
                                            "& .MuiOutlinedInput-root": {
                                              height: 35,
                                            },
                                            "& .MuiInputBase-input": {
                                              padding: "10px 14px",
                                            },
                                          }}
                                          {...params}
                                          variant="outlined"
                                          placeholder="Search Long Text"
                                          onChange={(e) => {
                                            handleLongTextInputChange(e);
                                          }}
                                        />
                                      </Tooltip>
                                    )}
                                  />
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.TAXCATO &&
                              < Grid item md={2} >
                                <LabelTypography>{t(item?.MDG_MAT_UI_FIELD_NAME)} </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    size="small"
                                    multiple
                                    limitTags={1}
                                    value={
                                      selectedTaxCategory?.length > 0
                                        ? selectedTaxCategory
                                        : selectedPresetTaxCategory?.length > 0
                                          ? selectedPresetTaxCategory
                                          : []
                                    }
                                    disableCloseOnSelect
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedTaxCategory([]);
                                        setselectedPresetTaxCategory([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code === "Select All"
                                      ) {
                                        handleSelectAllTaxCategory();
                                      } else {
                                        setselectedTaxCategory(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    options={
                                      dropdownData?.TaxCategory?.length
                                        ? [
                                          { code: "Select All", desc: "" },
                                          ...dropdownData?.TaxCategory,
                                        ]
                                        : dropdownData?.TaxCategory ?? []
                                    }
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return (
                                          `${option?.code}` ?? ""
                                        );
                                      else return "";
                                    }}
                                    renderOption={(props, option, { selected }) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isTaxCategorySelected(option) ||
                                                  (option?.code === "Select All" &&
                                                    selectedTaxCategory?.length ===
                                                    dropdownData?.TaxCategory?.length)
                                                }
                                              />
                                            }

                                            label={
                                              <>
                                                <strong>{`${option?.code} - ${option?.desc}`}</strong>
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <TextField
                                        sx={{
                                          fontSize: "12px !important",
                                          "& .MuiOutlinedInput-root": {
                                            height: 35,
                                          },
                                          "& .MuiInputBase-input": {
                                            padding: "10px 14px",
                                          },
                                        }}
                                        {...params}
                                        variant="outlined"
                                        placeholder="Select Tax Category"
                                      />
                                    )}
                                  />
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.POSTWITHOUTTAX &&
                              < Grid item md={2} >
                                <LabelTypography>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Select
                                    placeholder={"Select Blocking Status"}
                                    sx={{ height: "34.25px" }}
                                    size="small"
                                    value={rmSearchForm?.postingWithoutTaxAllowed}
                                    name="Posting Without Tax Allowed"
                                    onChange={(e) => handlePostingWithoutTaxAllowed(e)}
                                    displayEmpty={true}
                                    MenuProps={MenuProps}
                                  >
                                    <MenuItem sx={font_Small} disabled value={""} >
                                      <div
                                        style={
                                          {
                                            color: "#C1C1C1",
                                            fontSize: "12px",
                                          }
                                        }
                                      >
                                        Select Posting Without Tax Allowed
                                      </div>
                                    </MenuItem>
                                    {postingWithoutTaxValues.map((name) => (
                                      <MenuItem
                                        sx={font_Small}
                                        key={name}
                                        value={name}
                                        style={{
                                          fontSize: "12px !important",
                                          height: "34.25px",
                                        }}
                                      >

                                        <ListItemText
                                          sx={font_Small}
                                          primary={name}
                                          style={{ fontSize: "12px !important" }}
                                        />
                                      </MenuItem>
                                    ))}
                                  </Select>


                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.BLOCKEDPOSTCOA &&
                              < Grid item md={2} >
                                <LabelTypography>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Select
                                    placeholder={"Select Blocking Status"}
                                    sx={{ height: "34.25px" }}
                                    size="small"
                                    value={rmSearchForm?.blockedForPostingInCOA}
                                    name="Posting Without Tax Allowed"
                                    onChange={(e) => handleBlockedForPostingInCOA(e)}
                                    displayEmpty={true}
                                    MenuProps={MenuProps}
                                  >
                                    <MenuItem sx={font_Small} disabled value={""} >
                                      <div
                                        style={
                                          {
                                            color: "#C1C1C1",
                                            fontSize: "12px",
                                          }
                                        }
                                      >
                                        Select Blocked For Posting In COA
                                      </div>
                                    </MenuItem>
                                    {
                                      blockedForPostingInCOAValues?.map((name) => (
                                        <MenuItem
                                          sx={font_Small}
                                          key={name}
                                          value={name}
                                          style={{
                                            fontSize: "12px !important",
                                            height: "34.25px",
                                          }}
                                        >
                                          {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                          < ListItemText
                                            sx={font_Small}
                                            primary={name}
                                            style={{ fontSize: "12px !important" }}
                                          />
                                        </MenuItem>
                                      ))}
                                  </Select>
                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.BLOKEDPOSTCOMP &&
                              < Grid item md={2} >
                                <LabelTypography>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Select
                                    placeholder={
                                      "Select Blocked For Posting In Company"
                                    }
                                    sx={{ height: "34.25px" }}
                                    size="small"
                                    value={rmSearchForm?.blockedForPostingInCompany}
                                    name="Blocked For Posting In Company"
                                    onChange={(e) =>
                                      handleBlockedForPostingInCompany(e)
                                    }
                                    displayEmpty={true}
                                    MenuProps={MenuProps}
                                  >
                                    <MenuItem sx={font_Small} disabled value={""} >
                                      <div
                                        style={
                                          {
                                            color: "#C1C1C1",
                                            fontSize: "12px",
                                          }
                                        }
                                      >
                                        Select Blocked For Posting In Company
                                      </div>
                                    </MenuItem>
                                    {
                                      blockedForPostingInCompanyValues?.map((name) => (
                                        <MenuItem
                                          sx={font_Small}
                                          key={name}
                                          value={name}
                                          style={{
                                            fontSize: "12px !important",
                                            height: "34.25px",
                                          }}
                                        >
                                          {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                          < ListItemText
                                            sx={font_Small}
                                            primary={name}
                                            style={{ fontSize: "12px !important" }}
                                          />
                                        </MenuItem>
                                      ))}
                                  </Select>


                                </FormControl>
                              </Grid>}
                            {item?.MDG_MAT_JSON_FIELD_NAME === SEARCH_FIELD_TYPES.POSTONLY &&
                              < Grid item md={2} >
                                <LabelTypography>
                                  {t(item?.MDG_MAT_UI_FIELD_NAME)}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Select
                                    placeholder={"Select Post Automatically Only"}
                                    sx={{ height: "34.25px" }}
                                    size="small"
                                    value={rmSearchForm?.postAutoOnly}
                                    name="Open Item Management"
                                    onChange={(e) => handlePostAutoOnly(e)}
                                    displayEmpty={true}
                                    MenuProps={MenuProps}
                                  >
                                    <MenuItem sx={font_Small} disabled value={""} >
                                      <div
                                        style={
                                          {
                                            color: "#C1C1C1",
                                            fontSize: "12px",
                                          }
                                        }
                                      >
                                        Select Post Automatically Only
                                      </div>
                                    </MenuItem>
                                    {
                                      postAutoOnlyValues?.map((name) => (
                                        <MenuItem
                                          sx={font_Small}
                                          key={name}
                                          value={name}
                                          style={{
                                            fontSize: "12px !important",
                                            height: "34.25px",
                                          }}
                                        >
                                          {/* <Checkbox
    checked={
    rbSearchForm?.reqStatus.indexOf(name) > -1
    }
  /> */}
                                          < ListItemText
                                            sx={font_Small}
                                            primary={name}
                                            style={{ fontSize: "12px !important" }}
                                          />
                                        </MenuItem>
                                      ))}
                                  </Select>


                                </FormControl>
                              </Grid>}
                          </React.Fragment>)
                      })}
                    
                    <Grid item md={2}>
                      <LabelTypography sx={font_Small}>{t("Add New Filters")}</LabelTypography>
                      <FormControl sx={{ width: "100%" }}>
                        <Select
                          sx={{
                            font_Small,
                            fontSize: "12px",
                            width: "100%",
                          }}
                          size="small"
                          multiple
                          limitTags={2}
                          value={selectedOptions}
                          onChange={handleSelection}
                          renderValue={(selected) => selected.join(", ")}
                          MenuProps={{
                            MenuProps,
                          }}
                          endAdornment={
                            selectedOptions.length > 0 && (
                              <InputAdornment position="end" sx={{ marginRight: '10px' }}>
                                <IconButton
                                  size="small"
                                  onClick={() => setSelectedOptions([])}
                                  aria-label="Clear selections"
                                >
                                  <ClearIcon />
                                </IconButton>
                              </InputAdornment>
                            )
                          }
                        >
                          {items.map((option) => (
                            <MenuItem key={option.title} value={option.title}>
                              <Checkbox
                                checked={
                                  selectedOptions.indexOf(option.title) > -1
                                }
                              />
                              {option.title}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                      <Grid
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      ></Grid>
                    </Grid>

                    < Grid
                      container
                      sx={{ flexDirection: "row", padding: "0rem 1rem 0.5rem" }}
                      gap={1}
                    >
                      {selectedOptions?.map((option, i) => {
                        if (option === "Created By") {
                          return (
                            <>
                              <Grid item md={2} >
                                <LabelTypography>
                                  {t("Created By")}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    multiple
                                    disableCloseOnSelect
                                    size="small"
                                    value={
                                      selectedCreatedBy?.length > 0
                                        ? selectedCreatedBy
                                        : selectedPresetCreatedBy?.length > 0
                                          ? selectedPresetCreatedBy
                                          : []
                                    }
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedCreatedBy([]);
                                        setselectedPresetCreatedBy([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code ===
                                        "Select All"
                                      ) {
                                        handleSelectAllCreatedBy();
                                      } else {
                                        setselectedCreatedBy(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    limitTags={1}
                                    options={
                                      dropdownData?.CreatedBySearchGL?.length
                                        ? [
                                          { code: "Select All" },
                                          ...dropdownData?.CreatedBySearchGL,
                                        ]
                                        : dropdownData?.CreatedBySearchGL ?? []
                                    }
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return option?.code ?? "";
                                      else return "";
                                    }}
                                    renderOption={(
                                      props,
                                      option,
                                      { selected }
                                    ) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isCreatedBySelected(option) ||
                                                  (option?.code ===
                                                    "Select All" &&
                                                    selectedCreatedBy?.length ===
                                                    dropdownData
                                                      ?.CreatedBySearchGL
                                                      ?.length)
                                                }
                                              />
                                            }
                                            label={
                                              <>
                                                <strong>{option?.code} </strong>
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <TextField
                                        sx={{
                                          fontSize: "12px !important",
                                          "& .MuiOutlinedInput-root": {
                                            height: 35,
                                          },
                                          "& .MuiInputBase-input": {
                                            padding: "10px 14px",
                                          },
                                        }}
                                        {...params}
                                        variant="outlined"
                                        placeholder="Enter Created By"
                                      />
                                    )}
                                  />
                                </FormControl>
                              </Grid>
                             
                            </>
                          );
                        } else if (option === "Short Text") {
                          return (
                            <>
                              <Grid item md={2} >
                                <LabelTypography>
                                  {t("Short Text")}
                                </LabelTypography>
                                < FormControl fullWidth size="small" >
                                  <Autocomplete
                                    sx={{ height: "31px" }}
                                    fullWidth
                                    size="small"
                                    multiple
                                    disableCloseOnSelect
                                    noOptionsText={
                                      isDropDownLoading ? (
                                        <Box
                                          sx={{
                                            display: "flex",
                                            justifyContent: "center",
                                            mt: 1,
                                            zIndex: 9999,
                                            top: "10px",
                                          }}
                                        >
                                          <CircularProgress size={20} />
                                        </Box>
                                      ) : (
                                        "No Data Available"
                                      )
                                    }
                                    onChange={(e, value, reason) => {
                                      if (reason === "clear" || value?.length === 0) {
                                        setselectedShortText([]);
                                        setselectedPresetShortText([]);
                                        return;
                                      }

                                      if (
                                        value?.length > 0 &&
                                        value[value?.length - 1]?.code ===
                                        "Select All"
                                      ) {
                                        handleSelectAllShortTexts();
                                      } else {
                                        setselectedShortText(value);
                                      }
                                    }}
                                    renderTags={(value, getTagProps) =>
                                      value?.length > 0 ? (
                                        <>
                                          <Chip
                                            label={value[0]?.code}
                                            {...getTagProps({ index: 0 })}
                                            sx={{
                                              height: 20,
                                              fontSize: "0.75rem",
                                              '.MuiChip-label': { padding: "0 6px" }
                                            }}
                                          />
                                          {
                                            value?.length > 1 && (
                                              <Chip
                                                label={`+${value?.length - 1}`}
                                                sx={{
                                                  height: 20,
                                                  fontSize: "0.75rem",
                                                  '.MuiChip-label': { padding: "0 6px" }
                                                }
                                                }
                                              />
                                            )}
                                        </>
                                      ) : null
                                    }
                                    limitTags={1}
                                    value={memoizedSDValue}
                                    options={
                                      dropdownData?.ShortTextSearchGL?.length
                                        ? [
                                          { code: "Select All" },
                                          ...dropdownData?.ShortTextSearchGL,
                                        ]
                                        : dropdownData?.ShortTextSearchGL ?? []
                                    }
                                    getOptionLabel={(option) => {
                                      if (option?.code)
                                        return option?.code ?? "";
                                      else return "";
                                    }}
                                    renderOption={(
                                      props,
                                      option,
                                      { selected }
                                    ) => (
                                      <li {...props} >
                                        <FormGroup>
                                          <FormControlLabel
                                            control={
                                              < Checkbox
                                                checked={
                                                  isShortTextSelected(option) ||
                                                  (option?.code ===
                                                    "Select All" &&
                                                    selectedShortText?.length ===
                                                    dropdownData
                                                      ?.ShortTextSearchGL
                                                      ?.length)
                                                }
                                              />
                                            }
                                            label={
                                              <>
                                                <strong>{option?.code} </strong>
                                              </>
                                            }
                                          />
                                        </FormGroup>
                                      </li>
                                    )}
                                    renderInput={(params) => (
                                      <Tooltip
                                        title={
                                          shortTextInputValue?.length < 4
                                            ? "Enter at least 4 characters"
                                            : ""
                                        }
                                        arrow
                                        disableHoverListener={
                                          shortTextInputValue?.length >= 4
                                        }
                                        placement="top"
                                      >
                                        <TextField
                                          sx={
                                            {
                                              fontSize: "12px !important",
                                              "& .MuiOutlinedInput-root": {
                                                height: 35,
                                              },
                                              "& .MuiInputBase-input": {
                                                padding: "10px 14px",
                                              },
                                            }
                                          }
                                          {...params}
                                          variant="outlined"
                                          placeholder="Search Short Text"
                                          onChange={(e) => {
                                            handleShortTextInputChange(e);
                                          }}
                                        />
                                      </Tooltip>
                                    )}
                                  />
                                </FormControl>
                              </Grid>
                              {/* <Grid item md={2}>
                              <Typography sx={font_Small}>{option}</Typography>
                              <FormControl size="small" fullWidth>
                                <TextField
                                  sx={{ fontSize: "12px !important" }}
                                  fullWidth
                                  size="small"
                                  value={rmSearchForm?.GLname}
                                  onChange={handleShortText}
                                  placeholder="Enter Short Text"
                                />
                              </FormControl>
                            </Grid> */}
                            </>
                          );
                        } else if (
                          option === "Open Item Mgmt by Ledger Group"
                        ) {
                          return (
                            <Grid item md={2} >
                              <LabelTypography>{t(option)} </LabelTypography>
                              < FormControl size="small" fullWidth >
                                <Select
                                  placeholder={
                                    "Select Open Item Mgmt by Ledger Group"
                                  }
                                  sx={{ height: "34.25px" }
                                  }
                                  size="small"
                                  value={
                                    rmSearchForm?.openItemMgmtByLedgerGroup
                                  }
                                  name="Open Item Mgmt by Ledger Group"
                                  onChange={(e) =>
                                    handleOpenItemMgmtByLedgerGroup(e)
                                  }
                                  displayEmpty={true}
                                  MenuProps={MenuProps}
                                >
                                  <MenuItem sx={font_Small} disabled value={""} >
                                    <div
                                      style={
                                        {
                                          color: "#C1C1C1",
                                          fontSize: "12px",
                                        }
                                      }
                                    >
                                      Select Open Item Mgmt by Ledger Group
                                    </div>
                                  </MenuItem>
                                  {
                                    openItemManagementByLedgerGroupValues?.map(
                                      (name) => (
                                        <MenuItem
                                          sx={font_Small}
                                          key={name}
                                          value={name}
                                          style={{
                                            fontSize: "12px !important",
                                            height: "34.25px",
                                          }}
                                        >
                                          {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                          < ListItemText
                                            sx={font_Small}
                                            primary={name}
                                            style={{
                                              fontSize: "12px !important",
                                            }}
                                          />
                                        </MenuItem>
                                      )
                                    )}
                                </Select>
                              </FormControl>
                            </Grid>
                          );
                        } else if (option === "Open Item Management") {
                          return (
                            <Grid item md={2} >
                              <LabelTypography>{t(option)} </LabelTypography>
                              < FormControl size="small" fullWidth >
                                <Select
                                  placeholder={"Select Open Item Management"}
                                  sx={{ height: "34.25px" }
                                  }
                                  size="small"
                                  value={rmSearchForm?.openItemManagement}
                                  name="Open Item Management"
                                  onChange={(e) => handleOpenItemManagement(e)}
                                  displayEmpty={true}
                                  MenuProps={MenuProps}
                                >
                                  <MenuItem sx={font_Small} disabled value={""} >
                                    <div
                                      style={
                                        {
                                          color: "#C1C1C1",
                                          fontSize: "12px",
                                        }
                                      }
                                    >
                                      Select Open Item Management
                                    </div>
                                  </MenuItem>
                                  {
                                    openItemManagementValues?.map((name) => (
                                      <MenuItem
                                        sx={font_Small}
                                        key={name}
                                        value={name}
                                        style={{
                                          fontSize: "12px !important",
                                          height: "34.25px",
                                        }}
                                      >
                                        {/* <Checkbox
                                  checked={
                                    rbSearchForm?.reqStatus.indexOf(name) > -1
                                  }
                                /> */}
                                        < ListItemText
                                          sx={font_Small}
                                          primary={name}
                                          style={{ fontSize: "12px !important" }}
                                        />
                                      </MenuItem>
                                    ))}
                                </Select>
                              </FormControl>
                            </Grid>
                          );
                        } else if (option === "Created On") {
                          return (
                            <Grid item md={4} >
                              <LabelTypography>{t(option)} </LabelTypography>
                              < FormControl size="small" fullWidth >
                                <LocalizationProvider
                                  dateAdapter={AdapterDateFns}
                                >
                                  <DateRange
                                    handleDate={handleDate}
                                    date={seletedDateRange}
                                  />
                                </LocalizationProvider>
                              </FormControl>
                            </Grid>
                          );
                        } else {
                          return (
                            <Grid item md={2} >
                              <LabelTypography>{option} </LabelTypography>
                              < FormControl fullWidth size="small" >
                                <Autocomplete
                                  sx={{ height: "31px" }}
                                  fullWidth
                                  multiple
                                  disableCloseOnSelect
                                  size="small"
                                  value={
                                    selectedValues[option]?.length > 0
                                      ? selectedValues[option]
                                      : selectedPresetValues[option]?.length > 0
                                        ? selectedPresetValues[option]
                                        : []
                                  }
                                  noOptionsText={
                                    isDropDownLoading ? (
                                      <Box
                                        sx={{
                                          display: "flex",
                                          justifyContent: "center",
                                          mt: 1,
                                          zIndex: 9999,
                                          top: "10px",
                                        }}
                                      >
                                        <CircularProgress size={20} />
                                      </Box>
                                    ) : (
                                      "No Data Available"
                                    )
                                  }
                                  onChange={(e, value, reason) => {
                                    if (reason === "clear" || value?.length === 0) {
                                      setSelectedValues((prev) => ({
                                        ...prev,
                                        [option]: [],
                                      }));
                                      setSelectedPresetValues((prev) => ({
                                        ...prev,
                                        [option]: [],
                                      }));
                                      return;
                                    }

                                    if (
                                      value?.length > 0 &&
                                      value[value?.length - 1]?.code ===
                                      "Select All"
                                    ) {
                                      handleSelectAllOptions(option);
                                    } else {
                                      setSelectedValues((prev) => ({
                                        ...prev,
                                        [option]: value,
                                      }));
                                    }
                                  }}
                                  renderTags={(value, getTagProps) =>
                                    value?.length > 0 ? (
                                      <>
                                        <Chip
                                          label={value[0]?.code}
                                          {...getTagProps({ index: 0 })}
                                          sx={{
                                            height: 20,
                                            fontSize: "0.75rem",
                                            '.MuiChip-label': { padding: "0 6px" }
                                          }}
                                        />
                                        {
                                          value?.length > 1 && (
                                            <Chip
                                              label={`+${value?.length - 1}`}
                                              sx={{
                                                height: 20,
                                                fontSize: "0.75rem",
                                                '.MuiChip-label': { padding: "0 6px" }
                                              }
                                              }
                                            />
                                          )}
                                      </>
                                    ) : null
                                  }
                                  limitTags={1}
                                  options={
                                    dynamicOptions?.[option]?.length
                                      ? [
                                        { code: "Select All", desc: "." },
                                        ...dynamicOptions?.[option],
                                      ]
                                      : dynamicOptions?.[option] ?? []
                                  }
                                  getOptionLabel={(option) =>
                                    option?.code
                                      ? `${option?.code} - ${option?.desc}`
                                      : ""
                                  }
                                  renderOption={(
                                    props,
                                    dropdownOption,
                                    { selected }
                                  ) => (
                                    <li {...props} >
                                      <FormGroup>
                                        <FormControlLabel
                                          control={
                                            < Checkbox
                                              checked={
                                                isOptionSelected(
                                                  option,
                                                  dropdownOption
                                                ) ||
                                                (dropdownOption?.code ===
                                                  "Select All" &&
                                                  selectedValues[option]
                                                    ?.length ===
                                                  dynamicOptions?.[option]
                                                    ?.length)
                                              }
                                            />
                                          }
                                          label={
                                            <>
                                              <strong>
                                                {`${dropdownOption?.code} - ${dropdownOption?.desc}`}
                                              </strong>
                                            </>
                                          }
                                        />
                                      </FormGroup>
                                    </li>
                                  )}
                                  renderInput={(params) => (
                                    <TextField
                                      sx={{
                                        fontSize: "12px !important",
                                        "& .MuiOutlinedInput-root": {
                                          height: 35,
                                        },
                                        "& .MuiInputBase-input": {
                                          padding: "10px 14px",
                                        },
                                      }}
                                      {...params}
                                      variant="outlined"
                                      placeholder={`Select ${option}`}
                                    />
                                  )}
                                />
                              </FormControl>
                            </Grid>
                          );
                        }
                      })}
                    </Grid>
                  </Grid>
                  
                  
                  <ButtonContainer>
                    <ActionButton
                      variant="outlined"
                      size="small"
                      startIcon={< ClearIcon sx={{ fontSize: '1rem' }} />}
                      onClick={() => {
                        handleClear();
                      }}>
                      Clear
                    </ActionButton>
                    <Grid sx={{ ...button_Marginleft }}>
                      <ReusablePreset
                        moduleName={"GeneralLedger"}
                        PresetObj={PresetObj}
                        handleSearch={getFilter}
                        PresetMethod={PresetMethod}
                      />
                    </Grid>
                    {/* <Grid> */}
                    <ActionButton
                      variant="contained"
                      size="small"
                      startIcon={< SearchIcon sx={{ fontSize: '1rem' }} />}
                      sx={{ ...button_Primary, ...button_Marginleft }}
                      onClick={getFilter}
                    >
                      Search
                    </ActionButton>
                  </ButtonContainer>
                  
                 
                </AccordionDetails>
              </StyledAccordion>
            </Grid>
          </Grid>

          <Grid item sx={{ position: "relative" }}>
            <Stack>
              <ReusableTable
                isLoading={isLoading}

                module={"MaterialMaster"}
                width="100%"
                title={"List of General Ledgers (" + roCount + ")"}
                rows={rmDataRows}
                columns={dynamicColumns ?? []}
                showSearch={true}
                showRefresh={true}
                showSelectedCount={true}
                showExport={true}
                onSearch={(value) => handleSearchAction(value)}
                onRefresh={refreshPage}
                pageSize={pageSize}
                page={page}
                onPageSizeChange={handlePageSizeChange}
                rowCount={roCount ?? rmDataRows?.length ?? 0}
                onPageChange={handlePageChange}
                getRowIdValue={"id"}
                hideFooter={true}
                checkboxSelection={true}
                disableSelectionOnClick={true}
                status_onRowSingleClick={true}
                tempheight={'calc(100vh - 320px)'}
                onRowsSelectionHandler={onRowsSelectionHandler}
                callback_onRowSingleClick={(params) => {
                  const generalLedger = params?.row?.glAccount; 
                  navigate(
                    `/masterDataCockpit/generalLedger/displayGeneralLedgerMasterdata/${generalLedger}`,

                    {
                      state: params.row,
                    }
                  );
                }}
                showFirstPageoptions={true}
                showSelectAllOptions={true}
                onSelectAllOptions={handleSelectAllData}
                showCustomNavigation={true}
                stopPropagation_Column={"action"}
                status_onRowDoubleClick={true}
              />
            </Stack>
          </Grid>
          <Paper
            sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
            elevation={2}
          >
            <BottomNavigation
              className="container_BottomNav"
              showLabels
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
              value={value}
              onChange={(newValue) => {
                setValue(newValue);
              }}
            >

              <ButtonGroup
                variant="contained"
                ref={anchorRefChange}
                aria-label="split button"
              >
                <Button
                  size="small"
                  onClick={() => {
                    dispatch(resetPayloadDataGL())

                    navigate("/requestBench/GeneralLedgerRequestTab", {
                      state: {
                        steaperData: [
                          "Request Header",
                          "General Ledger List",
                          "Attachments & Comments",
                        ],
                        moduleName: "GeneralLedger",
                      },
                    });
                  }}
                  sx={{ cursor: "pointer" }}
                  className="createRequestButtonGL"
                >
                  Create Request
                </Button>
              </ButtonGroup>

              {enableDocumentUpload && (
                <AttachmentUploadDialog
                  artifactId=""
                  artifactName=""
                  setOpen={setEnableDocumentUpload}
                  handleUpload={uploadExcel}
                />
              )}
            </BottomNavigation>
          </Paper>

        </Stack>
      </div>
    </div>
  );
};

export default GeneralLedger;
