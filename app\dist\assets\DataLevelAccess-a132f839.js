import{r as e,cb as i}from"./index-226a1e75.js";import{i as s}from"./index-20540f9d.js";import"./react-beautiful-dnd.esm-0de399b3.js";import"./redux-49302a50.js";import"./index-ec49c19e.js";import"./index-0132bc09.js";import"./Check-30add394.js";import"./FileUploadOutlined-3ff8ee58.js";import"./DeleteOutline-259b9549.js";import"./Delete-3f2fc9ef.js";import"./asyncToGenerator-88583e02.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./AddOutlined-eac9d1ec.js";import"./DeleteOutlineOutlined-d41ebb56.js";import"./EditOutlined-b0a055aa.js";import"./Edit-3af3a8b3.js";import"./index-2a7424d8.js";import"./index-9c81b930.js";import"./DataObject-2e0c0294.js";import"./lz-string-127b8448.js";import"./VisibilityOutlined-b2b52c11.js";import"./Remove-98bfc4ee.js";import"./ChevronRight-8fea09f9.js";import"./index-62551ef3.js";import"./DeleteOutlined-9dca1b70.js";import"./index-2709f66d.js";import"./History-09ae589c.js";let p=class extends e.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r,o){console.error("Error caught in ErrorBoundary:",r,o)}render(){return this.state.hasError?i.jsx("div",{children:"Error fetching data"}):this.props.children}};const k=({roleDetails:t,destinations:r})=>i.jsx(p,{children:t&&i.jsx(s.TextRules,{orchestration:!1,ruleDetails:t,destinations:r,saveHandler:o=>console.log(o),translationDataObjects:[]})});export{k as default};
