import React, { useEffect, useState, useMemo, useRef } from "react";
import { Box, Button, DialogActions, DialogContent, Grid, Icon<PERSON>utton, Step, StepButton, Stepper, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { ArrowCircleLeftOutlined, WarningOutlined } from "@mui/icons-material";
import { useNavigate, useLocation } from "react-router-dom";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { button_Outlined, button_Primary } from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import RequestHeaderIO from "./RequestHeaderIO";
import useGenericDtCall from "@hooks/useGenericDtCall";
import { useDispatch, useSelector } from "react-redux";
import { setTabValue, resetInternalOrderState } from "./slice/InternalOrderSlice";
import { setLocalStorage } from "@helper/glhelper";
import { LOCAL_STORAGE_KEYS, MODULE_MAP, ARTIFACTNAMES, REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import RequestDetailsIO from "./RequestDetailsIO";
import useIOdropdownData from "./hooks/useIOdropdownData";
import useDisplayInternalOrderData from "./hooks/useDisplayInternalOrderData";
import useRequestHeaderFieldsIO from "./hooks/useRequestHeaderFieldsIO";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import PreviewPage from "@components/RequestBench/PreviewPage";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard";
import useDownloadExcel from "@hooks/useDownloadExcel";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import ChangeLogGlobal from "@components/Changelog/ChangeLogGlobal";

const CreateRequestforIO = () => {
  const { t } = useLang();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
  const reqBench = queryParams.get("reqBench");
  const rowData = location.state;

  const { getDtCall: getAttachmentDt, dtData: attachmentDtData } = useGenericDtCall();
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const { fetchAllDropdownIOData } = useIOdropdownData();
  const { getDisplayInternalOrderData, loading: displayLoading, error: displayError } = useDisplayInternalOrderData();
  const savedRequestData = useSelector((state) => state.internalOrder.savedReqData);
  const tabValue = useSelector((state) => state.internalOrder.tabValue);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [displayDataLoaded, setDisplayDataLoaded] = useState(false);
  const [lastLoadedRequestId, setLastLoadedRequestId] = useState(null);
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const { handleUploadMaterial } = useDownloadExcel(MODULE_MAP?.IO);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);

  const steps = [t("Request Header"), t("Internal Order List"), t("Attachments & Remarks"), t("Preview")];
  useRequestHeaderFieldsIO({ requestId, reqBench });

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.INTERNAL_ORDER);
    }
  };
  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };
  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const handleUploadMaterialIO = (file) => {
    handleUploadMaterial(file, setLoaderMessage, setBlurLoading, IOpayloadData, MODULE_MAP?.IO, RequestType, requestId, rowData);
  };

  const fetchAttachmentFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": MODULE_MAP.IO,
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    getAttachmentDt(payload);
  };
  const handleExportTemplateExcel = () => {
    //NOTE : will remove console when the functionality is implemented
    console.log("here download works");
  };

  useEffect(() => {
    if (attachmentDtData) {
      let responseData = attachmentDtData?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
      const attachmentNames = responseData || [];

      // If no attachment types are configured for Internal Order, provide a default
      if (attachmentNames.length === 0) {
        const defaultAttachmentType = [
          {
            MDG_ATTACHMENTS_NAME: "Document",
            MDG_ATTACH_CHNG_ENT_FIELDS: "",
          },
        ];
        setAttachmentsData(defaultAttachmentType);
      } else {
        setAttachmentsData(attachmentNames);
      }
    }
  }, [attachmentDtData]);

  useEffect(() => {
    fetchAttachmentFieldsFromDt();
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.IO);
  }, [requestId, reqBench]);

  useEffect(() => {
    fetchAllDropdownIOData();
  }, []);

  useEffect(() => {
    const loadInternalOrderData = async () => {
      if (requestId) {
        await getDisplayInternalOrderData(requestId, null, reqBench, null);

        const requestStatus = IOpayloadData?.requestHeaderData?.RequestStatus;

        const isDraftOrFailed = requestStatus === REQUEST_STATUS.DRAFT || requestStatus === REQUEST_STATUS.UPLOAD_FAILED;

        const isChangeWithUpload = RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD;
        const isCreateWithUpload = RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD;

        const hasEmptyBody = !IOpayloadData?.rowsBodyData || Object.keys(IOpayloadData?.rowsBodyData || {}).length === 0;

        if ((isChangeWithUpload && hasEmptyBody) || isCreateWithUpload) {
          if (isDraftOrFailed) {
            dispatch(setTabValue(0));
            setIsSecondTabEnabled(false);
            setIsAttachmentTabEnabled(false);
            return;
          }
        }

        dispatch(setTabValue(1));
        setIsSecondTabEnabled(true);
        setIsAttachmentTabEnabled(true);
      } else {
        dispatch(setTabValue(0));
      }
    };

    loadInternalOrderData();
  }, [requestId]);

  useEffect(() => {
    return () => {
      dispatch(resetInternalOrderState());
    };
  }, []);

  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Typography variant="h6" sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Request Header ID")}: <span>{savedRequestData?.RequestId || IOpayloadData?.requestHeaderData?.RequestId}</span>
          </Typography>
          {tabValue === 1 && (
            <Box sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}>
              <Button variant="outlined" size="small" title="Download Error Report" disabled={!requestId} onClick={() => setDialogOpen(true)} color="primary">
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              <Button variant="outlined" disabled={false} size="small" onClick={openChangeLog} title="Change Log">
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>
              <Button variant="outlined" disabled={!requestId} size="small" onClick={handleExportTemplateExcel} title="Export Excel">
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>

        <IconButton onClick={() => setisDialogVisible(true)} color="primary" title={t("Back")} sx={{ left: "-10px" }}>
          <ArrowCircleLeftOutlined sx={{ fontSize: "25px", color: "#000000" }} />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{ justifyContent: "center", margin: "25px 14%", marginTop: "-35px" }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton onClick={() => handleTabChange(index)}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{label}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {isChangeLogopen && <ChangeLogGlobal open={true} closeModal={() => setisChangeLogopen(false)} requestId={savedRequestData?.RequestId || requestId} requestType={RequestType || IOpayloadData?.requestHeaderData?.RequestType} module={MODULE_MAP?.IO} />}

        <ErrorReportDialog dialogState={dialogOpen} closeReusableDialog={() => setDialogOpen(false)} module={MODULE_MAP?.BK} />

        <Box sx={{ padding: "20px", borderRadius: "8px" }}>
          {tabValue === 0 && (
            <>
              <RequestHeaderIO setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} />
              {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.objectNumbers !== "Not Available") || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && <ExcelOperationsCard handleDownload={handleDownload} setEnableDocumentUpload={setEnableDocumentUpload} enableDocumentUpload={enableDocumentUpload} handleUploadMaterial={handleUploadMaterialIO} />}
            </>
          )}
          {tabValue === 1 && <RequestDetailsIO requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} />}
          {tabValue === 2 && <AttachmentsCommentsTab requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} attachmentsData={attachmentsData} requestIdHeader={savedRequestData?.RequestId} pcNumber={savedRequestData?.RequestId} module={MODULE_MAP?.IO} artifactName={ARTIFACTNAMES.IO} />}
          {tabValue === 3 && (
            <Box
              sx={{
                width: "100%",
                overflow: "auto",
              }}
            >
              <PreviewPage requestStatus={REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} module={MODULE_MAP?.IO} payloadData={IOpayloadData} payloadForDownloadExcel={IOpayloadData} />
            </Box>
          )}
        </Box>
      </Box>

      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined sx={{ color: colors.secondary.amber, fontSize: "20px" }} />} Title={t("Warning")} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{t("Are you sure you want to leave this page?")}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={button_Outlined} onClick={handleCancel}>
              {t("No")}
            </Button>
            <Button variant="contained" size="small" sx={button_Primary} onClick={handleYes}>
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default CreateRequestforIO;
