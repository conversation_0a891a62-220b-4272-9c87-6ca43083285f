import React, { useEffect, useState } from "react";
import { DataGrid } from "@mui/x-data-grid";
import {
  IconButton,
  Box,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  MenuItem,
  Autocomplete
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
import { colors } from "@constant/colors";
import { useSelector } from "react-redux";
import { END_POINTS } from "@constant/apiEndPoints";
import { useDispatch } from "react-redux";
import { doAjax } from "@components/Common/fetchService";
import { destination_ArticleMgmt } from "../../../destinationVariables";
import { API_CODE } from "@constant/enum";
import { setMultipleMaterialHeaderKey, updateMaterialData } from "@app/payloadSlice";
import { fetchClassByType } from "@helper/helper";
import useLogger from "@hooks/useLogger";

const ClassificationViewArticle = (props) => {
  const { customError } = useLogger();
  const [editOpen, setEditOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const payloadState = useSelector((state) => state.payload);
  const [rows, setRows] = useState(payloadState?.[props.materialID]?.payloadData?.Classification?.classification || []);
  const [values, setValues] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    if (payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classtype) {
      fetchClassByType(payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classtype, dispatch);
    }
  }, [payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classtype])
  useEffect(() => {
    if (payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classnum) {
      if (!rows?.length || (rows?.length && rows[0]?.className != payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classnum)) {
        fetchCharacteristics(payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classnum);
      }
    }
  }, [payloadState?.[props.materialID]?.payloadData?.Classification?.basic?.Classnum])
  useEffect(() => {
    dispatch(
      updateMaterialData({
        materialID: props?.materialID || "",
        keyName: "",
        data: rows ?? null,
        viewID: props?.activeViewTab,
        itemID: 'classification',
      })
    );
  }, [rows])
  const fetchCharacteristics = (className) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        const mappedRows = data.body.map((character, idx) => ({
          id: idx + 1,
          characteristic: character.code,
          description: character.desc,
          value: "",
          className: className
        }));
        setRows(mappedRows);
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(`/${destination_ArticleMgmt}${END_POINTS.DATA.GET_CHARACTERISTICS_BY_CLASS}?className=${className}`, "get", hSuccess, hError);
  }
  const handleEditClick = (row) => {
    setSelectedRow(row);
    setEditOpen(true);
    getValuesForCharacteristic(row.characteristic);
  };
  const getValuesForCharacteristic = (characteristic) => {
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setValues(data.body);
      }
    };
    const hError = (error) => {
      customError(error);
    };
    doAjax(`/${destination_ArticleMgmt}${END_POINTS.DATA.GET_CHARACTERISTIC_VALUES}?characteristics=${characteristic}`, "get", hSuccess, hError);
  }

  const handleEditClose = () => {
    setEditOpen(false);
    setSelectedRow(null);
  };

  const handleValueChange = (valueArray) => {
    setSelectedRow((prev) => ({
      ...prev,
      value: valueArray,
    }));
  };

  const handleSave = () => {
    setRows((prevRows) =>
      prevRows.map((row) =>
        row.id === selectedRow.id ? { ...row, value: selectedRow.value } : row
      )
    );

    setEditOpen(false);
  };

  const columns = [
    {
      field: "characteristic",
      headerName: "Characteristic",
      flex: 1,
      headerClassName: "super-app-theme--header",
      renderHeader: () => (
        <Typography variant="body2" fontWeight="bold">Characteristic</Typography>
      ),
    },
    {
      field: "description",
      headerName: "Description",
      flex: 2,
      headerClassName: "super-app-theme--header",
      renderHeader: () => (
        <Typography variant="body2" fontWeight="bold">Description</Typography>
      ),
    },
    {
      field: "value",
      flex: 1,
      headerAlign: "left",
      align: "left",
      headerClassName: "super-app-theme--header",
      renderHeader: () => (
        <Box sx={{ display: "flex", alignItems: "center" }}>
          <Typography variant="body2" fontWeight="bold">Value</Typography>
          <Typography color="error" sx={{ ml: 0.5 }}>*</Typography>
        </Box>
      ),
      renderCell: (params) => (
        <span>
          {
            Array.isArray(params.value) && params.value.length > 1 ? (
              <>
                {params.value[0]}...
                <span style={{ verticalAlign: "middle" }}>
                  <span
                    style={{ cursor: "pointer", color: "#888" }}
                    title={params.value.join(", ")}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      height="22"
                      width="22"
                      viewBox="0 0 24 24"
                      style={{ verticalAlign: "middle" }}
                    >
                      <circle cx="12" cy="12" r="10" fill="#e0e0e0" />
                      <text x="12" y="17" textAnchor="middle" fontSize="16" fill="#555" fontFamily="Arial" fontWeight="bold">i</text>
                    </svg>
                  </span>
                </span>
              </>
            ) :
              params.value.length === 1 ? (
                params.value
              ) : "--"
          }

        </span>
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      headerClassName: "super-app-theme--header",
      renderHeader: () => (
        <Typography variant="body2" fontWeight="bold">Actions</Typography>
      ),
      renderCell: (params) => (
        <IconButton color="primary" size="small" onClick={() => handleEditClick(params.row)}>
          <EditIcon />
        </IconButton>
      ),
    }
  ];


  return (
    <Box sx={{
      backgroundColor: "white",
      border: `1px solid ${colors.hover.hoverbg}`,
      borderRadius: "8px",
      boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
      px: 3,
      py: 2,
      mb: 3,
      mt: 2
    }}>
      <Typography
        sx={{
          fontSize: "12px",
          fontWeight: "700",
          paddingBottom: "10px",
        }}
      >
        {props?.characteristicDetails[0]}
      </Typography>

      {rows.length > 0 ? (
        <div style={{ width: "100%", height: rows.length * 53 + 56 }}> {/* Approx row height + header */}
          <DataGrid
            rows={rows}
            columns={columns}
            hideFooter
            disableColumnMenu
            disableSelectionOnClick
            sx={{
              fontFamily: "'Roboto','Helvetica','Arial',sans-serif",
              fontSize: "0.875rem",
              "& .MuiDataGrid-columnHeaders": {
                backgroundColor: "#f3f3fc",
                borderBottom: "1px solid #dcdcdc",
              },
              "& .MuiDataGrid-columnHeaderTitle": {
                fontWeight: 600,
                fontSize: "0.875rem",
              },
              "& .MuiDataGrid-cell": {
                color: "#333",
                fontSize: "0.875rem",
              },
            }}
          />
        </div>
      ) : (
        <Typography variant="body2" sx={{ color: "#888" }}>
          No characteristic data available.
        </Typography>
      )}

      <Dialog open={editOpen} onClose={handleEditClose} fullWidth maxWidth="sm">
        <DialogTitle>Edit Entry</DialogTitle>
        <DialogContent>
          <TextField
            margin="dense"
            label="Characteristic"
            fullWidth
            value={selectedRow?.characteristic || ""}
            disabled
          />
          <TextField
            margin="dense"
            label="Description"
            fullWidth
            value={selectedRow?.description || ""}
            disabled
          />
          {/* <TextField
            margin="dense"
            label="Value"
            fullWidth
            select
            value={selectedRow?.value || ""}
            onChange={handleValueChange}
          >
            {values.map((val) => (
              <MenuItem key={val.code} value={val.code}>
                {val.code}
              </MenuItem>
            ))}
          </TextField> */}
          <Autocomplete
            multiple
            freeSolo={false}
            options={values}
            getOptionLabel={(option) => option.code || ""}
            value={
              values.filter(val => (selectedRow?.value || []).includes(val.code))
            }
            onChange={(event, newValue) => {
              // newValue is an array of selected option objects
              // Extract the codes and pass to handleValueChange
              const selectedCodes = newValue.map(opt => opt.code);
              handleValueChange(selectedCodes);
            }}
            renderInput={(params) => (
              <TextField
                {...params}
                margin="dense"
                label="Value"
                fullWidth
                variant="outlined"
              />
            )}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleEditClose} color="secondary">Cancel</Button>
          <Button onClick={handleSave} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}
export default ClassificationViewArticle;
