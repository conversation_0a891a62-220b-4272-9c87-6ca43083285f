import React from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Tooltip
} from 'antd';
import { IconButton, useTheme } from '@mui/material';
import { ArrowCircleLeftOutlined } from '@mui/icons-material';
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import { colors } from '@constant/colors';
import { WORKFLOW_DATA_CONSTANTS } from '@constant/enum';

const { Title, Text } = Typography;

const WorkflowHeader = ({
  mode,
  navigate,
  headerData,
  module,
  fetchChangeLogDetails
}) => {
  const theme = useTheme()
  return (
    <>
      {/* Header Section */}
      <div style={{ marginBottom: 32 }}>
        <div style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%"
        }}>
          {/* Left side - Back button and title */}
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <IconButton
              onClick={() => navigate(-1)}
              color="primary"
              aria-label="go back"
              title="Back"
              sx={{ padding: 0 }}
            >
              <ArrowCircleLeftOutlined sx={{ fontSize: "25px", color: "#000000" }} />
            </IconButton>

            <Title
              level={3}
              style={{
                marginBottom: 2,
                fontWeight: 700
              }}
            >
              {mode === WORKFLOW_DATA_CONSTANTS?.MODES?.CREATE ? WORKFLOW_DATA_CONSTANTS?.TITLE_MODES?.CREATE : mode ===  WORKFLOW_DATA_CONSTANTS?.MODES?.EDIT ? WORKFLOW_DATA_CONSTANTS?.TITLE_MODES?.EDIT : WORKFLOW_DATA_CONSTANTS?.TITLE_MODES?.VIEW} - {module}
            </Title>
          </div>

          <Tooltip title="View Changelog History" placement="left">
            <IconButton
              onClick={fetchChangeLogDetails}
              color="primary"
              aria-label="view changelog"
              sx={{
                padding: '8px',
                border: `2px solid ${theme.palette.primary.main}`,
                borderRadius: '10px',
                '&:hover': {
                  backgroundColor: 'rgba(24, 144, 255, 0.1)'
                }
              }}
            >
              <TrackChangesTwoToneIcon
                style={{
                  fontSize: "24px",
                  color: theme.palette.primary.main
                }}
              />
            </IconButton>
          </Tooltip>
        </div>

        <Text type="secondary">Design your workflows with simplicity</Text>
      </div>

      {/* Workflow Header Information */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: '17px', fontWeight: 600, color: theme.palette.primary.main }}>
              Workflow Information
            </span>
          </div>
        }
        size="small"
        style={{
          marginTop: -20,
          marginBottom: 24,
          borderRadius: '8px',
          backgroundColor: '#fafafa',
          border: `1px solid ${theme.palette.primary.main}60`,
          boxShadow: '0 1px 4px rgba(0,0,0,0.1)'
        }}
        bodyStyle={{
          padding: '16px 24px'
        }}
      >
        <div style={{ fontSize: '12px', lineHeight: '20px' }}>
          <Row gutter={[24, 8]}>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Workflow ID:</Text></Col>
                <Col span={14}>{headerData?.workflowId || 'N/A'}</Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Workflow Name:</Text></Col>
                <Col span={14}>{headerData?.workflowName || 'N/A'}</Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Region:</Text></Col>
                <Col span={14}>{headerData?.region || 'N/A'}</Col>
              </Row>
            </Col>
          </Row>

          <Row gutter={[24, 8]} style={{ marginTop: 8 }}>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Scenario:</Text></Col>
                <Col span={14}>{headerData?.scenario || 'N/A'}</Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Template:</Text></Col>
                <Col span={14}>{headerData?.template || 'N/A'}</Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row gutter={[8, 4]}>
                <Col span={10}><Text strong>Bifurcation Group:</Text></Col>
                <Col span={14}>{headerData?.bifurcationGroup || 'N/A'}</Col>
              </Row>
            </Col>
          </Row>
        </div>
      </Card>
    </>
  );
};

export default WorkflowHeader;