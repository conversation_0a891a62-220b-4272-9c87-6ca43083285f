import{cs as M,A as Me,k as Te,O as l,an as Q,d as D,r as s,a as Se,b as xe,t as U,o as Ge,n as T,s as Ne,g as ye,E as be,cc as Ie,ct as Re,bA as Oe,cu as ve,c as i,j as t,N as Le,aZ as Pe,Q as ke,U as Fe,V as He,W as Ue,i as Be,X as B,Y as w,Z as $,$ as W,a7 as we,ab as z,ac as $e,ad as We,F as ze,b1 as je,ag as j,ah as Ye,bT as qe,C as f,bH as S,x as Y,bI as q,w as Je,aE as Ke,aG as Ve,bJ as x,J as Qe}from"./index-226a1e75.js";import{S as J}from"./SingleSelectDropdown-ee61a6b7.js";import{R as Ze}from"./ReusableHIerarchyTree-e69bb363.js";import{R as Xe}from"./ReusablePresetFilter-da63464b.js";import"./context-5b1a8b0b.js";import"./EyeOutlined-6bec9589.js";import"./EditOutlined-5e4d9326.js";import"./asyncToGenerator-88583e02.js";import"./ArrowLeftOutlined-cbc675ea.js";import"./index-a591cf5c.js";const et=M(Me)(({theme:g})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:g.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${g.palette.primary.light}20`}})),tt=M(Te)(({theme:g})=>({marginTop:"0px !important",border:`1px solid ${g.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),rt=M(l)({display:"flex",justifyContent:"flex-end",paddingRight:"0.75rem",paddingBottom:"0.75rem",paddingTop:"0rem",gap:"0.5rem"}),K=M(Q)({borderRadius:"4px",padding:"4px 12px",textTransform:"none",fontSize:"0.875rem"}),V=M(D)(({theme:g})=>({fontSize:"0.75rem",color:g.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500})),xt=()=>{var F,H;let g=s.useRef(null);const{t:d}=Se(),G=xe(),[Z,ot]=s.useState(!1),[X,at]=s.useState(""),[nt,ee]=s.useState(!1),[st,I]=s.useState(!1),[lt,te]=s.useState({code:"ETCA",desc:"ET FAMILY CO AREA"}),[it,re]=s.useState(null),[dt,oe]=s.useState(null),ae=U.useRef(null),[ne,R]=s.useState(!1),[se,ct]=s.useState(""),[le,ut]=s.useState(),[ie,pt]=s.useState(""),[N,de]=s.useState([]),{getDtCall:ce,dtData:E}=Ge(),[Ct,ue]=s.useState(),[m,O]=s.useState([{id:"1",title:"",child:[],tags:[],description:""}]);let y=T(e=>{var r;return(r=e.userManagement.entitiesAndActivities)==null?void 0:r.Material});const[gt,pe]=s.useState(m.length===0||!((F=m[0])!=null&&F.title)),Ce=T(e=>e.applicationConfig),v=()=>{var a,u,h,p;pe(!1),I(!0);var e={node:((a=n==null?void 0:n.costCenterGroup)==null?void 0:a.code)===""?"":(u=n==null?void 0:n.costCenterGroup)==null?void 0:u.code,controllingArea:((h=n==null?void 0:n.controllingArea)==null?void 0:h.code)===""?"":(p=n==null?void 0:n.controllingArea)==null?void 0:p.code,classValue:"0101",id:"",screenName:"Display"};const r=_=>{let C=[];_.statusCode===200&&(C.push(_.body.HierarchyTree),O(C)),O(C),I(!1)},o=_=>{};f(`/${S}/data/displayHierarchyTreeNodeStructure`,"post",r,o,e)},c=Ne(),ge=ye(),A=T(e=>{var r;return(r=e==null?void 0:e.AllDropDown)==null?void 0:r.dropDown}),n=T(e=>e.commonFilter.HierarchyNodeCostCenter),he=e=>{var r=e;let o={...n,controllingArea:r};c(Y({module:"HierarchyNodeCostCenter",filterData:o})),L(e.code)},me=e=>{var r=e;let o={...n,costCenterGroup:r};c(Y({module:"HierarchyNodeCostCenter",filterData:o}))},Ae=e=>{const r=a=>{c(x({keyName:"COAREA",data:a.body}))},o=a=>{};f(`/${S}/data/getControllingArea`,"get",r,o)},_e=()=>{let e={decisionTableId:null,decisionTableName:"MDG_CUSTOM_DROPDOWN_LIST",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":"CCG","MDG_CONDITIONS.MDG_FIELD_NAME":"Controlling Area"}],systemFilters:null,systemOrders:null,filterString:null};const r=a=>{var u,h;if(a.statusCode===200){const p=((h=(u=a==null?void 0:a.data)==null?void 0:u.result[0])==null?void 0:h.MDG_CUSTOM_LOOKUP_ACTION_TYPE)||[];let _=[];p==null||p.map(C=>{let b={};b.code=C==null?void 0:C.MDG_LOOKUP_CODE,b.desc=C==null?void 0:C.MDG_LOOKUP_DESC,_.push(b)}),c(x({keyName:"NewControllingArea",data:_}))}},o=a=>{};Ce.environment==="localhost"?f(`/${q}/rest/v1/invoke-rules`,"post",r,o,e):f(`/${q}/v1/invoke-rules`,"post",r,o,e)},fe=e=>{const r=a=>{c(x({keyName:"PRCTRGroup",data:a.body}))},o=a=>{};f(`/${S}/node/getZeroLevelNodes?controllingArea=${e}`,"get",r,o)},L=e=>{const r=a=>{c(x({keyName:"PRCTRGroupFilter",data:a.body}))},o=a=>{};f(`/${S}/data/getCostCtrGroup?controllingArea=${e}`,"get",r,o)},P=()=>{c(Je({module:"HierarchyNodeCostCenter"}))};s.useEffect(()=>{Ae(),fe("ETCA"),L("ETCA"),_e(),c(be()),c(Ie()),c(Re()),c(Oe()),c(ve({module:"HierarchyNodeCostCenter",groupName:"costCenterGroup"}))},[]);const Ee=()=>{R(!0)},k=()=>{R(!1)};s.useEffect(()=>{te({code:"ETCA",desc:"ET FAMILY CO AREA"}),re(null),oe(null)},[]);const De=()=>{let e={decisionTableId:null,decisionTableName:Qe.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Cost Center Group","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};ce(e)};return s.useEffect(()=>{var e,r;if(E){const o=(r=(e=E==null?void 0:E.result)==null?void 0:e[0])==null?void 0:r.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,a=o==null?void 0:o.filter(u=>u.MDG_MAT_FILTER_TYPE==="Additional").map(u=>({title:d(u.MDG_MAT_UI_FIELD_NAME)}));de(o),ue(a)}},[E]),s.useEffect(()=>{De()},[]),i("div",{ref:g,children:[t(Ke,{dialogState:ne,openReusableDialog:Ee,closeReusableDialog:k,dialogTitle:se,dialogMessage:le,handleDialogConfirm:k,dialogOkText:"OK",dialogSeverity:ie}),t(Ve,{blurLoading:Z,loaderMessage:X}),t("div",{style:{...Le,backgroundColor:"#FAFCFF"},children:i(Pe,{spacing:1,children:[t(l,{container:!0,mt:0,sx:ke,children:i(l,{item:!0,md:5,children:[t(D,{variant:"h3",children:t("strong",{children:d("Cost Center Group")})}),t(D,{variant:"body2",color:"#777",children:d("This view displays the selected Cost Center Hierarchy")})]})}),t(l,{container:!0,sx:Fe,children:t(l,{item:!0,md:12,children:i(tt,{defaultExpanded:!0,className:"filterCCG",children:[i(et,{expandIcon:t(He,{sx:{fontSize:"1.25rem",color:G.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[t(Ue,{sx:{fontSize:"1.25rem",marginRight:1,color:G.palette.primary.dark}}),t(D,{sx:{fontSize:"0.875rem",fontWeight:600,color:G.palette.primary.dark},children:d("Filter Cost Center Group")})]}),i(Be,{sx:{padding:"1rem 1rem 0.5rem"},children:[t(l,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:t(l,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:N==null?void 0:N.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,r)=>e.MDG_MAT_SEQUENCE_NO-r.MDG_MAT_SEQUENCE_NO).map((e,r)=>{var o,a,u,h;return i(U.Fragment,{children:[(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===B.CONTROLINGAREA&&i(l,{item:!0,md:2,children:[i(V,{sx:w,children:[d(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," ",t("span",{style:{color:(a=(o=$)==null?void 0:o.error)==null?void 0:a.dark},children:"*"})]}),t(W,{size:"small",fullWidth:!0,children:t(J,{options:(A==null?void 0:A.COAREA)??[],value:n==null?void 0:n.controllingArea,onChange:p=>{he(p)},placeholder:d("SELECT CONTROLLING AREA"),disabled:!1,minWidth:"90%",listWidth:210})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===B.CCGROUP&&i(l,{item:!0,md:2,children:[i(V,{sx:w,children:[d(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," ",t("span",{style:{color:(h=(u=$)==null?void 0:u.error)==null?void 0:h.dark},children:"*"})]}),t(W,{size:"small",fullWidth:!0,children:t(J,{options:(A==null?void 0:A.PRCTRGroupFilter)??[],value:n==null?void 0:n.costCenterGroup,onChange:p=>{me(p)},placeholder:d("SELECT COST CENTER GROUP"),disabled:!1,minWidth:"90%",listWidth:210})})]})]},r)})})}),i(rt,{children:[t(K,{variant:"outlined",size:"small",startIcon:t(we,{sx:{fontSize:"1rem"}}),onClick:()=>{P()},children:d("Clear")}),t(l,{sx:{...z},children:t(Xe,{moduleName:"MaterialMaster",handleSearch:v,onPresetActiveChange:e=>ee(e),onClearPreset:P})}),t(K,{variant:"contained",size:"small",startIcon:t($e,{sx:{fontSize:"1rem"}}),sx:{...We,...z},onClick:()=>{v()},children:d("Search")})]})]})]})})}),t(ze,{children:m.length>0&&((H=m[0])==null?void 0:H.label)&&t(l,{container:!0,children:t(l,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...je},children:t(l,{container:!0,display:"flex",flexDirection:"row",flexWrap:"nowrap",children:i(l,{item:!0,md:12,children:[m[0]?t(l,{item:!0,sx:{display:"flex",justifyContent:"space-between"}}):"",t(l,{children:t(D,{variant:"body1",fontWeight:"bold",justifyContent:"flex-start",marginBottom:2,children:d("Existing Structure in SAP")})}),t(Ze,{initialRawTreeData:m,editmode:!1,object:"Cost Center"})]})})})})}),t(j,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:(y==null?void 0:y.length)>0&&t(j,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:i(Ye,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},children:[t(Q,{size:"small",variant:"contained",onClick:()=>{ge("/requestBench/CostCenterGroupRequestTab")},className:"createRequestButtonCCG",children:d("Create Request")}),t(qe,{variant:"contained",ref:ae,"aria-label":"split button"})]})})})]})})]})};export{xt as default};
