import{c9 as qo,ca as Vo,cb as Xo,a as vo,r as d,o as gl,b as Ko,n as Le,at as Ke,bx as Jo,aP as Qo,s as Zo,g as bo,x as b,t as et,E as mo,aC as Oo,cc as xt,bA as jo,C as x,z as v,cd as S,c as C,ai as Ne,aj as _e,j as c,O as I,af as Ft,al as Ie,am as ke,bM as ec,bN as En,d as H,F as q,bG as K,a9 as le,bP as Tn,bQ as Et,N as tc,S as hl,Q as nc,bC as lc,U as ac,V as sc,W as oc,i as cc,X as Pe,$ as J,b3 as We,B as se,bD as He,aa as we,bE as ae,bF as Xe,T as Ce,a4 as ve,a8 as oe,Y as V,h as tt,a5 as rc,a6 as Ge,a7 as fl,a1 as dc,a2 as ic,a3 as uc,ab as Cl,ac as gc,ad as hc,ag as fc,ah as Cc,bT as Ac,an as m,ce as yc,k as xc,A as Ec,Z as Tc,bI as qt,cf as Tt,w as Al,aD as Sc,a_ as Sn,R as pn,ak as Vt,bW as pc,aG as Gc,cg as Dc,ch as Mc,aE as yl,aK as Gn,bJ as B,ci as Lc,cj as Nc,ck as _c,cl as Ic,cm as kc,cn as Pc,J as xl,c2 as Dn,bK as wc,aO as Bc,co as Xt}from"./index-226a1e75.js";import{d as Rc}from"./EditOutlined-b0a055aa.js";import{d as $c}from"./History-09ae589c.js";import{A as zc}from"./AttachmentUploadDialog-5b2112e0.js";import{R as Yc}from"./ReusablePresetFilter-da63464b.js";import"./CloudUpload-17ed0189.js";import"./Delete-3f2fc9ef.js";import"./utilityImages-067c3dc2.js";var Mn={},Uc=Vo;Object.defineProperty(Mn,"__esModule",{value:!0});var Tl=Mn.default=void 0,Wc=Uc(qo()),Hc=Xo;Tl=Mn.default=(0,Wc.default)((0,Hc.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z"}),"CheckCircleOutlined");const Fc=Ke(xc,{target:"e1wd0fis9"})(({theme:$})=>({marginTop:"0px !important",border:`1px solid ${$.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),qc=Ke(Ec,{target:"e1wd0fis8"})(({theme:$})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:$.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${$.palette.primary.light}20`}}),"");Tc.primary.main;const Vc=Ke(I,{target:"e1wd0fis5"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),El=Ke(m,{target:"e1wd0fis4"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),Z=Ke(H,{target:"e1wd0fis3"})(({theme:$})=>({fontSize:"0.75rem",color:$.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500}),""),Br=()=>{const{t:$}=vo();d.useState(!1);const[Be,O]=d.useState(!1),[vt,Ln]=d.useState([]),[Nn,Sl]=d.useState(""),[pl,_n]=d.useState(!1),[Gl,Kt]=d.useState(""),[Dl,Jt]=d.useState(),[Je,Ml]=d.useState(""),[Qe,Ll]=d.useState(""),[Re,nt]=d.useState({}),[Ae,lt]=d.useState([]),[ye,at]=d.useState([]),[ue,st]=d.useState([]),[Nl,St]=d.useState(!1),[Xc,_l]=d.useState(!1),[ge,ot]=d.useState([]),[Il,D]=d.useState(""),[he,ct]=d.useState([]),[xe,rt]=d.useState([]),[kl,Pl]=d.useState([]),{getDtCall:wl,dtData:dt}=gl(),{getDtCall:Bl,dtData:it}=gl(),[Qt,Rl]=d.useState([]),[In,Fe]=d.useState({}),[Zt,pt]=d.useState([]),[bt,Gt]=d.useState([]),[Dt,Mt]=d.useState([]),[Lt,Nt]=d.useState([]),[_t,It]=d.useState([]),[mt,kt]=d.useState([]),[$l,zl]=d.useState([]),[Yl,Ul]=d.useState([]),Ot=Ko(),Wl=d.useMemo(()=>xe.length>0?xe:mt.length>0?mt:[],[xe,mt]),Hl=d.useMemo(()=>Ae.length>0?Ae:Zt.length>0?Zt:[],[Ae,Zt]),Fl=d.useMemo(()=>ye.length>0?ye:bt.length>0?bt:[],[ye,bt]);d.useState({code:"ETCN",desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}),d.useState(!1),d.useState(!1);const[ql,vc]=d.useState([]);d.useState(""),Le(e=>e.appSettings.Format);const f=Le(e=>e.AllDropDown.dropDown);d.useState(!1),d.useState({}),d.useState({}),d.useState({}),d.useState({}),d.useState(!1),d.useState(!1);const[Kc,Ze]=d.useState(!1);d.useState(""),d.useState([]),d.useState(!1),d.useState(!1);const[jt,ut]=d.useState(0),[Pt,Vl]=d.useState(10),[Jc,Xl]=d.useState(0);d.useState("");const[j,Qc]=d.useState([]),[vl,fe]=d.useState(!1),[Zc,Kl]=d.useState(!1);d.useState(!1),d.useState(!1);const[F,Jl]=d.useState([]),[Ql,Zl]=d.useState(!1),[bc,bl]=d.useState([]),[ml,Ol]=d.useState([]),[jl,mc]=d.useState(""),[ea,ta]=d.useState(!1),[Oc,kn]=d.useState(""),[jc,Pn]=d.useState(""),[er,wn]=d.useState(!1),Bn=Le(e=>e.generalLedger.handleMassMode);Le(e=>{var n;return(n=e.userManagement.entitiesAndActivities)==null?void 0:n["Display Material"]}),Le(e=>e.userManagement.userData);const[be,$e]=d.useState(!1);d.useState(!1),d.useState(!1);const[y,ce]=d.useState([]),[T,re]=d.useState([]);d.useState([]),d.useState([]);const[qe,De]=d.useState(!1),[na,tr]=d.useState({}),[la,aa]=d.useState(!1),[me,wt]=d.useState("systemGenerated");d.useEffect(()=>{},[y]),d.useEffect(()=>{Xa(!0)},[ql]);const Bt=Ke(({className:e,...n})=>c(Ce,{...n,classes:{popper:e}}),{target:"e1wd0fis1"})({[`& .${Jo.tooltip}`]:{maxWidth:"none"}},""),[sa,oa]=d.useState(!1);d.useState([]);const[ca,P]=d.useState(!1),[ra,R]=d.useState(""),[ze,da]=d.useState([]),[Ye,ia]=d.useState([]),[ua,ga]=d.useState([]),[w,gt]=d.useState([]),[ha,Rn]=d.useState(!1);d.useState(["ENTER GL ACCOUNT"]);const l=Le(e=>e.commonFilter.GeneralLedger),{customError:G}=Qo(),A=Zo(),Ue=bo(),fa=48,Ca=8,Ve={PaperProps:{style:{maxHeight:fa*4.5+Ca,width:250}}},en=()=>{aa(!1),wt("systemGenerated")},tn=()=>{Rn(!1),wt("systemGenerated")},Aa=e=>{var n;wt((n=e==null?void 0:e.target)==null?void 0:n.value)},ya=e=>{var n;wt((n=e==null?void 0:e.target)==null?void 0:n.value)},xa=()=>{me==="systemGenerated"&&(Po(),en()),me==="mailGenerated"&&(wo(),en())},Ea=()=>{me==="systemGenerated"&&(tn(),cl()),me==="mailGenerated"&&(tn(),Uo())},Ta=e=>{const n=e.target.value;Vl(n),ut(0),Xl(0)},Sa=(e,n)=>{ut(n)},$n=[{field:"glAccount",headerName:"GL Account",editable:!1,flex:1,width:150},{field:"reqId",headerName:"Req Id",editable:!1,flex:1,width:200},{field:"requestedBy",headerName:"Requested By",editable:!1,flex:1,width:250}];d.useEffect(()=>{if(l!=null&&l["G/L Account"]){const n=(l==null?void 0:l["G/L Account"].split("$^$")).map(s=>({code:s}));kt(n)}if(l!=null&&l["Company Code"]){const n=(l==null?void 0:l["Company Code"].split("$^$")).map(s=>({code:s}));It(n)}if(l!=null&&l.TaxCategory){const n=(l==null?void 0:l.TaxCategory.split("$^$")).map(s=>({code:s}));Mt(n)}if(l!=null&&l.createdBy){const n=(l==null?void 0:l.createdBy.split("$^$")).map(s=>({code:s}));Nt(n)}if(l!=null&&l.glAcctLongText){const n=(l==null?void 0:l.glAcctLongText.split("$^$")).map(s=>({code:s}));pt(n)}if(l!=null&&l.shortText){const n=(l==null?void 0:l.shortText.split("$^$")).map(s=>({code:s}));Gt(n)}if(l!=null&&l.createdOn){const e=new Date(l==null?void 0:l.createdOn[0]),n=new Date(l==null?void 0:l.createdOn[1]);zl([e,n])}if(l!=null&&l["Account Group"]){const n=(l==null?void 0:l["Account Group"].split("$^$")).map(s=>({code:s}));Fe(s=>({...s,"Account Group":n??[]}))}if(l!=null&&l["G/L Account Type"]){const n=(l==null?void 0:l["G/L Account Type"].split("$^$")).map(s=>({code:s}));Fe(s=>({...s,"G/L Account Type":n??[]}))}if(l!=null&&l["Field Status Group"]){const n=(l==null?void 0:l["Field Status Group"].split("$^$")).map(s=>({code:s}));Fe(s=>({...s,"Field Status Group":n??[]}))}if(l!=null&&l["Recon Account for Acct Type"]){const n=(l==null?void 0:l["Recon Account for Acct Type"].split("$^$")).map(s=>({code:s}));Fe(s=>({...s,"Recon Account for Acct Type":n??[]}))}},[l]),d.useEffect(()=>{Object.keys(Re).forEach(e=>{var i;const n=(i=Re[e])==null?void 0:i.map(u=>u==null?void 0:u.code).join("$^$");let s={...l,[e]:n};A(b({module:"GeneralLedger",filterData:s}))})},[Re]),d.useEffect(()=>{var e=he.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,"Company Code":e};A(b({module:"GeneralLedger",filterData:n}))},[he]),d.useEffect(()=>{var e=xe.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,"G/L Account":e};A(b({module:"GeneralLedger",filterData:n}))},[xe]),d.useEffect(()=>{var e=ye.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,shortText:e};A(b({module:"GeneralLedger",filterData:n}))},[ye]),d.useEffect(()=>{var e=ge.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,createdBy:e};A(b({module:"GeneralLedger",filterData:n}))},[ge]),d.useEffect(()=>{var e=ue.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,TaxCategory:e};A(b({module:"GeneralLedger",filterData:n}))},[ue]),d.useEffect(()=>{var e=Ae.map(s=>s==null?void 0:s.code).join("$^$");let n={...l,glAcctLongText:e};A(b({module:"GeneralLedger",filterData:n}))},[Ae]),d.useEffect(()=>{},[]),d.useEffect(()=>{jt*Pt>=(ee==null?void 0:ee.length)&&oo()},[jt,Pt]);const pa=()=>{wn(!0),Ga()},Ga=()=>{var i,u;ut(0),Ze(!0);let e={glAccount:(l==null?void 0:l["G/L Account"])??"",chartOfAccount:(i=l==null?void 0:l.chartOfAccount)!=null&&i.code?(u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code:"ETCN",companyCode:(l==null?void 0:l["Company Code"])??"",taxCategory:(l==null?void 0:l.TaxCategory)??"",glAcctLongText:(l==null?void 0:l.glAcctLongText)??"",postingWithoutTaxAllowed:(l==null?void 0:l.postingWithoutTaxAllowed)==="Allowed"?"X":(l==null?void 0:l.postingWithoutTaxAllowed)==="Not Allowed"?"Y":"",blockedForPostingInCOA:(l==null?void 0:l.blockedForPostingInCOA)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCOA)==="Unblocked"?"Y":"",blockedForPostingInCompany:(l==null?void 0:l.blockedForPostingInCompany)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCompany)==="Unblocked"?"Y":"",accountGroup:(l==null?void 0:l["Account Group"])??"",shortText:(l==null?void 0:l.shortText)??"",glAccountType:(l==null?void 0:l["G/L Account Type"])??"",fieldStatusGroup:(l==null?void 0:l["Field Status Group"])??"",openItemMgmtbyLedgerGroup:(l==null?void 0:l.openItemMgmtByLedgerGroup)==="True - X"?"X":(l==null?void 0:l.openItemMgmtByLedgerGroup)==="False"?"Y":"",openItemManagement:(l==null?void 0:l.openItemManagement)==="True - X"?"X":(l==null?void 0:l.openItemManagement)==="False"?"Y":"",postAutoOnly:(l==null?void 0:l.postAutoOnly)==="True - X"?"X":(l==null?void 0:l.postAutoOnly)==="False"?"Y":"",reconAccountforAcctType:(l==null?void 0:l["Recon Account for Acct Type"])??"",fromDate:v(l==null?void 0:l.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:v(l==null?void 0:l.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:(l==null?void 0:l.createdBy)??"",top:Ra,skip:0};const n=r=>{var g,h,k,p,ne,z;if(r.statusCode===200){let X=[];(h=(g=r==null?void 0:r.body)==null?void 0:g.list)==null||h.forEach(M=>{if((M==null?void 0:M.COA)=="ETCN"){let E={};E.code=M==null?void 0:M.GLAccount,E.desc=M==null?void 0:M.GLAccount,X.push(E)}}),Object.values(X.reduce((M,E)=>(M[E.code]=E,M),{}));var o=[];for(let M=0;M<((p=(k=r==null?void 0:r.body)==null?void 0:k.list)==null?void 0:p.length);M++){var a=(ne=r==null?void 0:r.body)==null?void 0:ne.list[M],t={id:Gn(),chartOfAccount:(a==null?void 0:a.COA)!==""?a==null?void 0:a.COA:"",glAccountType:(a==null?void 0:a.Accounttype)!==""?a==null?void 0:a.Accounttype:"",glAcctLongText:(a==null?void 0:a.Description)!==""?a==null?void 0:a.Description:"",AccountGroup:(a==null?void 0:a.AccountGroup)!==""?a==null?void 0:a.AccountGroup:"",compCode:(a==null?void 0:a.CompanyCode)!==""?a==null?void 0:a.CompanyCode:"",glAccount:(a==null?void 0:a.GLAccount)!==""?a==null?void 0:a.GLAccount:"",groupAccountNumber:(a==null?void 0:a.GroupAccNo)!==""?a==null?void 0:a.GroupAccNo:"",postingWithoutTaxAllowed:(a==null?void 0:a.Pstnwotax)==="X"?"Allowed":"Not Allowed",blockedForPostingInCOA:(a==null?void 0:a.PostingBlockedCOA)==="X"?"Blocked":"Unblocked",blockedForPostingInCompany:(a==null?void 0:a.PostingBlockedCoCd)==="X"?"Blocked":"Unblocked",PostAutomaticallyOnly:(a==null?void 0:a.PostAuto)==="X"?"X":"",TaxCategory:a.Taxcategory!==""?a==null?void 0:a.Taxcategory:"",createdBy:(a==null?void 0:a.CreatedBy)!==""?a==null?void 0:a.CreatedBy:"",shortText:(a==null?void 0:a.GLname)!==""?a==null?void 0:a.GLname:"",gLAccountType:(a==null?void 0:a.Accounttype)!==""?a==null?void 0:a.Accounttype:"",fieldStatusGroup:(a==null?void 0:a.FieldStsGrp)!==""?a==null?void 0:a.FieldStsGrp:"",openItemManagement:(a==null?void 0:a.Openitmmanage)!==""?a==null?void 0:a.Openitmmanage:"",openItemMgmtByLedgerGroup:(a==null?void 0:a.OIMgmtByLedgerGrp)!==""?a==null?void 0:a.OIMgmtByLedgerGrp:"",reconAccountForAcctType:(a==null?void 0:a.ReconAcc)!==""?a==null?void 0:a.ReconAcc:"",createdOn:(a==null?void 0:a.CreatedOn)!==""?`${v(a.CreatedOn).format("DD MMM YYYY")}`:""};o.push(t)}o.sort((M,E)=>v(M.createdOn,"DD MMM YYYY HH:mm")-v(E.createdOn,"DD MMM YYYY HH:mm")),ut(Math.floor((o==null?void 0:o.length)/Pt)),Rt(o),Ze(!1),fn(o.length),ln((z=r==null?void 0:r.body)==null?void 0:z.count)}else r.statusCode===400&&(Kt("Warning"),Jt("Please Select Lesser Fields as the URL is getting too long !!"),Ut())},s=r=>{G(r)};x(`/${S}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",n,s,e)},[Da,W]=d.useState(!0),[Oe,nn]=d.useState(null),[Ma,La]=d.useState(null),[ee,Rt]=d.useState([]),[nr,zn]=d.useState([...ee]);et.useState(""),d.useState(!1),d.useState(""),d.useState(""),d.useState(!0);const[lr,de]=d.useState(!1),[ar,ie]=d.useState(!0);d.useState([]),d.useState([]);const[sr,ht]=d.useState(!0),[or,Na]=d.useState([]),[cr,_a]=d.useState([]);d.useState(!1);const[Me,Yn]=d.useState([]);d.useState([]);const[rr,Ia]=d.useState([]),[Ee,ka]=d.useState({});d.useState([]),d.useState([]),d.useState([]),d.useState(!1),d.useState([]);const[te,Un]=d.useState([]);d.useState([]),d.useState(!1),d.useState(!0),d.useState("sm"),d.useState(0),d.useState(0);const[Pa,Te]=d.useState(!1);d.useState(!1),d.useState(!1);const[dr,Se]=d.useState(!1);d.useState(!1),d.useState(!1);const[ir,wa]=d.useState([]);d.useState(""),et.useRef(null);const Ba=et.useRef(null);et.useRef(null),d.useState(!1),d.useState(!1);const[pe,ur]=d.useState([]);d.useState([]),d.useState([]);const[Ra,ln]=d.useState(0),[Q,$a]=d.useState([]);d.useState([]);const[Y,za]=d.useState("Company Code");d.useState([]),d.useState({}),d.useState({});const[Wn,gr]=d.useState([]);d.useState([]),d.useState([]),d.useState([]),d.useState([]),d.useState([]),d.useState([]),d.useState(""),d.useState([]),d.useState([]),d.useState([]),d.useState([]),d.useState(null),d.useState(null),d.useState(!1),d.useState([]);const[hr,an]=d.useState("");d.useState("");const[je,Ya]=d.useState([]);d.useState(null);const[Hn,fr]=d.useState([]),[Fn,Cr]=d.useState([]),[Ua,Ar]=d.useState(!1),[Wa,yr]=d.useState(!1),[Ha,Fa]=d.useState(!1),[qa,Va]=d.useState(!1);d.useState([]),d.useState(!1),d.useState(!1),d.useState(!1);const[xr,Xa]=d.useState(!0);d.useState(!1);const[Er,va]=d.useState([]);d.useState([]),d.useState([]),d.useState(null),d.useState("");const[Tr,Ka]=d.useState(!1),qn=d.useRef(null);d.useEffect(()=>{A(mo())},[]),d.useEffect(()=>{function e(n){qn.current&&!qn.current.contains(n.target)&&Ka(!1)}return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const $t=f==null?void 0:f.GlAccountForExtend;d.useState($t==null?void 0:$t.slice(0,100));const[Ja,Sr]=d.useState("");d.useState(!1),d.useEffect(()=>{},[Ee]),(e=>e.reduce((n,s)=>{const i=n[s.group]||[];return{...n,[s.group]:[...i,s]}},{}))([{title:"Option 1",group:"Group A"},{title:"Option 2",group:"Group A"},{title:"Option 3",group:"Group B"},{title:"Option 4",group:"Group B"}]),d.useState(""),Le(e=>e.generalLedger.editMultipleGlExtend);const Vn=Le(e=>e.applicationConfig);d.useState([]);const Qa=(e,n)=>{var s;n!==""?an(n):((s=e==null?void 0:e.nativeEvent)==null?void 0:s.inputType)==="deleteContentBackward"?an(""):an(i=>i)};let Za=[{field:"coa",headerName:"Chart Of Account",flex:1.5,width:100},{field:"GLAccount",headerName:"GL Account",flex:1.5,width:100,renderCell:e=>{const n=jl,s=e.id===n,i=na[e.value]===!0;return C("div",{style:{color:s?"#0033A0":"#333",fontWeight:s?"bold":"normal",padding:"12px",display:"flex",alignItems:"center"},children:[e.value,i&&c(Tl,{style:{color:"green",marginLeft:"15px"}})]})}},{field:"glDesc",headerName:"GL Description",flex:2,width:100}];Le(e=>e.commonSearchBar.GeneralLedger);const Xn=[{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1,width:100},{field:"accountType",headerName:"SAP G/L ACCOUNT TYPE ",editable:!1,flex:1,width:100},{field:"accountGroup",headerName:"SAP ACCOUNT GROUP (LEVEL 1)",editable:!1,flex:1,width:100},{field:"subAccountGroup",headerName:"SUB-ACCOUNT GROUP",editable:!1,flex:1,width:250},{field:"glAccountNumberCreated",headerName:"GL Account Number",editable:!1,flex:1,width:100,renderCell:e=>ml.find(s=>s.glAccount===e.value)?c(H,{sx:{fontSize:"12px",color:"red"},children:e.value}):c(H,{sx:{fontSize:"12px"},children:e.value})},{field:"compCodeCreated",headerName:"Company Code",editable:!1,flex:1,width:100}],ba=()=>{var n,s,i,u,r,o,a;let e={costCenterName:(l==null?void 0:l.costCenterName)??"",costCenter:"",controllingArea:((n=l==null?void 0:l.controllingArea)==null?void 0:n.code)??"",companyCode:((s=l==null?void 0:l.companyCode)==null?void 0:s.code)??"",profitCenter:((i=l==null?void 0:l.profitCenter)==null?void 0:i.code)??"",hierarchyArea:((u=l==null?void 0:l.hierarchyArea)==null?void 0:u.code)??"",costCenterCategory:((r=l==null?void 0:l.costCenterCategory)==null?void 0:r.code)??"",createdBy:"",fromDate:"",toDate:"",personResponsible:(Ee==null?void 0:Ee["Person Responsible"])??"",businessArea:((o=Ee==null?void 0:Ee["Business Area"])==null?void 0:o.code)??"",functionalArea:((a=Ee==null?void 0:Ee["Functional Area"])==null?void 0:a.code)??""};A(b({module:"CostCenter",filterData:e}))},ma=[{name:"CostCenterName",value:"TUK1"}],Oa=(e,n)=>{za(n)},vn=e=>{const n=y.findIndex(i=>(i==null?void 0:i.id)===(e==null?void 0:e.id));let s=[];n===-1?s=[...y,e]:n===0?s=y==null?void 0:y.slice(1):n===(y==null?void 0:y.length)-1?s=y==null?void 0:y.slice(0,-1):n>0&&(s=[...y==null?void 0:y.slice(0,n),...y==null?void 0:y.slice(n+1)]),ce(s)},Kn=()=>{!be&&Y==="Company Code"?ce(ze):be&&Y==="Company Code"?ce([]):re(!be&&Y==="Chart of Account"?Ye:[]),$e(!be)},Jn=e=>{const n=T.findIndex(i=>i.id===e.id);let s=[];n===-1?s=[...T,e]:n===0?s=T==null?void 0:T.slice(1):n===T.length-1?s=T==null?void 0:T.slice(0,-1):n>0&&(s=[...T==null?void 0:T.slice(0,n),...T==null?void 0:T.slice(n+1)]),re(s)};d.useState(0),et.useRef(null),d.useState(!1);const ja=()=>{_l(!0)},es=e=>{const n=i=>{A(B({keyName:"AccountGroup",data:i.body}))},s=i=>{G(i)};x(`/${S}/data/getAccountGroupCodeDesc?chartAccount=${e}`,"get",n,s)},Qn=e=>{var i,u;const n=r=>{var a;let o=[];(a=r==null?void 0:r.body)==null||a.map(t=>{let g={};g.code=t==null?void 0:t.code,g.desc=t==null?void 0:t.desc,o.push(g)}),A(B({keyName:"CompanyCode",data:o}))},s=r=>{G(r)};x(`/${S}/data/getCompanyCode?chartAccount=${e||((i=l==null?void 0:l.chartOfAccount)!=null&&i.code?(u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code:"ETCN")}`,"get",n,s)},ts=()=>{let e={decisionTableId:null,decisionTableName:"MDG_CUSTOM_DROPDOWN_LIST",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MODULE":"General Ledger","MDG_CONDITIONS.MDG_FIELD_NAME":"Chart Of Accounts"}],systemFilters:null,systemOrders:null,filterString:null};const n=i=>{var u,r;if(i.statusCode===200){const o=((r=(u=i==null?void 0:i.data)==null?void 0:u.result[0])==null?void 0:r.MDG_CUSTOM_LOOKUP_ACTION_TYPE)||[];let a=[];o==null||o.map(g=>{let h={};h.code=g==null?void 0:g.MDG_LOOKUP_CODE,h.desc=g==null?void 0:g.MDG_LOOKUP_DESC,a.push(h)});const t=a.sort((g,h)=>{var k;return(k=g==null?void 0:g.desc)==null?void 0:k.localeCompare(h==null?void 0:h.desc)});A(B({keyName:"NewChartOfAccounts",data:t}))}},s=i=>{G(i)};Vn.environment==="localhost"?x(`/${qt}/rest/v1/invoke-rules`,"post",n,s,e):x(`/${qt}/v1/invoke-rules`,"post",n,s,e)},ns=e=>{const n=i=>{A(B({keyName:"GroupAccountNumberSearch",data:i.body}))},s=i=>{G(i)};x(`/${S}/data/getGroupAccountNumber?chartAccount=${e}`,"get",n,s)},sn=e=>{const n=i=>{A(B({keyName:"GLAccountForSearch",data:i.body}))},s=i=>{G(i)};x(`/${S}/data/getGLBasedOnCompanyCode?companyCode=${e}`,"get",n,s)},ls=(e,n)=>{{var s=n;let i={...l,chartOfAccount:s};A(b({module:"GeneralLedger",filterData:i}))}$o(n==null?void 0:n.code),es(n==null?void 0:n.code),Qn(n==null?void 0:n.code),al(n==null?void 0:n.code),mn(n==null?void 0:n.code),ns(n==null?void 0:n.code)},as=e=>{var n,s;((n=Re[e])==null?void 0:n.length)===((s=te[e])==null?void 0:s.length)?(nt(i=>({...i,[e]:[]})),Fe(i=>({...i,[e]:[]}))):nt(i=>({...i,[e]:te[e]??[]}))},ss=()=>{he.length===(f==null?void 0:f.CompanyCode.length)?(ct([]),It([])):ct(f==null?void 0:f.CompanyCode)},os=()=>{var e;xe.length===((e=f==null?void 0:f.GLSearchData)==null?void 0:e.length)?(rt([]),kt([])):rt(f==null?void 0:f.GLSearchData)},cs=()=>{ye.length===(f==null?void 0:f.ShortTextSearchGL.length)?(at([]),Gt([])):at(f==null?void 0:f.ShortTextSearchGL)},rs=()=>{var e;ge.length===((e=f==null?void 0:f.CreatedBySearchGL)==null?void 0:e.length)?(ot([]),Nt([])):ot(dropdowndata==null?void 0:dropdowndata.CreatedBySearchGL)},ds=()=>{ue.length===(f==null?void 0:f.TaxCategory.length)?(st([]),Mt([])):st(f==null?void 0:f.TaxCategory)},is=()=>{Ae.length===(f==null?void 0:f.GLAcctLongText.length)?(lt([]),pt([])):lt(f==null?void 0:f.GLAcctLongText)},us=(e,n)=>{if(e.target.value!==null){var s=e.target.value;let i={...l,blockedForPostingInCOA:s};A(b({module:"GeneralLedger",filterData:i}))}sn(n==null?void 0:n.code)},gs=(e,n)=>{if(e.target.value!==null){var s=e.target.value;let i={...l,blockedForPostingInCompany:s};A(b({module:"GeneralLedger",filterData:i}))}sn(n==null?void 0:n.code)},hs=(e,n)=>{if(e.target.value!==null){var s=e.target.value;let i={...l,openItemMgmtByLedgerGroup:s};A(b({module:"GeneralLedger",filterData:i}))}},fs=(e,n)=>{if(e.target.value!==null){var s=e.target.value;let i={...l,openItemManagement:s};A(b({module:"GeneralLedger",filterData:i}))}},Cs=(e,n)=>{if(e.target.value!==null){var s=e.target.value;let i={...l,postingWithoutTaxAllowed:s};A(b({module:"GeneralLedger",filterData:i}))}sn(n==null?void 0:n.code)},As=e=>{const n=e.target.value;if(Sl(n),Oe&&clearTimeout(Oe),n.length>=4){const s=setTimeout(()=>{Ks(n)},500);nn(s)}},ys=e=>{const n=e.target.value;if(Ll(n),Oe&&clearTimeout(Oe),n.length>=4){const s=setTimeout(()=>{Js(n)},500);nn(s)}},xs=e=>{const n=e.target.value;if(Ml(n),Oe&&clearTimeout(Oe),n.length>=4){const s=setTimeout(()=>{Qs(n)},500);nn(s)}},on=d.useCallback(e=>{const i=(e.clipboardData||window.Clipboard).getData("Text").trim().split(`
`).map((u,r)=>{const o=u.split("	"),a={id:r+1};return Xn.forEach((t,g)=>{a[t.field]=o[g]||""}),a});Ya(i)},[]),cn=d.useCallback(e=>{const i=(e.clipboardData||window.Clipboard).getData("Text").trim().split(`
`).map((u,r)=>{const o=u.split("	"),a={id:r+1};return Za.forEach((t,g)=>{a[t.field]=o[g]||""}),a});wa(i)},[]);d.useEffect(()=>(document.addEventListener("paste",on),()=>{document.removeEventListener("paste",on)}),[on]),d.useEffect(()=>(document.addEventListener("paste",cn),()=>{document.removeEventListener("paste",cn)}),[cn]);const Es=e=>{Te(!1),P(!0);const n=new FormData;if([...e].forEach(i=>n.append("files",i)),Bn==="Change"){var s=`/${S}/massAction/getAllGLFromExcelWithLimitedFieldsForMassChange`;x(s,"postformdata",r=>{if(r.statusCode===200){W(!1),P(!1),R(""),Te(!1),L("Create");const o=c(H,{component:"div",children:C("ul",{children:[c("li",{children:"Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number."}),c("li",{children:"Then you can visit the Request Bench Tab and search for that request ID and do further actions on it."}),c("li",{children:"Note - All request IDs generated in the background would initially have the status Draft."})]})});_(),L("Header - Information"),N(o),D("success"),ie(!1),Se(!0),de(!0),W(!1)}else r.statusCode===429?(P(!1),R(""),Te(!1),L("Error"),Se(!1),N((r==null?void 0:r.message)||"Too many requests. Please try again later."),D("danger"),ie(!1),de(!0),_(),W(!1)):(Te(!1),L("Error"),Se(!1),N("Upload failed. Incorrect template tab name, please recheck upload file"),D("danger"),ie(!1),de(!0),_(),W(!1),P(!1),R(""));Wt()},r=>{P(!1),R(!1),kn("Error"),N(r==null?void 0:r.message),D("danger"),ie(!1),de(!0),Pn("OK"),P(!1),R(""),_()},n)}else if(Bn==="Extend"){var s=`/${S}/massAction/getAllGeneralLedgerFromExcelForMassExtend`;x(s,"postformdata",o=>{W(),o.statusCode===200?(Te(!1),A(Lc(o==null?void 0:o.body)),L("Extend"),N(`${e.name} has been Uploaded Succesfully`),D("success"),ie(!1),Se(!0),ja(),de(!0),W(!1),Ue("/masterDataCockpitNew/generalLedger/createMultipleGL")):o.statusCode===429?(P(!1),R(""),Te(!1),L("Error"),Se(!1),N((o==null?void 0:o.message)||"Too many requests. Please try again later."),D("danger"),ie(!1),de(!0),_(),W(!1)):(Te(!1),L("Create"),Se(!1),N("Creation Failed"),D("danger"),ie(!1),de(!0),_(),W(!1)),Wt()},o=>{G(o)},n)}else{var s=`/${S}/massAction/getAllGLFromExcelWithLimitedFields`;x(s,"postformdata",o=>{if(o.statusCode===200){W(!1),P(!1),R(""),Te(!1),L("Create");const a=c(H,{component:"div",children:C("ul",{children:[c("li",{children:"Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number."}),c("li",{children:"Then you can visit the Request Bench Tab and search for that request ID and do further actions on it."}),c("li",{children:"Note - All request IDs generated in the background would initially have the status Draft."})]})});_(),L("Header - Information"),N(a),D("success"),ie(!1),Se(!0),de(!0),W(!1)}else Te(!1),L("Error"),Se(!1),N("Upload failed. Incorrect template tab name, please recheck upload file"),D("danger"),ie(!1),de(!0),_(),P(!1),R(""),W(!1);Wt()},o=>{kn("Error"),P(!1),R(!1),N(o==null?void 0:o.message),D("danger"),ie(!1),de(!0),Pn("OK"),P(!1),R(""),_()},n)}},Zn=[{field:"coa",headerName:"Chart Of Account",editable:!1,flex:1},{field:"accountType",headerName:"Account Type",editable:!1,flex:1},{field:"accountGroup",headerName:"Account Group",editable:!1,flex:1},{field:"subAccountGroup",headerName:"Sub Account Group",editable:!1,flex:1},{field:"GLAccount",headerName:"GL Account",editable:!1,flex:1},{field:"rangeFrom",headerName:"Range From",editable:!1,flex:1},{field:"rangeTo",headerName:"Range To",editable:!1,flex:1},{field:"message",headerName:"Message",editable:!1,flex:1}],bn=[{field:"coa",headerName:"Chart Of Account",editable:!1,flex:1},{field:"glAccount",headerName:"GL Account",editable:!1,flex:1},{field:"errmessageGlName",headerName:"Error",editable:!1,flex:1}];let Ts={"Account Group":`/${S}/data/getSearchParamsAccGroup`,"G/L Account Type":`/${S}/data/getSearchParamsGLAccType`,"Field Status Group":`/${S}/data/getSearchParamsFieldStatusGrp`,"Recon Account for Acct Type":`/${S}/data/getSearchParamsReconAccForAccType`};const Ss=(e,n)=>{var s;return(s=Re[e])==null?void 0:s.some(i=>(i==null?void 0:i.code)===(n==null?void 0:n.code))},ps=e=>he.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),Gs=e=>xe.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),Ds=e=>ge.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),Ms=e=>ue.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),Ls=e=>Ae.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),Ns=e=>ye.some(n=>(n==null?void 0:n.code)===(e==null?void 0:e.code)),_s=e=>{const n=e.target.value;Yn(n),Ia([]),n.forEach(async s=>{const i=Ts[s];ao(i,s)})},Is={"Account Group":"AccountGroup","Short Text":"shortText","G/L Account Type":"gLAccountType","Field Status Group":"fieldStatusGroup","Open Item Mgmt by Ledger Group":"openItemMgmtByLedgerGroup","Open Item Management":"openItemManagement","Recon Account for Acct Type":"reconAccountForAcctType","Created On":"createdOn","Created By":"createdBy"},ks=["Allowed","Not Allowed",""],rn=["True","False",""],dn=["True","False",""],un=["Blocked","Unblocked",""],gn=["Blocked","Unblocked",""],hn=["True = X","False",""],Ps=()=>{let e="Control Data";const n=i=>{A(Nc(i.body))},s=i=>{G(i)};x(`/${S}/data/getViewFieldDetails?viewName=${e}`,"get",n,s)},ws=()=>{let e="Create/Bank/Interest";const n=i=>{A(_c(i.body))},s=i=>{G(i)};x(`/${S}/data/getViewFieldDetails?viewName=${e}`,"get",n,s)},Bs=()=>{let e="Type/Description";const n=i=>{A(Ic(i.body))},s=i=>{G(i)};x(`/${S}/data/getViewFieldDetails?viewName=${e}`,"get",n,s)},Rs=()=>{let e="Information";const n=i=>{A(kc(i.body))},s=i=>{G(i)};x(`/${S}/data/getViewFieldDetails?viewName=${e}`,"get",n,s)},$s=()=>{let e="Keyword/Translation";const n=i=>{A(Pc(i.body))},s=i=>{G(i)};x(`/${S}/data/getViewFieldDetails?viewName=${e}`,"get",n,s)},zs=()=>{const e=s=>{A(B({keyName:"TradingPartner",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getTradingPartner`,"get",e,n)},Ys=()=>{const e=s=>{A(B({keyName:"BusinessArea",data:s.body}))},n=s=>{G(s)};x(`/${Tt}/data/getBusinessArea`,"get",e,n)},Us=()=>{const e=s=>{A(B({keyName:"FunctionalArea",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getFunctionalArea`,"get",e,n)},Ws=()=>{const e=s=>{A(B({keyName:"ProfitCenter",data:s.body}))},n=s=>{G(s)};x(`/${Tt}/data/getProfitCenter`,"get",e,n)},Hs=()=>{const e=s=>{A(B({keyName:"CostingSheet",data:s.body}))},n=s=>{G(s)};x(`/${Tt}/data/getCostingSheet`,"get",e,n)},Fs=()=>{x("get",s=>{A(B({keyName:"CountryReg",data:s.body}))},s=>{G(s)})},qs=()=>{const e=s=>{A(B({keyName:"Jurisdiction",data:s.body}))},n=s=>{G(s)};x(`/${Tt}/data/getJurisdiction`,"get",e,n)},Vs=()=>{const e=s=>{A(B({keyName:"Region",data:s.body}))},n=s=>{G(s)};x(`/${Tt}/data/getRegion`,"get",e,n)},Xs=()=>{const e=s=>{A(B({keyName:"LanguageKey",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getLanguageKey`,"get",e,n)},vs=()=>{O(!0);const e=s=>{A(B({keyName:"ChartOfAccounts",data:s.body})),O(!1)},n=s=>{G(s)};x(`/${S}/data/getSearchParamsCOA`,"get",e,n)},Ks=e=>{var u;O(!0);let n={glAccount:e,coa:(u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code,top:200,skip:0};const s=r=>{A(B({keyName:"GLSearchData",data:r.body})),O(!1)},i=r=>{G(r)};x(`/${S}/data/getSearchParamsGL`,"post",s,i,n)},Js=e=>{var u;O(!0);let n={description:e,coa:(u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code,top:200,skip:0};const s=r=>{A(B({keyName:"GLAcctLongText",data:r.body})),O(!1)},i=r=>{G(r)};x(`/${S}/data/getSearchParamsLongText`,"post",s,i,n)},Qs=e=>{var u;O(!0);let n={name:e,coa:(u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code,top:200,skip:0};const s=r=>{A(B({keyName:"ShortTextSearchGL",data:r.body})),O(!1)},i=r=>{G(r)};x(`/${S}/data/getSearchParamsShortText`,"post",s,i,n)},mn=e=>{var u;O(!0);let n={coa:e||((u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code),top:200,skip:0};const s=r=>{A(B({keyName:"CreatedBySearchGL",data:r.body})),O(!1)},i=r=>{G(r)};x(`/${S}/data/getSearchParamsCreatedBy`,"post",s,i,n)},Zs=()=>{const e=s=>{A(B({keyName:"ReconAccountForAccountType",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getReconAccountForAccountType`,"get",e,n)},bs=()=>{const e=s=>{A(B({keyName:"SortKey",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getSortKey`,"get",e,n)},ms=()=>{const e=s=>{A(B({keyName:"PlanningLevel",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getPlanningLevel`,"get",e,n)},Os=()=>{const e=s=>{A(B({keyName:"InternalUOM",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getInternalUOM`,"get",e,n)},js=()=>{const e=s=>{A(B({keyName:"Language",data:s.body}))},n=s=>{G(s)};x(`/${S}/data/getLanguage`,"get",e,n)},eo=e=>{const n=i=>{A(B({keyName:"InterestIndicator",data:i.body}))},s=i=>{G(i)};x(`/${S}/data/getInterestIndicator`,"get",n,s)},to=e=>{const n=i=>{A(B({keyName:"InterestCalculationFrequency",data:i.body}))},s=i=>{G(i)};x(`/${S}/data/getInterestCalculationFreq`,"get",n,s)},On=e=>{va(e==null?void 0:e.code);const n=i=>{A(B({keyName:"GlAccountForExtend",data:i==null?void 0:i.body}))},s=i=>{G(i)};x(`/${S}/data/getGLAccountByCOA?chartAccount=${e==null?void 0:e.code}`,"get",n,s)};d.useEffect(()=>{},[Ja,$t]),d.useEffect(()=>{},[pe]),d.useEffect(()=>{On({code:"ETCN",desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}),Qn(),al(),mn(),Ys(),Us(),Ws(),Hs(),Fs(),qs(),Vs(),Xs(),Ps(),ws(),Bs(),Rs(),$s(),zs(),vs(),Zs(),bs(),ms(),Os(),js(),eo(),to(),A(Oo({})),A(xt()),lo(),no(),ts(),A(jo()),Ro({code:"ETCN",desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"}),On({code:"ETCN",desc:"ET NATURAL / OPERATIONAL CHART OF ACCOUNTS"})},[]);const no=()=>{const e=s=>{A(B({keyName:"RegulatedCompanyCode",data:s==null?void 0:s.body}))},n=s=>{G(s)};x(`/${S}/data/getRegulatedCompanyCodes`,"post",e,n)},lo=()=>{let e={decisionTableId:null,decisionTableName:"MDG_ET_CHNG_FIELD_SELECTION_DT",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"GENERAL LEDGER"}],systemFilters:null,systemOrders:null,filterString:null};W(!0);const n=i=>{var u,r;if(W(!1),i.statusCode===200){let o=(r=(u=i==null?void 0:i.data)==null?void 0:u.result[0])==null?void 0:r.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;Jl(o);let a=[],t=[],g=[];o==null||o.map((z,X)=>{if(z.MDG_FIELD_SELECTION_LVL=="COMPANY CODE"){let M={};M.id=X,M.name=z.MDG_SELECT_OPTION,a.push(M)}else if(z.MDG_FIELD_SELECTION_LVL=="CHART OF ACCOUNT"){let M={};M.id=X,M.name=z.MDG_SELECT_OPTION,t.push(M)}else{let M={};M.id=X,M.name=z.MDG_SELECT_OPTION,g.push(M)}});const h=new Set,k=a.filter(z=>h.has(z.name)?!1:(h.add(z.name),!0)),p=new Set,ne=t.filter(z=>p.has(z.name)?!1:(p.add(z.name),!0));da(k),ia(ne),ga(g)}Wt()},s=i=>{G(i)};Vn.environment==="localhost"?x(`/${qt}/rest/v1/invoke-rules`,"post",n,s,e):x(`/${qt}/v1/invoke-rules`,"post",n,s,e)},ao=(e,n)=>{var r;O(!0);let s={coa:(r=l==null?void 0:l.chartOfAccount)==null?void 0:r.code,top:200,skip:0};x(e,"post",o=>{const a=o.body;Un(t=>({...t,[n]:a})),O(!1)},o=>{G(o)},s)},ft=new Date,zt=new Date;zt.setDate(zt.getDate()-15),d.useState([zt,ft]),d.useState([zt,ft]);const so=e=>{e!==null&&A(b({module:"GeneralLedger",filterData:{...l,createdOn:e}}))},Yt=e=>{var u,r;wn(!1),ut(0),Ze(!0);let n={glAccount:(l==null?void 0:l["G/L Account"])??"",chartOfAccount:(u=l==null?void 0:l.chartOfAccount)!=null&&u.code?(r=l==null?void 0:l.chartOfAccount)==null?void 0:r.code:"ETCN",postAutoOnly:"",companyCode:(l==null?void 0:l["Company Code"])??"",taxCategory:(l==null?void 0:l.TaxCategory)??"",glAcctLongText:(l==null?void 0:l.glAcctLongText)??"",postingWithoutTaxAllowed:(l==null?void 0:l.postingWithoutTaxAllowed)==="Allowed"?"X":(l==null?void 0:l.postingWithoutTaxAllowed)==="Not Allowed"?"Y":"",blockedForPostingInCOA:(l==null?void 0:l.blockedForPostingInCOA)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCOA)==="Unblocked"?"Y":"",blockedForPostingInCompany:(l==null?void 0:l.blockedForPostingInCompany)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCompany)==="Unblocked"?"Y":"",accountGroup:(l==null?void 0:l["Account Group"])??"",shortText:(l==null?void 0:l.shortText)??"",glAccountType:(l==null?void 0:l["G/L Account Type"])??"",fieldStatusGroup:(l==null?void 0:l["Field Status Group"])??"",openItemMgmtbyLedgerGroup:(l==null?void 0:l.openItemMgmtByLedgerGroup)==="True"?"X":(l==null?void 0:l.openItemMgmtByLedgerGroup)==="False"?"Y":"",openItemManagement:(l==null?void 0:l.openItemManagement)==="True"?"X":(l==null?void 0:l.openItemManagement)==="False"?"Y":"",reconAccountforAcctType:(l==null?void 0:l["Recon Account for Acct Type"])??"",fromDate:v(l==null?void 0:l.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:v(l==null?void 0:l.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:(l==null?void 0:l.createdBy)??"",top:100,skip:0};const s=o=>{var h,k,p,ne,z,X;if(o.statusCode===200){let M=[];(k=(h=o==null?void 0:o.body)==null?void 0:h.list)==null||k.forEach(E=>{if((E==null?void 0:E.COA)=="ETCN"){let U={};U.code=E==null?void 0:E.GLAccount,U.desc=E==null?void 0:E.GLAccount,M.push(U)}}),Object.values(M.reduce((E,U)=>(E[U.code]=U,E),{}));var a=[];for(let E=0;E<((ne=(p=o==null?void 0:o.body)==null?void 0:p.list)==null?void 0:ne.length);E++){var t=(z=o==null?void 0:o.body)==null?void 0:z.list[E],g={id:Gn(),chartOfAccount:(t==null?void 0:t.COA)!==""?t==null?void 0:t.COA:"NA",glAccountType:(t==null?void 0:t.Accounttype)!==""?t==null?void 0:t.Accounttype:"NA",glAcctLongText:(t==null?void 0:t.Description)!==""?t==null?void 0:t.Description:"NA",AccountGroup:(t==null?void 0:t.AccountGroup)!==""?t==null?void 0:t.AccountGroup:"NA",companyCode:(t==null?void 0:t.CompanyCode)!==""?t==null?void 0:t.CompanyCode:"NA",glAccount:(t==null?void 0:t.GLAccount)!==""?t==null?void 0:t.GLAccount:"NA",groupAccountNumber:(t==null?void 0:t.GroupAccNo)!==""?t==null?void 0:t.GroupAccNo:"NA",postingWithoutTaxAllowed:(t==null?void 0:t.Pstnwotax)==="X"?"Allowed":"Not Allowed",blockedForPostingInCOA:(t==null?void 0:t.PostingBlockedCOA)==="X"?"Blocked":"Unblocked",blockedForPostingInCompany:(t==null?void 0:t.PostingBlockedCoCd)==="X"?"Blocked":"Unblocked",postAutoOnly:(t==null?void 0:t.PostAuto)==="X"?"X":"",taxCategory:t.Taxcategory!==""?t==null?void 0:t.Taxcategory:"NA",createdBy:(t==null?void 0:t.CreatedBy)!==""?t==null?void 0:t.CreatedBy:"NA",shortText:(t==null?void 0:t.GLname)!==""?t==null?void 0:t.GLname:"NA",gLAccountType:(t==null?void 0:t.Accounttype)!==""?t==null?void 0:t.Accounttype:"NA",fieldStatusGroup:(t==null?void 0:t.FieldStsGrp)!==""?t==null?void 0:t.FieldStsGrp:"NA",openItemManagement:(t==null?void 0:t.Openitmmanage)!==""?t==null?void 0:t.Openitmmanage:"",openItemMgmtByLedgerGroup:(t==null?void 0:t.OIMgmtByLedgerGrp)!==""?t==null?void 0:t.OIMgmtByLedgerGrp:"",reconAccountForAcctType:(t==null?void 0:t.ReconAcc)!==""?t==null?void 0:t.ReconAcc:"NA",createdOn:(t==null?void 0:t.CreatedOn)!==""?`${v(t.CreatedOn).format("DD MMM YYYY")}`:"NA"};a.push(g)}a.sort((E,U)=>v(E.createdOn,"DD MMM YYYY HH:mm")-v(U.createdOn,"DD MMM YYYY HH:mm")),Rt(a),Ze(!1),fn(a.length),ln((X=o==null?void 0:o.body)==null?void 0:X.count)}else o.statusCode===400&&(Kt("Warning"),Jt("Please Select Lesser Fields as the URL is getting too long !!"),Ut())},i=o=>{G(o)};x(`/${S}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",s,i,n)},oo=e=>{var u,r;Ze(!0);let n={glAccount:(l==null?void 0:l["G/L Account"])??"",chartOfAccount:(u=l==null?void 0:l.chartOfAccount)!=null&&u.code?(r=l==null?void 0:l.chartOfAccount)==null?void 0:r.code:"ETCN",companyCode:(l==null?void 0:l["Company Code"])??"",postAutoOnly:"",taxCategory:(l==null?void 0:l.TaxCategory)??"",glAcctLongText:(l==null?void 0:l.glAcctLongText)??"",postingWithoutTaxAllowed:(l==null?void 0:l.postingWithoutTaxAllowed)==="Allowed"?"X":(l==null?void 0:l.postingWithoutTaxAllowed)==="Not Allowed"?"Y":"",blockedForPostingInCOA:(l==null?void 0:l.blockedForPostingInCOA)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCOA)==="Unblocked"?"Y":"",blockedForPostingInCompany:(l==null?void 0:l.blockedForPostingInCompany)==="Blocked"?"X":(l==null?void 0:l.blockedForPostingInCompany)==="Unblocked"?"Y":"",accountGroup:(l==null?void 0:l["Account Group"])??"",shortText:(l==null?void 0:l.shortText)??"",glAccountType:(l==null?void 0:l["G/L Account Type"])??"",fieldStatusGroup:(l==null?void 0:l["Field Status Group"])??"",openItemMgmtbyLedgerGroup:(l==null?void 0:l.openItemMgmtByLedgerGroup)==="True"?"X":(l==null?void 0:l.openItemMgmtByLedgerGroup)==="False"?"Y":"",openItemManagement:(l==null?void 0:l.openItemManagement)==="True"?"X":(l==null?void 0:l.openItemManagement)==="False"?"Y":"",reconAccountforAcctType:(l==null?void 0:l["Recon Account for Acct Type"])??"",fromDate:v(l==null?void 0:l.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:v(l==null?void 0:l.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",createdBy:(l==null?void 0:l.createdBy)??"",top:"100",skip:(ee==null?void 0:ee.length)??0};const s=o=>{var h,k,p,ne,z,X;if(o.statusCode===200){let M=[];(k=(h=o==null?void 0:o.body)==null?void 0:h.list)==null||k.forEach(E=>{if((E==null?void 0:E.COA)=="ETCN"){let U={};U.code=E==null?void 0:E.GLAccount,U.desc=E==null?void 0:E.GLAccount,M.push(U)}}),Object.values(M.reduce((E,U)=>(E[U.code]=U,E),{}));var a=[];for(let E=0;E<((ne=(p=o==null?void 0:o.body)==null?void 0:p.list)==null?void 0:ne.length);E++){var t=(z=o==null?void 0:o.body)==null?void 0:z.list[E],g={id:Gn(),chartOfAccount:(t==null?void 0:t.COA)!==""?t==null?void 0:t.COA:"NA",glAccountType:(t==null?void 0:t.Accounttype)!==""?t==null?void 0:t.Accounttype:"NA",glAcctLongText:(t==null?void 0:t.Description)!==""?t==null?void 0:t.Description:"NA",AccountGroup:(t==null?void 0:t.AccountGroup)!==""?t==null?void 0:t.AccountGroup:"NA",companyCode:(t==null?void 0:t.CompanyCode)!==""?t==null?void 0:t.CompanyCode:"NA",glAccount:(t==null?void 0:t.GLAccount)!==""?t==null?void 0:t.GLAccount:"NA",groupAccountNumber:(t==null?void 0:t.GroupAccNo)!==""?t==null?void 0:t.GroupAccNo:"NA",postingWithoutTaxAllowed:(t==null?void 0:t.Pstnwotax)==="X"?"Allowed":"Not Allowed",blockedForPostingInCOA:(t==null?void 0:t.PostingBlockedCOA)==="X"?"Blocked":"Unblocked",blockedForPostingInCompany:(t==null?void 0:t.PostingBlockedCoCd)==="X"?"Blocked":"Unblocked",postAutoOnly:(t==null?void 0:t.PostAuto)==="X"?"X":"",taxCategory:t.Taxcategory!==""?t==null?void 0:t.Taxcategory:"NA",createdBy:(t==null?void 0:t.CreatedBy)!==""?t==null?void 0:t.CreatedBy:"NA",shortText:(t==null?void 0:t.GLname)!==""?t==null?void 0:t.GLname:"NA",gLAccountType:(t==null?void 0:t.Accounttype)!==""?t==null?void 0:t.Accounttype:"NA",fieldStatusGroup:(t==null?void 0:t.FieldStsGrp)!==""?t==null?void 0:t.FieldStsGrp:"NA",openItemManagement:(t==null?void 0:t.Openitmmanage)!==""?t==null?void 0:t.Openitmmanage:"NA",openItemMgmtByLedgerGroup:(t==null?void 0:t.OIMgmtByLedgerGrp)!==""?t==null?void 0:t.OIMgmtByLedgerGrp:"NA",reconAccountForAcctType:(t==null?void 0:t.ReconAcc)!==""?t==null?void 0:t.ReconAcc:"NA",createdOn:(t==null?void 0:t.CreatedOn)!==""?`${v(t.CreatedOn).format("DD MMM YYYY")}`:"NA"};a.push(g)}a.sort((E,U)=>v(E.createdOn,"DD MMM YYYY HH:mm")-v(U.createdOn,"DD MMM YYYY HH:mm")),Rt(E=>[...E,...a]),Ze(!1),fn(a.length),ln((X=o==null?void 0:o.body)==null?void 0:X.count)}else o.statusCode===400&&(Kt("Warning"),Jt("Please Select Lesser Fields as the URL is getting too long !!"),Ut())},i=o=>{G(o)};x(`/${S}/data/getGeneralLedgersBasedOnAdditionalParams`,"post",s,i,n)};d.useState([]),d.useEffect(()=>{},[ze,Ye]),d.useState(null);const[jn,fn]=d.useState(0);d.useState(!1),d.useState(!1),d.useState(!1),d.useState(!1),d.useState(!1),d.useState(!1),d.useState(""),d.useState("");const[co,el]=d.useState(!1),[ro,L]=d.useState(""),[io,N]=d.useState(),_=()=>{el(!0)},tl=()=>{el(!1),D(""),N(""),L("")},Ut=()=>{_n(!0)},nl=()=>{_n(!1)};d.useState(null),d.useState(null),d.useState(null);const uo=()=>{A(Al({module:"GeneralLedger"})),ka(e=>{const n={...e};return Object.keys(n).forEach(s=>{Object.keys(n[s]).forEach(i=>{n[s][i]=""})}),n}),ct([]),rt([]),lt([]),at([]),st([]),ot([]),nt({}),It([]),kt([]),pt([]),Gt([]),Mt([]),Nt([]),Fe({})},go=e=>{const n=e==null?void 0:e.map(t=>ee.find(g=>g.id===t));var s=n==null?void 0:n.map(t=>t.company),i=new Set(s),u=n==null?void 0:n.map(t=>t.vendor),r=new Set(u),o=n==null?void 0:n.map(t=>t.paymentTerm),a=new Set(o);n.length>0?i.size===1?r.size===1?a.size!==1?(ht(!0),L("Error"),N("Invoice cannot be generated for vendors with different payment terms"),D("danger"),_()):ht(!1):(ht(!0),L("Error"),N("Invoice cannot be generated for multiple suppliers"),D("danger"),_()):(ht(!0),L("Error"),N("Invoice cannot be generated for multiple companies"),D("danger"),_()):ht(!0),Na(e),_a(n)};function ho(){A(Al({module:"GeneralLedger"})),Yt()}d.useState([]),d.useState([]);const[Dr,fo]=d.useState(!1);d.useState(null),d.useState(null),d.useState([]);const Wt=()=>{fo(!1)};d.useState(null),d.useState("");const Co=(e,n)=>({field:e,headerName:n,editable:!1,flex:1,renderCell:s=>{const i=s.value?s.value.split(",").map(o=>o.trim()):[],u=i.length-1;if(i.length===0)return"-";const r=o=>{const[a,...t]=o.split("-");return C(q,{children:[c("strong",{children:a}),t.length?` - ${t.join("-")}`:""]})};return C(se,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[c(Ce,{title:i[0],placement:"top",arrow:!0,children:c(H,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:r(i[0])})}),u>0&&c(se,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:c(Ce,{arrow:!0,placement:"right",title:C(se,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[C(H,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",n,"s (",u,")"]}),i.slice(1).map((o,a)=>c(H,{variant:"body2",sx:{mb:.5},children:r(o)},a))]}),children:C(se,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[c(InfoIcon,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),C(H,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",u]})]})})})]})}}),Ao=(e,n)=>({field:e,headerName:n,editable:!1,flex:1,renderCell:s=>{var r;const[i,...u]=((r=s.value)==null?void 0:r.split(" - "))||[];return C("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[c("strong",{children:i})," ",u.length?`- ${u.join(" - ")}`:""]})}}),yo=()=>({field:"dataValidation",headerName:$("Audit History"),editable:!1,flex:1,renderCell:e=>c(se,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:c(Ce,{title:"View Audit Log",placement:"top",children:c(Ge,{onClick:s=>{var i,u;s.stopPropagation(),Ue((i=wc)==null?void 0:i.AUDIT_LOG,{state:{materialNumber:e.row.glAccount,module:(u=Bc)==null?void 0:u.GL}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:c($c,{sx:{fontSize:"1.5rem"}})})})})}),xo=()=>{console.log("Error");let e={decisionTableId:null,decisionTableName:xl.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"General Ledger","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};wl(e)},Eo=()=>{let e={decisionTableId:null,decisionTableName:xl.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"General Ledger","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};Bl(e)},To=[{field:"chartOfAccount",headerName:"Chart Of Account",editable:!1,flex:1},{field:"compCode",headerName:"Company Code",editable:!1,flex:1},{field:"glAccount",headerName:"G/L Account",editable:!1,flex:1},{field:"glAcctLongText",headerName:"Long Text",editable:!1,flex:1},{field:"TaxCategory",headerName:"Tax Category",editable:!1,flex:1},{field:"postingWithoutTaxAllowed",headerName:"Posting Without Tax Allowed",editable:!1,flex:1},{field:"PostAutomaticallyOnly",headerName:"Post Automatically Only",editable:!1,flex:1},{field:"blockedForPostingInCOA",headerName:"Blocked For Posting In COA",editable:!1,flex:1},{field:"blockedForPostingInCompany",headerName:"Blocked For Posting In Company",editable:!1,flex:1}],So=[{field:"actions",align:"center",flex:1,headerAlign:"center",headerName:"Actions",sortable:!1,renderCell:e=>c("div",{children:c(Ce,{title:"Change",children:c(Ge,{"aria-label":"View Metadata",onClick:()=>zo(e.row),children:c(Rc,{})})})})}],po=Me==null?void 0:Me.map(e=>{const n=Is[e];return n?{field:n,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null),Go=[...To,...po,...So],Do=e=>{const n=[];let s=(e==null?void 0:e.sort((i,u)=>i.MDG_MAT_SEQUENCE_NO-u.MDG_MAT_SEQUENCE_NO))||[];return s&&(s==null||s.forEach(i=>{if((i==null?void 0:i.MDG_MAT_VISIBILITY)===Sc.DISPLAY&&i!=null&&i.MDG_MAT_UI_FIELD_NAME){const u=i.MDG_MAT_JSON_FIELD_NAME,r=i.MDG_MAT_UI_FIELD_NAME;u==="DataValidation"?n.push(yo()):i.MDG_MAT_FIELD_TYPE==="Multiple"?n.push(Co(u,r)):i.MDG_MAT_FIELD_TYPE==="Single"&&n.push(Ao(u,r))}})),n};d.useEffect(()=>{var e,n,s,i;if(dt){const u=Do((n=(e=dt==null?void 0:dt.result)==null?void 0:e[0])==null?void 0:n.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);Pl(u),console.log("col",u)}if(it){const u=(i=(s=it==null?void 0:it.result)==null?void 0:s[0])==null?void 0:i.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,r=u==null?void 0:u.filter(o=>o.MDG_MAT_FILTER_TYPE==="Additional").map(o=>({title:$(o.MDG_MAT_UI_FIELD_NAME)}));Rl(u),console.log("res",u,r),Ul(r)}},[dt,it]),d.useEffect(()=>{Yt()},[]),d.useEffect(()=>{xo(),Eo()},[]);let Mo=d.useRef(null);const Cn=()=>{Zl(!1)},Lo=()=>{let e=Io();if(e.length===0){P(!0);let n=[];je==null||je.map(u=>{var r={glAccount:u==null?void 0:u.glAccountNumberCreated,coa:u==null?void 0:u.chartOfAccount.toUpperCase()};n.push(r)});const s=u=>{var r,o;if(P(!1),u.body.length===0){let a=_o(),t=Object.values(a.reduce((g,h)=>(g[h.id]=h,g),{}));No(t)}else(r=u.body)==null||r.map(a=>a.glAccount),Ol(u.body),P(!1),L("Duplicate Check"),Se(!1),N(`There is a direct match for foowing Gl Account
                ${(o=u.body)==null?void 0:o.map(a=>a.glAccount).join(",")}`),D("danger"),ie(!1),de(!0),_(),setSubmitForReviewDisabled(!0)},i=u=>{};x(`/${S}/alter/fetchGlAccountNCoaDupliChkMass`,"post",s,i,n)}else{const n=e.map(s=>s.glAccountNumberCreated).join(",");P(!1),L("Duplicate Check"),Se(!1),N(`Please Review Following glAccountNumbers  ${n} , Something is Wrong In That`),D("danger"),ie(!1),de(!0),_(),setSubmitForReviewDisabled(!0)}},No=async e=>{let n=i=>{W(!1);const u=URL.createObjectURL(i),r=document.createElement("a");r.href=u,r.setAttribute("download","GeneraL Ledger_Mass Create With Copy.xlsx"),document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(u),_(),L("Success"),N("GeneraL Ledger_Mass Create with Copy.xlsx has been downloaded successfully"),D("success")},s=i=>{i.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"))};x(`/${S}/excel/downloadExcelForMassCreateWithCopy`,"postandgetblob",n,s,e)},_o=()=>{const e=[];return je.forEach((n,s)=>{let i={};Wn.forEach(u=>{n.accountType===u.MDG_GL_ACCOUNT_TYPE_CODE&&n.accountGroup===u.MDG_GL_ACCOUNT_GRP_CODE&&n.subAccountGroup===u.MDG_GL_SUB_ACCOUNT_GRP&&(i.id=s,i.chartOfAccount="ETCN",i.accountType=n==null?void 0:n.accountType,i.accountGroup=n==null?void 0:n.accountGroup,i.subAccountGroup=n==null?void 0:n.subAccountGroup,i.glAccountNumberCreated=n==null?void 0:n.glAccountNumberCreated,i.compCodeCreated=n==null?void 0:n.compCodeCreated,i.glAccountNumberCopyFrom=u==null?void 0:u.MDG_GL_GLACCOUNT_NO,i.compCodeCopyFrom=u==null?void 0:u.MDG_GL_COMPANY_CODE,e.push(i))})}),e},Io=()=>{const e=[];return je.forEach(n=>{let s=!1;for(const i of Wn)if(n.accountType===i.MDG_GL_ACCOUNT_TYPE_CODE&&n.accountGroup===i.MDG_GL_ACCOUNT_GRP_CODE&&n.subAccountGroup===i.MDG_GL_SUB_ACCOUNT_GRP){const u=parseInt(n.glAccountNumberCreated,10),r=parseInt(i.MDG_GL_L1_RANGE,10),o=parseInt(i.MDG_GL_L2_RANGE,10);if(u>=r&&u<=o){s=!0;break}}s||e.push(n)}),bl(e),e},ko=()=>{A(setFields(selectedFieldsforCCSunSlice)),W(!0),setIsCheckboxSelected(!1),yn()},Po=async()=>{R("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),P(!0);let e=s=>{if((s==null?void 0:s.size)!==0||(s==null?void 0:s.type)!==""){P(!1),R("");const i=URL.createObjectURL(s),u=document.createElement("a");u.href=i,u.setAttribute("download","General Ledger Mass Create.xlsx"),document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(i),_(),L("Success"),N("General Ledger Mass Create.xlsx has been downloaded successfully"),D("success")}else P(!1),R(!1),_(),L("Error"),N("Please try again."),D("danger")},n=s=>{s.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"))};x(`/${S}/excel/downloadExcel`,"getblobfile",e,n)},wo=async()=>{P(!0);let e=s=>{P(!1),R(""),_(),L("Success"),N("Download has been started. You will get the Excel file via email"),D("success")},n=s=>{s.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"))};x(`/${S}/excel/downloadExcelInMail`,"get",e,n)},An=()=>{W(!0);let e={};e.GlAccount=[],e.templateName=Ht(w),j==null||j.map(o=>({compCode:o==null?void 0:o.compCode,glAccount:o==null?void 0:o.glAccount,coa:o==null?void 0:o.chartOfAccount}));let n={},s=["GL Account","Chart Of Account","Company Code","Account Type","Account Group","Business Segment","OIM/CSL Indicator","Tax Field Check"],i=[];Y==="Company Code"?(y.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,i.push(g)}})}),A(setFields(i))):(T.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,i.push(g)}})}),A(setFields(i))),i==null||i.map(o=>{s==null||s.push(o.name)}),n.GlAccount=[],n.headers=s,n.templateHeaders="",n.templateName="",R("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),P(!0);let u=o=>{if((o==null?void 0:o.size)!==0||(o==null?void 0:o.type)!==""){P(!1),R(""),W(!1);const a=URL.createObjectURL(o),t=document.createElement("a");t.href=a,t.setAttribute("download","General Ledger Mass Change.xlsx"),document.body.appendChild(t),t.click(),document.body.removeChild(t),URL.revokeObjectURL(a),_(),L("Success"),N("General Ledger Mass Change.xlsx has been downloaded successfully"),$e(!1),D("success")}else P(!1),R(!1),_(),L("Error"),N("Please try again."),D("danger")},r=o=>{o.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"),$e(!1))};Y==="Change Temporary Block"?x(`/${S}/excel/downloadExcelWithDataForTempBlock`,"postandgetblob",u,r,e):x(`/${S}/excel/downloadExcelWithData`,"postandgetblob",u,r,n)},Bo=()=>{W(!0);let e={};e.GlAccount=[],e.templateName=Ht(w),j==null||j.map(o=>({compCode:o==null?void 0:o.compCode,glAccount:o==null?void 0:o.glAccount,coa:o==null?void 0:o.chartOfAccount}));let n={},s=["GL Account","Chart Of Account","Company Code","Account Type","Account Group","Business Segment","OIM/CSL Indicator","Tax Field Check"],i=[];Y==="Company Code"?(y.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,i.push(g)}})}),A(setFields(i))):(T.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,i.push(g)}})}),A(setFields(i))),i==null||i.map(o=>{s==null||s.push(o.name)}),n.GlAccount=[],n.headers=s,n.templateHeaders="",n.templateName="",P(!0);let u=o=>{P(!1),R(""),_(),L("Success"),N("Download has been started. You will get the Excel file via email"),D("success")},r=o=>{o.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"),$e(!1))};Y==="Change Temporary Block"?x(`/${S}/excel/downloadExcelWithDataForTempBlockInMail`,"post",u,r,e):x(`/${S}/excel/downloadExcelWithDataInMail`,"post",u,r,n)},Ht=e=>{if(e.includes("Temp Block/Unblock-Post Automatically Only"))return"poAutoOnly";if(e.includes("Temp Block/Unblock-Comp Code"))return"blockCCLevel";if(e.includes("Temp Block/Unblock-COA"))return"blockCoaLevel"},yn=()=>{W(!0);var e=j==null?void 0:j.map(a=>({compCode:a==null?void 0:a.compCode,glAccount:a==null?void 0:a.glAccount,coa:a==null?void 0:a.chartOfAccount}));let n={},s=["GL Account","Chart Of Account","Company Code","Account Type","Account Group","Business Segment","OIM/CSL Indicator","Tax Field Check"],i={},u=[];Y==="Company Code"?(y.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))):Y==="Chart of Account"?(T.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))):(w.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))),u==null||u.map(a=>{s==null||s.push(a.name)}),i.GlAccount=e,i.templateName=Ht(w),n.GlAccount=e,n.headers=s,n.templateHeaders="",n.templateName="",R("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),P(!0);let r=a=>{if((a==null?void 0:a.size)!==0||(a==null?void 0:a.type)!==""){P(!1),R(""),W(!1),$e(!1);const t=URL.createObjectURL(a),g=document.createElement("a");g.href=t,g.setAttribute("download","GeneraL Ledger_Mass Change.xlsx"),document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(t),_(),L("Success"),N("GeneraL Ledger_Mass Change.xlsx has been downloaded successfully"),D("success")}else P(!1),R(!1),_(),L("Error"),N("Please try again."),D("danger")},o=a=>{a.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"))};Y==="Change Temporary Block"?x(`/${S}/excel/downloadExcelWithDataForTempBlock`,"postandgetblob",r,o,i):x(`/${S}/excel/downloadExcelWithData`,"postandgetblob",r,o,n)},ll=()=>{W(!0);var e=j==null?void 0:j.map(a=>({compCode:a==null?void 0:a.compCode,glAccount:a==null?void 0:a.glAccount,coa:a==null?void 0:a.chartOfAccount}));let n={},s=["GL Account","Chart Of Account","Company Code","Account Type","Account Group","Business Segment","OIM/CSL Indicator","Tax Field Check"],i={},u=[];Y==="Company Code"?(y.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))):Y==="Chart of Account"?(T.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))):(w.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a.name){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,u.push(h)}})}),A(setFields(u))),u==null||u.map(a=>{s==null||s.push(a.name)}),i.GlAccount=e,i.templateName=Ht(w),n.GlAccount=e,n.headers=s,n.templateHeaders="",n.templateName="",P(!0);let r=a=>{P(!1),R(""),W(!1),$e(!1),_(),L("Success"),N("Download has been started. You will get the Excel file via email"),D("success")},o=a=>{a.message&&(P(!1),R(!1),_(),L("Error"),N("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),D("danger"))};Y==="Change Temporary Block"?x(`/${S}/excel/downloadExcelWithDataForTempBlockInMail`,"post",r,o,i):x(`/${S}/excel/downloadExcelWithDataInMail`,"post",r,o,n)},Ro=e=>{const n=i=>{var t;let u=[];(t=i.body)==null||t.map(g=>{let h={};h.code=g==null?void 0:g.code,h.desc=g==null?void 0:g.desc,u.push(h)});const r=u==null?void 0:u.filter(g=>!(g!=null&&g.code.startsWith("7"))).concat(u.filter(g=>{var h;return(h=g==null?void 0:g.code)==null?void 0:h.startsWith("7")})),a=[{title:"Select All",group:null},...(g=>g==null?void 0:g.map(h=>({title:`${h==null?void 0:h.code} - ${h==null?void 0:h.desc}`,group:h!=null&&h.code.startsWith("7")?"SUN Active Company Code":"ET Active Company Code"})))(r)];A(B({keyName:"CompanyCodeForWithCopy",data:a}))},s=i=>{G(i)};x(`/${S}/data/getCompanyCode?chartAccount=${e==null?void 0:e.code}`,"get",n,s)},al=e=>{var u;O(!0);let n={coa:e||((u=l==null?void 0:l.chartOfAccount)==null?void 0:u.code),top:200,skip:0};const s=r=>{A(B({keyName:"TaxCategory",data:r.body})),O(!1)},i=r=>{G(r)};x(`/${S}/data/getSearchParamsTaxCategory`,"post",s,i,n)},$o=e=>{const n=i=>{Un(u=>({...u,"Account Group":i.body}))},s=i=>{G(i)};x(`/${S}/data/getAccountGroupCodeDesc?chartAccount=${e}`,"get",n,s)},sl={convertJsonToExcel:()=>{let e=[];$n.forEach(n=>{n.headerName.toLowerCase()!=="action"&&!n.hide&&e.push({header:n.headerName,key:n.field})}),Dn({fileName:`Duplicate Requests -${v(ft).format("DD-MMM-YYYY")}`,columns:e,rows:vt})},button:()=>c(m,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>sl.convertJsonToExcel(),children:"Download"})},xn=()=>{oa(!1)},zo=e=>{fe(!0),$a(e)},ol=()=>{De(!1),$e(!1),fe(!1),Kl(!1),ce([]),re([]),$e(!1)},Yo=()=>{qe!==!0&&pe.length>=0?cl():Rn(!0)},cl=()=>{var r;if(qe===!0&&pe.length===0)if((y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0||(w==null?void 0:w.length)>0){An(),fe(!1),ce([]),re([]),w([]),De(!1);return}else L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_();let e=[];if(Y==="Company Code")y.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,e.push(g)}})});else if(Y==="Chart of Account")T.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,e.push(g)}})});else{let o=[];w.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,o.push(h),e.push(h)}})}),A(setFields(o))}let n=(r=e==null?void 0:e.map(o=>o==null?void 0:o.name))==null?void 0:r.join(","),s=[];qe===!0&&pe.length>0?s=j.map(o=>({glAccount:o.glAccount,compCode:o.compCode,changedFieldsToCheck:n})):s=[{glAccount:Q==null?void 0:Q.glAccount,compCode:Q==null?void 0:Q.compCode,changedFieldsToCheck:n}];const i=o=>{if(o.some(t=>t.statusCode!==200)){const t=o.filter(h=>h.statusCode===400);let g=[];t==null||t.map((h,k)=>{var z,X,M,E,U,Ct,At,yt;let p={};const ne=(X=(z=h==null?void 0:h.message)==null?void 0:z.match(/GL Account:\s*(\d+)/))==null?void 0:X[1];p.id=k,p.glAccount=ne,p.reqId=((E=(M=h==null?void 0:h.body)==null?void 0:M.EditIds)==null?void 0:E[0])||((Ct=(U=h==null?void 0:h.body)==null?void 0:U.MassEditIds)==null?void 0:Ct[0]),p.requestedBy=(yt=(At=h==null?void 0:h.body)==null?void 0:At.RequestCreatedBy)==null?void 0:yt[0],g.push(p)}),Ln(g),St(!0)}else if(qe===!0)pe.length>0?(y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(yn(),fe(!1),ce([]),re([]),gt([]),De(!1)):(w==null?void 0:w.length)>0?(yn(),fe(!1),ce([]),re([]),gt([]),De(!1)):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_()):pe.length===0&&((y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(An(),fe(!1),ce([]),re([]),De(!1)):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_()));else{if(Y==="Company Code"){let t=[];y.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g.name){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}else if(Y==="Chart of Account"){let t=[];T.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g.name){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}else{let t=[];w.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}(y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(A(xt()),A(Xt()),Ue("/masterDataCockpitNew/generalLedger/changeGLField",{state:Q})):(w==null?void 0:w.length)>0?(A(xt()),A(Xt()),Ue("/masterDataCockpitNew/generalLedger/changeGLFieldTemporaryBlock",{state:Q})):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_())}};let u=o=>{o.message&&(_(),D("danger"))};x(`/${S}/alter/checkDuplicateGLRequest`,"post",i,u,s)},Uo=()=>{var r;if(qe===!0&&pe.length===0)if((y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0||(w==null?void 0:w.length)>0){Bo(),fe(!1),ce([]),re([]),w([]),De(!1);return}else L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_();let e=[];if(Y==="Company Code")y.forEach(o=>{F.map((a,t)=>{if(a.MDG_SELECT_OPTION===o.name){let g={};g.id=t,g.name=a.MDG_FIELD_NAME,e.push(g)}})});else if(Y!=="Chart Of Account"){let o=[];w.forEach(a=>{F.map((t,g)=>{if(t.MDG_SELECT_OPTION===a){let h={};h.id=g,h.name=t.MDG_FIELD_NAME,o.push(h)}})}),A(setFields(o))}let n=(r=e==null?void 0:e.map(o=>o==null?void 0:o.name))==null?void 0:r.join(","),s=[];qe===!0&&pe.length>0?s=j.map(o=>({glAccount:o.glAccount,compCode:o.compCode,changedFieldsToCheck:n})):s=[{glAccount:Q==null?void 0:Q.glAccount,compCode:Q==null?void 0:Q.compCode,changedFieldsToCheck:n}];const i=o=>{if(o.some(t=>t.statusCode!==200)){const t=o.filter(h=>h.statusCode===400);let g=[];t==null||t.map((h,k)=>{var z,X,M,E,U,Ct,At,yt;let p={};const ne=(X=(z=h==null?void 0:h.message)==null?void 0:z.match(/GL Account:\s*(\d+)/))==null?void 0:X[1];p.id=k,p.glAccount=ne,p.reqId=((E=(M=h==null?void 0:h.body)==null?void 0:M.EditIds)==null?void 0:E[0])||((Ct=(U=h==null?void 0:h.body)==null?void 0:U.MassEditIds)==null?void 0:Ct[0]),p.requestedBy=(yt=(At=h==null?void 0:h.body)==null?void 0:At.RequestCreatedBy)==null?void 0:yt[0],g.push(p)}),Ln(g),St(!0)}else if(qe===!0)pe.length>0?(y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(ll(),fe(!1),ce([]),re([]),gt([]),De(!1)):(w==null?void 0:w.length)>0?(ll(),fe(!1),ce([]),re([]),gt([]),De(!1)):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_()):pe.length===0&&((y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(An(),fe(!1),ce([]),re([]),De(!1)):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_()));else{if(Y==="Company Code"){let t=[];y.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g.name){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}else if(Y==="Chart of Account"){let t=[];T.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g.name){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}else{let t=[];w.forEach(g=>{F.map((h,k)=>{if(h.MDG_SELECT_OPTION===g){let p={};p.id=k,p.name=h.MDG_FIELD_NAME,t.push(p)}})}),A(setFields(t))}(y==null?void 0:y.length)>0||(T==null?void 0:T.length)>0?(A(xt()),A(Xt()),Ue("/masterDataCockpitNew/generalLedger/changeGLField",{state:Q})):(w==null?void 0:w.length)>0?(A(xt()),A(Xt()),Ue("/masterDataCockpitNew/generalLedger/changeGLFieldTemporaryBlock",{state:Q})):(L("Error"),N("Please Select Any Field To Proceed?"),D("danger"),_())}},u=o=>{G(o)};x(`/${S}/alter/checkDuplicateGLRequest`,"post",i,u,s)},Wo=()=>{ta(!1)},rl=()=>{Fa(!1)},dl=()=>{Va(!1)},Ho=e=>{if(!e){zn([...ee]);return}const n=ee.filter(s=>{var o;let i=!1,u=Object.keys(s);const r=Go.map(a=>a.field);for(let a=0;a<u.length&&!(r.includes(u[a])&&(i=s[u[a]]?(s==null?void 0:s[u==null?void 0:u[a]])&&((o=s==null?void 0:s[u==null?void 0:u[a]].toString().toLowerCase())==null?void 0:o.indexOf(e==null?void 0:e.toLowerCase()))!=-1:!1,i));a++);return i});zn([...n]),Rt([...n])},Fo=e=>{gt([e.target.value])},il={convertJsonToExcel:()=>{let e=[];Zn.forEach(n=>{n.headerName.toLowerCase()!=="action"&&!n.hide&&e.push({header:n.headerName,key:n.field})}),Dn({fileName:`Error log Data-${v(ft).format("DD-MMM-YYYY")}`,columns:e,rows:Hn})},button:()=>c(m,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>il.convertJsonToExcel(),children:"Download"})},ul={convertJsonToExcel:()=>{let e=[];bn.forEach(n=>{n.headerName.toLowerCase()!=="action"&&!n.hide&&e.push({header:n.headerName,key:n.field})}),Dn({fileName:`Error log Data-${v(ft).format("DD-MMM-YYYY")}`,columns:e,rows:Fn})},button:()=>c(m,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>ul.convertJsonToExcel(),children:"Download"})};return C("div",{ref:Mo,children:[C(Ne,{open:qa,fullWidth:!0,onClose:dl,sx:{"& .MuiDialog-paper":{minWidth:"800px",minheight:"500px"}},children:[C(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[c(H,{variant:"h6",color:"red",children:"Following Gl is already Created Or In Workflow in following Chart Of Account"}),C(I,{md:1,children:[c(Ce,{title:"Export Table",children:c(Ge,{sx:Sn,onClick:ul.convertJsonToExcel,children:c(pn,{iconName:"IosShare"})})}),c(Ge,{sx:{width:"max-content"},onClick:dl,children:c(Vt,{})})]})]}),c(Ie,{sx:{padding:".5rem 1rem"},children:Wa&&c(Ft,{width:"100%",rows:Fn,columns:bn,pageSize:10,getRowIdValue:"glAccount",getRowId:"glAccount",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),c(ke,{sx:{display:"flex",justifyContent:"end"}})]}),C(Ne,{open:Nl,onClose:()=>St(!1),maxWidth:"md",fullWidth:!0,children:[c(_e,{sx:{bgcolor:"#FFDAB9",color:"warning.contrastText"},children:C(H,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[C("span",{children:[c(pc,{sx:{mr:1}})," Duplicate Requests Alert"]}),c(Ce,{title:"Export Table",children:c(Ge,{sx:Sn,onClick:sl.convertJsonToExcel,children:c(pn,{iconName:"IosShare"})})})]})}),c(Ie,{children:c("div",{style:{marginTop:"20px"},children:c(Ft,{height:400,rows:vt,columns:$n,pageSize:vt.length,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})})}),c(ke,{children:c(m,{variant:"contained",onClick:()=>{St(!1),fe(!1)},children:"OK"})})]}),C(Ne,{open:la,onClose:en,children:[c(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:c(H,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),c(Ie,{children:c(J,{children:C(Tn,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:me,onChange:Aa,children:[c(Bt,{arrow:!0,placement:"bottom",title:c("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:c(K,{value:"systemGenerated",control:c(Et,{}),label:"System-Generated"})}),c(Bt,{arrow:!0,placement:"bottom",title:c("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:c(K,{value:"mailGenerated",control:c(Et,{}),label:"Mail-Generated"})})]})})}),c(ke,{children:c(m,{variant:"contained",onClick:xa,children:"OK"})})]}),C(Ne,{open:ha,onClose:tn,children:[c(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:c(H,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),c(Ie,{children:c(J,{children:C(Tn,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:me,onChange:ya,children:[c(Bt,{arrow:!0,placement:"bottom",title:c("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:c(K,{value:"systemGenerated",control:c(Et,{}),label:"System-Generated"})}),c(Bt,{arrow:!0,placement:"bottom",title:c("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:c(K,{value:"mailGenerated",control:c(Et,{}),label:"Mail-Generated"})})]})})}),c(ke,{children:c(m,{variant:"contained",onClick:Ea,children:"OK"})})]}),c(Gc,{blurLoading:ca,loaderMessage:ra}),C(Ne,{open:Ha,fullWidth:!0,onClose:rl,sx:{"& .MuiDialog-paper":{minWidth:"1000px",minheight:"500px"}},children:[C(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[c(H,{variant:"h6",color:"red",children:"Errors For GL Account Range Is Not Matched"}),C(I,{md:1,children:[c(Ce,{title:"Export Table",children:c(Ge,{sx:Sn,onClick:il.convertJsonToExcel,children:c(pn,{iconName:"IosShare"})})}),c(Ge,{sx:{width:"max-content"},onClick:rl,children:c(Vt,{})})]})]}),c(Ie,{sx:{padding:".5rem 1rem"},children:Ua&&c(Ft,{width:"100%",rows:Hn,columns:Zn,pageSize:10,getRowIdValue:"GLAccount",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})}),c(ke,{sx:{display:"flex",justifyContent:"end"}})]}),C(Ne,{open:ea,children:[c(_e,{children:"Error"}),c(Ie,{children:c(Dc,{children:"Please select Company Codes to be extended for all selected GLs"})}),c(ke,{children:c(m,{onClick:Wo,color:"primary",children:"OK"})})]}),C(Ne,{open:Ql,onClose:Cn,sx:{"& .MuiDialog-paper":{minWidth:"800px",minheight:"500px"}},children:[C(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[C(I,{sx:{display:"block"},children:[c(H,{variant:"h7",children:"Mass Create With Copy"}),c(H,{variant:"body2",color:"#777",children:"To view Company Code"})]}),c(Ge,{sx:{width:"max-content"},onClick:Cn,children:c(Vt,{})})]}),c(Ie,{sx:{padding:".5rem 1rem",overflowY:"auto"},children:c("div",{style:{height:500,width:"100%"},children:c(Mc,{rows:je,columns:Xn,getRowHeight:()=>"auto"})})}),C(ke,{sx:{display:"flex",justifyContent:"end"},children:[c(m,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Cn,children:"Cancel"}),c(m,{className:"button_primary--normal",type:"save",onClick:Lo,variant:"contained",children:"Apply"})]})]}),C("div",{children:[C(Ne,{open:vl,onClose:ol,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[c(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:C(I,{children:[c(I,{sx:{display:"flex",justifyContent:"space-between"},children:C(ec,{color:"primary",value:Y,exclusive:!0,onChange:Oa,"aria-label":"Platform",children:[c(En,{value:"Company Code",disabled:(T==null?void 0:T.length)!==0,children:"Company Code"}),c(En,{value:"Chart of Account",disabled:(y==null?void 0:y.length)!==0,children:"Chart of Account"}),c(En,{value:"Change Temporary Block",disabled:(y==null?void 0:y.length)!==0,children:"TEMP BLOCK/UNBLOCK"})]})}),c(I,{children:c(H,{variant:"h6",children:"Select the field(s) to be changed"})})]})}),c(Ie,{sx:{padding:".5rem 1rem",maxHeight:400},children:c(I,{container:!0,children:Y==="Company Code"?C(q,{children:[c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:Kn,checked:be}),sx:{height:{xs:"3vh"}},label:"SELECT ALL"})}),ze==null?void 0:ze.map(e=>{var n;return c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:()=>vn(e),checked:y==null?void 0:y.some(s=>s.id===e.id)}),label:(n=e.name)==null?void 0:n.toUpperCase(),sx:{height:{xs:"3vh"}}})},e==null?void 0:e.id)})]}):Y==="Chart of Account"?C(q,{children:[c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:Kn,checked:be}),sx:{height:{xs:"3vh"}},label:"SELECT ALL"})}),Ye==null?void 0:Ye.map(e=>{var n;return c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:()=>Jn(e),checked:T==null?void 0:T.some(s=>s.id===e.id)}),sx:{height:{xs:"3vh"}},label:(n=e.name)==null?void 0:n.toUpperCase()})},e.id)})]}):c(q,{children:c(Tn,{value:w,onChange:Fo,"aria-label":"radio-options",children:ua.map(e=>{var n;return c(K,{value:e.name.toString(),control:c(Et,{}),label:(n=e.name)==null?void 0:n.toUpperCase(),sx:{height:{xs:"3vh"}}},e.name)})})})})}),C(ke,{sx:{display:"flex",justifyContent:"end"},children:[c(m,{sx:{width:"max-content",textTransform:"capitalize"},onClick:ol,children:"Cancel"}),c(m,{className:"button_primary--normal",type:"save",onClick:Yo,variant:"contained",children:"Apply"})]})]}),C(Ne,{open:sa,onClose:xn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[C(_e,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[c(H,{variant:"h6",children:"Select the field(s) to be changed"}),c(Ge,{sx:{width:"max-content"},onClick:xn,children:c(Vt,{})})]}),c(Ie,{sx:{padding:".5rem 1rem"},children:c(I,{container:!0,children:Y==="Company Code"?ze==null?void 0:ze.map(e=>c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:()=>vn(e),checked:y.some(n=>n.id===e.id)}),sx:{height:{xs:"3vh"}},label:e.name})},e.id)):Ye==null?void 0:Ye.map(e=>c(I,{item:!0,xs:12,children:c(K,{control:c(le,{onChange:()=>Jn(e),checked:T.some(n=>n.id===e.id)}),sx:{height:{xs:"3vh"}},label:e.name})},e.id))})}),C(ke,{sx:{display:"flex",justifyContent:"end"},children:[c(m,{sx:{width:"max-content",textTransform:"capitalize"},onClick:xn,children:"Cancel"}),c(m,{className:"button_primary--normal",type:"save",onClick:ko,variant:"contained",children:"Apply"})]})]})]}),c(yl,{dialogState:co,openReusableDialog:_,closeReusableDialog:tl,dialogTitle:ro,dialogMessage:io,handleDialogConfirm:tl,dialogOkText:"OK",dialogSeverity:Il}),c(yl,{dialogState:pl,openReusableDialog:Ut,closeReusableDialog:nl,dialogTitle:Gl,dialogMessage:Dl,handleDialogConfirm:nl,dialogSeverity:"danger",showCancelButton:!1,dialogOkText:"OK"}),c("div",{style:{...tc,backgroundColor:"#FAFCFF"},children:C(hl,{spacing:1,children:[c(I,{container:!0,sx:nc,children:C(I,{item:!0,md:5,sx:lc,children:[c(H,{variant:"h3",children:c("strong",{children:$("General Ledger")})}),c(H,{variant:"body2",color:"#777",children:$("This view displays the list of General Ledgers")})]})}),c(I,{container:!0,sx:ac,children:c(I,{item:!0,md:12,children:C(Fc,{defaultExpanded:!1,children:[C(qc,{expandIcon:c(sc,{sx:{fontSize:"1.25rem",color:Ot.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterGL",children:[c(oc,{sx:{fontSize:"1.25rem",marginRight:1,color:Ot.palette.primary.dark}}),c(H,{sx:{fontSize:"0.875rem",fontWeight:600,color:Ot.palette.primary.dark},children:$("Filter General Ledger")})]}),C(cc,{sx:{padding:"0.5rem 1rem 0.5rem"},children:[C(I,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[Qt==null?void 0:Qt.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,n)=>e.MDG_MAT_SEQUENCE_NO-n.MDG_MAT_SEQUENCE_NO).map((e,n)=>{var s,i,u;return C(et.Fragment,{children:[(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.CHARTOFACC&&C(I,{item:!0,md:2,children:[c(Z,{children:$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),c(J,{size:"small",fullWidth:!0,children:c(We,{sx:{height:"31px"},fullWidth:!0,size:"small",value:l==null?void 0:l.chartOfAccount,onChange:ls,noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",options:(f==null?void 0:f.NewChartOfAccounts)??[],getOptionLabel:r=>r!=null&&r.code?`${r==null?void 0:r.code} - ${r==null?void 0:r.desc}`??"":"",renderOption:(r,o)=>c("li",{...r,children:c(H,{style:{fontSize:12},children:o!=null&&o.desc?C(q,{children:[C("strong",{children:[o==null?void 0:o.code," "]})," -"," ",o==null?void 0:o.desc]}):C("strong",{children:[o==null?void 0:o.code," "]})})}),renderInput:r=>c(we,{sx:{fontSize:"12px !important"},...r,variant:"outlined",placeholder:"Select Chart Of Account"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.GLACC&&C(I,{item:!0,md:2,children:[C(Z,{children:[$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," "]}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:Wl,noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(r,o,a)=>{var t;if(a==="clear"||(o==null?void 0:o.length)===0){rt([]),kt([]);return}(o==null?void 0:o.length)>0&&((t=o[(o==null?void 0:o.length)-1])==null?void 0:t.code)==="Select All"?os():rt(o)},renderTags:(r,o)=>{var a;return(r==null?void 0:r.length)>0?C(q,{children:[c(ae,{label:(a=r[0])==null?void 0:a.code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(r==null?void 0:r.length)>1&&c(ae,{label:`+${(r==null?void 0:r.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},limitTags:1,options:(s=f==null?void 0:f.GLSearchData)!=null&&s.length?[{code:"Select All",desc:"Select All"},...f==null?void 0:f.GLSearchData]:(f==null?void 0:f.GLSearchData)??[],getOptionLabel:r=>r!=null&&r.code?`${r==null?void 0:r.code}`??"":"",renderOption:(r,o,{selected:a})=>{var t;return c("li",{...r,children:c(Xe,{children:c(K,{control:c(le,{checked:Gs(o)||(o==null?void 0:o.code)==="Select All"&&(xe==null?void 0:xe.length)===((t=f==null?void 0:f.GLSearchData)==null?void 0:t.length)}),label:C(q,{children:[C("strong",{children:[o==null?void 0:o.code," "]})," -"," ",o==null?void 0:o.desc]})})})})},renderInput:r=>{const o=r.inputProps.onChange;return c(Ce,{title:Nn.length<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:Nn.length>=4,placement:"top",children:c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...r,variant:"outlined",placeholder:"Select General Ledger",inputProps:{...r.inputProps,onChange:a=>{const t=a.target.value.replace(/\s+/g,"");As(a),o&&o({...a,target:{...a.target,value:t}})}}})})}})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.COMPANYCODE&&C(I,{item:!0,md:2,children:[C(Z,{children:[$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," "]}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,limitTags:1,disableCloseOnSelect:!0,value:(he==null?void 0:he.length)>0?he:(_t==null?void 0:_t.length)>0?_t:[],noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(r,o,a)=>{var t;if(a==="clear"||(o==null?void 0:o.length)===0){ct([]),It([]);return}(o==null?void 0:o.length)>0&&((t=o[(o==null?void 0:o.length)-1])==null?void 0:t.code)==="Select All"?ss():ct(o)},renderTags:(r,o)=>{var a;return(r==null?void 0:r.length)>0?C(q,{children:[c(ae,{label:(a=r[0])==null?void 0:a.code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(r==null?void 0:r.length)>1&&c(ae,{label:`+${(r==null?void 0:r.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},options:[{code:"Select All",desc:""},...(f==null?void 0:f.CompanyCode)??[]],getOptionLabel:r=>r!=null&&r.code?`${r==null?void 0:r.code}`??"":"",renderOption:(r,o,{selected:a})=>{var t;return c("li",{...r,children:c(Xe,{children:c(K,{control:c(le,{checked:ps(o)||(o==null?void 0:o.code)==="Select All"&&(he==null?void 0:he.length)===((t=f==null?void 0:f.CompanyCode)==null?void 0:t.length)}),label:C(q,{children:[C("strong",{children:[o==null?void 0:o.code," "]})," -"," ",o==null?void 0:o.desc]})})})})},renderInput:r=>c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...r,variant:"outlined",placeholder:"Select Company Code"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.LONGGLTEXT&&C(I,{item:!0,md:2,children:[C(Z,{children:[$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," "]}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(r,o,a)=>{var t;if(a==="clear"||(o==null?void 0:o.length)===0){lt([]),pt([]);return}(o==null?void 0:o.length)>0&&((t=o[(o==null?void 0:o.length)-1])==null?void 0:t.code)==="Select All"?is():lt(o)},renderTags:(r,o)=>{var a;return(r==null?void 0:r.length)>0?C(q,{children:[c(ae,{label:(a=r[0])==null?void 0:a.code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(r==null?void 0:r.length)>1&&c(ae,{label:`+${(r==null?void 0:r.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},limitTags:1,value:Hl,options:(i=f==null?void 0:f.GLAcctLongText)!=null&&i.length?[{code:"Select All"},...f==null?void 0:f.GLAcctLongText]:(f==null?void 0:f.GLAcctLongText)??[],onInputChange:Qa,filterOptions:(r,{inputValue:o})=>r.filter(a=>{var t,g;return(g=(t=a==null?void 0:a.code)==null?void 0:t.toLowerCase())==null?void 0:g.startsWith(o==null?void 0:o.toLowerCase())}).slice(0,500),getOptionLabel:r=>r!=null&&r.code?`${r==null?void 0:r.code}`??"":"",renderOption:(r,o,{selected:a})=>{var t;return c("li",{...r,children:c(Xe,{children:c(K,{control:c(le,{checked:Ls(o)||(o==null?void 0:o.code)==="Select All"&&(Ae==null?void 0:Ae.length)===((t=f==null?void 0:f.GLAcctLongText)==null?void 0:t.length)}),label:c(q,{children:C("strong",{children:[o==null?void 0:o.code," "]})})})})})},renderInput:r=>c(Ce,{title:(Qe==null?void 0:Qe.length)<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:(Qe==null?void 0:Qe.length)>=4,placement:"top",children:c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...r,variant:"outlined",placeholder:"Search Long Text",onChange:o=>{ys(o)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.TAXCATO&&C(I,{item:!0,md:2,children:[C(Z,{children:[$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)," "]}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,limitTags:1,value:(ue==null?void 0:ue.length)>0?ue:(Dt==null?void 0:Dt.length)>0?Dt:[],disableCloseOnSelect:!0,noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(r,o,a)=>{var t;if(a==="clear"||(o==null?void 0:o.length)===0){st([]),Mt([]);return}(o==null?void 0:o.length)>0&&((t=o[(o==null?void 0:o.length)-1])==null?void 0:t.code)==="Select All"?ds():st(o)},renderTags:(r,o)=>{var a;return(r==null?void 0:r.length)>0?C(q,{children:[c(ae,{label:(a=r[0])==null?void 0:a.code,...o({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(r==null?void 0:r.length)>1&&c(ae,{label:`+${(r==null?void 0:r.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},options:(u=f==null?void 0:f.TaxCategory)!=null&&u.length?[{code:"Select All",desc:""},...f==null?void 0:f.TaxCategory]:(f==null?void 0:f.TaxCategory)??[],getOptionLabel:r=>r!=null&&r.code?`${r==null?void 0:r.code}`??"":"",renderOption:(r,o,{selected:a})=>{var t;return c("li",{...r,children:c(Xe,{children:c(K,{control:c(le,{checked:Ms(o)||(o==null?void 0:o.code)==="Select All"&&(ue==null?void 0:ue.length)===((t=f==null?void 0:f.TaxCategory)==null?void 0:t.length)}),label:c(q,{children:c("strong",{children:`${o==null?void 0:o.code} - ${o==null?void 0:o.desc}`})})})})})},renderInput:r=>c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...r,variant:"outlined",placeholder:"Select Tax Category"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.POSTWITHOUTTAX&&C(I,{item:!0,md:2,children:[c(Z,{children:$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),c(J,{fullWidth:!0,size:"small",children:C(ve,{placeholder:"Select Blocking Status",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.postingWithoutTaxAllowed,name:"Posting Without Tax Allowed",onChange:r=>Cs(r),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Posting Without Tax Allowed"})}),ks.map(r=>c(oe,{sx:V,value:r,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:r,style:{fontSize:"12px !important"}})},r))]})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.BLOCKEDPOSTCOA&&C(I,{item:!0,md:2,children:[c(Z,{children:$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),c(J,{fullWidth:!0,size:"small",children:C(ve,{placeholder:"Select Blocking Status",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.blockedForPostingInCOA,name:"Posting Without Tax Allowed",onChange:r=>us(r),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Blocked For Posting In COA"})}),un==null?void 0:un.map(r=>c(oe,{sx:V,value:r,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:r,style:{fontSize:"12px !important"}})},r))]})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.BLOKEDPOSTCOMP&&C(I,{item:!0,md:2,children:[c(Z,{children:$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),c(J,{fullWidth:!0,size:"small",children:C(ve,{placeholder:"Select Blocked For Posting In Company",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.blockedForPostingInCompany,name:"Blocked For Posting In Company",onChange:r=>gs(r),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Blocked For Posting In Company"})}),gn==null?void 0:gn.map(r=>c(oe,{sx:V,value:r,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:r,style:{fontSize:"12px !important"}})},r))]})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===Pe.POSTONLY&&C(I,{item:!0,md:2,children:[c(Z,{children:$(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),c(J,{fullWidth:!0,size:"small",children:C(ve,{placeholder:"Select Post Automatically Only",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.postAutoOnly,name:"Open Item Management",onChange:r=>handlePostAutoOnly(r),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Post Automatically Only"})}),hn==null?void 0:hn.map(r=>c(oe,{sx:V,value:r,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:r,style:{fontSize:"12px !important"}})},r))]})})]})]},n)}),C(I,{item:!0,md:2,children:[c(Z,{sx:V,children:$("Add New Filters")}),c(J,{sx:{width:"100%"},children:c(ve,{sx:{font_Small:V,fontSize:"12px",width:"100%"},size:"small",multiple:!0,limitTags:2,value:Me,onChange:_s,renderValue:e=>e.join(", "),MenuProps:{MenuProps:Ve},endAdornment:Me.length>0&&c(rc,{position:"end",sx:{marginRight:"10px"},children:c(Ge,{size:"small",onClick:()=>Yn([]),"aria-label":"Clear selections",children:c(fl,{})})}),children:Yl.map(e=>C(oe,{value:e.title,children:[c(le,{checked:Me.indexOf(e.title)>-1}),e.title]},e.title))})}),c(I,{style:{display:"flex",justifyContent:"space-around"}})]}),c(I,{container:!0,sx:{flexDirection:"row",padding:"0rem 1rem 0.5rem"},gap:1,children:Me==null?void 0:Me.map((e,n)=>{var s,i,u,r,o;return e==="Created By"?c(q,{children:C(I,{item:!0,md:2,children:[c(Z,{children:$("Created By")}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:(ge==null?void 0:ge.length)>0?ge:(Lt==null?void 0:Lt.length)>0?Lt:[],noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(a,t,g)=>{var h;if(g==="clear"||(t==null?void 0:t.length)===0){ot([]),Nt([]);return}(t==null?void 0:t.length)>0&&((h=t[(t==null?void 0:t.length)-1])==null?void 0:h.code)==="Select All"?rs():ot(t)},renderTags:(a,t)=>{var g;return(a==null?void 0:a.length)>0?C(q,{children:[c(ae,{label:(g=a[0])==null?void 0:g.code,...t({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(a==null?void 0:a.length)>1&&c(ae,{label:`+${(a==null?void 0:a.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},limitTags:1,options:(s=f==null?void 0:f.CreatedBySearchGL)!=null&&s.length?[{code:"Select All"},...f==null?void 0:f.CreatedBySearchGL]:(f==null?void 0:f.CreatedBySearchGL)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,t,{selected:g})=>{var h;return c("li",{...a,children:c(Xe,{children:c(K,{control:c(le,{checked:Ds(t)||(t==null?void 0:t.code)==="Select All"&&(ge==null?void 0:ge.length)===((h=f==null?void 0:f.CreatedBySearchGL)==null?void 0:h.length)}),label:c(q,{children:C("strong",{children:[t==null?void 0:t.code," "]})})})})})},renderInput:a=>c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:"Enter Created By"})})})]})}):e==="Short Text"?c(q,{children:C(I,{item:!0,md:2,children:[c(Z,{children:$("Short Text")}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(a,t,g)=>{var h;if(g==="clear"||(t==null?void 0:t.length)===0){at([]),Gt([]);return}(t==null?void 0:t.length)>0&&((h=t[(t==null?void 0:t.length)-1])==null?void 0:h.code)==="Select All"?cs():at(t)},renderTags:(a,t)=>{var g;return(a==null?void 0:a.length)>0?C(q,{children:[c(ae,{label:(g=a[0])==null?void 0:g.code,...t({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(a==null?void 0:a.length)>1&&c(ae,{label:`+${(a==null?void 0:a.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},limitTags:1,value:Fl,options:(i=f==null?void 0:f.ShortTextSearchGL)!=null&&i.length?[{code:"Select All"},...f==null?void 0:f.ShortTextSearchGL]:(f==null?void 0:f.ShortTextSearchGL)??[],getOptionLabel:a=>a!=null&&a.code?(a==null?void 0:a.code)??"":"",renderOption:(a,t,{selected:g})=>{var h;return c("li",{...a,children:c(Xe,{children:c(K,{control:c(le,{checked:Ns(t)||(t==null?void 0:t.code)==="Select All"&&(ye==null?void 0:ye.length)===((h=f==null?void 0:f.ShortTextSearchGL)==null?void 0:h.length)}),label:c(q,{children:C("strong",{children:[t==null?void 0:t.code," "]})})})})})},renderInput:a=>c(Ce,{title:(Je==null?void 0:Je.length)<4?"Enter at least 4 characters":"",arrow:!0,disableHoverListener:(Je==null?void 0:Je.length)>=4,placement:"top",children:c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:"Search Short Text",onChange:t=>{xs(t)}})})})})]})}):e==="Open Item Mgmt by Ledger Group"?C(I,{item:!0,md:2,children:[C(Z,{children:[$(e)," "]}),c(J,{size:"small",fullWidth:!0,children:C(ve,{placeholder:"Select Open Item Mgmt by Ledger Group",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.openItemMgmtByLedgerGroup,name:"Open Item Mgmt by Ledger Group",onChange:a=>hs(a),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Open Item Mgmt by Ledger Group"})}),rn==null?void 0:rn.map(a=>c(oe,{sx:V,value:a,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:a,style:{fontSize:"12px !important"}})},a))]})})]}):e==="Open Item Management"?C(I,{item:!0,md:2,children:[C(Z,{children:[$(e)," "]}),c(J,{size:"small",fullWidth:!0,children:C(ve,{placeholder:"Select Open Item Management",sx:{height:"34.25px"},size:"small",value:l==null?void 0:l.openItemManagement,name:"Open Item Management",onChange:a=>fs(a),displayEmpty:!0,MenuProps:Ve,children:[c(oe,{sx:V,disabled:!0,value:"",children:c("div",{style:{color:"#C1C1C1",fontSize:"12px"},children:"Select Open Item Management"})}),dn==null?void 0:dn.map(a=>c(oe,{sx:V,value:a,style:{fontSize:"12px !important",height:"34.25px"},children:c(tt,{sx:V,primary:a,style:{fontSize:"12px !important"}})},a))]})})]}):e==="Created On"?C(I,{item:!0,md:4,children:[C(Z,{children:[$(e)," "]}),c(J,{size:"small",fullWidth:!0,children:c(dc,{dateAdapter:ic,children:c(uc,{handleDate:so,date:$l})})})]}):C(I,{item:!0,md:2,children:[C(Z,{children:[e," "]}),c(J,{fullWidth:!0,size:"small",children:c(We,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:((u=Re[e])==null?void 0:u.length)>0?Re[e]:((r=In[e])==null?void 0:r.length)>0?In[e]:[],noOptionsText:Be?c(se,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:c(He,{size:20})}):"No Data Available",onChange:(a,t,g)=>{var h;if(g==="clear"||(t==null?void 0:t.length)===0){nt(k=>({...k,[e]:[]})),Fe(k=>({...k,[e]:[]}));return}(t==null?void 0:t.length)>0&&((h=t[(t==null?void 0:t.length)-1])==null?void 0:h.code)==="Select All"?as(e):nt(k=>({...k,[e]:t}))},renderTags:(a,t)=>{var g;return(a==null?void 0:a.length)>0?C(q,{children:[c(ae,{label:(g=a[0])==null?void 0:g.code,...t({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),(a==null?void 0:a.length)>1&&c(ae,{label:`+${(a==null?void 0:a.length)-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null},limitTags:1,options:(o=te==null?void 0:te[e])!=null&&o.length?[{code:"Select All",desc:"."},...te==null?void 0:te[e]]:(te==null?void 0:te[e])??[],getOptionLabel:a=>a!=null&&a.code?`${a==null?void 0:a.code} - ${a==null?void 0:a.desc}`:"",renderOption:(a,t,{selected:g})=>{var h,k;return c("li",{...a,children:c(Xe,{children:c(K,{control:c(le,{checked:Ss(e,t)||(t==null?void 0:t.code)==="Select All"&&((h=Re[e])==null?void 0:h.length)===((k=te==null?void 0:te[e])==null?void 0:k.length)}),label:c(q,{children:c("strong",{children:`${t==null?void 0:t.code} - ${t==null?void 0:t.desc}`})})})})})},renderInput:a=>c(we,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...a,variant:"outlined",placeholder:`Select ${e}`})})})]})})})]}),C(Vc,{children:[c(El,{variant:"outlined",size:"small",startIcon:c(fl,{sx:{fontSize:"1rem"}}),onClick:()=>{uo()},children:"Clear"}),c(I,{sx:{...Cl},children:c(Yc,{moduleName:"GeneralLedger",PresetObj:ma,handleSearch:Yt,PresetMethod:ba})}),c(El,{variant:"contained",size:"small",startIcon:c(gc,{sx:{fontSize:"1rem"}}),sx:{...hc,...Cl},onClick:Yt,children:"Search"})]})]})]})})}),c(I,{item:!0,sx:{position:"relative"},children:c(hl,{children:c(Ft,{isLoading:Da,module:"MaterialMaster",width:"100%",title:"List of General Ledgers ("+jn+")",rows:ee,columns:kl??[],showSearch:!0,showRefresh:!0,showSelectedCount:!0,showExport:!0,onSearch:e=>Ho(e),onRefresh:ho,pageSize:Pt,page:jt,onPageSizeChange:Ta,rowCount:jn??(ee==null?void 0:ee.length)??0,onPageChange:Sa,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,tempheight:"calc(100vh - 320px)",onRowsSelectionHandler:go,callback_onRowSingleClick:e=>{var s;const n=(s=e==null?void 0:e.row)==null?void 0:s.glAccount;Ue(`/masterDataCockpit/generalLedger/displayGeneralLedgerMasterdata/${n}`,{state:e.row})},showFirstPageoptions:!0,showSelectAllOptions:!0,onSelectAllOptions:pa,showCustomNavigation:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})})}),c(fc,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:C(Cc,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Ma,onChange:e=>{La(e)},children:[c(Ac,{variant:"contained",ref:Ba,"aria-label":"split button",children:c(m,{size:"small",onClick:()=>{A(yc()),Ue("/requestBench/GeneralLedgerRequestTab",{state:{steaperData:["Request Header","General Ledger List","Attachments & Comments"],moduleName:"GeneralLedger"}})},sx:{cursor:"pointer"},className:"createRequestButtonGL",children:"Create Request"})}),Pa&&c(zc,{artifactId:"",artifactName:"",setOpen:Te,handleUpload:Es})]})})]})})]})};export{Br as default};
