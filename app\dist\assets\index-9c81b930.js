import{ef as c,pL as L,cb as P,pM as _,r as g,pN as b,ee as T,b9 as j,mE as v,pO as x,pP as O,pQ as V,pR as G,pS as q,pT as z,pU as D,hD as F}from"./index-226a1e75.js";function $(s){var r,e,t="";if(typeof s=="string"||typeof s=="number")t+=s;else if(typeof s=="object")if(Array.isArray(s)){var n=s.length;for(r=0;r<n;r++)s[r]&&(e=$(s[r]))&&(t&&(t+=" "),t+=e)}else for(e in s)s[e]&&(t&&(t+=" "),t+=e);return t}function R(){for(var s,r,e=0,t="",n=arguments.length;e<n;e++)(s=arguments[e])&&(r=$(s))&&(t&&(t+=" "),t+=r);return t}function H(s){return s}function E(s){const{theme:r,name:e,props:t}=s;if(!r||!r.components||!r.components[e]||!r.components[e].defaultProps)return t;const n=c({},t),a=r.components[e].defaultProps;let o;for(o in a)n[o]===void 0&&(n[o]=a[o]);return n}class I{constructor(r={}){this.options=r}collect(r){const e=new Map;this.sheetsRegistry=new L;const t=b();return P.jsx(_,c({sheetsManager:e,serverGenerateClassName:t,sheetsRegistry:this.sheetsRegistry},this.options,{children:r}))}toString(){return this.sheetsRegistry?this.sheetsRegistry.toString():""}getStyleElement(r){return g.createElement("style",c({id:"jss-server-side",key:"jss-server-side",dangerouslySetInnerHTML:{__html:this.toString()}},r))}}const K=["name"],Q=["children","className","clone","component"];function U(s,r){const e={};return Object.keys(s).forEach(t=>{r.indexOf(t)===-1&&(e[t]=s[t])}),e}function B(s){return(e,t={})=>{const{name:n}=t,a=T(t,K);let o=n;const i=j(typeof e=="function"?p=>({root:h=>e(c({theme:p},h))}):{root:e},c({Component:s,name:n||s.displayName,classNamePrefix:o},a));let m;e.filterProps&&(m=e.filterProps,delete e.filterProps),e.propTypes&&(e.propTypes,delete e.propTypes);const N=g.forwardRef(function(h,S){const{children:f,className:d,clone:u,component:C}=h,W=T(h,Q),k=i(h),w=R(k.root,d);let y=W;if(m&&(y=U(y,m)),u)return g.cloneElement(f,c({className:R(f.props.className,w)},y));if(typeof f=="function")return f(c({className:w},y));const A=C||s;return P.jsx(A,c({ref:S,className:w},y,{children:f}))});return v(N,s),N}}const J=(s,r)=>{const{classes:e={}}=s,t=x();let n="";return t&&t.components&&t.components[r]&&t.components[r].variants&&t.components[r].variants.forEach(o=>{let l=!0;Object.keys(o.props).forEach(i=>{s[i]!==o.props[i]&&(l=!1)}),l&&(n=`${n}${e[O(o.props)]} `)}),n},X=J,Y=["defaultTheme","withTheme","name"],Z=["classes"],ee=(s,r={})=>e=>{const{defaultTheme:t,withTheme:n=!1,name:a}=r,o=T(r,Y);let l=a;const i=j(s,c({defaultTheme:t,Component:e,name:a||e.displayName,classNamePrefix:l},o)),m=g.forwardRef(function(p,h){const S=T(p,Z),f=i(c({},e.defaultProps,p));let d,u=S;return(typeof a=="string"||n)&&(d=x()||t,a&&(u=E({theme:d,name:a,props:S})),n&&!u.theme&&(u.theme=d)),P.jsx(e,c({ref:h,classes:f},u))});return v(m,e),m},te=ee;function M(s={}){const{defaultTheme:r}=s;return t=>{const n=g.forwardRef(function(o,l){const i=x()||r;return P.jsx(t,c({theme:i,ref:l},o))});return v(n,t),n}}const se=M(),re=se;/**
 * @mui/styles v5.18.0
 *
 * @license MIT
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */const ne=Object.freeze(Object.defineProperty({__proto__:null,ServerStyleSheets:I,StylesContext:V,StylesProvider:_,ThemeProvider:G,createGenerateClassName:b,createStyles:H,getThemeProps:E,jssPreset:q,makeStyles:j,mergeClasses:z,propsToClassKey:O,sheetsManager:D,styled:B,useTheme:x,useThemeVariants:X,withStyles:te,withTheme:re,withThemeCreator:M},Symbol.toStringTag,{value:"Module"})),ae=F(ne);export{ae as r};
