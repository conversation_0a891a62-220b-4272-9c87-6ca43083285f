import{c9 as q,ca as X,cb as G,r as Z,x7 as Pe,n as ve,s as Be,g as Ze,j as pe,F as ze,r1 as Ae,aC as He}from"./index-226a1e75.js";import{W as Ye,u as Qe}from"./propData-3de5c575.js";import{d as Je,e as $e,g as qe,h as we,b as Xe,s as he,m as Ge,c as Ke}from"./redux-49302a50.js";import{d as Ce,e as ke,r as et,t as tt,g as Te,w as at,A as it,c as nt}from"./configData-978802d4.js";import{r as lt,a as rt}from"./react-beautiful-dnd.esm-0de399b3.js";import{r as ot}from"./index-9c81b930.js";import"./index-2a7424d8.js";import"./asyncToGenerator-88583e02.js";import"./_baseDelay-5448b93c.js";import"./Chip-a06f5bd7.js";import"./Paper-164eb9eb.js";import"./Dropdown-6cea9e1f.js";import"./TextField-17bdd2f4.js";import"./Tooltip-d0e36572.js";import"./TableContainer-debf0374.js";import"./CheckBox-e52b9f98.js";import"./Autocomplete-b446b668.js";import"./AccordionDetails-2418f9ae.js";import"./Box-06e0824d.js";import"./InputAdornment-19c51729.js";import"./Backdrop-ef004339.js";import"./DialogActions-98850990.js";import"./DialogContentText-e4d806a4.js";import"./CircularProgress-1acedaf0.js";import"./FormControlLabel-57cd7a82.js";import"./DashboardSetting-970ae243.js";import"./Switch-a9aa0e31.js";import"./Grid-97e89306.js";import"./Zoom-f387e7b2.js";var ct={},Le={},K={},ut=X;Object.defineProperty(K,"__esModule",{value:!0});K.default=void 0;var dt=ut(q()),J=G;K.default=(0,dt.default)([(0,J.jsx)("path",{d:"M12 4c-4.42 0-8 3.58-8 8s3.58 8 8 8 8-3.58 8-8-3.58-8-8-8m-5 9.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5m5 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5",opacity:".3"},"0"),(0,J.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8"},"1"),(0,J.jsx)("circle",{cx:"7",cy:"12",r:"1.5"},"2"),(0,J.jsx)("circle",{cx:"12",cy:"12",r:"1.5"},"3"),(0,J.jsx)("circle",{cx:"17",cy:"12",r:"1.5"},"4")],"PendingTwoTone");var ee={},st=X;Object.defineProperty(ee,"__esModule",{value:!0});ee.default=void 0;var mt=st(q()),Ee=G;ee.default=(0,mt.default)([(0,Ee.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m-2 13-4-4 1.41-1.41L10 14.17l6.59-6.59L18 9z",opacity:".3"},"0"),(0,Ee.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z"},"1")],"CheckCircleTwoTone");var te={},ft=X;Object.defineProperty(te,"__esModule",{value:!0});te.default=void 0;var vt=ft(q()),Ne=G;te.default=(0,vt.default)([(0,Ne.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8m5 11.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z",opacity:".3"},"0"),(0,Ne.jsx)("path",{d:"M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8m3.59-13L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41z"},"1")],"CancelTwoTone");var ae={},pt=X;Object.defineProperty(ae,"__esModule",{value:!0});ae.default=void 0;var wt=pt(q()),xe=G;ae.default=(0,wt.default)([(0,xe.jsx)("path",{d:"M12 4c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8M7 7h7v2H7zm0 3h7v2H7zm3 5H7v-2h3zm4.05 3.36-2.83-2.83 1.41-1.41 1.41 1.41L17.59 12 19 13.41z",opacity:".3"},"0"),(0,xe.jsx)("path",{d:"M12 4c4.41 0 8 3.59 8 8s-3.59 8-8 8-8-3.59-8-8 3.59-8 8-8m0-2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2m2 8H7v2h7zm0-3H7v2h7zm-7 8h3v-2H7zm12-1.59L17.59 12l-3.54 3.54-1.41-1.41-1.41 1.41 2.83 2.83z"},"1")],"PlaylistAddCheckCircleTwoTone");var De={};(function(w){Object.defineProperty(w,"__esModule",{value:!0}),w.default=void 0;var d=ot;function t(r){"@babel/helpers - typeof";return t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},t(r)}function N(r,o,b){return(o=M(o))in r?Object.defineProperty(r,o,{value:b,enumerable:!0,configurable:!0,writable:!0}):r[o]=b,r}function M(r){var o=h(r,"string");return t(o)=="symbol"?o:o+""}function h(r,o){if(t(r)!="object"||!r)return r;var b=r[Symbol.toPrimitive];if(b!==void 0){var R=b.call(r,o||"default");if(t(R)!="object")return R;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(r)}var V=(0,d.makeStyles)(function(r){return{connectorLineCol:{minHeight:70,margin:"5px auto !important",borderRightWidth:"1.2 !important",backgroundColor:"#3026B9",opacity:.6},connectorHeaderLineCol:{minHeight:30,margin:"5px auto !important",borderRightWidth:"1.2 !important",backgroundColor:"#3026B9",opacity:.6},subjectLine:{color:r.palette.text.primary,fontSize:"14px"},endLineText:{color:"#757575",fontSize:"14px"},subTextLine:{color:r.palette.text.secondary,fontSize:"10px"},SelectedSubTextLine:{color:r.palette.text.primary,fontSize:"10px"},avatarSize:{height:"27px !important",width:"27px !important",fontSize:12},avatarContainerSize:{height:"25px !important",width:"25px !important"},statusButtonStyle:{width:"fit-content",padding:"7px 10px",background:"rgba(255, 255, 255, 0.36)",border:"1px solid #EEEEEE",boxShadow:"0px 1px 1px rgb(0 0 0 / 7%)",borderRadius:"4px"},selectedStatusButtonStyle:{width:"fit-content",padding:"7px 10px",background:"rgba(255, 255, 255, 0.9)",border:"1px solid #EEEEEE",boxShadow:"0px 1px 1px rgb(0 0 0 / 7%)",borderRadius:"4px"},nextProcessText:{border:"2px dashed #B71C1C",borderRadius:"4px",color:"#757575",fontSize:"14px"},hoverDiv:{cursor:"pointer",width:"100%",padding:"3px 3px 5px 8px","&:hover":{backgroundColor:"rgba(29, 29, 17, 0.08)",borderRadius:"3px"}},selectedDiv:{width:"100%",background:"linear-gradient(180.76deg, #C1DCFF -113.11%, rgba(255, 255, 255, 0) 198.03%)!important",borderRadius:"3px"},flowableContainer:{width:"100%"},flowableContainerWithLog:{width:"50%"},flowableImageWidth:{width:"50%"},flowableImageWidthWithLog:{width:"80%"},flowableImageContainer:N(N(N(N({margin:"5px",border:"1px solid #EAE9FF",borderRadius:"4px",background:"#fff",boxShadow:"0px 0px 16px rgb(207 207 207 / 25%), 0px 0px 8px rgb(255 252 252 / 25%), 0px 0px 4px rgb(249 249 249 / 25%), 0px 0px 2px #e0e0e0",height:"14rem",overflow:"hidden",cursor:"pointer",position:"relative",padding:0},"margin","0.5em"),"backgroundRepeat","no-repeat"),"backgroundPosition","center"),"backgroundSize","auto"),flowableActions:{padding:"5px",height:"45px"},flowableDetails:{position:"relative",backgroundSize:"cover",backgroundColor:"#FAFCFF",borderRadius:"4px",height:"160px",marginTop:"112px",padding:"5px",color:"#3026b9",fontSize:"13px",transition:"margin-top .5s ease","&:hover":{marginTop:"90px"}},flowableTitleText:{fontSize:"14px",margin:0,padding:"2px",color:"#373e48"},flowableBasicDetails:{minHeight:"30px",width:"100%",marginTop:"0.3rem",display:"flex",alignItems:"flex-end"},flowableSubText:{fontSize:"13px",color:"#3026b9"}}});w.default=V})(De);var We={};(function(w){Object.defineProperty(w,"__esModule",{value:!0}),w.default=void 0;var d=m(Z),t=he,N=m(lt),M=m(Je),h=m($e),V=m(qe),r=m(we),o=m(Xe),b=Ce,R=ke;function m(T){return T&&T.__esModule?T:{default:T}}var n=function(S){var u=S.task,L=(0,t.useSelector)(function(v){var g;return(g=v.app)===null||g===void 0||(g=g.appConfig)===null||g===void 0||(g=g.SERVICE_BASE_URL_MAP)===null||g===void 0?void 0:g.NativeWorkflowServices}),D=(u==null?void 0:u.technicalStatus)==="COMPLETED"?"".concat(L,"/getInteractiveHistoryDiagram/pdd/").concat(u.processName,"/pid/").concat(u.processId):"".concat(L,"/getInteractiveDiagram/pdd/").concat(u.processName,"/pid/").concat(u.processId);return d.default.createElement(d.default.Fragment,null,d.default.createElement("iframe",{title:"Flowable Diagram",src:D,style:{height:"calc(72vh)",width:"calc(100%)"},className:"cwitmIframe"}))},y=function(S){var u=S.openDialog,L=S.updateOpenDialog,D=S.task;return d.default.createElement(N.default,{open:u,fullWidth:!0,maxWidth:"lg"},d.default.createElement(M.default,{sx:{m:0,p:2}},d.default.createElement(r.default,{direction:"row",justifyContent:"space-between",alignItems:"center"},d.default.createElement(V.default,{required:"true",variant:"h6",color:"text.primary",className:"cwitmWeight500"},"Work Flow"),d.default.createElement(r.default,{direction:"row",justifyContent:"end"},d.default.createElement(o.default,{onClick:function(){L(!1)},"aria-label":"close"},d.default.createElement(b.Icon,{icon:d.default.createElement(R.Close,null),sx:{cursor:"pointer"},edge:"end"}))))),d.default.createElement(h.default,null,d.default.createElement(r.default,{sx:{width:"100%"},justifyContent:"center"},d.default.createElement(n,{task:D}))))};w.default=y})(We);(function(w){function d(a){"@babel/helpers - typeof";return d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},d(a)}Object.defineProperty(w,"__esModule",{value:!0}),w.default=void 0;var t=g(Z),N=he,M=Te,h=et(),V=v(tt),r=v(rt),o=v(we),b=v(Ge),R=v(Ke),m=Ce,n=ke,y=v(K),T=v(ee);v(Pe);var S=v(te),u=v(ae),L=at,D=v(De);v(We);function v(a){return a&&a.__esModule?a:{default:a}}function g(a,s){if(typeof WeakMap=="function")var c=new WeakMap,F=new WeakMap;return(g=function(f,O){if(!O&&f&&f.__esModule)return f;var x,C,i={__proto__:null,default:f};if(f===null||d(f)!="object"&&typeof f!="function")return i;if(x=O?F:c){if(x.has(f))return x.get(f);x.set(f,i)}for(var j in f)j!=="default"&&{}.hasOwnProperty.call(f,j)&&((C=(x=Object.defineProperty)&&Object.getOwnPropertyDescriptor(f,j))&&(C.get||C.set)?x(i,j,C):i[j]=f[j]);return i})(a,s)}function _(a){return A(a)||ie(a)||ge(a)||z()}function z(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ie(a){if(typeof Symbol<"u"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function A(a){if(Array.isArray(a))return ne(a)}function H(a,s){return Me(a)||Re(a,s)||ge(a,s)||Se()}function Se(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ge(a,s){if(a){if(typeof a=="string")return ne(a,s);var c={}.toString.call(a).slice(8,-1);return c==="Object"&&a.constructor&&(c=a.constructor.name),c==="Map"||c==="Set"?Array.from(a):c==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(c)?ne(a,s):void 0}}function ne(a,s){(s==null||s>a.length)&&(s=a.length);for(var c=0,F=Array(s);c<s;c++)F[c]=a[c];return F}function Re(a,s){var c=a==null?null:typeof Symbol<"u"&&a[Symbol.iterator]||a["@@iterator"];if(c!=null){var F,B,f,O,x=[],C=!0,i=!1;try{if(f=(c=c.call(a)).next,s===0){if(Object(c)!==c)return;C=!1}else for(;!(C=(F=f.call(c)).done)&&(x.push(F.value),x.length!==s);C=!0);}catch(j){i=!0,B=j}finally{try{if(!C&&c.return!=null&&(O=c.return(),Object(O)!==O))return}finally{if(i)throw B}}return x}}function Me(a){if(Array.isArray(a))return a}var Fe=function(s){var c,F,B,f=s.task,O=s.refresh,x=(0,N.useSelector)(function(k){return k.workflow}),C=(0,N.useSelector)(function(k){return k.app}),i=(0,D.default)(),j=(0,N.useDispatch)(),Ue=(0,t.useState)(),be=H(Ue,2),P=be[0],Ve=be[1],Y=C==null?void 0:C.userList;(0,N.useSelector)(function(k){var e;return(e=k.userReducer)===null||e===void 0||(e=e.appConfig)===null||e===void 0?void 0:e.SERVICE_BASE_URL_MAP}),C==null||(c=C.appConfig)===null||c===void 0||(c=c.applicationProperties)===null||c===void 0||(c=c.default)===null||c===void 0||c.dateTimeFormat;var p=x==null?void 0:x.workflowData,_e=x==null?void 0:x.loading;(0,t.useEffect)(function(){Ve(null),(f==null?void 0:f.systemId)==="Flowable"&&O&&j((0,M.getWorkflowData)(f.taskId,f.processId))},[]);var le=function(e,I,E){if(e)switch(I.toUpperCase()){case"GROUP":return(0,h.getGroupNameByID)(e,groupsList,E);case"USER":default:return E?(0,h.getUserNameByID)(e,Y,E):(0,h.getUserNameByID)(e,Y)}},re=function(e,I){for(var E,W,U,l=e||[],oe=(l==null||(E=l[0])===null||E===void 0?void 0:E.displayName)||le(l==null||(W=l[0])===null||W===void 0?void 0:W.ownerId,l==null||(U=l[0])===null||U===void 0?void 0:U.ownerType,I),Q=1;Q<(l==null?void 0:l.length)-1;Q++){var ce,ue,de;oe+=", ".concat((l==null||(ce=l[Q])===null||ce===void 0?void 0:ce.displayName)||le(l==null||(ue=l[Q])===null||ue===void 0?void 0:ue.ownerId,l==null||(de=l[Q])===null||de===void 0?void 0:de.ownerType,I))}if((l==null?void 0:l.length)>1){var se,me,fe;oe+=" and ".concat((l==null||(se=l[l.length-1])===null||se===void 0?void 0:se.displayName)||le(l==null||(me=l[l.length-1])===null||me===void 0?void 0:me.ownerId,l==null||(fe=l[l.length-1])===null||fe===void 0?void 0:fe.ownerType,I))}return oe},$=function(e,I){switch(e==null?void 0:e.technicalStatus){case"READY":return re(e.owners,I);case"RESERVED":return re(e.owners,I);case"DRAFTED":return re(e.owners,I);case"COMPLETED":return I?(0,h.getUserNameByID)(e==null?void 0:e.completedBy,Y,I):(0,h.getUserNameByID)(e==null?void 0:e.completedBy,Y);default:return e==null?void 0:e.updatedBy}},ye=function(e){switch(e==null?void 0:e.technicalStatus){case"COMPLETED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===P?i.selectedStatusButtonStyle:i.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},(e==null?void 0:e.color)==="#FF0101"?t.default.createElement(S.default,{sx:{color:e==null?void 0:e.color,width:21,height:21}}):t.default.createElement(L.ApprovedCheckCircleRounded,null),t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400 cwitmML8")},(0,h.getCustomChipLabel)(e==null?void 0:e.technicalStatus,e==null?void 0:e.itmStatus)));case"READY":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===P?i.selectedStatusButtonStyle:i.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(T.default,{sx:{width:20,height:20,color:"#007AD4"}}),t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400 cwitmML8")},"Open"));case"DRAFTED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===P?i.selectedStatusButtonStyle:i.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(u.default,{sx:{width:20,height:20,color:"#FF6F00"}}),t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400 cwitmML8")},"Drafted"));case"RESERVED":return t.default.createElement("div",{className:"".concat((e==null?void 0:e.taskId)===P?i.selectedStatusButtonStyle:i.statusButtonStyle," cwitmHbox cwitmAlignItemsCenter cwitmMT8")},t.default.createElement(y.default,{sx:{width:20,height:20,color:"#FF6F00"}}),t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400 cwitmML8")},"In Progress"));default:return null}},Ie=function(e){var I,E,W=p==null||(I=p.data)===null||I===void 0?void 0:I.filter(function(U){return(U==null?void 0:U.technicalStatus)!=="COMPLETED"});if(e+1===(p==null||(E=p.data)===null||E===void 0?void 0:E.length))return t.default.createElement(t.default.Fragment,null,(W==null?void 0:W.length)>0&&t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(i.avatarContainerSize," cwitmVbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},t.default.createElement(L.ProcessFlowEllipseIcon,null)),t.default.createElement(r.default,{className:i.connectorHeaderLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmML16 cwitmMB8"},t.default.createElement("span",{className:"".concat(i.nextProcessText," cwitmHbox cwitmAlignItemsCenter cwitmPagePadding cwitmWeight400")},"Further steps yet to be determined"))),t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement(L.ProcessFlowEndIcon,null),t.default.createElement("div",{className:"cwitmML16"},t.default.createElement("span",{className:"".concat(i.endLineText," cwitmWeight400")},"End"))))},je=function(e,I){var E,W,U;return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement(L.CheckCircleRounded,null),t.default.createElement(r.default,{className:"".concat(i.connectorHeaderLineCol),orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400")},(0,h.getUserNameByID)(e==null?void 0:e.createdBy,Y)," "," initiated the workflow"),t.default.createElement(R.default,{title:"Created at ".concat((0,h.dateTimeFormatter)(p==null||(E=p.data)===null||E===void 0||(E=E[0])===null||E===void 0?void 0:E.createdOnForProcess,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat(i.subTextLine," cwitmWeight400")},(0,h.dateTimeFormatter)(p==null||(W=p.data)===null||W===void 0||(W=W[0])===null||W===void 0?void 0:W.createdOnForProcess,null,!0,null)))))),t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(i.avatarContainerSize," cwitmHbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},e!=null&&e.owners&&(Object==null||(U=Object.values(e==null?void 0:e.owners))===null||U===void 0?void 0:U.length)>1?t.default.createElement(m.Icon,{elementid:"cwitm-workspace-taskcard-nature",style:{width:"25px",height:"25px",padding:"0.25rem",borderRadius:"50%",backgroundColor:"#DBD9FF",fontSize:"16px"},size:"small",icon:t.default.createElement(n.ProfileTwoUser,null)}):t.default.createElement(m.Avatar,{size:"small",alt:V.default.toHeaderCase($(e,!0)),className:i.avatarSize,style:{color:"#3026b9",backgroundColor:"#e8e7fa"}})),t.default.createElement(r.default,{className:i.connectorLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16 ".concat((e==null?void 0:e.taskId)===P?i.selectedDiv:"")},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400")},$(e)),(e==null?void 0:e.createdOn)!==""&&t.default.createElement(R.default,{title:"Updated at ".concat((0,h.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat((e==null?void 0:e.taskId)===P?i.SelectedSubTextLine:i.subTextLine," cwitmWeight400")},(0,h.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null))),ye(e)))),Ie(I))},Oe=function(e,I){return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmHbox"},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("div",{className:"".concat(i.avatarContainerSize," cwitmHbox cwitmJustifyContentCenter cwitmAlignItemsCenter")},t.default.createElement(m.Avatar,{size:"small",alt:V.default.toHeaderCase($(e,!0)),className:i.avatarSize,style:{color:"#3026b9",backgroundColor:"#e8e7fa"}})),t.default.createElement(r.default,{className:i.connectorLineCol,orientation:"vertical",flexItem:!0})),t.default.createElement("div",{className:"cwitmVbox cwitmML16 cwitmMB16 ".concat((e==null?void 0:e.taskId)===P?i.selectedDiv:"")},t.default.createElement("div",{className:"cwitmVbox"},t.default.createElement("span",{className:"".concat(i.subjectLine," cwitmWeight400")},$(e)),(e==null?void 0:e.createdOn)!==""&&t.default.createElement(R.default,{title:"Updated at ".concat((0,h.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null)),placement:"bottom-start"},t.default.createElement("span",{className:"".concat((e==null?void 0:e.taskId)===P?i.SelectedSubTextLine:i.subTextLine," cwitmWeight400")},(0,h.dateTimeFormatter)(e==null?void 0:e.updatedOn,null,!0,null))),ye(e)))),Ie(I))};return t.default.createElement(t.default.Fragment,null,t.default.createElement("div",{className:"cwitmScroll cwitmScrollX cwitmSetHeight100 cwitmSetWidth100",style:{padding:"15px 24px 30px 20px"}},_e?t.default.createElement(o.default,{className:"cwitmSetWidth100"},t.default.createElement(o.default,{direction:"row",sx:{height:"90px",paddingTop:"10px"},className:"cwitmSetWidth100",spacing:2,alignItems:"flex-start"},t.default.createElement(b.default,{variant:"circular",width:24,height:24,sx:{marginTop:"3px"}}),t.default.createElement(o.default,{className:"cwitmSetWidth100"},t.default.createElement(b.default,{height:18,width:"20%"}))),_(Array(4).keys()).map(function(k,e){return t.default.createElement(o.default,{key:e,direction:"row",sx:{height:"90px"},spacing:2,alignItems:"flex-start",className:"cwitmSetWidth100"},t.default.createElement(b.default,{variant:"circular",width:24,height:24,sx:{marginTop:"5px"}}),t.default.createElement(o.default,{sx:{paddingTop:"2px"},className:"cwitmSetWidth100"},t.default.createElement(b.default,{height:18,width:"15%"}),t.default.createElement(b.default,{height:10,width:"25%"}),t.default.createElement(b.default,{height:18,width:"80%"})))})):t.default.createElement(t.default.Fragment,null,p&&(p==null?void 0:p.data)&&(p==null||(F=p.data)===null||F===void 0?void 0:F.length)>0&&(p==null||(B=p.data)===null||B===void 0?void 0:B.map(function(k,e){return e===0?je(k,e):Oe(k,e)})))))};w.default=Fe})(Le);(function(w){function d(m){"@babel/helpers - typeof";return d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},d(m)}Object.defineProperty(w,"__esModule",{value:!0}),w.default=void 0;var t=b(Z),N=he,M=Te,h=o(we),V=o(Le),r=o(it);function o(m){return m&&m.__esModule?m:{default:m}}function b(m,n){if(typeof WeakMap=="function")var y=new WeakMap,T=new WeakMap;return(b=function(u,L){if(!L&&u&&u.__esModule)return u;var D,v,g={__proto__:null,default:u};if(u===null||d(u)!="object"&&typeof u!="function")return g;if(D=L?T:y){if(D.has(u))return D.get(u);D.set(u,g)}for(var _ in u)_!=="default"&&{}.hasOwnProperty.call(u,_)&&((v=(D=Object.defineProperty)&&Object.getOwnPropertyDescriptor(u,_))&&(v.get||v.set)?D(g,_,v):g[_]=u[_]);return g})(m,n)}var R=function(n){var y=n.task,T=n.token,S=n.destinationData,u=n.userData,L=n.useWorkAccess,D=n.useConfigServerDestination,v=n.userList,g=n.configData,_=n.userPreferences,z=(0,N.useDispatch)(),ie=(0,N.useSelector)(function(H){return H.app}),A=(0,N.useSelector)(function(H){return H.TaskDetail.selectedTask});return(0,t.useEffect)(function(){z((0,M.setInitialAppData)({token:T,destinationData:S,userPreferences:_,userData:u,useWorkAccess:L,useConfigServerDestination:D,configData:g})),z((0,M.setUserList)(v)),z((0,M.setSelectedTask)(y))},[]),t.default.createElement(t.default.Fragment,null,ie.initialDataLoadFlag&&(A==null?void 0:A.taskId)&&t.default.createElement(h.default,{direction:"row",justifyContent:"space-between",sx:{padding:0,height:"100%"}},t.default.createElement(V.default,{task:y,refresh:!0})))};w.default=(0,r.default)(R)})(ct);function Yt(){let w=ve(n=>{var y;return(y=n.userManagement)==null?void 0:y.userData});ve(n=>{var y;return(y=n.userManagement)==null?void 0:y.groups});const d=ve(n=>n.applicationConfig);let t=Be();const N=Ze();Z.useState(null),Z.useState(null);const[M,h]=Z.useState(null),V={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},r={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}},o=n=>{var T,S;t(He(n));var y={PRC:"/purchaseRequest/workbench/singleworkbench/",POC:"/purchaseOrder/confirmationTracker/taskDetail/",INC:"/invoices/workbench/singleInvoice/",RTC:"/ReturnManagement/SingleReturnWorkbench/",SEC:"/serviceSheet/workbench/singleServiceWorkbench/",PFC:"/planningManagement/singlePlanningTask/"};y[(T=n==null?void 0:n.taskDesc)==null?void 0:T.slice(0,4)]&&N(`${y[(S=n==null?void 0:n.taskDesc)==null?void 0:S.slice(0,4)]}${n==null?void 0:n.taskDesc}`)},b=()=>{console.log("fetchFilterView")},R=()=>{console.log("clearFilterView")},m=(n,y)=>{console.log("Success flag.",n),console.log("Task Payload.",y)};return pe("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:pe(ze,{children:pe(Ye,{token:"eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vY2EtZ2JkLmF1dGhlbnRpY2F0aW9uLnVzMTAuaGFuYS5vbmRlbWFuZC5jb20vdG9rZW5fa2V5cyIsImtpZCI6ImRlZmF1bHQtand0LWtleS1kZjk5ODA5MzZhIiwidHlwIjoiSldUIiwiamlkIjogInVyQWRCVkJPN3VkV0FPMmFVaHp1QTRZU0V4aEE5TU96L05rTHF0UzkvR0E9In0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TomSTo1o_CU1U8ExR7p5kJOZmgQirKjQSsoyUKgSBHcyMZ7ZoUB3DiZSDANEeMYnYOv__ZcyBwnY4JmIBSTvQDiWrasSnlcd2rwAx6oVREZCH3lanUM3qQd0CuAKnJMWghGmj6_XSaBnJc2Kulk8LAusknZ87EpK1EbBby1Ajrua6LafLahVkaIj4KnQkHlIa4cSXbAGVXnqAecvpX7rUI1wmwcnE1f4az3oCFoNWC7LK_pF74pZoie6yTBP4s7aQyoOwk6q_ayJdVgMjvodvE-6h01nbEw-3oRlu3YgwZOTSib5apz8upBrRJ6DdKIinfas5Q_VAXXywRjWq5wswQ",configData:nt,destinationData:V,userData:{...w,user_id:w==null?void 0:w.userName},userPreferences:r,userPermissions:Qe,userList:{},groupList:{},userListBySystem:M,useWorkAccess:d.environment==="localhost",useConfigServerDestination:d.environment==="localhost",inboxTypeKey:"ADMIN_COMPLETED_TASKS",workspaceLabel:"Admin Completed Tasks",workspaceFiltersByAPIDriven:!0,subInboxTypeKey:"COMPLETED",cachingBaseUrl:Ae,onTaskClick:o,onActionComplete:m,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:b,savedFilterViewData:[],clearFilterView:R,filterViewList:[],selectedTabId:null,userProcess:[]})})})}export{Yt as default};
