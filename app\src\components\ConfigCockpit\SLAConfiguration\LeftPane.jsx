// LeftPane.js
import React from 'react';
import {
  Card,
  Button,
  Select,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  FilterOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { useTheme } from '@mui/material';

const { Title, Text } = Typography;
const { Option } = Select;

const LeftPane = ({
  isCollapsed,
  onToggleCollapse,
  regionOptions,
  timezoneOptions,
  selectedRegion,
  selectedTimezone,
  onRegionChange,
  onTimezoneChange,
  onClearFilters,
  onOpenAddModal,
  fetchTimezonesByRegion
}) => {
  const theme = useTheme()
  if (isCollapsed) {
    return (
      <Card
        size="small"
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ 
          padding: '8px', 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center',
          justifyContent: 'flex-start'
        }}
      >
        <Tooltip title="Expand" placement="right">
          <Button
            type="text"
            icon={<DoubleRightOutlined />}
            onClick={onToggleCollapse}
            style={{ marginBottom: '16px' }}
          />
        </Tooltip>
        
        <Divider style={{ margin: '10px 0' }} />
        
        <Tooltip title="Add Business Hour" placement="right">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={onOpenAddModal}
            style={{
              backgroundColor: theme?.palette?.primary?.main,
              marginTop: '16px',
              marginBottom: '16px',
              align: 'center'
            }}
          />
        </Tooltip>

        <div style={{
            fontSize: '15px',
            fontWeight: 'bold',
            color: theme?.palette?.primary?.main,
            writingMode: 'sideways-lr',
            textOrientation: 'mixed',
            maxHeight: '500px',
            overflow: 'hidden'
        }}>
        Add New Record
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={5} style={{ margin: 0 }}>
              <FilterOutlined style={{ marginRight: 8 }} />
              Filters & Actions
            </Title>
          </Col>
          <Col>
            <Tooltip title="Collapse">
              <Button
                type="text"
                icon={<DoubleLeftOutlined />}
                onClick={onToggleCollapse}
                size="small"
              />
            </Tooltip>
          </Col>
        </Row>
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px' }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Filters Section */}
        <div>
          <Title level={5} style={{ marginBottom: 12 }}>
            Filter Data
          </Title>
          
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <div>
              <Text strong style={{ display: 'block', marginBottom: 4 }}>
                Region
              </Text>
              <Select
                placeholder="Select region"
                style={{ width: '100%' }}
                value={selectedRegion}
                onChange={(value) => {
                    onRegionChange(value);
                    if (value) {
                    fetchTimezonesByRegion(value);
                    }
                }}
                allowClear
                showSearch
                optionFilterProp="label"
                filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                }
                >
                {regionOptions.map(option => (
                    <Option
                    key={option.value}
                    value={option.value}
                    label={`${option.value}-${option.label}`}
                    >
                    {option.value} - {option.label}
                    </Option>
                ))}
                </Select>
            </div>

            <div>
              <Text strong style={{ display: 'block', marginBottom: 4 }}>
                Timezone
              </Text>
              <Select
                placeholder="Select timezone"
                style={{ width: '100%' }}
                value={selectedTimezone}
                onChange={onTimezoneChange}
                disabled={!selectedRegion}
                allowClear
                showSearch
                optionFilterProp="label"
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
              >
                {timezoneOptions.map(option => (
                  <Option key={option.value} value={option.value} label={`${option.value}-${option.label}`}>
                    {option.value}
                  </Option>
                ))}
              </Select>
            </div>

            {(selectedRegion || selectedTimezone) && (
              <Button
                type="default"
                icon={<ClearOutlined />}
                onClick={onClearFilters}
                style={{ width: '100%' }}
              >
                Clear Filters
              </Button>
            )}
          </Space>
        </div>

        <Divider style={{ margin: '12px 0' }} />

        {/* Actions Section */}
        <div>
          <Title level={5} style={{ marginBottom: 12 }}>
            Quick Actions
          </Title>
          
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={onOpenAddModal}
            style={{ width: '100%', backgroundColor: theme?.palette?.primary?.main, }}
            size="large"
          >
            Add Business Hour
          </Button>
        </div>

        {/* Info Section */}
        <div style={{ 
          background: theme?.palette?.primary?.light, 
          padding: '12px', 
          borderRadius: '6px',
          marginTop: 'auto'
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <strong>Tip:</strong> Use filters to narrow down the business hours data. 
            Click on any row in the middle pane to view detailed information in the right pane.
          </Text>
        </div>
      </Space>
    </Card>
  );
};

export default LeftPane;