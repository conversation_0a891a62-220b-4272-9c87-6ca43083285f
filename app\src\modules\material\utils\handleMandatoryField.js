import { VISIBILITY_TYPE } from "@constant/enum";
import {setIsAnyMandatoryFieldEmpty} from "@app/payloadslice";
import { setMaterialRows } from "@slice/requestDataSlice";

export function handleMandatoryField({
  details,
  newValue,
  materialID,
  storedRows,
  dispatch,
}) {
  if (details?.visibility === VISIBILITY_TYPE.MANDATORY && !newValue) {
    dispatch(setIsAnyMandatoryFieldEmpty(true));
    const updatedRows = storedRows.map((row) =>
      row.id === materialID ? { ...row, validated: false } : row
    );
    dispatch(setMaterialRows(updatedRows));
  }
}
