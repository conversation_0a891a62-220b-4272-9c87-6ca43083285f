import{l as Kr,go as cf,aP as df,r as xr,n as yi,s as ff,cZ as pf,bf as mf,da as vf,gp as yf,fR as bi,C as hi,bI as gi,aT as Ri,t as bf,j as N,aR as hf,c as gf,B as Je,ft as Rf,a6 as Pf,dr as _f,O as Pi,al as Ef,ai as Cf}from"./index-226a1e75.js";import{a as wf,q as qf,c as $f,b as Fl,l as Sf,t as Of}from"./lz-string-127b8448.js";import xf from"./ErrorHistory-e3f4447c.js";var ye={},jl={exports:{}};jl.exports;(function(e){const t=(o=0)=>l=>`\x1B[${38+o};5;${l}m`,a=(o=0)=>(l,u,i)=>`\x1B[${38+o};2;${l};${u};${i}m`;function n(){const o=new Map,l={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};l.color.gray=l.color.blackBright,l.bgColor.bgGray=l.bgColor.bgBlackBright,l.color.grey=l.color.blackBright,l.bgColor.bgGrey=l.bgColor.bgBlackBright;for(const[u,i]of Object.entries(l)){for(const[s,d]of Object.entries(i))l[s]={open:`\x1B[${d[0]}m`,close:`\x1B[${d[1]}m`},i[s]=l[s],o.set(d[0],d[1]);Object.defineProperty(l,u,{value:i,enumerable:!1})}return Object.defineProperty(l,"codes",{value:o,enumerable:!1}),l.color.close="\x1B[39m",l.bgColor.close="\x1B[49m",l.color.ansi256=t(),l.color.ansi16m=a(),l.bgColor.ansi256=t(10),l.bgColor.ansi16m=a(10),Object.defineProperties(l,{rgbToAnsi256:{value:(u,i,s)=>u===i&&i===s?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(i/255*5)+Math.round(s/255*5),enumerable:!1},hexToRgb:{value:u=>{const i=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(u.toString(16));if(!i)return[0,0,0];let{colorString:s}=i.groups;s.length===3&&(s=s.split("").map(c=>c+c).join(""));const d=Number.parseInt(s,16);return[d>>16&255,d>>8&255,d&255]},enumerable:!1},hexToAnsi256:{value:u=>l.rgbToAnsi256(...l.hexToRgb(u)),enumerable:!1}}),l}Object.defineProperty(e,"exports",{enumerable:!0,get:n})})(jl);var Ts=jl.exports,Q={};Object.defineProperty(Q,"__esModule",{value:!0});Q.printIteratorEntries=Af;Q.printIteratorValues=Mf;Q.printListItems=If;Q.printObjectProperties=Bf;const Tf=(e,r)=>{const t=Object.keys(e).sort(r);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach(a=>{Object.getOwnPropertyDescriptor(e,a).enumerable&&t.push(a)}),t};function Af(e,r,t,a,n,o,l=": "){let u="",i=e.next();if(!i.done){u+=r.spacingOuter;const s=t+r.indent;for(;!i.done;){const d=o(i.value[0],r,s,a,n),c=o(i.value[1],r,s,a,n);u+=s+d+l+c,i=e.next(),i.done?r.min||(u+=","):u+=","+r.spacingInner}u+=r.spacingOuter+t}return u}function Mf(e,r,t,a,n,o){let l="",u=e.next();if(!u.done){l+=r.spacingOuter;const i=t+r.indent;for(;!u.done;)l+=i+o(u.value,r,i,a,n),u=e.next(),u.done?r.min||(l+=","):l+=","+r.spacingInner;l+=r.spacingOuter+t}return l}function If(e,r,t,a,n,o){let l="";if(e.length){l+=r.spacingOuter;const u=t+r.indent;for(let i=0;i<e.length;i++)l+=u,i in e&&(l+=o(e[i],r,u,a,n)),i<e.length-1?l+=","+r.spacingInner:r.min||(l+=",");l+=r.spacingOuter+t}return l}function Bf(e,r,t,a,n,o){let l="";const u=Tf(e,r.compareKeys);if(u.length){l+=r.spacingOuter;const i=t+r.indent;for(let s=0;s<u.length;s++){const d=u[s],c=o(d,r,i,a,n),f=o(e[d],r,i,a,n);l+=i+c+": "+f,s<u.length-1?l+=","+r.spacingInner:r.min||(l+=",")}l+=r.spacingOuter+t}return l}var se={};Object.defineProperty(se,"__esModule",{value:!0});se.test=se.serialize=se.default=void 0;var _i=Q,Yr=function(){return typeof globalThis<"u"?globalThis:typeof Yr<"u"?Yr:typeof self<"u"?self:typeof window<"u"?window:Function("return this")()}(),fo=Yr["jest-symbol-do-not-touch"]||Yr.Symbol;const Nf=typeof fo=="function"&&fo.for?fo.for("jest.asymmetricMatcher"):1267621,Tr=" ",As=(e,r,t,a,n,o)=>{const l=e.toString();return l==="ArrayContaining"||l==="ArrayNotContaining"?++a>r.maxDepth?"["+l+"]":l+Tr+"["+(0,_i.printListItems)(e.sample,r,t,a,n,o)+"]":l==="ObjectContaining"||l==="ObjectNotContaining"?++a>r.maxDepth?"["+l+"]":l+Tr+"{"+(0,_i.printObjectProperties)(e.sample,r,t,a,n,o)+"}":l==="StringMatching"||l==="StringNotMatching"||l==="StringContaining"||l==="StringNotContaining"?l+Tr+o(e.sample,r,t,a,n):e.toAsymmetricMatcher()};se.serialize=As;const Ms=e=>e&&e.$$typeof===Nf;se.test=Ms;const Ff={serialize:As,test:Ms};var jf=Ff;se.default=jf;var ce={};Object.defineProperty(ce,"__esModule",{value:!0});ce.test=ce.serialize=ce.default=void 0;var Is=Bs(wf),$=Bs(Ts);function Bs(e){return e&&e.__esModule?e:{default:e}}const Df=e=>e.replace((0,Is.default)(),r=>{switch(r){case $.default.red.close:case $.default.green.close:case $.default.cyan.close:case $.default.gray.close:case $.default.white.close:case $.default.yellow.close:case $.default.bgRed.close:case $.default.bgGreen.close:case $.default.bgYellow.close:case $.default.inverse.close:case $.default.dim.close:case $.default.bold.close:case $.default.reset.open:case $.default.reset.close:return"</>";case $.default.red.open:return"<red>";case $.default.green.open:return"<green>";case $.default.cyan.open:return"<cyan>";case $.default.gray.open:return"<gray>";case $.default.white.open:return"<white>";case $.default.yellow.open:return"<yellow>";case $.default.bgRed.open:return"<bgRed>";case $.default.bgGreen.open:return"<bgGreen>";case $.default.bgYellow.open:return"<bgYellow>";case $.default.inverse.open:return"<inverse>";case $.default.dim.open:return"<dim>";case $.default.bold.open:return"<bold>";default:return""}}),Ns=e=>typeof e=="string"&&!!e.match((0,Is.default)());ce.test=Ns;const Fs=(e,r,t,a,n,o)=>o(Df(e),r,t,a,n);ce.serialize=Fs;const kf={serialize:Fs,test:Ns};var Lf=kf;ce.default=Lf;var de={};Object.defineProperty(de,"__esModule",{value:!0});de.test=de.serialize=de.default=void 0;var Ei=Q;const Uf=" ",js=["DOMStringMap","NamedNodeMap"],Hf=/^(HTML\w*Collection|NodeList)$/,Wf=e=>js.indexOf(e)!==-1||Hf.test(e),Ds=e=>e&&e.constructor&&!!e.constructor.name&&Wf(e.constructor.name);de.test=Ds;const zf=e=>e.constructor.name==="NamedNodeMap",ks=(e,r,t,a,n,o)=>{const l=e.constructor.name;return++a>r.maxDepth?"["+l+"]":(r.min?"":l+Uf)+(js.indexOf(l)!==-1?"{"+(0,Ei.printObjectProperties)(zf(e)?Array.from(e).reduce((u,i)=>(u[i.name]=i.value,u),{}):{...e},r,t,a,n,o)+"}":"["+(0,Ei.printListItems)(Array.from(e),r,t,a,n,o)+"]")};de.serialize=ks;const Gf={serialize:ks,test:Ds};var Vf=Gf;de.default=Vf;var fe={},I={},Dl={};Object.defineProperty(Dl,"__esModule",{value:!0});Dl.default=Kf;function Kf(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}Object.defineProperty(I,"__esModule",{value:!0});I.printText=I.printProps=I.printElementAsLeaf=I.printElement=I.printComment=I.printChildren=void 0;var Ls=Yf(Dl);function Yf(e){return e&&e.__esModule?e:{default:e}}const Jf=(e,r,t,a,n,o,l)=>{const u=a+t.indent,i=t.colors;return e.map(s=>{const d=r[s];let c=l(d,t,u,n,o);return typeof d!="string"&&(c.indexOf(`
`)!==-1&&(c=t.spacingOuter+u+c+t.spacingOuter+a),c="{"+c+"}"),t.spacingInner+a+i.prop.open+s+i.prop.close+"="+i.value.open+c+i.value.close}).join("")};I.printProps=Jf;const Xf=(e,r,t,a,n,o)=>e.map(l=>r.spacingOuter+t+(typeof l=="string"?Us(l,r):o(l,r,t,a,n))).join("");I.printChildren=Xf;const Us=(e,r)=>{const t=r.colors.content;return t.open+(0,Ls.default)(e)+t.close};I.printText=Us;const Qf=(e,r)=>{const t=r.colors.comment;return t.open+"<!--"+(0,Ls.default)(e)+"-->"+t.close};I.printComment=Qf;const Zf=(e,r,t,a,n)=>{const o=a.colors.tag;return o.open+"<"+e+(r&&o.close+r+a.spacingOuter+n+o.open)+(t?">"+o.close+t+a.spacingOuter+n+o.open+"</"+e:(r&&!a.min?"":" ")+"/")+">"+o.close};I.printElement=Zf;const ep=(e,r)=>{const t=r.colors.tag;return t.open+"<"+e+t.close+" …"+t.open+" />"+t.close};I.printElementAsLeaf=ep;Object.defineProperty(fe,"__esModule",{value:!0});fe.test=fe.serialize=fe.default=void 0;var Me=I;const rp=1,Hs=3,Ws=8,zs=11,tp=/^((HTML|SVG)\w*)?Element$/,ap=e=>{try{return typeof e.hasAttribute=="function"&&e.hasAttribute("is")}catch{return!1}},np=e=>{const r=e.constructor.name,{nodeType:t,tagName:a}=e,n=typeof a=="string"&&a.includes("-")||ap(e);return t===rp&&(tp.test(r)||n)||t===Hs&&r==="Text"||t===Ws&&r==="Comment"||t===zs&&r==="DocumentFragment"},Gs=e=>{var r;return(e==null||(r=e.constructor)===null||r===void 0?void 0:r.name)&&np(e)};fe.test=Gs;function op(e){return e.nodeType===Hs}function lp(e){return e.nodeType===Ws}function po(e){return e.nodeType===zs}const Vs=(e,r,t,a,n,o)=>{if(op(e))return(0,Me.printText)(e.data,r);if(lp(e))return(0,Me.printComment)(e.data,r);const l=po(e)?"DocumentFragment":e.tagName.toLowerCase();return++a>r.maxDepth?(0,Me.printElementAsLeaf)(l,r):(0,Me.printElement)(l,(0,Me.printProps)(po(e)?[]:Array.from(e.attributes).map(u=>u.name).sort(),po(e)?{}:Array.from(e.attributes).reduce((u,i)=>(u[i.name]=i.value,u),{}),r,t+r.indent,a,n,o),(0,Me.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),r,t+r.indent,a,n,o),r,t)};fe.serialize=Vs;const ip={serialize:Vs,test:Gs};var up=ip;fe.default=up;var pe={};Object.defineProperty(pe,"__esModule",{value:!0});pe.test=pe.serialize=pe.default=void 0;var lr=Q;const sp="@@__IMMUTABLE_ITERABLE__@@",cp="@@__IMMUTABLE_LIST__@@",dp="@@__IMMUTABLE_KEYED__@@",fp="@@__IMMUTABLE_MAP__@@",Ci="@@__IMMUTABLE_ORDERED__@@",pp="@@__IMMUTABLE_RECORD__@@",mp="@@__IMMUTABLE_SEQ__@@",vp="@@__IMMUTABLE_SET__@@",yp="@@__IMMUTABLE_STACK__@@",We=e=>"Immutable."+e,vt=e=>"["+e+"]",ir=" ",wi="…",bp=(e,r,t,a,n,o,l)=>++a>r.maxDepth?vt(We(l)):We(l)+ir+"{"+(0,lr.printIteratorEntries)(e.entries(),r,t,a,n,o)+"}";function hp(e){let r=0;return{next(){if(r<e._keys.length){const t=e._keys[r++];return{done:!1,value:[t,e.get(t)]}}return{done:!0,value:void 0}}}}const gp=(e,r,t,a,n,o)=>{const l=We(e._name||"Record");return++a>r.maxDepth?vt(l):l+ir+"{"+(0,lr.printIteratorEntries)(hp(e),r,t,a,n,o)+"}"},Rp=(e,r,t,a,n,o)=>{const l=We("Seq");return++a>r.maxDepth?vt(l):e[dp]?l+ir+"{"+(e._iter||e._object?(0,lr.printIteratorEntries)(e.entries(),r,t,a,n,o):wi)+"}":l+ir+"["+(e._iter||e._array||e._collection||e._iterable?(0,lr.printIteratorValues)(e.values(),r,t,a,n,o):wi)+"]"},mo=(e,r,t,a,n,o,l)=>++a>r.maxDepth?vt(We(l)):We(l)+ir+"["+(0,lr.printIteratorValues)(e.values(),r,t,a,n,o)+"]",Ks=(e,r,t,a,n,o)=>e[fp]?bp(e,r,t,a,n,o,e[Ci]?"OrderedMap":"Map"):e[cp]?mo(e,r,t,a,n,o,"List"):e[vp]?mo(e,r,t,a,n,o,e[Ci]?"OrderedSet":"Set"):e[yp]?mo(e,r,t,a,n,o,"Stack"):e[mp]?Rp(e,r,t,a,n,o):gp(e,r,t,a,n,o);pe.serialize=Ks;const Ys=e=>e&&(e[sp]===!0||e[pp]===!0);pe.test=Ys;const Pp={serialize:Ks,test:Ys};var _p=Pp;pe.default=_p;var me={},Js={exports:{}},q={};/** @license React v17.0.2
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yt=60103,bt=60106,pr=60107,mr=60108,vr=60114,yr=60109,br=60110,hr=60112,gr=60113,kl=60120,Rr=60115,Pr=60116,Xs=60121,Qs=60122,Zs=60117,ec=60129,rc=60131;if(typeof Symbol=="function"&&Symbol.for){var M=Symbol.for;yt=M("react.element"),bt=M("react.portal"),pr=M("react.fragment"),mr=M("react.strict_mode"),vr=M("react.profiler"),yr=M("react.provider"),br=M("react.context"),hr=M("react.forward_ref"),gr=M("react.suspense"),kl=M("react.suspense_list"),Rr=M("react.memo"),Pr=M("react.lazy"),Xs=M("react.block"),Qs=M("react.server.block"),Zs=M("react.fundamental"),ec=M("react.debug_trace_mode"),rc=M("react.legacy_hidden")}function z(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case yt:switch(e=e.type,e){case pr:case vr:case mr:case gr:case kl:return e;default:switch(e=e&&e.$$typeof,e){case br:case hr:case Pr:case Rr:case yr:return e;default:return r}}case bt:return r}}}var Ep=yr,Cp=yt,wp=hr,qp=pr,$p=Pr,Sp=Rr,Op=bt,xp=vr,Tp=mr,Ap=gr;q.ContextConsumer=br;q.ContextProvider=Ep;q.Element=Cp;q.ForwardRef=wp;q.Fragment=qp;q.Lazy=$p;q.Memo=Sp;q.Portal=Op;q.Profiler=xp;q.StrictMode=Tp;q.Suspense=Ap;q.isAsyncMode=function(){return!1};q.isConcurrentMode=function(){return!1};q.isContextConsumer=function(e){return z(e)===br};q.isContextProvider=function(e){return z(e)===yr};q.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===yt};q.isForwardRef=function(e){return z(e)===hr};q.isFragment=function(e){return z(e)===pr};q.isLazy=function(e){return z(e)===Pr};q.isMemo=function(e){return z(e)===Rr};q.isPortal=function(e){return z(e)===bt};q.isProfiler=function(e){return z(e)===vr};q.isStrictMode=function(e){return z(e)===mr};q.isSuspense=function(e){return z(e)===gr};q.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===pr||e===vr||e===ec||e===mr||e===gr||e===kl||e===rc||typeof e=="object"&&e!==null&&(e.$$typeof===Pr||e.$$typeof===Rr||e.$$typeof===yr||e.$$typeof===br||e.$$typeof===hr||e.$$typeof===Zs||e.$$typeof===Xs||e[0]===Qs)};q.typeOf=z;Js.exports=q;var Mp=Js.exports;Object.defineProperty(me,"__esModule",{value:!0});me.test=me.serialize=me.default=void 0;var Re=Ip(Mp),Ar=I;function tc(e){if(typeof WeakMap!="function")return null;var r=new WeakMap,t=new WeakMap;return(tc=function(a){return a?t:r})(e)}function Ip(e,r){if(!r&&e&&e.__esModule)return e;if(e===null||typeof e!="object"&&typeof e!="function")return{default:e};var t=tc(r);if(t&&t.has(e))return t.get(e);var a={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(o!=="default"&&Object.prototype.hasOwnProperty.call(e,o)){var l=n?Object.getOwnPropertyDescriptor(e,o):null;l&&(l.get||l.set)?Object.defineProperty(a,o,l):a[o]=e[o]}return a.default=e,t&&t.set(e,a),a}const ac=(e,r=[])=>(Array.isArray(e)?e.forEach(t=>{ac(t,r)}):e!=null&&e!==!1&&r.push(e),r),qi=e=>{const r=e.type;if(typeof r=="string")return r;if(typeof r=="function")return r.displayName||r.name||"Unknown";if(Re.isFragment(e))return"React.Fragment";if(Re.isSuspense(e))return"React.Suspense";if(typeof r=="object"&&r!==null){if(Re.isContextProvider(e))return"Context.Provider";if(Re.isContextConsumer(e))return"Context.Consumer";if(Re.isForwardRef(e)){if(r.displayName)return r.displayName;const t=r.render.displayName||r.render.name||"";return t!==""?"ForwardRef("+t+")":"ForwardRef"}if(Re.isMemo(e)){const t=r.displayName||r.type.displayName||r.type.name||"";return t!==""?"Memo("+t+")":"Memo"}}return"UNDEFINED"},Bp=e=>{const{props:r}=e;return Object.keys(r).filter(t=>t!=="children"&&r[t]!==void 0).sort()},nc=(e,r,t,a,n,o)=>++a>r.maxDepth?(0,Ar.printElementAsLeaf)(qi(e),r):(0,Ar.printElement)(qi(e),(0,Ar.printProps)(Bp(e),e.props,r,t+r.indent,a,n,o),(0,Ar.printChildren)(ac(e.props.children),r,t+r.indent,a,n,o),r,t);me.serialize=nc;const oc=e=>e!=null&&Re.isElement(e);me.test=oc;const Np={serialize:nc,test:oc};var Fp=Np;me.default=Fp;var ve={};Object.defineProperty(ve,"__esModule",{value:!0});ve.test=ve.serialize=ve.default=void 0;var Mr=I,Jr=function(){return typeof globalThis<"u"?globalThis:typeof Jr<"u"?Jr:typeof self<"u"?self:typeof window<"u"?window:Function("return this")()}(),vo=Jr["jest-symbol-do-not-touch"]||Jr.Symbol;const jp=typeof vo=="function"&&vo.for?vo.for("react.test.json"):245830487,Dp=e=>{const{props:r}=e;return r?Object.keys(r).filter(t=>r[t]!==void 0).sort():[]},lc=(e,r,t,a,n,o)=>++a>r.maxDepth?(0,Mr.printElementAsLeaf)(e.type,r):(0,Mr.printElement)(e.type,e.props?(0,Mr.printProps)(Dp(e),e.props,r,t+r.indent,a,n,o):"",e.children?(0,Mr.printChildren)(e.children,r,t+r.indent,a,n,o):"",r,t);ve.serialize=lc;const ic=e=>e&&e.$$typeof===jp;ve.test=ic;const kp={serialize:lc,test:ic};var Lp=kp;ve.default=Lp;Object.defineProperty(ye,"__esModule",{value:!0});ye.default=ye.DEFAULT_OPTIONS=void 0;var Up=ye.format=gc,uc=ye.plugins=void 0,Hp=be(Ts),Xe=Q,Wp=be(se),zp=be(ce),Gp=be(de),Vp=be(fe),Kp=be(pe),Yp=be(me),Jp=be(ve);function be(e){return e&&e.__esModule?e:{default:e}}const sc=Object.prototype.toString,Xp=Date.prototype.toISOString,Qp=Error.prototype.toString,$i=RegExp.prototype.toString,yo=e=>typeof e.constructor=="function"&&e.constructor.name||"Object",Zp=e=>typeof window<"u"&&e===window,em=/^Symbol\((.*)\)(.*)$/,rm=/\n/gi;class cc extends Error{constructor(r,t){super(r),this.stack=t,this.name=this.constructor.name}}function tm(e){return e==="[object Array]"||e==="[object ArrayBuffer]"||e==="[object DataView]"||e==="[object Float32Array]"||e==="[object Float64Array]"||e==="[object Int8Array]"||e==="[object Int16Array]"||e==="[object Int32Array]"||e==="[object Uint8Array]"||e==="[object Uint8ClampedArray]"||e==="[object Uint16Array]"||e==="[object Uint32Array]"}function am(e){return Object.is(e,-0)?"-0":String(e)}function nm(e){return`${e}n`}function Si(e,r){return r?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Oi(e){return String(e).replace(em,"Symbol($1)")}function xi(e){return"["+Qp.call(e)+"]"}function dc(e,r,t,a){if(e===!0||e===!1)return""+e;if(e===void 0)return"undefined";if(e===null)return"null";const n=typeof e;if(n==="number")return am(e);if(n==="bigint")return nm(e);if(n==="string")return a?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if(n==="function")return Si(e,r);if(n==="symbol")return Oi(e);const o=sc.call(e);return o==="[object WeakMap]"?"WeakMap {}":o==="[object WeakSet]"?"WeakSet {}":o==="[object Function]"||o==="[object GeneratorFunction]"?Si(e,r):o==="[object Symbol]"?Oi(e):o==="[object Date]"?isNaN(+e)?"Date { NaN }":Xp.call(e):o==="[object Error]"?xi(e):o==="[object RegExp]"?t?$i.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):$i.call(e):e instanceof Error?xi(e):null}function fc(e,r,t,a,n,o){if(n.indexOf(e)!==-1)return"[Circular]";n=n.slice(),n.push(e);const l=++a>r.maxDepth,u=r.min;if(r.callToJSON&&!l&&e.toJSON&&typeof e.toJSON=="function"&&!o)return ne(e.toJSON(),r,t,a,n,!0);const i=sc.call(e);return i==="[object Arguments]"?l?"[Arguments]":(u?"":"Arguments ")+"["+(0,Xe.printListItems)(e,r,t,a,n,ne)+"]":tm(i)?l?"["+e.constructor.name+"]":(u||!r.printBasicPrototype&&e.constructor.name==="Array"?"":e.constructor.name+" ")+"["+(0,Xe.printListItems)(e,r,t,a,n,ne)+"]":i==="[object Map]"?l?"[Map]":"Map {"+(0,Xe.printIteratorEntries)(e.entries(),r,t,a,n,ne," => ")+"}":i==="[object Set]"?l?"[Set]":"Set {"+(0,Xe.printIteratorValues)(e.values(),r,t,a,n,ne)+"}":l||Zp(e)?"["+yo(e)+"]":(u||!r.printBasicPrototype&&yo(e)==="Object"?"":yo(e)+" ")+"{"+(0,Xe.printObjectProperties)(e,r,t,a,n,ne)+"}"}function om(e){return e.serialize!=null}function pc(e,r,t,a,n,o){let l;try{l=om(e)?e.serialize(r,t,a,n,o,ne):e.print(r,u=>ne(u,t,a,n,o),u=>{const i=a+t.indent;return i+u.replace(rm,`
`+i)},{edgeSpacing:t.spacingOuter,min:t.min,spacing:t.spacingInner},t.colors)}catch(u){throw new cc(u.message,u.stack)}if(typeof l!="string")throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof l}".`);return l}function mc(e,r){for(let t=0;t<e.length;t++)try{if(e[t].test(r))return e[t]}catch(a){throw new cc(a.message,a.stack)}return null}function ne(e,r,t,a,n,o){const l=mc(r.plugins,e);if(l!==null)return pc(l,e,r,t,a,n);const u=dc(e,r.printFunctionName,r.escapeRegex,r.escapeString);return u!==null?u:fc(e,r,t,a,n,o)}const Ll={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},vc=Object.keys(Ll),U={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:Ll};ye.DEFAULT_OPTIONS=U;function lm(e){if(Object.keys(e).forEach(r=>{if(!U.hasOwnProperty(r))throw new Error(`pretty-format: Unknown option "${r}".`)}),e.min&&e.indent!==void 0&&e.indent!==0)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(e.theme!==void 0){if(e.theme===null)throw new Error('pretty-format: Option "theme" must not be null.');if(typeof e.theme!="object")throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}const im=e=>vc.reduce((r,t)=>{const a=e.theme&&e.theme[t]!==void 0?e.theme[t]:Ll[t],n=a&&Hp.default[a];if(n&&typeof n.close=="string"&&typeof n.open=="string")r[t]=n;else throw new Error(`pretty-format: Option "theme" has a key "${t}" whose value "${a}" is undefined in ansi-styles.`);return r},Object.create(null)),um=()=>vc.reduce((e,r)=>(e[r]={close:"",open:""},e),Object.create(null)),yc=e=>e&&e.printFunctionName!==void 0?e.printFunctionName:U.printFunctionName,bc=e=>e&&e.escapeRegex!==void 0?e.escapeRegex:U.escapeRegex,hc=e=>e&&e.escapeString!==void 0?e.escapeString:U.escapeString,Ti=e=>{var r;return{callToJSON:e&&e.callToJSON!==void 0?e.callToJSON:U.callToJSON,colors:e&&e.highlight?im(e):um(),compareKeys:e&&typeof e.compareKeys=="function"?e.compareKeys:U.compareKeys,escapeRegex:bc(e),escapeString:hc(e),indent:e&&e.min?"":sm(e&&e.indent!==void 0?e.indent:U.indent),maxDepth:e&&e.maxDepth!==void 0?e.maxDepth:U.maxDepth,min:e&&e.min!==void 0?e.min:U.min,plugins:e&&e.plugins!==void 0?e.plugins:U.plugins,printBasicPrototype:(r=e==null?void 0:e.printBasicPrototype)!==null&&r!==void 0?r:!0,printFunctionName:yc(e),spacingInner:e&&e.min?" ":`
`,spacingOuter:e&&e.min?"":`
`}};function sm(e){return new Array(e+1).join(" ")}function gc(e,r){if(r&&(lm(r),r.plugins)){const a=mc(r.plugins,e);if(a!==null)return pc(a,e,Ti(r),"",0,[])}const t=dc(e,yc(r),bc(r),hc(r));return t!==null?t:fc(e,Ti(r),"",0,[])}const cm={AsymmetricMatcher:Wp.default,ConvertAnsi:zp.default,DOMCollection:Gp.default,DOMElement:Vp.default,Immutable:Kp.default,ReactElement:Yp.default,ReactTestComponent:Jp.default};uc=ye.plugins=cm;var dm=gc;ye.default=dm;function ur(e){"@babel/helpers - typeof";return ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},ur(e)}function Ai(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,a)}return t}function Mi(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Ai(Object(t),!0).forEach(function(a){fm(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ai(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function fm(e,r,t){return r=pm(r),r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function pm(e){var r=mm(e,"string");return ur(r)==="symbol"?r:String(r)}function mm(e,r){if(ur(e)!=="object"||e===null)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var a=t.call(e,r||"default");if(ur(a)!=="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return(r==="string"?String:Number)(e)}function Rc(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=qf(e,"aria-describedby").map(function(n){return $f(n,Mi(Mi({},r),{},{compute:"description"}))}).join(" ");if(t===""){var a=e.getAttribute("title");t=a===null?"":a}return t}var H={},ht={},Ce={},gt={};Object.defineProperty(gt,"__esModule",{value:!0});gt.default=void 0;function vm(){var e=this,r=0,t={"@@iterator":function(){return t},next:function(){if(r<e.length){var n=e[r];return r=r+1,{done:!1,value:n}}else return{done:!0}}};return t}var ym=vm;gt.default=ym;Object.defineProperty(Ce,"__esModule",{value:!0});Ce.default=gm;var bm=hm(gt);function hm(e){return e&&e.__esModule?e:{default:e}}function rl(e){"@babel/helpers - typeof";return rl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},rl(e)}function gm(e,r){return typeof Symbol=="function"&&rl(Symbol.iterator)==="symbol"&&Object.defineProperty(e,Symbol.iterator,{value:bm.default.bind(r)}),e}Object.defineProperty(ht,"__esModule",{value:!0});ht.default=void 0;var Rm=Pm(Ce);function Pm(e){return e&&e.__esModule?e:{default:e}}function bo(e,r){return Cm(e)||Em(e,r)||Pc(e,r)||_m()}function _m(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Em(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],n=!0,o=!1,l,u;try{for(t=t.call(e);!(n=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));n=!0);}catch(i){o=!0,u=i}finally{try{!n&&t.return!=null&&t.return()}finally{if(o)throw u}}return a}}function Cm(e){if(Array.isArray(e))return e}function wm(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=Pc(e))||r&&e&&typeof e.length=="number"){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(s){throw s},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o=!0,l=!1,u;return{s:function(){t=t.call(e)},n:function(){var s=t.next();return o=s.done,s},e:function(s){l=!0,u=s},f:function(){try{!o&&t.return!=null&&t.return()}finally{if(l)throw u}}}}function Pc(e,r){if(e){if(typeof e=="string")return Ii(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ii(e,r)}}function Ii(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}var Ie=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],tl={entries:function(){return Ie},forEach:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=wm(Ie),n;try{for(a.s();!(n=a.n()).done;){var o=bo(n.value,2),l=o[0],u=o[1];r.call(t,u,l,Ie)}}catch(i){a.e(i)}finally{a.f()}},get:function(r){var t=Ie.find(function(a){return a[0]===r});return t&&t[1]},has:function(r){return!!tl.get(r)},keys:function(){return Ie.map(function(r){var t=bo(r,1),a=t[0];return a})},values:function(){return Ie.map(function(r){var t=bo(r,2),a=t[1];return a})}},qm=(0,Rm.default)(tl,tl.entries());ht.default=qm;var Rt={};Object.defineProperty(Rt,"__esModule",{value:!0});Rt.default=void 0;var $m=Sm(Ce);function Sm(e){return e&&e.__esModule?e:{default:e}}function ho(e,r){return Tm(e)||xm(e,r)||_c(e,r)||Om()}function Om(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xm(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],n=!0,o=!1,l,u;try{for(t=t.call(e);!(n=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));n=!0);}catch(i){o=!0,u=i}finally{try{!n&&t.return!=null&&t.return()}finally{if(o)throw u}}return a}}function Tm(e){if(Array.isArray(e))return e}function Am(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=_c(e))||r&&e&&typeof e.length=="number"){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(s){throw s},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o=!0,l=!1,u;return{s:function(){t=t.call(e)},n:function(){var s=t.next();return o=s.done,s},e:function(s){l=!0,u=s},f:function(){try{!o&&t.return!=null&&t.return()}finally{if(l)throw u}}}}function _c(e,r){if(e){if(typeof e=="string")return Bi(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Bi(e,r)}}function Bi(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}var Be=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],al={entries:function(){return Be},forEach:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=Am(Be),n;try{for(a.s();!(n=a.n()).done;){var o=ho(n.value,2),l=o[0],u=o[1];r.call(t,u,l,Be)}}catch(i){a.e(i)}finally{a.f()}},get:function(r){var t=Be.find(function(a){return a[0]===r});return t&&t[1]},has:function(r){return!!al.get(r)},keys:function(){return Be.map(function(r){var t=ho(r,1),a=t[0];return a})},values:function(){return Be.map(function(r){var t=ho(r,2),a=t[1];return a})}},Mm=(0,$m.default)(al,al.entries());Rt.default=Mm;var Ke={},Pt={},_t={};Object.defineProperty(_t,"__esModule",{value:!0});_t.default=void 0;var Im={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]},Bm=Im;_t.default=Bm;var Et={};Object.defineProperty(Et,"__esModule",{value:!0});Et.default=void 0;var Nm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]},Fm=Nm;Et.default=Fm;var Ct={};Object.defineProperty(Ct,"__esModule",{value:!0});Ct.default=void 0;var jm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]},Dm=jm;Ct.default=Dm;var wt={};Object.defineProperty(wt,"__esModule",{value:!0});wt.default=void 0;var km={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Lm=km;wt.default=Lm;var qt={};Object.defineProperty(qt,"__esModule",{value:!0});qt.default=void 0;var Um={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},Hm=Um;qt.default=Hm;var $t={};Object.defineProperty($t,"__esModule",{value:!0});$t.default=void 0;var Wm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]},zm=Wm;$t.default=zm;var St={};Object.defineProperty(St,"__esModule",{value:!0});St.default=void 0;var Gm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},Vm=Gm;St.default=Vm;var Ot={};Object.defineProperty(Ot,"__esModule",{value:!0});Ot.default=void 0;var Km={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},Ym=Km;Ot.default=Ym;var xt={};Object.defineProperty(xt,"__esModule",{value:!0});xt.default=void 0;var Jm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]},Xm=Jm;xt.default=Xm;var Tt={};Object.defineProperty(Tt,"__esModule",{value:!0});Tt.default=void 0;var Qm={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]},Zm=Qm;Tt.default=Zm;var At={};Object.defineProperty(At,"__esModule",{value:!0});At.default=void 0;var ev={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]},rv=ev;At.default=rv;var Mt={};Object.defineProperty(Mt,"__esModule",{value:!0});Mt.default=void 0;var tv={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]},av=tv;Mt.default=av;Object.defineProperty(Pt,"__esModule",{value:!0});Pt.default=void 0;var nv=D(_t),ov=D(Et),lv=D(Ct),iv=D(wt),uv=D(qt),sv=D($t),cv=D(St),dv=D(Ot),fv=D(xt),pv=D(Tt),mv=D(At),vv=D(Mt);function D(e){return e&&e.__esModule?e:{default:e}}var yv=[["command",nv.default],["composite",ov.default],["input",lv.default],["landmark",iv.default],["range",uv.default],["roletype",sv.default],["section",cv.default],["sectionhead",dv.default],["select",fv.default],["structure",pv.default],["widget",mv.default],["window",vv.default]],bv=yv;Pt.default=bv;var It={},Bt={};Object.defineProperty(Bt,"__esModule",{value:!0});Bt.default=void 0;var hv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},gv=hv;Bt.default=gv;var Nt={};Object.defineProperty(Nt,"__esModule",{value:!0});Nt.default=void 0;var Rv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]},Pv=Rv;Nt.default=Pv;var Ft={};Object.defineProperty(Ft,"__esModule",{value:!0});Ft.default=void 0;var _v={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},Ev=_v;Ft.default=Ev;var jt={};Object.defineProperty(jt,"__esModule",{value:!0});jt.default=void 0;var Cv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]},wv=Cv;jt.default=wv;var Dt={};Object.defineProperty(Dt,"__esModule",{value:!0});Dt.default=void 0;var qv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},$v=qv;Dt.default=$v;var kt={};Object.defineProperty(kt,"__esModule",{value:!0});kt.default=void 0;var Sv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Ov=Sv;kt.default=Ov;var Lt={};Object.defineProperty(Lt,"__esModule",{value:!0});Lt.default=void 0;var xv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]},Tv=xv;Lt.default=Tv;var Ut={};Object.defineProperty(Ut,"__esModule",{value:!0});Ut.default=void 0;var Av={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Mv=Av;Ut.default=Mv;var Ht={};Object.defineProperty(Ht,"__esModule",{value:!0});Ht.default=void 0;var Iv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Bv=Iv;Ht.default=Bv;var Wt={};Object.defineProperty(Wt,"__esModule",{value:!0});Wt.default=void 0;var Nv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]},Fv=Nv;Wt.default=Fv;var zt={};Object.defineProperty(zt,"__esModule",{value:!0});zt.default=void 0;var jv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Dv=jv;zt.default=Dv;var Gt={};Object.defineProperty(Gt,"__esModule",{value:!0});Gt.default=void 0;var kv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]},Lv=kv;Gt.default=Lv;var Vt={};Object.defineProperty(Vt,"__esModule",{value:!0});Vt.default=void 0;var Uv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]},Hv=Uv;Vt.default=Hv;var Kt={};Object.defineProperty(Kt,"__esModule",{value:!0});Kt.default=void 0;var Wv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},zv=Wv;Kt.default=zv;var Yt={};Object.defineProperty(Yt,"__esModule",{value:!0});Yt.default=void 0;var Gv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},Vv=Gv;Yt.default=Vv;var Jt={};Object.defineProperty(Jt,"__esModule",{value:!0});Jt.default=void 0;var Kv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Yv=Kv;Jt.default=Yv;var Xt={};Object.defineProperty(Xt,"__esModule",{value:!0});Xt.default=void 0;var Jv={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Xv=Jv;Xt.default=Xv;var Qt={};Object.defineProperty(Qt,"__esModule",{value:!0});Qt.default=void 0;var Qv={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]},Zv=Qv;Qt.default=Zv;var Zt={};Object.defineProperty(Zt,"__esModule",{value:!0});Zt.default=void 0;var ey={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]},ry=ey;Zt.default=ry;var ea={};Object.defineProperty(ea,"__esModule",{value:!0});ea.default=void 0;var ty={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},ay=ty;ea.default=ay;var ra={};Object.defineProperty(ra,"__esModule",{value:!0});ra.default=void 0;var ny={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},oy=ny;ra.default=oy;var ta={};Object.defineProperty(ta,"__esModule",{value:!0});ta.default=void 0;var ly={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]},iy=ly;ta.default=iy;var aa={};Object.defineProperty(aa,"__esModule",{value:!0});aa.default=void 0;var uy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},sy=uy;aa.default=sy;var na={};Object.defineProperty(na,"__esModule",{value:!0});na.default=void 0;var cy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},dy=cy;na.default=dy;var oa={};Object.defineProperty(oa,"__esModule",{value:!0});oa.default=void 0;var fy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},py=fy;oa.default=py;var la={};Object.defineProperty(la,"__esModule",{value:!0});la.default=void 0;var my={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]},vy=my;la.default=vy;var ia={};Object.defineProperty(ia,"__esModule",{value:!0});ia.default=void 0;var yy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]},by=yy;ia.default=by;var ua={};Object.defineProperty(ua,"__esModule",{value:!0});ua.default=void 0;var hy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},gy=hy;ua.default=gy;var sa={};Object.defineProperty(sa,"__esModule",{value:!0});sa.default=void 0;var Ry={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]},Py=Ry;sa.default=Py;var ca={};Object.defineProperty(ca,"__esModule",{value:!0});ca.default=void 0;var _y={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Ey=_y;ca.default=Ey;var da={};Object.defineProperty(da,"__esModule",{value:!0});da.default=void 0;var Cy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},wy=Cy;da.default=wy;var fa={};Object.defineProperty(fa,"__esModule",{value:!0});fa.default=void 0;var qy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]},$y=qy;fa.default=$y;var pa={};Object.defineProperty(pa,"__esModule",{value:!0});pa.default=void 0;var Sy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]},Oy=Sy;pa.default=Oy;var ma={};Object.defineProperty(ma,"__esModule",{value:!0});ma.default=void 0;var xy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]},Ty=xy;ma.default=Ty;var va={};Object.defineProperty(va,"__esModule",{value:!0});va.default=void 0;var Ay={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},My=Ay;va.default=My;var ya={};Object.defineProperty(ya,"__esModule",{value:!0});ya.default=void 0;var Iy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},By=Iy;ya.default=By;var ba={};Object.defineProperty(ba,"__esModule",{value:!0});ba.default=void 0;var Ny={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},Fy=Ny;ba.default=Fy;var ha={};Object.defineProperty(ha,"__esModule",{value:!0});ha.default=void 0;var jy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Dy=jy;ha.default=Dy;var ga={};Object.defineProperty(ga,"__esModule",{value:!0});ga.default=void 0;var ky={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Ly=ky;ga.default=Ly;var Ra={};Object.defineProperty(Ra,"__esModule",{value:!0});Ra.default=void 0;var Uy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]},Hy=Uy;Ra.default=Hy;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0});Pa.default=void 0;var Wy={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]},zy=Wy;Pa.default=zy;var _a={};Object.defineProperty(_a,"__esModule",{value:!0});_a.default=void 0;var Gy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]},Vy=Gy;_a.default=Vy;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0});Ea.default=void 0;var Ky={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]},Yy=Ky;Ea.default=Yy;var Ca={};Object.defineProperty(Ca,"__esModule",{value:!0});Ca.default=void 0;var Jy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]},Xy=Jy;Ca.default=Xy;var wa={};Object.defineProperty(wa,"__esModule",{value:!0});wa.default=void 0;var Qy={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]},Zy=Qy;wa.default=Zy;var qa={};Object.defineProperty(qa,"__esModule",{value:!0});qa.default=void 0;var eb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},rb=eb;qa.default=rb;var $a={};Object.defineProperty($a,"__esModule",{value:!0});$a.default=void 0;var tb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]},ab=tb;$a.default=ab;var Sa={};Object.defineProperty(Sa,"__esModule",{value:!0});Sa.default=void 0;var nb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},ob=nb;Sa.default=ob;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0});Oa.default=void 0;var lb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]},ib=lb;Oa.default=ib;var xa={};Object.defineProperty(xa,"__esModule",{value:!0});xa.default=void 0;var ub={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},sb=ub;xa.default=sb;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0});Ta.default=void 0;var cb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},db=cb;Ta.default=db;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0});Aa.default=void 0;var fb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]},pb=fb;Aa.default=pb;var Ma={};Object.defineProperty(Ma,"__esModule",{value:!0});Ma.default=void 0;var mb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]},vb=mb;Ma.default=vb;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0});Ia.default=void 0;var yb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]},bb=yb;Ia.default=bb;var Ba={};Object.defineProperty(Ba,"__esModule",{value:!0});Ba.default=void 0;var hb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},gb=hb;Ba.default=gb;var Na={};Object.defineProperty(Na,"__esModule",{value:!0});Na.default=void 0;var Rb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]},Pb=Rb;Na.default=Pb;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0});Fa.default=void 0;var _b={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]},Eb=_b;Fa.default=Eb;var ja={};Object.defineProperty(ja,"__esModule",{value:!0});ja.default=void 0;var Cb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]},wb=Cb;ja.default=wb;var Da={};Object.defineProperty(Da,"__esModule",{value:!0});Da.default=void 0;var qb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]},$b=qb;Da.default=$b;var ka={};Object.defineProperty(ka,"__esModule",{value:!0});ka.default=void 0;var Sb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},Ob=Sb;ka.default=Ob;var La={};Object.defineProperty(La,"__esModule",{value:!0});La.default=void 0;var xb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]},Tb=xb;La.default=Tb;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0});Ua.default=void 0;var Ab={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]},Mb=Ab;Ua.default=Mb;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0});Ha.default=void 0;var Ib={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]},Bb=Ib;Ha.default=Bb;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0});Wa.default=void 0;var Nb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]},Fb=Nb;Wa.default=Fb;var za={};Object.defineProperty(za,"__esModule",{value:!0});za.default=void 0;var jb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Db=jb;za.default=Db;var Ga={};Object.defineProperty(Ga,"__esModule",{value:!0});Ga.default=void 0;var kb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Lb=kb;Ga.default=Lb;var Va={};Object.defineProperty(Va,"__esModule",{value:!0});Va.default=void 0;var Ub={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Hb=Ub;Va.default=Hb;var Ka={};Object.defineProperty(Ka,"__esModule",{value:!0});Ka.default=void 0;var Wb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},zb=Wb;Ka.default=zb;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0});Ya.default=void 0;var Gb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]},Vb=Gb;Ya.default=Vb;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0});Ja.default=void 0;var Kb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]},Yb=Kb;Ja.default=Yb;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0});Xa.default=void 0;var Jb={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]},Xb=Jb;Xa.default=Xb;var Qa={};Object.defineProperty(Qa,"__esModule",{value:!0});Qa.default=void 0;var Qb={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]},Zb=Qb;Qa.default=Zb;var Za={};Object.defineProperty(Za,"__esModule",{value:!0});Za.default=void 0;var eh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},rh=eh;Za.default=rh;var en={};Object.defineProperty(en,"__esModule",{value:!0});en.default=void 0;var th={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},ah=th;en.default=ah;var rn={};Object.defineProperty(rn,"__esModule",{value:!0});rn.default=void 0;var nh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]},oh=nh;rn.default=oh;var tn={};Object.defineProperty(tn,"__esModule",{value:!0});tn.default=void 0;var lh={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},ih=lh;tn.default=ih;var an={};Object.defineProperty(an,"__esModule",{value:!0});an.default=void 0;var uh={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]},sh=uh;an.default=sh;var nn={};Object.defineProperty(nn,"__esModule",{value:!0});nn.default=void 0;var ch={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]},dh=ch;nn.default=dh;var on={};Object.defineProperty(on,"__esModule",{value:!0});on.default=void 0;var fh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},ph=fh;on.default=ph;var ln={};Object.defineProperty(ln,"__esModule",{value:!0});ln.default=void 0;var mh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]},vh=mh;ln.default=vh;var un={};Object.defineProperty(un,"__esModule",{value:!0});un.default=void 0;var yh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]},bh=yh;un.default=bh;var sn={};Object.defineProperty(sn,"__esModule",{value:!0});sn.default=void 0;var hh={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]},gh=hh;sn.default=gh;Object.defineProperty(It,"__esModule",{value:!0});It.default=void 0;var Rh=p(Bt),Ph=p(Nt),_h=p(Ft),Eh=p(jt),Ch=p(Dt),wh=p(kt),qh=p(Lt),$h=p(Ut),Sh=p(Ht),Oh=p(Wt),xh=p(zt),Th=p(Gt),Ah=p(Vt),Mh=p(Kt),Ih=p(Yt),Bh=p(Jt),Nh=p(Xt),Fh=p(Qt),jh=p(Zt),Dh=p(ea),kh=p(ra),Lh=p(ta),Uh=p(aa),Hh=p(na),Wh=p(oa),zh=p(la),Gh=p(ia),Vh=p(ua),Kh=p(sa),Yh=p(ca),Jh=p(da),Xh=p(fa),Qh=p(pa),Zh=p(ma),eg=p(va),rg=p(ya),tg=p(ba),ag=p(ha),ng=p(ga),og=p(Ra),lg=p(Pa),ig=p(_a),ug=p(Ea),sg=p(Ca),cg=p(wa),dg=p(qa),fg=p($a),pg=p(Sa),mg=p(Oa),vg=p(xa),yg=p(Ta),bg=p(Aa),hg=p(Ma),gg=p(Ia),Rg=p(Ba),Pg=p(Na),_g=p(Fa),Eg=p(ja),Cg=p(Da),wg=p(ka),qg=p(La),$g=p(Ua),Sg=p(Ha),Og=p(Wa),xg=p(za),Tg=p(Ga),Ag=p(Va),Mg=p(Ka),Ig=p(Ya),Bg=p(Ja),Ng=p(Xa),Fg=p(Qa),jg=p(Za),Dg=p(en),kg=p(rn),Lg=p(tn),Ug=p(an),Hg=p(nn),Wg=p(on),zg=p(ln),Gg=p(un),Vg=p(sn);function p(e){return e&&e.__esModule?e:{default:e}}var Kg=[["alert",Rh.default],["alertdialog",Ph.default],["application",_h.default],["article",Eh.default],["banner",Ch.default],["blockquote",wh.default],["button",qh.default],["caption",$h.default],["cell",Sh.default],["checkbox",Oh.default],["code",xh.default],["columnheader",Th.default],["combobox",Ah.default],["complementary",Mh.default],["contentinfo",Ih.default],["definition",Bh.default],["deletion",Nh.default],["dialog",Fh.default],["directory",jh.default],["document",Dh.default],["emphasis",kh.default],["feed",Lh.default],["figure",Uh.default],["form",Hh.default],["generic",Wh.default],["grid",zh.default],["gridcell",Gh.default],["group",Vh.default],["heading",Kh.default],["img",Yh.default],["insertion",Jh.default],["link",Xh.default],["list",Qh.default],["listbox",Zh.default],["listitem",eg.default],["log",rg.default],["main",tg.default],["marquee",ag.default],["math",ng.default],["menu",og.default],["menubar",lg.default],["menuitem",ig.default],["menuitemcheckbox",ug.default],["menuitemradio",sg.default],["meter",cg.default],["navigation",dg.default],["none",fg.default],["note",pg.default],["option",mg.default],["paragraph",vg.default],["presentation",yg.default],["progressbar",bg.default],["radio",hg.default],["radiogroup",gg.default],["region",Rg.default],["row",Pg.default],["rowgroup",_g.default],["rowheader",Eg.default],["scrollbar",Cg.default],["search",wg.default],["searchbox",qg.default],["separator",$g.default],["slider",Sg.default],["spinbutton",Og.default],["status",xg.default],["strong",Tg.default],["subscript",Ag.default],["superscript",Mg.default],["switch",Ig.default],["tab",Bg.default],["table",Ng.default],["tablist",Fg.default],["tabpanel",jg.default],["term",Dg.default],["textbox",kg.default],["time",Lg.default],["timer",Ug.default],["toolbar",Hg.default],["tooltip",Wg.default],["tree",zg.default],["treegrid",Gg.default],["treeitem",Vg.default]],Yg=Kg;It.default=Yg;var cn={},dn={};Object.defineProperty(dn,"__esModule",{value:!0});dn.default=void 0;var Jg={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},Xg=Jg;dn.default=Xg;var fn={};Object.defineProperty(fn,"__esModule",{value:!0});fn.default=void 0;var Qg={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},Zg=Qg;fn.default=Zg;var pn={};Object.defineProperty(pn,"__esModule",{value:!0});pn.default=void 0;var eR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},rR=eR;pn.default=rR;var mn={};Object.defineProperty(mn,"__esModule",{value:!0});mn.default=void 0;var tR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},aR=tR;mn.default=aR;var vn={};Object.defineProperty(vn,"__esModule",{value:!0});vn.default=void 0;var nR={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]},oR=nR;vn.default=oR;var yn={};Object.defineProperty(yn,"__esModule",{value:!0});yn.default=void 0;var lR={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]},iR=lR;yn.default=iR;var bn={};Object.defineProperty(bn,"__esModule",{value:!0});bn.default=void 0;var uR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},sR=uR;bn.default=sR;var hn={};Object.defineProperty(hn,"__esModule",{value:!0});hn.default=void 0;var cR={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]},dR=cR;hn.default=dR;var gn={};Object.defineProperty(gn,"__esModule",{value:!0});gn.default=void 0;var fR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},pR=fR;gn.default=pR;var Rn={};Object.defineProperty(Rn,"__esModule",{value:!0});Rn.default=void 0;var mR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},vR=mR;Rn.default=vR;var Pn={};Object.defineProperty(Pn,"__esModule",{value:!0});Pn.default=void 0;var yR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},bR=yR;Pn.default=bR;var _n={};Object.defineProperty(_n,"__esModule",{value:!0});_n.default=void 0;var hR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]},gR=hR;_n.default=gR;var En={};Object.defineProperty(En,"__esModule",{value:!0});En.default=void 0;var RR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},PR=RR;En.default=PR;var Cn={};Object.defineProperty(Cn,"__esModule",{value:!0});Cn.default=void 0;var _R={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},ER=_R;Cn.default=ER;var wn={};Object.defineProperty(wn,"__esModule",{value:!0});wn.default=void 0;var CR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},wR=CR;wn.default=wR;var qn={};Object.defineProperty(qn,"__esModule",{value:!0});qn.default=void 0;var qR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]},$R=qR;qn.default=$R;var $n={};Object.defineProperty($n,"__esModule",{value:!0});$n.default=void 0;var SR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},OR=SR;$n.default=OR;var Sn={};Object.defineProperty(Sn,"__esModule",{value:!0});Sn.default=void 0;var xR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},TR=xR;Sn.default=TR;var On={};Object.defineProperty(On,"__esModule",{value:!0});On.default=void 0;var AR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},MR=AR;On.default=MR;var xn={};Object.defineProperty(xn,"__esModule",{value:!0});xn.default=void 0;var IR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},BR=IR;xn.default=BR;var Tn={};Object.defineProperty(Tn,"__esModule",{value:!0});Tn.default=void 0;var NR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},FR=NR;Tn.default=FR;var An={};Object.defineProperty(An,"__esModule",{value:!0});An.default=void 0;var jR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},DR=jR;An.default=DR;var Mn={};Object.defineProperty(Mn,"__esModule",{value:!0});Mn.default=void 0;var kR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},LR=kR;Mn.default=LR;var In={};Object.defineProperty(In,"__esModule",{value:!0});In.default=void 0;var UR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},HR=UR;In.default=HR;var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0});Bn.default=void 0;var WR={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]},zR=WR;Bn.default=zR;var Nn={};Object.defineProperty(Nn,"__esModule",{value:!0});Nn.default=void 0;var GR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]},VR=GR;Nn.default=VR;var Fn={};Object.defineProperty(Fn,"__esModule",{value:!0});Fn.default=void 0;var KR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},YR=KR;Fn.default=YR;var jn={};Object.defineProperty(jn,"__esModule",{value:!0});jn.default=void 0;var JR={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]},XR=JR;jn.default=XR;var Dn={};Object.defineProperty(Dn,"__esModule",{value:!0});Dn.default=void 0;var QR={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]},ZR=QR;Dn.default=ZR;var kn={};Object.defineProperty(kn,"__esModule",{value:!0});kn.default=void 0;var eP={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]},rP=eP;kn.default=rP;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0});Ln.default=void 0;var tP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]},aP=tP;Ln.default=aP;var Un={};Object.defineProperty(Un,"__esModule",{value:!0});Un.default=void 0;var nP={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},oP=nP;Un.default=oP;var Hn={};Object.defineProperty(Hn,"__esModule",{value:!0});Hn.default=void 0;var lP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},iP=lP;Hn.default=iP;var Wn={};Object.defineProperty(Wn,"__esModule",{value:!0});Wn.default=void 0;var uP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]},sP=uP;Wn.default=sP;var zn={};Object.defineProperty(zn,"__esModule",{value:!0});zn.default=void 0;var cP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]},dP=cP;zn.default=dP;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0});Gn.default=void 0;var fP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]},pP=fP;Gn.default=pP;var Vn={};Object.defineProperty(Vn,"__esModule",{value:!0});Vn.default=void 0;var mP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]},vP=mP;Vn.default=vP;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0});Kn.default=void 0;var yP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]},bP=yP;Kn.default=bP;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0});Yn.default=void 0;var hP={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]},gP=hP;Yn.default=gP;Object.defineProperty(cn,"__esModule",{value:!0});cn.default=void 0;var RP=R(dn),PP=R(fn),_P=R(pn),EP=R(mn),CP=R(vn),wP=R(yn),qP=R(bn),$P=R(hn),SP=R(gn),OP=R(Rn),xP=R(Pn),TP=R(_n),AP=R(En),MP=R(Cn),IP=R(wn),BP=R(qn),NP=R($n),FP=R(Sn),jP=R(On),DP=R(xn),kP=R(Tn),LP=R(An),UP=R(Mn),HP=R(In),WP=R(Bn),zP=R(Nn),GP=R(Fn),VP=R(jn),KP=R(Dn),YP=R(kn),JP=R(Ln),XP=R(Un),QP=R(Hn),ZP=R(Wn),e_=R(zn),r_=R(Gn),t_=R(Vn),a_=R(Kn),n_=R(Yn);function R(e){return e&&e.__esModule?e:{default:e}}var o_=[["doc-abstract",RP.default],["doc-acknowledgments",PP.default],["doc-afterword",_P.default],["doc-appendix",EP.default],["doc-backlink",CP.default],["doc-biblioentry",wP.default],["doc-bibliography",qP.default],["doc-biblioref",$P.default],["doc-chapter",SP.default],["doc-colophon",OP.default],["doc-conclusion",xP.default],["doc-cover",TP.default],["doc-credit",AP.default],["doc-credits",MP.default],["doc-dedication",IP.default],["doc-endnote",BP.default],["doc-endnotes",NP.default],["doc-epigraph",FP.default],["doc-epilogue",jP.default],["doc-errata",DP.default],["doc-example",kP.default],["doc-footnote",LP.default],["doc-foreword",UP.default],["doc-glossary",HP.default],["doc-glossref",WP.default],["doc-index",zP.default],["doc-introduction",GP.default],["doc-noteref",VP.default],["doc-notice",KP.default],["doc-pagebreak",YP.default],["doc-pagelist",JP.default],["doc-part",XP.default],["doc-preface",QP.default],["doc-prologue",ZP.default],["doc-pullquote",e_.default],["doc-qna",r_.default],["doc-subtitle",t_.default],["doc-tip",a_.default],["doc-toc",n_.default]],l_=o_;cn.default=l_;var Jn={},Xn={};Object.defineProperty(Xn,"__esModule",{value:!0});Xn.default=void 0;var i_={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]},u_=i_;Xn.default=u_;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0});Qn.default=void 0;var s_={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]},c_=s_;Qn.default=c_;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0});Zn.default=void 0;var d_={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]},f_=d_;Zn.default=f_;Object.defineProperty(Jn,"__esModule",{value:!0});Jn.default=void 0;var p_=Ul(Xn),m_=Ul(Qn),v_=Ul(Zn);function Ul(e){return e&&e.__esModule?e:{default:e}}var y_=[["graphics-document",p_.default],["graphics-object",m_.default],["graphics-symbol",v_.default]],b_=y_;Jn.default=b_;Object.defineProperty(Ke,"__esModule",{value:!0});Ke.default=void 0;var h_=_r(Pt),g_=_r(It),R_=_r(cn),P_=_r(Jn),__=_r(Ce);function _r(e){return e&&e.__esModule?e:{default:e}}function E_(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function nl(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=Ec(e))||r&&e&&typeof e.length=="number"){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(s){throw s},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o=!0,l=!1,u;return{s:function(){t=t.call(e)},n:function(){var s=t.next();return o=s.done,s},e:function(s){l=!0,u=s},f:function(){try{!o&&t.return!=null&&t.return()}finally{if(l)throw u}}}}function rr(e,r){return q_(e)||w_(e,r)||Ec(e,r)||C_()}function C_(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ec(e,r){if(e){if(typeof e=="string")return Ni(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ni(e,r)}}function Ni(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}function w_(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],n=!0,o=!1,l,u;try{for(t=t.call(e);!(n=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));n=!0);}catch(i){o=!0,u=i}finally{try{!n&&t.return!=null&&t.return()}finally{if(o)throw u}}return a}}function q_(e){if(Array.isArray(e))return e}var oe=[].concat(h_.default,g_.default,R_.default,P_.default);oe.forEach(function(e){var r=rr(e,2),t=r[1],a=nl(t.superClass),n;try{for(a.s();!(n=a.n()).done;){var o=n.value,l=nl(o),u;try{var i=function(){var d=u.value,c=oe.find(function(g){var b=rr(g,1),_=b[0];return _===d});if(c)for(var f=c[1],m=0,y=Object.keys(f.props);m<y.length;m++){var v=y[m];Object.prototype.hasOwnProperty.call(t.props,v)||Object.assign(t.props,E_({},v,f.props[v]))}};for(l.s();!(u=l.n()).done;)i()}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}});var ol={entries:function(){return oe},forEach:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=nl(oe),n;try{for(a.s();!(n=a.n()).done;){var o=rr(n.value,2),l=o[0],u=o[1];r.call(t,u,l,oe)}}catch(i){a.e(i)}finally{a.f()}},get:function(r){var t=oe.find(function(a){return a[0]===r});return t&&t[1]},has:function(r){return!!ol.get(r)},keys:function(){return oe.map(function(r){var t=rr(r,1),a=t[0];return a})},values:function(){return oe.map(function(r){var t=rr(r,2),a=t[1];return a})}},$_=(0,__.default)(ol,ol.entries());Ke.default=$_;var eo={},Fi=Object.prototype.toString,Cc=function(r){var t=Fi.call(r),a=t==="[object Arguments]";return a||(a=t!=="[object Array]"&&r!==null&&typeof r=="object"&&typeof r.length=="number"&&r.length>=0&&Fi.call(r.callee)==="[object Function]"),a},go,ji;function S_(){if(ji)return go;ji=1;var e;if(!Object.keys){var r=Object.prototype.hasOwnProperty,t=Object.prototype.toString,a=Cc,n=Object.prototype.propertyIsEnumerable,o=!n.call({toString:null},"toString"),l=n.call(function(){},"prototype"),u=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],i=function(f){var m=f.constructor;return m&&m.prototype===f},s={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},d=function(){if(typeof window>"u")return!1;for(var f in window)try{if(!s["$"+f]&&r.call(window,f)&&window[f]!==null&&typeof window[f]=="object")try{i(window[f])}catch{return!0}}catch{return!0}return!1}(),c=function(f){if(typeof window>"u"||!d)return i(f);try{return i(f)}catch{return!1}};e=function(m){var y=m!==null&&typeof m=="object",v=t.call(m)==="[object Function]",g=a(m),b=y&&t.call(m)==="[object String]",_=[];if(!y&&!v&&!g)throw new TypeError("Object.keys called on a non-object");var x=l&&v;if(b&&m.length>0&&!r.call(m,0))for(var S=0;S<m.length;++S)_.push(String(S));if(g&&m.length>0)for(var O=0;O<m.length;++O)_.push(String(O));else for(var h in m)!(x&&h==="prototype")&&r.call(m,h)&&_.push(String(h));if(o)for(var C=c(m),T=0;T<u.length;++T)!(C&&u[T]==="constructor")&&r.call(m,u[T])&&_.push(u[T]);return _}}return go=e,go}var O_=Array.prototype.slice,x_=Cc,Di=Object.keys,jr=Di?function(r){return Di(r)}:S_(),ki=Object.keys;jr.shim=function(){if(Object.keys){var r=function(){var t=Object.keys(arguments);return t&&t.length===arguments.length}(1,2);r||(Object.keys=function(a){return x_(a)?ki(O_.call(a)):ki(a)})}else Object.keys=jr;return Object.keys||jr};var Hl=jr,Dr=Object.defineProperty||!1;if(Dr)try{Dr({},"a",{value:1})}catch{Dr=!1}var ro=Dr,Wl=SyntaxError,k=TypeError,T_=Object.getOwnPropertyDescriptor,kr=T_;if(kr)try{kr([],"length")}catch{kr=null}var we=kr,Li=ro,A_=Wl,Ne=k,Ui=we,zl=function(r,t,a){if(!r||typeof r!="object"&&typeof r!="function")throw new Ne("`obj` must be an object or a function`");if(typeof t!="string"&&typeof t!="symbol")throw new Ne("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new Ne("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new Ne("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new Ne("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new Ne("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,l=arguments.length>5?arguments[5]:null,u=arguments.length>6?arguments[6]:!1,i=!!Ui&&Ui(r,t);if(Li)Li(r,t,{configurable:l===null&&i?i.configurable:!l,enumerable:n===null&&i?i.enumerable:!n,value:a,writable:o===null&&i?i.writable:!o});else if(u||!n&&!o&&!l)r[t]=a;else throw new A_("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},ll=ro,wc=function(){return!!ll};wc.hasArrayLengthDefineBug=function(){if(!ll)return null;try{return ll([],"length",{value:1}).length!==1}catch{return!0}};var Gl=wc,M_=Hl,I_=typeof Symbol=="function"&&typeof Symbol("foo")=="symbol",B_=Object.prototype.toString,N_=Array.prototype.concat,Hi=zl,F_=function(e){return typeof e=="function"&&B_.call(e)==="[object Function]"},qc=Gl(),j_=function(e,r,t,a){if(r in e){if(a===!0){if(e[r]===t)return}else if(!F_(a)||!a())return}qc?Hi(e,r,t,!0):Hi(e,r,t)},$c=function(e,r){var t=arguments.length>2?arguments[2]:{},a=M_(r);I_&&(a=N_.call(a,Object.getOwnPropertySymbols(r)));for(var n=0;n<a.length;n+=1)j_(e,a[n],r[a[n]],t[a[n]])};$c.supportsDescriptors=!!qc;var qe=$c,Sc={exports:{}},Vl=Object,Oc=Error,D_=EvalError,k_=RangeError,L_=ReferenceError,U_=URIError,H_=Math.abs,W_=Math.floor,z_=Math.max,G_=Math.min,V_=Math.pow,K_=Math.round,Y_=Number.isNaN||function(r){return r!==r},J_=Y_,X_=function(r){return J_(r)||r===0?r:r<0?-1:1},to=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var r={},t=Symbol("test"),a=Object(t);if(typeof t=="string"||Object.prototype.toString.call(t)!=="[object Symbol]"||Object.prototype.toString.call(a)!=="[object Symbol]")return!1;var n=42;r[t]=n;for(var o in r)return!1;if(typeof Object.keys=="function"&&Object.keys(r).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(r).length!==0)return!1;var l=Object.getOwnPropertySymbols(r);if(l.length!==1||l[0]!==t||!Object.prototype.propertyIsEnumerable.call(r,t))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var u=Object.getOwnPropertyDescriptor(r,t);if(u.value!==n||u.enumerable!==!0)return!1}return!0},Wi=typeof Symbol<"u"&&Symbol,Q_=to,Kl=function(){return typeof Wi!="function"||typeof Symbol!="function"||typeof Wi("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:Q_()},Ro,zi;function xc(){return zi||(zi=1,Ro=typeof Reflect<"u"&&Reflect.getPrototypeOf||null),Ro}var Po,Gi;function Tc(){if(Gi)return Po;Gi=1;var e=Vl;return Po=e.getPrototypeOf||null,Po}var _o,Vi;function Z_(){if(Vi)return _o;Vi=1;var e="Function.prototype.bind called on incompatible ",r=Object.prototype.toString,t=Math.max,a="[object Function]",n=function(i,s){for(var d=[],c=0;c<i.length;c+=1)d[c]=i[c];for(var f=0;f<s.length;f+=1)d[f+i.length]=s[f];return d},o=function(i,s){for(var d=[],c=s||0,f=0;c<i.length;c+=1,f+=1)d[f]=i[c];return d},l=function(u,i){for(var s="",d=0;d<u.length;d+=1)s+=u[d],d+1<u.length&&(s+=i);return s};return _o=function(i){var s=this;if(typeof s!="function"||r.apply(s)!==a)throw new TypeError(e+s);for(var d=o(arguments,1),c,f=function(){if(this instanceof c){var b=s.apply(this,n(d,arguments));return Object(b)===b?b:this}return s.apply(i,n(d,arguments))},m=t(0,s.length-d.length),y=[],v=0;v<m;v++)y[v]="$"+v;if(c=Function("binder","return function ("+l(y,",")+"){ return binder.apply(this,arguments); }")(f),s.prototype){var g=function(){};g.prototype=s.prototype,c.prototype=new g,g.prototype=null}return c},_o}var Eo,Ki;function Er(){if(Ki)return Eo;Ki=1;var e=Z_();return Eo=Function.prototype.bind||e,Eo}var Co,Yi;function Yl(){return Yi||(Yi=1,Co=Function.prototype.call),Co}var wo,Ji;function Jl(){return Ji||(Ji=1,wo=Function.prototype.apply),wo}var eE=typeof Reflect<"u"&&Reflect&&Reflect.apply,rE=Er(),tE=Jl(),aE=Yl(),nE=eE,Ac=nE||rE.call(aE,tE),oE=Er(),lE=k,iE=Yl(),uE=Ac,Xl=function(r){if(r.length<1||typeof r[0]!="function")throw new lE("a function is required");return uE(oE,iE,r)},sE=Xl,Xi=we,Mc;try{Mc=[].__proto__===Array.prototype}catch(e){if(!e||typeof e!="object"||!("code"in e)||e.code!=="ERR_PROTO_ACCESS")throw e}var qo=!!Mc&&Xi&&Xi(Object.prototype,"__proto__"),Ic=Object,Qi=Ic.getPrototypeOf,cE=qo&&typeof qo.get=="function"?sE([qo.get]):typeof Qi=="function"?function(r){return Qi(r==null?r:Ic(r))}:!1,Zi=xc(),eu=Tc(),ru=cE,Ql=Zi?function(r){return Zi(r)}:eu?function(r){if(!r||typeof r!="object"&&typeof r!="function")throw new TypeError("getProto: not an object");return eu(r)}:ru?function(r){return ru(r)}:null,dE=Function.prototype.call,fE=Object.prototype.hasOwnProperty,pE=Er(),Zl=pE.call(dE,fE),P,mE=Vl,vE=Oc,yE=D_,bE=k_,hE=L_,ze=Wl,Ue=k,gE=U_,RE=H_,PE=W_,_E=z_,EE=G_,CE=V_,wE=K_,qE=X_,Bc=Function,$o=function(e){try{return Bc('"use strict"; return ('+e+").constructor;")()}catch{}},sr=we,$E=ro,So=function(){throw new Ue},SE=sr?function(){try{return arguments.callee,So}catch{try{return sr(arguments,"callee").get}catch{return So}}}():So,Fe=Kl(),A=Ql,OE=Tc(),xE=xc(),Nc=Jl(),Cr=Yl(),De={},TE=typeof Uint8Array>"u"||!A?P:A(Uint8Array),Pe={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?P:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?P:ArrayBuffer,"%ArrayIteratorPrototype%":Fe&&A?A([][Symbol.iterator]()):P,"%AsyncFromSyncIteratorPrototype%":P,"%AsyncFunction%":De,"%AsyncGenerator%":De,"%AsyncGeneratorFunction%":De,"%AsyncIteratorPrototype%":De,"%Atomics%":typeof Atomics>"u"?P:Atomics,"%BigInt%":typeof BigInt>"u"?P:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?P:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?P:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?P:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":vE,"%eval%":eval,"%EvalError%":yE,"%Float16Array%":typeof Float16Array>"u"?P:Float16Array,"%Float32Array%":typeof Float32Array>"u"?P:Float32Array,"%Float64Array%":typeof Float64Array>"u"?P:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?P:FinalizationRegistry,"%Function%":Bc,"%GeneratorFunction%":De,"%Int8Array%":typeof Int8Array>"u"?P:Int8Array,"%Int16Array%":typeof Int16Array>"u"?P:Int16Array,"%Int32Array%":typeof Int32Array>"u"?P:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Fe&&A?A(A([][Symbol.iterator]())):P,"%JSON%":typeof JSON=="object"?JSON:P,"%Map%":typeof Map>"u"?P:Map,"%MapIteratorPrototype%":typeof Map>"u"||!Fe||!A?P:A(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":mE,"%Object.getOwnPropertyDescriptor%":sr,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?P:Promise,"%Proxy%":typeof Proxy>"u"?P:Proxy,"%RangeError%":bE,"%ReferenceError%":hE,"%Reflect%":typeof Reflect>"u"?P:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?P:Set,"%SetIteratorPrototype%":typeof Set>"u"||!Fe||!A?P:A(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?P:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Fe&&A?A(""[Symbol.iterator]()):P,"%Symbol%":Fe?Symbol:P,"%SyntaxError%":ze,"%ThrowTypeError%":SE,"%TypedArray%":TE,"%TypeError%":Ue,"%Uint8Array%":typeof Uint8Array>"u"?P:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?P:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?P:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?P:Uint32Array,"%URIError%":gE,"%WeakMap%":typeof WeakMap>"u"?P:WeakMap,"%WeakRef%":typeof WeakRef>"u"?P:WeakRef,"%WeakSet%":typeof WeakSet>"u"?P:WeakSet,"%Function.prototype.call%":Cr,"%Function.prototype.apply%":Nc,"%Object.defineProperty%":$E,"%Object.getPrototypeOf%":OE,"%Math.abs%":RE,"%Math.floor%":PE,"%Math.max%":_E,"%Math.min%":EE,"%Math.pow%":CE,"%Math.round%":wE,"%Math.sign%":qE,"%Reflect.getPrototypeOf%":xE};if(A)try{null.error}catch(e){var AE=A(A(e));Pe["%Error.prototype%"]=AE}var ME=function e(r){var t;if(r==="%AsyncFunction%")t=$o("async function () {}");else if(r==="%GeneratorFunction%")t=$o("function* () {}");else if(r==="%AsyncGeneratorFunction%")t=$o("async function* () {}");else if(r==="%AsyncGenerator%"){var a=e("%AsyncGeneratorFunction%");a&&(t=a.prototype)}else if(r==="%AsyncIteratorPrototype%"){var n=e("%AsyncGenerator%");n&&A&&(t=A(n.prototype))}return Pe[r]=t,t},tu={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},wr=Er(),Xr=Zl,IE=wr.call(Cr,Array.prototype.concat),BE=wr.call(Nc,Array.prototype.splice),au=wr.call(Cr,String.prototype.replace),Qr=wr.call(Cr,String.prototype.slice),NE=wr.call(Cr,RegExp.prototype.exec),FE=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,jE=/\\(\\)?/g,DE=function(r){var t=Qr(r,0,1),a=Qr(r,-1);if(t==="%"&&a!=="%")throw new ze("invalid intrinsic syntax, expected closing `%`");if(a==="%"&&t!=="%")throw new ze("invalid intrinsic syntax, expected opening `%`");var n=[];return au(r,FE,function(o,l,u,i){n[n.length]=u?au(i,jE,"$1"):l||o}),n},kE=function(r,t){var a=r,n;if(Xr(tu,a)&&(n=tu[a],a="%"+n[0]+"%"),Xr(Pe,a)){var o=Pe[a];if(o===De&&(o=ME(a)),typeof o>"u"&&!t)throw new Ue("intrinsic "+r+" exists, but is not available. Please file an issue!");return{alias:n,name:a,value:o}}throw new ze("intrinsic "+r+" does not exist!")},Z=function(r,t){if(typeof r!="string"||r.length===0)throw new Ue("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof t!="boolean")throw new Ue('"allowMissing" argument must be a boolean');if(NE(/^%?[^%]*%?$/,r)===null)throw new ze("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var a=DE(r),n=a.length>0?a[0]:"",o=kE("%"+n+"%",t),l=o.name,u=o.value,i=!1,s=o.alias;s&&(n=s[0],BE(a,IE([0,1],s)));for(var d=1,c=!0;d<a.length;d+=1){var f=a[d],m=Qr(f,0,1),y=Qr(f,-1);if((m==='"'||m==="'"||m==="`"||y==='"'||y==="'"||y==="`")&&m!==y)throw new ze("property names with quotes must have matching quotes");if((f==="constructor"||!c)&&(i=!0),n+="."+f,l="%"+n+"%",Xr(Pe,l))u=Pe[l];else if(u!=null){if(!(f in u)){if(!t)throw new Ue("base intrinsic for "+r+" exists, but the property is not available.");return}if(sr&&d+1>=a.length){var v=sr(u,f);c=!!v,c&&"get"in v&&!("originalValue"in v.get)?u=v.get:u=u[f]}else c=Xr(u,f),u=u[f];c&&!i&&(Pe[l]=u)}}return u},LE=Z,nu=zl,UE=Gl(),ou=we,lu=k,HE=LE("%Math.floor%"),WE=function(r,t){if(typeof r!="function")throw new lu("`fn` is not a function");if(typeof t!="number"||t<0||t>4294967295||HE(t)!==t)throw new lu("`length` must be a positive 32-bit integer");var a=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in r&&ou){var l=ou(r,"length");l&&!l.configurable&&(n=!1),l&&!l.writable&&(o=!1)}return(n||o||!a)&&(UE?nu(r,"length",t,!0,!0):nu(r,"length",t)),r},zE=Er(),GE=Jl(),VE=Ac,KE=function(){return VE(zE,GE,arguments)};(function(e){var r=WE,t=ro,a=Xl,n=KE;e.exports=function(l){var u=a(arguments),i=l.length-(arguments.length-1);return r(u,1+(i>0?i:0),!0)},t?t(e.exports,"apply",{value:n}):e.exports.apply=n})(Sc);var Ye=Sc.exports,Fc=Z,jc=Xl,YE=jc([Fc("%String.prototype.indexOf%")]),B=function(r,t){var a=Fc(r,!!t);return typeof a=="function"&&YE(r,".prototype.")>-1?jc([a]):a},JE=Hl,Dc=to(),kc=B,Lr=Vl,XE=kc("Array.prototype.push"),iu=kc("Object.prototype.propertyIsEnumerable"),QE=Dc?Lr.getOwnPropertySymbols:null,Lc=function(r,t){if(r==null)throw new TypeError("target must be an object");var a=Lr(r);if(arguments.length===1)return a;for(var n=1;n<arguments.length;++n){var o=Lr(arguments[n]),l=JE(o),u=Dc&&(Lr.getOwnPropertySymbols||QE);if(u)for(var i=u(o),s=0;s<i.length;++s){var d=i[s];iu(o,d)&&XE(l,d)}for(var c=0;c<l.length;++c){var f=l[c];if(iu(o,f)){var m=o[f];a[f]=m}}}return a},Oo=Lc,ZE=function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",r=e.split(""),t={},a=0;a<r.length;++a)t[r[a]]=r[a];var n=Object.assign({},t),o="";for(var l in n)o+=l;return e!==o},eC=function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch{return e[1]==="y"}return!1},Uc=function(){return!Object.assign||ZE()||eC()?Oo:Object.assign},rC=qe,tC=Uc,aC=function(){var r=tC();return rC(Object,{assign:r},{assign:function(){return Object.assign!==r}}),r},nC=qe,oC=Ye,lC=Lc,Hc=Uc,iC=aC,uC=oC.apply(Hc()),Wc=function(r,t){return uC(Object,arguments)};nC(Wc,{getPolyfill:Hc,implementation:lC,shim:iC});var sC=Wc,zc=Z,Gc=Ye,cC=Gc(zc("String.prototype.indexOf")),Vc=function(r,t){var a=zc(r,!!t);return typeof a=="function"&&cC(r,".prototype.")>-1?Gc(a):a},cr=function(){return typeof(function(){}).name=="string"},tr=Object.getOwnPropertyDescriptor;if(tr)try{tr([],"length")}catch{tr=null}cr.functionsHaveConfigurableNames=function(){if(!cr()||!tr)return!1;var r=tr(function(){},"name");return!!r&&!!r.configurable};var dC=Function.prototype.bind;cr.boundFunctionsHaveNames=function(){return cr()&&typeof dC=="function"&&(function(){}).bind().name!==""};var fC=cr,uu=zl,pC=Gl(),mC=fC.functionsHaveConfigurableNames(),vC=k,yC=function(r,t){if(typeof r!="function")throw new vC("`fn` is not a function");var a=arguments.length>2&&!!arguments[2];return(!a||mC)&&(pC?uu(r,"name",t,!0,!0):uu(r,"name",t)),r},bC=yC,hC=k,gC=Object,Kc=bC(function(){if(this==null||this!==gC(this))throw new hC("RegExp.prototype.flags getter called on non-object");var r="";return this.hasIndices&&(r+="d"),this.global&&(r+="g"),this.ignoreCase&&(r+="i"),this.multiline&&(r+="m"),this.dotAll&&(r+="s"),this.unicode&&(r+="u"),this.unicodeSets&&(r+="v"),this.sticky&&(r+="y"),r},"get flags",!0),RC=Kc,PC=qe.supportsDescriptors,_C=Object.getOwnPropertyDescriptor,Yc=function(){if(PC&&/a/mig.flags==="gim"){var r=_C(RegExp.prototype,"flags");if(r&&typeof r.get=="function"&&"dotAll"in RegExp.prototype&&"hasIndices"in RegExp.prototype){var t="",a={};if(Object.defineProperty(a,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(a,"sticky",{get:function(){t+="y"}}),r.get.call(a),t==="dy")return r.get}}return RC},EC=qe.supportsDescriptors,CC=Yc,wC=we,qC=Object.defineProperty,$C=Oc,su=Ql,SC=/a/,OC=function(){if(!EC||!su)throw new $C("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var r=CC(),t=su(SC),a=wC(t,"flags");return(!a||a.get!==r)&&qC(t,"flags",{configurable:!0,enumerable:!1,get:r}),r},xC=qe,TC=Ye,AC=Kc,Jc=Yc,MC=OC,Xc=TC(Jc());xC(Xc,{getPolyfill:Jc,implementation:AC,shim:MC});var IC=Xc,Ur={exports:{}},BC=to,$e=function(){return BC()&&!!Symbol.toStringTag},NC=$e(),FC=B,il=FC("Object.prototype.toString"),ao=function(r){return NC&&r&&typeof r=="object"&&Symbol.toStringTag in r?!1:il(r)==="[object Arguments]"},Qc=function(r){return ao(r)?!0:r!==null&&typeof r=="object"&&"length"in r&&typeof r.length=="number"&&r.length>=0&&il(r)!=="[object Array]"&&"callee"in r&&il(r.callee)==="[object Function]"},jC=function(){return ao(arguments)}();ao.isLegacyArguments=Qc;var Zc=jC?ao:Qc,ei=typeof Map=="function"&&Map.prototype,xo=Object.getOwnPropertyDescriptor&&ei?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Zr=ei&&xo&&typeof xo.get=="function"?xo.get:null,cu=ei&&Map.prototype.forEach,ri=typeof Set=="function"&&Set.prototype,To=Object.getOwnPropertyDescriptor&&ri?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,et=ri&&To&&typeof To.get=="function"?To.get:null,du=ri&&Set.prototype.forEach,DC=typeof WeakMap=="function"&&WeakMap.prototype,ar=DC?WeakMap.prototype.has:null,kC=typeof WeakSet=="function"&&WeakSet.prototype,nr=kC?WeakSet.prototype.has:null,LC=typeof WeakRef=="function"&&WeakRef.prototype,fu=LC?WeakRef.prototype.deref:null,UC=Boolean.prototype.valueOf,HC=Object.prototype.toString,WC=Function.prototype.toString,zC=String.prototype.match,ti=String.prototype.slice,ue=String.prototype.replace,GC=String.prototype.toUpperCase,pu=String.prototype.toLowerCase,ed=RegExp.prototype.test,mu=Array.prototype.concat,G=Array.prototype.join,VC=Array.prototype.slice,vu=Math.floor,ul=typeof BigInt=="function"?BigInt.prototype.valueOf:null,Ao=Object.getOwnPropertySymbols,sl=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,Ge=typeof Symbol=="function"&&typeof Symbol.iterator=="object",or=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Ge||"symbol")?Symbol.toStringTag:null,rd=Object.prototype.propertyIsEnumerable,yu=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function bu(e,r){if(e===1/0||e===-1/0||e!==e||e&&e>-1e3&&e<1e3||ed.call(/e/,r))return r;var t=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof e=="number"){var a=e<0?-vu(-e):vu(e);if(a!==e){var n=String(a),o=ti.call(r,n.length+1);return ue.call(n,t,"$&_")+"."+ue.call(ue.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return ue.call(r,t,"$&_")}var cl=cf,hu=cl.custom,gu=nd(hu)?hu:null,td={__proto__:null,double:'"',single:"'"},KC={__proto__:null,double:/(["\\])/g,single:/(['\\])/g},no=function e(r,t,a,n){var o=t||{};if(Y(o,"quoteStyle")&&!Y(td,o.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Y(o,"maxStringLength")&&(typeof o.maxStringLength=="number"?o.maxStringLength<0&&o.maxStringLength!==1/0:o.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=Y(o,"customInspect")?o.customInspect:!0;if(typeof l!="boolean"&&l!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Y(o,"indent")&&o.indent!==null&&o.indent!=="	"&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Y(o,"numericSeparator")&&typeof o.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var u=o.numericSeparator;if(typeof r>"u")return"undefined";if(r===null)return"null";if(typeof r=="boolean")return r?"true":"false";if(typeof r=="string")return ld(r,o);if(typeof r=="number"){if(r===0)return 1/0/r>0?"0":"-0";var i=String(r);return u?bu(r,i):i}if(typeof r=="bigint"){var s=String(r)+"n";return u?bu(r,s):s}var d=typeof o.depth>"u"?5:o.depth;if(typeof a>"u"&&(a=0),a>=d&&d>0&&typeof r=="object")return dl(r)?"[Array]":"[Object]";var c=fw(o,a);if(typeof n>"u")n=[];else if(od(n,r)>=0)return"[Circular]";function f(F,Ae,co){if(Ae&&(n=VC.call(n),n.push(Ae)),co){var vi={depth:o.depth};return Y(o,"quoteStyle")&&(vi.quoteStyle=o.quoteStyle),e(F,vi,a+1,n)}return e(F,o,a+1,n)}if(typeof r=="function"&&!Ru(r)){var m=aw(r),y=Ir(r,f);return"[Function"+(m?": "+m:" (anonymous)")+"]"+(y.length>0?" { "+G.call(y,", ")+" }":"")}if(nd(r)){var v=Ge?ue.call(String(r),/^(Symbol\(.*\))_[^)]*$/,"$1"):sl.call(r);return typeof r=="object"&&!Ge?Qe(v):v}if(sw(r)){for(var g="<"+pu.call(String(r.nodeName)),b=r.attributes||[],_=0;_<b.length;_++)g+=" "+b[_].name+"="+ad(YC(b[_].value),"double",o);return g+=">",r.childNodes&&r.childNodes.length&&(g+="..."),g+="</"+pu.call(String(r.nodeName))+">",g}if(dl(r)){if(r.length===0)return"[]";var x=Ir(r,f);return c&&!dw(x)?"["+fl(x,c)+"]":"[ "+G.call(x,", ")+" ]"}if(XC(r)){var S=Ir(r,f);return!("cause"in Error.prototype)&&"cause"in r&&!rd.call(r,"cause")?"{ ["+String(r)+"] "+G.call(mu.call("[cause]: "+f(r.cause),S),", ")+" }":S.length===0?"["+String(r)+"]":"{ ["+String(r)+"] "+G.call(S,", ")+" }"}if(typeof r=="object"&&l){if(gu&&typeof r[gu]=="function"&&cl)return cl(r,{depth:d-a});if(l!=="symbol"&&typeof r.inspect=="function")return r.inspect()}if(nw(r)){var O=[];return cu&&cu.call(r,function(F,Ae){O.push(f(Ae,r,!0)+" => "+f(F,r))}),Pu("Map",Zr.call(r),O,c)}if(iw(r)){var h=[];return du&&du.call(r,function(F){h.push(f(F,r))}),Pu("Set",et.call(r),h,c)}if(ow(r))return Mo("WeakMap");if(uw(r))return Mo("WeakSet");if(lw(r))return Mo("WeakRef");if(ZC(r))return Qe(f(Number(r)));if(rw(r))return Qe(f(ul.call(r)));if(ew(r))return Qe(UC.call(r));if(QC(r))return Qe(f(String(r)));if(typeof window<"u"&&r===window)return"{ [object Window] }";if(typeof globalThis<"u"&&r===globalThis||typeof Kr<"u"&&r===Kr)return"{ [object globalThis] }";if(!JC(r)&&!Ru(r)){var C=Ir(r,f),T=yu?yu(r)===Object.prototype:r instanceof Object||r.constructor===Object,re=r instanceof Object?"":"null prototype",E=!T&&or&&Object(r)===r&&or in r?ti.call(he(r),8,-1):re?"Object":"",Te=T||typeof r.constructor!="function"?"":r.constructor.name?r.constructor.name+" ":"",Or=Te+(E||re?"["+G.call(mu.call([],E||[],re||[]),": ")+"] ":"");return C.length===0?Or+"{}":c?Or+"{"+fl(C,c)+"}":Or+"{ "+G.call(C,", ")+" }"}return String(r)};function ad(e,r,t){var a=t.quoteStyle||r,n=td[a];return n+e+n}function YC(e){return ue.call(String(e),/"/g,"&quot;")}function Se(e){return!or||!(typeof e=="object"&&(or in e||typeof e[or]<"u"))}function dl(e){return he(e)==="[object Array]"&&Se(e)}function JC(e){return he(e)==="[object Date]"&&Se(e)}function Ru(e){return he(e)==="[object RegExp]"&&Se(e)}function XC(e){return he(e)==="[object Error]"&&Se(e)}function QC(e){return he(e)==="[object String]"&&Se(e)}function ZC(e){return he(e)==="[object Number]"&&Se(e)}function ew(e){return he(e)==="[object Boolean]"&&Se(e)}function nd(e){if(Ge)return e&&typeof e=="object"&&e instanceof Symbol;if(typeof e=="symbol")return!0;if(!e||typeof e!="object"||!sl)return!1;try{return sl.call(e),!0}catch{}return!1}function rw(e){if(!e||typeof e!="object"||!ul)return!1;try{return ul.call(e),!0}catch{}return!1}var tw=Object.prototype.hasOwnProperty||function(e){return e in this};function Y(e,r){return tw.call(e,r)}function he(e){return HC.call(e)}function aw(e){if(e.name)return e.name;var r=zC.call(WC.call(e),/^function\s*([\w$]+)/);return r?r[1]:null}function od(e,r){if(e.indexOf)return e.indexOf(r);for(var t=0,a=e.length;t<a;t++)if(e[t]===r)return t;return-1}function nw(e){if(!Zr||!e||typeof e!="object")return!1;try{Zr.call(e);try{et.call(e)}catch{return!0}return e instanceof Map}catch{}return!1}function ow(e){if(!ar||!e||typeof e!="object")return!1;try{ar.call(e,ar);try{nr.call(e,nr)}catch{return!0}return e instanceof WeakMap}catch{}return!1}function lw(e){if(!fu||!e||typeof e!="object")return!1;try{return fu.call(e),!0}catch{}return!1}function iw(e){if(!et||!e||typeof e!="object")return!1;try{et.call(e);try{Zr.call(e)}catch{return!0}return e instanceof Set}catch{}return!1}function uw(e){if(!nr||!e||typeof e!="object")return!1;try{nr.call(e,nr);try{ar.call(e,ar)}catch{return!0}return e instanceof WeakSet}catch{}return!1}function sw(e){return!e||typeof e!="object"?!1:typeof HTMLElement<"u"&&e instanceof HTMLElement?!0:typeof e.nodeName=="string"&&typeof e.getAttribute=="function"}function ld(e,r){if(e.length>r.maxStringLength){var t=e.length-r.maxStringLength,a="... "+t+" more character"+(t>1?"s":"");return ld(ti.call(e,0,r.maxStringLength),r)+a}var n=KC[r.quoteStyle||"single"];n.lastIndex=0;var o=ue.call(ue.call(e,n,"\\$1"),/[\x00-\x1f]/g,cw);return ad(o,"single",r)}function cw(e){var r=e.charCodeAt(0),t={8:"b",9:"t",10:"n",12:"f",13:"r"}[r];return t?"\\"+t:"\\x"+(r<16?"0":"")+GC.call(r.toString(16))}function Qe(e){return"Object("+e+")"}function Mo(e){return e+" { ? }"}function Pu(e,r,t,a){var n=a?fl(t,a):G.call(t,", ");return e+" ("+r+") {"+n+"}"}function dw(e){for(var r=0;r<e.length;r++)if(od(e[r],`
`)>=0)return!1;return!0}function fw(e,r){var t;if(e.indent==="	")t="	";else if(typeof e.indent=="number"&&e.indent>0)t=G.call(Array(e.indent+1)," ");else return null;return{base:t,prev:G.call(Array(r+1),t)}}function fl(e,r){if(e.length===0)return"";var t=`
`+r.prev+r.base;return t+G.call(e,","+t)+`
`+r.prev}function Ir(e,r){var t=dl(e),a=[];if(t){a.length=e.length;for(var n=0;n<e.length;n++)a[n]=Y(e,n)?r(e[n],e):""}var o=typeof Ao=="function"?Ao(e):[],l;if(Ge){l={};for(var u=0;u<o.length;u++)l["$"+o[u]]=o[u]}for(var i in e)Y(e,i)&&(t&&String(Number(i))===i&&i<e.length||Ge&&l["$"+i]instanceof Symbol||(ed.call(/[^\w$]/,i)?a.push(r(i,e)+": "+r(e[i],e)):a.push(i+": "+r(e[i],e))));if(typeof Ao=="function")for(var s=0;s<o.length;s++)rd.call(e,o[s])&&a.push("["+r(o[s])+"]: "+r(e[o[s]],e));return a}var pw=no,mw=k,oo=function(e,r,t){for(var a=e,n;(n=a.next)!=null;a=n)if(n.key===r)return a.next=n.next,t||(n.next=e.next,e.next=n),n},vw=function(e,r){if(e){var t=oo(e,r);return t&&t.value}},yw=function(e,r,t){var a=oo(e,r);a?a.value=t:e.next={key:r,next:e.next,value:t}},bw=function(e,r){return e?!!oo(e,r):!1},hw=function(e,r){if(e)return oo(e,r,!0)},gw=function(){var r,t={assert:function(a){if(!t.has(a))throw new mw("Side channel does not contain "+pw(a))},delete:function(a){var n=r&&r.next,o=hw(r,a);return o&&n&&n===o&&(r=void 0),!!o},get:function(a){return vw(r,a)},has:function(a){return bw(r,a)},set:function(a,n){r||(r={next:void 0}),yw(r,a,n)}};return t},Rw=Z,qr=B,Pw=no,_w=k,_u=Rw("%Map%",!0),Ew=qr("Map.prototype.get",!0),Cw=qr("Map.prototype.set",!0),ww=qr("Map.prototype.has",!0),qw=qr("Map.prototype.delete",!0),$w=qr("Map.prototype.size",!0),id=!!_u&&function(){var r,t={assert:function(a){if(!t.has(a))throw new _w("Side channel does not contain "+Pw(a))},delete:function(a){if(r){var n=qw(r,a);return $w(r)===0&&(r=void 0),n}return!1},get:function(a){if(r)return Ew(r,a)},has:function(a){return r?ww(r,a):!1},set:function(a,n){r||(r=new _u),Cw(r,a,n)}};return t},Sw=Z,lo=B,Ow=no,Br=id,xw=k,je=Sw("%WeakMap%",!0),Tw=lo("WeakMap.prototype.get",!0),Aw=lo("WeakMap.prototype.set",!0),Mw=lo("WeakMap.prototype.has",!0),Iw=lo("WeakMap.prototype.delete",!0),Bw=je?function(){var r,t,a={assert:function(n){if(!a.has(n))throw new xw("Side channel does not contain "+Ow(n))},delete:function(n){if(je&&n&&(typeof n=="object"||typeof n=="function")){if(r)return Iw(r,n)}else if(Br&&t)return t.delete(n);return!1},get:function(n){return je&&n&&(typeof n=="object"||typeof n=="function")&&r?Tw(r,n):t&&t.get(n)},has:function(n){return je&&n&&(typeof n=="object"||typeof n=="function")&&r?Mw(r,n):!!t&&t.has(n)},set:function(n,o){je&&n&&(typeof n=="object"||typeof n=="function")?(r||(r=new je),Aw(r,n,o)):Br&&(t||(t=Br()),t.set(n,o))}};return a}:Br,Nw=k,Fw=no,jw=gw,Dw=id,kw=Bw,Lw=kw||Dw||jw,ud=function(){var r,t={assert:function(a){if(!t.has(a))throw new Nw("Side channel does not contain "+Fw(a))},delete:function(a){return!!r&&r.delete(a)},get:function(a){return r&&r.get(a)},has:function(a){return!!r&&r.has(a)},set:function(a,n){r||(r=Lw()),r.set(a,n)}};return t},Uw=Zl,Ze=ud(),K=k,ai={assert:function(e,r){if(!e||typeof e!="object"&&typeof e!="function")throw new K("`O` is not an object");if(typeof r!="string")throw new K("`slot` must be a string");if(Ze.assert(e),!ai.has(e,r))throw new K("`"+r+"` is not present on `O`")},get:function(e,r){if(!e||typeof e!="object"&&typeof e!="function")throw new K("`O` is not an object");if(typeof r!="string")throw new K("`slot` must be a string");var t=Ze.get(e);return t&&t["$"+r]},has:function(e,r){if(!e||typeof e!="object"&&typeof e!="function")throw new K("`O` is not an object");if(typeof r!="string")throw new K("`slot` must be a string");var t=Ze.get(e);return!!t&&Uw(t,"$"+r)},set:function(e,r,t){if(!e||typeof e!="object"&&typeof e!="function")throw new K("`O` is not an object");if(typeof r!="string")throw new K("`slot` must be a string");var a=Ze.get(e);a||(a={},Ze.set(e,a)),a["$"+r]=t}};Object.freeze&&Object.freeze(ai);var Hw=ai,er=Hw,Ww=Wl,Eu=typeof StopIteration=="object"?StopIteration:null,zw=function(r){if(!Eu)throw new Ww("this environment lacks StopIteration");er.set(r,"[[Done]]",!1);var t={next:function(){var n=er.get(this,"[[Iterator]]"),o=!!er.get(n,"[[Done]]");try{return{done:o,value:o?void 0:n.next()}}catch(l){if(er.set(n,"[[Done]]",!0),l!==Eu)throw l;return{done:!0,value:void 0}}}};return er.set(t,"[[Iterator]]",r),t},Gw={}.toString,sd=Array.isArray||function(e){return Gw.call(e)=="[object Array]"},cd=B,Vw=cd("String.prototype.valueOf"),Kw=function(r){try{return Vw(r),!0}catch{return!1}},Yw=cd("Object.prototype.toString"),Jw="[object String]",Xw=$e(),dd=function(r){return typeof r=="string"?!0:!r||typeof r!="object"?!1:Xw?Kw(r):Yw(r)===Jw},ni=typeof Map=="function"&&Map.prototype?Map:null,Qw=typeof Set=="function"&&Set.prototype?Set:null,rt;ni||(rt=function(r){return!1});var fd=ni?Map.prototype.has:null,Cu=Qw?Set.prototype.has:null;!rt&&!fd&&(rt=function(r){return!1});var pd=rt||function(r){if(!r||typeof r!="object")return!1;try{if(fd.call(r),Cu)try{Cu.call(r)}catch{return!0}return r instanceof ni}catch{}return!1},Zw=typeof Map=="function"&&Map.prototype?Map:null,oi=typeof Set=="function"&&Set.prototype?Set:null,tt;oi||(tt=function(r){return!1});var wu=Zw?Map.prototype.has:null,md=oi?Set.prototype.has:null;!tt&&!md&&(tt=function(r){return!1});var vd=tt||function(r){if(!r||typeof r!="object")return!1;try{if(md.call(r),wu)try{wu.call(r)}catch{return!0}return r instanceof oi}catch{}return!1},qu=Zc,$u=zw;if(Kl()||to()){var Io=Symbol.iterator;Ur.exports=function(r){if(r!=null&&typeof r[Io]<"u")return r[Io]();if(qu(r))return Array.prototype[Io].call(r)}}else{var eq=sd,rq=dd,Su=Z,tq=Su("%Map%",!0),aq=Su("%Set%",!0),L=Vc,Ou=L("Array.prototype.push"),xu=L("String.prototype.charCodeAt"),nq=L("String.prototype.slice"),oq=function(r,t){var a=r.length;if(t+1>=a)return t+1;var n=xu(r,t);if(n<55296||n>56319)return t+1;var o=xu(r,t+1);return o<56320||o>57343?t+1:t+2},Bo=function(r){var t=0;return{next:function(){var n=t>=r.length,o;return n||(o=r[t],t+=1),{done:n,value:o}}}},Tu=function(r,t){if(eq(r)||qu(r))return Bo(r);if(rq(r)){var a=0;return{next:function(){var o=oq(r,a),l=nq(r,a,o);return a=o,{done:o>r.length,value:l}}}}if(t&&typeof r["_es6-shim iterator_"]<"u")return r["_es6-shim iterator_"]()};if(!tq&&!aq)Ur.exports=function(r){if(r!=null)return Tu(r,!0)};else{var lq=pd,iq=vd,Au=L("Map.prototype.forEach",!0),Mu=L("Set.prototype.forEach",!0);if(typeof process>"u"||!process.versions||!process.versions.node)var Iu=L("Map.prototype.iterator",!0),Bu=L("Set.prototype.iterator",!0);var Nu=L("Map.prototype.@@iterator",!0)||L("Map.prototype._es6-shim iterator_",!0),Fu=L("Set.prototype.@@iterator",!0)||L("Set.prototype._es6-shim iterator_",!0),uq=function(r){if(lq(r)){if(Iu)return $u(Iu(r));if(Nu)return Nu(r);if(Au){var t=[];return Au(r,function(n,o){Ou(t,[o,n])}),Bo(t)}}if(iq(r)){if(Bu)return $u(Bu(r));if(Fu)return Fu(r);if(Mu){var a=[];return Mu(r,function(n){Ou(a,n)}),Bo(a)}}};Ur.exports=function(r){return uq(r)||Tu(r)}}}var sq=Ur.exports,ju=function(e){return e!==e},yd=function(r,t){return r===0&&t===0?1/r===1/t:!!(r===t||ju(r)&&ju(t))},cq=yd,bd=function(){return typeof Object.is=="function"?Object.is:cq},dq=bd,fq=qe,pq=function(){var r=dq();return fq(Object,{is:r},{is:function(){return Object.is!==r}}),r},mq=qe,vq=Ye,yq=yd,hd=bd,bq=pq,gd=vq(hd(),Object);mq(gd,{getPolyfill:hd,implementation:yq,shim:bq});var hq=gd,gq=Ye,Rd=B,Rq=Z,pl=Rq("%ArrayBuffer%",!0),Hr=Rd("ArrayBuffer.prototype.byteLength",!0),Pq=Rd("Object.prototype.toString"),Du=!!pl&&!Hr&&new pl(0).slice,ku=!!Du&&gq(Du),Pd=Hr||ku?function(r){if(!r||typeof r!="object")return!1;try{return Hr?Hr(r):ku(r,0),!0}catch{return!1}}:pl?function(r){return Pq(r)==="[object ArrayBuffer]"}:function(r){return!1},_d=B,_q=_d("Date.prototype.getDay"),Eq=function(r){try{return _q(r),!0}catch{return!1}},Cq=_d("Object.prototype.toString"),wq="[object Date]",qq=$e(),$q=function(r){return typeof r!="object"||r===null?!1:qq?Eq(r):Cq(r)===wq},Lu=B,Sq=$e(),Oq=Zl,xq=we,ml;if(Sq){var Tq=Lu("RegExp.prototype.exec"),Uu={},No=function(){throw Uu},Hu={toString:No,valueOf:No};typeof Symbol.toPrimitive=="symbol"&&(Hu[Symbol.toPrimitive]=No),ml=function(r){if(!r||typeof r!="object")return!1;var t=xq(r,"lastIndex"),a=t&&Oq(t,"value");if(!a)return!1;try{Tq(r,Hu)}catch(n){return n===Uu}}}else{var Aq=Lu("Object.prototype.toString"),Mq="[object RegExp]";ml=function(r){return!r||typeof r!="object"&&typeof r!="function"?!1:Aq(r)===Mq}}var Ed=ml,Iq=B,Wu=Iq("SharedArrayBuffer.prototype.byteLength",!0),Bq=Wu?function(r){if(!r||typeof r!="object")return!1;try{return Wu(r),!0}catch{return!1}}:function(r){return!1},Cd=B,Nq=Cd("Number.prototype.toString"),Fq=function(r){try{return Nq(r),!0}catch{return!1}},jq=Cd("Object.prototype.toString"),Dq="[object Number]",kq=$e(),Lq=function(r){return typeof r=="number"?!0:!r||typeof r!="object"?!1:kq?Fq(r):jq(r)===Dq},wd=B,Uq=wd("Boolean.prototype.toString"),Hq=wd("Object.prototype.toString"),Wq=function(r){try{return Uq(r),!0}catch{return!1}},zq="[object Boolean]",Gq=$e(),Vq=function(r){return typeof r=="boolean"?!0:r===null||typeof r!="object"?!1:Gq?Wq(r):Hq(r)===zq},vl={exports:{}},Fo,zu;function Kq(){if(zu)return Fo;zu=1;var e=B,r=Ed,t=e("RegExp.prototype.exec"),a=k;return Fo=function(o){if(!r(o))throw new a("`regex` must be a RegExp");return function(u){return t(o,u)!==null}},Fo}var qd=B,Yq=qd("Object.prototype.toString"),Jq=Kl(),Xq=Kq();if(Jq){var Qq=qd("Symbol.prototype.toString"),Zq=Xq(/^Symbol\(.*\)$/),e$=function(r){return typeof r.valueOf()!="symbol"?!1:Zq(Qq(r))};vl.exports=function(r){if(typeof r=="symbol")return!0;if(!r||typeof r!="object"||Yq(r)!=="[object Symbol]")return!1;try{return e$(r)}catch{return!1}}}else vl.exports=function(r){return!1};var r$=vl.exports,yl={exports:{}},Gu=typeof BigInt<"u"&&BigInt,t$=function(){return typeof Gu=="function"&&typeof BigInt=="function"&&typeof Gu(42)=="bigint"&&typeof BigInt(42)=="bigint"},a$=t$();if(a$){var n$=BigInt.prototype.valueOf,o$=function(r){try{return n$.call(r),!0}catch{}return!1};yl.exports=function(r){return r===null||typeof r>"u"||typeof r=="boolean"||typeof r=="string"||typeof r=="number"||typeof r=="symbol"||typeof r=="function"?!1:typeof r=="bigint"?!0:o$(r)}}else yl.exports=function(r){return!1};var l$=yl.exports,i$=dd,u$=Lq,s$=Vq,c$=r$,d$=l$,f$=function(r){if(r==null||typeof r!="object"&&typeof r!="function")return null;if(i$(r))return"String";if(u$(r))return"Number";if(s$(r))return"Boolean";if(c$(r))return"Symbol";if(d$(r))return"BigInt"},at=typeof WeakMap=="function"&&WeakMap.prototype?WeakMap:null,Vu=typeof WeakSet=="function"&&WeakSet.prototype?WeakSet:null,nt;at||(nt=function(r){return!1});var bl=at?at.prototype.has:null,jo=Vu?Vu.prototype.has:null;!nt&&!bl&&(nt=function(r){return!1});var p$=nt||function(r){if(!r||typeof r!="object")return!1;try{if(bl.call(r,bl),jo)try{jo.call(r,jo)}catch{return!0}return r instanceof at}catch{}return!1},hl={exports:{}},m$=Z,$d=B,v$=m$("%WeakSet%",!0),Do=$d("WeakSet.prototype.has",!0);if(Do){var ko=$d("WeakMap.prototype.has",!0);hl.exports=function(r){if(!r||typeof r!="object")return!1;try{if(Do(r,Do),ko)try{ko(r,ko)}catch{return!0}return r instanceof v$}catch{}return!1}}else hl.exports=function(r){return!1};var y$=hl.exports,b$=pd,h$=vd,g$=p$,R$=y$,P$=function(r){if(r&&typeof r=="object"){if(b$(r))return"Map";if(h$(r))return"Set";if(g$(r))return"WeakMap";if(R$(r))return"WeakSet"}return!1},Sd=Function.prototype.toString,Le=typeof Reflect=="object"&&Reflect!==null&&Reflect.apply,gl,Wr;if(typeof Le=="function"&&typeof Object.defineProperty=="function")try{gl=Object.defineProperty({},"length",{get:function(){throw Wr}}),Wr={},Le(function(){throw 42},null,gl)}catch(e){e!==Wr&&(Le=null)}else Le=null;var _$=/^\s*class\b/,Rl=function(r){try{var t=Sd.call(r);return _$.test(t)}catch{return!1}},Lo=function(r){try{return Rl(r)?!1:(Sd.call(r),!0)}catch{return!1}},zr=Object.prototype.toString,E$="[object Object]",C$="[object Function]",w$="[object GeneratorFunction]",q$="[object HTMLAllCollection]",$$="[object HTML document.all class]",S$="[object HTMLCollection]",O$=typeof Symbol=="function"&&!!Symbol.toStringTag,x$=!(0 in[,]),Pl=function(){return!1};if(typeof document=="object"){var T$=document.all;zr.call(T$)===zr.call(document.all)&&(Pl=function(r){if((x$||!r)&&(typeof r>"u"||typeof r=="object"))try{var t=zr.call(r);return(t===q$||t===$$||t===S$||t===E$)&&r("")==null}catch{}return!1})}var A$=Le?function(r){if(Pl(r))return!0;if(!r||typeof r!="function"&&typeof r!="object")return!1;try{Le(r,null,gl)}catch(t){if(t!==Wr)return!1}return!Rl(r)&&Lo(r)}:function(r){if(Pl(r))return!0;if(!r||typeof r!="function"&&typeof r!="object")return!1;if(O$)return Lo(r);if(Rl(r))return!1;var t=zr.call(r);return t!==C$&&t!==w$&&!/^\[object HTML/.test(t)?!1:Lo(r)},M$=A$,I$=Object.prototype.toString,Od=Object.prototype.hasOwnProperty,B$=function(r,t,a){for(var n=0,o=r.length;n<o;n++)Od.call(r,n)&&(a==null?t(r[n],n,r):t.call(a,r[n],n,r))},N$=function(r,t,a){for(var n=0,o=r.length;n<o;n++)a==null?t(r.charAt(n),n,r):t.call(a,r.charAt(n),n,r)},F$=function(r,t,a){for(var n in r)Od.call(r,n)&&(a==null?t(r[n],n,r):t.call(a,r[n],n,r))};function j$(e){return I$.call(e)==="[object Array]"}var D$=function(r,t,a){if(!M$(t))throw new TypeError("iterator must be a function");var n;arguments.length>=3&&(n=a),j$(r)?B$(r,t,n):typeof r=="string"?N$(r,t,n):F$(r,t,n)},k$=["Float16Array","Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"],Uo=k$,L$=typeof globalThis>"u"?Kr:globalThis,U$=function(){for(var r=[],t=0;t<Uo.length;t++)typeof L$[Uo[t]]=="function"&&(r[r.length]=Uo[t]);return r},ot=D$,H$=U$,Ku=Ye,li=B,Gr=we,Nr=Ql,W$=li("Object.prototype.toString"),xd=$e(),Yu=typeof globalThis>"u"?Kr:globalThis,_l=H$(),ii=li("String.prototype.slice"),z$=li("Array.prototype.indexOf",!0)||function(r,t){for(var a=0;a<r.length;a+=1)if(r[a]===t)return a;return-1},lt={__proto__:null};xd&&Gr&&Nr?ot(_l,function(e){var r=new Yu[e];if(Symbol.toStringTag in r&&Nr){var t=Nr(r),a=Gr(t,Symbol.toStringTag);if(!a&&t){var n=Nr(t);a=Gr(n,Symbol.toStringTag)}lt["$"+e]=Ku(a.get)}}):ot(_l,function(e){var r=new Yu[e],t=r.slice||r.set;t&&(lt["$"+e]=Ku(t))});var G$=function(r){var t=!1;return ot(lt,function(a,n){if(!t)try{"$"+a(r)===n&&(t=ii(n,1))}catch{}}),t},V$=function(r){var t=!1;return ot(lt,function(a,n){if(!t)try{a(r),t=ii(n,1)}catch{}}),t},K$=function(r){if(!r||typeof r!="object")return!1;if(!xd){var t=ii(W$(r),8,-1);return z$(_l,t)>-1?t:t!=="Object"?!1:V$(r)}return Gr?G$(r):null},Y$=B,Ju=Y$("ArrayBuffer.prototype.byteLength",!0),J$=Pd,X$=function(r){return J$(r)?Ju?Ju(r):r.byteLength:NaN},Td=sC,V=Vc,Xu=IC,Q$=Z,Ve=sq,Z$=ud,Qu=hq,Zu=Zc,es=sd,rs=Pd,ts=$q,as=Ed,ns=Bq,os=Hl,ls=f$,is=P$,us=K$,ss=X$,cs=V("SharedArrayBuffer.prototype.byteLength",!0),ds=V("Date.prototype.getTime"),Ho=Object.getPrototypeOf,fs=V("Object.prototype.toString"),it=Q$("%Set%",!0),El=V("Map.prototype.has",!0),ut=V("Map.prototype.get",!0),ps=V("Map.prototype.size",!0),st=V("Set.prototype.add",!0),Ad=V("Set.prototype.delete",!0),ct=V("Set.prototype.has",!0),Vr=V("Set.prototype.size",!0);function ms(e,r,t,a){for(var n=Ve(e),o;(o=n.next())&&!o.done;)if(W(r,o.value,t,a))return Ad(e,o.value),!0;return!1}function Md(e){if(typeof e>"u")return null;if(typeof e!="object")return typeof e=="symbol"?!1:typeof e=="string"||typeof e=="number"?+e==+e:!0}function eS(e,r,t,a,n,o){var l=Md(t);if(l!=null)return l;var u=ut(r,l),i=Td({},n,{strict:!1});return typeof u>"u"&&!El(r,l)||!W(a,u,i,o)?!1:!El(e,l)&&W(a,u,i,o)}function rS(e,r,t){var a=Md(t);return a??(ct(r,a)&&!ct(e,a))}function vs(e,r,t,a,n,o){for(var l=Ve(e),u,i;(u=l.next())&&!u.done;)if(i=u.value,W(t,i,n,o)&&W(a,ut(r,i),n,o))return Ad(e,i),!0;return!1}function W(e,r,t,a){var n=t||{};if(n.strict?Qu(e,r):e===r)return!0;var o=ls(e),l=ls(r);if(o!==l)return!1;if(!e||!r||typeof e!="object"&&typeof r!="object")return n.strict?Qu(e,r):e==r;var u=a.has(e),i=a.has(r),s;if(u&&i){if(a.get(e)===a.get(r))return!0}else s={};return u||a.set(e,s),i||a.set(r,s),nS(e,r,n,a)}function ys(e){return!e||typeof e!="object"||typeof e.length!="number"||typeof e.copy!="function"||typeof e.slice!="function"||e.length>0&&typeof e[0]!="number"?!1:!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))}function tS(e,r,t,a){if(Vr(e)!==Vr(r))return!1;for(var n=Ve(e),o=Ve(r),l,u,i;(l=n.next())&&!l.done;)if(l.value&&typeof l.value=="object")i||(i=new it),st(i,l.value);else if(!ct(r,l.value)){if(t.strict||!rS(e,r,l.value))return!1;i||(i=new it),st(i,l.value)}if(i){for(;(u=o.next())&&!u.done;)if(u.value&&typeof u.value=="object"){if(!ms(i,u.value,t.strict,a))return!1}else if(!t.strict&&!ct(e,u.value)&&!ms(i,u.value,t.strict,a))return!1;return Vr(i)===0}return!0}function aS(e,r,t,a){if(ps(e)!==ps(r))return!1;for(var n=Ve(e),o=Ve(r),l,u,i,s,d,c;(l=n.next())&&!l.done;)if(s=l.value[0],d=l.value[1],s&&typeof s=="object")i||(i=new it),st(i,s);else if(c=ut(r,s),typeof c>"u"&&!El(r,s)||!W(d,c,t,a)){if(t.strict||!eS(e,r,s,d,t,a))return!1;i||(i=new it),st(i,s)}if(i){for(;(u=o.next())&&!u.done;)if(s=u.value[0],c=u.value[1],s&&typeof s=="object"){if(!vs(i,e,s,c,t,a))return!1}else if(!t.strict&&(!e.has(s)||!W(ut(e,s),c,t,a))&&!vs(i,e,s,c,Td({},t,{strict:!1}),a))return!1;return Vr(i)===0}return!0}function nS(e,r,t,a){var n,o;if(typeof e!=typeof r||e==null||r==null||fs(e)!==fs(r)||Zu(e)!==Zu(r))return!1;var l=es(e),u=es(r);if(l!==u)return!1;var i=e instanceof Error,s=r instanceof Error;if(i!==s||(i||s)&&(e.name!==r.name||e.message!==r.message))return!1;var d=as(e),c=as(r);if(d!==c||(d||c)&&(e.source!==r.source||Xu(e)!==Xu(r)))return!1;var f=ts(e),m=ts(r);if(f!==m||(f||m)&&ds(e)!==ds(r)||t.strict&&Ho&&Ho(e)!==Ho(r))return!1;var y=us(e),v=us(r);if(y!==v)return!1;if(y||v){if(e.length!==r.length)return!1;for(n=0;n<e.length;n++)if(e[n]!==r[n])return!1;return!0}var g=ys(e),b=ys(r);if(g!==b)return!1;if(g||b){if(e.length!==r.length)return!1;for(n=0;n<e.length;n++)if(e[n]!==r[n])return!1;return!0}var _=rs(e),x=rs(r);if(_!==x)return!1;if(_||x)return ss(e)!==ss(r)?!1:typeof Uint8Array=="function"&&W(new Uint8Array(e),new Uint8Array(r),t,a);var S=ns(e),O=ns(r);if(S!==O)return!1;if(S||O)return cs(e)!==cs(r)?!1:typeof Uint8Array=="function"&&W(new Uint8Array(e),new Uint8Array(r),t,a);if(typeof e!=typeof r)return!1;var h=os(e),C=os(r);if(h.length!==C.length)return!1;for(h.sort(),C.sort(),n=h.length-1;n>=0;n--)if(h[n]!=C[n])return!1;for(n=h.length-1;n>=0;n--)if(o=h[n],!W(e[o],r[o],t,a))return!1;var T=is(e),re=is(r);return T!==re?!1:T==="Set"||re==="Set"?tS(e,r,t,a):T==="Map"?aS(e,r,t,a):!0}var oS=function(r,t,a){return W(r,t,a,Z$())};Object.defineProperty(eo,"__esModule",{value:!0});eo.default=void 0;var lS=ui(oS),iS=ui(Ce),Id=ui(Ke);function ui(e){return e&&e.__esModule?e:{default:e}}function Wo(e,r){return cS(e)||sS(e,r)||Bd(e,r)||uS()}function uS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function sS(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],n=!0,o=!1,l,u;try{for(t=t.call(e);!(n=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));n=!0);}catch(i){o=!0,u=i}finally{try{!n&&t.return!=null&&t.return()}finally{if(o)throw u}}return a}}function cS(e){if(Array.isArray(e))return e}function dS(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=Bd(e))||r&&e&&typeof e.length=="number"){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(s){throw s},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o=!0,l=!1,u;return{s:function(){t=t.call(e)},n:function(){var s=t.next();return o=s.done,s},e:function(s){l=!0,u=s},f:function(){try{!o&&t.return!=null&&t.return()}finally{if(l)throw u}}}}function Bd(e,r){if(e){if(typeof e=="string")return bs(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return bs(e,r)}}function bs(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}var le=[],hs=Id.default.keys();for(var zo=0;zo<hs.length;zo++){var Go=hs[zo],Vo=Id.default.get(Go);if(Vo)for(var gs=[].concat(Vo.baseConcepts,Vo.relatedConcepts),Ko=0;Ko<gs.length;Ko++){var Rs=gs[Ko];if(Rs.module==="HTML"){var Yo=Rs.concept;Yo&&function(){var e=JSON.stringify(Yo),r=le.find(function(o){return JSON.stringify(o[0])===e}),t=void 0;r?t=r[1]:t=[];for(var a=!0,n=0;n<t.length;n++)if(t[n]===Go){a=!1;break}a&&t.push(Go),le.push([Yo,t])}()}}}var Cl={entries:function(){return le},forEach:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=dS(le),n;try{for(a.s();!(n=a.n()).done;){var o=Wo(n.value,2),l=o[0],u=o[1];r.call(t,u,l,le)}}catch(i){a.e(i)}finally{a.f()}},get:function(r){var t=le.find(function(a){return(0,lS.default)(r,a[0])});return t&&t[1]},has:function(r){return!!Cl.get(r)},keys:function(){return le.map(function(r){var t=Wo(r,1),a=t[0];return a})},values:function(){return le.map(function(r){var t=Wo(r,2),a=t[1];return a})}},fS=(0,iS.default)(Cl,Cl.entries());eo.default=fS;var io={};Object.defineProperty(io,"__esModule",{value:!0});io.default=void 0;var pS=Fd(Ce),Nd=Fd(Ke);function Fd(e){return e&&e.__esModule?e:{default:e}}function Jo(e,r){return yS(e)||vS(e,r)||jd(e,r)||mS()}function mS(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vS(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],n=!0,o=!1,l,u;try{for(t=t.call(e);!(n=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));n=!0);}catch(i){o=!0,u=i}finally{try{!n&&t.return!=null&&t.return()}finally{if(o)throw u}}return a}}function yS(e){if(Array.isArray(e))return e}function bS(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=jd(e))||r&&e&&typeof e.length=="number"){t&&(e=t);var a=0,n=function(){};return{s:n,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(s){throw s},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var o=!0,l=!1,u;return{s:function(){t=t.call(e)},n:function(){var s=t.next();return o=s.done,s},e:function(s){l=!0,u=s},f:function(){try{!o&&t.return!=null&&t.return()}finally{if(l)throw u}}}}function jd(e,r){if(e){if(typeof e=="string")return Ps(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Ps(e,r)}}function Ps(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}var ie=[],Dd=Nd.default.keys(),hS=function(r){var t=Dd[r],a=Nd.default.get(t);if(a)for(var n=[].concat(a.baseConcepts,a.relatedConcepts),o=0;o<n.length;o++){var l=n[o];if(l.module==="HTML"){var u=l.concept;if(u){var i=ie.find(function(d){return d[0]===t}),s=void 0;i?s=i[1]:s=[],s.push(u),ie.push([t,s])}}}};for(var Xo=0;Xo<Dd.length;Xo++)hS(Xo);var wl={entries:function(){return ie},forEach:function(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=bS(ie),n;try{for(a.s();!(n=a.n()).done;){var o=Jo(n.value,2),l=o[0],u=o[1];r.call(t,u,l,ie)}}catch(i){a.e(i)}finally{a.f()}},get:function(r){var t=ie.find(function(a){return a[0]===r});return t&&t[1]},has:function(r){return!!wl.get(r)},keys:function(){return ie.map(function(r){var t=Jo(r,1),a=t[0];return a})},values:function(){return ie.map(function(r){var t=Jo(r,2),a=t[1];return a})}},gS=(0,pS.default)(wl,wl.entries());io.default=gS;Object.defineProperty(H,"__esModule",{value:!0});var ke=H.roles=Ld=H.roleElements=kd=H.elementRoles=H.dom=H.aria=void 0,RS=$r(ht),PS=$r(Rt),_S=$r(Ke),ES=$r(eo),CS=$r(io);function $r(e){return e&&e.__esModule?e:{default:e}}var wS=RS.default;H.aria=wS;var qS=PS.default;H.dom=qS;var $S=_S.default;ke=H.roles=$S;var SS=ES.default,kd=H.elementRoles=SS,OS=CS.default,Ld=H.roleElements=OS;function Ud(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const xS=(e,r,t,a,n,o,l)=>{const u=a+t.indent,i=t.colors;return e.map(s=>{const d=r[s];let c=l(d,t,u,n,o);return typeof d!="string"&&(c.indexOf(`
`)!==-1&&(c=t.spacingOuter+u+c+t.spacingOuter+a),c="{"+c+"}"),t.spacingInner+a+i.prop.open+s+i.prop.close+"="+i.value.open+c+i.value.close}).join("")},TS=3,AS=(e,r,t,a,n,o)=>e.map(l=>{const u=typeof l=="string"?Hd(l,r):o(l,r,t,a,n);return u===""&&typeof l=="object"&&l!==null&&l.nodeType!==TS?"":r.spacingOuter+t+u}).join(""),Hd=(e,r)=>{const t=r.colors.content;return t.open+Ud(e)+t.close},MS=(e,r)=>{const t=r.colors.comment;return t.open+"<!--"+Ud(e)+"-->"+t.close},IS=(e,r,t,a,n)=>{const o=a.colors.tag;return o.open+"<"+e+(r&&o.close+r+a.spacingOuter+n+o.open)+(t?">"+o.close+t+a.spacingOuter+n+o.open+"</"+e:(r&&!a.min?"":" ")+"/")+">"+o.close},BS=(e,r)=>{const t=r.colors.tag;return t.open+"<"+e+t.close+" …"+t.open+" />"+t.close},NS=1,Wd=3,zd=8,Gd=11,FS=/^((HTML|SVG)\w*)?Element$/,jS=e=>{const r=e.constructor.name,{nodeType:t,tagName:a}=e,n=typeof a=="string"&&a.includes("-")||typeof e.hasAttribute=="function"&&e.hasAttribute("is");return t===NS&&(FS.test(r)||n)||t===Wd&&r==="Text"||t===zd&&r==="Comment"||t===Gd&&r==="DocumentFragment"};function DS(e){return e.nodeType===Wd}function kS(e){return e.nodeType===zd}function Qo(e){return e.nodeType===Gd}function LS(e){return{test:r=>{var t;return(r==null||(t=r.constructor)==null?void 0:t.name)&&jS(r)},serialize:(r,t,a,n,o,l)=>{if(DS(r))return Hd(r.data,t);if(kS(r))return MS(r.data,t);const u=Qo(r)?"DocumentFragment":r.tagName.toLowerCase();return++n>t.maxDepth?BS(u,t):IS(u,xS(Qo(r)?[]:Array.from(r.attributes).map(i=>i.name).sort(),Qo(r)?{}:Array.from(r.attributes).reduce((i,s)=>(i[s.name]=s.value,i),{}),t,a+t.indent,n,o,l),AS(Array.prototype.slice.call(r.childNodes||r.children).filter(e),t,a+t.indent,n,o,l),t,a)}}}let Vd=null,si=null,ci=null;try{const e=module&&module.require;si=e.call(module,"fs").readFileSync,ci=e.call(module,"@babel/code-frame").codeFrameColumns,Vd=e.call(module,"chalk")}catch{}function US(e){const r=e.indexOf("(")+1,t=e.indexOf(")"),a=e.slice(r,t),n=a.split(":"),[o,l,u]=[n[0],parseInt(n[1],10),parseInt(n[2],10)];let i="";try{i=si(o,"utf-8")}catch{return""}const s=ci(i,{start:{line:l,column:u}},{highlightCode:!0,linesBelow:0});return Vd.dim(a)+`
`+s+`
`}function HS(){if(!si||!ci)return"";const r=new Error().stack.split(`
`).slice(1).find(t=>!t.includes("node_modules/"));return US(r)}const Kd=3;function Zo(){return typeof jest<"u"&&jest!==null?setTimeout._isMockFunction===!0||Object.prototype.hasOwnProperty.call(setTimeout,"clock"):!1}function di(){if(typeof window>"u")throw new Error("Could not find default container");return window.document}function Yd(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&e.ownerDocument.defaultView===null?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):typeof e.debug=="function"&&typeof e.logTestingPlaygroundURL=="function"?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function ee(e){if(!e||typeof e.querySelector!="function"||typeof e.querySelectorAll!="function")throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+r(e)+".");function r(t){return typeof t=="object"?t===null?"null":t.constructor.name:typeof t}}const WS=()=>{let e;try{var r,t;e=JSON.parse((r=process)==null||(t=r.env)==null?void 0:t.COLORS)}catch{}return typeof e=="boolean"?e:typeof process<"u"&&process.versions!==void 0&&process.versions.node!==void 0},{DOMCollection:zS}=uc,GS=1,VS=8;function KS(e){return e.nodeType!==VS&&(e.nodeType!==GS||!e.matches(w().defaultIgnore))}function dt(e,r,t){if(t===void 0&&(t={}),e||(e=di().body),typeof r!="number"&&(r=typeof process<"u"&&{}.DEBUG_PRINT_LIMIT||7e3),r===0)return"";e.documentElement&&(e=e.documentElement);let a=typeof e;if(a==="object"?a=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+a);const{filterNode:n=KS,...o}=t,l=Up(e,{plugins:[LS(n),zS],printFunctionName:!1,highlight:WS(),...o});return r!==void 0&&e.outerHTML.length>r?l.slice(0,r)+"...":l}const _s=function(){const e=HS();console.log(e?dt(...arguments)+`

`+e:dt(...arguments))};let _e={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,r){const t=dt(r),a=new Error([e,"Ignored nodes: comments, "+_e.defaultIgnore+`
`+t].filter(Boolean).join(`

`));return a.name="TestingLibraryElementError",a},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function YS(e){try{return _e._disableExpensiveErrorDiagnostics=!0,e()}finally{_e._disableExpensiveErrorDiagnostics=!1}}function JS(e){typeof e=="function"&&(e=e(_e)),_e={..._e,...e}}function w(){return _e}const XS=["button","meter","output","progress","select","textarea","input"];function Jd(e){return XS.includes(e.nodeName.toLowerCase())?"":e.nodeType===Kd?e.textContent:Array.from(e.childNodes).map(r=>Jd(r)).join("")}function ql(e){let r;return e.tagName.toLowerCase()==="label"?r=Jd(e):r=e.value||e.textContent,r}function Xd(e){if(e.labels!==void 0){var r;return(r=e.labels)!=null?r:[]}if(!QS(e))return[];const t=e.ownerDocument.querySelectorAll("label");return Array.from(t).filter(a=>a.control===e)}function QS(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||e.tagName==="INPUT"&&e.getAttribute("type")!=="hidden"}function Qd(e,r,t){let{selector:a="*"}=t===void 0?{}:t;const n=r.getAttribute("aria-labelledby"),o=n?n.split(" "):[];return o.length?o.map(l=>{const u=e.querySelector('[id="'+l+'"]');return u?{content:ql(u),formControl:null}:{content:"",formControl:null}}):Array.from(Xd(r)).map(l=>{const u=ql(l),i="button, input, meter, output, progress, select, textarea",s=Array.from(l.querySelectorAll(i)).filter(d=>d.matches(a))[0];return{content:u,formControl:s}})}function Zd(e){if(e==null)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Oe(e,r,t,a){if(typeof e!="string")return!1;Zd(t);const n=a(e);return typeof t=="string"||typeof t=="number"?n.toLowerCase().includes(t.toString().toLowerCase()):typeof t=="function"?t(n,r):rf(t,n)}function X(e,r,t,a){if(typeof e!="string")return!1;Zd(t);const n=a(e);return t instanceof Function?t(n,r):t instanceof RegExp?rf(t,n):n===String(t)}function ef(e){let{trim:r=!0,collapseWhitespace:t=!0}=e===void 0?{}:e;return a=>{let n=a;return n=r?n.trim():n,n=t?n.replace(/\s+/g," "):n,n}}function ge(e){let{trim:r,collapseWhitespace:t,normalizer:a}=e;if(!a)return ef({trim:r,collapseWhitespace:t});if(typeof r<"u"||typeof t<"u")throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return a}function rf(e,r){const t=e.test(r);return e.global&&e.lastIndex!==0&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),t}function uo(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter(r=>r.nodeType===Kd&&!!r.textContent).map(r=>r.textContent).join("")}const ZS=eO(kd);function tf(e){return e.hidden===!0||e.getAttribute("aria-hidden")==="true"||e.ownerDocument.defaultView.getComputedStyle(e).display==="none"}function fi(e,r){r===void 0&&(r={});const{isSubtreeInaccessible:t=tf}=r;if(e.ownerDocument.defaultView.getComputedStyle(e).visibility==="hidden")return!0;let n=e;for(;n;){if(t(n))return!0;n=n.parentElement}return!1}function pi(e){for(const{match:r,roles:t}of ZS)if(r(e))return[...t];return[]}function eO(e){function r(l){let{name:u,attributes:i}=l;return""+u+i.map(s=>{let{name:d,value:c,constraints:f=[]}=s;return f.indexOf("undefined")!==-1?":not(["+d+"])":c?"["+d+'="'+c+'"]':"["+d+"]"}).join("")}function t(l){let{attributes:u=[]}=l;return u.length}function a(l,u){let{specificity:i}=l,{specificity:s}=u;return s-i}function n(l){let{attributes:u=[]}=l;const i=u.findIndex(d=>d.value&&d.name==="type"&&d.value==="text");i>=0&&(u=[...u.slice(0,i),...u.slice(i+1)]);const s=r({...l,attributes:u});return d=>i>=0&&d.type!=="text"?!1:d.matches(s)}let o=[];for(const[l,u]of e.entries())o=[...o,{match:n(l),roles:Array.from(u),specificity:t(l)}];return o.sort(a)}function rO(e,r){let{hidden:t=!1}=r===void 0?{}:r;function a(n){return[n,...Array.from(n.children).reduce((o,l)=>[...o,...a(l)],[])]}return a(e).filter(n=>t===!1?fi(n)===!1:!0).reduce((n,o)=>{let l=[];return o.hasAttribute("role")?l=o.getAttribute("role").split(" ").slice(0,1):l=pi(o),l.reduce((u,i)=>Array.isArray(u[i])?{...u,[i]:[...u[i],o]}:{...u,[i]:[o]},n)},{})}function tO(e,r){let{hidden:t,includeDescription:a}=r;const n=rO(e,{hidden:t});return Object.entries(n).filter(o=>{let[l]=o;return l!=="generic"}).map(o=>{let[l,u]=o;const i="-".repeat(50),s=u.map(d=>{const c='Name "'+Fl(d,{computedStyleSupportsPseudoElements:w().computedStyleSupportsPseudoElements})+`":
`,f=dt(d.cloneNode(!1));if(a){const m='Description "'+Rc(d,{computedStyleSupportsPseudoElements:w().computedStyleSupportsPseudoElements})+`":
`;return""+c+m+f}return""+c+f}).join(`

`);return l+`:

`+s+`

`+i}).join(`
`)}function aO(e){return e.tagName==="OPTION"?e.selected:Sr(e,"aria-selected")}function nO(e){if(!("indeterminate"in e&&e.indeterminate))return"checked"in e?e.checked:Sr(e,"aria-checked")}function oO(e){return Sr(e,"aria-pressed")}function lO(e){var r,t;return(r=(t=Sr(e,"aria-current"))!=null?t:e.getAttribute("aria-current"))!=null?r:!1}function iO(e){return Sr(e,"aria-expanded")}function Sr(e,r){const t=e.getAttribute(r);if(t==="true")return!0;if(t==="false")return!1}function uO(e){const r={H1:1,H2:2,H3:3,H4:4,H5:5,H6:6};return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||r[e.tagName]}const Es=ef();function sO(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}function Cs(e){return new RegExp(sO(e.toLowerCase()),"i")}function te(e,r,t,a){let{variant:n,name:o}=a,l="";const u={},i=[["Role","TestId"].includes(e)?t:Cs(t)];o&&(u.name=Cs(o)),e==="Role"&&fi(r)&&(u.hidden=!0,l=`Element is inaccessible. This means that the element and all its children are invisible to screen readers.
    If you are using the aria-hidden prop, make sure this is the right choice for your case.
    `),Object.keys(u).length>0&&i.push(u);const s=n+"By"+e;return{queryName:e,queryMethod:s,queryArgs:i,variant:n,warning:l,toString(){l&&console.warn(l);let[d,c]=i;return d=typeof d=="string"?"'"+d+"'":d,c=c?", { "+Object.entries(c).map(f=>{let[m,y]=f;return m+": "+y}).join(", ")+" }":"",s+"("+d+c+")"}}}function ae(e,r,t){return t&&(!r||r.toLowerCase()===e.toLowerCase())}function $l(e,r,t){var a,n;if(r===void 0&&(r="get"),e.matches(w().defaultIgnore))return;const o=(a=e.getAttribute("role"))!=null?a:(n=pi(e))==null?void 0:n[0];if(o!=="generic"&&ae("Role",t,o))return te("Role",e,o,{variant:r,name:Fl(e,{computedStyleSupportsPseudoElements:w().computedStyleSupportsPseudoElements})});const l=Qd(document,e).map(f=>f.content).join(" ");if(ae("LabelText",t,l))return te("LabelText",e,l,{variant:r});const u=e.getAttribute("placeholder");if(ae("PlaceholderText",t,u))return te("PlaceholderText",e,u,{variant:r});const i=Es(uo(e));if(ae("Text",t,i))return te("Text",e,i,{variant:r});if(ae("DisplayValue",t,e.value))return te("DisplayValue",e,Es(e.value),{variant:r});const s=e.getAttribute("alt");if(ae("AltText",t,s))return te("AltText",e,s,{variant:r});const d=e.getAttribute("title");if(ae("Title",t,d))return te("Title",e,d,{variant:r});const c=e.getAttribute(w().testIdAttribute);if(ae("TestId",t,c))return te("TestId",e,c,{variant:r})}function Fr(e,r){e.stack=r.stack.replace(r.message,e.message)}function cO(e,r){let{container:t=di(),timeout:a=w().asyncUtilTimeout,showOriginalStackTrace:n=w().showOriginalStackTrace,stackTraceError:o,interval:l=50,onTimeout:u=s=>(s.message=w().getElementError(s.message,t).message,s),mutationObserverOptions:i={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=r;if(typeof e!="function")throw new TypeError("Received `callback` arg must be a function");return new Promise(async(s,d)=>{let c,f,m,y=!1,v="idle";const g=setTimeout(O,a),b=Zo();if(b){const{unstable_advanceTimersWrapper:h}=w();for(S();!y;){if(!Zo()){const C=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");n||Fr(C,o),d(C);return}if(h(()=>{jest.advanceTimersByTime(l)}),S(),y)break;await h(async()=>{await new Promise(C=>{setTimeout(C,0),jest.advanceTimersByTime(0)})})}}else{try{ee(t)}catch(C){d(C);return}f=setInterval(x,l);const{MutationObserver:h}=Yd(t);m=new h(x),m.observe(t,i),S()}function _(h,C){y=!0,clearTimeout(g),b||(clearInterval(f),m.disconnect()),h?d(h):s(C)}function x(){if(Zo()){const h=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return n||Fr(h,o),d(h)}else return S()}function S(){if(v!=="pending")try{const h=YS(e);typeof(h==null?void 0:h.then)=="function"?(v="pending",h.then(C=>{v="resolved",_(null,C)},C=>{v="rejected",c=C})):_(null,h)}catch(h){c=h}}function O(){let h;c?(h=c,!n&&h.name==="TestingLibraryElementError"&&Fr(h,o)):(h=new Error("Timed out in waitFor."),n||Fr(h,o)),_(u(h),null)}})}function dO(e,r){const t=new Error("STACK_TRACE_MESSAGE");return w().asyncWrapper(()=>cO(e,{stackTraceError:t,...r}))}function af(e,r){return w().getElementError(e,r)}function fO(e,r){return af(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",r)}function so(e,r,t,a){let{exact:n=!0,collapseWhitespace:o,trim:l,normalizer:u}=a===void 0?{}:a;const i=n?X:Oe,s=ge({collapseWhitespace:o,trim:l,normalizer:u});return Array.from(r.querySelectorAll("["+e+"]")).filter(d=>i(d.getAttribute(e),d,t,s))}function ft(e,r){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),o=1;o<a;o++)n[o-1]=arguments[o];const l=e(t,...n);if(l.length>1){const u=l.map(i=>af(null,i).message).join(`

`);throw fO(r(t,...n)+`

Here are the matching elements:

`+u,t)}return l[0]||null}}function nf(e,r){return w().getElementError(`A better query is available, try this:
`+e.toString()+`
`,r)}function pO(e,r){return function(t){for(var a=arguments.length,n=new Array(a>1?a-1:0),o=1;o<a;o++)n[o-1]=arguments[o];const l=e(t,...n);if(!l.length)throw w().getElementError(r(t,...n),t);return l}}function pt(e){return(r,t,a,n)=>dO(()=>e(r,t,a),{container:r,...n})}const He=(e,r,t)=>function(a){for(var n=arguments.length,o=new Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];const u=e(a,...o),[{suggest:i=w().throwSuggestions}={}]=o.slice(-1);if(u&&i){const s=$l(u,t);if(s&&!r.endsWith(s.queryName))throw nf(s.toString(),a)}return u},j=(e,r,t)=>function(a){for(var n=arguments.length,o=new Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];const u=e(a,...o),[{suggest:i=w().throwSuggestions}={}]=o.slice(-1);if(u.length&&i){const s=[...new Set(u.map(d=>{var c;return(c=$l(d,t))==null?void 0:c.toString()}))];if(s.length===1&&!r.endsWith($l(u[0],t).queryName))throw nf(s[0],a)}return u};function xe(e,r,t){const a=He(ft(e,r),e.name,"query"),n=pO(e,t),o=ft(n,r),l=He(o,e.name,"get"),u=j(n,e.name.replace("query","get"),"getAll"),i=pt(j(n,e.name,"findAll")),s=pt(He(o,e.name,"find"));return[a,u,l,i,s]}function mO(e){return Array.from(e.querySelectorAll("label,input")).map(r=>({node:r,textToMatch:ql(r)})).filter(r=>{let{textToMatch:t}=r;return t!==null})}const vO=function(e,r,t){let{exact:a=!0,trim:n,collapseWhitespace:o,normalizer:l}=t===void 0?{}:t;const u=a?X:Oe,i=ge({collapseWhitespace:o,trim:n,normalizer:l});return mO(e).filter(d=>{let{node:c,textToMatch:f}=d;return u(f,c,r,i)}).map(d=>{let{node:c}=d;return c})},dr=function(e,r,t){let{selector:a="*",exact:n=!0,collapseWhitespace:o,trim:l,normalizer:u}=t===void 0?{}:t;ee(e);const i=n?X:Oe,s=ge({collapseWhitespace:o,trim:l,normalizer:u}),d=Array.from(e.querySelectorAll("*")).filter(c=>Xd(c).length||c.hasAttribute("aria-labelledby")).reduce((c,f)=>{const m=Qd(e,f,{selector:a});m.filter(v=>!!v.formControl).forEach(v=>{i(v.content,v.formControl,r,s)&&v.formControl&&c.push(v.formControl)});const y=m.filter(v=>!!v.content).map(v=>v.content);return i(y.join(" "),f,r,s)&&c.push(f),y.length>1&&y.forEach((v,g)=>{i(v,f,r,s)&&c.push(f);const b=[...y];b.splice(g,1),b.length>1&&i(b.join(" "),f,r,s)&&c.push(f)}),c},[]).concat(so("aria-label",e,r,{exact:n,normalizer:s}));return Array.from(new Set(d)).filter(c=>c.matches(a))},Ee=function(e,r){for(var t=arguments.length,a=new Array(t>2?t-2:0),n=2;n<t;n++)a[n-2]=arguments[n];const o=dr(e,r,...a);if(!o.length){const l=vO(e,r,...a);if(l.length){const u=l.map(i=>yO(e,i)).filter(i=>!!i);throw u.length?w().getElementError(u.map(i=>"Found a label with the text of: "+r+", however the element associated with this label (<"+i+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+i+" />, you can use aria-label or aria-labelledby instead.").join(`

`),e):w().getElementError("Found a label with the text of: "+r+`, however no form control was found associated to that label. Make sure you're using the "for" attribute or "aria-labelledby" attribute correctly.`,e)}else throw w().getElementError("Unable to find a label with the text of: "+r,e)}return o};function yO(e,r){const t=r.getAttribute("for");if(!t)return null;const a=e.querySelector('[id="'+t+'"]');return a?a.tagName.toLowerCase():null}const of=(e,r)=>"Found multiple elements with the text of: "+r,bO=He(ft(dr,of),dr.name,"query"),lf=ft(Ee,of),hO=pt(j(Ee,Ee.name,"findAll")),gO=pt(He(lf,Ee.name,"find")),RO=j(Ee,Ee.name,"getAll"),PO=He(lf,Ee.name,"get"),_O=j(dr,dr.name,"queryAll"),Sl=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return ee(r[0]),so("placeholder",...r)},EO=(e,r)=>"Found multiple elements with the placeholder text of: "+r,CO=(e,r)=>"Unable to find an element with the placeholder text of: "+r,wO=j(Sl,Sl.name,"queryAll"),[qO,$O,SO,OO,xO]=xe(Sl,EO,CO),Ol=function(e,r,t){let{selector:a="*",exact:n=!0,collapseWhitespace:o,trim:l,ignore:u=w().defaultIgnore,normalizer:i}=t===void 0?{}:t;ee(e);const s=n?X:Oe,d=ge({collapseWhitespace:o,trim:l,normalizer:i});let c=[];return typeof e.matches=="function"&&e.matches(a)&&(c=[e]),[...c,...Array.from(e.querySelectorAll(a))].filter(f=>!u||!f.matches(u)).filter(f=>s(uo(f),f,r,d))},TO=(e,r)=>"Found multiple elements with the text: "+r,AO=function(e,r,t){t===void 0&&(t={});const{collapseWhitespace:a,trim:n,normalizer:o,selector:l}=t,i=ge({collapseWhitespace:a,trim:n,normalizer:o})(r.toString()),s=i!==r.toString(),d=(l??"*")!=="*";return"Unable to find an element with the text: "+(s?i+" (normalized from '"+r+"')":r)+(d?", which matches selector '"+l+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."},MO=j(Ol,Ol.name,"queryAll"),[IO,BO,NO,FO,jO]=xe(Ol,TO,AO),xl=function(e,r,t){let{exact:a=!0,collapseWhitespace:n,trim:o,normalizer:l}=t===void 0?{}:t;ee(e);const u=a?X:Oe,i=ge({collapseWhitespace:n,trim:o,normalizer:l});return Array.from(e.querySelectorAll("input,textarea,select")).filter(s=>s.tagName==="SELECT"?Array.from(s.options).filter(c=>c.selected).some(c=>u(uo(c),c,r,i)):u(s.value,s,r,i))},DO=(e,r)=>"Found multiple elements with the display value: "+r+".",kO=(e,r)=>"Unable to find an element with the display value: "+r+".",LO=j(xl,xl.name,"queryAll"),[UO,HO,WO,zO,GO]=xe(xl,DO,kO),VO=/^(img|input|area|.+-.+)$/i,Tl=function(e,r,t){return t===void 0&&(t={}),ee(e),so("alt",e,r,t).filter(a=>VO.test(a.tagName))},KO=(e,r)=>"Found multiple elements with the alt text: "+r,YO=(e,r)=>"Unable to find an element with the alt text: "+r,JO=j(Tl,Tl.name,"queryAll"),[XO,QO,ZO,ex,rx]=xe(Tl,KO,YO),tx=e=>{var r;return e.tagName.toLowerCase()==="title"&&((r=e.parentElement)==null?void 0:r.tagName.toLowerCase())==="svg"},Al=function(e,r,t){let{exact:a=!0,collapseWhitespace:n,trim:o,normalizer:l}=t===void 0?{}:t;ee(e);const u=a?X:Oe,i=ge({collapseWhitespace:n,trim:o,normalizer:l});return Array.from(e.querySelectorAll("[title], svg > title")).filter(s=>u(s.getAttribute("title"),s,r,i)||tx(s)&&u(uo(s),s,r,i))},ax=(e,r)=>"Found multiple elements with the title: "+r+".",nx=(e,r)=>"Unable to find an element with the title: "+r+".",ox=j(Al,Al.name,"queryAll"),[lx,ix,ux,sx,cx]=xe(Al,ax,nx);function Ml(e,r,t){let{exact:a=!0,collapseWhitespace:n,hidden:o=w().defaultHidden,name:l,description:u,trim:i,normalizer:s,queryFallbacks:d=!1,selected:c,checked:f,pressed:m,current:y,level:v,expanded:g}=t===void 0?{}:t;ee(e);const b=a?X:Oe,_=ge({collapseWhitespace:n,trim:i,normalizer:s});if(c!==void 0){var x;if(((x=ke.get(r))==null?void 0:x.props["aria-selected"])===void 0)throw new Error('"aria-selected" is not supported on role "'+r+'".')}if(f!==void 0){var S;if(((S=ke.get(r))==null?void 0:S.props["aria-checked"])===void 0)throw new Error('"aria-checked" is not supported on role "'+r+'".')}if(m!==void 0){var O;if(((O=ke.get(r))==null?void 0:O.props["aria-pressed"])===void 0)throw new Error('"aria-pressed" is not supported on role "'+r+'".')}if(y!==void 0){var h;if(((h=ke.get(r))==null?void 0:h.props["aria-current"])===void 0)throw new Error('"aria-current" is not supported on role "'+r+'".')}if(v!==void 0&&r!=="heading")throw new Error('Role "'+r+'" cannot have "level" property.');if(g!==void 0){var C;if(((C=ke.get(r))==null?void 0:C.props["aria-expanded"])===void 0)throw new Error('"aria-expanded" is not supported on role "'+r+'".')}const T=new WeakMap;function re(E){return T.has(E)||T.set(E,tf(E)),T.get(E)}return Array.from(e.querySelectorAll(dx(r,a,s?_:void 0))).filter(E=>{if(E.hasAttribute("role")){const F=E.getAttribute("role");if(d)return F.split(" ").filter(Boolean).some(co=>b(co,E,r,_));if(s)return b(F,E,r,_);const[Ae]=F.split(" ");return b(Ae,E,r,_)}return pi(E).some(F=>b(F,E,r,_))}).filter(E=>c!==void 0?c===aO(E):f!==void 0?f===nO(E):m!==void 0?m===oO(E):y!==void 0?y===lO(E):g!==void 0?g===iO(E):v!==void 0?v===uO(E):!0).filter(E=>l===void 0?!0:X(Fl(E,{computedStyleSupportsPseudoElements:w().computedStyleSupportsPseudoElements}),E,l,Te=>Te)).filter(E=>u===void 0?!0:X(Rc(E,{computedStyleSupportsPseudoElements:w().computedStyleSupportsPseudoElements}),E,u,Te=>Te)).filter(E=>o===!1?fi(E,{isSubtreeInaccessible:re})===!1:!0)}function dx(e,r,t){var a;if(typeof e!="string")return"*";const n=r&&!t?'*[role~="'+e+'"]':"*[role]",o=(a=Ld.get(e))!=null?a:new Set,l=new Set(Array.from(o).map(u=>{let{name:i}=u;return i}));return[n].concat(Array.from(l)).join(",")}const uf=e=>{let r="";return e===void 0?r="":typeof e=="string"?r=' and name "'+e+'"':r=" and name `"+e+"`",r},fx=function(e,r,t){let{name:a}=t===void 0?{}:t;return'Found multiple elements with the role "'+r+'"'+uf(a)},px=function(e,r,t){let{hidden:a=w().defaultHidden,name:n,description:o}=t===void 0?{}:t;if(w()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+r+'"'+uf(n);let l="";Array.from(e.children).forEach(d=>{l+=tO(d,{hidden:a,includeDescription:o!==void 0})});let u;l.length===0?a===!1?u="There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":u="There are no available roles.":u=(`
Here are the `+(a===!1?"accessible":"available")+` roles:

  `+l.replace(/\n/g,`
  `).replace(/\n\s\s\n/g,`

`)+`
`).trim();let i="";n===void 0?i="":typeof n=="string"?i=' and name "'+n+'"':i=" and name `"+n+"`";let s="";return o===void 0?s="":typeof o=="string"?s=' and description "'+o+'"':s=" and description `"+o+"`",(`
Unable to find an `+(a===!1?"accessible ":"")+'element with the role "'+r+'"'+i+s+`

`+u).trim()},mx=j(Ml,Ml.name,"queryAll"),[vx,yx,bx,hx,gx]=xe(Ml,fx,px),mi=()=>w().testIdAttribute,Il=function(){for(var e=arguments.length,r=new Array(e),t=0;t<e;t++)r[t]=arguments[t];return ee(r[0]),so(mi(),...r)},Rx=(e,r)=>"Found multiple elements by: ["+mi()+'="'+r+'"]',Px=(e,r)=>"Unable to find an element by: ["+mi()+'="'+r+'"]',_x=j(Il,Il.name,"queryAll"),[Ex,Cx,wx,qx,$x]=xe(Il,Rx,Px);var Bl=Object.freeze({__proto__:null,queryAllByLabelText:_O,queryByLabelText:bO,getAllByLabelText:RO,getByLabelText:PO,findAllByLabelText:hO,findByLabelText:gO,queryByPlaceholderText:qO,queryAllByPlaceholderText:wO,getByPlaceholderText:SO,getAllByPlaceholderText:$O,findAllByPlaceholderText:OO,findByPlaceholderText:xO,queryByText:IO,queryAllByText:MO,getByText:NO,getAllByText:BO,findAllByText:FO,findByText:jO,queryByDisplayValue:UO,queryAllByDisplayValue:LO,getByDisplayValue:WO,getAllByDisplayValue:HO,findAllByDisplayValue:zO,findByDisplayValue:GO,queryByAltText:XO,queryAllByAltText:JO,getByAltText:ZO,getAllByAltText:QO,findAllByAltText:ex,findByAltText:rx,queryByTitle:lx,queryAllByTitle:ox,getByTitle:ux,getAllByTitle:ix,findAllByTitle:sx,findByTitle:cx,queryByRole:vx,queryAllByRole:mx,getAllByRole:yx,getByRole:bx,findAllByRole:hx,findByRole:gx,queryByTestId:Ex,queryAllByTestId:_x,getByTestId:wx,getAllByTestId:Cx,findAllByTestId:qx,findByTestId:$x});function Sx(e,r,t){return r===void 0&&(r=Bl),t===void 0&&(t={}),Object.keys(r).reduce((a,n)=>{const o=r[n];return a[n]=o.bind(null,e),a},t)}const ws={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}}},qs={doubleClick:"dblClick"};function fr(e,r){return w().eventWrapper(()=>{if(!r)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+r.type+'" event - please provide a DOM element.');return e.dispatchEvent(r)})}function el(e,r,t,a){let{EventType:n="Event",defaultInit:o={}}=a===void 0?{}:a;if(!r)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const l={...o,...t},{target:{value:u,files:i,...s}={}}=l;u!==void 0&&Ox(r,u),i!==void 0&&Object.defineProperty(r,"files",{configurable:!0,enumerable:!0,writable:!0,value:i}),Object.assign(r,s);const d=Yd(r),c=d[n]||d.Event;let f;if(typeof c=="function")f=new c(e,l);else{f=d.document.createEvent(n);const{bubbles:y,cancelable:v,detail:g,...b}=l;f.initEvent(e,y,v,g),Object.keys(b).forEach(_=>{f[_]=b[_]})}return["dataTransfer","clipboardData"].forEach(y=>{const v=l[y];typeof v=="object"&&(typeof d.DataTransfer=="function"?Object.defineProperty(f,y,{value:Object.getOwnPropertyNames(v).reduce((g,b)=>(Object.defineProperty(g,b,{value:v[b]}),g),new d.DataTransfer)}):Object.defineProperty(f,y,{value:v}))}),f}Object.keys(ws).forEach(e=>{const{EventType:r,defaultInit:t}=ws[e],a=e.toLowerCase();el[e]=(n,o)=>el(a,n,o,{EventType:r,defaultInit:t}),fr[e]=(n,o)=>fr(n,el[e](n,o))});function Ox(e,r){const{set:t}=Object.getOwnPropertyDescriptor(e,"value")||{},a=Object.getPrototypeOf(e),{set:n}=Object.getOwnPropertyDescriptor(a,"value")||{};if(n&&t!==n)n.call(e,r);else if(t)t.call(e,r);else throw new Error("The given element does not have a value setter")}Object.keys(qs).forEach(e=>{const r=qs[e];fr[e]=function(){return fr[r](...arguments)}});function xx(e){return e.replace(/[ \t]*[\n][ \t]*/g,`
`)}function Tx(e){return Sf.compressToEncodedURIComponent(xx(e))}function Ax(e){return"https://testing-playground.com/#markup="+Tx(e)}const Mx=(e,r,t)=>Array.isArray(e)?e.forEach(a=>_s(a,r,t)):_s(e,r,t),Ix=function(e){if(e===void 0&&(e=di().body),!e||!("innerHTML"in e)){console.log("The element you're providing isn't a valid DOM element.");return}if(!e.innerHTML){console.log("The provided element doesn't have any children.");return}const r=Ax(e.innerHTML);return console.log(`Open this URL in your browser

`+r),r},$s={debug:Mx,logTestingPlaygroundURL:Ix};typeof document<"u"&&document.body?Sx(document.body,Bl,$s):Object.keys(Bl).reduce((e,r)=>(e[r]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e),$s);const Bx=Of.act;function sf(){if(typeof globalThis<"u")return globalThis;if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")}function J(e){sf().IS_REACT_ACT_ENVIRONMENT=e}function mt(){return sf().IS_REACT_ACT_ENVIRONMENT}function Nx(e){return r=>{const t=mt();J(!0);try{let a=!1;const n=e(()=>{const o=r();return o!==null&&typeof o=="object"&&typeof o.then=="function"&&(a=!0),o});if(a){const o=n;return{then:(l,u)=>{o.then(i=>{J(t),l(i)},i=>{J(t),u(i)})}}}else return J(t),n}catch(a){throw J(t),a}}}const Nl=Nx(Bx);Object.keys(fr).forEach(e=>{});JS({unstable_advanceTimersWrapper:e=>Nl(e),asyncWrapper:async e=>{const r=mt();J(!1);try{return await e()}finally{J(r)}},eventWrapper:e=>{let r;return Nl(()=>{r=e()}),r}});const Fx=new Set,Ss=[];function Os(){Ss.forEach(e=>{let{root:r,container:t}=e;Nl(()=>{r.unmount()}),t.parentNode===document.body&&document.body.removeChild(t)}),Ss.length=0,Fx.clear()}var xs;if((typeof process>"u"||!((xs=process.env)!=null&&xs.RTL_SKIP_AUTO_CLEANUP))&&(typeof afterEach=="function"?afterEach(()=>{Os()}):typeof teardown=="function"&&teardown(()=>{Os()}),typeof beforeAll=="function"&&typeof afterAll=="function")){let e=mt();beforeAll(()=>{e=mt(),J(!0)}),afterAll(()=>{J(e)})}const Ux=(e={})=>{const{customError:r}=df(),[t,a]=xr.useState([]),n=yi(y=>y.userManagement.taskData),[o,l]=xr.useState([]),u=yi(y=>y.applicationConfig),i=ff(),[s,d]=xr.useState(!1);let c={handleSubmitForApproval:7,handleSaveAsDraft:1,handleSendBack:2,handleReject:4,handleValidate:6,handleSAPSyndication:9,handleIdGenerator:5,handleSubmitForReview:8,handleCorrection:3};const f=pf(mf.CURRENT_TASK,!0,{});return xr.useEffect(()=>{const y=((n==null?void 0:n.taskDesc)||(f==null?void 0:f.taskDesc)||vf.INITIATOR).trim().toLowerCase(),v=t==null?void 0:t.filter(b=>{var _;return((_=b==null?void 0:b.MDG_DYN_BTN_TASK_NAME)==null?void 0:_.trim().toLowerCase())===y}),g=v==null?void 0:v.sort((b,_)=>{const x=c[b.MDG_DYN_BTN_ACTION_TYPE],S=c[_.MDG_DYN_BTN_ACTION_TYPE];return x-S});l(g),i(yf(g||[])),(g!=null&&g.find(b=>b.MDG_DYN_BTN_BUTTON_NAME===bi.SEND_BACK)||g!=null&&g.find(b=>b.MDG_DYN_BTN_BUTTON_NAME===bi.CORRECTION))&&d(!0)},[t]),{getButtonsDisplayGlobal:(y,v,g,b)=>{let _={decisionTableId:null,decisionTableName:v??"MDG_MAT_DYN_BUTTON_CONFIG",version:g??"v3",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_DYN_BTN_MODULE_NAME":y,"MDG_CONDITIONS.MDG_DYN_BTN_REQUEST_TYPE":(n==null?void 0:n.ATTRIBUTE_2)||(f==null?void 0:f.ATTRIBUTE_2)||b}],systemFilters:null,systemOrders:null,filterString:null};const x=O=>{var h,C;if(O.statusCode===200){let T=(C=(h=O==null?void 0:O.data)==null?void 0:h.result[0])==null?void 0:C.MDG_DYN_BTN_ACTION_TYPE;a(T)}},S=O=>{r(O)};u.environment==="localhost"?hi(`/${gi}${Ri.INVOKE_RULES.LOCAL}`,"post",x,S,_):hi(`/${gi}${Ri.INVOKE_RULES.PROD}`,"post",x,S,_)},showWfLevels:s}},jx=bf.forwardRef(function(r,t){return N(hf,{direction:"up",ref:t,...r})}),Hx=({closeReusableDialog:e,dialogState:r,onClose:t,isHierarchyCheck:a=!1,module:n})=>gf(Cf,{fullWidth:!0,maxWidth:"xl",TransitionComponent:jx,transitionDuration:400,PaperProps:{sx:{width:"92vw",height:"85vh",maxWidth:"none",borderRadius:"16px",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",border:"1px solid rgba(255, 255, 255, 0.1)",boxShadow:"0 25px 50px -12px rgba(0, 0, 0, 0.25)",overflow:"hidden",position:"relative","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,zIndex:-1}}},open:r,onClose:t||e,children:[N(Je,{sx:{position:"absolute",top:-50,right:-50,width:200,height:200,borderRadius:"50%",animation:"pulse 4s ease-in-out infinite","@keyframes pulse":{"0%, 100%":{transform:"scale(1)",opacity:.3},"50%":{transform:"scale(1.1)",opacity:.1}}}}),N(Je,{sx:{position:"absolute",bottom:-30,left:-30,width:150,height:150,borderRadius:"50%",animation:"float 6s ease-in-out infinite","@keyframes float":{"0%, 100%":{transform:"translateY(0px)"},"50%":{transform:"translateY(-20px)"}}}}),N(Pf,{"aria-label":"close",onClick:t||e,sx:{position:"absolute",right:16,top:16,zIndex:1e3,backgroundColor:"rgba(0, 0, 0, 0.1)",backdropFilter:"blur(10px)",border:"1px solid rgba(0, 0, 0, 0.15)",color:"#374151",width:44,height:44,transition:"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.15)",transform:"scale(1.05)",color:"#1f2937",boxShadow:"0 8px 16px rgba(0, 0, 0, 0.15)"},"&:active":{transform:"scale(0.95)"}},children:N(Rf,{})}),N(Ef,{sx:{padding:0,height:"100%",position:"relative",overflow:"hidden"},children:N(Je,{sx:{height:"100%",background:"rgba(255, 255, 255, 0.95)",backdropFilter:"blur(20px)",position:"relative",overflow:"hidden"},children:N(Je,{sx:{height:"100%",padding:"32px",overflow:"auto",position:"relative","&::-webkit-scrollbar":{width:"8px"},"&::-webkit-scrollbar-track":{background:"rgba(0, 0, 0, 0.1)",borderRadius:"4px"},"&::-webkit-scrollbar-thumb":{background:"linear-gradient(135deg, #667eea, #764ba2)",borderRadius:"4px","&:hover":{background:"linear-gradient(135deg, #5a6fd8, #6a4190)"}}},children:N(_f,{in:r,timeout:600,children:N(Je,{sx:{background:"rgba(255, 255, 255, 0.8)",backdropFilter:"blur(10px)",borderRadius:"16px",border:"1px solid rgba(255, 255, 255, 0.2)",padding:"24px",minHeight:"100%",boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)",position:"relative",overflow:"hidden","&::before":{content:'""',position:"absolute",top:0,left:0,right:0,height:"4px",background:"linear-gradient(90deg, #667eea, #764ba2, #667eea)",backgroundSize:"200% 100%",animation:"shimmer 3s ease-in-out infinite","@keyframes shimmer":{"0%":{backgroundPosition:"-200% 0"},"100%":{backgroundPosition:"200% 0"}}}},children:N(Pi,{container:!0,children:N(Pi,{item:!0,xs:12,children:N(xf,{isHierarchyCheck:a,module:n})})})})})})})})]});export{Hx as E,Ux as u};
