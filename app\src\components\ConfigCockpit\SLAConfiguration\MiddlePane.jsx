// MiddlePane.js
import React from 'react';
import {
  Card,
  Table,
  Tag,
  Space,
  Button,
  Tooltip,
  Typography,
  Popover,
  Empty
} from 'antd';
import {
  InfoCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { useTheme } from '@mui/material';

const { Title, Text } = Typography;

const MiddlePane = ({
  data,
  isLoading,
  onRowSelect,
  selectedRowId
}) => {
  const theme = useTheme()
  const columns = [
    {
      title: 'Region',
      dataIndex: 'region',
      key: 'region',
      width: 120,
      ellipsis: true,
      render: (text) => (
        <Tag color="blue" style={{ margin: 0 }}>
          {text}
        </Tag>
      )
    },
    {
      title: 'Timezone',
      dataIndex: 'timeZone',
      key: 'timeZone',
      width: 150,
      ellipsis: true,
      render: (text) => (
        <Text code style={{ fontSize: '15px' }}>
          {text}
        </Text>
      )
    },
    {
      title: 'Day',
      dataIndex: 'dayOfWeek',
      key: 'dayOfWeek',
      width: 100,
      render: (text) => (
        <Tag color="green">
          {text?.slice(0, 3)}
        </Tag>
      )
    },
    {
      title: 'Business Hours',
      key: 'businessHours',
      width: 140,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <ClockCircleOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
          <Text style={{ fontSize: '12px' }}>
            {moment(record.workStartTime, "HH:mm:ss").format("HH:mm")} - {moment(record.workEndTime, "HH:mm:ss").format("HH:mm")}
          </Text>
        </div>
      )
    },
    {
      title: 'Off Hours',
      dataIndex: 'offHoursRanges',
      key: 'offHoursRanges',
      width: 120,
      render: (ranges) => {
        if (!ranges || ranges.length === 0) {
          return <Text type="secondary" style={{ fontSize: '12px' }}>None</Text>;
        }

        const content = (
          <div style={{ maxWidth: '300px' }}>
            <Title level={5} style={{ margin: '0 0 8px 0' }}>
              All Off Hours Ranges
            </Title>
            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {ranges.map((range, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  <Tag color="orange" size="small">
                    {range.startTime} - {range.endTime}
                  </Tag>
                </div>
              ))}
            </div>
          </div>
        );

        return (
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Tag color="orange" size="small">
              {ranges[0].startTime} - {ranges[0].endTime}
            </Tag>
            {ranges.length > 1 && (
              <Popover content={content} title="" placement="topRight" trigger="hover">
                <Button
                  type="text"
                  icon={<InfoCircleOutlined />}
                  size="small"
                  style={{ minWidth: 'auto', padding: '2px', fontSize: '12px' }}
                />
              </Popover>
            )}
          </div>
        );
      }
    },
    {
      title: 'Active',
      dataIndex: 'isActive',
      key: 'isActive',
      width: 80,
      align: 'center',
      render: (isActive) => {
        const active = isActive !== false;
        return (
          <Tag color={active ? 'green' : 'red'} style={{ fontSize: '11px', fontWeight: 'bold' }}>
            {active ? 'ON' : 'OFF'}
          </Tag>
        );
      }
    },
  ];

  return (
    <Card
      title={
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Title level={5} style={{ margin: 0 }}>
            Business Schedule ({data.length} records)
          </Title>
        </div>
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '0' }}
    >
      {data.length === 0 && !isLoading ? (
        <div style={{ padding: '40px', textAlign: 'center' }}>
          <Empty
            description="No business hours configured"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </div>
      ) : (
        <>
          <Table
            columns={columns}
            dataSource={data}
            rowKey="id"
            loading={isLoading}
            pagination={false}
            scroll={{ 
              y: 'calc(max(400px, 100vh - 300px))',
              x: 800
            }}
            size="small"
            rowSelection={null}
            onRow={(record) => ({
              onClick: () => onRowSelect(record),
              style: {
                cursor: 'pointer',
                backgroundColor: selectedRowId === record.id ? theme?.palette?.primary?.light : 'transparent'
              },
             
            })}
          />
          {data.length > 0 && (
            <div style={{ 
              padding: '8px 16px',
              borderTop: '1px solid #f0f0f0',
              backgroundColor: '#fafafa',
              textAlign: 'right',
              fontSize: '14px',
              color: '#666'
            }}>
              Total: {data.length} items
            </div>
          )}
        </>
      )}
    </Card>
  );
};

export default MiddlePane;