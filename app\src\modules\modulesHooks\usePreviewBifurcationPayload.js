import { useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import { MODULE_MAP, REQUEST_TYPE } from "@constant/enum";
import useChangePayloadCreation from "@hooks/useChangePayloadCreation";
import usePayloadCreation from "@hooks/usePayloadCreation";
import usepayloadCreationArticle from "@article/hooks/usepayloadCreationArticle";
import { createPayloadForBK, createBOMPayload, changePayloadForCC, createPayloadForCC, createPayloadForPC,createPayloadForGL, changePayloadForPC,createPayloadForPCG, createPayloadForCCG, createPayloadFor<PERSON>, createPayloadForGLExtend } from "../../functions";
import { setFetchedCostCenterDataCc } from "@app/costCenterTabsSlice";
import { changePayloadForGL } from './../../functions';



const usePreviewBifurcationPayload = ({
  module,
  requestId,
  requestType,
  templateName,
  payloadData,
}) => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const isReqBench = queryParams.get("reqBench");
  const initialReqScreen = !Boolean(taskData?.taskId) && !isReqBench;
  const bomRows = useSelector((state) => state.bom.bomRows);
  const payloadFields = useSelector((state) => state.bom.BOMpayloadData);
  const tabFieldValues = useSelector((state) => state.bom.tabFieldValues);
  const createdRequestId = useSelector((state) => state.bom.requestHeaderID);
  const initialPayload = useSelector((state) => state.request.requestHeader);
  const task = useSelector((state) => state?.userManagement.taskData);
  const glRows = useSelector((state) => state.generalLedger.payload.rowsHeaderData);
  const selectedExtendDropdownData = useSelector(
      (state) => state?.generalLedger?.selecteddropdownDataForExtendedCode
    );
  const selectedcopyFromCompanyCode = useSelector(
      (state) => state?.generalLedger?.selectedcopyFromCompanyCode
    );

  const reduxPayload = useSelector((state) => {
    switch (module) {
      case MODULE_MAP?.CC:
        return state.costCenter.payload;
      case MODULE_MAP?.PC:
        return state.profitCenter.payload;
      case MODULE_MAP?.GL:
        return state.generalLedger.payload;
      case MODULE_MAP?.CCG:
        return state.hierarchyData;
      case MODULE_MAP?.PCG:
        return state.hierarchyData;
      case MODULE_MAP?.CEG:
        return state.hierarchyData;
      case MODULE_MAP?.IO:
        return state.internalOrder;
      default:
        return null; // or some default value
    }
  });

  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  

  const requestHeaderData = useSelector(
      (state) => state.profitCenter.payload.requestHeaderData
    );


   const requestHeaderDataGL = useSelector(
      (state) => state.generalLedger.payload.requestHeaderData
    );


  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const { fetchedProfitCenterData, fetchReqBenchData } = useSelector(
    (state) => state.profitCenter
  );
  const { fetchedGeneralLedgerData } = useSelector(
    (state) => state.generalLedger
  );
  const fetchReqBenchDataGL= useSelector((state) => state.generalLedger.fetchReqBenchData);
  const { fetchedCostCenterData, fetchReqBenchDataCC } = useSelector(
    (state) => state.costCenter
  );

  const { createPayloadFromReduxState } = usePayloadCreation({
    initialReqScreen,
    isReqBench,
  });
  const {createPayloadFromReduxStateArticle} = usepayloadCreationArticle({
    initialReqScreen,
    isReqBench,
  })

  const { changePayloadForTemplate } = useChangePayloadCreation(templateName);

  const payloadMap = {
    [MODULE_MAP.MAT]: () => {
      if (
        requestType === REQUEST_TYPE.CHANGE ||
        requestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        return requestId
          ? changePayloadForTemplate(true)
          : changePayloadForTemplate(false);
      }
      return createPayloadFromReduxState(payloadData);
    },
    [MODULE_MAP.ART]:() => {
      return createPayloadFromReduxStateArticle(payloadData);
    },
    [MODULE_MAP.PC]: () => {
  if (
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
  ) {
    return createPayloadForPC(
      reduxPayload,
      requestHeaderSlice,
      requestId,
      taskData,
      dynamicData
    );
  }

  return changePayloadForPC(
    requestHeaderData,
    taskData,
    fetchReqBenchData,
    fetchedProfitCenterData
  );
},
    [MODULE_MAP.CC]: () => {
  if (
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
  ) {
    return createPayloadForCC(
      reduxPayload,
      requestHeaderSlice,
      // requestId,
      taskData,
      dynamicData
    );
  }

  return changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    task,
    isReqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData
  );
},
    [MODULE_MAP.GL]: () => {
      if (
    requestType === REQUEST_TYPE?.CREATE ||
    requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
  ) {
  
    return createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      // requestId,
      '',
      taskData,
      dynamicData,
      '',
      ''
    );
  }

   if (
    requestType === REQUEST_TYPE?.EXTEND
  ) {
  
    return createPayloadForGLExtend(
      reduxPayload,
      requestHeaderSlice,
      glRows,
      task,
      selectedExtendDropdownData,dynamicData,
      selectedcopyFromCompanyCode
    );
  }
  return changePayloadForGL(
    requestHeaderDataGL,
    initialPayload,
    task,
    isReqBench,
    fetchReqBenchDataGL,
    fetchedGeneralLedgerData
  );
},
    [MODULE_MAP.PCG]: () => createPayloadForPCG(payloadData,requestHeaderSlice),
    [MODULE_MAP.CCG]: () => createPayloadForCCG(payloadData,requestHeaderSlice),
    [MODULE_MAP.CEG]: () => createPayloadForPCG(payloadData,requestHeaderSlice),
    [MODULE_MAP.BK]: () => createPayloadForBK(payloadData?.payload,payloadData?.requestHeaderResponse),
    [MODULE_MAP.BOM]: () => createBOMPayload(
      bomRows,
      tabFieldValues,
      payloadFields,
      createdRequestId),
    [MODULE_MAP.IO]: () => createPayloadForIO(
      reduxPayload,
      requestHeaderSlice,
      requestId,
      taskData,
      dynamicData),
    [MODULE_MAP.ART]: () => {
      return createPayloadFromReduxStateArticle(payloadData);
    },
  };
 
  const generatePayload = payloadMap[module] || (() => null);

  return generatePayload();
};

export default usePreviewBifurcationPayload;
