// RightPane.js
import React from 'react';
import {
  Card,
  Button,
  Descriptions,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Tooltip,
  Empty,
  Divider
} from 'antd';
import {
  DoubleRightOutlined,
  DoubleLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  GlobalOutlined,
  CalendarOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '@mui/material';

const { Title, Text } = Typography;

const RightPane = ({
  isCollapsed,
  onToggleCollapse,
  selectedRowData,
  onEditRow,
  onDeleteRow
}) => {
  const theme = useTheme()
  if (isCollapsed) {
    return (
      <Card
        size="small"
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ 
          padding: '8px', 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center',
          justifyContent: 'flex-start'
        }}
      >
        <Tooltip title="Expand" placement="left">
          <Button
            type="text"
            icon={<DoubleLeftOutlined />}
            onClick={onToggleCollapse}
            style={{ marginBottom: '16px' }}
          />
        </Tooltip>
        
        <Divider style={{ margin: '8px 0' }} />

        <Tooltip title="Edit Business Hour" placement="right">
            <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => onEditRow(selectedRowData)}
            style={{
                backgroundColor: theme?.palette?.primary?.main,
                marginTop: '16px',
                marginBottom: '16px',
                align: 'center'
            }}
            />
        </Tooltip>

        <Tooltip title="Delete Business Hour" placement="right">
            <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDeleteRow(selectedRowData)}
            style={{
                marginBottom: '16px',
                align: 'center'
            }}
            />
        </Tooltip>

        <div style={{
            fontSize: '18px',
            fontWeight: 'bold',
            marginTop: '20px',
            color: '#666',
            textAlign: 'center',
            writingMode: 'vertical-rl',
            textOrientation: 'mixed',
            maxHeight: '500px',
            overflow: 'hidden'
        }}>
            {
                selectedRowData ? `${selectedRowData?.region} - ${selectedRowData?.timeZone} - ${selectedRowData?.dayOfWeek}` : `No Row Selected!`
            }
            
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={5} style={{ margin: 0 }}>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              View Details
            </Title>
          </Col>
          <Col>
            <Tooltip title="Collapse">
              <Button
                type="text"
                icon={<DoubleRightOutlined />}
                onClick={onToggleCollapse}
                size="small"
              />
            </Tooltip>
          </Col>
        </Row>
      }
      size="small"
      style={{ height: '100%' }}
      bodyStyle={{ padding: '16px' }}
    >
      {!selectedRowData ? (
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          justifyContent: 'center',
          height: '100%',
          textAlign: 'center'
        }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div>
                <Text type="secondary">No row selected</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  Click on any row in the table to view details
                </Text>
              </div>
            }
          />
        </div>
      ) : (
        <Space direction="vertical" style={{ width: '100%', alignItems: "right" }} size="large">
          {/* Quick Actions */}
          <div style={{ display: "flex", justifyContent: "flex-end" }}>
            <Space>
              <Button
                type="primary"
                icon={<EditOutlined />}
                style={{ backgroundColor: theme?.palette?.primary?.main }}
                onClick={() => onEditRow(selectedRowData)}
                size="small"
              >
                Edit
              </Button>
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={() => onDeleteRow(selectedRowData)}
                size="small"
              >
                Delete
              </Button>
            </Space>
          </div>

          <Divider style={{ margin: '-10px 0' }} />

          {/* Basic Information */}
          <div>
            <Title level={5} style={{ marginTop: -25, marginBottom: 12, color: '#1890ff' }}>
              <GlobalOutlined style={{ marginRight: 8 }} />
              Location Information
            </Title>
            <Descriptions column={1} size="small" bordered>
              <Descriptions.Item 
                label={<Text strong>Region</Text>}
              >
                <Tag color="blue">{selectedRowData.region}</Tag>
              </Descriptions.Item>
              <Descriptions.Item 
                label={<Text strong>Timezone</Text>}
              >
                <Text code>{selectedRowData.timeZone}</Text>
              </Descriptions.Item>
            </Descriptions>
          </div>

          {/* Schedule Information */}
          <div>
            <Title level={5} style={{ marginBottom: 12, color: '#52c41a' }}>
              <CalendarOutlined style={{ marginRight: 8 }} />
              Schedule Information
            </Title>
            <Descriptions column={1} size="small" bordered>
              <Descriptions.Item 
                label={<Text strong>Day of Week</Text>}
              >
                <Tag color="green">{selectedRowData.dayOfWeek}</Tag>
              </Descriptions.Item>
              <Descriptions.Item 
                label={<Text strong>Business Hours</Text>}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <ClockCircleOutlined style={{ color: '#1890ff' }} />
                  <Text>
                    {selectedRowData.workStartTime} - {selectedRowData.workEndTime}
                  </Text>
                </div>
              </Descriptions.Item>
            </Descriptions>
          </div>

          {/* Off Hours Information */}
          <div>
            <Title level={5} style={{ marginBottom: 12, color: '#fa8c16' }}>
              <ClockCircleOutlined style={{ marginRight: 8 }} />
              Off Hours Ranges
            </Title>
            <Card size="small" style={{ backgroundColor: '#fafafa' }}>
              {!selectedRowData.offHoursRanges || selectedRowData.offHoursRanges.length === 0 ? (
                <Text type="secondary" style={{ fontStyle: 'italic' }}>
                  No off hours ranges configured
                </Text>
              ) : (
                <Space wrap>
                  {selectedRowData.offHoursRanges.map((range, index) => (
                    <Tag key={index} color="orange">
                      {range.startTime} - {range.endTime}
                    </Tag>
                  ))}
                </Space>
              )}
            </Card>
          </div>

          {/* Summary Section */}
          <div style={{ 
            background: theme?.palette?.primary?.light, 
            padding: '12px', 
            borderRadius: '6px',
            marginTop: 'auto'
          }}>
            <Title level={5} style={{ marginBottom: 8, fontSize: '14px' }}>
              Summary
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              This business hour configuration applies to <strong>{selectedRowData.region}</strong> region 
              on <strong>{selectedRowData.dayOfWeek}</strong>s with business hours from{' '}
              <strong>{selectedRowData.workStartTime}</strong> to <strong>{selectedRowData.workEndTime}</strong>
              {selectedRowData.offHoursRanges && selectedRowData.offHoursRanges.length > 0 && (
                <span> and has {selectedRowData.offHoursRanges.length} off-hour range(s)</span>
              )}.
            </Text>
          </div>
        </Space>
      )}
    </Card>
  );
};

export default RightPane;