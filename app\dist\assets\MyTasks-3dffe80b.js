import{n as d,cZ as pe,aP as me,s as Ie,g as Re,r as l,cw as se,q$ as ue,az as de,aA as ae,aB as Ne,aC as J,r0 as Ee,c as Ue,j as Y,r1 as he,C as b,bc as te,hQ as U,aJ as Se,r2 as a,cr as v,aO as T,bK as I,r3 as De,ae as ge,bf as w,au as F,aE as Me,aT as Z,r4 as fe}from"./index-226a1e75.js";import{u as ye,g as Ae,W as Ce}from"./propData-3de5c575.js";import{c as Le}from"./configData-978802d4.js";import{u as Ve}from"./useDisplayDataDto-192489ea.js";import"./redux-49302a50.js";import"./index-2a7424d8.js";import"./react-beautiful-dnd.esm-0de399b3.js";import"./index-9c81b930.js";import"./asyncToGenerator-88583e02.js";import"./_baseDelay-5448b93c.js";import"./Chip-a06f5bd7.js";import"./Paper-164eb9eb.js";import"./Dropdown-6cea9e1f.js";import"./TextField-17bdd2f4.js";import"./Tooltip-d0e36572.js";import"./TableContainer-debf0374.js";import"./CheckBox-e52b9f98.js";import"./Autocomplete-b446b668.js";import"./AccordionDetails-2418f9ae.js";import"./Box-06e0824d.js";import"./InputAdornment-19c51729.js";import"./Backdrop-ef004339.js";import"./DialogActions-98850990.js";import"./DialogContentText-e4d806a4.js";import"./CircularProgress-1acedaf0.js";import"./FormControlLabel-57cd7a82.js";import"./DashboardSetting-970ae243.js";import"./Switch-a9aa0e31.js";import"./Grid-97e89306.js";import"./Zoom-f387e7b2.js";import"./useChangeMaterialRows-cd4e01b9.js";import"./useFinanceCostingRows-699f667f.js";function Ii(){const R=d(e=>e.userManagement.userData),c=R!=null&&R.emailId?R:pe(w.USER_DATA,!0,{});d(e=>e.userManagement.taskData);const{customLog:h}=me(),B=d(e=>e.appSettings.language),N=d(e=>e.applicationConfig);let p=Ie();const o=Re();l.useState({});const[X,j]=l.useState(null),[q,t]=l.useState(!1),[G,P]=l.useState(""),[Q,$]=l.useState(),[z,x]=l.useState(""),[H,We]=l.useState(null),{getDisplayData:K}=Ve(),{showSnackbar:E}=se(),k={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},ee={DateTimeFormat:{dateTimeFormat:"DD MMM YYYY||HH:mm",timeZone:"Asia/Calcutta"}},ie=["localhost","127.0.0.1"].includes(N.environment),oe=e=>{const r={eventId:"TASK_FORWARDING",taskName:e==null?void 0:e.forwardedTasks.map(i=>i.taskDesc).join(","),requestId:e==null?void 0:e.forwardedTasks.map(i=>`${i.ATTRIBUTE_1}`).join(","),recipientGroup:e==null?void 0:e.recipientUsers.map(i=>i.ownerId).join(","),flowType:e==null?void 0:e.forwardedTasks.map(i=>i.ATTRIBUTE_2).join(",")},n=i=>{h(i)};b(`/${F}/mail/sendMail`,"post",n,r)},S=()=>{t(!0)},D=()=>{t(!1)},re=async e=>{var r,n,i,s,m,g,M,f,y,A,C,L,V,W,_,O;te(w.CURRENT_TASK,e);try{if((e==null?void 0:e.taskNature)==="Single-User"||(e==null?void 0:e.taskNature)!=="Single-User"&&(e==null?void 0:e.itmStatus)!=="Open"){if(p(J(e)),(e==null?void 0:e.processDisplayName)==="Material")if(!(e!=null&&e.ATTRIBUTE_1))E(U.FETCHING_REQUEST_ID,"info");else if(!(e!=null&&e.ATTRIBUTE_2))E(U.FETCHING_REQUEST_TYPE,"info");else if(!(e!=null&&e.ATTRIBUTE_5))E(U.FETCH_ROLE,"info");else{const u=await K(e==null?void 0:e.ATTRIBUTE_1,e==null?void 0:e.ATTRIBUTE_2,null,e,null);(u==null?void 0:u.statusCode)===Se.STATUS_200&&o(`/requestBench/createRequest?RequestId=${(e==null?void 0:e.ATTRIBUTE_1)||(e==null?void 0:e.requestId)}&RequestType=${(e==null?void 0:e.ATTRIBUTE_2)||a(e==null?void 0:e.requestId)}`)}(e==null?void 0:e.processDisplayName)===((r=v)==null?void 0:r.BOM)&&o(`/requestBench/bomCreateRequest?RequestId=${(e==null?void 0:e.ATTRIBUTE_1)||(e==null?void 0:e.requestId)}&RequestType=${(e==null?void 0:e.ATTRIBUTE_2)||a(e==null?void 0:e.requestId)}`),(e==null?void 0:e.processDisplayName)===((n=v)==null?void 0:n.ART)&&o(`/requestBench/createArticle?RequestId=${(e==null?void 0:e.ATTRIBUTE_1)||(e==null?void 0:e.requestId)}&RequestType=${(e==null?void 0:e.ATTRIBUTE_2)||a(e==null?void 0:e.requestId)}`),(e==null?void 0:e.processDisplayName)===((i=T)==null?void 0:i.IO)&&o(`${(s=I)==null?void 0:s.CREATE_IO}?RequestId=${(e==null?void 0:e.ATTRIBUTE_1)||(e==null?void 0:e.requestId)}&RequestType=${(e==null?void 0:e.ATTRIBUTE_2)||a(e==null?void 0:e.requestId)}`),(e==null?void 0:e.processDisplayName)===((m=T)==null?void 0:m.BK)&&o(`/${(g=I)==null?void 0:g.CREATE_BK}?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((M=T)==null?void 0:M.PCG)&&o(`/${(f=I)==null?void 0:f.CREATE_PCG}?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((y=T)==null?void 0:y.CCG)&&o(`/${(A=I)==null?void 0:A.CREATE_CCG}?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((C=T)==null?void 0:C.CEG)&&o(`/${(L=I)==null?void 0:L.CREATE_CEG}?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((V=T)==null?void 0:V.BK)&&o(`/requestBench/BankKeyCreateRequest?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((W=T)==null?void 0:W.PC)&&o(`/requestBench/ProfitCenterRequestTab?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)==="General Ledger"&&o(`/requestBench/GeneralLedgerRequestTab?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),(e==null?void 0:e.processDisplayName)===((_=T)==null?void 0:_.CC)&&o(`/requestBench/CostCenterRequestTab?RequestId=${e==null?void 0:e.ATTRIBUTE_1}&RequestType=${e==null?void 0:e.ATTRIBUTE_2}`),p(De({url:window.location.pathname,module:"ITMWorkbench"}))}else $("Kindly claim the task before proceeding"),P("Claim Task"),x("info"),S()}catch{h((O=ge)==null?void 0:O.ERROR_SET_ROLE)}},ce=()=>{console.log("fetchFilterView")},ne=()=>{console.log("clearFilterView")},le=()=>{const e=ie?`/${F}${Z.API.USER_LIST_LOCAL}`:`/${fe}${Z.API.USER_LIST_PROD}`;b(e,"get",r=>{var n=r.data,i=n==null?void 0:n.map(m=>({...m,userId:m==null?void 0:m.businessEmailId})),s={...r,data:i};j({MDG:[...s.data]})})},Te=(e,r)=>{console.log("Success flag.",e),console.log("Task Payload.",r)};return l.useEffect(()=>{ue({}),p(de()),p(ae([])),p(Ne({})),p(J({})),p(Ee([])),le()},[c==null?void 0:c.emailId]),Ue("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:[c&&Y(Ce,{token:"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fPEGXtMLUSCuHS72ExOZlLYsJtt3oSpMTrIpDciN9gmZZrx2tG0yjpjsZw2lccNyzoAssTNpDNlrpEP-gnZYm8vLAYv50E5Z4BjwTiIewEaP6f8z4auy25plytTLjdX_aKb-8BNaxOaSCUkV8fAlvZ8JhiCGe3PYNPjbxlgZeI5mWK7wVLCZK7s8-MpX-zvgtcMKFlWT9BAENuhFt9qU5rA7DEbqbIARbVgnRVNPe_N19f8QpJLYJV56gQaMQK9-ExrXNFTavG_VpAtZt4bJ5YkHuUvQsDIfWwJreOjgtPow3ui_iJs7kA03gxnno1eO26Mu7E3NDohfztI_fRA-tA",configData:Le,destinationData:k,userData:{...c,user_id:c==null?void 0:c.emailId},userPreferences:ee,userPermissions:ye,userList:H,groupList:{},languageTranslationData:Ae(B),userListBySystem:X,useWorkAccess:N.environment==="localhost",useConfigServerDestination:N.environment==="localhost",inboxTypeKey:"MY_TASKS",workspaceLabel:"Open Tasks",workspaceFiltersByAPIDriven:!1,subInboxTypeKey:null,cachingBaseUrl:he,onTaskClick:re,onActionComplete:Te,selectedFilterView:null,isFilterView:!1,fetchFilterViewList:ce,savedFilterViewData:[],clearFilterView:ne,filterViewList:[],selectedTabId:null,forwardTaskData:oe,userProcess:[]}),Y(Me,{dialogState:q,openReusableDialog:S,closeReusableDialog:D,dialogTitle:G,dialogMessage:Q,handleDialogConfirm:D,dialogOkText:"OK",dialogSeverity:z})]})}export{Ii as default};
