import { createSlice } from "@reduxjs/toolkit";
import { action } from "../components/ConfigCockpit/UserManagement/Data/data";

const initialState = {
  dataCheck:[],
  dataLoading: true,
  allTabsData: {},
  basicData: {},
  salesData: [],
  purchasingData: [],
  mrpData: [],
  accountingData: [],
  requestHeaderData:[],
  dataToSend: {
    orgData: [
      {
        info: {
          code: "",
          desc: "",
        },
        desc: "Plant",
      },
      {
        info: {
          code: "",
          desc: "",
        },
        desc: "Sales Organization",
      },
      {
        info: {
          code: "",
          desc: "",
        },
        desc: "Distribution Channel",
      },
    ],
  },
  additionalData: [],
  value: [],
  industrySector: [],
  materialType: [],
  orgData: {},
  tabsStatus: [],
  rolesTabs: {
    Sales: ["Sales"],
    "MDM Steward": [
      "Basic Data",
      "Classification",
      "Purchasing",
      "Sales",
      "Accounting",
      // "Attachments & Comments",
    ],
    Procurement: ["Purchasing"],
    Planning: ["MRP"],
    Approver: [
      "Basic Data",
      "Classification",
      "Purchasing",
      "Sales",
      "Accounting",
    ],
    Finance: ["Accounting"],
    "Super User": [
      "Basic Data",
      "Classification",
      "Purchasing",
      "Sales",
      "Accounting",
    ],
  },
  tabsStatusEffect: true,
  allMaterialFieldConfigDT:[],
  changeFieldsDT:[],
  matViews: {},
  matOdataViews : {},
  articleViews: {},
};

export const tabsDetailsSlice = createSlice({
  name: "tabsData",
  initialState,
  reducers: {
    setDataCheck: (state,action)=>{
      state.dataCheck=action.payload;
    },
    setDataLoading: (state, action) => {
      state.dataLoading = action.payload;
    },
    changeTemplateDT: (state, action) => {
      state.changeFieldsDT = action.payload;
    },
    requestHeaderTabs: (state, action) => {
      state.requestHeaderData = action.payload;
    },
    basicDataTabs: (state, action) => {
      state.basicData = action.payload;
    },
    salesDataTabs: (state, action) => {
      state.salesData = action.payload;
    },
    purchasingDataTabs: (state, action) => {
      state.purchasingData = action.payload;
    },
    mrpDataTabs: (state, action) => {
      state.mrpData = action.payload;
    },
    accountingDataTabs: (state, action) => {
      state.accountingData = action.payload;
    },
    newMaterialData: (state, action) => {
      state.dataToSend = action.payload;
    },
    newAdditionalData: (state, action) => {
      state.additionalData = action.payload;
    },
    newValueForData: (state, action) => {
      state.value = action.payload;
    },
    newValueForIndustrySector: (state, action) => {
      state.industrySector = action.payload;
    },
    newValueForMaterialType: (state, action) => {
      state.materialType = action.payload;
    },
    setOrgData: (state, action) => {
      state.orgData[action.payload.keyName] = action.payload.data;
    },
    clearOrgData: (state) => {
      state.orgData = {};
      state.tabsStatusEffect = true;
    },
    setTabStatus: (state, action) => {
      if (state.tabsStatus.findIndex((item) => item == action.payload) == -1) {
        state.tabsStatus.push({
          keyname: action.payload.keyname,
          status: action.payload.status,
        });
      }
    },
    updateTabStatus: (state, action) => {
      state.tabsStatus[action.payload].status = true;
      return state;
    },
    changeTabStatusEffect: (state, action) => {
      state.tabsStatusEffect = false;
    },
    clearTabsData: (state) => {
      state.basicData = {};
      state.salesData = {};
      state.purchasingData = {};
      state.mrpData = {};
      state.accountingData = {};
      state.dataToSend = [];
    },
    clearTabStatus: (state) => {
      state.tabsStatus = [];
    },
    setRolesTab: (state) => {
      state.rolesTabs = action.payload;
    },
    setAllTabsData : (state,action) =>{
      let payload = action.payload;
      let tabsData = state.allTabsData;
      if(!tabsData) tabsData = {};
      tabsData[payload.tab] = payload.data;
      state.allTabsData = tabsData;
    },
    setMaterialFieldConfig: (state, action) => {
      const existingRegionIndex = state.allMaterialFieldConfigDT.findIndex(
        item => Object.keys(item)[0] === Object.keys(action.payload)[0]
      );
      
      if (existingRegionIndex !== -1) {
        const regionKey = Object.keys(action.payload)[0];
        const existingRegion = state.allMaterialFieldConfigDT[existingRegionIndex];
        state.allMaterialFieldConfigDT[existingRegionIndex] = {
          [regionKey]: {
            ...existingRegion[regionKey],
            ...action.payload[regionKey]
          }
        };
      } else {
        state.allMaterialFieldConfigDT.push(action.payload);
      }
    },
    clearMaterialFieldConfig: (state) => {
      state.allMaterialFieldConfigDT = initialState.allMaterialFieldConfigDT;
    },
    updateAllTabsData : (state,action) => {
      state.allTabsData = action.payload;
    },
    setMatViews: (state,action) =>{
      let payload = action.payload;
      state.matViews[payload.matType] = payload.views;
    },
    setMatOdataViews: (state,action) =>{
      let payload = action.payload;
      state.matOdataViews[payload.matType] = payload.views;
    },
    setArticleViews: (state,action) =>{
      let payload = action.payload;
      state.articleViews[payload.matType] = payload.views;
    }
  },
});

// Action creators are generated for each case reducer function
export const {
  setDataCheck,
  setDataLoading,
  changeTemplateDT,
  requestHeaderTabs,
  basicDataTabs,
  salesDataTabs,
  purchasingDataTabs,
  mrpDataTabs,
  accountingDataTabs,
  newMaterialData,
  newAdditionalData,
  newValueForData,
  newValueForMaterialType,
  newValueForIndustrySector,
  clearTabsData,
  setOrgData,
  setTabStatus,
  updateTabStatus,
  clearOrgData,
  clearTabStatus,
  changeTabStatusEffect,
  setRolesTab,
  setAllTabsData,
  setMaterialFieldConfig,
  updateAllTabsData,
  clearMaterialFieldConfig,
  setMatViews,
  setMatOdataViews,
  setArticleViews
} = tabsDetailsSlice.actions;

export default tabsDetailsSlice.reducer;
