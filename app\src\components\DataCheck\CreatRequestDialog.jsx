import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  FormControl,
  Select,
  MenuItem,
  Typography,
  Button,
  IconButton,
  Divider,
  Box,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import MaterialDropdown from '@components/Common/ui/dropdown/MaterialDropdown';
import LargeDropdown from '@components/Common/ui/dropdown/LargeDropdown';
import DateRange from '@components/Common/DateRangePicker';
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { useSnackbar } from "@hooks/useSnackbar";
import SingleSelectDropdown from '@components/Common/ui/dropdown/SingleSelectDropdown';
import { DATA_CLEANSE_CONSTANTS } from '@constant/enum';
import { END_POINTS } from '@constant/apiEndPoints';

const CreateRequestDialog = ({
  open,
  onClose,
  handleSubmit = () => { },
  title = DATA_CLEANSE_CONSTANTS.CREATE_REQUEST,
  submitButtonText = DATA_CLEANSE_CONSTANTS.INITIATE_REQUEST,
  isLoading = false
}) => {
  const [selectedModule, setSelectedModule] = useState('material');
  const [selectedMaterial, setSelectedMaterial] = useState([]);
  const [selectedMaterialType, setSelectedMaterialType] = useState([]);
  const [selectedMaterialGroup, setSelectedMaterialGroup] = useState([]);
  const [selectedBusinessRule, setSelectedBusinessRule] = useState([]);
  const [createdOnDate, setCreatedOnDate] = useState(null);
  const [rmSearchForm, setRmSearchForm] = useState({ createdOn: null, top: { code: "10", desc: "" } });
  const [materialOptions, setMaterialOptions] = useState([]);
  const [matType, setMatType] = useState([]);
  const [matGroup, setMatGroup] = useState([]);
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [timerId, setTimerId] = useState(null);

  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [profitCenterOptions, setProfitCenterOptions] = useState([]);
  const [glAccountOptions, setGlAccountOptions] = useState([]);
  const [hierarchyOptions, setHierarchyOptions] = useState([]);
  const [selectedCostCenter, setSelectedCostCenter] = useState([]);
  const [selectedProfitCenter, setSelectedProfitCenter] = useState([]);
  const [selectedGLAccount, setSelectedGLAccount] = useState([]);
  const [selectedHierarchy, setSelectedHierarchy] = useState([]);
  const [validationError, setValidationError] = useState('');

  const moduleOptions = [
    { code: 'material', desc: 'Material' },
    { code: 'cost_center', desc: 'Cost Center' },
    { code: 'profit_center', desc: 'Profit Center' },
    { code: 'general_ledger', desc: 'General Ledger' },
    { code: 'hierarchy', desc: 'Hierarchy' }
  ];

  const getBusinessRules = (module) => {
    const businessRulesMap = {
      material: [
        { desc: 'Rule-1', code: 'MAT_MATERIAL_FIELD_CONFIG' },
        { desc: 'Rule-2', code: 'MAT_MATERIAL_TABLE_FIELD_CONFIG' },
        { desc: 'Rule-3', code: 'MAT_MATERIAL_COLUMN_FIELD_CONFIG' },
        { desc: 'Rule-4', code: 'MAT_MATERIAL_SEARCHSCREEN_FIELD_CONFIG' },
        { desc: 'Rule-5', code: 'MAT_MATERIAL_PLANT_FIELD_CONFIG' }
      ],
      cost_center: [
        { code: '1', desc: 'CC_COST_CENTER_CONFIG' },
        { code: '2', desc: 'CC_BUDGET_CONFIG' },
        { code: '3', desc: 'CC_ALLOCATION_CONFIG' }
      ],
      profit_center: [
        { code: '1', desc: 'PC_PROFIT_CENTER_CONFIG' },
        { code: '2', desc: 'PC_REVENUE_CONFIG' },
        { code: '3', desc: 'PC_ANALYSIS_CONFIG' }
      ],
      general_ledger: [
        { code: '1', desc: 'GL_ACCOUNT_CONFIG' },
        { code: '2', desc: 'GL_POSTING_CONFIG' },
        { code: '3', desc: 'GL_BALANCE_CONFIG' }
      ],
      hierarchy: [
        { code: '1', desc: 'HIE_STRUCTURE_CONFIG' },
        { code: '2', desc: 'HIE_LEVEL_CONFIG' },
        { code: '3', desc: 'HIE_RELATIONSHIP_CONFIG' }
      ]
    };
    return businessRulesMap[module] || [];
  };

  const validateMandatoryFields = () => {
    const errors = [];
    if (!selectedBusinessRule || selectedBusinessRule.length === 0) {
      errors.push(DATA_CLEANSE_CONSTANTS?.BR_FAIL);
    }
    if (!rmSearchForm?.top?.code) {
      errors.push(DATA_CLEANSE_CONSTANTS?.NO_FAIL);
    }
    if (!rmSearchForm?.createdOn) {
      errors.push(DATA_CLEANSE_CONSTANTS?.CO_FAIL);
    }
    return errors;
  };

  const font_Small = {
    fontSize: '12px',
    fontWeight: 500
  };

  const LabelTypography = ({ children, sx, required = false }) => (
    <Typography sx={{ ...font_Small, marginBottom: '4px', ...sx }}>
      {children}
      {required && <span style={{ color: 'red', marginLeft: '2px' }}>*</span>}
    </Typography>
  );

  const getMaterialGroup = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      setMatGroup(data.body);
      setIsDropDownLoading(false);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.GET_MATERIAL_GRP}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getMaterialSearch = (inputValue = "") => {
    setIsDropDownLoading(true);
    let payload = {
      materialNo: inputValue,
      salesOrg: "",
      top: 200,
      skip: 0
    };
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setMaterialOptions(data.body);
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.SEARCH_MAT_NO}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  const getMaterialType = () => {
    setIsDropDownLoading(true);
    const hSuccess = (data) => {
      setIsDropDownLoading(false);
      setMatType(data.body);
    };
    const hError = (error) => {
      setIsDropDownLoading(false);
    };
    doAjax(
      `/${destination_MaterialMgmt}${END_POINTS?.DATA_CLEANSE_APIS?.GET_MAT_TYPE}`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCostCenterData = () => {
    setCostCenterOptions([
      { code: '1', desc: 'Cost Center 1' },
      { code: '2', desc: 'Cost Center 2' },
      { code: '3', desc: 'Cost Center 3' }
    ]);
  };

  const getProfitCenterData = () => {
    setProfitCenterOptions([
      { code: '1', desc: 'Profit Center 1' },
      { code: '2', desc: 'Profit Center 2' },
      { code: '3', desc: 'Profit Center 3' }
    ]);
  };

  const getGLAccountData = () => {
    setGlAccountOptions([
      { code: '1', desc: 'GL Account 1' },
      { code: '2', desc: 'GL Account 2' },
      { code: '3', desc: 'GL Account 3' }
    ]);
  };

  const getHierarchyData = () => {
    setHierarchyOptions([
      { code: '1', desc: 'Hierarchy Level 1' },
      { code: '2', desc: 'Hierarchy Level 2' },
      { code: '3', desc: 'Hierarchy Level 3' }
    ]);
  };

  const handleMatInputChange = (e) => {
    const inputValue = e.target.value;
    if (timerId) {
      clearTimeout(timerId);
    }

    if (inputValue.length >= 4) {
      const newTimerId = setTimeout(() => {
        getMaterialSearch(inputValue);
      }, 500);
      setTimerId(newTimerId);
    }
  };

  const handleDate = (date) => {
    setRmSearchForm({ ...rmSearchForm, createdOn: date });
  };

  const handleInputChange = (fieldName, value) => {
    if (value !== null) {
      const tempData = value;

      let tempFilterData = {
        ...rmSearchForm,
        [fieldName]: tempData,
      };

      setRmSearchForm(tempFilterData)
    }
  };

  const handleModuleChange = (module) => {
    setSelectedModule(module);
    handleClear();
    switch (module) {
      case 'material':
        getMaterialGroup();
        getMaterialType();
        break;
      case 'cost_center':
        getCostCenterData();
        break;
      case 'profit_center':
        getProfitCenterData();
        break;
      case 'general_ledger':
        getGLAccountData();
        break;
      case 'hierarchy':
        getHierarchyData();
        break;
      default:
        break;
    }
  };

  const handleClear = () => {
    setSelectedMaterial([]);
    setSelectedMaterialType([]);
    setSelectedMaterialGroup([]);
    setSelectedBusinessRule([]);
    setCreatedOnDate(null);
    setSelectedCostCenter([]);
    setSelectedProfitCenter([]);
    setSelectedGLAccount([]);
    setSelectedHierarchy([]);
    setRmSearchForm({ createdOn: null, top: { code: "10", desc: "" } })
    setValidationError(''); 
  };

  const handleClose = () => {
    handleClear();
    onClose();
    setSelectedModule("material")
    setValidationError(''); 
  };
  const topOptions = [
    { code: '10', desc: "" },
    { code: '20', desc: "" },
    { code: '50', desc: "" },
    { code: '100', desc: "" },
    { code: '200', desc: "" },
    { code: '500', desc: "" },
    { code: '1000', desc: "" },
  ];

  const renderModuleFilters = () => {
    switch (selectedModule) {
      case 'material':
        return (
          <>
            <Grid item xs={12} md={6}>
              <LabelTypography>{DATA_CLEANSE_CONSTANTS.MATERIAL}</LabelTypography>
              <FormControl size="small" fullWidth>
                <MaterialDropdown
                  matGroup={materialOptions}
                  selectedMaterialGroup={selectedMaterial}
                  setSelectedMaterialGroup={setSelectedMaterial}
                  isDropDownLoading={isDropDownLoading}
                  placeholder={DATA_CLEANSE_CONSTANTS.SELECT_MATERIAL}
                  onInputChange={handleMatInputChange}
                  minCharacters={4}
                />
              </FormControl>
            </Grid>

            {/* Material Type */}
            <Grid item xs={12} md={6}>
              <LabelTypography>{DATA_CLEANSE_CONSTANTS.MATERIAL_TYPE}</LabelTypography>
              <LargeDropdown
                matGroup={matType}
                selectedMaterialGroup={selectedMaterialType}
                setSelectedMaterialGroup={setSelectedMaterialType}
                placeholder={DATA_CLEANSE_CONSTANTS.SELECT_MATERIAL_TYPE}
              />
            </Grid>

            {/* Material Group */}
            <Grid item xs={12} md={6}>
              <LabelTypography>{DATA_CLEANSE_CONSTANTS.MATERIAL_GROUP}</LabelTypography>
              <LargeDropdown
                matGroup={matGroup}
                selectedMaterialGroup={selectedMaterialGroup}
                setSelectedMaterialGroup={setSelectedMaterialGroup}
                placeholder={DATA_CLEANSE_CONSTANTS.SELECT_MATERIAL_GROUP}
              />
            </Grid>
          </>
        );

      case 'cost_center':
        return (
          <>
            {/* Cost Center */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Cost Center</LabelTypography>
              <LargeDropdown
                matGroup={costCenterOptions}
                selectedMaterialGroup={selectedCostCenter}
                setSelectedMaterialGroup={setSelectedCostCenter}
                placeholder={DATA_CLEANSE_CONSTANTS.SELECT_COST_CENTER}
              />
            </Grid>

            {/* Cost Center Category */}
            <Grid item xs={12} md={6}>
              <LabelTypography>{DATA_CLEANSE_CONSTANTS.CATEGORY}</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}
                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Category
                  </MenuItem>
                  <MenuItem value="production">Production</MenuItem>
                  <MenuItem value="administration">Administration</MenuItem>
                  <MenuItem value="sales">Sales & Marketing</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Budget Amount */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Budget Range</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}
                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Budget Range
                  </MenuItem>
                  <MenuItem value="0-10000">0 - 10,000</MenuItem>
                  <MenuItem value="10000-50000">10,000 - 50,000</MenuItem>
                  <MenuItem value="50000+">50,000+</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </>
        );

      case 'profit_center':
        return (
          <>
            {/* Profit Center */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Profit Center</LabelTypography>
              <LargeDropdown
                matGroup={profitCenterOptions}
                selectedMaterialGroup={selectedProfitCenter}
                setSelectedMaterialGroup={setSelectedProfitCenter}
                placeholder="Select Profit Center"
              />
            </Grid>

            {/* Revenue Type */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Revenue Type</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}
                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Revenue Type
                  </MenuItem>
                  <MenuItem value="primary">Primary Revenue</MenuItem>
                  <MenuItem value="secondary">Secondary Revenue</MenuItem>
                  <MenuItem value="other">Other Revenue</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Profit Margin */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Profit Margin Range</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}
                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Margin Range
                  </MenuItem>
                  <MenuItem value="0-10">0% - 10%</MenuItem>
                  <MenuItem value="10-25">10% - 25%</MenuItem>
                  <MenuItem value="25+">25%+</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </>
        );

      case 'general_ledger':
        return (
          <>
            {/* GL Account */}
            <Grid item xs={12} md={6}>
              <LabelTypography>GL Account</LabelTypography>
              <LargeDropdown
                matGroup={glAccountOptions}
                selectedMaterialGroup={selectedGLAccount}
                setSelectedMaterialGroup={setSelectedGLAccount}
                placeholder="Select GL Account"
              />
            </Grid>

            {/* Account Type */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Account Type</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}

                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Account Type
                  </MenuItem>
                  <MenuItem value="asset">Asset</MenuItem>
                  <MenuItem value="liability">Liability</MenuItem>
                  <MenuItem value="equity">Equity</MenuItem>
                  <MenuItem value="revenue">Revenue</MenuItem>
                  <MenuItem value="expense">Expense</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Balance Type */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Balance Type</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}

                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Balance Type
                  </MenuItem>
                  <MenuItem value="debit">Debit Balance</MenuItem>
                  <MenuItem value="credit">Credit Balance</MenuItem>
                  <MenuItem value="zero">Zero Balance</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </>
        );

      case 'hierarchy':
        return (
          <>
            {/* Hierarchy Level */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Hierarchy Level</LabelTypography>
              <LargeDropdown
                matGroup={hierarchyOptions}
                selectedMaterialGroup={selectedHierarchy}
                setSelectedMaterialGroup={setSelectedHierarchy}
                placeholder="Select Hierarchy Level"
              />
            </Grid>

            {/* Parent Node */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Parent Node</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}

                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Parent Node
                  </MenuItem>
                  <MenuItem value="root">Root</MenuItem>
                  <MenuItem value="branch1">Branch 1</MenuItem>
                  <MenuItem value="branch2">Branch 2</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Node Type */}
            <Grid item xs={12} md={6}>
              <LabelTypography>Node Type</LabelTypography>
              <FormControl size="small" fullWidth>
                <Select
                  value=""
                  onChange={() => { }}
                  displayEmpty
                  sx={{ fontSize: '12px' }}

                  MenuProps={{
                    disablePortal: false,
                    PaperProps: {
                      style: {
                        zIndex: 1302,
                        maxHeight: 200
                      }
                    }
                  }}
                >
                  <MenuItem value="" disabled>
                    Select Node Type
                  </MenuItem>
                  <MenuItem value="parent">Parent Node</MenuItem>
                  <MenuItem value="child">Child Node</MenuItem>
                  <MenuItem value="leaf">Leaf Node</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    if (open) {
      getMaterialGroup();
      getMaterialType();
    }
  }, [open]);

  useEffect(() => {
    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
    };
  }, [timerId]);

  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            minHeight: '400px',
            overflow: 'visible'
          }
        }}
        sx={{
          '& .MuiDialog-container': {
            overflow: 'visible'
          },
          '& .MuiDialog-paper': {
            overflow: 'visible'
          }
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            pb: 1
          }}
        >
          <Typography variant="h6" component="div">
            {title}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Module Selector */}
            <FormControl size="small" sx={{ minWidth: 150 }}>
              <Select
                value={selectedModule}
                onChange={(e) => handleModuleChange(e.target.value)}
                sx={{ fontSize: '12px' }}

                MenuProps={{
                  disablePortal: false,
                  PaperProps: {
                    style: {
                      zIndex: 1302,
                      maxHeight: 200
                    }
                  }
                }}
              >
                {moduleOptions.map((module) => (
                  <MenuItem key={module.code} value={module.code}>
                    {module.desc}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <IconButton
              edge="end"
              color="inherit"
              onClick={handleClose}
              aria-label="close"
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <Divider />

        <DialogContent sx={{ pt: 3, overflow: 'visible' }}>
          <Grid container spacing={3}>
            {renderModuleFilters()}

            {/* Business Rule - Multiselect with mandatory indicator */}
            <Grid item xs={12} md={6}>
              <LabelTypography required>{DATA_CLEANSE_CONSTANTS.BUSINESS_RULE}</LabelTypography>
              <LargeDropdown
                matGroup={getBusinessRules(selectedModule)}
                selectedMaterialGroup={selectedBusinessRule}
                setSelectedMaterialGroup={setSelectedBusinessRule}
                placeholder={DATA_CLEANSE_CONSTANTS.SELECT}
              />
            </Grid>

            {/* Number of Objects - Mandatory */}
            <Grid item xs={12} md={6}>
              <LabelTypography required>{DATA_CLEANSE_CONSTANTS.NO_OBJECTS}</LabelTypography>
              <FormControl size="small" fullWidth>
                <SingleSelectDropdown
                  options={topOptions}
                  value={rmSearchForm?.top?.code}
                  onChange={(value) => handleInputChange("top", value)}
                  placeholder={"Select Number of Objects"}
                  disabled={false}
                  minWidth="90%"
                  listWidth={210}
                />
              </FormControl>
            </Grid>

            {/* Created On Date Range - Mandatory */}
            <Grid item xs={12} md={6}>
              <LabelTypography required>Created On</LabelTypography>
              <FormControl fullWidth sx={{ padding: 0 }}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DateRange
                    handleDate={handleDate}
                    date={rmSearchForm?.createdOn}
                  />
                </LocalizationProvider>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>

        <Divider />
        <DialogActions sx={{ p: 2, gap: 1, flexDirection: 'column' }}>
          {validationError && (
            <Alert severity="error" sx={{ width: '100%', mb: 1 }}>
              {validationError}
            </Alert>
          )}
          <Box sx={{ display: 'flex', gap: 1, width: '100%', justifyContent: 'flex-end' }}>
            <Button
              onClick={handleClear}
              variant="outlined"
              color="warning"
              disabled={isLoading}
            >
              Clear
            </Button>
            <Button
              onClick={() => {
                const errors = validateMandatoryFields();
                if (errors.length > 0) {
                  setValidationError(errors.join(', '));
                  return;
                }
                setValidationError('');
                handleSubmit(rmSearchForm);
              }}
              variant="contained"
              color="primary"
              disabled={isLoading}
              sx={{
                minWidth: '120px'
              }}
            >
              {isLoading ? 'Creating...' : submitButtonText}
            </Button>
          </Box>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CreateRequestDialog;