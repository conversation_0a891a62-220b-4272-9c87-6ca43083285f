import { destination_ArticleMgmt, destination_MaterialMgmt ,destination_ProfitCenter_Mass, destination_CostCenter_Mass, destination_BankKey, destination_GeneralLedger_Mass, destination_InternalOrder } from "../destinationVariables";

export const API_CODE = {
  STATUS_200: 200,
  STATUS_201: 201,
  STATUS_202: 202,
  STATUS_300: 300,
  STATUS_429: 429,
  STATUS_204: 204,
  STATUS_0: 0,
  STATUS_414: 414,
  STATUS_423: 423,
  STATUS_500: 500,
  STATUS_501: 501,
  STATUS_SUCCESS:'success'
};
export const MODULE = {
  PCG: "PCG",
  CCG: "CCG",
  CEG: "CEG",
  CC: "CostCenter",
  GL: "GeneralLedger",
  BOM: "BOM",
  IO: "InternalOrder",
  BK: "BankKey",
};

export const MATERIAL_TABLE = {
  MATERIALNUMBER: "materialNumber",
  GLOBALMATERIALDESCRIPTION: "globalMaterialDescription",
  MATERIALTYPE: "materialType",
  INCLUDED: "included",
  VIEWS: "views",
};

export const ARTICLE_TABLE = {
  MATERIALNUMBER: "materialNumber",
  GLOBALMATERIALDESCRIPTION: "globalMaterialDescription",
  COMPONENTLIST:"componentList",
  MATERIALTYPE: "materialType",
  INCLUDED: "included",
  VIEWS: "views",
};

export const MATERIAL_VIEWS = {
  BASIC_DATA: "Basic Data",
  SALES: "Sales",
  MRP: "MRP",
  PURCHASING: "Purchasing",
  ACCOUNTING: "Accounting",
  CLASSIFICATION: "Classification",
  PLANT_STOCKS: "Plant stocks",
  PRODUCTION: "Production",
  QUALITY_MANAGEMENT: "Quality management",
  WAREHOUSE_MANAGEMENT: "Warehouse management",
  WORK_SCHEDULING: "Work scheduling",
  WORK_SCHEDULING_2: "Work Scheduling",
  COSTING: "Costing",
  STORAGE: "Storage",
  STORAGE_LOCATION_STOCKS: "Storage location stocks",
  FORCASTING: "Forcasting",
  DESCRIPTION: "Description",
  SALES_PLANT: "Sales-Plant",
  SALES_GENERAL: "Sales-General",
  PURCHASING_GENERAL: "Purchasing-General",
  ADDITIONAL_DATA: "AdditionalData",
  ADDITIONAL_EAN_DATA: "AdditionalEANData",
  WAREHOUSE: "Warehouse",
  WORKSCHEDULING: "Work Scheduling",
  CHANGE_LOG_WORK_SCHEDULING: "WorkScheduling",
  TAXCLASSIFICATION: "TaxClassification",
  TAX_DATA: "TaxData",
  FINANCE_COST_DATA: "FinanceCostData",
  BOM: 'BOM',
  SOURCE_LIST: 'Source List',
  PIR: 'PIR',
  STORAGE: 'Plant Data|Storage',
  STORAGE_PLANT: 'Plant Data|Storage-Plant',
  STORAGE_GENERAL: 'Plant Data|Storage General',
  SUPPLIER_FORM: 'Supplier Form',
  LISTING: 'Listing',
  POS: 'POS',
  LOGISTIC_DC: 'Logistics: DC',
  LOGISTIC_STORE: 'Logistics: Store',
  SALES_DATA : 'Sales Data',
  PURCHASING_DATA : 'Purchasing Data',
  CHARACTERISTIC : 'Characteristic',
  MERCHANT_INPUT : 'Merchant Input',
};

export const MATERIAL_VIEWS_GENERAL_VIEWS = {
  SALES_GENERAL: "Sales-General",
  PURCHASING_GENERAL: "Purchasing-General",
  STORAGE_GENERAL: 'Plant Data|Storage General',
}

export const DIALOUGE_BOX_MESSAGES = {
  LEAVE_PAGE_MESSAGE:
    "You have unsaved changes. Are you sure you want to leave?",
  DRAFT_MESSAGE: "Are you sure! You really want to save as draft?",
  DELETE_MESSAGE: "Are you sure! You really want to delete?",
  CANCEL_MESSAGE: "Are you sure! You really want to cancel?",
  ERROR_MSG: "Failed to fetch data",
  MATL_ERROR_MSG: "Material must be selected from the available options",
  LANG_ERROR_MSG: "The combination of Material and Language must be unique",
  ALTUNIT_ERROR_MSG:
    "The combination of Material and Alternative Unit of Measure must be unique",
  UPLOAD_FAILED: "Attachment Upload Failed. Please Try Again",
  ALT_UPLOAD_ERROR:
    "Oops! Sorry We Ran In Some Issue, Please Try After Sometime",
  MAX_CANCEL_LIMIT: "Maximum 10 rows can be cancelled at once",
  CANCEL_SUCCESS: "Requests Cancelled Successfully",
  CANCEL_FAILED: "Failed Cancelling Requests",
  FIN_TASK_PREFIX: "Finance Task created with Request Id FCA",
  SAVE_AS_DRAFT_MSG:
    "The following materials will be Saved as Draft for the Request !",
  VALIDATE_MSG:
    "The following materials will be processed further in the Request !",
};

export const VISIBILITY_TYPE = {
  MANDATORY: "Mandatory",
  OPTIONAL: "Optional",
  DISPLAY: "Display",
  REQUIRED: "Required",
  ENABLED: "Enabled",
  HIDDEN: 'Hidden',
  HEADER: 'Header',
  HIDDEN1: 'Hidden1'
};

export const FIELD_VISIBILITY = {
  MANDATORY: "MANDATORY",
};

export const REQUEST_TYPE = {
  CREATE: "Create",
  CHANGE: "Change",
  EXTEND: "Extend",
  CREATE_WITH_UPLOAD: "Create with Upload",
  CHANGE_WITH_UPLOAD: "Change with Upload",
  EXTEND_WITH_UPLOAD: "Extend with Upload",
  FINANCE_COSTING: "Finance Costing",
};

export const prefixMap = {
  Create: "NMA",
  Change: "CMA",
  Extend: "EMA",
  "Create with Upload": "NME",
  "Change with Upload": "CME",
  "Extend with Upload": "EME",
};

export const MODULE_MAP = {
  MAT: "Material",
  PCG: "Profit Center Group",
  CCG: "Cost Center Group",
  CEG: "Cost Element Group",
  CC: "Cost Center",
  PC: "Profit Center",
  GL: "General Ledger",
  IO: "Internal Order",
  BOM: "BillOfMaterial",
  BK: "Bank Key",
  ART: "Article"
};

export const MODULE_OPTIONS=[
  'Material',
  'Profit Center',
  'Cost Center',
  'General Ledger',
]

export const DROP_DOWN_SELECT_OR_MAP = {
  [MODULE_MAP.PC]: (state) => state.profitCenterDropdownData?.dropDown || {},
  [MODULE_MAP.PCG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CCG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CEG]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.CC]: (state) => state.costCenterDropDownData?.dropDown || {},
  [MODULE_MAP.PC]: (state) => state.AllDropDown?.dropDown || {},
  [MODULE_MAP.GL]: (state) => state.generalLedgerDropDownData?.dropDown || {},
  [MODULE_MAP.IO]: (state) => state.internalOrder?.dropDownDataIO || {},

  [MODULE_MAP.BOM]: (state) => state.bom?.dropDownData || {},
  [MODULE_MAP.BK]: (state) => state.bankKey?.dropDownData || {},
};
export const MODULE_PAYLOAD_DATA = {
  [MODULE.PC]: (state) => state.profitCenter.payload?.rowsBodyData || {},
  [MODULE.CC]: (state) => state.costCenter.payload?.rowsBodyData || {},
  [MODULE.GL]: (state) => state.generalLedger.payload?.rowsBodyData || {},
  [MODULE.IO]: (state) => state.internalOrder?.payload?.rowsBodyData || {},
  [MODULE.BOM]: (state) => state.bom?.payload?.rowsBodyData || {},
  [MODULE.BK]: (state) => state.bankKey?.payload?.rowsBodyData || {},
};
export const OBJECT_MUMBER_KEY = {
  [MODULE.PC]: "Profitcenter",
  [MODULE.CC]: "Costcenter",
  [MODULE.GL]: "GeneralLedger",
  [MODULE.BK]: "BankKey"
}

export const INITIAL_PAYLOAD_MAP = {
  [MODULE_MAP.MAT]: (state) => state.payload.payloadData?.data || state.payload.payloadData || {},
  [MODULE_MAP.PC]: (state) => state.profitCenter.payload.requestHeaderData || {},
  [MODULE_MAP.PCG]: (state) => state.hierarchyData?.requestHeaderData || {},
  [MODULE_MAP.CCG]: (state) => state.hierarchyData?.requestHeaderData || {},
  [MODULE_MAP.CEG]: (state) => state.hierarchyData?.requestHeaderData || {},
  [MODULE_MAP.CC]: (state) => state.costCenter.payload.requestHeaderData || {},
  [MODULE_MAP.GL]: (state) => state?.generalLedger?.payload?.requestHeaderData || {},
  [MODULE_MAP.BK]: (state) => state.bankKey?.payload?.requestHeaderData || {},
  [MODULE_MAP.BOM]: (state) => state.bom?.BOMpayloadData || {},
  [MODULE_MAP.IO]: (state) =>state.internalOrder?.IOpayloadData?.requestHeaderData || {},
  [MODULE_MAP.ART] : (state) => state.payload.payloadData?.data || state.payload.payloadData || {},
};

export const REQUEST_HEADER_MAP = {
  [MODULE_MAP.MAT]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.PC]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.PCG]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.CCG]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.CC]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.PC]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.GL]: (state) => state.tabsData.requestHeaderData || {},
  [MODULE_MAP.BK]: (state) => state.bankKey.headerFieldsBnky || {},
  [MODULE_MAP.BOM]: (state) => state.bom.headerFieldsBOM || {},
  [MODULE_MAP.IO]: (state) => state.internalOrder.requestHeaderDTIO || {},
   [MODULE_MAP.BK]: (state) => state.bankKey.headerFieldsBnky || {},
   [MODULE_MAP.ART] : (state) => state.tabsData.requestHeaderData || {},
}

export const VALIDATION_STATUS = {
  default: "default",
};

export const BUTTON_NAME = {
  SAVE: "save",
  SAVE_AS_DRAFT: "Save As Draft",
  VALIDATE: "Validate",
  SEND_BACK: "Send Back",
  CORRECTION: "Correction",
  SUBMIT: 'Submit',
  SAP_SYNDICATE: "SAP Syndication",
  APPROVE:"APPROVE"
};

export const BUTTON_TYPE = {
  
  SAVE_AS_DRAFT: "SAVE_AS_DRAFT",
  VALIDATE: "VALIDATE",
  SEND_BACK: "SEND_BACK",
  CORRECTION: "CORRECTION",
  SUBMIT: 'SUBMIT_FOR_REVIEW',
  SAP_SYNDICATE: "SYNDICATE",
  APPROVE:"APPROVE"
};

export const BUTTON_JSON_NAME = {
  CORRECTION: "CorrectionMdm",
  SEND_BACK: "SendBackI",
  SAP_SYNDICATE: "FinalSyndication"
}

export const REQUEST_STATUS = {
  DRAFT: "Draft",
  DRAFT_IN_CAPS: "DRAFT",
  PENDING_DATA_ENTRY: "Data Entry Pending",
  REJECTED: "Rejected",
  APPROVAL_PENDING: "Approval Pending",
  VALIDATED_SUCCESS: "Validated Successfully",
  APPROVED: "Approved",
  CANCELED: "Canceled",
  SENT_BACK_BY_INTERMEDIATE_USER: "Sent Back By Intermediate User",
  SENT_BACK_BY_MDM_TEAM: "Sent Back By MDM Team",
  SUBMITTED_FOR_REVIEW: "Submitted For Review",
  SYNDICATED_IN_SAP: "Syndicated In SAP",
  SYNDICATED_IN_SAP_DIRECT: "Syndicated In SAP(Direct)",
  SYNDICATION_FAILED: "Syndication Failed",
  SYNDICATION_FAILED_DIRECT: "Syndication Failed(Direct)",
  SYNDICATED_PARTIALLY: "Syndicated Partially",
  SYNDICATED_PARTIALLY_DIRECT: "Syndicated Partially(Direct)",
  SYNDICATED_PARTIALLY_DIRECT_HYPHEN: "Syndicated-Partially(Direct)",
  UPLOAD_FAILED: "Upload Failed",
  UPLOAD_SUCCESSFUL: "Upload Successful",
  VALIDATED_MDM: "Validated-MDM",
  VALIDATED_MDM_DIRECT: "Validated-MDM(Direct)",
  VALIDATION_FAILED_MDM: "Validation Failed-MDM",
  APPROVER_SLA_EXCEEDED: "Approver SLA Exceeded",
  DATA_OWNER_SLA_EXCEEDED: "Data Owner SLA Exceeded",
  MDM_STEWARD_SLA_EXCEEDED: "MDM Steward SLA Exceeded",
  CORRECTION_PENDING: "Correction Pending",
  VALIDATED_REQUESTOR: "Validated-Requestor",
  VALIDATION_FAILED_REQUESTOR: "Validation Failed-Requestor",
  ENABLE_FOR_FIRST_TIME: "Enable For First Time",
  VALIDATION_FAILED_MDM_DIRECT: "Validation Failed-MDM(Direct)",
  SCHEDULED_FOR_SYNDICATION: "Scheduled For Syndication",
  CANCELLED: "Canceled",
  COMPLETED: "Completed",
  IN_PROGRESS: "In Progress",
  FORM_TO_SUPPLIER:"Form To Supplier",
  MERCHAN: "Merchandising Admin Review",
  BRAND_CREATION:"Brand Creation",
  MDG_CR_CREATION:"MDG CR Creation",
  ARTICLE_ENRICHMENT:"Article Enrichment",
  ARTICLE_ACTIVATION_S4:"Article Activation S4",
  SOURCE_LIST_CREATION:"Source List Creation",
  PROCESS_COMPLETED:"Process Completed"
};

export const CHANGE_CC_INFO = {
  "All Other Fields": "Change All Other Fields or Unblock",
  "Change PC On CC": "Profit Center Change",
  "Address Change": "Change Address Fields",
  "Block": "Block",
}

export const ENABLE_STATUSES = [
  REQUEST_STATUS.DRAFT,
  REQUEST_STATUS.DRAFT_IN_CAPS,
  REQUEST_STATUS.VALIDATED_REQUESTOR,
  REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR,
  REQUEST_STATUS.UPLOAD_FAILED,
  REQUEST_STATUS.UPLOAD_SUCCESSFUL,
  REQUEST_STATUS.SYNDICATION_FAILED_DIRECT,
  REQUEST_STATUS.SYNDICATED_PARTIALLY_DIRECT_HYPHEN,
  REQUEST_STATUS.ENABLE_FOR_FIRST_TIME,
  REQUEST_STATUS.VALIDATION_FAILED_MDM_DIRECT,
  REQUEST_STATUS.VALIDATED_MDM_DIRECT,
];
export const DISABLE_STATUSES = [
  REQUEST_STATUS.SYNDICATED_IN_SAP,
  REQUEST_STATUS.SYNDICATED_IN_SAP_DIRECT,
];
export const CHANGE_LOG_STATUSES = [
  REQUEST_STATUS.DRAFT,
  REQUEST_STATUS.VALIDATED_REQUESTOR,
  REQUEST_STATUS.VALIDATION_FAILED_REQUESTOR,
  REQUEST_STATUS.UPLOAD_FAILED,
];

export const CHANGE_KEYS = {
  MATERIAL_TYPE: "Material Type",
  MATERIAL_NUM: "Material Number",
  DIVISION: "Division",
  PLANT: "Plant",
  MRP_CTRLER: "MRP Controller",
  WAREHOUSE: "Warehouse",
  SALES_ORG: "Sales Org",
  DIST_CHNL: "Distribution Channel",
  STORAGE_LOC: "Storage Location",
  CTRL_AREA_PCG: "Controlling Area",
  PRCTR_GRP: "Profit Center Group",
  PRCTR_GRP_DESC: "Profit Center Group Description",
  CCCTR_GRP: "Cost Center Group",
  COA: "Chart Of Account",
  CCCTR_GRP_DESC: "Cost Center Group Description",
  CECTR_GRP: "Cost Element Group",
  CECTR_GRP_DESC: "Cost Element Group Description",
};

export const MODULE_KEY_MAP = {
  PCG: {
    COA: CHANGE_KEYS?.COA,
    CTR_GRP: CHANGE_KEYS?.PRCTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.PRCTR_GRP_DESC,
  },
  CCG: {
    COA: CHANGE_KEYS?.COA,
    CTR_GRP: CHANGE_KEYS?.CCCTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.CCCTR_GRP_DESC,
  },
  CEG: {
    COA: CHANGE_KEYS?.COA,
    CTR_GRP: CHANGE_KEYS?.CECTR_GRP,
    CTRL_AREA: CHANGE_KEYS?.CTRL_AREA_PCG,
    CTR_GRP_DESC: CHANGE_KEYS?.CECTR_GRP_DESC,
  },
};

export const DELETE_MODAL_BUTTONS_NAME = {
  DELETE: "Yes",
  CANCEL: "Cancel",
};
export const DESTINATION = {
  DEST_IDM: "cw-oauth2-idm",
};

export const DESTINATION_FIN = {
  PC: "destination_ProfitCenter_Mass",
  CC: "destination_CostCenter_Mass",
};

export const DECISION_TABLE_NAME = {
  MDG_MAT_REGION_DIVISION_MAPPING: "MDG_MAT_REGION_DIVISION_MAPPING",
  MDG_MAT_IAS_GROUP_ROLE_MAPPING: "MDG_MAT_IAS_GROUP_ROLE_MAPPING",
  MDG_MAT_SEARCHSCREEN_COLUMN: 'MDG_MAT_SEARCHSCREEN_COLUMN',
  MDG_MAT_SEARCHSCREEN_PARAMETER: "MDG_MAT_SEARCHSCREEN_PARAMETER",
  MDG_MAT_MATERIAL_FIELD_CONFIG: "MDG_MAT_MATERIAL_FIELD_CONFIG",
  MDG_MAT_REQUEST_HEADER_CONFIG: "MDG_MAT_REQUEST_HEADER_CONFIG",
  MDG_MAT_BOM_CONFIG: "MDG_BOM_MATERIAL_FIELD_CONFIG",
  MDG_MAT_BOM_BUTTONS: "MDG_MAT_DYN_BUTTON_CONFIG",
  MDG_MAT_ATTACHMENT_CONFIG: "MDG_MAT_ATTACHMENT_CONFIG",
  MDG_FMD_REQUEST_HEADER_CONFIG: "MDG_FMD_REQUEST_HEADER_CONFIG",
  MDG_BNKY_FIELD_CONFIG: "MDG_BNKY_FIELD_CONFIG",
  DICISIONTABLENAME: 'MDG_MAT_MATERIAL_BIFURCATION_DT',
};

export const COLUMN_FIELD_TYPES = {
  MULTIPLE: 'Multiple',
  DATE: 'Date',
  SINGLE: 'Single',
  STATUS: 'Status',
  DOCUMENTTYPE: 'DocumentType'
}

export const SEARCH_FIELD_TYPES = {
  INPUT: 'Input',
  CALENDAR: 'Calendar',
  REQUESTTYPE: 'reqType',
  REQUESTPRIORITY: 'priorityLvl',
  CREATEDBYUSER: 'createdBy',
  REQSTATUS: 'reqStatus',
  DIVISION: 'Division',
  MATERIALNOS: 'objectNumbers',
  TEMPLATENAME: 'template',
  DOCTYPE: 'docType',
  ATTACHMENTTYPE: 'attachmentType',
  UPLOADEDBY: 'uploadedBy',
  UPLOADEDDATE: 'uploadedDate',

  CREATEDON: 'createdOn',
  PURSTATUS: 'PurStatus',
  DISTRIBUTIONCHANNEL: 'distributionChannel',
  MATERIALGROUP: 'materialGroup',
  MATERIALTYPE: 'materialType',
  NUMBER: 'number',
  PLANT: 'plant',
  SALESORG: 'salesOrg',
  REGION: 'Region',
  COMPANYCODE: 'companyCode',
  COSTCENTER: 'costCenter',
  CONTROLINGAREA: 'controllingArea',
  LONGDESC: 'description',
  COSTCENTERCAT: 'costCenterCategory',
  PERSONRES: 'personResponsible',
  USERRES: 'userResponsible',
  BLOCKINGSAT: 'blockingStatus',
  CREATEDBY: 'createdBy',
  COUNTRY: 'country',
  STREET: 'street',
  SEGMENT: 'segment',
  PROFITCENTERNAME: 'profitCenterName',
  REGIONPC: 'region',
  CITY: 'city',
  LONGTEXT: 'longText',
  PROFITCENTER: 'profitCenter',
  RECONACC: 'reconAccountforAcctType',
  OPENMNGE: 'openItemManagement',
  OPENIMNGELG: 'openItemMgmtbyLedgerGroup',
  FIELDSTATUS: 'fieldStatusGroup',
  GLACCOUNT: 'glAccountType',
  SHORTTEXT: 'shortText',
  ACCGROUP: 'accountGroup',
  POSTONLY: 'postAutoOnly',
  BLOKEDPOSTCOMP: 'blockedForPostingInCompany',
  BLOCKEDPOSTCOA: 'blockedForPostingInCOA',
  POSTWITHOUTTAX: 'postingWithoutTaxAllowed',
  TAXCATO: 'taxCategory',
  LONGGLTEXT: 'glAcctLongText',
  GLACC: 'glAccount',
  CHARTOFACC: 'chartOfAccount',
  CCGROUP: 'node',
  BANKNO: 'BankNo',
  CITY: 'City',
  STREETLNG: 'StreetLng',
  BANKBRANCH: 'BankBranch',
  BANKCTRY: 'BankCtry',
  BANKNAME: 'BankName',
  BANKKEYY: 'BankKey',
  ALTERBOM: 'AlternativeBOM',
  DOCUMENT: 'Document',
  COMPO: 'Component',
  BOMUSAGE: 'BOMUsage',
  PLANTBOM: 'Plant',
  MATERIALBOM: 'Material',
  OBJCLASS: 'Objectclass',
  REQCC: 'RequestCctr',
  RESCC: 'Respcctr',
  WBSELE: 'WbsElement',
  GLACCIO: 'GlAccount',
  PROFITCEN: 'ProfitCtr',
  FUNAREA: 'FuncArea',
  PLANTIO: 'Plant',
  CURRENCYIO: 'Currency',
  CCIO: 'Costcenter',
  COMPANYCODEIO: 'CompCode',
  ORDERNAME: 'OrderName',
  ORDER: 'Order',
  ORDERTYPE: 'OrderType',
  CONTROAREA: 'CoArea'


}

export const SEARCH_CONFIG_BY_TAB = {
  0: {
    destination: destination_ArticleMgmt,
    endpoint: "/data/getSearchParamMaterial",
    payloadKey: "material"
  },
  1: {  
    destination: destination_MaterialMgmt,
    endpoint: "/data/getSearchParamMaterial",
    payloadKey: "material"
  },
  2: {  
    destination: destination_ProfitCenter_Mass,
    endpoint: "/data/getProfitCenterLookUp",
    payloadKey: "profitCenter"
  },
  3: {  
    destination: destination_CostCenter_Mass,
    endpoint: "/data/getCostCenterLookUp",
    payloadKey: "costCenter"
  },
  4: {  
    destination: destination_BankKey,
    endpoint: "/data/getBankKeyLookUp",
    payloadKey: "bankKey"
  },
  5: {  
    destination: destination_GeneralLedger_Mass,
    endpoint: "/data/getGeneralLedgerLookUp",
    payloadKey: "glAccount"
  },
  6: {  
    destination: destination_CostCenter_Mass,
    endpoint: "/data/getCostCenterGroupLookUp",
    payloadKey: "costCenterGroup"
  },
  7: {  
    destination: destination_ProfitCenter_Mass,
    endpoint: "/data/getProfitCenterGroupLookUp",
    payloadKey: "profitCenterGroup"
  },
  8: {  
    destination: destination_GeneralLedger_Mass,
    endpoint: "/data/getGeneralLedgerGroupLookUp",
    payloadKey: "glGroup"
  },
  10: {  
    destination: destination_InternalOrder,
    endpoint: "/api/v1/lookup/getInternalOrderLookUp",
    payloadKey: "internalOrder"
  }
};

export const SEARCH_TYPE = {
  STD: 'Std ',
  ADDITIONAL: 'Additional'
}

export const DT_KEY_NAMES = {
  MDG_MAT_DIVISION: "MDG_MAT_DIVISION",
  MDG_MAT_DIVISION_DESC: "MDG_MAT_DIVISION_DESC",
};

export const LOADING_MESSAGE = {
  DT_LOADING: "Loading DT...",
  REPORT_LOADING:
    "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience.",
  CHANGELOG_LOADING: "Loading Changelog...",
  TAXDATA_LOADING: "Loading Tax Data...",
  LOADING_PERM: "Loading permissions...",
  LOADING_USER_ACCESS: "Loading User Access Data, Please Wait...",
};

export const REGION = [
  { code: "US", desc: "US" },
  { code: "EUR", desc: "EUR" },
];

export const REQUEST_PRIORITY = [
  { code: "High", desc: "" },
  { code: "Medium", desc: "" },
  { code: "Low", desc: "" },
];

export const REQUEST_TYPE_OPTIONS = [
  { code: "Create", desc: "Create New BOM in Application" },
  { code: "Change", desc: "Modify Existing BOM in Application" },
  { code: "Create with Upload", desc: "Create New BOM with Excel Upload" },
  { code: "Change with Upload", desc: "Modify Existing BOM with Excel Upload" },
]
export const REQUEST_TYPE_OPTIONS_IO = [
  { code: "Create" },
  { code: "Change" },
  { code: "Create with Upload" },
  { code: "Change with Upload" },
]

export const MATERIAL_TYPE_DRODOWN = [
  { code: "FERT", desc: "Finished Goods" },
  { code: "HALB", desc: "Semifinished Goods" },
  { code: "ROH", desc: "Raw Materials" },
  { code: "ZROH", desc: "Spares,Mktg Non-Value" },
];
export const ARTICLE_TYPE_DRODOWN = [
  {code:"ZMER", desc:"Retail Trading Goods"},
  { code: "FERT", desc: "Finished Goods" },
  { code: "HALB", desc: "Semifinished Goods" },
  { code: "ROH", desc: "Raw Materials" },
  { code: "ZROH", desc: "Spares,Mktg Non-Value" },
];
export const CHAR_TYPE_DROPDOWN = [
  { code:"0", desc:"Color Characteristic"},
  { code: "1", desc: "Size Characteristic" },
  { code: "2", desc: "Size Characteristc (Main Size)" },
  { code: "3", desc: "Not Typed" },
]

export const DEPENDEND_DROPDOWN_FOR_CREATE = [
  "Chart Of Account",
  "Company Code",
  "Account Type",
  "Account Group",
  "GL Account"
];

export const REGION_CODE = {
  US: "US",
  EUR: "EUR",
};

export const DIVERSION_CONTROL_FLAG = [
  { code: "", desc: "No Batch Control" },
  { code: "B", desc: "Perishable Items" },
  { code: "U", desc: "FDA UDI/Batch Controlled" },
  { code: "X", desc: "FDA Batch Control" },
];

export const PRICE_CTRL_DATA = [
  { code: "S", desc: "Standard Price" },
  { code: "V", desc: "Moving Average Price" },
];

export const PAGESIZE = {
  TOP_SKIP: 100,
};
export const DT_TABLES = {
  SALES_DIV_PRICE_MAPPING: "MDG_MAT_SALESDIV_PRICEGRP_MAPPING",
  REG_PLNT_INSPSTK_MAPPING: "MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING",
  MDG_MAT_PRODUCT_HIERARCHY: "MDG_MAT_PRODUCT_HIERARCHY",
  MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT: "MDG_MAT_REGION_MATTYPE_PLANT_UOM_DT",
  MDG_ORG_ELEMENT_DEFAULT_VALUE: "MDG_ORG_ELEMENT_DEFAULT_VALUE",

};
export const ERROR_MESSAGES = {
  NO_DATA_AVAILABLE: "No Data Available",
  CHANGE_LOG_MESSAGE: "Error processing changelog data:",
  NO_FIELDS_CONFIGURED: "No Fields Configured",
  NO_DATA_CONFIGURED: "No Fields Configured for this view",
  NO_RECORDS: "There are no records to display at the moment",
  DATE_VALIDATION_ERROR: "Date validation error:",
  DUPLICATE_COMBINATION: "Duplicate combination found",
  DOCUMENT_DELETE_FAILED: (name) => `Failed to delete ${name}`,
  MANDATORY_FILTER_MD: (name) => `Please fill ${name} first`,
  MANDATORY_FILTER_BOM: "Please fill all fields (Plant, BOM Usage, Alternative BOM) to validate.",
  FETCH_CHANGELOG_ERROR: "Error fetching changelog data:",
  DESCRIPTION_VALIDITY_US:
    "Invalid characters used. Only English alphabets, numbers, '/', '\"', '-', and spaces are allowed",
  DESCRIPTION_VALIDITY_EUR:
    "Invalid characters used. Only English alphabets are allowed. No special characters",
  ERROR_FETCHING_DATA: "Error fetching data",
  ERROR_GET_DISPLAY_DATA: "Error in getDisplaydata processing",
  ERROR_SET_ROLE: "Failed to set role",
  // Dashboard related error messages
  DASHBOARD_REFRESH_FAILED: "Dashboard refresh failed:",
  DECISION_TABLE_FETCH_ERROR: "Decision table fetch error:",
  USER_PREFERENCES_FETCH_ERROR: "User preferences fetch error:",
  GRAPH_DATA_FETCH_ERROR: "Error fetching graph data:",
  DASHBOARD_INIT_FAILED: "Dashboard initialization failed:",
  FILTER_CHANGE_UPDATE_FAILED: "Filter change update failed:",
  // ... other existing error messages
  DATA_NOT_FOUND_FOR_SEARCH: "No data found for the selected criteria.",
  DUPLICATE_MATERIAL: "Duplicate material number ",
  DUPLICATE_MATERIAL_DESCRIPTION: "Duplicate material description ",
  ERROR_NO_USER: "No users found",
  ERROR_PERM: "No permissions found",
  ERROR_SEARCH: "Try adjusting your search criteria",
  ERROR_FETCHING_ROLES: "Error fetching roles & Access",
  ERROR_FETCHING_USER: "Error fetching User List",
  USER_ACCESS_ERROR: "An unknown error occurred, Please try again later.",
  SYSTEM_GENERATED_MSG: [
    "The download process may take some time depending on the file size. Please wait while we prepare your file.",
    "After the download is complete, you can upload the data by navigating to the respective Request ID from the Request Bench.",
  ],
  EMAIL_DELIVERY_MSG: [
    "Email delivery may take a few minutes depending on server load. Please check your spam/junk folder if not received.",
    "Once you receive the email, you can upload the data by navigating to the respective Request ID from the Request Bench.",
  ],
  ERROR_UOM: "Base UOM Code not found in dtData",
  ERROR_EXPORT: "No data available for export.",
  ERROR_FETCHING_LANGU: "Error fetching languages",
  NO_MATERIAL_FOUND: "No material found for the given search parameters",
  WENT_WRONG: "Oops! Something went wrong",
  UNEXPECTED_ERROR:
    "We encountered an unexpected error. Please try reloading the page.",
  NO_DATA_FOUND:
    "No data was found for the selected criteria. Please try again.",
  ERR_DOWNLOADING_EXCEL:
    "Oops! Something went wrong while downloading the excel file. Please try again later.",
  NUMBER_OF_FILES_LIMIT: "You can only upload a maximum of 5 files at once",
  INVALID_MAN_ID: "Invalid Manufacturer ID",
  ERR_MAN_ID: "Error validating Manufacturer ID",
  MAIN_DESCR_MANDATORY: "Main Description is mandatory",
  CANCELED_ERR: "Canceled Requests cannot be fetched !!",
  NO_ERROR_FOUND: "No errors found for this request",
  FAILED_TO_CHECK_INTERNET_SPEED: "Failed to check internet speed (0 Mbps)",
  NO_INTERNET_CONNECTION: "You are offline. Check your internet connection",
  BACK_ONLINE: "You are back online",
  FAILED_FETCH_SAP_SYSTEM: "Failed to fetch SAP system configuration",
  DOC_UPLOAD_FILES_LIMIT: "Cannot upload more than 5 files",
  DOC_UPLOAD_SIZE_LIMIT: "File size exceeded 50MB",
  ERROR_REQUEST_HEADER: "Error occured while saving Request Header",
  BOM_SAVE_AS_DRAFT_ERROR: "Error occurred while saving BOM as draft",
  BOM_SUBMIT_FOR_REVIEW_ERROR: "Error occurred while submitting BOM for review",
  BOM_SUBMIT_FOR_APPROVE_ERROR: "Error occurred while submitting BOM for approval",
  BOM_VALIDATE_ERROR: "Error occurred while validating BOM",
  FAILED_UPDATE_SAP_SYSTEM: (system) => `Failed to update SAP system to ${system}`,
  // Internal Order specific error messages
  IO_SAVE_AS_DRAFT_ERROR: "Error occurred while saving as draft",
  IO_SUBMIT_FOR_REVIEW_ERROR: "Error occurred while submitting for review",
  IO_SUBMIT_FOR_APPROVAL_ERROR: "Error occurred while submitting for approval",
  IO_VALIDATION_ERROR: "Error occurred while validating the request",
  IO_PROCESSING_ERROR: "Error occurred while processing request",
  IO_MANDATORY_FIELDS_ERROR: (fields) => `Please fill all the mandatory fields: ${fields.join(", ")}`,
  IO_DUPLICATE_ERROR: "Duplicate Internal Order found within current data.",
  IO_DUPLICATE_ROWS_ERROR: (duplicates) => `Duplicate rows found: ${duplicates.join(", ")}`,
  APP_SET_ERROR: "Application Settings Update Failed",
  LANG_SET_ERROR: "Language Settings Update Failed",
  IO_REFERENCE_ERROR: "Error occurred while fetching reference data",
};
export const ROLES = {
  SUPER_USER: "Z_MDG_SUPER_USER",
};

export const FIELD_TYPE = {
  INPUT: "Input",
  DROPDOWN: "Dropdown",
  DATE_FIELD: "Date field",
};

export const PAGE_TYPE = {
  DISPLAY: "display",
  REQUESTOR: "requestor",
};

export const EXPORT_EXCEL_KEYS = {
  REGION: "Region",
  MATERIAL_TYPE: "Material Type",
  MATERIAL_NUMBER: "Material Number",
  SALES_ORG: "Sales Org",
  DISTRIBUTION_CHANNEL: "Distribution Channel",
  PLANT: "Plant",
  WAREHOUSE: "Warehouse",
  STORAGE_LOCATION: "Storage Location",
};

export const EXPORT_PARAMETERS = [
  {
    key: EXPORT_EXCEL_KEYS.REGION,
    options: REGION,
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.MATERIAL_TYPE,
    options: MATERIAL_TYPE_DRODOWN,
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.MATERIAL_NUMBER,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.SALES_ORG,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.DISTRIBUTION_CHANNEL,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.PLANT,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.WAREHOUSE,
    options: [],
    singleSelect: true,
  },
  {
    key: EXPORT_EXCEL_KEYS.STORAGE_LOCATION,
    options: [],
    singleSelect: true,
  },
];

export const SUCCESS_MESSAGES = {
  DOCUMENT_DELETED: (name) => `${name} has been deleted successfully`,
  DUPLICATE_COMBINATION: "Validation successful - No duplicate combination found.",
  REQUEST_HEADER_CREATED: "Request Header Created Successfully with request ID",
  DB_OPERATIONAL: "Database is operational",
  EMAIL_OPERATIONAL: "Email service is working fine",
  IDM_OPERATIONAL: "IDM Service is active",
  ODATA_OPERATIONAL: "OData Service is operational",
  BPA_OPERATIONAL: "SAP BPA Workflow Service is operational",
  SHAREPOINT_OPERATIONAL: "SharePoint Service is operational",
  USER_FETCHED: "Users fetched successfully",
  USER_ACCESS_SUCCESS: "User Access Data Fetched Successfully",
  DOWNLOAD_MAIL_INITIATED:
    "Download has been started. You will get the Excel file via email.",
  MAN_ID_VALID: "Manufacturer ID Validated Successfully",
  SAP_DOWNLOAD_SUCCESS:
    "SAP Excel Report.xlsx has been downloaded successfully.",
  SUCCESS_REQUEST_HEADER: "Request Header Created Successfully! Request ID: ",
  BOM_SAVED_AS_DRAFT: "BOM saved as draft successfully",
  BOM_SUBMITTED_FOR_REVIEW: "BOM submitted for review successfully",
  BOM_SUBMITTED_FOR_APPROVE: "BOM submitted for approval successfully",
  BOM_VALIDATED: "BOM validated successfully",
  // Internal Order specific success messages
  IO_SAVED_AS_DRAFT: "Internal Orders saved as draft successfully",
  IO_SUBMITTED_FOR_REVIEW: "Internal Orders submitted for review successfully",
  IO_SUBMITTED_FOR_APPROVAL: "Internal Orders submitted for approval successfully",
  IO_VALIDATION_INITIATED: "Internal Orders validation initiated",
  IO_SYNDICATION_INITIATED: "Internal Orders syndication initiated",
  IO_VALIDATION_SUCCESSFUL: "Validation Successful",
  APP_SET_SUCCESS: "Application Settings Updated Successfully",
  LANG_SET_SUCCESS: "Language Settings Updated Successfully",
  IO_ROW_ADDED_WITH_REFERENCE: "Row added with reference data successfully",
};

export const INFO_MESSAGES = {
  FETCHING_REQUEST_ID: 'Please wait while we retrieve the Request ID.',
  FETCHING_REQUEST_TYPE: 'Please wait while we retrieve the Request Type.',
  TEMPLATE_MESSAGE: 'Choose a template category from the sidebar to view its templates',
  FETCH_ROLE: 'Please wait while we retrieve the Role',
}

export const ERROR_MESSAGES_HEALTH = {
  DB_DOWN: "Database service is down",
  EMAIL_DOWN: "Email service is facing issues",
  IDM_DOWN: "IDM Service is down",
  ODATA_DOWN: "OData Service is facing issues",
  BPA_DOWN: "SAP BPA Workflow Service is facing issues",
  SHAREPOINT_DOWN: "SharePoint Service is facing issues",
  WENT_WRONG: "Oops! Something went wrong",
  UNEXPECTED_ERROR:
    "We encountered an unexpected error. Please try reloading the page",
};

export const DROP_DOWN_ITEM_SIZE = {
  HEIGHT: 32,
};
export const CHARACTER_LIMIT = {
  EUR: "35",
  US: "26",
  US_SPECIAL: "12",
};
export const DASHBOARD_REPORT_LABELS = {
  ALL_MATERIALS_BY_PLANT_AND_SALES:
    "List of materials created by plant & Sales Org (update of interco price)",
  DRAFT_STATUS: "List of Request Ids in draft status",
  MATERIAL_LIST: "Created materials list",
  PRICING_GROUP: "Material Pricing group",
  MISSING_HTS: "Missing HTS Code Report",
  REQUEST_VS_OBJECT_TYPE: "Request (In Nos) Vs Object Type",
  AVG_REQUEST_ID_LIFE_CYCLE: "Avg Request Id Life Cycle (In Hours) Vs Object Type",
  REJECTED_REQUESTS_PERCENTAGE: "Not Successfully Completed Req Ids due to Rejection(in %age) Vs Object Type",
  NOT_SUCCESSFULLY_COMPLETED_REQUESTS: "Not Successfully Completed Req Ids (in %age) Vs Object Type",
  SLA_BREACHED_REQUESTS: "SLA Breached Req Ids(In Nos) Vs Object Type",
  NO_OF_REQUESTS_REJECTED: "No. of Requests Rejected/Cancelled(In Nos) Vs Users",
  NO_OF_REQUESTS_PENDING: "No. of Requests Pending(In Nos) Vs Approvers/MDM",
  AVG_MDM_TASK_COMPLETION_TIME: "Avg time taken by MDM Task For Completion Vs Object Type",
  AVG_APPROVER_TASK_COMPLETION_TIME: "Avg time taken by Approver Task Vs Object Type",
  NO_OF_REQUESTS_REJECTED_VS_REQUESTORS: "Req Ids Rejected (in Nos) Vs Requestors",
  APPROVERS_WHO_HAVE_MISSED_SLA: "Approvers Who have Missed SLA(In Nos)",
  APPROVERS_AVG_APPROVAL_TIME: "Approvers Avg. Approval Time(In Hours)",
  REQUESTS_COMPLETED_BY_MDM: "No of Requests Completed By MDM Team",
  OPEN_REQUESTS_VS_TYPE: "Open Requests(In Nos) Vs Object Type",
  REQUESTOR_APPROVER_COUNTS: "Requestor/Approvers Counts Vs Object Type",
  OPEN_WORKFLOWS_REPORT: "Open Workflows Report",
  TEMP_BLOCK_REQUESTS_REPORT: "Temporary Block Requests Report",
  SCHEDULED_REQUESTS_REPORT: "Scheduled Requests Report",
  PDF_SCHEDULER_REPORT: "PDF Scheduler Report",
  ON_HOLD_WITH_REQUESTORS_REPORT: "Requests On Hold With Requestors",
  ALL_ERROR_REQUESTS_REPORT: "All Error Requests",
  OBJECT_NUMBERS_REPORT: "Object Numbers Report",
  SYNDICATION_ATTACHMENT_FAILED_REPORT: "Syndication & Attachement Failed Report",
  VALIDATION_FAILED_UNEXPECTED_ERROR:"Validation failed due to an unexpected error",
  ERROR_VALIDATING:"Error occurred while validating the request"
};

export const SEARCH_BAR_LABELS = {
  TOOLTIP: "Search for Table data",
  LABEL: "Search Documents",
};

export const SAPViewPaths = [
  "DisplayMaterialSAPView",
  "DisplayInternalOrderSAPView",
];

export const TASK_NAME = {
  REQ_INITIATE: "Z_MAT_REQ_INITIATE",
  REQ_INITIATE_FIN: "Z_FIN_REQ_INITIATE",
  REQ_DISPLAY_FIN: "Z_FIN_REQ_DISPLAY",
  REQ_INITIATE_DOWNLOAD: "Z_FIN_REQ_DOWNLOAD",
  REQ_INITIATE_DOWNLOAD_MAT: "Z_MAT_REQ_DOWNLOAD",
  REQUESTOR: "Requestor",
  INITIATOR: 'Initiator'
};

export const REGEX = {
  ADDING_SPACE: /([A-Z])/g,
};

export const DASHBOARD_CARD_TITLE = {
  REQUEST_VS_OBJECT_TYPE: "Request (In Numbers) Vs Request Type",
  PENDING_REQUESTS_VS_GROUPS:
    "Number of Requests Pending (In Numbers) Vs Groups",
  AVG_MDM_TASK_COMPLETION_TIME:
    "Avg time taken by MDM Task For Completion Vs Request Type",
  REJECTED_REQUESTS_PERCENTAGE:
    "Not Successfully Completed Req Ids due to Rejection(in %age) Vs Request Type",
  OPEN_REQUESTS_VS_TYPE: "Open Requests In Workspace Vs Request Type",
  REQUESTS_COMPLETED_BY_MDM: "Number of Requests Completed By MDM Users",
  APPROVERS_AVG_APPROVAL_TIME: "Approvers Avg. Approval Time(In Hours)",
  REQUESTS_VS_CHANGE_TEMPLATE: "Number of Requests Vs Change Template",
  REQUESTS_VS_REGION: "Number of Requests Vs Region",
  TOP_5_REQUESTORS: "Top 5 Requestors",
};

export const DT_FIELDS_NAME = {
  COUNTRY_OF_ORIGIN: "Countryori",
  VAL_CLASS: "ValClass",
  PARENT_MAT_NUMBER: "ParentMatNumber",
  RETURN_MAT_NUMBER: "ReturnMatNumber",
};
export const DEFAULT_VALUES = {
  DEFAULT_IND_SECTOR: { code: "M", desc: "Mechanical engineering" },
  DEFAULT_ARTICLE_CATEGORY: { code: '01', desc: 'Generic article'},
  DEFAULT_TAX_CLASS: { TaxClass: "1", TaxClassDesc: "Taxable" },
  DEFAULT_VERSION : 0,
};

export const HEADINGS = {
  COPY_ORG_DATA_VALUES: "Choose Sales Combination to copy",
  COPY_ORG_DATA_VALES_HEADING: "Select Org Data to Copy",
};
export const LOADER_MESSAGES = {
  VALIDATING_MATS: "Validating Materials, please wait!",
  VALIDATE_MANDATORY: "Validate mandatory fields before proceeding",
  LOADING: "Loading, please wait..."
};

export const SERVICE_NAMES = {
  DATABASE: "DATABASE",
  IDM: "IDM",
  MAIL: "MAIL",
  SAP_BPA_WORKFLOW: "SAP_BPA_WORKFLOW",
  ODATA: "ODATA",
  SHAREPOINT: "SHAREPOINT",
};

export const SERVICE_NAME_MAP = {
  [SERVICE_NAMES.DATABASE]: "Database Services",
  [SERVICE_NAMES.IDM]: "IDM Services",
  [SERVICE_NAMES.MAIL]: "Email Services",
  [SERVICE_NAMES.SAP_BPA_WORKFLOW]: "SAP BPA Workflow Services",
  [SERVICE_NAMES.ODATA]: "OData Services",
  [SERVICE_NAMES.SHAREPOINT]: "SharePoint Services",
};

export const RELATION_DROPDOWN = [
  { code: "Parent", desc: "Parent" },
  { code: "Child", desc: "Child" },
];
export const CATEGORY_DROPDOWN = [{ code: "L", desc: "Stock Item" }];

export const CHAT_MESSAGES = {
  CREATE_ACC: "Create an account, login, and select a user from the sidebar to start your conversation",
  WELCOME: "Welcome to Chat",
  MESSAGE: "Type your message...",
  ACTIVE: "Active now",
  CREATE_LOGIN: "Create an account and login to see other users",
  NO_USERS: "No Users Found",
  NO_USER: "User not found",
  WEBSOCKET_DISCONNECTED: "WebSocket not connected. Please refresh and try again.",
  ERROR: "Error loading messages:",
  ERROR_LOGGING: "Error logging in: ",
  PLEASE_LOGIN: "Please login first",
  ERROR_CHAT: "Failed to create/load chat",
  EMAIL: "Please enter an email address",
};

export const EXCLUDED_VIEWS = [
  "Header",
  "Sales-General",
  "Sales-Plant",
  "Purchasing-General",
  "Warehouse Management",
  "Forecasting",
  "Storage Location Stocks",
  "Plant Stock",
  "Quality Management",
  "productionResources/tools", ,
  "BOM",
  "Source List",
  "PIR",
  "Plant Data|Storage General",
  "Plant Data|Storage-Plant",
];
export const UI_HIDDEN_VIEWS = [
  MATERIAL_VIEWS.SALES_PLANT,
  MATERIAL_VIEWS.STORAGE_PLANT,
];

export const ALT_UNITS = {
  EA: "EA",
  CA: "CA",
  CT: "CT",
  PAL: "PAL",
  PA: "PA",
};

export const EAN_CATEGORIES = {
  IE: "IE",
  IC: "IC",
  UC: "UC",
  MB: "MB",
  MI: "MI",
};

export const TABLE_FIELDS_UOM = {
  EAN_UPC: "eanUpc",
  EAN_CATEGORY: "eanCategory",
  MAIN_EAN: "MainEan",
};

export const LOCAL_STORAGE_KEYS = {
  CURRENT_TASK: "currentTask",
  REQUEST_BENCH_TASK: "requestBenchTask",
  ROLE: "role",
  MODULE: 'module',
  STATUS_VALIDATE:"validatedRowsStatus",
  USER_DATA: "userData"
};

export const FIELD_NAME_MAPPINGS = {
  plant: "Plant",
  salesOrg: "Sales Organization",
  group: "Material Group",
  number: "Material Number",
  description: "Material Description",
  oldMaterialNumber: "Old Material Number",
  labOffice: "Lab/Office",
  transportationGroup: "Transportation Group",
  productHierarchy: "Product Hierarchy",
  changedBy: "Changed By",
  changedOn: "Changed On",
  basicMaterial: "Basic Material",
  division: "Division",
  createdBy: "Created By",
  distributionChannel: "Distribution Channel",
  storageLocation: "Storage Location",
  warehouseNo: "Warehouse No",
  PurStatus: "X-Plant Material Status",
  type: "Material Type",
  createdOn: "Created On",
  XplantMaterialStatus: "X-Plant Material Status",
  purchasingGroup: "Purchasing Group",
};



export const DESCRIPTION_DATA = {
  CATEGORY: "Description",
  TABLE_HEADERS: {
    ID: "ID",
    LANGUAGE: "Language",
    DESCRIPTION: "Material Description"
  },
  FIELDS: {
    ID: "id",
    LANGUAGE: "language",
    DESCRIPTION: "materialDescription"
  },
  ACCORDION_TITLE: "Material Descriptions"
};

export const UOM_DATA = {
  CATEGORY: "Units of Measure",
  TABLE_HEADERS: {
    ALT_UNIT: "Alternative Unit",
    NUMERATOR: "Numerator",
    DENOMINATOR: "Denominator",
    EAN_UPC: "EAN/UPC",
    EAN_CATEGORY: "EAN Category",
    DIMENSIONS: "Dimensions (L×W×H)",
    DIMENSION_UNIT: "Dimension Unit",
    VOLUME: "Volume",
    VOLUME_UNIT: "Volume Unit",
    GROSS_WEIGHT: "Gross Weight",
    NET_WEIGHT: "Net Weight",
    WEIGHT_UNIT: "Weight Unit"
  },
  FIELDS: {
    ALT_UNIT: "aUnit",
    NUMERATOR: "yValue",
    DENOMINATOR: "xValue",
    EAN_UPC: "eanUpc",
    EAN_CATEGORY: "eanCategory",
    LENGTH: "length",
    WIDTH: "width",
    HEIGHT: "height",
    DIMENSION_UNIT: "unitsOfDimension",
    VOLUME: "volume",
    VOLUME_UNIT: "volumeUnit",
    GROSS_WEIGHT: "grossWeight",
    NET_WEIGHT: "netWeight",
    WEIGHT_UNIT: "weightUnit"
  },
  ACCORDION_TITLE: "Units of Measure"
};

export const TAX_DATA = {
  CATEGORY: "TaxData",
  SUB_CATEGORY: "TaxData",
  DATASET: "TaxDataSet",
  TABLE_HEADERS: {
    COUNTRY: "Country",
    TAX_TYPE: "Tax Type",
    TAX_CLASS: "Tax Class",
    DESCRIPTION: "Description"
  },
  FIELDS: {
    COUNTRY: "Country",
    TAX_TYPE: "TaxType",
    TAX_CLASS: "TaxClass",
    TAX_CLASS_DESC: "TaxClassDesc",
    SELECTED_TAX_CLASS: "SelectedTaxClass"
  },
  ACCORDION_TITLE: "Tax Classification"
};
export const WORK_FLOW_LEVELS = {
  '-1': 'Requestor',
  0: 'Final Creation',
  1: 'Data Entry',
  2: 'Additional Master Data',
  3: 'Cost',
  4: 'Record Approval'
}
export const DASHBOARD_COLUMNS = {
  FIRST: 'First',
  SECOND: 'Second',
  THIRD: 'Third'
}
export const DASHBOARD_MAIN_COLUMNS = [
  {
    id: DASHBOARD_COLUMNS.FIRST
  },
  {
    id: DASHBOARD_COLUMNS.SECOND
  },
  {
    id: DASHBOARD_COLUMNS.THIRD
  }
]
export const CHART_TYPE = {
  BAR: 'BAR',
  STACKED_BAR: 'STACKED_BAR',
  LINE: 'LINE',
  STACKED_LINE: 'STACKED_LINE',
  STACK_LINE: 'STACK_LINE',
  COLUMN: 'COLUMN',
  STACK_COLUMN: 'STACK_COLUMN',
  PIE: 'PIE',
  AREA: 'AREA',
  STACKED_AREA: 'STACKED_AREA',
  DONUT: 'DONUT'
};

export const LINEAR_CHART_TYPES = [
  { value: CHART_TYPE.BAR, label: 'Bar' },
  { value: CHART_TYPE.STACKED_BAR, label: 'Stacked Bar' },
  { value: CHART_TYPE.COLUMN, label: 'Column' },
  { value: CHART_TYPE.STACK_COLUMN, label: 'Stacked Column' },
  { value: CHART_TYPE.LINE, label: 'Line' },
  { value: CHART_TYPE.STACKED_LINE, label: 'Stacked Line' },
  { value: CHART_TYPE.STACK_LINE, label: 'Stacked Line' },
  { value: CHART_TYPE.AREA, label: 'Area' },
  { value: CHART_TYPE.STACKED_AREA, label: 'Stacked Area' }
];

export const CIRCULAR_CHART_TYPES = [
  { value: CHART_TYPE.PIE, label: 'Pie' },
  { value: CHART_TYPE.DONUT, label: 'Donut' }
];

export const STACKED_CHART_TYPES = [
  CHART_TYPE.STACK_COLUMN,
  CHART_TYPE.STACKED_BAR,
  CHART_TYPE.STACKED_LINE,
  CHART_TYPE.STACK_LINE,
  CHART_TYPE.STACKED_AREA
];

export const MODULE_BASED_DESTINATION = {
  [MODULE_MAP.MAT]: destination_MaterialMgmt,
  [MODULE_MAP.ART]: destination_ArticleMgmt
}

export const HOME_CAROUSEL_BANNER = [
  {
    id: 1,
    title: "Augment your customer experience",
    subtitle: "Transform your business with cutting-edge solutions",
    description: "Leverage advanced analytics and AI-powered insights to deliver exceptional customer experiences that drive growth and satisfaction.",
    image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=1200&h=500&fit=crop",
    gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
  },
  {
    id: 2,
    title: "Innovate with confidence",
    subtitle: "Next-generation collaboration tools",
    description: "Streamline your workflow with intelligent automation and real-time collaboration features designed for modern businesses.",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=500&fit=crop",
    gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"
  },
  {
    id: 3,
    title: "Scale your success",
    subtitle: "Enterprise-grade performance",
    description: "Built for scale with enterprise security, 99.9% uptime, and seamless integration with your existing systems.",
    image: "https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=1200&h=500&fit=crop",
    gradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"
  }
]
export const VAR_ORD_UNIT = [
  { code: '1', desc: 'Active' },
  { code: '2', desc: 'Active with own price' }
]

export const REQUEST_HEADER_FILED_NAMES = {
  REQUEST_TYPE: 'RequestType',
  REQUEST_ID: 'RequestId',
  REGION: 'Region',
}

export const DOC_SNACKBAR_MESSAGES = {
  FILES: {
    FETCH_ERROR: 'Error fetching files',
    UPLOAD_SUCCESS: (count) => `${count} file(s) upload has been initiated`,
    UPLOAD_FAILED: 'Upload failed',
    UPLOAD_ERROR: 'Upload error',
    DELETE_SUCCESS: 'File deleted successfully',
    DELETE_FAILED: 'Failed deleting document',
    DELETE_ERROR: 'Failed deleting document',
    VISIBILITY_SUCCESS: (isVisible) => `File ${isVisible ? 'made visible' : 'hidden'} successfully`,
    VISIBILITY_FAILED: 'Visibility update failed',
    VISIBILITY_ERROR: 'Visibility update error',
    MISSING_USER: 'Missing userId for edit action',
    MISSING_ORG_DATA: 'Please fill atleast one Org Data row',
  }
};
export const DATA_CLEANSE_CONSTANTS = {
  MODULE: 'Type of Module',
  NO_OBJECTS: 'Number of Objects',
  BUSINESS_RULES: 'Total Business Rules',
  AGGREGATE_SCORE: 'Aggregate Score',
  CREATEDBY: 'Created By',
  SEARCH: 'Search materials...',
  MATERIAL: 'Material',
  NO_MATERIAL_FOUND: 'No materials found',
  ADJUST: 'Try adjusting your search terms',
  ANALYSIS_DETAILS: 'Analysis Details',
  OVERALL_SCORE: 'Overall Score',
  BUSINESS_RULE: 'Business Rule',
  FIELDS: 'Fields',
  PASS: 'Pass',
  FAIL: 'Fail',
  SCORE: 'Score',
  PRESENT_VALUE: 'Present Value',
  EXPECTED_VALUE: 'Expected Value',
  NO_MATERIAL: 'No Material Selected',
  VALIDATIONS_DETAILS: 'Select a material from the sidebar to view its validation details',
  CLEANSE_REQUEST: 'Data Cleanse Request',
  VIEW: 'This view displays the details of the materials data cleansing report',
  PDF: 'Download as PDF',
  ERROR: 'An error occurred while creating the request',
  DATA_CLEANSE: 'Data Cleanse',
  FILTER: 'Filter Data Cleanse Requests',
  NEW_REQUEST: 'Create New Request',
  INITIATE_REQUEST: 'Initiate Request',
  CREATE_REQUEST: 'Create Request',
  SELECT_MATERIAL: 'Select Material',
  MATERIAL_TYPE: 'Material Type',
  SELECT_MATERIAL_TYPE: 'Select Material Type',
  MATERIAL_GROUP: 'Material Group',
  SELECT_MATERIAL_GROUP: 'Select Material Group',
  SELECT_COST_CENTER: 'Select Cost Center',
  CATEGORY: 'Cost Center Category',
  SELECT: 'Select Business Rule',
  FAILED_FETCHING_DATA: 'No Data Found for this Data Cleanse Request!',
  EXPORT_SUCCESS: `_Data Cleanse.pdf has been exported successfully!`,
  BR_FAIL: 'Business Rule is required',
  NO_FAIL: 'Number of Objects is required',
  CO_FAIL: 'Created On date range is required',
}

export const BK_HEADER_MANDATORY = [
  { jsonName: "BankCtry", fieldName: "Bank Ctry/Reg." },
  { jsonName: "BankKey", fieldName: "Bank Key" },
  { jsonName: "BankName", fieldName: "Bank Name" },
  { jsonName: "BankBranch", fieldName: "Bank Branch" },
]

export const SUCCESS_DIALOG_MESSAGE = {
  TITLE: "Success",
  SUBTEXT: "Click OK to redirected to the Request Bench .",
  BUTTONTEXT: "OK",
  REDIRECT: "/requestbench"
}
export const FAILURE_DIALOG_MESSAGE = {
  TITLE: "failed",
  SUBTEXT: "You will be redirected to the Request Bench .",
  BUTTONTEXT: "Go to Request Bench",
  REDIRECT: "/requestbench"
}

export const LOGOUT = 'LOGOUT'

export const ARTIFACTNAMES = {
  MATERIALMASTER: 'MaterialMaster',
  BOM: 'BOM',
  IO: 'InternalOrder',
  CC: 'CostCenter',
  PC:'ProfitCenter',
  PCG: 'Profit Center Group',
  CCG: 'Cost Center Group',
  CEG: 'Cost Element Group',
  ART:"Article",
  BK: 'BankKey',
  GL: 'GeneralLedger'
}

export const PROCESS_TASK_NAME = {
  BOM:'Bill Of Material',
  ART:"Article Master"

}
export const ORG_FIELDS = {
  PLANT: "Plant",
  SALES_ORG: "Sales Organization",
  DISTRIBUTION_CHANNEL: "Distribution Channel",
  STORAGE_LOCATION: "Storage Location",
  WAREHOUSE: "Warehouse",
}

export const ACTIVE_TAB_MODULE_MAP = {
  0: MODULE_MAP?.MAT,
  1: MODULE_MAP?.ART,
  2: MODULE_MAP?.PC,
  3: MODULE_MAP?.CC,
  4: MODULE_MAP?.BK,
  5: MODULE_MAP?.GL,
  6: MODULE_MAP?.CCG,
  7: MODULE_MAP?.PCG,
  8: MODULE_MAP?.CEG,
  9: MODULE_MAP?.BOM,
  10: MODULE_MAP?.IO,
}

export const changeLogWFColumns = [
  { field: "workflowId", headerName: "Workflow Id", width: 150, },
  { field: "workflowName", headerName: "Workflow Name", width: 150, },
  { field: 'operationType', headerName: 'Operation', width: 120 },
  { field: 'taskName', headerName: 'Task Name', width: 150 },
  { field: 'fieldName', headerName: 'Field Name', width: 200 },
  { field: 'oldValue', headerName: 'Old Value', width: 150 },
  { field: 'newValue', headerName: 'New Value', width: 150 },
  { field: 'changedBy', headerName: 'Changed By', width: 250 },
  { field: 'changedOn', headerName: 'Changed On', width: 180 },
]

export const NEW_WF_DATA = {
  '-1': {
    levelName: 'Requestor Level',
    tasks: [{
      id: 'requestor-task',
      workflowTaskName: 'Requestor Task',
      description: 'Initial request submission',
      isFixed: true,
      workflowGroup: 'Z_MAT_REQ_INITIATE',
      slaHigh: 24,
      slaMedium: 24,
      slaLow: 24,
      sendBackAllowedTo: []
    }]
  },
  0: {
    levelName: 'Final Level',
    tasks: [{
      id: 'final-task',
      workflowTaskName: 'Final Task',
      description: 'Final task approval and closure',
      isFixed: false,
      workflowGroup: 'Z_DATA_MAT_STEWARD_GROUP',
      slaHigh: 48,
      slaMedium: 48,
      slaLow: 48,
      sendBackAllowedTo: []
    }]
  }
}
export const WORKFLOW_DATA_CONSTANTS = {
  TASK_DESC: 'Task description',
  LEVEL: "Level",
  MODES: {
    CREATE: "create",
    EDIT: "edit",
    VIEW: "view"
  },
  TITLE_MODES: {
    CREATE: "Workflow Creation",
    EDIT: "Edit Workflow",
    VIEW: "View Workflow"
  },
  CHANGE_LOGS_TYPES: {
    ADDED: "ADDED",
    REMOVED: 'REMOVED',
    CHANGED: 'CHANGED',
  },
  SUCCESS_DESC: "has been added to level",
  SUCCESS_MSG: "Task Added",
  LEVMOV_MSG: 'Task Deleted & Levels Re-adjusted',
  LEVMOV_MSG_TASK_DEL: 'Task Deleted',
  LELMOV_DESC: 'has been deleted and levels have been re-numbered',
  LELMOV_DESC_DEL: 'has been deleted',
  HANDLE_MSG: 'Same Level',
  HANDLE_DESC: 'Task is already in the selected level',
  LEVELADJUST_MSG: 'Task Moved & Levels Re-adjusted',
  LEVELADJUST_MSG_MSGS: 'Task Moved',
  LEVELADJUST_DESC_MOV: 'moved to level',
  LEVELADJUST_DESC_LEV: 'and levels have been re-numbered',
  HANDLE_TASK_MSG: 'Task Updated',
  HANDLE_TASK_MSG_DESC: ' has been updated successfully',
  SENDBACKALLOWED: 'SendbackAllowed',
  POPCONFIRM_DESC: 'Are you sure you want to delete this task?',
  CHECKDUP_ERR_MSG: 'Duplicate entry found. Please modify the workflow name or other parameters.',
  MODAL_FILTERS: {
    LVL_NUM: {
      name: "levelNumber",
      label: "Level Number",
      message: "Please select Level Number",
      placeholder: "Select Level Number",
    },
    LVL_NAME: {
      name: "levelName",
      label: "Level Name",
      message: "Please select Level Name",
      placeholder: "Enter Level Name",
    },
    TASK_NAME: {
      name: "workflowTaskName",
      label: "Workflow Task Name",
      message: "Please enter Task Name",
      placeholder: "Enter Task Name",
    },
    TASK_DESC: {
      name: "requestBenchStatus",
      label: "Request Bench Status",
      message: "Please enter Request Bench Status",
      placeholder: "Enter Request Bench Status",
    },
    WORKFLOW_GROUP: {
      name: "workflowGroup",
      label: "Workflow Group",
      message: "Please select Workflow Group",
      placeholder: "Select Workflow Group",
    },
    SEND_BACK_ALLOWED_TO: {
      name: "sendBackAllowedTo",
      label: "Send Back Allowed To",
      message: "Please select Send Back Allowed To",
      placeholder: "Select Send Back Allowed To",
    },
    SLA_HIGH: {
      name: "slaHigh",
      label: "SLA (hrs) - High",
      message: "Please enter SLA for High Priority",
      placeholder: "Enter High SLA hours",
    },
    SLA_MEDIUM: {
      name: "slaMedium",
      label: "SLA (hrs) - Medium",
      message: "Please enter SLA for Medium Priority",
      placeholder: "Enter Medium SLA hours",
    },
    SLA_LOW: {
      name: "slaLow",
      label: "SLA (hrs) - Low",
      message: "Please enter SLA for Low Priority",
      placeholder: "Enter Low SLA hours",
    },
  },

}

export const ERROR_FIELD_MAP = {
  [MODULE_MAP?.BK]: [
    { label: "SAP Message", key: "sapMessage" },
    { label: "Bank Key Duplicate Error", key: "bankKeyDuplicateError" },
    { label: "Object SAP Error", key: "objectSapError" },
    { label: "Object DB Error", key: "objectDbError" },
    { label: "Description SAP Error", key: "descSapError" },
    { label: "Description DB Error", key: "descDbError" },
  ],
  [MODULE_MAP?.IO]: [
    { label: "SAP Message", key: "SapMessage" },
    { label: "Object SAP Error", key: "ObjectSapError" },
    { label: "Object DB Error", key: "ObjectDbError" },
  ],
  [MODULE_MAP?.MAT]: [
    { label: "SAP Message", key: "sapMessage" },
    { label: "Material Duplicate Error", key: "materialDuplicateError" },
  ],
  [MODULE_MAP?.PC]: sameErrorFields(),
  [MODULE_MAP?.CC]: sameErrorFields(),
  [MODULE_MAP?.BOM]: sameErrorFields(),
  [MODULE_MAP?.ART]: sameErrorFields(),
  [MODULE_MAP?.CCG]: sameErrorFields(),
  [MODULE_MAP?.CEG]: sameErrorFields(),
  [MODULE_MAP?.PCG]: sameErrorFields(),
  [MODULE_MAP?.GL]: sameErrorFieldsGL(),
};
export const ORG_DATA =[
    {
      id: 0,
      salesOrg: {
        code: 'I00X',
        desc: 'India Sales Org'
      },
      dc: {
        value: {
          code: 'IO',
          desc: 'I00X'
        },
        options: [
          {
            code: 'IO',
            desc: 'I00X'
          }
        ]
      },
      plant: {
        value: {
          code: 'I00X',
          desc: 'Industry X'
        },
        options: []
      },
      sloc: {
        value: {},
        options: [
          {
            code: 'I01X',
            desc: 'Global Storage'
          },
          {
            code: 'I02X',
            desc: 'PROC. STORAGE'
          },
          {
            code: 'I03X',
            desc: 'Prod. Storage'
          }
        ]
      },
      warehouse: {
        value: {},
        options: []
      },
      mrpProfile: null,
      distributionCenter: {
        value: {
          code: 'I00X',
          desc: 'Industry X'
        },
        options: []
      },
      purchasingOrg: {
        value: {
          code: 'I00X',
          desc: 'Purchasing Org 1'
        },
        options: []
      },
      store: {
        value: {
          code: 'I00X',
          desc: 'Industry X'
        },
        options: []
      },
      supplier: {
        value: {
          code: '0017300001',
          desc: 'Domestic US Supplier 1'
        },
        options: []
      }
    }
  ]
// Define shared structure once
function sameErrorFields() {
  return [
    { label: "SAP Message", key: "sapMessage" },
    { label: "Object SAP Error", key: "objectSapError" },
    { label: "Short Description SAP Error", key: "shortDescSapError" },
    { label: "Long Description SAP Error", key: "longDescSapError" },
    { label: "Object DB Error", key: "objectDbError" },
    { label: "Short Description DB Error", key: "shortDescDbError" },
    { label: "Long Description DB Error", key: "longDescDbError" },
  ];
}

function sameErrorFieldsGL (){
   return [
    { label: "SAP Message", key: "sapMessage" },
    { label: "Object SAP Error", key: "objectSapError" },
    { label: "SAP Response", key: "sapResponse" },
    { label: "Object Number Range Error", key: "objectNoRangeError" },
    { label: "Short Description SAP Error", key: "shortDescSapError" },
    { label: "Long Description SAP Error", key: "longDescSapError" },
    { label: "Object DB Error", key: "objectDbError" },
    { label: "Short Description DB Error", key: "shortDescDbError" },
    { label: "Long Description DB Error", key: "longDescDbError" },
    { label: "Duplicate DB Error", key: "dbDuplicateCheck" },
    
  ];
}

export const WORKFLOW_FILTERS= [
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Created By",
        "MDG_MAT_JSON_FIELD_NAME": "createdBy",
        "MDG_MAT_SEQUENCE_NO": 5,
        "MDG_MAT_FIELD_TYPE": "Calendar"
    },
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Bifurcation Group",
        "MDG_MAT_JSON_FIELD_NAME": "bifurcationGroup",
        "MDG_MAT_SEQUENCE_NO": 3,
        "MDG_MAT_FIELD_TYPE": "Drop Down"
    },
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Workflow Name",
        "MDG_MAT_JSON_FIELD_NAME": "workflowName",
        "MDG_MAT_SEQUENCE_NO": 4,
        "MDG_MAT_FIELD_TYPE": "Drop Down"
    },
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Date Range",
        "MDG_MAT_JSON_FIELD_NAME": "dateRange",
        "MDG_MAT_SEQUENCE_NO": 6,
        "MDG_MAT_FIELD_TYPE": "Calendar"
    },
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Scenario",
        "MDG_MAT_JSON_FIELD_NAME": "scenario",
        "MDG_MAT_SEQUENCE_NO": 2,
        "MDG_MAT_FIELD_TYPE": "Drop Down"
    },
    {
        "MDG_MAT_VISIBILITY": "Optional",
        "MDG_MAT_DEFAULT_VALUE": null,
        "MDG_MAT_UI_FIELD_NAME": "Region",
        "MDG_MAT_JSON_FIELD_NAME": "region",
        "MDG_MAT_SEQUENCE_NO": 1,
        "MDG_MAT_FIELD_TYPE": "Drop Down"
    }
]

export const WF_FILTER_OPTIONS = {
  REGION: ["US", "EUR"],
  SCENARIO: ["Create", "Change", "Create with Upload", "Change with Upload", "Extend", "Extend with Upload"],
  BIFURCATION_GROUP: ["GROUP-1", "GROUP-2", "GROUP-3"],
}

export const LANG_LIST = [ "English", "Hindi", "Japanese", "French", "Spanish", "Odia", "German", "Russian", "Arabic", "Greek" ]

export const CONSOLIDATED_REQUEST_HISTORY = {
  TIMELINE_SUCCESS:"Exported Timeline successfully",
  TIMELINE_FAILURE:"Failed to Export Timeline",
  CONSOLIDATED_PDF_SUCCESS:"Exported Consolidated History successfully",
  CONSOLIDATED_PDF_FAILURE:"Failed to Export Consolidated History"
}
export const STEPS_BY_PAGE = {
    app: [
      {
        target: ".sideNavButton_Home",
        content: "This is the homepage of the application, find the brodcasting related things and training videos here",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_Dashboard",
        content: "This is the dashboard where you can find various analytics and reports",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_Workspace",
        content: "This is the menu where you can find your open and completed tasks",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_MasterData",
        content: "Here you can find the Master Data related information",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_RequestBench",
        content: "Access Active/completed requests and scheduler management",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_DataCheck",
        content: "Identify inconsistencies in accordance to business rules to ensure optimal data quality",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_DocumentManagement",
        content: "All uploaded documents in the DMS are available for viewing here",
        disableBeacon: true,
      },
      {
        target: ".sideNavButton_ConfigCockpit",
        content: "Administrative responsibilities and configurations are managed here",
        disableBeacon: true,
      },
      {
        target: ".aiChat",
        content: "You can ask any question related to material master data",
        disableBeacon: true,
      },
      {
        target: ".settingIcon",
        content: "You can change your application settings here",
        disableBeacon: true,
      },
      {
        target: ".guideIcon",
        content: "You can take application guide through this",
        disableBeacon: true,
      },
      {
        target: ".healthMonitorIcon",
        content: "You can monitor system health here",
        disableBeacon: true,
      },
      {
        target: ".openChatIcon",
        content: "You can open the chat window",
        disableBeacon: true,
      },
      {
        target: ".notificationIcon",
        content: "You can see notifications here",
        disableBeacon: true,
      }
      
    ],
    requestBench: [
      {
        target: ".parentChildSwitch",
        content: "You can switch between parent and child requests By toggling this",
        disableBeacon: true,
      },
      {
        target: ".requestBenchFilter",
        content: "You can filter requests here",
        disableBeacon: true,
      },
      {
        target: ".moduleName",
        content: "You can navigate to other objects requests here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      // {
      //   target: ".cancelRequest",
      //   content: "You can cancel requests here",
      //   disableBeacon: true,
      // },
      {
        target: ".manageScheduler",
        content: "You can Manage the Scheduled requests here",
        disableBeacon: true,
      },
      // {
      //   target: ".viewFlow",
      //   content: "You can Manage here",
      //   disableBeacon: true,
      // }
    ],
    materialManagement : [
      {
        target: ".filterMaterial",
        content: "You can filter materials here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonMaterial",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      },
      {
        target: ".sapdataMaterial",
        content: "You can view SAP data on clicking this button",
        disableBeacon: true,
      }
    ],
    Bom :[
      {
        target: ".filterBillOfMaterial",
        content: "You can filter Bill Of Material here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonBOM",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    internalOrder :[
      {
        target: ".filterIO",
        content: "You can filter Internal Order here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonAll_createRequest",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      },
      // {
      //   target: ".createRequestButtonAll-sapExport",
      //   content: "You can create new request on clicking this button",
      //   disableBeacon: true,
      // }
    ],
    BankKey :[
      {
        target: ".filterBK",
        content: "You can filter Bank Key here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonBK",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    article :[
      {
        target: ".filterArticle",
        content: "You can filter Article here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonArticle",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      },
      {
        target: ".sapdataAM",
        content: "You can view SAP data on clicking this button",
        disableBeacon: true,
      }
    ],
    costcenter :[
      {
        target: ".filterCC",
        content: "You can filter Cost Center here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonCC",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    profitcenter :[
      {
        target: ".filterPC",
        content: "You can filter Profit Center here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonPC",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    generalLedger :[
      {
        target: ".filterGL",
        content: "You can filter General Ledger here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonGL",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    CCG :[
      {
        target: ".filterCCG",
        content: "You can filter Cost Center Group here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonCCG",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    PCG :[
      {
        target: ".filterPCG",
        content: "You can filter Cost Center Group here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonPCG",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    GLH :[
      {
        target: ".filterGLH",
        content: "You can filter Cost Center Group here",
        disableBeacon: true,
      },
      {
        target: ".createRequestButtonGLH",
        content: "You can create new request on clicking this button",
        disableBeacon: true,
      }
    ],
    DC :[
      {
        target: ".filterDC",
        content: "You can filter Data Cleanse here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".requestbutton",
        content: "You can Request new Data Cleanse on clicking this button",
        disableBeacon: true,
      }
    ],
    documentManage :[
      {
        target: ".filterDM",
        content: "You can filter Documents here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      }
    ],
    dashBoard :[
      {
        target: ".manageDashBoard",
        content: "You can Manage Dashboard here",
        disableBeacon: true,
      },
      {
        target: ".parentChildSwitchDB",
        content: "You can switch here",
        disableBeacon: true,
      },
      {
        target: ".tabbed",
        content: "You can switch here",
        disableBeacon: true,
      },
      {
        target: ".filterDashBoard",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
      {
        target: ".kpiCard",
        content: "You can click to see the data here",
        disableBeacon: true,
      }
    ],
    // MyTasks : [
    //   {
    //     target: ".",
    //     content: "This is the Workspace",
    //     disableBeacon: true,
    //   },
    // ]
    broadConfig : [
      {
        target: ".filterBroadCast",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
      {
        target: ".btn-mr",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
      {
        target: ".btn-nb",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
    ],
    documentConfig : [
      {
        target: ".uploadDoc",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
      {
        target: ".refreshButtonCD",
        content: "You can filter Dashboard here",
        disableBeacon: true,
      },
    ],
    workflowConfig: [
      {
        target: ".filterWorkFlow",
        content: "You can filter here",
        disableBeacon: true,
      },
      {
        target: ".module-WF",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".searchtable",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".buttonChangeWorkFlow",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".buttonCreateWorkFlow",
        content: "You can Search here",
        disableBeacon: true,
      },
    ],
    emailTemplate:[
      {
        target: ".createButtonNewTemplate",
        content: "You can Search here",
        disableBeacon: true,
      },
      {
        target: ".btn-ml",
        content: "You can Search here",
        disableBeacon: true,
      },
    ]
  };
