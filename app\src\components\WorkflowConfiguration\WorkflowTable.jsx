import { Close as CloseIcon, DeleteForeverOutlined } from "@mui/icons-material";
import DeleteOutlinedIcon from "@mui/icons-material/DeleteOutlined";
import PreviewIcon from "@mui/icons-material/Preview";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined';
import useLang from "@hooks/useLang";
import { colors } from "@constant/colors";
import { Button, DialogContent, DialogActions, IconButton, Tooltip } from "@mui/material";
import * as React from "react";
import { useState } from "react";
import ReusableTable from "../Common/ReusableTable";
import { DELETE_MODAL_BUTTONS_NAME, DIALOUGE_BOX_MESSAGES, ACTIVE_TAB_MODULE_MAP } from "@constant/enum";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { useSnackbar } from "@hooks/useSnackbar";
import { destination_Admin } from "../../destinationVariables";
import { doAjax } from "../Common/fetchService";
import { END_POINTS } from "@constant/apiEndPoints";
import { button_Outlined, button_Primary } from "../Common/commonStyles";

function WorkflowTable({ 
    isLoading, 
    tableData, 
    selectedRows, 
    activeTab, 
    onSelectionChange, 
    onSearch, 
    onChangeLogExport, 
    onDownloadAllData, 
    onDelete, 
    onView, 
    onEdit, 
    onViewChangelog,
    rmDataRows,
    setTableData
}) {
    const { t } = useLang();
    const { showSnackbar } = useSnackbar();
    const [isDeleteDialogVisible, setIsDeleteDialogVisible] = useState({
        data: {},
        isVisible: false,
    });
    const [blurLoading, setBlurLoading] = useState(false);

    const columns = [
        {
            field: "workflowId",
            headerName: t("Workflow Id"),
            flex: 0.5,
            editable: false,
            align: "center"
        },
        {
            field: "workflowName",
            headerName: t("Workflow Name"),
            flex: 1,
            editable: false,
        },
        {
            field: "scenario",
            headerName: t("Scenario"),
            flex: 1,
        },
        {
            field: "createdAt",
            headerName: t("Created At"),
            flex: 1,
        },
        {
            field: "updatedAt",
            headerName: t("Updated At"),
            flex: 1,
        },
        {
            field: "createdBy",
            headerName: t("Created By"),
            flex: 1.5,
            align: "left",
        },
        {
            field: "actions",
            headerName: t("Action"),
            sortable: false,
            flex: 1,
            align: "center",
            headerAlign: "center",
            disableClickEventBubbling: true,
            renderCell: (params) => {
                return (
                    <div>
                        <Tooltip title={t("Cancel")}>
                            <IconButton
                                disabled={false}
                                aria-label="View Metadata"
                                onClick={(e) => {
                                    e.stopPropagation()
                                    setIsDeleteDialogVisible({ data: params, isVisible: true });
                                }}
                            >
                                <DeleteOutlinedIcon
                                    sx={{
                                        color: (theme) => ("#cc3300"),
                                        padding: "1px"
                                    }}
                                />
                            </IconButton>
                        </Tooltip>

                        <Tooltip title={t("View Workflow")}>
                            <IconButton
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onView(params.row?.workflowId);
                                }}
                                disabled={false}
                            >
                                <PreviewIcon sx={{ color: `${colors.blue.indigo}`, padding: "1px" }}
                                />
                            </IconButton>
                        </Tooltip>

                        <Tooltip title={t("Edit Workflow")}>
                            <IconButton
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onEdit(params.row?.workflowId);
                                }}
                                disabled={false}
                            >
                                <ModeEditOutlineOutlinedIcon sx={{ color: `${colors.warning.amber}`, padding: "1px" }}
                                />
                            </IconButton>
                        </Tooltip>

                        <Tooltip title={t("View Changelog")}>
                            <IconButton
                                onClick={(e) => {
                                    e.stopPropagation()
                                    onViewChangelog(params.row?.workflowId);
                                }}
                                disabled={false}
                            >
                                <TrackChangesTwoToneIcon sx={{ color: `${colors.icon.green}`, padding: "1px" }}
                                />
                            </IconButton>
                        </Tooltip>
                    </div>
                );
            },
        },
    ];

    const handleDelete = () => {
        setBlurLoading(true);
        const payload = {
            "workflowId": isDeleteDialogVisible?.data?.row?.workflowId
        };
        const hSuccess = (data) => {
            setBlurLoading(false);
            showSnackbar(data?.data, "success");
            setIsDeleteDialogVisible({ data: {}, isVisible: false });
            // Refresh the data after deletion
            onDelete(null); // Signal parent to refresh
        };
        const hError = (error) => {
            setBlurLoading(false);
            showSnackbar(error?.message, "error");
        };
        const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.DELETE_WF}`;
        doAjax(newUrl, "post", hSuccess, hError, payload)
    }

    const handleSearchAction = (value) => {
        if (!value) {
            setTableData([...rmDataRows]);
            return;
        }
        const selected = rmDataRows.filter((row) => {
            let rowMatched = false;
            let keys = Object.keys(row);

            for (let k = 0; k < keys.length; k++) {
                rowMatched = !row[keys[k]]
                    ? false
                    : row?.[keys?.[k]] &&
                    row?.[keys?.[k]]
                        .toString()
                        .toLowerCase()
                        ?.indexOf(value?.toLowerCase()) != -1;

                if (rowMatched) break;
            }
            return rowMatched;
        });

        setTableData([...selected]);
        if (onSearch) onSearch(value);
    };

    return (
        <>
            <ReusableTable
                module={"WorkflowBench"}
                isLoading={isLoading}
                width="100%"
                title={t("Workflow Details List")}
                rows={tableData ?? []}
                columns={columns}
                getRowIdValue={"id"}
                showSearch={true}
                showChangeLogExport={true}
                showAllWFDataExport={true}
                onSearch={handleSearchAction}
                handleChangeLogExport={onChangeLogExport}
                handleDownloadWFAllData={onDownloadAllData}
                checkboxSelection={true}
                onRowsSelectionHandler={onSelectionChange}
                selectionModel={selectedRows}
                disableSelectionOnClick
            />

            {isDeleteDialogVisible?.isVisible && (
                <CustomDialog
                    isOpen={isDeleteDialogVisible?.isVisible}
                    titleIcon={
                        <DeleteForeverOutlined
                            size="small"
                            color="error"
                            sx={{ fontSize: "20px" }}
                        />
                    }
                    Title={"Delete Workflow!"}
                    handleClose={() =>
                        setIsDeleteDialogVisible({
                            data: {},
                            isVisible: false,
                        })}
                >
                    <DialogContent sx={{ mt: 2 }}>
                        {DIALOUGE_BOX_MESSAGES.CANCEL_MESSAGE}
                    </DialogContent>
                    <DialogActions>
                        <Button 
                            variant="outlined" 
                            size="small" 
                            sx={{ ...button_Outlined }} 
                            onClick={() => setIsDeleteDialogVisible({ data: {}, isVisible: false })}
                        >
                            {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
                        </Button>
                        <Button 
                            variant="contained" 
                            size="small" 
                            sx={{ ...button_Primary }} 
                            onClick={handleDelete}
                        >
                            {t(DELETE_MODAL_BUTTONS_NAME.DELETE)}
                        </Button>
                    </DialogActions>
                </CustomDialog>
            )}
        </>
    );
}

export default WorkflowTable;