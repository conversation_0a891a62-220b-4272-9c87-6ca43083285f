import { Box, Button, Checkbox, TextField, Typography, IconButton, <PERSON>ltip, Tabs, Tab, Paper, Stack, Dialog, DialogTitle, DialogContent, DialogActions, FormControl, FormLabel, RadioGroup, FormControlLabel, Radio, Grid } from "@mui/material";
import FlexibleValidationDialog from "@components/Common/FlexibleValidationDialog";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import { useState, useEffect } from "react";
import useLang from "@hooks/useLang";
import { DataGrid } from "@mui/x-data-grid";
import { v4 as uuidv4 } from "uuid";
import { DeleteOutlineOutlined as DeleteOutlineOutlinedIcon, CloseFullscreen as CloseFullscreenIcon, CropFree as CropFreeIcon, FormatColorResetRounded, TaskAlt as TaskAltIcon, CancelOutlined as CancelOutlinedIcon } from "@mui/icons-material";
import { useSelector, useDispatch } from "react-redux";
import { updateTableColumnDataIO, deleteRowDataIO, setActiveRowIdIO, setSelectedOrderTypeIO, setValidatedRowsIO, setValidatedStatusIO, setOriginalRowDataIO, setOriginalTabDataIO, setDropDownDataIO, setOpenDialogIO, setIOpayloadData, setIOPayloadFields } from "./slice/InternalOrderSlice";
import useInternalOrderFieldConfig from "@hooks/useInternalOrderFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import { MODULE, MODULE_MAP, REQUEST_TYPE, SUCCESS_MESSAGES, ERROR_MESSAGES, REQUEST_STATUS, ENABLE_STATUSES } from "@constant/enum";
import { useNavigate, useLocation } from "react-router-dom";
import { createPayloadForIO } from "../../functions";
import { destination_InternalOrder } from "../../destinationVariables";
import { END_POINTS } from "../../constant/apiEndPoints";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { doAjax } from "@components/Common/fetchService";
import { useSnackbar } from "@hooks/useSnackbar";
import useLogger from "@hooks/useLogger";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import useInternalOrderValidation from "./hooks/useInternalOrderValidation";

const RequestDetailsIO = (props) => {
  const { t } = useLang();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const isrequestId = queryParams.get("RequestId");
  const reqBench = queryParams.get("reqBench");
  const reqBenchData = location?.state || {};
  const { fetchInternalOrderFieldConfig, fieldConfigByOrderType } = useInternalOrderFieldConfig();
  const { getButtonsDisplayGlobal, showWfLevels } = useButtonDTConfig();
  const { checkValidation, checkForDuplicateRows } = useInternalOrderValidation();

  // State management
  const [rows, setRows] = useState([]);
  const [isGridZoomed, setIsGridZoomed] = useState(false);
  const [page, setPage] = useState(0);
  const [selectedTab, setSelectedTab] = useState(0);
  const [isTabsZoomed, setIsTabsZoomed] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("");
  const [wfLevels, setWfLevels] = useState([]);

  // Validation state management
  const [missingMandatoryFields, setMissingMandatoryFields] = useState(null);
  const [mandatoryFailedView, setMandatoryFailedView] = useState(null);
  const [fieldErrors, setFieldErrors] = useState([]);
  const [activeViewTab, setActiveViewTab] = useState("");
  const [missingValidationCards, setMissingValidationCards] = useState([]);

  // FlexibleValidationDialog state (like Bank Key)
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  const [errorFieldMap, setErrorFieldMap] = useState({});
  const [customMessageDialog, setCustomMessageDialog] = useState({
    open: false,
    message: "",
    title: ""
  });

  // With/Without Reference state
  const openDialog = useSelector((state) => state.internalOrder.isOpenDialog);
  const [withReference, setWithReference] = useState("yes");
  const [selectedDialogOrderType, setSelectedDialogOrderType] = useState("");
  const [selectedDialogCompCode, setSelectedDialogCompCode] = useState("");
  const [selectedDialogInternalOrder, setSelectedDialogInternalOrder] = useState("");
  const [isDropDownLoading, setIsDropDownLoading] = useState(false);
  const [selectedPreviousRowId, setSelectedPreviousRowId] = useState("");

  const { showSnackbar } = useSnackbar();
  const { customError, log } = useLogger();
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();

  // Get persistent state from Redux
  const activeRowId = useSelector((state) => state.internalOrder.activeRowId);
  const selectedOrderType = useSelector((state) => state.internalOrder.selectedOrderType);
  const IODropdownData = useSelector((state) => state.internalOrder.dropDownDataIO);
  const IOpayloadData = useSelector((state) => state.internalOrder.IOpayloadData);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const savedRequestData = useSelector((state) => state.internalOrder.savedReqData);
  const reduxPayload = useSelector((state) => state.internalOrder);
  const requestHeaderSlice = useSelector((state) => state.internalOrder.requestHeaderDTIO);

  const validatedRows = useSelector((state) => state.internalOrder.validatedRows);
  const changeLogDataIO = useSelector((state) => state.changeLog.createChangeLogDataIO);
  const validatedRowsStatus = useSelector((state) => state.internalOrder.validatedRowsStatus);
  const requestId = savedRequestData?.RequestId || IOpayloadData?.requestHeaderData?.RequestId || "";
  const requestHeaderData = IOpayloadData?.requestHeaderData || "";
  const childRequestHeaderData = IOpayloadData?.childRequestHeaderData || "";
  const disableCheck = !ENABLE_STATUSES.includes(props?.requestStatus) || (isrequestId && !reqBench);

  // Dynamic data for workflow and button configuration
  const dynamicData = {
    // Workflow data
    taskId: taskData?.taskId || null,
    processInstanceId: taskData?.processInstanceId || null,
    assignee: taskData?.assignee || null,

    // Button configuration
    buttonConfig: filteredButtons,

    // Request metadata
    requestType: REQUEST_TYPE?.CREATE,
    moduleType: MODULE_MAP?.IO,

    // User context
    userContext: {
      userId: taskData?.assignee,
      timestamp: new Date().toISOString(),
    },

    // Spread any additional task data
    ...taskData,
  };

  const internalOrderTabs = (fieldConfigByOrderType[selectedOrderType]?.allTabsData || []).filter((tab) => tab.tab.toLowerCase() !== "header" && tab.tab.toLowerCase() !== "request header");

  // Control dialog opening based on conditions (like bankKey)
  useEffect(() => {
    if (
      (isrequestId && !reqBench) ||
      (reqBench && reqBenchData?.reqStatus !== REQUEST_STATUS?.DRAFT) ||
      (reqBench && reqBenchData?.reqStatus === REQUEST_STATUS?.DRAFT && reqBenchData?.objectNumbers !== "Not Available")
    ) {
      dispatch(setOpenDialogIO(false));
    }
  }, []);

  useEffect(() => {
    if (taskData?.ATTRIBUTE_1) {
      getButtonsDisplayGlobal("Internal Order", "MDG_DYN_BTN_DT", "v3");
    }
  }, [taskData]);

  useEffect(() => {
    const fetchWorkflowLevels = async () => {
      try {
        const workflowLevelsDtData = await getDynamicWorkflowDT(requestHeaderData?.RequestType, requestHeaderData?.Region, "", childRequestHeaderData?.InternalOrderGroup, taskData?.ATTRIBUTE_3, "v1", "MDG_INTORD_DYNAMIC_WORKFLOW_DT", MODULE_MAP?.IO);
        setWfLevels(workflowLevelsDtData);
      } catch (err) {
        customError(err);
      }
    };
    if (requestHeaderData?.RequestType && requestHeaderData?.Region && taskData?.ATTRIBUTE_3) {
      fetchWorkflowLevels();
    }
  }, [requestHeaderData, taskData?.ATTRIBUTE_3]);
  useEffect(() => {
    const rowsBodyData = IOpayloadData?.rowsBodyData || {};
    const reduxRows = Object.keys(rowsBodyData).map((uniqueId) => ({
      id: uniqueId,
      included: rowsBodyData[uniqueId]?.included ?? true,
      internalOrder: rowsBodyData[uniqueId]?.internalOrder ?? "",
      controllingArea: rowsBodyData[uniqueId]?.controllingArea ?? "",
      orderType: rowsBodyData[uniqueId]?.orderType ?? "",
      description: rowsBodyData[uniqueId]?.description ?? "",
      compCode: rowsBodyData[uniqueId]?.compCode ?? "",
    }));

    if (reduxRows.length > 0) {
      setRows(reduxRows);

      // If no activeRowId is set but we have rows, set the first row as active
      if (!activeRowId && reduxRows.length > 0) {
        const firstRow = reduxRows[0];
        const orderTypeValue = firstRow.orderType?.code || firstRow.orderType;
        dispatch(setActiveRowIdIO(firstRow.id));
        if (orderTypeValue) {
          dispatch(setSelectedOrderTypeIO(orderTypeValue));
        }
      }
    }
  }, [IOpayloadData?.rowsBodyData, activeRowId, dispatch]);

  // Auto-select first row and load its field configuration
  useEffect(() => {
    if (rows.length > 0 && !activeRowId) {
      const firstRow = rows[0];
      const orderTypeValue = firstRow.orderType?.code || firstRow.orderType;

      dispatch(setActiveRowIdIO(firstRow.id));

      if (orderTypeValue) {
        dispatch(setSelectedOrderTypeIO(orderTypeValue));
        try {
          fetchInternalOrderFieldConfig(orderTypeValue);
        } catch (error) {
          customError("Failed to fetch field configuration for first row:", error);
        }
      }
    }
  }, [rows, activeRowId, dispatch]);

  useEffect(() => {
    if (selectedOrderType) {
      fetchInternalOrderFieldConfig(selectedOrderType);
    }
  }, [selectedOrderType]);

  const handleButtonClick = async (type, remarks) => {
    if (type === "VALIDATE") {
      validateAllRows(type);
      return;
    }

    setBlurLoading(true);

    const apiEndpoint = END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.IO]?.[requestHeaderData?.RequestType]?.[type];
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderData, requestId, taskData, selectedLevel,remarks,changeLogDataIO);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(data?.message || SUCCESS_MESSAGES?.DEFAULT, "success");

      if (apiEndpoint?.NAVIGATE_TO) {
        navigate(apiEndpoint?.NAVIGATE_TO);
      }
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(error?.error || ERROR_MESSAGES.IO_PROCESSING_ERROR, "error");
    };

    doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
  };

  const validateAllRows = async () => {
    // First check for duplicate rows within the current data
    if (!checkForDuplicateRows(rows)) {
      return; // Stop validation if duplicates found
    }

    // Validate all rows for mandatory fields
    let allValid = true;
    let firstInvalidRow = null;

    for (let i = 0; i < rows.length; i++) {
      const row = rows[i];
      const { missingFields, isValid, viewType, failedView } = checkValidation(row);

      if (!isValid && missingFields) {
        allValid = false;
        if (!firstInvalidRow) {
          firstInvalidRow = { row, missingFields, viewType, failedView };
        }

        // Mark row as invalid
        dispatch(setValidatedStatusIO({ rowId: row.id, status: "error" }));
        dispatch(setValidatedRowsIO({ rowId: row.id, isValid: false }));
      } else {
        // Mark row as valid
        dispatch(setValidatedStatusIO({ rowId: row.id, status: "success" }));
        dispatch(setValidatedRowsIO({ rowId: row.id, isValid: true }));
      }
    }

    if (!allValid && firstInvalidRow) {
      // Set the active row to the first invalid row
      dispatch(setActiveRowIdIO(firstInvalidRow.row.id));

      // Set validation states for highlighting
      setMissingMandatoryFields(firstInvalidRow.missingFields);
      setMandatoryFailedView(firstInvalidRow.failedView);

      // Set field errors for individual field highlighting
      if (typeof firstInvalidRow.missingFields === 'object' && !Array.isArray(firstInvalidRow.missingFields)) {
        // View-level validation errors
        const allMissingFieldNames = Object.values(firstInvalidRow.missingFields).flat();
        setFieldErrors(allMissingFieldNames);

        // Navigate to the failed view tab (like Material module)
        if (firstInvalidRow.viewType) {
          const viewIndex = internalOrderTabs.findIndex((tab) => tab.tab === firstInvalidRow.viewType);
          if (viewIndex !== -1) {
            setSelectedTab(viewIndex);
            setActiveViewTab(firstInvalidRow.viewType);

            // Set missingValidationCards to contain the view name (like Material module)
            setMissingValidationCards([firstInvalidRow.viewType]);
          }
        }

        // Prepare data for FlexibleValidationDialog
        const errorFieldsForRow = [];
        let flatMissingFields = [];

        Object.entries(firstInvalidRow.missingFields).forEach(([viewName, fields]) => {
          flatMissingFields.push(...fields);
          // For field highlighting, we need to track which fields are missing
          fields.forEach(fieldName => {
            // Find the corresponding jsonName for this fieldName
            const orderType = typeof firstInvalidRow.row.orderType === "object" ? firstInvalidRow.row.orderType?.code : firstInvalidRow.row.orderType;
            const fieldConfig = fieldConfigByOrderType[orderType];
            if (fieldConfig && fieldConfig.mandatoryFields) {
              Object.values(fieldConfig.mandatoryFields).forEach(mandatoryFields => {
                const field = mandatoryFields.find(f => f.fieldName === fieldName);
                if (field) {
                  errorFieldsForRow.push(field.jsonName);
                }
              });
            }
          });
        });

        // Set error field map for highlighting (like Bank Key)
        setErrorFieldMap((prev) => ({
          ...prev,
          [firstInvalidRow.row.id]: errorFieldsForRow,
        }));

        // Show FlexibleValidationDialog with missing fields
        setMissingFields(flatMissingFields);
        setMissingFieldsDialogOpen(true);
      } else {
        // Table-level validation errors
        setFieldErrors([]);
        setMissingValidationCards([]);
        setMissingFields(firstInvalidRow.missingFields);
        setMissingFieldsDialogOpen(true);
      }

      return; // Stop validation if mandatory fields are missing
    }

    // Clear validation states if all rows are valid
    setMissingMandatoryFields(null);
    setMandatoryFailedView(null);
    setFieldErrors([]);
    setMissingValidationCards([]);

    // Proceed with API validation if all mandatory fields are filled
    setBlurLoading(true);
    const finalPayload = createPayloadForIO(reduxPayload, requestHeaderSlice, requestId, taskData, dynamicData,changeLogDataIO);

    const hSuccess = (data) => {
      setBlurLoading(false);
      showSnackbar(SUCCESS_MESSAGES.IO_VALIDATION_INITIATED, "success");
      navigate("/requestbench");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(ERROR_MESSAGES.IO_VALIDATION_ERROR, "error");
    };

    doAjax(`/${destination_InternalOrder}${END_POINTS.MASS_ACTION.VALIDATE_MASS_INTERNAL_ORDER}`, "POST", hSuccess, hError, finalPayload);
  };

  // Individual row validation function

  // Get validation status for a row
  const getValidationStatus = (row) => {
    const isValidated = validatedRows[row.id];
    const validationStatus = validatedRowsStatus[row.id];

    if (isValidated === true) return "success";
    if (validationStatus === "error") return "error";
    return "default";
  };
  const areAllRowsValidated = () => {
    if (rows.length === 0) return true;

    return rows.every(row => {
      const isValidated = validatedRows[row.id];
      return isValidated === true;
    });
  };

  // Individual row validation functions
  const checkDuplicateInternalOrder = (row) => {
    // UI-level duplicate check within current form data using local rows state
    const duplicateFound = rows.some(
      (otherRow) =>
        otherRow.id !== row.id && // Don't compare with itself
        otherRow.orderType === row.orderType &&
        otherRow.controllingArea === row.controllingArea &&
        otherRow.compCode === row.compCode &&
        otherRow.internalOrder === row.internalOrder &&
        row.internalOrder && // Only check if internal order is not empty
        row.internalOrder.trim() !== "" // And not just whitespace
    );

    return !duplicateFound;
  };
  const handleValidateRow = async (row) => {
    const { missingFields, isValid, viewType, failedView } = checkValidation(row);

    if (!isValid && missingFields) {
      // Set validation states for highlighting
      setMissingMandatoryFields(missingFields);
      setMandatoryFailedView(failedView);

      // Set field errors for individual field highlighting
      if (typeof missingFields === 'object' && !Array.isArray(missingFields)) {
        // View-level validation errors
        const allMissingFieldNames = Object.values(missingFields).flat();
        setFieldErrors(allMissingFieldNames);



        // Navigate to the failed view tab (like Material module)
        if (viewType) {
          // Use internalOrderTabs instead of allTabsData for correct index
          const viewIndex = internalOrderTabs.findIndex(tab => tab.tab === viewType);

          if (viewIndex !== -1) {
            setSelectedTab(viewIndex);
            setActiveViewTab(viewType);

            // Set missingValidationCards to contain the view name (like Material module)
            setMissingValidationCards([viewType]);
          }
        }

        // Prepare data for FlexibleValidationDialog
        const errorFieldsForRow = [];
        let flatMissingFields = [];

        Object.entries(missingFields).forEach(([viewName, fields]) => {
          flatMissingFields.push(...fields);
          // For field highlighting, we need to track which fields are missing
          fields.forEach(fieldName => {
            // Find the corresponding jsonName for this fieldName
            const orderType = typeof row.orderType === "object" ? row.orderType?.code : row.orderType;
            const fieldConfig = fieldConfigByOrderType[orderType];
            if (fieldConfig && fieldConfig.mandatoryFields) {
              Object.values(fieldConfig.mandatoryFields).forEach(mandatoryFields => {
                const field = mandatoryFields.find(f => f.fieldName === fieldName);
                if (field) {
                  errorFieldsForRow.push(field.jsonName);
                }
              });
            }
          });
        });

        // Set error field map for highlighting (like Bank Key)
        setErrorFieldMap((prev) => ({
          ...prev,
          [row.id]: errorFieldsForRow,
        }));

        // Show FlexibleValidationDialog with missing fields
        setMissingFields(flatMissingFields);
        setMissingFieldsDialogOpen(true);
      } else {
        // Table-level validation errors
        setFieldErrors([]);
        setMissingValidationCards([]);

        // Show FlexibleValidationDialog for table-level errors
        setMissingFields(missingFields);
        setMissingFieldsDialogOpen(true);
      }

      dispatch(setValidatedStatusIO({ rowId: row.id, status: "error" }));
      dispatch(setValidatedRowsIO({ rowId: row.id, isValid: false }));
      return;
    }

    // Clear validation states if validation passes
    setMissingMandatoryFields(null);
    setMandatoryFailedView(null);
    setFieldErrors([]);
    setMissingValidationCards([]);

    // Check for duplicates if mandatory validation passes
    const isDuplicateFound = !checkDuplicateInternalOrder(row);

    if (isDuplicateFound) {
      showSnackbar(ERROR_MESSAGES.IO_DUPLICATE_ERROR, "error");
      dispatch(setValidatedStatusIO({ rowId: row.id, status: "error" }));
      dispatch(setValidatedRowsIO({ rowId: row.id, isValid: false }));
      return;
    }

    // Store original data for dirty checking
    dispatch(
      setOriginalRowDataIO({
        rowId: row.id,
        data: JSON.parse(JSON.stringify(row)),
      })
    );

    // Get tab data for this row
    const tabData = IOpayloadData.rowsBodyData[row.id]?.payload || {};
    dispatch(
      setOriginalTabDataIO({
        rowId: row.id,
        data: JSON.parse(JSON.stringify(tabData)),
      })
    );

    showSnackbar(SUCCESS_MESSAGES.IO_VALIDATION_SUCCESSFUL, "success");
    dispatch(setValidatedStatusIO({ rowId: row.id, status: "success" }));
    dispatch(setValidatedRowsIO({ rowId: row.id, isValid: true }));
  };



  const getInternalOrders = (orderType, compCode) => {
    setIsDropDownLoading(true);
    const url = `/${destination_InternalOrder}${END_POINTS.DISPLAY_INTERNAL_ORDER.GET_IO}`;
    const payload = {
      orderType: orderType,
      compCode: compCode,
      top: "10",
      skip: ""
    };

    doAjax(
      url,
      "post",
      (data) => {
        setIsDropDownLoading(false);
        dispatch(
          setDropDownDataIO({
            keyName: "internalOrderOptions",
            data: data?.body || [],
          })
        );
      },
      () => {
        setIsDropDownLoading(false);
      },
      payload
    );
  };

  const handleAddRow = () => {
    dispatch(setOpenDialogIO(true));
  };

  const handleDialogClose = () => {
    dispatch(setOpenDialogIO(false));
    setWithReference("yes");
    setSelectedDialogOrderType("");
    setSelectedDialogCompCode("");
    setSelectedDialogInternalOrder("");
    // Clear internal order options
    dispatch(
      setDropDownDataIO({
        keyName: "internalOrderOptions",
        data: [],
      })
    );
  };

  const handleAddWithRef = async (orderType, compCode, internalOrder) => {
    setBlurLoading(true);
    const url = `/${destination_InternalOrder}${END_POINTS.DISPLAY_INTERNAL_ORDER.DISPLAY_MASS_DTO}`;
    const payload = {
      internalOrder: internalOrder
    };

    const hSuccess = (response) => {
      setBlurLoading(false);
      const apiResponse = response?.body?.[0] || {};

      let rowsBody = { ...IOpayloadData.rowsBodyData };
      let rowsHeader = [];
      const dynamicKey = uuidv4();
      if (!dynamicKey) return;
      // Create new row for header data (table display)
      const newRow = {
        id: dynamicKey,
        included: true,
        internalOrder: "",
        controllingArea: apiResponse.CoArea || "",
        orderType: apiResponse.orderType || orderType,
        description:  "",
        compCode: apiResponse.CompCode||"",
        validated: "default",
 
      };

      // Handle rows header data (for table display)
      if (IOpayloadData?.rowsHeaderData?.length === 0) {
        rowsHeader.push(newRow);
      } else {
        rowsHeader = [...IOpayloadData.rowsHeaderData];
        const existingIndex = IOpayloadData.rowsHeaderData.findIndex(row => row.id === dynamicKey);

        if (existingIndex !== -1) {
          rowsHeader[existingIndex] = newRow;
        } else {
          rowsHeader.push(newRow);
        }
      }

      // Store complete API response in rowsBodyData (for payload)
      rowsBody[dynamicKey] = {
        ...newRow,
        payload:{...apiResponse}
      };
      dispatch(setIOpayloadData({ keyName: "rowsHeaderData", data: rowsHeader }));
      dispatch(setIOpayloadData({ keyName: "rowsBodyData", data: rowsBody }));
      setRows(rowsHeader);

      // Clear dialog selections
      setSelectedDialogOrderType("");
      setSelectedDialogCompCode("");
      setSelectedDialogInternalOrder("");

      showSnackbar(SUCCESS_MESSAGES.IO_ROW_ADDED_WITH_REFERENCE, "success");
    };

    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(error?.error || ERROR_MESSAGES.IO_REFERENCE_ERROR || "Error adding row with reference", "error");
    };

    // Make the API call
    doAjax(url, "POST", hSuccess, hError, payload);
  };

  const handleAddWithoutRef = () => {
    const id = uuidv4();
    const newRow = {
      id,
      included: true,
      internalOrder: "",
      controllingArea: "",
      orderType: "",
      description: "",
      compCode: "",
    };

    // Add to local state
    setRows([...rows, newRow]);

    // Initialize table column data in Redux
    Object.entries(newRow).forEach(([fieldName, fieldValue]) => {
      if (fieldName !== "id") {
        dispatch(
          updateTableColumnDataIO({
            uniqueId: id,
            keyName: fieldName,
            data: fieldValue,
          })
        );
      }
    });
  };
  const handleAddWithPreviousRowRef = (previousRowId) => {
    const existingRowsBody = { ...IOpayloadData.rowsBodyData };
    const previousRowData = existingRowsBody[previousRowId] || {};
          let rowsHeader = [];
    const dynamicKey = uuidv4();
      // Store complete API response in rowsBodyData (for payload)
      existingRowsBody[dynamicKey] = {
      ...previousRowData,
      description: "",
      internalOrder: "",
      id: dynamicKey,

      };
      dispatch(setIOpayloadData({ keyName: "rowsBodyData", data: existingRowsBody }));
      setRows(rowsHeader);

      // Clear dialog selections
      setSelectedDialogOrderType("");
      setSelectedDialogCompCode("");
      setSelectedDialogInternalOrder("");

      showSnackbar(SUCCESS_MESSAGES.IO_ROW_ADDED_WITH_REFERENCE, "success");
  };
   const handleProceed = async () => {
    if (withReference === "no") {
      handleAddWithoutRef();
    } else if (withReference === "yes") {
      if (selectedPreviousRowId) {
        handleAddWithPreviousRowRef(selectedPreviousRowId);
      } else {
        await handleAddWithRef(selectedDialogOrderType, selectedDialogCompCode, selectedDialogInternalOrder);
      }
    }
    handleDialogClose();
  };
  const handleCellEdit = ({ id, field, value }) => {
    // Convert description to uppercase
    const processedValue = field === "description" ? value.toUpperCase() : value;

    // 1. Update local state
    const updatedRows = rows.map((row) => (row.id === id ? { ...row, [field]: processedValue } : row));
    setRows(updatedRows);

    // 2. Update Redux (if needed)
    dispatch(
      updateTableColumnDataIO({
        uniqueId: id,
        keyName: field,
        data: processedValue,
      })
    );
    if (field === "orderType") {
      const selectedCode = typeof value === "object" ? value.code : value;
      dispatch(setActiveRowIdIO(id));
      dispatch(setSelectedOrderTypeIO(selectedCode));
      setSelectedTab(0);
    }
  };

  const handleRowClick = (params) => {
    const clickedRow = params.row;
    const orderTypeValue = typeof clickedRow.orderType === "object" ? clickedRow.orderType?.code : clickedRow.orderType;

    // Set active row
    dispatch(setActiveRowIdIO(clickedRow.id));
    setSelectedTab(0);

    // Normalize orderType into object form
    if (orderTypeValue) {
      dispatch(setSelectedOrderTypeIO(orderTypeValue));
    } else {
      dispatch(setSelectedOrderTypeIO(""));
    }
  };

  const handleDeleteRow = (rowId) => {
    const updatedRows = rows.filter((row) => row.id !== rowId);
    setRows(updatedRows);

    // Use deleteRowDataIO which removes from both header and body data, plus cleanup
    dispatch(deleteRowDataIO({ rowId: rowId }));

    // If the deleted row was the active row, clear the active row
    if (activeRowId === rowId) {
      dispatch(setActiveRowIdIO(null));
      dispatch(setSelectedOrderTypeIO(""));
    }
  };

  const toggleGridZoom = () => {
    setIsGridZoomed(!isGridZoomed);
  };

  const toggleTabsZoom = () => {
    setIsTabsZoomed(!isTabsZoomed);
    if (isGridZoomed); // optional: exit grid zoom when tabs zoom
  };
  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Simple columns definition
  const columns = [
    {
      field: "included",
      headerName: t("Included"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => (
        <Checkbox
          checked={params.row.included}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "included",
              value: e.target.checked,
            })
          }
        />
      ),
    },
    {
      field: "lineNumber",
      headerName: t("Line Number"),
      flex: 0.6,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        const rowIndex = rows?.findIndex((row) => row?.id === params?.row?.id);
        const lineNumber = (rowIndex + 1) * 10;
        return <div>{lineNumber}</div>;
      },
    },
    {
      field: "internalOrder",
      headerName: t("Internal Order"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      editable: false,
      renderCell: (params) => {
        return (
          <TextField
            value={params.row.internalOrder || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "internalOrder",
                value: newValue,
              })
            }
            size="small"
            fullWidth
            variant="outlined"
            disabled="true"
            placeholder={t("Enter Internal Order")}
          />
        );
      },
    },
    //will uncomment when internal order id logic is clear from suvendu
    // {
    //   field: "internalOrderid",
    //   headerName: t("Internal Order ID"),
    //   flex: 0.5,
    //   align: "center",
    //   headerAlign: "center",
    //   editable: false,
    //   renderCell: (params) => {
    //     // Get the internal order ID from the display API response stored in payload
    //     const fullRowData = IOpayloadData?.rowsBodyData?.[params.row.id] || {};
    //     const payloadData = fullRowData.payload || {};
    //     const internalOrderId = payloadData.InternalOrderId || payloadData.internalOrderId || payloadData.ID || "";

    //     return (
    //       <div style={{ textAlign: "center" }}>
    //         {internalOrderId || "-"}
    //       </div>
    //     );
    //   },
    // },
    {
      field: "controllingArea",
      headerName: t("Controlling Area"),
      renderHeader: () => (
        <span>
          {t("Controlling Area")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      align: "center",
      editable: false,
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.controllingArea || []}
            value={params.row.controllingArea || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "controllingArea",
                value: newValue,
              })
            }
            placeholder={t("SELECT CONTROLLING AREA")}
            minWidth="90%"
            listWidth={235}
            disabled={disableCheck}
          />
        );
      },
    },
    {
      field: "orderType",
      headerName: t("Order Type"),
      renderHeader: () => (
        <span>
          {t("Order Type")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            // options={IODropdownData?.orderType || []}
            options={IODropdownData?.orderType || []}
            value={params.row.orderType || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "orderType",
                value: newValue,
              })
            }
            placeholder={t("Select Order Type")}
            minWidth="90%"
            listWidth={235}
            disabled={disableCheck}
          />
        );
      },
    },
    {
      field: "description",
      headerName: t("Description"),
      renderHeader: () => (
        <span>
          {t("Description")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => (
        <TextField
          value={params.row.description || ""}
          onChange={(e) =>
            handleCellEdit({
              id: params.row.id,
              field: "description",
              value: e.target.value,
            })
          }
          size="small"
          fullWidth
          variant="outlined"
          placeholder={t("Enter Description")}
          disabled={disableCheck}
        />
      ),
    },
    {
      field: "compCode",
      headerName: t("Comp Code"),
      renderHeader: () => (
        <span>
          {t("Comp Code")}
          <span style={{ color: "red" }}>*</span>
        </span>
      ),
      flex: 0.7,
      editable: false,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        return (
          <SingleSelectDropdown
            options={IODropdownData?.compCode || []}
            value={params.row.compCode || ""}
            onChange={(newValue) =>
              handleCellEdit({
                id: params.row.id,
                field: "compCode",
                value: newValue,
              })
            }
            placeholder={t("SELECT COMPANY CODE")}
            minWidth="90%"
            listWidth={235}
            disabled={disableCheck}
          />
        );
      },
    },
    {
      field: "actions",
      headerName: t("Actions"),
      flex: 0.5,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        let validateStatus = getValidationStatus(params?.row);

        const handleValidate = async (e) => {
          e.stopPropagation();
          await handleValidateRow(params.row);
        };

        return (
          <Stack direction="row" alignItems="center" sx={{ marginLeft: "0.5rem", marginRight: "0.5rem" }} spacing={0.5}>
            <Tooltip title={validateStatus === "success" ? "Validated Successfully" : validateStatus === "error" ? t("Validation Failed") : t("Click to Validate")}>
              <IconButton onClick={handleValidate} color={validateStatus === "success" ? "success" : validateStatus === "error" ? "error" : "default"}>
                {validateStatus === "error" ? <CancelOutlinedIcon /> : <TaskAltIcon />}
              </IconButton>
            </Tooltip>

            <Tooltip title={t("Delete Row")}>
              <IconButton onClick={() => handleDeleteRow(params.row.id)} color="error">
                <DeleteOutlineOutlinedIcon />
              </IconButton>
            </Tooltip>
          </Stack>
        );
      },
    },
  ];
  return (
    <div>
      <div style={{ padding: "0", width: "100%", margin: "0", marginTop: "20px" }}>
        <Box
          sx={{
            position: isGridZoomed ? "fixed" : "relative",
            top: isGridZoomed ? 0 : "auto",
            left: isGridZoomed ? 0 : "auto",
            right: isGridZoomed ? 0 : "auto",
            bottom: isGridZoomed ? 0 : "auto",
            width: isGridZoomed ? "100vw" : "100%",
            height: isGridZoomed ? "100vh" : "auto",
            zIndex: isGridZoomed ? 1004 : undefined,
            backgroundColor: isGridZoomed ? "white" : "transparent",
            padding: isGridZoomed ? "20px" : "0",
            display: "flex",
            flexDirection: "column",
            boxShadow: isGridZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            transition: "all 0.3s ease",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("Internal Order List")}</Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={handleAddRow}
                disabled={!areAllRowsValidated()}
              >
                + {t("Add")}
              </Button>
              <Tooltip title={isGridZoomed ? t("Exit Zoom") : t("Zoom In")} sx={{ zIndex: "1009" }}>
                <IconButton
                  onClick={toggleGridZoom}
                  color="primary"
                  sx={{
                    backgroundColor: "rgba(0, 0, 0, 0.05)",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  {isGridZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <div style={{ width: "100%", height: "100%", overflowX: "auto" }}>
            <div style={{ height: "100%" }}>
              <DataGrid
                rows={rows}
                columns={columns}
                pageSize={50}
                autoHeight={false}
                page={page}
                rowsPerPageOptions={[50]}
                onPageChange={(newPage) => handlePageChange(newPage)}
                onRowClick={handleRowClick}
                pagination
                disableSelectionOnClick
                getRowClassName={(params) => (params.id === activeRowId ? "selected-row" : "")}
                style={{
                  border: "1px solid #ccc",
                  borderRadius: "8px",
                  width: "100%",
                  height: isGridZoomed ? "calc(100vh - 150px)" : `${Math.min(rows.length * 50 + 130, 300)}px`,
                  overflow: "auto",
                }}
                sx={{
                  "& .MuiDataGrid-cell": {
                    padding: "8px",
                  },
                  "& .MuiDataGrid-columnHeaders": {
                    backgroundColor: "#f5f5f5",
                    fontWeight: "bold",
                  },
                  "& .selected-row": {
                    backgroundColor: "rgb(234 233 255)",
                  },
                }}
              />
            </div>
          </div>
        </Box>
      </div>

      {/* Field Configuration Tabs Section */}
      {selectedOrderType && internalOrderTabs.length > 0 && (
        <Box
          sx={{
            position: isTabsZoomed ? "fixed" : "relative",
            top: isTabsZoomed ? 0 : "auto",
            left: isTabsZoomed ? 0 : "auto",
            right: isTabsZoomed ? 0 : "auto",
            bottom: isTabsZoomed ? 0 : "auto",
            width: isTabsZoomed ? "100vw" : "100%",
            height: isTabsZoomed ? "100vh" : "auto",
            zIndex: isTabsZoomed ? 1300 : "auto", // higher zIndex for full-screen components
            backgroundColor: isTabsZoomed ? "#fff" : "transparent",
            padding: isTabsZoomed ? "24px" : "0", // add padding to prevent top cut-off
            boxSizing: "border-box", // ensure padding is respected
            overflow: "auto", // prevent content from overflowing
            margin: isTabsZoomed ? 0 : "20px 0 0 0", // reset margin if zoomed
            display: "flex",
            flexDirection: "column",
            boxShadow: isTabsZoomed ? "0px 0px 15px rgba(0, 0, 0, 0.2)" : "none",
            borderRadius: "8px",
            border: "1px solid #e0e0e0",
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "8px 16px",
              backgroundColor: "#f5f5f5",
              borderRadius: "8px 8px 0 0",
            }}
          >
            <Typography variant="h6">{t("View Details")}</Typography>
            <Tooltip title={isTabsZoomed ? t("Exit Zoom") : t("Zoom In")}>
              <IconButton
                onClick={toggleTabsZoom}
                color="primary"
                sx={{
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  "&:hover": {
                    backgroundColor: "rgba(0, 0, 0, 0.1)",
                  },
                }}
              >
                {isTabsZoomed ? <CloseFullscreenIcon /> : <CropFreeIcon />}
              </IconButton>
            </Tooltip>
          </Box>
          <Tabs
            value={selectedTab}
            onChange={(_, newValue) => setSelectedTab(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: 1,
              borderColor: "divider",
              "& .MuiTabs-indicator": {
                backgroundColor: "#3026B9",
                height: "3px",
              },
            }}
          >
            {internalOrderTabs.map((tab, index) => (
              <Tab key={index} label={tab.tab} />
            ))}
          </Tabs>
          <Paper elevation={2} sx={{ p: 3, borderRadius: 4, marginTop: 2 }}>
            {internalOrderTabs[selectedTab] && activeRowId && (
              <GenericTabsGlobal
                disabled={false}
                basicDataTabDetails={internalOrderTabs[selectedTab].data}
                dropDownData={IODropdownData}
                activeViewTab={internalOrderTabs[selectedTab].tab}
                uniqueId={activeRowId}
                selectedRow={(() => {
                  const tableRow = rows.find((row) => row.id === activeRowId) || {};
                  const fullRowData = IOpayloadData?.rowsBodyData?.[activeRowId] || {};
                  const payloadData = fullRowData.payload || {};

                  // Extract metadata and table fields (exclude payload to avoid circular reference)
                  const { payload, ...metadataAndTableFields } = fullRowData;

                  // Merge in correct priority order:
                  // 1. Base metadata and table fields from Redux
                  // 2. Current table UI state (for real-time updates)
                  // 3. Payload data (view field edits) - HIGHEST PRIORITY
                  const mergedData = {
                    ...metadataAndTableFields,  // Base data from transformation
                    ...tableRow,               // Current UI state
                    ...payloadData            // User edits (most important)
                  };

                  return mergedData;
                })()}
                module={MODULE.IO}
                fieldErrors={errorFieldMap[activeRowId] || []}
                missingValidationCards={missingValidationCards}
              />
            )}
          </Paper>
        </Box>
      )}

      {/* Bottom Navigation for Actions */}
      <BottomNavGlobal handleSaveAsDraft={handleButtonClick} handleSubmitForReview={handleButtonClick} handleSubmitForApprove={handleButtonClick} handleSendBack={handleButtonClick} handleCorrection={handleButtonClick} handleRejectAndCancel={handleButtonClick} handleValidateAndSyndicate={handleButtonClick} validateAllRows={validateAllRows} isSaveAsDraftEnabled={true} validateEnabled={true} filteredButtons={filteredButtons} moduleName={MODULE_MAP.IO} showWfLevels={showWfLevels} selectedLevel={selectedLevel} setSelectedLevel={setSelectedLevel} workFlowLevels={wfLevels} />

      {/* With/Without Reference Dialog */}
      {openDialog && (
        <Dialog
          fullWidth
          open={openDialog}
          maxWidth="lg"
          onClose={handleDialogClose}
          sx={{
            "&::webkit-scrollbar": {
              width: "1px",
            },
          }}
        >
          <DialogTitle
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
              height: "max-content",
              padding: ".5rem",
              paddingLeft: "1rem",
              background: (theme) => theme.palette.primary.light,
              display: "flex",
            }}
          >
            <Typography variant="h6">Add New</Typography>
          </DialogTitle>
          <DialogContent
            sx={{
              padding: ".5rem 1rem",
              alignItems: "center",
              justifyContent: "center",
              margin: "0px 25px",
            }}
          >
            <FormControl component="fieldset" sx={{ paddingBottom: "2%" }}>
              <FormLabel
                component="legend"
                sx={{
                  padding: "15px 0px",
                  fontWeight: "600",
                  fontSize: "15px",
                }}
              >
                How would you like to proceed?
              </FormLabel>
              <RadioGroup row aria-label="internal-order-reference" name="internal-order-reference" value={withReference} onChange={(event) => setWithReference(event.target.value)}>
                <FormControlLabel value="yes" control={<Radio />} label="With Reference" />
                <FormControlLabel value="no" control={<Radio />} label="Without Reference" />
              </RadioGroup>
            </FormControl>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Grid container spacing={2}>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={IODropdownData?.orderType || []}
                      value={IODropdownData?.orderType?.find((item) => item.code === selectedDialogOrderType) || null}
                      onChange={(newValue) => {
                        setSelectedDialogOrderType(newValue?.code || "");
                        setSelectedDialogInternalOrder(""); // Clear internal order selection
                        // Clear internal order options when order type changes
                        dispatch(
                          setDropDownDataIO({
                            keyName: "internalOrderOptions",
                            data: [],
                          })
                        );
                        // Fetch new internal orders if both order type and comp code are selected
                        if (newValue?.code && selectedDialogCompCode) {
                          getInternalOrders(newValue?.code, selectedDialogCompCode);
                        }
                      }}
                      placeholder="SELECT ORDER TYPE"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={IODropdownData?.compCode || []}
                      value={IODropdownData?.compCode?.find((item) => item.code === selectedDialogCompCode) || null}
                      onChange={(newValue) => {
                        setSelectedDialogCompCode(newValue?.code || "");
                        setSelectedDialogInternalOrder(""); // Clear internal order selection
                        // Clear internal order options when comp code changes
                        dispatch(
                          setDropDownDataIO({
                            keyName: "internalOrderOptions",
                            data: [],
                          })
                        );
                        // Fetch new internal orders if both order type and comp code are selected
                        if (selectedDialogOrderType && newValue?.code) {
                          getInternalOrders(selectedDialogOrderType, newValue?.code);
                        }
                      }}
                      placeholder="SELECT COMPANY CODE"
                      minWidth="90%"
                      listWidth={235}
                      disabled={withReference === "no"}
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <SingleSelectDropdown
                      options={IODropdownData?.internalOrderOptions || []}
                      value={IODropdownData?.internalOrderOptions?.find((item) => item.code === selectedDialogInternalOrder) || null}
                      onChange={(newValue) => {
                        setSelectedDialogInternalOrder(newValue?.code || "");
                      }}
                      isLoading={isDropDownLoading}
                      placeholder="SELECT INTERNAL ORDER"
                      disabled={withReference === "no"}
                      minWidth="90%"
                      listWidth={235}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            {rows.length > 0 && (
              <>
                <Box sx={{ textAlign: "center", my: 2 }}>
                  <Typography variant="body2" sx={{ color: "#666", fontWeight: "bold" }}>
                    OR
                  </Typography>
                </Box>

                <SingleSelectDropdown
                  options={rows.map((row, index) => {
                    const lineNumber = (index + 1) * 10;
                    return {
                      code: lineNumber.toString(),
                      desc: `${lineNumber}`,
                    };
                  })}
                  value={
                    selectedPreviousRowId
                      ? (() => {
                          const selectedRow = rows.find((r) => r.id === selectedPreviousRowId);
                          if (selectedRow) {
                            const index = rows.findIndex((r) => r.id === selectedPreviousRowId);
                            const lineNumber = (index + 1) * 10;
                            const orderTypeDisplay = typeof selectedRow?.orderType === "object" ? selectedRow?.orderType?.desc || selectedRow?.orderType?.code || "" : selectedRow?.orderType || "";
                            return {
                              code: lineNumber.toString(),
                              desc: `${lineNumber}${orderTypeDisplay ? ` (${orderTypeDisplay})` : ""}`,
                            };
                          }
                          return null;
                        })()
                      : null
                  }
                  onChange={(newValue) => {
                    const lineNumber = parseInt(newValue?.code || "0");
                    const rowIndex = lineNumber / 10 - 1;
                    const selectedRow = rows[rowIndex];
                    setSelectedPreviousRowId(selectedRow?.id || "");
                  }}
                  placeholder="SELECT INTERNAL ORDER LINE NUMBER"
                  minWidth="90%"
                  listWidth={450}
                />
              </>
            )}
          </DialogContent>
          <DialogActions sx={{ display: "flex", justifyContent: "end" }}>
            <Button
              sx={{
                width: "max-content",
                textTransform: "capitalize",
              }}
              onClick={handleDialogClose}
              variant="outlined"
            >
              Cancel
            </Button>
            <Button className="button_primary--normal" type="save" onClick={handleProceed} variant="contained">
              Proceed
            </Button>
          </DialogActions>
        </Dialog>
      )}

      {/* Loading and Notification Components */}
      {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}

      {/* FlexibleValidationDialog for Missing Fields (like Bank Key) */}
      <FlexibleValidationDialog
        open={missingFieldsDialogOpen}
        onClose={() => setMissingFieldsDialogOpen(false)}
        missingFields={missingFields}
        t={t}
      />

      {/* Custom Message Dialog for other validation errors */}
      <FlexibleValidationDialog
        open={customMessageDialog.open}
        onClose={() => setCustomMessageDialog({ open: false, message: "", title: "" })}
        customMessage={customMessageDialog.message}
        title={customMessageDialog.title}
        t={t}
      />
    </div>
  );
};

export default RequestDetailsIO;