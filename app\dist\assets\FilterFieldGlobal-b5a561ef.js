import{cq as Yt,Z as d,cZ as Tt,bf as vt,s as Ee,n as h,aO as se,e8 as qt,eK as wt,e4 as Wt,eL as zt,eM as Kt,eN as Jt,eO as Qt,u as Ge,a as Dt,c5 as N,r as V,j as r,O as Re,aZ as Ae,c as Q,d as ue,aa as Be,F as je,eP as mt,eQ as Nt,eR as xe,dX as Ce,eS as Ie,eT as ct,cC as Oe,eU as it,eV as Bt,eW as ot,eX as ge,t as lt,dO as Zt,dQ as Xt,C as dt,bU as kt,aD as Le,T as Ct,b3 as Lt,eY as Gt,c4 as Ft,eZ as ut,c6 as Pt,bJ as ft,e_ as Vt,e$ as pt,dg as ea,dq as ta,f0 as aa,bF as na,bG as sa,a9 as _t,bE as yt,dP as ra,B as ca,bu as Te,a1 as ia,a6 as oa,a7 as da,ep as ua,f1 as la}from"./index-226a1e75.js";import{S as xt,A as ma}from"./AdapterDayjs-ca6db362.js";import{D as Na}from"./DatePicker-e5574363.js";const ha=()=>{const t=Date.UTC(new Date().getUTCFullYear(),new Date().getUTCMonth(),new Date().getUTCDate(),new Date().getUTCHours(),new Date().getUTCMinutes(),new Date().getUTCSeconds());return{sapFormat:`/Date(${t})/`,humanReadable:Yt(t).format("DD MMM YYYY HH:mm:ss UTC")}},St=ha().sapFormat;function ga(t,a){return t!=null&&t.hasOwnProperty(a)?t[a]:null}const Mt=(t,a,e)=>{var m;try{return((m=e==null?void 0:e.rowsBodyData)==null?void 0:m[t][a])||"-"}catch{return"-"}};function Rt(t){const a=/\/Date\((\d+)\)\//.exec(t);return a?parseInt(a[1]):0}function ya(t){const a={};for(const m of t){const R=`${m.ObjectNo}_${m.FieldName}`;a[R]||(a[R]=[]),a[R].push(m)}const e=[];for(const m of Object.values(a))if(new Set(m.map(u=>u.CurrentValue)).size===1)e.push(m[0]);else{const u=m.reduce((n,x)=>Rt(n.ChangedOn)>Rt(x.ChangedOn)?n:x);e.push(u)}return e}function At(t){let a=ya(t);const e={};return a.forEach(m=>{const R=m.ObjectNo;e[R]||(e[R]=[]),e[R].push({ObjectNo:`${m==null?void 0:m.ObjectNo}`,ChangedBy:m.ChangedBy,ChangedOn:m.ChangedOn,FieldName:m.FieldName,PreviousValue:m.PreviousValue,jsonName:m.JsonName,CurrentValue:m.CurrentValue,SAPValue:m.SAPValue})}),e}d.primary.main,d.primary.accent,d.primary.medium,d.blue.main,d.blue.indigo,d.blue.sky,d.secondary.teal,d.secondary.green,d.secondary.lime,d.secondary.yellow,d.secondary.amber,d.icon.orange,d.warning.orange,d.warning.deep,d.error.mild,d.error.dark,d.icon.purple,d.success.vibrant,d.info.bright;const ba=(t,a=!0,e=null)=>{try{const m=localStorage.getItem(t);return m===null?e:a?JSON.parse(m):m}catch{return e}},Sa=(t,a,e=!0)=>{try{const m=e?JSON.stringify(a):a;return localStorage.setItem(t,m),!0}catch{return!1}},Ot=Tt(vt.MODULE,!0,{}),ht=()=>{const t=Ee(),a=h(n=>n.userManagement.userData),e=h(n=>n.changeLog.createPayloadCopyForChangeLog||[]);h(n=>{var x;return((x=n.generalLedger.payload)==null?void 0:x.requestHeaderdata)||{}}),h(n=>n.changeLog.createChangeLogDataGL);const m=h(n=>n.changeLog.createTemplateArray);return{updateChangeLogGl:({objectNumber:n,uniqueId:x,viewName:l,plantData:j,fieldName:c,jsonName:E,currentValue:T,isDescriptionData:q=!1,isUnitOfMeasure:w=!1,isAdditionalEAN:Y=!1,uomId:D=null,eanId:B=null,language:S})=>{var W,z,$,G,Z,ae,ne,b,M,_,H;let v;v=Mt(x,E,e),ga(Wt,l),(W=e==null?void 0:e.rowsBodyData)==null||W[x];let y="";Ot==((z=se)==null?void 0:z.GL)?y=(G=($=e==null?void 0:e.rowsBodyData)==null?void 0:$[x])==null?void 0:G.GLAccount:Ot==((Z=se)==null?void 0:Z.CC)?y=y=(ne=(ae=e==null?void 0:e.rowsBodyData)==null?void 0:ae[x])==null?void 0:ne.Costcenter:y=y=(M=(b=e==null?void 0:e.rowsBodyData)==null?void 0:b[x])==null?void 0:M.ProfitCenter;let L=(H=(_=e==null?void 0:e.rowsBodyData)==null?void 0:_[x])==null?void 0:H.Accounttype;const A={ObjectNo:`${y}`,ChangedBy:a==null?void 0:a.emailId,ChangedOn:St,FieldName:c,JsonName:E,PreviousValue:v,CurrentValue:T,SAPValue:v,ObjNo:y,AccType:L};t(qt(A));const te=[...m,A];let F={...At(te)};t(wt(F))},updateChangeLogGlForChange:({uniqueId:n,filedName:x,jsonName:l,currentValue:j,requestId:c,childRequestId:E,accountNumber:T})=>{var v,y,L,A;let q;q=Mt(n,l,e),(y=(v=e==null?void 0:e.rowsBodyData)==null?void 0:v[n])==null||y.GLAccount;let w=(A=(L=e==null?void 0:e.rowsBodyData)==null?void 0:L[n])==null?void 0:A.Accounttype;const Y={ObjectNo:T,ChangedBy:a==null?void 0:a.emailId,ChangedOn:St,FieldName:x,JsonName:l,PreviousValue:q,CurrentValue:j,SAPValue:q,ObjNo:T,AccType:w};t(qt(Y));const D=[...m,Y];let S={...At(D)};t(wt(S))}}},$t=()=>{const t=Ee(),a=h(n=>n.changeLog.createPayloadCopyForChangeLog||{}),e=h(n=>n.changeLog.createChangeLogDataBK||{}),m=h(n=>n.changeLog.createChangeLogDataIO||{}),R=h(n=>n.userManagement.userData);return{updateChangeLogGlobal:({uniqueId:n,viewName:x,fieldName:l,jsonName:j,currentValue:c,module:E=(T=>(T=se)==null?void 0:T.BK)()})=>{var y,L,A,te,p,F,W,z,$,G;const q=zt().humanReadable,w=Kt(n,j,a,E),Y=E===((y=se)==null?void 0:y.BK)?(L=a==null?void 0:a[n])==null?void 0:L.BankNo:E===((A=se)==null?void 0:A.IO)&&((te=a==null?void 0:a[n])==null?void 0:te.internalOrder)||n,D={ObjectNo:Y,ChangedBy:R==null?void 0:R.emailId,ChangedOn:q,FieldName:l,PreviousValue:w,CurrentValue:c,SAPValue:w,ViewName:x,JsonName:j},B=E===((p=se)==null?void 0:p.BK)?((F=e==null?void 0:e[n])==null?void 0:F.changeLog)||[]:E===((W=se)==null?void 0:W.IO)?((z=m==null?void 0:m[n])==null?void 0:z.changeLog)||[]:[],S=B.findIndex(Z=>Z.ObjectNo===Y&&Z.JsonName===j);let v;S!==-1?(v=[...B],v[S]={...v[S],CurrentValue:c,ChangedOn:q}):v=[...B,D],E===(($=se)==null?void 0:$.BK)?t(Jt({uniqueId:n,changeLog:v})):E===((G=se)==null?void 0:G.IO)&&t(Qt({uniqueId:n,changeLog:v}))}}},bt=t=>{var H,O,ce,le,K,ee,de,o,k,I,J,ie,re,oe,ye,qe,me,_e,$e,He,Ue,Ye,We,ze,Ke;const{uniqueId:a,field:e,disabled:m,handleChange:R,module:u}=t,n=Ge(),l=new URLSearchParams(n.search).get("RequestId"),j=h(f=>f.payload.payloadData),c=mt[u]||(()=>({})),E=Nt[u]||(()=>({})),T=(H=c==null?void 0:c[a])==null?void 0:H[E];let q=(O=c==null?void 0:c[a])==null?void 0:O.RequestStatus;const w=h(f=>f.profitCenter.payload||{}),Y=h(f=>f.costCenter.payload||{}),D=h(f=>f.generalLedger.payload||{}),B=h(f=>f.bom.BOMpayloadData||{}),S=h(f=>f.internalOrder.IOpayloadData||{}),v=h(f=>f.bankKey.payload||{}),{updateChangeLogGl:y}=ht(),{updateChangeLogGlobal:L}=$t(),A=["displayGeneralLedgerMasterdata","displayCostCenter","displayProfitCenter","displayBankKeySAPData"].some(f=>{var P;return(P=n==null?void 0:n.pathname)==null?void 0:P.includes(f)}),{t:te}=Dt(),p=h(f=>f.hierarchyData||{}),F=u===((ce=N)==null?void 0:ce.CC)?((K=(le=Y==null?void 0:Y.rowsBodyData)==null?void 0:le[a])==null?void 0:K[e==null?void 0:e.jsonName])??((ee=Y==null?void 0:Y.requestHeaderData)==null?void 0:ee[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===((de=N)==null?void 0:de.GL)?((k=(o=D==null?void 0:D.rowsBodyData)==null?void 0:o[a])==null?void 0:k[e==null?void 0:e.jsonName])??((I=D==null?void 0:D.requestHeaderData)==null?void 0:I[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===((J=N)==null?void 0:J.PCG)||u===((ie=N)==null?void 0:ie.CCG)||u===((re=N)==null?void 0:re.CEG)?((oe=p==null?void 0:p.requestHeaderData)==null?void 0:oe[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===N.BOM?(B==null?void 0:B[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===N.IO?((me=(qe=(ye=S==null?void 0:S.rowsBodyData)==null?void 0:ye[a])==null?void 0:qe.payload)==null?void 0:me[e==null?void 0:e.jsonName])??((_e=S==null?void 0:S.requestHeaderData)==null?void 0:_e[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===(($e=se)==null?void 0:$e.BK)?((Ue=(He=v==null?void 0:v.rowsBodyData)==null?void 0:He[a])==null?void 0:Ue[e==null?void 0:e.jsonName])??((Ye=v==null?void 0:v.requestHeaderData)==null?void 0:Ye[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":((ze=(We=w==null?void 0:w.rowsBodyData)==null?void 0:We[a])==null?void 0:ze[e==null?void 0:e.jsonName])??((Ke=w==null?void 0:w.requestHeaderData)==null?void 0:Ke[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"",[W,z]=V.useState(F),[$,G]=V.useState(!1),[Z,ae]=V.useState({}),[ne,b]=V.useState(!1),M=Ee();V.useEffect(()=>{z(F),ae(f=>({...f,[e==null?void 0:e.jsonName]:(F==null?void 0:F.length)||0}))},[F]);const _=f=>{var Je,Qe,Ze,Xe,Fe,Pe,fe,Ve,pe,et,tt,at,nt,s,g,X,i,U,Ne,he,we,ke;let P=f.target.value;if(e.dataType==="QUAN"?P.length>e.maxLength&&(P=P.slice(0,e.maxLength)):(P=P.replace(/[^a-zA-Z0-9\-&()#,. ]/g,"").replace(/\s{2,}/g," ").replace(/\s*([-&()#,.])\s*/g,"$1").trimStart(),ae(ve=>({...ve,[e==null?void 0:e.jsonName]:P.length}))),z(P),u===((Je=N)==null?void 0:Je.CC))M(xe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:P,viewID:e==null?void 0:e.viewName})),l&&!Ce.includes(q)&&y({objectNumber:T,uniqueId:a||"",viewName:(Qe=t==null?void 0:t.field)==null?void 0:Qe.viewName,plantData:"",fieldName:(Ze=t==null?void 0:t.field)==null?void 0:Ze.fieldName,jsonName:(Xe=t==null?void 0:t.field)==null?void 0:Xe.jsonName,currentValue:P,childRequestId:l});else if(u===((Fe=N)==null?void 0:Fe.GL))M(Ie({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:P,viewID:e==null?void 0:e.viewName})),l&&!Ce.includes(q)&&y({objectNumber:T,uniqueId:a||"",viewName:(Pe=t==null?void 0:t.field)==null?void 0:Pe.viewName,plantData:"",fieldName:(fe=t==null?void 0:t.field)==null?void 0:fe.fieldName,jsonName:(Ve=t==null?void 0:t.field)==null?void 0:Ve.jsonName,currentValue:P,requestId:j==null?void 0:j.RequestId,childRequestId:l});else if(u===((pe=N)==null?void 0:pe.BOM))M(ct({keyName:e==null?void 0:e.jsonName,data:P}));else if(u===((et=N)==null?void 0:et.IO))M(Oe({uniqueId:a||"",keyName:e==null?void 0:e.jsonName,data:P})),l&&L({uniqueId:a||"",viewName:e==null?void 0:e.viewName,fieldName:e==null?void 0:e.fieldName,jsonName:e==null?void 0:e.jsonName,currentValue:P||"",requestId:l,childRequestId:l,module:(tt=se)==null?void 0:tt.IO});else if(u===((at=se)==null?void 0:at.BK)){M(it({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:P})),l&&!Ce.includes(q)&&L({uniqueId:a||"",viewName:(nt=t==null?void 0:t.field)==null?void 0:nt.viewName,fieldName:(s=t==null?void 0:t.field)==null?void 0:s.fieldName,jsonName:(g=t==null?void 0:t.field)==null?void 0:g.jsonName,currentValue:P,requestId:l,childRequestId:l});const ve=(X=v==null?void 0:v.rowsHeaderData)==null?void 0:X.map(De=>De.id===a?{...De,validated:"default"}:De);M(Bt(ve))}else u===((i=N)==null?void 0:i.PCG)||u===((U=N)==null?void 0:U.CCG)||u===((Ne=N)==null?void 0:Ne.CEG)?M(ot({keyName:(e==null?void 0:e.jsonName)||"",data:P})):(M(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:P,viewID:e==null?void 0:e.viewName})),l&&!Ce.includes(q)&&y({objectNumber:T,uniqueId:a||"",viewName:(he=t==null?void 0:t.field)==null?void 0:he.viewName,plantData:"",fieldName:(we=t==null?void 0:t.field)==null?void 0:we.fieldName,jsonName:(ke=t==null?void 0:t.field)==null?void 0:ke.jsonName,currentValue:P,requestId:j==null?void 0:j.RequestId,childRequestId:l}))};return r(je,{children:r(Re,{item:!0,md:2,mb:1.5,children:r(Ae,{children:A?Q("div",{style:{padding:"16px",backgroundColor:d.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[Q(ue,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:te(e==null?void 0:e.fieldName),children:[te(e==null?void 0:e.fieldName)||"Field Name",((e==null?void 0:e.visibility)==="Required"||(e==null?void 0:e.visibility)==="MANDATORY")&&r("span",{style:{color:d.error.darkRed,marginLeft:"2px"},children:"*"})]}),r("div",{style:{fontSize:"0.8rem",color:d.black.dark,marginTop:"4px"},children:Q("span",{style:{fontWeight:500,color:d.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:[W,!W&&r(xt,{fallback:"--"})]})})]}):Q(je,{children:[Q(ue,{variant:"body2",color:"#777",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:e==null?void 0:e.fieldName,children:[e.fieldName,(e.visibility==="Mandatory"||e.visibility==="0")&&r("span",{style:{color:"red"},children:"*"})]}),r(Be,{size:"small",type:e.dataType==="QUAN"?"number":"text",placeholder:m?"":`Enter ${e.fieldName}`,error:t==null?void 0:t.isError,value:W,title:W,onBlur:()=>G(!1),inputProps:{style:{textTransform:"uppercase",fontSize:"12px"},maxLength:e.maxLength},onFocus:()=>G(!0),onClick:()=>b(!0),helperText:t!=null&&t.isError?"This field is required":$&&(Z[e==null?void 0:e.jsonName]===e.maxLength?"Max Length Reached":`${Z[e==null?void 0:e.jsonName]}/${e.maxLength}`),FormHelperTextProps:{sx:{color:t!=null&&t.isError||$&&Z[e==null?void 0:e.jsonName]===e.maxLength?"red":"blue",position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light},"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:t!=null&&t.isError||$&&Z[e==null?void 0:e.jsonName]>=e.maxLength?"red":""},"& fieldset":{borderColor:t!=null&&t.isError||$&&Z[e==null?void 0:e.jsonName]>=e.maxLength?"red":""}}},onChange:_,disabled:m,required:e.visibility==="Mandatory"||e.visibility==="0"})]})})})})},Et=2,Ht=lt.createContext({}),Ca=lt.forwardRef((t,a)=>{const e=lt.useContext(Ht);return r("div",{ref:a,...t,...e})}),va=lt.forwardRef(function(a,e){const{children:m,...R}=a,u=[];m.forEach(j=>{u.push(j)});const n=u.length,x=Xt.HEIGHT,l=()=>n>8?8*x:u.length*x;return r("div",{ref:e,children:r(Ht.Provider,{value:R,children:r(Zt,{itemData:u,height:l()+2*Et,width:"100%",outerElementType:Ca,innerElementType:"ul",itemSize:x,overscanCount:5,itemCount:n,children:({data:j,index:c,style:E})=>{const T=j[c],q={...E,top:E.top+Et};return r("li",{style:{...q,listStyle:"none"},children:T})}})})})}),Da=t=>{var I,J,ie,re,oe,ye,qe,me,_e,$e,He,Ue,Ye,We,ze,Ke,f,P,Je,Qe,Ze,Xe,Fe,Pe,fe,Ve,pe,et,tt,at,nt;const{uniqueId:a,field:e,disabled:m,dropDownData:R,handleChange:u,module:n,isError:x}=t,[l,j]=V.useState(null),c=Ee(),E=ba(vt.MODULE,!0,{}),T=Ge(),{updateChangeLogGl:q}=ht(),{updateChangeLogGlobal:w}=$t(),D=new URLSearchParams(T.search).get("RequestId"),B=h(s=>s.payload.payloadData),S=mt[n]||(()=>({})),v=Nt[n]||(()=>({})),y=(I=S==null?void 0:S[a])==null?void 0:I[v],L=S[a];let A=(ie=(J=S==null?void 0:S[a])==null?void 0:J.Torequestheaderdata)==null?void 0:ie.RequestStatus;const te=["displayGeneralLedgerMasterdata","DisplayMaterialSAPView","displayCostCenter","displayProfitCenter","displayBankKeySAPData"].some(s=>{var g;return(g=T==null?void 0:T.pathname)==null?void 0:g.includes(s)}),{t:p}=Dt();h(s=>s.userManagement.roles);const F=Gt[E]||(()=>({})),W=h(F),z=h(s=>s.profitCenter.payload||{}),$=h(s=>s.costCenter.payload||{}),G=h(s=>s.generalLedger.payload||{}),Z=h(s=>s.hierarchyData||{}),ae=h(s=>{var g;return((g=s==null?void 0:s.payload)==null?void 0:g.changeFieldSelectiondata)||[]}),ne=h(s=>s.bom.BOMpayloadData||{}),b=h(s=>s.internalOrder.IOpayloadData||{}),M=h(s=>s.bankKey.payload||{}),_=n===((re=N)==null?void 0:re.CC)?((ye=(oe=$==null?void 0:$.rowsBodyData)==null?void 0:oe[a])==null?void 0:ye[e==null?void 0:e.jsonName])??((qe=$==null?void 0:$.requestHeaderData)==null?void 0:qe[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":n===((me=N)==null?void 0:me.GL)?(($e=(_e=G==null?void 0:G.rowsBodyData)==null?void 0:_e[a])==null?void 0:$e[e==null?void 0:e.jsonName])??((He=G==null?void 0:G.requestHeaderData)==null?void 0:He[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":n===((Ue=N)==null?void 0:Ue.PCG)||n===((Ye=N)==null?void 0:Ye.CCG)||n===((We=N)==null?void 0:We.CEG)?((ze=Z==null?void 0:Z.requestHeaderData)==null?void 0:ze[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":n===N.BOM?(ne==null?void 0:ne[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":n===N.IO?((P=(f=(Ke=b==null?void 0:b.rowsBodyData)==null?void 0:Ke[a])==null?void 0:f.payload)==null?void 0:P[e==null?void 0:e.jsonName])??((Je=b==null?void 0:b.requestHeaderData)==null?void 0:Je[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":n===((Qe=se)==null?void 0:Qe.BK)?((Xe=(Ze=M==null?void 0:M.rowsBodyData)==null?void 0:Ze[a])==null?void 0:Xe[e==null?void 0:e.jsonName])??((Fe=M==null?void 0:M.requestHeaderData)==null?void 0:Fe[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":((fe=(Pe=z==null?void 0:z.rowsBodyData)==null?void 0:Pe[a])==null?void 0:fe[e==null?void 0:e.jsonName])??((Ve=z==null?void 0:z.requestHeaderData)==null?void 0:Ve[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??null,H=n===((pe=N)==null?void 0:pe.CC)?(tt=(et=$==null?void 0:$.rowsBodyData)==null?void 0:et[a])==null?void 0:tt.AddrCountry:(nt=(at=z==null?void 0:z.rowsBodyData)==null?void 0:at[a])==null?void 0:nt.Country,O=(R==null?void 0:R[e==null?void 0:e.jsonName])||(W==null?void 0:W[e==null?void 0:e.jsonName])||[];V.useEffect(()=>{H&&(de(H),K(H))},[H]),V.useEffect(()=>{var s,g,X,i,U,Ne,he,we,ke,ve,De,st,rt;if(_!=null&&_!=="")if(_!=null&&_.code)j(_);else if(O){if(!Array.isArray(O)&&!Array.isArray(O==null?void 0:O[a])){j(null);return}const C=O!=null&&O.length?O==null?void 0:O.find(be=>{var Se,Me;return((Se=be==null?void 0:be.code)==null?void 0:Se.trim())===((Me=_==null?void 0:_.toString())==null?void 0:Me.trim())}):(s=O==null?void 0:O[a])==null?void 0:s.find(be=>{var Se,Me;return((Se=be==null?void 0:be.code)==null?void 0:Se.trim())===((Me=_==null?void 0:_.toString())==null?void 0:Me.trim())});C?(j({code:C==null?void 0:C.code,desc:C==null?void 0:C.desc}),n===((g=N)==null?void 0:g.CC)?c(xe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc},viewID:e==null?void 0:e.viewName})):n===((X=N)==null?void 0:X.GL)?c(Ie({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc},viewID:e==null?void 0:e.viewName})):n===((i=N)==null?void 0:i.PCG)||n===((U=N)==null?void 0:U.CCG)||n===((Ne=N)==null?void 0:Ne.CEG)?c(ot({keyName:(e==null?void 0:e.jsonName)||"",data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc}})):n===N.BOM?c(ct({keyName:e==null?void 0:e.jsonName,data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc}})):n===N.IO?c(Oe({uniqueId:a||"",keyName:e==null?void 0:e.jsonName,data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc},viewID:e==null?void 0:e.viewName})):n===((he=se)==null?void 0:he.BK)?c(it({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc},viewID:e==null?void 0:e.viewName})):c(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:{code:C==null?void 0:C.code,desc:C==null?void 0:C.desc},viewID:e==null?void 0:e.viewName}))):(j(null),n===((we=N)==null?void 0:we.CC)?c(xe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:null,viewID:e==null?void 0:e.viewName})):n===((ke=N)==null?void 0:ke.GL)?c(Ie({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:null,viewID:e==null?void 0:e.viewName})):n===((ve=N)==null?void 0:ve.PCG)||n===((De=N)==null?void 0:De.CCG)||n===((st=N)==null?void 0:st.CEG)?c(ot({keyName:(e==null?void 0:e.jsonName)||"",data:null})):n===N.BOM?c(ct({keyName:e==null?void 0:e.jsonName,data:null})):n===((rt=se)==null?void 0:rt.BK)?c(it({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:null,viewID:e==null?void 0:e.viewName})):c(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:null,viewID:e==null?void 0:e.viewName})))}else j(null);else j(null)},[_,W]);const ce=s=>{const g=ae.filter(i=>(i==null?void 0:i.MDG_CHANGE_TEMPLATE_NAME)===s&&(i==null?void 0:i.MDG_MAT_CHANGE_TYPE)==="Item"&&(i==null?void 0:i.MDG_MAT_FIELD_VISIBILITY)!=="Hidden"&&(i==null?void 0:i.MDG_MAT_FIELD_VISIBILITY)!=="Display").sort((i,U)=>{const Ne=Number(i==null?void 0:i.MDG_MAT_FIELD_SEQUENCE)||0,he=Number(U==null?void 0:U.MDG_MAT_FIELD_SEQUENCE)||0;return Ne-he}),X=[...new Set(g.map(i=>i==null?void 0:i.MDG_MAT_FIELD_NAME).filter(Boolean))].map(i=>({code:i}));c(ft({keyName:"FieldName",data:X||[],keyName2:a}))},le=s=>{const g=ae.filter(i=>(i==null?void 0:i.MDG_MAT_TEMPLATE)===s).sort((i,U)=>{const Ne=Number(i==null?void 0:i.MDG_SEQUENCE_NO)||0,he=Number(U==null?void 0:U.MDG_SEQUENCE_NO)||0;return Ne-he}),X=[...new Set(g.map(i=>i==null?void 0:i.MDG_MAT_UI_FIELD_NAME).filter(Boolean))].map(i=>({code:i}));c(Vt({keyName:"FieldName",data:X||[],keyName2:a}))},K=s=>{const g=i=>{c(Ft({keyName:"Regio",data:(i==null?void 0:i.body)||[]})),c(ut({keyName:"Regio",data:(i==null?void 0:i.body)||[],keyName2:a}))},X=i=>{};dt(`/${kt}/data/getRegionBasedOnCountry?country=${s}`,"get",g,X)},ee=(s,g)=>{const X=U=>{c(ut({keyName:"AccountId",data:(U==null?void 0:U.body)||[],keyName2:a}))},i=U=>{};dt(`/${pt}/data/getAccountId?companyCode=${s}&houseBank=${g}`,"get",X,i)},de=s=>{const g=i=>{c(Pt({keyName:"AddrRegion",data:(i==null?void 0:i.body)||[]})),c(ut({keyName:"AddrRegion",data:(i==null?void 0:i.body)||[],keyName2:a}))},X=i=>{};dt(`/${kt}/data/getRegionBasedOnCountry?country=${s}`,"get",g,X)},o=s=>{var g={bankCtry:s};const X=U=>{c(ta({keyName:"AddrRegion",data:(U==null?void 0:U.body)||[]})),c(ut({keyName:"AddrRegion",data:(U==null?void 0:U.body)||[],keyName2:a}))},i=U=>{};dt(`/${ea}/data/getRegionBasedOnCountry`,"post",X,i,g)},k=s=>{var g,X,i,U,Ne,he,we,ke,ve,De,st,rt,C,be,Se,Me,It,jt;if(j(s),(e==null?void 0:e.jsonName)==="TemplateName"&&ce(s==null?void 0:s.code),(e==null?void 0:e.jsonName)==="Country"&&K(s==null?void 0:s.code),(e==null?void 0:e.jsonName)==="HouseBank"&&ee(L==null?void 0:L.CompanyCode,s==null?void 0:s.code),(e==null?void 0:e.jsonName)==="AddrCountry"&&de(s==null?void 0:s.code),(e==null?void 0:e.jsonName)==="Country"&&o(s==null?void 0:s.code),n===((g=N)==null?void 0:g.CC))c(xe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})),D&&!Ce.includes(A)&&q({objectNumber:y,uniqueId:a||"",viewName:(X=t==null?void 0:t.field)==null?void 0:X.viewName,plantData:"",fieldName:(i=t==null?void 0:t.field)==null?void 0:i.fieldName,jsonName:(U=t==null?void 0:t.field)==null?void 0:U.jsonName,currentValue:s!=null&&s.desc?`${s==null?void 0:s.code}-${s.desc}`:`${s==null?void 0:s.code}`,requestId:B==null?void 0:B.RequestId,childRequestId:D});else if(n===((Ne=N)==null?void 0:Ne.GL))(e==null?void 0:e.jsonName)==="TemplateName"&&le(s==null?void 0:s.code),c(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})),c(Ie({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})),D&&!Ce.includes(A)&&q({objectNumber:y,uniqueId:a||"",viewName:(he=t==null?void 0:t.field)==null?void 0:he.viewName,plantData:"",fieldName:(we=t==null?void 0:t.field)==null?void 0:we.fieldName,jsonName:(ke=t==null?void 0:t.field)==null?void 0:ke.jsonName,currentValue:`${s==null?void 0:s.code}-${(s==null?void 0:s.desc)??""}`,requestId:B==null?void 0:B.RequestId,childRequestId:D});else if(n===N.BOM)c(ct({keyName:e==null?void 0:e.jsonName,data:s??null}));else if(n===N.IO)c(Oe({uniqueId:a||"",keyName:e==null?void 0:e.jsonName,data:s??null,viewID:e==null?void 0:e.viewName})),D&&w({uniqueId:a||"",viewName:e==null?void 0:e.viewName,fieldName:e==null?void 0:e.fieldName,jsonName:e==null?void 0:e.jsonName,currentValue:(s==null?void 0:s.code)||"",requestId:D,childRequestId:D,module:(ve=se)==null?void 0:ve.IO});else if(n===((De=se)==null?void 0:De.BK)){c(it({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})),D&&!Ce.includes(A)&&w({uniqueId:a||"",viewName:e==null?void 0:e.viewName,fieldName:e==null?void 0:e.fieldName,jsonName:e==null?void 0:e.jsonName,currentValue:s,requestId:D,childRequestId:D});const Ut=(st=M==null?void 0:M.rowsHeaderData)==null?void 0:st.map(gt=>gt.id===a?{...gt,validated:"default"}:gt);c(Bt(Ut))}else(e==null?void 0:e.jsonName)==="Country"&&K(s==null?void 0:s.code),n===((rt=N)==null?void 0:rt.CC)?c(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})):n===((C=N)==null?void 0:C.PCG)||n===((be=N)==null?void 0:be.CCG)||n===((Se=N)==null?void 0:Se.CEG)?c(ot({keyName:(e==null?void 0:e.jsonName)||"",data:s??null})):(c(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:s??null,viewID:e==null?void 0:e.viewName})),D&&!Ce.includes(A)&&q({objectNumber:y,uniqueId:a||"",viewName:(Me=t==null?void 0:t.field)==null?void 0:Me.viewName,plantData:"",fieldName:(It=t==null?void 0:t.field)==null?void 0:It.fieldName,jsonName:(jt=t==null?void 0:t.field)==null?void 0:jt.jsonName,currentValue:`${s==null?void 0:s.code}-${(s==null?void 0:s.desc)??""}`,requestId:B==null?void 0:B.RequestId,childRequestId:D}))};return r(je,{children:r(Re,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:r(Ae,{children:te?Q("div",{style:{padding:"16px",backgroundColor:d.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[Q(ue,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:p(e==null?void 0:e.fieldName),children:[(e==null?void 0:e.fieldName)||"Field Name",((e==null?void 0:e.visibility)==="Required"||(e==null?void 0:e.visibility)===Le.MANDATORY)&&r("span",{style:{color:d.error.dark},children:"*"})]}),r("div",{style:{fontSize:"0.8rem",color:d.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",cursor:"pointer"},children:l!=null&&l.code||l!=null&&l.desc?r(Ct,{title:l!=null&&l.code?`${l==null?void 0:l.code} - ${(l==null?void 0:l.desc)||""}`:"--",arrow:!0,children:Q("span",{children:[r("strong",{style:{fontWeight:600,color:d.secondary.grey,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:l==null?void 0:l.code}),(l==null?void 0:l.desc)&&Q("span",{style:{fontWeight:500,color:d.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:["- ",l==null?void 0:l.desc]})]})}):r(xt,{fallback:"--"})})]}):Q(je,{children:[Q(ue,{variant:"body2",color:d.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:e==null?void 0:e.fieldName,children:[(e==null?void 0:e.fieldName)||"Field Name",((e==null?void 0:e.visibility)==="Required"||(e==null?void 0:e.visibility)===Le.MANDATORY)&&r("span",{style:{color:d.error.dark},children:"*"})]}),r(Lt,{size:"small",disabled:m||e.disabled,options:Array.isArray(O)?O:Array.isArray(O==null?void 0:O[a])?O[a]:[],value:l,getOptionLabel:s=>s!=null&&s.desc?`${(s==null?void 0:s.code)||""} - ${(s==null?void 0:s.desc)||""}`:s!=null&&s.code&&!(s!=null&&s.desc)?`${(s==null?void 0:s.code)||""}`:`${s||""}`,ListboxComponent:va,onChange:(s,g)=>{j(g),k(g||null)},sx:{height:"31px","& .MuiAutocomplete-listbox":{padding:0,"& .MuiAutocomplete-option":{paddingLeft:"16px",paddingTop:"4px",paddingBottom:"4px",justifyContent:"flex-start"}},"& .MuiAutocomplete-option":{display:"flex",alignItems:"center",minHeight:"36px"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light}},renderOption:(s,g)=>{const X=r(ue,{component:"li",style:{fontSize:12,padding:"8px 16px",width:"100%",cursor:"pointer",display:"flex",alignItems:"start"},children:Q("span",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",textAlign:"left",width:"100%"},title:`${g==null?void 0:g.code}${g!=null&&g.desc?` - ${g==null?void 0:g.desc}`:""}`,children:[r("strong",{children:g==null?void 0:g.code}),g!=null&&g.desc?` - ${g==null?void 0:g.desc}`:""]})});return(e==null?void 0:e.jsonName)==="RequestType"?r("li",{...s,children:r(Ct,{title:(g==null?void 0:g.tooltip)||"",placement:"right-end",arrow:!0,children:r("div",{style:{width:"100%"},children:X})})}):r("li",{...s,children:X})},renderInput:s=>{var g,X,i;return r(Be,{...s,required:e.required,error:x,placeholder:m||((g=t.field)==null?void 0:g.visibility)===Le.DISPLAY?"":`SELECT ${((i=(X=t==null?void 0:t.field)==null?void 0:X.fieldName)==null?void 0:i.toUpperCase())||""}`,sx:{"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:x?"red":"#E0E0E0"},"&:hover fieldset":{borderColor:x?"red":"#BDBDBD"},"&.Mui-focused fieldset":{borderColor:x?"red":"#3026B9"}}}})}})]})})})})},xa=t=>{var ee,de;const a=Ee(),e=Ge(),m=new URLSearchParams(e.search),R=h(o=>o.tabsData.changeFieldsDT),{uniqueId:u,field:n,disabled:x,dropDownData:l,handleChange:j,module:c}=t,E=Tt(vt.MODULE,!0,{}),T=Gt[E]||(()=>({})),q=h(T),w=c===((ee=N)==null?void 0:ee.CC)?h(o=>o.profitCenter.payload.requestHeaderData):c===((de=N)==null?void 0:de.GL)?h(o=>o.generalLedger.payload.requestHeaderData):h(o=>o.profitCenter.payload.requestHeaderData),Y=h(o=>o.payload.dynamicKeyValues),D=h(o=>{var k;return(k=o==null?void 0:o.costCenter)==null?void 0:k.payload}),B=R==null?void 0:R["Field Selectivity"],S=m.get("RequestId"),[v,y]=V.useState([]),[L,A]=V.useState(null),[te,p]=V.useState(""),[F,W]=V.useState(!1),z=V.useRef(null),$=(l==null?void 0:l[n==null?void 0:n.jsonName])||(q==null?void 0:q[n==null?void 0:n.jsonName])||[];let G=$.map(o=>(o==null?void 0:o.code)||"");const Z=h(o=>{var k;return((k=o==null?void 0:o.payload)==null?void 0:k.changeFieldSelectiondata)||[]});V.useEffect(()=>{var o,k,I,J;if(B==="Disabled")y(G),ce(G);else{if(S){y(((k=(o=Y==null?void 0:Y.requestHeaderData)==null?void 0:o.FieldName)==null?void 0:k.split("$^$"))||((J=(I=D==null?void 0:D.requestHeaderData)==null?void 0:I.FieldName)==null?void 0:J.split(", "))||[]);return}y([])}},[B,w==null?void 0:w.TemplateName,$]),V.useEffect(()=>{var o;c==((o=N)==null?void 0:o.GL)&&(w!=null&&w.FieldName)&&y([w==null?void 0:w.FieldName])},[Z]);const ae=(o,k)=>{A(o.currentTarget),p(k),W(!0)},ne=()=>{W(!1)},b=()=>{W(!0)},M=()=>{W(!1)},H=!!L?"custom-popover":void 0,O=()=>{v.length===G.length?(y([]),ce([])):(y(G),ce(G))},ce=o=>{var k,I;c===((k=N)==null?void 0:k.CC)&&a(xe({uniqueId:u||"",keyName:(n==null?void 0:n.jsonName)||"",data:o||[]})),c===((I=N)==null?void 0:I.GL)?(a(aa({values:o})),a(Ie({uniqueId:u||"",keyName:(n==null?void 0:n.jsonName)||"",data:o||[]}))):c===N.IO?a(Oe({uniqueId:u||"",keyName:(n==null?void 0:n.jsonName)||"",data:o||[],viewID:(n==null?void 0:n.viewName)||""})):a(ge({uniqueId:u||"",keyName:(n==null?void 0:n.jsonName)||"",data:o||[],viewID:(n==null?void 0:n.viewName)||""}))},le=o=>v.includes(o),K=B==="Disabled";return r(Re,{item:!0,md:2,sx:{marginBottom:"12px !important"},children:(n==null?void 0:n.visibility)==="Hidden"?null:Q(Ae,{children:[Q(ue,{variant:"body2",color:d.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:n==null?void 0:n.fieldName,children:[(n==null?void 0:n.fieldName)||"Field Name",((n==null?void 0:n.visibility)==="Mandatory"||(n==null?void 0:n.visibility)==="0")&&r("span",{style:{color:"red"},children:"*"})]}),r(Lt,{multiple:!0,fullWidth:!0,disableCloseOnSelect:!0,disabled:x,size:"small",value:v,onChange:(o,k,I)=>{if(!K){if(I==="clear"||(k==null?void 0:k.length)===0){y([]),ce([]);return}k.length>0&&k[k.length-1]==="Select All"?O():(y(k),ce(k))}},options:G.length?["Select All",...G]:[],getOptionLabel:o=>`${o}`||"",renderOption:(o,k,{selected:I})=>r("li",{...o,style:{pointerEvents:K?"none":"auto"},children:r(na,{children:r(sa,{control:r(_t,{disabled:K,checked:le(k)||k==="Select All"&&v.length===G.length}),label:r(ue,{style:{fontSize:12},children:r("strong",{children:k})})})})}),renderTags:(o,k)=>{var J,ie;const I=Array.isArray(o)?o.join("<br />"):"";return(o==null?void 0:o.length)>1?Q(je,{children:[r(yt,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"},"&.Mui-disabled":{color:(ie=(J=d)==null?void 0:J.text)==null?void 0:ie.primary,opacity:1}},label:`${o[0]}`,...k({index:0})}),r(yt,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`+${o.length-1}`,onMouseEnter:re=>ae(re,I),onMouseLeave:ne}),r(ra,{id:H,open:F,anchorEl:L,onClose:ne,anchorOrigin:{vertical:"bottom",horizontal:"center"},transformOrigin:{vertical:"top",horizontal:"center"},onMouseEnter:b,onMouseLeave:M,ref:z,sx:{"& .MuiPopover-paper":{backgroundColor:"#f5f5f5",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.1)",borderRadius:"8px",padding:"10px",fontSize:"0.875rem",color:"#4791db",border:"1px solid #ddd"}},children:r(ca,{sx:{maxHeight:"270px",overflowY:"auto",padding:"5px"},dangerouslySetInnerHTML:{__html:te}})})]}):o.map((re,oe)=>r(yt,{sx:{height:25,fontSize:"0.85rem",".MuiChip-label":{padding:"0 6px"}},label:`${re}`,...k({index:oe})}))},renderInput:o=>r(Be,{...o,variant:"outlined",placeholder:(v==null?void 0:v.length)===0?`Select ${n==null?void 0:n.fieldName}`:"",InputProps:{...o.InputProps,endAdornment:K?null:o.InputProps.endAdornment},sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}}})})]})})},Ia=t=>{var F,W,z,$,G,Z,ae,ne,b,M,_,H,O,ce,le;const{uniqueId:a,field:e,disabled:m,handleChange:R,module:u}=t,n=Ee(),x=Ge(),{updateChangeLogGl:l}=ht(),c=new URLSearchParams(x.search).get("RequestId"),E=h(K=>K.payload.payloadData),T=mt[u]||(()=>({})),q=Nt[u]||(()=>({})),w=(F=T==null?void 0:T[a])==null?void 0:F[q];(z=(W=T==null?void 0:T[a])==null?void 0:W.Torequestheaderdata)==null||z.RequestStatus;const Y=h(K=>K.profitCenter.payload),D=h(K=>K.costCenter.payload),B=h(K=>K.generalLedger.payload||{}),S=h(K=>K.internalOrder.IOpayloadData||{}),v=u===(($=N)==null?void 0:$.CC)?((Z=(G=D==null?void 0:D.rowsBodyData)==null?void 0:G[a])==null?void 0:Z[e==null?void 0:e.jsonName])??(e==null?void 0:e.value):u==="GeneralLedger"?((ne=(ae=B==null?void 0:B.rowsBodyData)==null?void 0:ae[a])==null?void 0:ne[e==null?void 0:e.jsonName])??((b=B==null?void 0:B.requestHeaderData)==null?void 0:b[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===N.IO?((H=(_=(M=S==null?void 0:S.rowsBodyData)==null?void 0:M[a])==null?void 0:_.payload)==null?void 0:H[e==null?void 0:e.jsonName])??((O=S==null?void 0:S.requestHeaderData)==null?void 0:O[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":((le=(ce=Y==null?void 0:Y.rowsBodyData)==null?void 0:ce[a])==null?void 0:le[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??!1,y=v==="X"||v===!0||v==="TRUE",[L,A]=V.useState(y),te=["displayGeneralLedgerMasterdata","displayProfitCenter","displayCostCenter","DisplayMaterialSAPView","DisplayBankKeySAPView"].some(K=>{var ee;return(ee=x==null?void 0:x.pathname)==null?void 0:ee.includes(K)});Dt(),V.useEffect(()=>{A(y),y&&n(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:y,viewID:e==null?void 0:e.viewName}))},[y]);const p=K=>{var de,o,k,I,J,ie,re,oe,ye;const ee=K.target.checked;A(ee),u==="CostCenter"?(n(xe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:ee,viewID:e==null?void 0:e.viewName})),l({objectNumber:w,uniqueId:a||"",viewName:(de=t==null?void 0:t.field)==null?void 0:de.viewName,plantData:"",fieldName:(o=t==null?void 0:t.field)==null?void 0:o.fieldName,jsonName:(k=t==null?void 0:t.field)==null?void 0:k.jsonName,currentValue:ee,requestId:E==null?void 0:E.RequestId,childRequestId:c})):u==="GeneralLedger"?(n(Ie({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:ee,viewID:e==null?void 0:e.viewName})),l({objectNumber:w,uniqueId:a||"",viewName:(I=t==null?void 0:t.field)==null?void 0:I.viewName,plantData:"",fieldName:(J=t==null?void 0:t.field)==null?void 0:J.fieldName,jsonName:(ie=t==null?void 0:t.field)==null?void 0:ie.jsonName,currentValue:ee,requestId:E==null?void 0:E.RequestId,childRequestId:c})):u===N.IO?n(Oe({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:ee,viewID:e==null?void 0:e.viewName})):(n(ge({uniqueId:a||"",keyName:(e==null?void 0:e.jsonName)||"",data:ee,viewID:e==null?void 0:e.viewName})),l({objectNumber:w,uniqueId:a||"",viewName:(re=t==null?void 0:t.field)==null?void 0:re.viewName,plantData:"",fieldName:(oe=t==null?void 0:t.field)==null?void 0:oe.fieldName,jsonName:(ye=t==null?void 0:t.field)==null?void 0:ye.jsonName,currentValue:ee,requestId:E==null?void 0:E.RequestId,childRequestId:c}))};return r(Re,{item:!0,md:2,children:te?Q("div",{style:{padding:"16px",backgroundColor:d.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[Q(ue,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:e.fieldName,children:[e.fieldName,e.visibility==="Required"||e.visibility==="0"||(e==null?void 0:e.visibility)===Le.MANDATORY?r("span",{style:{color:d.error.dark},children:"*"}):""]}),r("div",{style:{fontSize:"0.8rem",color:d.black.dark,marginTop:"4px"},children:r("span",{style:{fontWeight:500,color:d.secondary.grey,letterSpacing:"0.5px",wordSpacing:"1px"},children:L?"Yes":"No"})})]}):Q(je,{children:[Q(ue,{variant:"body2",color:d.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:e.fieldName,children:[e.fieldName,e.visibility==="Required"||e.visibility==="0"||(e==null?void 0:e.visibility)===Le.MANDATORY?r("span",{style:{color:d.error.dark,marginLeft:10},children:"*"}):""]}),r(_t,{sx:{padding:0,marginTop:"5px","&.Mui-disabled":{color:d.hover.light},"&.Mui-disabled.Mui-checked":{color:d.hover.light}},disabled:m,checked:L,onChange:p})]})})},ja=t=>{var $,G,Z,ae,ne,b,M,_,H,O,ce,le,K,ee,de,o,k;const{uniqueId:a,field:e,disabled:m,handleChange:R,module:u}=t,n=Ee(),x=Ge(),j=new URLSearchParams(x.search).get("RequestId"),c=h(I=>I.payload.payloadData),[E,T]=V.useState(null),[q,w]=V.useState(null);V.useState(!1);const{updateChangeLogGl:Y}=ht(),D=mt[u]||(()=>({})),B=Nt[u]||(()=>({})),S=($=D==null?void 0:D[a])==null?void 0:$[B];let v=(Z=(G=D==null?void 0:D[a])==null?void 0:G.Torequestheaderdata)==null?void 0:Z.RequestStatus;const y=h(I=>I.profitCenter.payload||{}),L=h(I=>I.generalLedger.payload||{}),A=h(I=>I.costCenter.payload||{}),te=h(I=>I.internalOrder.IOpayloadData||{}),p=u===((ae=N)==null?void 0:ae.CC)?((b=(ne=A==null?void 0:A.rowsBodyData)==null?void 0:ne[a])==null?void 0:b[e==null?void 0:e.jsonName])??((M=A==null?void 0:A.requestHeaderData)==null?void 0:M[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u==="GeneralLedger"?((H=(_=L==null?void 0:L.rowsBodyData)==null?void 0:_[a])==null?void 0:H[e==null?void 0:e.jsonName])??((O=L==null?void 0:L.requestHeaderData)==null?void 0:O[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":u===N.IO?((K=(le=(ce=te==null?void 0:te.rowsBodyData)==null?void 0:ce[a])==null?void 0:le.payload)==null?void 0:K[e==null?void 0:e.jsonName])??((ee=te==null?void 0:te.requestHeaderData)==null?void 0:ee[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"":((o=(de=y==null?void 0:y.rowsBodyData)==null?void 0:de[a])==null?void 0:o[e==null?void 0:e.jsonName])??((k=y==null?void 0:y.requestHeaderData)==null?void 0:k[e==null?void 0:e.jsonName])??(e==null?void 0:e.value)??"";h(I=>{var J;return((J=I.payload)==null?void 0:J.errorFields)||[]});const F=["displayGeneralLedgerMasterdata","DisplayMaterialSAPView","displayCostCenter","displayProfitCenter","displayBankKeySAPView"].some(I=>{var J;return(J=x==null?void 0:x.pathname)==null?void 0:J.includes(I)});V.useEffect(()=>{if(p){const J=Te(p,"DD/MM/YYYY",!0).isValid()?Te(p,"DD/MM/YYYY"):Te(p);T(J)}else T(null)},[p,e==null?void 0:e.jsonName,a]),V.useEffect(()=>{if(F&&(e!=null&&e.value)){const I=new Date(e.value);if(isNaN(I.getTime()))w(null);else{const J=String(I.getDate()).padStart(2,"0"),ie=String(I.getMonth()+1).padStart(2,"0"),re=I.getFullYear(),oe=`${J}/${ie}/${re}`;w(oe)}}else w(null)},[F,e==null?void 0:e.jsonName,e==null?void 0:e.value]);const W=I=>{var J,ie,re,oe,ye,qe;if(e!=null&&e.jsonName){const me=I?Te(I).format("YYYY-MM-DD"):null;u=="CostCenter"?(n(xe({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:me,viewID:e==null?void 0:e.viewName})),j&&!Ce.includes(v)&&Y({objectNumber:S,uniqueId:a||"",viewName:(J=t==null?void 0:t.field)==null?void 0:J.viewName,plantData:"",fieldName:(ie=t==null?void 0:t.field)==null?void 0:ie.fieldName,jsonName:(re=t==null?void 0:t.field)==null?void 0:re.jsonName,currentValue:me,requestId:c==null?void 0:c.RequestId,childRequestId:j})):u=="GeneralLedger"?(n(Ie({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:me,viewID:e==null?void 0:e.viewName})),j&&!Ce.includes(v)&&Y({objectNumber:S,uniqueId:a||"",viewName:(oe=t==null?void 0:t.field)==null?void 0:oe.viewName,plantData:"",fieldName:(ye=t==null?void 0:t.field)==null?void 0:ye.fieldName,jsonName:(qe=t==null?void 0:t.field)==null?void 0:qe.jsonName,currentValue:me,requestId:c==null?void 0:c.RequestId,childRequestId:j})):u===N.IO?n(Oe({uniqueId:a||"",keyName:e==null?void 0:e.jsonName,data:me,viewID:e==null?void 0:e.viewName})):n(ge({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:me,viewID:e==null?void 0:e.viewName})),T(I)}},z=()=>{T(null),e!=null&&e.jsonName&&(u=="CostCenter"?n(xe({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:null,viewID:e==null?void 0:e.viewName})):u=="GeneralLedger"?n(Ie({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:null,viewID:e==null?void 0:e.viewName})):u===N.IO?n(Oe({uniqueId:a||"",keyName:e==null?void 0:e.jsonName,data:null,viewID:e==null?void 0:e.viewName})):n(ge({uniqueId:a,keyName:e==null?void 0:e.jsonName,data:null,viewID:e==null?void 0:e.viewName})))};return r(je,{children:r(Re,{item:!0,md:2,children:r(Ae,{children:F?Q("div",{style:{padding:"16px",backgroundColor:d.primary.white,borderRadius:"8px",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)",margin:"16px 0",transition:"all 0.3s ease"},children:[Q(ue,{variant:"body1",style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontWeight:600,fontSize:"12px",marginBottom:"4px",display:"flex",alignItems:"center"},title:e==null?void 0:e.fieldName,children:[(e==null?void 0:e.fieldName)||"Field Name",((e==null?void 0:e.visibility)==="Required"||(e==null?void 0:e.visibility)===Le.MANDATORY)&&r("span",{style:{color:d.error.dark},children:"*"})]}),r("div",{style:{fontSize:"0.8rem",color:d.black.dark,marginTop:"4px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",cursor:"pointer"},children:q?r(Ct,{title:q||"--",arrow:!0,children:r("span",{children:r("strong",{style:{fontWeight:600,color:d.secondary.grey,marginRight:"6px",letterSpacing:"0.5px",wordSpacing:"1px"},children:q})})}):r(xt,{fallback:"--"})})]}):Q(je,{children:[Q(ue,{variant:"body2",color:d.secondary.grey,sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:e==null?void 0:e.fieldName,children:[e==null?void 0:e.fieldName,((e==null?void 0:e.visibility)==="Required"||(e==null?void 0:e.visibility)==="0")&&r("span",{style:{color:"red"},children:"*"})]}),Q(Ae,{direction:"row",spacing:1,alignItems:"center",children:[r(ia,{dateAdapter:ma,sx:{flex:1},children:r(Na,{value:E,onChange:W,disabled:m,minDate:Te("1900-01-01"),maxDate:Te("9999-12-31"),slotProps:{textField:{size:"small",placeholder:m?"":"Select date",fullWidth:!0,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light},width:"100%"}}}})}),E&&!m&&r(oa,{size:"small",onClick:z,sx:{color:d.secondary.grey,padding:"4px",flexShrink:0},children:r(da,{fontSize:"small"})})]})]})})})})},Ma=t=>{var p,F,W,z,$,G,Z,ae,ne;const a=Ee(),e=Ge(),m=h(b=>b.payload),R=h(b=>b.profitCenter.payload.requestHeaderData),u=h(b=>b.generalLedger.payload.requestHeaderData),n=h(b=>b.costCenter.payload.requestHeaderData),x=h(b=>b.hierarchyData.requestHeaderData),l=h(b=>b.internalOrder.IOpayloadData.requestHeaderData),j=h(b=>b.bankKey.payload.requestHeaderData);let c=h(b=>b.userManagement.taskData);const T=new URLSearchParams(e.search).get("RequestId"),{field:q,disabled:w,dropDownData:Y,uniqueId:D,viewName:B,plantData:S,module:v}=t,y=h(b=>b.userManagement.userData),L=h(b=>b.bom.BOMpayloadData),A=b=>{e.pathname.includes("material")&&a(S?ua({key:q.fieldName,value:b,viewName:B,plantData:S}):la({key:q.fieldName,value:b}))};if(V.useEffect(()=>{var b,M,_;if(!(c!=null&&c.requestId)&&(((b=t==null?void 0:t.field)==null?void 0:b.fieldName)==="Created On"||((M=t==null?void 0:t.field)==null?void 0:M.fieldName)==="Updated On")){const H=new Date;v===N.CC?a(xe({keyName:t.field.jsonName,data:H})):v===N.GL?a(Ie({keyName:t.field.jsonName,data:H})):v===N.BOM?a(ct({keyName:t.field.jsonName,data:H})):v===N.IO?a(Oe({keyName:t.field.jsonName,data:H})):v===((_=se)==null?void 0:_.BK)?a(it({keyName:t.field.jsonName,data:H})):a(v==="PCG"||v==="CCG"||v==="CEG"?ot({keyName:t.field.jsonName,data:H}):ge({keyName:t.field.jsonName,data:H}))}},[]),((p=t==null?void 0:t.field)==null?void 0:p.fieldName)==="Created By")return r(Re,{item:!0,md:2,children:Q(Ae,{children:[r(ue,{variant:"body2",color:"#777",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:t.field.fieldName,children:t.field.fieldName}),r(Be,{value:T?((F=m==null?void 0:m.payloadData)==null?void 0:F.ReqCreatedBy)||(L==null?void 0:L.ReqCreatedBy)||(R==null?void 0:R.ReqCreatedBy)||(n==null?void 0:n.ReqCreatedBy)||(u==null?void 0:u.ReqCreatedBy)||(x==null?void 0:x.ReqCreatedBy)||(j==null?void 0:j.ReqCreatedBy)||(l==null?void 0:l.ReqCreatedBy):y==null?void 0:y.emailId,title:T?((W=m==null?void 0:m.payloadData)==null?void 0:W.ReqCreatedBy)||(L==null?void 0:L.ReqCreatedBy)||(R==null?void 0:R.ReqCreatedBy)||(n==null?void 0:n.ReqCreatedBy)||(u==null?void 0:u.ReqCreatedBy)||(x==null?void 0:x.ReqCreatedBy)||(l==null?void 0:l.ReqCreatedBy)||(j==null?void 0:j.ReqCreatedBy):y==null?void 0:y.emailId,size:"small",disabled:!!(y!=null&&y.emailId),sx:{cursor:"not-allowed","& .MuiInputBase-root":{height:"34px"},"& .MuiInputBase-input":{cursor:"not-allowed"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light}}})]})});if(((z=t==null?void 0:t.field)==null?void 0:z.fieldName)==="Created On"){const b=new Date,M=String(b.getDate()).padStart(2,"0"),_=String(b.getMonth()+1).padStart(2,"0"),H=b.getFullYear(),O=`${M}-${_}-${H}`;return r(Re,{item:!0,md:2,children:Q(Ae,{children:[r(ue,{variant:"body2",color:"#777",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:($=t.field)==null?void 0:$.fieldName,children:(G=t==null?void 0:t.field)==null?void 0:G.fieldName}),r(Be,{size:"small",value:O,disabled:!0,sx:{cursor:"not-allowed","& .MuiInputBase-root":{height:"34px"},"& .MuiInputBase-input":{cursor:"not-allowed"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light}}})]})})}else if(((Z=t==null?void 0:t.field)==null?void 0:Z.fieldName)==="Updated On"){const b=new Date,M=String(b.getDate()).padStart(2,"0"),_=String(b.getMonth()+1).padStart(2,"0"),H=b.getFullYear(),O=`${M}-${_}-${H}`;return r(Re,{item:!0,md:2,children:Q(Ae,{children:[r(ue,{variant:"body2",color:"#777",sx:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"100%"},title:(ae=t.field)==null?void 0:ae.fieldName,children:(ne=t==null?void 0:t.field)==null?void 0:ne.fieldName}),r(Be,{size:"small",value:O,disabled:!0,sx:{cursor:"not-allowed","& .MuiInputBase-root":{height:"34px"},"& .MuiInputBase-input":{cursor:"not-allowed"},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:d.black.dark,color:d.black.dark},backgroundColor:d.hover.light}}})]})})}return r(je,{children:(()=>{switch(q.fieldType){case"Input":return r(bt,{uniqueId:D,field:q,disabled:w,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Auto":return r(bt,{uniqueId:D,field:q,disabled:w,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Disable Input":return r(bt,{uniqueId:D,field:q,disabled:!0,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Drop Down":return r(Da,{uniqueId:D,field:q,disabled:w,dropDownData:Y,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Multi Select":return r(xa,{uniqueId:D,field:q,disabled:w,dropDownData:Y,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Radio Button":return r(Ia,{uniqueId:D,field:q,disabled:w,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});case"Calendar":return r(ja,{uniqueId:D,field:q,disabled:w,handleChange:A,selectedRow:t==null?void 0:t.selectedRow,module:t==null?void 0:t.module,isError:t==null?void 0:t.isError});default:return null}})()})};export{Ma as F,ba as g,Sa as s,ht as u};
