import{t as b,r as u,c as x,Z as n,j as a,V as f,d as h,A as v,O as l,b1 as w,B as s,i as y,k as F}from"./index-226a1e75.js";import{F as N}from"./FilterField-868050e3.js";const C=e=>{var r;const[m,c]=b.useState({0:(e==null?void 0:e.isMandatoryFailed)||!1});u.useEffect(()=>{c(i=>({...i,0:!!(e!=null&&e.isMandatoryFailed)}))},[e==null?void 0:e.isMandatoryFailed]);const o=i=>(d,t)=>{c(g=>({...g,[i]:t}))};return x(F,{sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:e.isMandatoryFailed?n.error.dark:n.hover.hoverbg},onChange:o(0),expanded:m[0],children:[a(v,{expandIcon:a(f,{}),sx:{backgroundColor:n.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:n.hover.hoverbg}},children:a(h,{variant:"h6",sx:{fontWeight:"bold"},children:e==null?void 0:e.viewName})}),a(y,{children:(r=e==null?void 0:e.GeneralFields)==null?void 0:r.map(i=>x(l,{item:!0,md:12,sx:{backgroundColor:n.white,maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${n.grey}`,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...w},children:[a(l,{container:!0,children:a(h,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:i[0]})}),a(s,{children:a(l,{container:!0,spacing:1,children:[...i[1]].filter(d=>d.visibility!=="Hidden").sort((d,t)=>d.sequenceNo-t.sequenceNo).map(d=>a(N,{disabled:e==null?void 0:e.disabled,field:d,dropDownData:e.dropDownData,materialID:e==null?void 0:e.materialID,selectedMaterialNumber:e==null?void 0:e.selectedMaterialNumber,viewName:e==null?void 0:e.viewName,plantData:e.viewName,missingFields:e.missingFields},d.fieldName))})})]},i[0]))})]},1)};export{C as G};
