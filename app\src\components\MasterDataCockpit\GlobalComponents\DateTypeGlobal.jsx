import { colors } from "@constant/colors";
import { <PERSON><PERSON>, I<PERSON>B<PERSON><PERSON>, Stack, TextField, Typography } from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import ClearIcon from "@mui/icons-material/Clear";
import { updateModuleFieldData } from "@app/profitCenterTabsSlice";
import { useSelector } from "react-redux";
import dayjs from "dayjs";
import { updateModuleFieldDataGL } from "@app/generalLedgerTabSlice";
import { updateModuleFieldDataCC } from "@app/costCenterTabsSlice";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { CHANGE_LOG_STATUSES, MODULE, MODULE_PAYLOAD_DATA, OBJECT_MUMBER_KEY, VISIBILITY_TYPE } from "@constant/enum";
import { updateModuleFieldDataIO } from "@InternalOrder/slice/internalOrderSlice";
import SkeletonWithFallback from "@components/Common/ui/SkeletonWithFallback";
import {Tooltip} from "@mui/material";

const DateTypeGlobal = (props) => {
  const { uniqueId, field, disabled, handleChange, module } = props;
  
  

  const dispatch = useDispatch();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);

  const [value, setValue] = useState(null);
  const [sapValue,setsapValue]=useState(null)
  const [showDateError, setShowDateError] = useState(false);

  const { updateChangeLogGl } = useChangeLogUpdateGl();
   const rowsBodyData = MODULE_PAYLOAD_DATA[module] || (() => ({}));
   const objectKey = OBJECT_MUMBER_KEY[module] || (() => ({}));
    const rowsObjectNumber = rowsBodyData?.[uniqueId]?.[objectKey]

  let requestStatus = rowsBodyData?.[uniqueId]?.["Torequestheaderdata"]?.["RequestStatus"];
  const valueFromPayloadPC = useSelector(
    (state) => state.profitCenter.payload || {}
  );
  const valueFromPayloadGL = useSelector(
    (state) => state.generalLedger.payload || {}
  );
  const valueFromPayloadCC = useSelector(
    (state) => state.costCenter.payload || {}
  );
  const valueFromPayloadIO = useSelector(
    (state) => state.internalOrder.IOpayloadData || {}
  );

  const initialFieldValue =
    module === MODULE?.CC
      ? valueFromPayloadCC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
        valueFromPayloadCC?.requestHeaderData?.[field?.jsonName] ??
        field?.value ??
        ""
      : module === "GeneralLedger"
      ? valueFromPayloadGL?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
        valueFromPayloadGL?.requestHeaderData?.[field?.jsonName] ??
        field?.value ??
        ""
      : module === MODULE.IO
      ? valueFromPayloadIO?.rowsBodyData?.[uniqueId]?.payload?.[
          field?.jsonName
        ] ??
        valueFromPayloadIO?.requestHeaderData?.[field?.jsonName] ??
        field?.value ??
        ""
      : valueFromPayloadPC?.rowsBodyData?.[uniqueId]?.[field?.jsonName] ??
        valueFromPayloadPC?.requestHeaderData?.[field?.jsonName] ??
        field?.value ??
        "";

        
  const errorFields = useSelector((state) => state.payload?.errorFields || []);
  const SAPview = [
    "displayGeneralLedgerMasterdata",
    "DisplayMaterialSAPView",
    "displayCostCenter",
    "displayProfitCenter",
    "displayBankKeySAPView",
  ].some((path) => location?.pathname?.includes(path));

  

  useEffect(() => {
    if (initialFieldValue) {
      const isValid = dayjs(initialFieldValue, "DD/MM/YYYY", true).isValid();
      const parsed = isValid
        ? dayjs(initialFieldValue, "DD/MM/YYYY")
        : dayjs(initialFieldValue); // fallback to direct parsing

      setValue(parsed);
    } else {
      setValue(null);
    }
  }, [initialFieldValue, field?.jsonName, uniqueId]);




  useEffect(() => {
  if (SAPview && field?.value) {
    const date = new Date(field.value); // already a Date, just reformat

    if (!isNaN(date.getTime())) {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      const convertedDate = `${day}/${month}/${year}`;
      setsapValue(convertedDate);
    } else {
      setsapValue(null); // invalid date
    }
  } else {
    setsapValue(null);
  }
}, [SAPview, field?.jsonName, field?.value]);



  const handleDateChange = (newValue) => {
    if (field?.jsonName) {
      const formattedDate = newValue
        ? dayjs(newValue).format("YYYY-MM-DD")
        : null;

      if (module == "CostCenter") {
        dispatch(
          updateModuleFieldDataCC({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );

        if (requestId && !CHANGE_LOG_STATUSES.includes(requestStatus)) {
          updateChangeLogGl({
            objectNumber: rowsObjectNumber,
            uniqueId: uniqueId || "",
            viewName: props?.field?.viewName,
            plantData: "",
            fieldName: props?.field?.fieldName,
            jsonName: props?.field?.jsonName,
            currentValue: formattedDate,
            requestId: initialPayload?.RequestId,
            childRequestId: requestId,
          });
        }
      } else if (module == "GeneralLedger") {
        dispatch(
          updateModuleFieldDataGL({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );

        {
          requestId &&
            !CHANGE_LOG_STATUSES.includes(requestStatus) &&
            updateChangeLogGl({
              objectNumber: rowsObjectNumber,
              uniqueId: uniqueId || "",
              viewName: props?.field?.viewName,
              plantData: "",
              fieldName: props?.field?.fieldName,
              jsonName: props?.field?.jsonName,
              currentValue: formattedDate,
              requestId: initialPayload?.RequestId,
              childRequestId: requestId,
            });
        }
      } else if (module === MODULE.IO) {
        dispatch(
          updateModuleFieldDataIO({
            uniqueId: uniqueId || "",
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );
      } else {
        dispatch(
          updateModuleFieldData({
            uniqueId,
            keyName: field?.jsonName,
            data: formattedDate,
            viewID: field?.viewName,
          })
        );
      }

      setValue(newValue);
    }
  };

  const handleClearDate = () => {
    setValue(null);
    if (field?.jsonName) {
      if (module == "CostCenter") {
        dispatch(
          updateModuleFieldDataCC({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      } else if (module == "GeneralLedger") {
        dispatch(
          updateModuleFieldDataGL({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      } else if (module === MODULE.IO) {
        dispatch(
          updateModuleFieldDataIO({
            uniqueId: uniqueId || "",
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      } else {
        dispatch(
          updateModuleFieldData({
            uniqueId,
            keyName: field?.jsonName,
            data: null,
            viewID: field?.viewName,
          })
        );
      }
    }
  };

  return (
    <>
      <Grid item md={2}>
        <Stack>
          {SAPview ? (
            <div
              style={{
                padding: "16px",
                backgroundColor: colors.primary.white,
                borderRadius: "8px",
                boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                margin: "16px 0",
                transition: "all 0.3s ease",
              }}
            >
              <Typography
                variant="body1"
                style={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                  fontWeight: 600,
                  fontSize: "12px",
                  marginBottom: "4px",
                  display: "flex",
                  alignItems: "center",
                }}
                title={field?.fieldName}
              >
                {field?.fieldName || "Field Name"}
                {(field?.visibility === "Required" ||
                  field?.visibility === VISIBILITY_TYPE.MANDATORY) && (
                  <span style={{ color: colors.error.dark }}>*</span>
                )}
              </Typography>

              <div
                style={{
                  fontSize: "0.8rem",
                  color: colors.black.dark,
                  marginTop: "4px",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  width: "100%",
                  cursor: "pointer",
                }}
              >
                {sapValue ? (
                  <Tooltip
                    title={
                      sapValue
                        ? sapValue  
                        : "--"
                    }
                    arrow
                  >
                    <span>
                      <strong
                        style={{
                          fontWeight: 600,
                          color: colors.secondary.grey,
                          marginRight: "6px",
                          letterSpacing: "0.5px",
                          wordSpacing: "1px",
                        }}
                      >
                        {sapValue}
                      </strong>
                      
                    </span>
                  </Tooltip>
                ) : (
                  <SkeletonWithFallback fallback="--" />
                )}
              </div>
            </div>
          ) : (
            <>
              <Typography
                variant="body2"
                color={colors.secondary.grey}
                sx={{
                  whiteSpace: "nowrap",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  maxWidth: "100%",
                }}
                title={field?.fieldName}
              >
                {field?.fieldName}
                {(field?.visibility === "Required" ||
                  field?.visibility === "0") && (
                  <span style={{ color: "red" }}>*</span>
                )}
              </Typography>

              <Stack direction="row" spacing={1} alignItems="center">
                <LocalizationProvider
                  dateAdapter={AdapterDayjs}
                  sx={{ flex: 1 }}
                >
                  
                  <DatePicker
                    value={value}
                    onChange={handleDateChange}
                    disabled={disabled}
                    minDate={dayjs("1900-01-01")}
                    maxDate={dayjs("9999-12-31")}
                    slotProps={{
                      textField: {
                        size: "small",
                        placeholder: disabled ? "" : "Select date",
                        fullWidth: true,
                        sx: {
                          "& .MuiInputBase-root.Mui-disabled": {
                            "& > input": {
                              WebkitTextFillColor: colors.black.dark,
                              color: colors.black.dark,
                            },
                            backgroundColor: colors.hover.light,
                          },
                          width: "100%",
                        },
                      },
                    }}
                  />
                </LocalizationProvider>

                {value && !disabled && (
                  <IconButton
                    size="small"
                    onClick={handleClearDate}
                    sx={{
                      color: colors.secondary.grey,
                      padding: "4px",
                      flexShrink: 0,
                    }}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                )}
              </Stack>
            </>
          )}
        </Stack>
      </Grid>
    </>
  );
};

export default DateTypeGlobal;
