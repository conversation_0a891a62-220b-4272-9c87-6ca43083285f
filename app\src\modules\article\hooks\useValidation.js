import { MATERIAL_VIEWS, REGION_CODE } from "@constant/enum";
import { checkAllKeys, checkObjectAndCount, checkOrgsKeys, convertForBasicDataMandatoryFields, filterMrpCombination, filterNullValues, generateUniqueCombinations, getMissingElements, getMissingFieldValues, getMissingValues, getMissingValuesForBasic, getPlantCodes } from "@helper/helper";
import { useSelector } from "react-redux";

const useValidation = (singlePayloadData, allMaterialFieldConfigDT, selectedViews, setSelectedMaterialID, currentRowData) => {
  let taskData = useSelector((state) => state.userManagement.taskData);
  const checkValidation = (materialId, orgRow, isInternalRangeAllowed, isExternalRangeAllowed, isExtWOCheckAllowed) => {
    //setSelectedMaterialID(materialId);
    const payloadData = singlePayloadData?.[materialId]?.payloadData;
    const headerData = singlePayloadData?.[materialId]?.headerData;
    const ManufacturerID = singlePayloadData?.[materialId]?.ManufacturerID;
    const region = singlePayloadData?.payloadData?.Region;
    const allow_val_check_number =taskData?.taskDesc !== "Generic Article Activation Task" || !taskData || Object.keys(taskData).length === 0;

    if (!headerData?.materialNumber && !allow_val_check_number) {
      return { missingFields: ["Article number"], isValid: false };
    }
    if (!headerData?.materialType) {
      return { missingFields: ["Article Type"], isValid: false };
    }
    if (!headerData?.articleCategory) {
      return { missingFields: ["Article Category"], isValid: false };
    }
    if (!headerData?.apparelMcat?.code) {
      return { missingFields: ["Merchandising Category"], isValid: false };
    }

    if (!headerData?.globalMaterialDescription || !payloadData) {
      return { missingFields: ["Article Description"], isValid: false };
    }
    if (((headerData.articleCategory.code == "11" || headerData.articleCategory == "11") && !(Object.keys(headerData?.articleComponents ?? {}).length > 0)) || !payloadData) {
      return { missingFields: ["Components"], isValid: false };
    }

    // if(!ManufacturerID && singlePayloadData?.payloadData?.Region===REGION_CODE?.EUR){
    //   return { missingFields: ["Manufacturer ID(EAN)"], isValid: false };
    // }

    // const eanData = singlePayloadData?.[materialId]?.eanData;
    // if(eanData?.length > 1&&(singlePayloadData?.payloadData?.Region === REGION_CODE?.EUR)) {
    //   const hasInvalidEan = eanData.slice(1).some(row => row.eanUpc && !row.eanUpc.includes(ManufacturerID));
    //   if(hasInvalidEan) {
    //     return { missingFields: ["EAN/UPC must contain Manufacturer ID"], isValid: false };
    //   }
    // }

    const onlyBasicDataFields = convertForBasicDataMandatoryFields(payloadData[MATERIAL_VIEWS.BASIC_DATA]);
    const supplierFormFields = convertForBasicDataMandatoryFields(payloadData[MATERIAL_VIEWS.SUPPLIER_FORM]);
    onlyBasicDataFields["Material"] = headerData?.materialNumber;
    onlyBasicDataFields["MatlDesc"] = headerData?.globalMaterialDescription;

    const gotMatConfigFieldFromRedux = allMaterialFieldConfigDT?.find((materialTypes) => materialTypes?.[region] && materialTypes?.[region][headerData?.materialType?.code]);
    const allViewsMandatoryFields = gotMatConfigFieldFromRedux && gotMatConfigFieldFromRedux[region] && gotMatConfigFieldFromRedux[region][headerData?.materialType?.code]?.mandatoryFields;

    // Check Mandatory Fields for Basic Data View
    const mandatoryFieldsOfBasicData = allViewsMandatoryFields?.[MATERIAL_VIEWS.BASIC_DATA];
    if (mandatoryFieldsOfBasicData?.length > 0) {
      for (const field of mandatoryFieldsOfBasicData) {
        if (!onlyBasicDataFields[field?.jsonName]) {
          const missingFields = getMissingValuesForBasic(mandatoryFieldsOfBasicData, onlyBasicDataFields);
          return { missingFields, viewType: MATERIAL_VIEWS.BASIC_DATA, isValid: false, plant: [MATERIAL_VIEWS.BASIC_DATA] };
        }
      }
    }
    // Check Mandatory Fields for Supplier form View
    const mandatoryFieldsOfSupplierForm = allViewsMandatoryFields?.[MATERIAL_VIEWS.SUPPLIER_FORM];
    if (selectedViews.includes(MATERIAL_VIEWS.SUPPLIER_FORM) && mandatoryFieldsOfSupplierForm?.length > 0) {
      for (const field of mandatoryFieldsOfSupplierForm) {
        if (!supplierFormFields[field?.jsonName]) {
          const missingFields = getMissingValuesForBasic(mandatoryFieldsOfSupplierForm, supplierFormFields);
          return { missingFields, viewType: MATERIAL_VIEWS.SUPPLIER_FORM, isValid: false, plant: [MATERIAL_VIEWS.SUPPLIER_FORM] };
        }
      }
    }

    // Mandatory field check for all org data dependent views
    for (const view of selectedViews) {
      const uniqueCombinations = generateUniqueCombinations(orgRow);
      const { displayCombinations } = uniqueCombinations?.[view] || {};
      if (displayCombinations && displayCombinations[0] && displayCombinations?.length > 0) {
        const mandatoryFields = allViewsMandatoryFields?.[view];
        if (mandatoryFields) {
          let missingFields = {};
          for (const combination of displayCombinations) {
            const combinationData = payloadData[view]?.[combination];
            if (combinationData) {
              const missing = getMissingFieldValues(mandatoryFields, combinationData);
              if (Object.keys(missing)?.length > 0) {
                missingFields[combination] = Object.keys(missing);
              }
            } else {
              missingFields[combination] = mandatoryFields.map((field) => field.fieldName);
            }
            if (Object.keys(missingFields)?.length > 0) {
              return { missingFields, viewType: view, isValid: false, plant: Object.keys(missingFields) };
            }
          }
        }
      }
    }
    // Mandatory field check for all org data independent views(Sales General)
    if (selectedViews.includes(MATERIAL_VIEWS.SALES)) {
      const mandatoryFieldsOfSalesGeneral = allViewsMandatoryFields?.[MATERIAL_VIEWS.SALES_GENERAL];
      let missingFields = {};
      if (mandatoryFieldsOfSalesGeneral && payloadData[MATERIAL_VIEWS.SALES_GENERAL]) {
        const missing = getMissingFieldValues(mandatoryFieldsOfSalesGeneral, payloadData[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL]);
        if (Object.keys(missing).length > 0) {
          missingFields[MATERIAL_VIEWS.SALES_GENERAL] = Object.keys(missing);
        }
      } else if (mandatoryFieldsOfSalesGeneral) {
        missingFields[MATERIAL_VIEWS.SALES_GENERAL] = mandatoryFieldsOfSalesGeneral.map((field) => field.fieldName);
      }
      if (Object.keys(missingFields)?.length > 0) {
        return { missingFields, viewType: MATERIAL_VIEWS.SALES, isValid: false, plant: [MATERIAL_VIEWS.SALES_GENERAL] };
      }
    }

    // Mandatory field check for all org data independent views(Supplier Form)
    if (selectedViews.includes(MATERIAL_VIEWS.SUPPLIER_FORM)) {
      const mandatoryFieldsOfSupplierForm = allViewsMandatoryFields?.[MATERIAL_VIEWS.SUPPLIER_FORM];
      let missingFields = {};
      if (mandatoryFieldsOfSupplierForm && payloadData[MATERIAL_VIEWS.SUPPLIER_FORM]) {
        const missing = getMissingFieldValues(mandatoryFieldsOfSupplierForm, payloadData[MATERIAL_VIEWS.SUPPLIER_FORM]?.basic);
        if (Object.keys(missing).length > 0) {
          missingFields[MATERIAL_VIEWS.SUPPLIER_FORM] = Object.keys(missing);
        }
      } else if (mandatoryFieldsOfSupplierForm) {
        missingFields[MATERIAL_VIEWS.SUPPLIER_FORM] = mandatoryFieldsOfSupplierForm.map((field) => field.fieldName);
      }
      if (Object.keys(missingFields)?.length > 0) {
        return { missingFields, viewType: MATERIAL_VIEWS.SUPPLIER_FORM, isValid: false, plant: [MATERIAL_VIEWS.SUPPLIER_FORM] };
      }
    }
    // Mandatory field check for all org data independent views(Purchasing General)
    if (selectedViews.includes(MATERIAL_VIEWS.PURCHASING)) {
      const mandatoryFieldsOfPurchasingGeneral = allViewsMandatoryFields?.[MATERIAL_VIEWS.PURCHASING_GENERAL];
      let missingFields = {};
      if (mandatoryFieldsOfPurchasingGeneral && payloadData[MATERIAL_VIEWS.PURCHASING_GENERAL]) {
        const missing = getMissingFieldValues(mandatoryFieldsOfPurchasingGeneral, payloadData[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[MATERIAL_VIEWS.PURCHASING_GENERAL]);
        if (Object.keys(missing).length > 0) {
          missingFields[MATERIAL_VIEWS.PURCHASING_GENERAL] = Object.keys(missing);
        }
      } else if (mandatoryFieldsOfPurchasingGeneral) {
        missingFields[MATERIAL_VIEWS.PURCHASING_GENERAL] = mandatoryFieldsOfPurchasingGeneral.map((field) => field.fieldName);
      }
      if (Object.keys(missingFields)?.length > 0) {
        return { missingFields, viewType: MATERIAL_VIEWS.PURCHASING, isValid: false, plant: [MATERIAL_VIEWS.PURCHASING_GENERAL] };
      }
    }
    // Mandatory field check for all org data independent views(Storage General)
    if (selectedViews.includes(MATERIAL_VIEWS.STORAGE)) {
      const mandatoryFieldsOfStorageGeneral = allViewsMandatoryFields?.[MATERIAL_VIEWS.STORAGE_GENERAL];
      let missingFields = {};
      if (mandatoryFieldsOfStorageGeneral && payloadData[MATERIAL_VIEWS.STORAGE_GENERAL]) {
        const missing = getMissingFieldValues(mandatoryFieldsOfStorageGeneral, payloadData[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]);
        if (Object.keys(missing).length > 0) {
          missingFields[MATERIAL_VIEWS.STORAGE_GENERAL] = Object.keys(missing);
        }
      } else if (mandatoryFieldsOfStorageGeneral) {
        missingFields[MATERIAL_VIEWS.STORAGE_GENERAL] = mandatoryFieldsOfStorageGeneral.map((field) => field.fieldName);
      }
      if (Object.keys(missingFields)?.length > 0) {
        return { missingFields, viewType: MATERIAL_VIEWS.STORAGE, isValid: false, plant: [MATERIAL_VIEWS.STORAGE_GENERAL] };
      }
    }
    return { missingFields: null, isValid: true };
  };

  return { checkValidation };
};

export default useValidation;
