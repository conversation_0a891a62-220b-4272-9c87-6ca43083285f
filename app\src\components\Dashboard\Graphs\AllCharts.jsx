import React from "react";
import { Box, Grid, CircularProgress, Typography } from "@mui/material";
import GraphCards from "./GraphCards";

const AllCharts = ({ cards = [], loading, graphLoadingStates = {}, onRefreshGraph = () => {}, isTabbed = false, userPreferences = [] }) => {
  if (loading) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!cards.length) {
    return (
      <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
        <Typography variant="h6" color="text.secondary">
          No charts available. Please configure your dashboard.
        </Typography>
      </Box>
    );
  }

  // Sort cards by sequence
  const sortedCards = [...cards].sort((a, b) => {
    if (isTabbed) {
      // For tabbed view, use SecKpiSequence from user preferences
      const kpiIdA = a.kpiId || a.id || `KPI_${a.id}`;
      const kpiIdB = b.kpiId || b.id || `KPI_${b.id}`;

      const prefA = userPreferences.find(pref =>
        pref.KpiId === kpiIdA ||
        pref.KpiId === a.kpiId ||
        pref.KpiId === a.id ||
        pref.KpiId === `KPI_${a.id}`
      );
      const prefB = userPreferences.find(pref =>
        pref.KpiId === kpiIdB ||
        pref.KpiId === b.kpiId ||
        pref.KpiId === b.id ||
        pref.KpiId === `KPI_${b.id}`
      );

      const sequenceA = prefA?.SecKpiSequence ?? 999;
      const sequenceB = prefB?.SecKpiSequence ?? 999;

      return sequenceA - sequenceB;
    } else {
      // For non-tabbed view, use overall sequence
      const sequenceA = a.GraphSequence || a.graphDetails?.KpiSequence || a.Sequence || 0;
      const sequenceB = b.GraphSequence || b.graphDetails?.KpiSequence || b.Sequence || 0;

      return sequenceA - sequenceB;
    }
  });

  return (
    <Grid container spacing={2}>
      {sortedCards.map((card, index) => {
        const kpiId = card.kpiId || card.id;
        const isGraphLoading = graphLoadingStates[kpiId] || false;

        return (
          <Grid
            key={card.id}
            item
            xs={12}
            md={6}
            lg={4}
          >
            <GraphCards
              title={card.graphDetails?.graphName}
              data={card}
              isLoading={isGraphLoading}
              onRefresh={() => onRefreshGraph(kpiId)}
            />
          </Grid>
        );
      })}
    </Grid>
  );
};

export default AllCharts;
