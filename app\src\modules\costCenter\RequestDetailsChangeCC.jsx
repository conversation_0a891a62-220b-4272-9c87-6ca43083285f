import { useState, useEffect, forwardRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Dialog,
  DialogActions,
  DialogContent,
  Button,
  Slide,
  Typography,
  Box,
  Paper,
  Checkbox,
  TextField,
} from "@mui/material";
import { destination_CostCenter_Mass,destination_ProfitCenter_Mass } from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import { useLocation, useNavigate } from "react-router-dom";
import ReusableSnackBar from "../../components/Common/ReusableSnackBar";
import DownloadDialog from "../../components/Common/DownloadDialog";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import { API_CODE, ERROR_MESSAGES, FAILURE_DIALOG_MESSAGE, MODULE_MAP, SUCCESS_DIALOG_MESSAGE } from "@constant/enum";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { REQUEST_TYPE } from "@constant/enum";
import {
  transformCostCenterResponseChange,
  showToast,
  changePayloadForCC,
} from "../../functions";
import FilterChangeDropdown from "../../components/Common/ui/dropdown/FilterChangeDropdown";
import { APP_END_POINTS } from "@constant/appEndPoints";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import BottomNavGlobal from "./../../components/RequestBench/RequestPages/BottomNavGlobal";
import { updateReqBenchRowCc } from "@app/costCenterTabsSlice";
import ReusableDataTable from "../../components/Common/ReusableTable";
import {
  setChangedFieldsMapCc,
  setFetchedCostCenterDataCc,
  setFetchReqBenchDataCc,
  setOriginalCostCenterDataCc,
  setOriginalReqBenchDataCc,
  updateCostCenterRowCc,
} from "@app/costCenterTabsSlice";
import SingleSelectDropdown from "@components/Common/ui/dropdown/SingleSelectDropdown";
import ObjectLockDialog from "@components/Common/ObjectLockDialog";
import { getFieldsForTemplate } from "@helper/fieldHelper";
import { useSnackbar } from "@hooks/useSnackbar";
import SuccessDialog from "@components/Common/SubmitDialog";
import useDynamicWorkflowDTCC from "@costCenter/useDynamicWorkFlowDTCC";
import { useChangeLogUpdateGl } from "@hooks/useChangeLogUpdateGl";
import { setCreatePayloadCopyForChangeLog } from '@app/changeLogReducer';
const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});
const RequestDetailsChangeCC = ({
  reqBench,
  requestId,
  apiResponses,
  downloadClicked,
  setDownloadClicked,
  module,
  isDisabled,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    fetchedCostCenterData,
    fetchReqBenchDataCC,
    changedFieldsMap,
  } = useSelector((state) => state.costCenter);
  const requestHeaderData = useSelector(
    (state) => state.costCenter.payload.requestHeaderData
  );
  const initialPayload = useSelector((state) => state.request.requestHeader);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const task = useSelector((state) => state?.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestId = queryParams.get("RequestId");
  const isreqBench = queryParams.get("reqBench");
  const [open, setOpen] = useState(true);
  const [selectedControllingArea, setSelectedControllingArea] = useState("");
  const [selectedRow, setSelectedRow] = useState(null);
  const [dropdownDataCompany, setDropdownDataCompany] = useState([]);
  const [openDownloadDialog, setOpenDownloadDialog] = useState(false);
  const [selectedCompanyCodes, setSelectedCompanyCodes] = useState([]);
  const [costCenterOptions, setCostCenterOptions] = useState([]);
  const [selectedCostCenters, setSelectedCostCenters] = useState([]);
  const [successMsg, setSuccessMsg] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [messageDialogMessage, setMessageDialogMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState("");
  const [loaderMessage, setLoaderMessage] = useState("");
  const [downloadType, setDownloadType] = useState("systemGenerated");
  const [dropdownDataFunctionalArea, setDropdownDataFunctionalArea] = useState([]);
  const [dropdownDataProfitCenter, setDropdownDataProfitCenter] = useState([]);
  const [alertType, setAlertType] = useState("success");
  const [alertMsg, setAlertMsg] = useState("");
  const [duplicateFieldsData, setDuplicateFieldsData] = useState([]);
  const [showTableInDialog, setShowTableInDialog] = useState(false);
  const [dialogData, setDialogData] = useState({
    title: "",
    message: "",
    subText: "",
    buttonText: "",
    redirectTo: "",
  });
   const [successDialogOpen, setSuccessDialogOpen] = useState(false);

  const { getButtonsDisplayGlobal, showWfLevels } = useButtonDTConfig();
  const lockIndicatorMap = useSelector(
    (state) => state.payload.lockIndicatorData
  );
  const requestBenchData = location.state;
  const isButtonEnabled = requestBenchData?.reqStatus === "Validated-Requestor";
  const requestHeaderSlice = useSelector(
    (state) => state.request.requestHeader
  );
  const fetchedDisplayData = useSelector((state)=>state.costCenter.payload)
  const [showGrid, setShowGrid] = useState(false);
  const { showSnackbar } = useSnackbar();
  const changeFieldDataRaw = useSelector(
    (state) => state?.payload?.changeFieldSelectiondata
  );
  const changeFieldData = Array.isArray(changeFieldDataRaw)
    ? changeFieldDataRaw
    : [];
  const fieldNameListRaw = requestHeaderData?.FieldName;
  const reqBenchFieldName = apiResponses?.[0]?.Torequestheaderdata?.FieldName;
  const rawFieldName = reqBench ? reqBenchFieldName : fieldNameListRaw;
    const [wfLevels,setWfLevels] = useState([]);
    const [selectedLevel, setSelectedLevel] = useState('');
      const { updateChangeLogGlForChange } = useChangeLogUpdateGl();

      const changeChangeLogData = useSelector(
    (state) => state.changeLog.createChangeLogDataGL
  );
  const { getDynamicWorkflowDT } = useDynamicWorkflowDTCC();
 useEffect(() => {
        const fetchWorkflowLevels = async () => {
          try {
            const workflowLevelsDtData = await getDynamicWorkflowDT(
              requestHeaderData?.TemplateName,
              fetchReqBenchDataCC?.[0]?.CostcenterType,
              task?.ATTRIBUTE_3,
              "v4",
              "MDG_DYNAMIC_WF_CC_DT",
              MODULE_MAP?.CC
            );
            setWfLevels(workflowLevelsDtData);
          } catch (err) {
            customError(err);
          }
        };
       if (requestHeaderData?.TemplateName && fetchReqBenchDataCC?.[0]?.CostcenterType  && task?.ATTRIBUTE_3) {
         
          fetchWorkflowLevels();
        }
      }, [requestHeaderData?.TemplateName, fetchReqBenchDataCC?.[0]?.CostcenterType,task?.ATTRIBUTE_3]);
  const fieldNameList = Array.isArray(rawFieldName)
    ? rawFieldName.map((f) => f.trim())
    : typeof rawFieldName === "string"
    ? rawFieldName.split(",").map((f) => f.trim())
    : [];
  const { allFields, mandatoryFields, fieldMetaMap } =
    getFieldsForTemplate(changeFieldData, fieldNameList);
  useEffect(() => {
    if (task?.ATTRIBUTE_1 || RequestId) {
      getButtonsDisplayGlobal("Cost Center", "MDG_DYN_BTN_DT", "v3");
    }
  }, [task]);


     const handleDropdownDataChange = (e, params, value) => {
      const newValue = e?.code;
        updateChangeLogGlForChange({
          uniqueId: params.row.id,
          filedName:params?.colDef?.headerName,
          jsonName: params?.field,
          currentValue: newValue,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId,
          accountNumber:params.row.costCenter
        });
      const updatedRow = {
        ...params.row,
        [params.field]: newValue,
      };
      if (reqBench) {
        dispatch(updateReqBenchRowCc(updatedRow));
      } else {
        dispatch(updateCostCenterRowCc(updatedRow));
      }
    };
  

  // Function to handle opening and closing the popup
  const handleClose = () => {
    setOpen(false);
    setDownloadClicked(false);
    navigate("/requestbench");
  };
  const handleOk = (params) => {
    getObjectLock(params);
  };
  // Dropdown change handlers
  const allColumns = [
    {
      field: "included",
      width: 120,
      align: "center",
      headerAlign: "center",
      disableColumnMenu: true,
      sortable: false,
        renderHeader: () => {
          const data = reqBench ? fetchReqBenchDataCC : fetchedCostCenterData;
          const allSelected = data.every((row) => row.included);
          const someSelected = data.some((row) => row.included);
      
          return (
            <Checkbox
              checked={allSelected}
              indeterminate={!allSelected && someSelected}
              disabled={isDisabled}
              onChange={(e) => {
                const updatedData = data.map((row) => ({
                  ...row,
                  included: e.target.checked,
                }));
                if (reqBench) {
                  dispatch(setFetchReqBenchDataCc(updatedData));
                } else {
                  dispatch(setFetchedCostCenterDataCc(updatedData));
                }
              }}
            />
          );
        },
        renderCell: (params) => {
          const data = reqBench ? fetchReqBenchDataCC : fetchedCostCenterData;
          const rowIndex = data.findIndex((row) => row.id === params.row.id);
      
          return (
            <Checkbox
              checked={data[rowIndex]?.included || false}
              disabled={isDisabled}
              onChange={(e) => {
                const updatedData = [...data];
                updatedData[rowIndex] = {
                  ...updatedData[rowIndex],
                  included: e.target.checked,
                };
                if (reqBench) {
                  dispatch(setFetchReqBenchDataCc(updatedData));
                } else {
                  dispatch(setFetchedCostCenterDataCc(updatedData));
                }
              }}
            />
          );
        },
    },
    {
      field: "lineNumber",
      headerName: "Sl No",
      width: 100,
      align: "center",
      headerAlign: "center",
      renderCell: (params) => {
        const rowIndex = (
          reqBench ? fetchReqBenchDataCC : fetchedCostCenterData
        ).findIndex((row) => row.id === params.row.id);
        return <div>{rowIndex + 1}</div>;
      },
    },
    {
      field: "controllingArea",
      headerName: "Controlling Area",
      width: 150,
      editable: false,
    },
    {
      field: "compCode",
      headerName: "Company Codes",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "costCenter",
      headerName: "Cost Center",
      width: 150,
      editable: false,
      renderCell: (params) => (
        <span
          style={{ color: "#9e9e9e", pointerEvents: "none", cursor: "default" }}
        >
          {params.value || ""}
        </span>
      ),
    },
    {
      field: "ProfitCtr",
      headerName: "Profit Center",
      width: 250,
      editable: false,
      renderCell: (params) => {
        const field = params.field;
        const options = dropdownDataProfitCenter || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;
        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(e)=>handleDropdownDataChange(e,params,selectedValue)}
            // Note: Might be used for Checking if any issue occur
            // onChange={(selectedOption) => {
            //   const newValue = selectedOption?.code || "";
            //   const updatedRow = {
            //     ...params.row,
            //     [field]: newValue,
            //   };
            //   if (reqBench) {
            //     dispatch(updateReqBenchRowCc(updatedRow));
            //   } else {
            //     dispatch(updateCostCenterRowCc(updatedRow));
            //   }
            // }}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "FuncAreaLong",
      headerName: "Functional Area",
      width: 250,
      editable: false,
      renderCell: (params) => {
        const field = params.field;
        const options = dropdownDataFunctionalArea || [];
        const selectedValue =
          options.find(
            (item) =>
              item.code === params.row[field] || item.desc === params.row[field]
          ) || null;
        return (
          <SingleSelectDropdown
            options={options}
            value={selectedValue}
            onChange={(e)=>handleDropdownDataChange(e,params,selectedValue)}
            // Note: Might be used for Checking if any issue occur
            // onChange={(selectedOption) => {
            //   const newValue = selectedOption?.code || "";
            //   const updatedRow = {
            //     ...params.row,
            //     [field]: newValue,
            //   };
            //   if (reqBench) {
            //     dispatch(updateReqBenchRowCc(updatedRow));
            //   } else {
            //     dispatch(updateCostCenterRowCc(updatedRow));
            //   }
            // }}
            placeholder={`Select ${params.colDef.headerName}`}
            disabled={false}
            minWidth="90%"
            listWidth={235}
          />
        );
      },
    },
    {
      field: "Name",
      headerName: "Short Description",
      width: 200,
      editable: false,
    },
    {
      field: "Descript",
      headerName: "Long Description",
      width: 250,
      editable: false,
    },
    {
      field: "PersonInChargeUser",
      headerName: "CC User Responsible",
      width: 250,
      editable: false,
    },
    {
      field: "PersonInCharge",
      headerName: "CC Person Responsible",
      width: 250,
      editable: false,
    },
    {
      field: "blockingStatus",
      headerName: "Blocking Status",
      width: 250,
      editable: false,
      renderCell: () => {
        if (requestHeaderData?.TemplateName === "All Other Fields") {
          return "Unblock";
        } else if (requestHeaderData?.TemplateName === "Block") {
          return "Block";
        }
      },
    },
    { field: "createdBy", headerName: "Created By", width: 150 },
    {
      field: "validFrom",
      headerName: "Valid From",
      width: 150,
    },
    { field: "validTo", headerName: "Valid To", width: 150 },
    { field: "AddrCity", headerName: "City", width: 150, editable: true },
    {
      field: "AddrCountry",
      headerName: "Country/Reg.",
      width: 150,
      editable: true,
    },
    { field: "AddrStreet", headerName: "Street", width: 150, editable: true },
    { field: "AddrPostlCode", headerName: "Postal Code", width: 150, editable: true },
    { field: "AddrRegion", headerName: "Region", width: 150, editable: true },
    { field: "AddrName1", headerName: "Name 1", width: 150, editable: true },
    { field: "AddrName2", headerName: "Name 2", width: 150, editable: true },
    { field: "AddrName3", headerName: "Name 3", width: 150, editable: true },
    { field: "AddrName4", headerName: "Name 4", width: 150, editable: true },
  ];

    const handleGridTextChange = (params) => (e) => {
      const newValue = e.target.value.toUpperCase();
      params.api.setEditCellValue({
        id: params.id,
        field: params.field,
        value: newValue,
      });
  
      // if (requestId) {
        updateChangeLogGlForChange({
          uniqueId: params.row.id,
          filedName:params?.colDef?.headerName,
          jsonName: params?.field,
          currentValue: newValue,
          requestId: initialPayload?.RequestId,
          childRequestId: requestId,
          accountNumber:params.row.costCenter
        });
      // }
    };
  
  const fixedColumns = allColumns.slice(0, 4);
  const dynamicColumns = allColumns
    .slice(4)
    .filter((col) => allFields.includes(col.headerName?.trim()))
    .map((col) => {
      const trimmedHeader = col.headerName?.trim();
      const isMandatory = mandatoryFields.includes(trimmedHeader);
      const isEditableField = [
        "Descript",
        "Name",
        "PersonInChargeUser",
        "costCenterName",
        "PersonInCharge",
        "AddrCity",
        "AddrStreet",
        "AddrPostlCode",
        "AddrName1",
        "AddrName2",
        "AddrName3",
        "AddrName4",
      ].includes(col.field);
      const maxLength = 40;
      return {
        ...col,
        headerName: trimmedHeader,
        editable: true,
        renderHeader: () =>
          isMandatory ? (
            <span>
              {trimmedHeader} <span style={{ color: "red" }}>*</span>
            </span>
          ) : (
            trimmedHeader
          ),
        ...(isEditableField && {
 renderCell: (params) => (
            <Box sx={{ position: "relative", width: "100%" }}>
              <TextField
                value={params.value || ""}
                variant="outlined"
                size="small"
                disabled={isDisabled}
                // onChange={(e) =>
                //   params.api.setEditCellValue({
                //     id: params.id,
                //     field: params.field,
                //     value: e.target.value.toUpperCase(),
                //   })
                // }
                onChange={handleGridTextChange(params)}
                fullWidth
                InputProps={{
                  style: {
                    maxLength: maxLength,
                    paddingBottom: "5px",
                  },
                }}
              />
              <Box
                sx={{
                  position: "absolute",
                  bottom: 0,
                  left: 14,
                  fontSize: "8px",
                  color: "blue",
                  pointerEvents: "none",
                }}
              >
                {params.value?.length || 0}/{maxLength}
              </Box>
            </Box>
          ),
 
          renderEditCell: (params) => {
            const currentValue = params.value || "";
            // const maxLength = 40;
            const currentLength = currentValue.length;
            const hasReachedMax = currentLength >= maxLength;
 
            return (
              <Box sx={{ position: "relative", width: "100%" }}>
                <TextField
                  value={currentValue}
                  autoFocus
                  onFocus={(e) => {
                    e.target.setSelectionRange(0, e.target.value.length);
                  }}
                  // onChange={(e) => {
                  //   const newValue = e.target.value.toUpperCase();
                  //   if (newValue.length <= maxLength) {
                  //     params.api.setEditCellValue({
                  //       id: params.id,
                  //       field: params.field,
                  //       value: newValue,
                  //     });
                  //   }
                  // }}
                  onChange={handleGridTextChange(params)}
                  variant="outlined"
                  size="small"
                  fullWidth
                  placeholder={`Enter ${trimmedHeader}`}
                  inputProps={{
                    maxLength: maxLength + 1, // Allow 1 extra to detect overflow visually
                    style: { paddingBottom: "15px" },
                  }}
                />
                <Box
                  sx={{
                    position: "absolute",
                    bottom: 0,
                    left: 14,
                    fontSize: "9px",
                    color: hasReachedMax ? "red" : "blue",
                    pointerEvents: "none",
                  }}
                >
                  {hasReachedMax
                    ? "Max length reached"
                    : `${currentLength}/${maxLength}`}
                </Box>
              </Box>
            );
          },
 
        }),
      };
    });
  const columns = [...fixedColumns, ...dynamicColumns];
  const processRowUpdate = (newRow) => {
    dispatch(updateCostCenterRowCc(newRow));
    return newRow;
  };
  const processRowUpdateReqBench = (newRow) => {
    dispatch(updateReqBenchRowCc(newRow));
    return newRow;
  };
  const handleRowClick = (params) => {
    setSelectedRow(params.row);
  };
  useEffect(() => {
    if (selectedControllingArea && selectedCompanyCodes.length > 0) {
      fetchCostCenters();
    }
  }, [selectedControllingArea, selectedCompanyCodes]);
  useEffect(() => {
    getCompanyCode();
    getCostCenterCategory();
    getFunctionalArea();
  }, []);
  useEffect(() => {
    getProfitCenter()
  }, []);
  const getCompanyCode = () => {
    const hSuccess = (data) => {
      setDropdownDataCompany(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CompanyCode", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCompanyCodeBasedOnControllingArea?controllingArea=ETCA&rolePrefix=ETP`,
      "get",
      hSuccess,
      hError
    );
  };
  const getProfitCenter = (CA,compcode) => {
      const payload = {
    controllingArea: CA,
    companyCode: compcode,
    top: "100",
    skip: "0",
  };
    const hSuccess = (data) => {
      setDropdownDataProfitCenter(data.body?.list);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "ProfitCtr", data: data.body?.list },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(`/${destination_ProfitCenter_Mass}/data/getProfitCentersNo`, "post", hSuccess, hError, payload);
  }
  const getCostCenterCategory = () => {
    const hSuccess = (data) => {
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "CostcenterType", data: data.body },
      });
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCenterCategory`,
      "get",
      hSuccess,
      hError
    );
  };
  const getFunctionalArea = () => {
    const hSuccess = (data) => {
      setDropdownDataFunctionalArea(data.body);
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "FuncAreaLong", data: data.body },
      });
    };
    const hError = (error) => {
      console.error(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getFunctionalArea`,
      "get",
      hSuccess,
      hError
    );
  };
  const getCountryData = () => {
    const hSuccess = (data) => {
      dispatch({
        type: "SET_DROPDOWN",
        payload: { keyName: "Country", data: data.body },
      });
    };
    const hError = (error) => {
      console.log(error);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCountryOrReg`,
      "get",
      hSuccess,
      hError
    );
  };
  useEffect(() => {
    getCountryData();
  }, []);
  const fetchCostCenters = () => {
    const controllingArea = `${selectedControllingArea}`;
    selectedCompanyCodes.forEach((companyCode) => {
      const payload = {
        controllingArea,
        companyCode,
        top: "100",
        skip: "0",
      };
      doAjax(
        `/${destination_CostCenter_Mass}/data/getCostCentersNo`,
        "post",
        (data) => {
          if (Array.isArray(data.body?.list)) {
            const newCostCenters = data.body.list.map((item) => ({
              code: item.code,
              desc: item.desc,
              companyCode: companyCode, // Needed for filtering
            }));

            // Merge without duplicates
            setCostCenterOptions((prev) => {
              const existingCodes = new Set(prev.map((cc) => cc.code));
              const uniqueNew = newCostCenters.filter(
                (cc) => !existingCodes.has(cc.code)
              );
              return [...prev, ...uniqueNew];
            });
          }
        },
        (err) => console.error("Cost Center fetch failed", err),
        payload
      );
    });
  };
  const transformCostCenterData = (data) => {
    return data.map((item) => {
      const shouldUnblock =
        item?.controlTabDto?.LockIndActualPrimaryCosts ||
        item?.controlTabDto?.LockIndPlanPrimaryCosts ||
        item?.controlTabDto?.LockIndActSecondaryCosts ||
        item?.controlTabDto?.LockIndPlanSecondaryCosts ||
        item?.controlTabDto?.LockIndActualRevenues ||
        item?.controlTabDto?.LockIndPlanRevenues;
      const existingBlockingStatus = shouldUnblock ? "Block" : "Unblock";
      return {
        included:true,
        id: item.costCenter,
        costCenterHeaderID: item?.costCenterHeaderId,
        costCenterErrorId: item?.costCenterErrorId,
        costCenter: item.costCenter,
        controllingArea: item.controllingArea,
        CostcenterType: item?.basicDataTabDto?.CostcenterType,
        FuncAreaLong: item?.basicDataTabDto?.FuncAreaLong,
        currency: item?.basicDataTabDto?.Currency,
        profitCtr: item?.basicDataTabDto?.ProfitCtr,
        compCode: item?.basicDataTabDto?.CompCode,
        Name: item?.basicDataTabDto?.Name,
        Descript: item.basicDataTabDto?.Descript || "",
        PersonInChargeUser: item.basicDataTabDto?.PersonInCharge || "",
        PersonInCharge: item.basicDataTabDto?.PersonInChargeUser || "",
        createdBy: item.historyTabDto?.ReqCreatedBy || "",
        validFrom: item?.fromValid || "",
        validTo: item?.toValid || "",
        AddrCity: item.addressTabDto?.City || "",
        AddrStreet: item.addressTabDto?.Street || "",
        AddrCountry: item.addressTabDto?.Country || "",
        AddrRegion: item.addressTabDto?.AddrRegion || "", // renamed from "region"
        AddrPostlCode:item.addressTabDto?.AddrPostlCode || "",
        regio: item.addressTabDto?.Regio || "", // renamed from "region"
        AddrName1: item.addressTabDto?.Name1 || "",
        AddrName2: item.addressTabDto?.Name2 || "",
        AddrName3: item.addressTabDto?.Name3 || "",
        AddrName4: item.addressTabDto?.Name4 || "",
        lockIndActualPrimaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Primary Costs",
          item?.controlTabDto?.LockIndActualPrimaryCosts
        ),
        lockIndPlanPrimaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Primary Costs",
          item?.controlTabDto?.LockIndPlanPrimaryCosts
        ),
        lockIndActSecondaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Secondary Costs",
          item?.controlTabDto?.LockIndActSecondaryCosts
        ),
        lockIndPlanSecondaryCosts: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Secondary Costs",
          item?.controlTabDto?.LockIndPlanSecondaryCosts
        ),
        lockIndActualRevenues: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Actual Revenue",
          item?.controlTabDto?.LockIndActualRevenues
        ),
        lockIndPlanRevenues: getLockIndicatorValue(
          item?.basicDataTabDto?.CostcenterType,
          requestHeaderData?.TemplateName,
          existingBlockingStatus,
          "Plan Revenue",
          item?.controlTabDto?.LockIndPlanRevenues
        ),
      };
    });
  };
  const getObjectLock = (params) => {
    if (!selectedCostCenters.length || !selectedControllingArea) return;
    const objectLockPayload = selectedCostCenters?.map((cc) => ({
      controllingArea: selectedControllingArea,
      costCenter: cc,
      changedFieldsToCheck: initialPayload?.fieldName??initialPayload?.FieldName,
    }));
    const successHandler = (response) => {
      const hasError = response?.some((item) => item?.statusCode !== 200);
      if (!hasError) {
        if (params === "OK") {
          setOpen(false);
          if(reqBench!=="true"){
             setShowGrid(true);
          }
          fetchCostCenterDetails(); // 🔥 Call the new API here
        } else if (params === "Download") {
          handleDownloadDialogOpen();
        }
      } else {
        const filteredData = response.filter((item) => item.statusCode === 400);
        let duplicateFieldsArr = [];
        filteredData?.forEach((item, index) => {
          const dataHash = {
            id: `${item?.body?.costCenter}_${index}`, // ✅ UNIQUE ID
            objectNo: item?.body?.costCenter,
            reqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingRequestHeaderId)
              ?.filter(Boolean),
            childReqId: item?.body?.matchingRequests
              ?.map((req) => req?.matchingChildHeaderIdsSet)
              ?.filter(Boolean),
            requestedBy: item?.body?.matchingRequests
              ?.map((req) => req?.RequestCreatedBy)
              ?.filter(Boolean),
          };
          duplicateFieldsArr.push(dataHash);
        });
        setDuplicateFieldsData(duplicateFieldsArr);
        setShowTableInDialog(true);
      }
    };
    const errorHandler = (err) => {
      console.error("Failed to fetch cost center details", err);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/alter/checkDuplicateCCRequest`,
      "post",
      successHandler,
      errorHandler,
      objectLockPayload
    );
  };
  const fetchCostCenterDetails = () => {
    if (!selectedCostCenters.length || !selectedControllingArea) return;
    const payload = {
      coAreaCCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: selectedControllingArea,
      })),
    };
    const successHandler = (data) => {
      const rawData = data?.body || [];
      const transformed = transformCostCenterData(rawData);
      if(reqBench==="true"){
        dispatch(setFetchReqBenchDataCc(transformed));
      }else{
        dispatch(setFetchedCostCenterDataCc(transformed));
      }

      let rowsBodyData = {};
      
      transformed?.forEach((data) => {
        const dynamicKey = data?.id ?? "";
        rowsBodyData[dynamicKey] = {
          ...data,
        };
      });

      let requestHeaderData ={}
      let rowsHeaderData ={}

      

      const CopyDataForCCInitial = {
        requestHeaderData,
        rowsHeaderData,
        rowsBodyData,
      };
      
      dispatch(setCreatePayloadCopyForChangeLog(CopyDataForCCInitial));

     
      dispatch(setOriginalCostCenterDataCc(CopyDataForCCInitial));
      getProfitCenter(transformed?.[0]?.controllingArea,transformed?.[0]?.compCode)
    };
    const errorHandler = (err) => {
      console.error("Failed to fetch cost center details", err);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/data/getCostCentersData`,
      "post",
      successHandler,
      errorHandler,
      payload
    );
  };
  useEffect(() => {
    if (
      reqBench === "true" &&
      Array.isArray(apiResponses) &&
      apiResponses.length > 0 &&
      fetchReqBenchDataCC.length === 0
    ) {
      let transformedData = ''
      if(apiResponses?.CostCenterID!==null){
        transformedData = transformCostCenterResponseChange(apiResponses);
      }
      // const transformedData = transformCostCenterResponseChange(apiResponses);
      dispatch(setFetchReqBenchDataCc(transformedData));
      dispatch(setOriginalReqBenchDataCc(transformedData));
    }
  }, [apiResponses, reqBench]);
  useEffect(() => {
      if (
        task &&
        Object.keys(task).length !== 0 &&
        Array.isArray(apiResponses) &&
        apiResponses.length > 0
      ) {
        const transformedData = transformCostCenterResponseChange(apiResponses);
  
      dispatch(setFetchReqBenchDataCc(transformedData));
      dispatch(setOriginalReqBenchDataCc(transformedData));
      }
    }, [apiResponses, task]);
  useEffect(() => {
    if (downloadClicked) {
      setOpen(true);
    }
  }, [downloadClicked]);

  const parsedData = (apiResponses ?? []).map((item) => {
    let changedFields = {};
    if (typeof item.changedFields === "object" && item.changedFields !== null) {
      changedFields = item.changedFields;
    } else if (typeof item.ChangedFields === "string") {
      try {
        changedFields = JSON.parse(item.ChangedFields);
      } catch {
        changedFields = {};
      }
    }
    const { changedFields: _, ChangedFields, ...rest } = item;
    return {
      ...rest,
      changedFields,
    };
  });
  useEffect(() => {
    if (!parsedData || parsedData.length === 0) return;
    const newChangedFieldsMap = {};
    parsedData.forEach((row) => {
      newChangedFieldsMap[row.CostCenterID] = row.changedFields || {};
    });
    dispatch(setChangedFieldsMapCc(newChangedFieldsMap));
  }, [apiResponses]);
  const getLockIndicatorValue = (
    CCCategory,
    TemplateName,
    ExistingBlockingStatus,
    LockIndicator,
    ExistingLockIndicatorValue
  ) => {
    if (TemplateName === "Block") {
      return "X";
    } else if (TemplateName === "All Other Fields") {
      if (ExistingBlockingStatus === "Unblock") {
        return ExistingLockIndicatorValue === true ? "X" : ""; // Return the existing value for Unblock
      } else if (ExistingBlockingStatus === "Block") {
        const match = lockIndicatorMap.find((obj) =>
          obj.hasOwnProperty(CCCategory)
        );
        if (match) {
          const indicators = match[CCCategory];
          if (indicators.includes(LockIndicator)) {
            return "X"; // Block if the LockIndicator is in the list for the category
          }
        }
      }
    } else if (TemplateName === "CC Category & FERC Indicator Change") {
      if (ExistingBlockingStatus === "Unblock") {
        const match = lockIndicatorMap.find((obj) =>
          obj.hasOwnProperty(CCCategory)
        );
        if (match) {
          const indicators = match[CCCategory];
          if (indicators.includes(LockIndicator)) {
            return "X"; // Block if the LockIndicator is in the list for the category
          }
        }
      } else if (ExistingBlockingStatus === "Block") {
        return "X"; // Block all indicators in this case
      }
    }
    return ""; // Default case if no conditions match
  };

  // Note: It will be used for ReferenceError, might be needed later
  //   const payload = changePayloadForCC(
  //   requestHeaderSlice,
  //   initialPayload,
  //   task,
  //   isreqBench,
  //   fetchReqBenchDataCC,
  //   fetchedCostCenterData,
  //   remarks,
  //   userInput
  // );

  const handleSaveAsDraft = () => {
  const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
      setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
      showSnackbar(error?.message,'error')
      setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSendBack = (type,remarks,userInput) => {
  const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,          // dynamic remarks
    userInput,         // dynamic user input
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/costCentersSendToLevel`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleRejectAndCancel = (type,remarks,userInput) => {
     const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,          // dynamic remarks
    userInput,         // dynamic user input
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_200) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSaveAsDraft`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForReview = (type,remarks,userInput) => {
      const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,          // dynamic remarks
    userInput,         // dynamic user input
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersSubmitForReview`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSubmitForApprove = (type,remarks,userInput) => {
     const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,          // dynamic remarks
    userInput,         // dynamic user input
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/changeCostCentersApprovalSubmit`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const validateAllRows = (type,remarks,userInput) => {
    const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,
    userInput,
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleValidateAndSyndicate = (type,remarks,userInput) => {
      const payload = changePayloadForCC(
    requestHeaderSlice,
    initialPayload,
    fetchedDisplayData,
    task,
    isreqBench,
    fetchReqBenchDataCC,
    fetchedCostCenterData,
    remarks,
    userInput,
    selectedLevel,
    changeChangeLogData
  );
    const hSuccess = (data) => {
setBlurLoading(false);

      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
        showSnackbar(error?.message,'error')
        setBlurLoading(false);
    };
    doAjax(
      type === "VALIDATE"
        ? `/${destination_CostCenter_Mass}/massAction/validateMassCostCenter`
        : `/${destination_CostCenter_Mass}/massAction/changeCostCentersApproved`,
      "POST",
      hSuccess,
      hError,
      payload
    );
  };
  const handleSnackBarOpen = () => {
    setOpenSnackbar(true);
  };
  const handleSnackBarClose = () => {
    setOpenSnackbar(false);
  };
  const handleDownloadDialogOpen = () => {
    setOpenDownloadDialog(true);
  };
  const handleDownloadDialogClose = () => {
    setOpenDownloadDialog(false);
    setDownloadType("systemGenerated");
  };
  const handleDownloadTypeChange = (event) => {
    setDownloadType(event?.target?.value);
  };
  const onDownloadTypeChange = () => {
    if (downloadType === "systemGenerated") {
      handleDownload();
      handleDownloadDialogClose();
    }
    if (downloadType === "mailGenerated") {
      handleEmailDownload();
      handleDownloadDialogClose();
    }
  };
  const handleDownload = () => {
    setLoaderMessage(
      "Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."
    );
    setBlurLoading(true);
    setOpen(false);
    setDownloadClicked(false);
    if (!RequestId) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    let payload = {
      coAreaCCs: selectedCostCenters.map((cc) => ({
        costCenter: cc,
        controllingArea: selectedControllingArea,
      })),
      requestId:
        requestHeaderData?.RequestId || initialPayload?.requestId || "",
      templateHeaders: requestHeaderData?.FieldName
        ? requestHeaderData.FieldName?.join("$^$")
        : "",
      templateName: requestHeaderData?.TemplateName
        ? requestHeaderData.TemplateName
        : "",
      dtName: "MDG_CHANGE_TEMPLATE_DT",
      version: "v6",
    };
    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showToast(ERROR_MESSAGES?.NO_DATA_FOUND, "error", {
          position: "top-center",
          largeWidth: true,
        });
        setTimeout(() => {
          navigate(APP_END_POINTS?.REQUEST_BENCH);
        }, 2600);
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");
      link.href = href;
      link.setAttribute(
        "download",
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `${requestHeaderData?.TemplateName}_Mass Change.xlsx has been downloaded successfully.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showToast(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error", {
        position: "top-center",
      });
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcelWithData`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };
  const handleEmailDownload = () => {
    setBlurLoading(true);
    let templateKeys =
      Templates[initialPayload?.TemplateName]?.map((item) => item.key) || [];
    let payload = {};
    if (activeTab === 0) {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] = convertedValues?.[key] ? convertedValues?.[key] : "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    } else {
      payload = {
        materialDetails: [
          templateKeys.reduce((acc, key) => {
            acc[key] =
              rowsOfMaterialData
                .map((row) => row[key]?.trim())
                .filter((value) => value !== "")
                .join(",") || "";
            return acc;
          }, {}),
        ],
        templateHeaders: initialPayload?.FieldName
          ? initialPayload.FieldName?.join("$^$")
          : "",
        requestId: RequestId || initialPayload?.RequestId || "",
        templateName: initialPayload?.TemplateName
          ? initialPayload.TemplateName
          : "",
        dtName: "MDG_MAT_CHANGE_TEMPLATE",
        version: "v4",
        rolePrefix: "",
      };
    }
    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      setSuccessMsg(true);
      setMessageDialogMessage(
        `Download has been started. You will get the Excel file via email.`
      );
      setAlertType("success");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    const hError = () => {
      setBlurLoading(false);
      setSuccessMsg(true);
      setMessageDialogMessage(
        "Oops! Something went wrong. Please try again later."
      );
      setAlertType("danger");
      handleSnackBarOpen();
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };
    doAjax(
      `/${destination_CostCenter_Mass}/excel/downloadExcelWithDataInMail`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <div>
      {successMsg && (
        <ReusableSnackBar
          openSnackBar={openSnackbar}
          alertMsg={messageDialogMessage}
          alertType={alertType}
          handleSnackBarClose={handleSnackBarClose}
        />
      )}
            <SuccessDialog
              open={successDialogOpen}
              onClose={() => setSuccessDialogOpen(false)}
              title={dialogData.title}
              message={dialogData.message}
              subText={dialogData.subText}
              buttonText={dialogData.buttonText}
              redirectTo={dialogData.redirectTo}
            />
            <ReusableSnackBar
              openSnackBar={openSnackbar}
              alertMsg={alertMsg}
              handleSnackBarClose={handleSnackBarClose}
              alertType={alertType}
            />
      {(requestHeaderData?.TemplateName || downloadClicked) && (
        <>
          {fetchedCostCenterData.length === 0 && !reqBench && Object.keys(task || {}).length === 0 && (
            <>
              <Dialog
                open={open}
                TransitionComponent={Transition}
                keepMounted
                onClose={(event, reason) => {
                  if (
                    reason === "backdropClick" ||
                    reason === "escapeKeyDown"
                  ) {
                    return;
                  }
                  handleClose();
                }}
                maxWidth="sm"
                fullWidth
              >
                <Box
                  sx={{
                    backgroundColor: "#e3f2fd",
                    padding: "1rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <FeedOutlinedIcon
                    color="primary"
                    sx={{ marginRight: "0.5rem" }}
                  />
                  <Typography variant="h6" component="div" color="primary">
                    Select Cost Center for Change
                  </Typography>
                </Box>
                <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{
                        key: "controllingArea",
                        label: "Controlling Area",
                      }}
                      dropDownData={{
                        controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                      }}
                      selectedValues={{
                        controllingArea: selectedControllingArea
                          ? [{ code: selectedControllingArea }]
                          : [],
                      }}
                      handleSelectionChange={(key, value) => {
                        setSelectedControllingArea(
                          value.length > 0 ? value[0].code || value[0] : ""
                        );
                      }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      singleSelect={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "companyCode", label: "Company Code" }}
                      dropDownData={{ companyCode: dropdownDataCompany || [] }}
                      selectedValues={{
                        companyCode: selectedCompanyCodes.map(
                          (code) =>
                            dropdownDataCompany?.find(
                              (item) => item.code === code
                            ) || { code }
                        ),
                      }}
                      handleSelectAll={() => {
                        if (
                          selectedCompanyCodes.length ===
                          dropdownDataCompany?.length
                        ) {
                          setSelectedCompanyCodes([]);
                        } else {
                          setSelectedCompanyCodes(
                            dropdownDataCompany?.map((item) => item.code) || []
                          );
                        }
                      }}
                    handleSelectionChange={(key,value) => {
                      setSelectedCompanyCodes(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                      formatOptionLabel={(option) => {
                        if (option.code && option.desc) {
                          return `${option.code} - ${option.desc}`;
                        }
                        return option.code || "";
                      }}
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                  <Box sx={{ marginBottom: "1rem" }}>
                    <FilterChangeDropdown
                      param={{ key: "costCenter", label: "Cost Center" }}
                        dropDownData={{
                        costCenter: costCenterOptions
                          .filter((cc) =>
                            selectedCompanyCodes.includes(cc.companyCode)
                          )
                          .map((cc) => ({
                            code: cc.code,
                            desc: cc.desc,
                          })),
                      }}
                      selectedValues={{
                        costCenter: selectedCostCenters
                          .filter((code) =>
                            costCenterOptions.some(
                              (cc) =>
                                cc.code === code &&
                                selectedCompanyCodes.includes(cc.companyCode)
                            )
                          )
                          .map((code) => ({ code })),
                      }}
                      handleSelectAll={() => {
                        const filteredCCs = costCenterOptions.filter((cc) =>
                          selectedCompanyCodes.includes(cc.companyCode)
                        );
                        if (
                          selectedCostCenters.length === filteredCCs.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(
                            filteredCCs.map((cc) => cc.code)
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const ccCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedCostCenters(ccCodes);
                      }}
                      formatOptionLabel={(option) =>
                        typeof option === "string"
                          ? option
                          : option.code || option
                      }
                      isSelectAll={true}
                      errors={{}}
                    />
                  </Box>
                </DialogContent>
                <DialogActions
                  sx={{
                    padding: "0.5rem 1.5rem",
                    display: "flex",
                    alignItems: "center",
                  }}
                >
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      onClick={handleClose}
                      color="error"
                      variant="outlined"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        textTransform: "none",
                        borderColor: "#cc3300",
                        fontWeight: 500,
                      }}
                    >
                      Cancel
                    </Button>
                    {requestHeaderData?.RequestType !==
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleOk("OK");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        OK
                      </Button>
                    )}
                    {requestHeaderData?.RequestType ===
                      REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                      <Button
                        onClick={() => {
                          handleOk("Download");
                        }}
                        variant="contained"
                        sx={{
                          height: 36,
                          minWidth: "3.5rem",
                          backgroundColor: "#3B30C8",
                          textTransform: "none",
                          fontWeight: 500,
                          "&:hover": {
                            backgroundColor: "#2c278f",
                          },
                        }}
                      >
                        Download
                      </Button>
                    )}
                  </Box>
                </DialogActions>
              </Dialog>
              <DownloadDialog
                onDownloadTypeChange={onDownloadTypeChange}
                open={openDownloadDialog}
                downloadType={downloadType}
                handleDownloadTypeChange={handleDownloadTypeChange}
                onClose={handleDownloadDialogClose}
              />
              <ReusableBackDrop
                blurLoading={blurLoading}
                loaderMessage={loaderMessage}
              />
            </>
          )}
          {showGrid && (
            <Box sx={{ mt: 4, px: 4 }}>
              <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                Cost Center List
              </Typography>
              <Paper
                elevation={3}
                sx={{
                  borderRadius: 3,
                  overflow: "hidden",
                  border: "1px solid #e0e0e0",
                  backgroundColor: "#fafbff",
                }}
              >
                <Box sx={{ p: 2 }}>
                  <ReusableDataTable
                    rows={fetchedCostCenterData}
                    columns={columns}
                    pageSize={10}
                    tempheight="50vh"
                    getRowIdValue="id"
                    editMode="row"
                    status_onRowSingleClick
                    callback_onRowSingleClick={handleRowClick}
                    processRowUpdate={(updatedRow) =>
                                        processRowUpdate(updatedRow)
                                      }
                    experimentalFeatures={{ newEditingApi: true }}
                    isCellEditable={(params) =>
                      !["costCenter", "companyCode"].includes(params.field)
                    }
                    getRowClassName={(params) =>
                      selectedRow?.id === params.row.id ? "Mui-selected" : ""
                    }
                  />
                </Box>
              </Paper>
              <Box
                sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveAsDraft}
                >
                  Save as draft
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={validateAllRows}
                >
                  Validate
                </Button>
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleSubmitForReview}
                  disabled={!isButtonEnabled}
                >
                  Submit
                </Button>
              </Box>
            </Box>
          )}
        </>
      )}
      {showTableInDialog && (
        <ObjectLockDialog
          duplicateFieldsArr={duplicateFieldsData}
          moduleName={MODULE_MAP?.["CC"]}
          open={showTableInDialog}
          onClose={() => setShowTableInDialog(false)}
        />
      )}
      <>
        {fetchReqBenchDataCC?.[0]?.costCenterHeaderID === null && reqBench==="true" && (
          <>
            <Dialog
              open={open}
              // TransitionComponent={Transition}
              // keepMounted
              onClose={(event, reason) => {
                if (reason === "backdropClick" || reason === "escapeKeyDown") {
                  return;
                }
                handleClose();
              }}
              maxWidth="sm"
              fullWidth
            >
              <Box
                sx={{
                  backgroundColor: "#e3f2fd",
                  padding: "1rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <FeedOutlinedIcon
                  color="primary"
                  sx={{ marginRight: "0.5rem" }}
                />
                <Typography variant="h6" component="div" color="primary">
                  Select Cost Center for Change
                </Typography>
              </Box>

              <DialogContent sx={{ padding: "1.5rem 1.5rem 1rem" }}>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{
                      key: "controllingArea",
                      label: "Controlling Area",
                    }}
                    dropDownData={{
                      controllingArea: [{ code: "ETCA", desc: "ETCA" }],
                    }}
                    selectedValues={{
                      controllingArea: selectedControllingArea
                        ? [{ code: selectedControllingArea }]
                        : [],
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedControllingArea(
                        value.length > 0 ? value[0].code || value[0] : ""
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    singleSelect={true}
                    errors={{}}
                  />
                </Box>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "companyCode", label: "Company Code" }}
                    dropDownData={{ companyCode: dropdownDataCompany || [] }}
                    selectedValues={{
                      companyCode: selectedCompanyCodes.map(
                        (code) =>
                          dropdownDataCompany?.find(
                            (item) => item.code === code
                          ) || { code }
                      ),
                    }}
                    handleSelectAll={() => {
                      if (
                        selectedCompanyCodes.length ===
                        dropdownDataCompany?.length
                      ) {
                        setSelectedCompanyCodes([]);
                      } else {
                        setSelectedCompanyCodes(
                          dropdownDataCompany?.map((item) => item.code) || []
                        );
                      }
                    }}
                    handleSelectionChange={(key, value) => {
                      setSelectedCompanyCodes(
                        value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        )
                      );
                    }}
                    formatOptionLabel={(option) => {
                      if (option.code && option.desc) {
                        return `${option.code} - ${option.desc}`;
                      }
                      return option.code || "";
                    }}
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>
                <Box sx={{ marginBottom: "1rem" }}>
                  <FilterChangeDropdown
                    param={{ key: "costCenter", label: "Cost Center" }}
                    dropDownData={{
                        costCenter: costCenterOptions
                          .filter((cc) =>
                            selectedCompanyCodes.includes(cc.companyCode)
                          )
                          .map((cc) => ({
                            code: cc.code,
                            desc: cc.desc,
                          })),
                      }}
                      selectedValues={{
                        costCenter: selectedCostCenters
                          .filter((code) =>
                            costCenterOptions.some(
                              (cc) =>
                                cc.code === code &&
                                selectedCompanyCodes.includes(cc.companyCode)
                            )
                          )
                          .map((code) => ({ code })),
                      }}
                      handleSelectAll={() => {
                        const filteredCCs = costCenterOptions.filter((cc) =>
                          selectedCompanyCodes.includes(cc.companyCode)
                        );
                        if (
                          selectedCostCenters.length === filteredCCs.length
                        ) {
                          setSelectedCostCenters([]);
                        } else {
                          setSelectedCostCenters(
                            filteredCCs.map((cc) => cc.code)
                          );
                        }
                      }}
                      handleSelectionChange={(key, value) => {
                        const ccCodes = value.map((item) =>
                          typeof item === "string" ? item : item.code || item
                        );
                        setSelectedCostCenters(ccCodes);
                      }}
                    formatOptionLabel={(option) =>
                      typeof option === "string"
                        ? option
                        : option.code || option
                    }
                    isSelectAll={true}
                    errors={{}}
                  />
                </Box>
              </DialogContent>
              <DialogActions
                sx={{
                  padding: "0.5rem 1.5rem",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Box sx={{ display: "flex", gap: 1 }}>
                  <Button
                    onClick={handleClose}
                    color="error"
                    variant="outlined"
                    sx={{
                      height: 36,
                      minWidth: "3.5rem",
                      textTransform: "none",
                      borderColor: "#cc3300",
                      fontWeight: 500,
                    }}
                  >
                    Cancel
                  </Button>
                  {requestHeaderData?.RequestType !==
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={()=>handleOk("OK")}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      OK
                    </Button>
                  )}
                  {requestHeaderData?.RequestType ===
                    REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
                    <Button
                      onClick={() => {
                        handleDownloadDialogOpen();
                      }}
                      variant="contained"
                      sx={{
                        height: 36,
                        minWidth: "3.5rem",
                        backgroundColor: "#3B30C8",
                        textTransform: "none",
                        fontWeight: 500,
                        "&:hover": {
                          backgroundColor: "#2c278f",
                        },
                      }}
                    >
                      Download
                    </Button>
                  )}
                </Box>
              </DialogActions>
            </Dialog>
            <DownloadDialog
              onDownloadTypeChange={onDownloadTypeChange}
              open={openDownloadDialog}
              downloadType={downloadType}
              handleDownloadTypeChange={handleDownloadTypeChange}
              onClose={handleDownloadDialogClose}
            />
            <ReusableBackDrop
              blurLoading={blurLoading}
              loaderMessage={loaderMessage}
            />
          </>
        )}
        {reqBench === "true" && (
          <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              Cost Center Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  rows={fetchReqBenchDataCC}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={(updatedRow) =>
                                        processRowUpdateReqBench(updatedRow)
                                      }
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
                moduleName={module}
                showWfLevels = {showWfLevels}
                selectedLevel={selectedLevel}
                workFlowLevels={wfLevels}
                setSelectedLevel={setSelectedLevel}
              />
            </Box>
          </Box>
        )}
{ task && Object.keys(task).length > 0 && (
              <Box sx={{ marginTop: "20px", padding: "16px" }}>
            <Typography variant="h5" gutterBottom>
              Cost Center Lists
            </Typography>
            <Paper
              elevation={4}
              sx={{ p: 0, borderRadius: 2, overflow: "hidden", mt: "50px" }}
            >
              <div>
                <ReusableDataTable
                  rows={fetchReqBenchDataCC}
                  columns={columns}
                  pageSize={10}
                  tempheight={"50vh"}
                  getRowIdValue={"id"}
                  status_onRowSingleClick={true}
                  editMode="cell"
                  callback_onRowSingleClick={handleRowClick}
                  processRowUpdate={(updatedRow) =>
                                        processRowUpdateReqBench(updatedRow)
                                      }
                  experimentalFeatures={{ newEditingApi: true }}
                  isCellEditable={(params) =>
                    !["costCenter", "companyCode"].includes(params.field)
                  }
                  getRowClassName={(params) =>
                    selectedRow?.id === params.row.id ? "Mui-selected" : ""
                  }
                />
              </div>
            </Paper>
            <Box
              sx={{ display: "flex", justifyContent: "right", mt: 3, gap: 2 }}
            >
              <BottomNavGlobal
                handleSaveAsDraft={handleSaveAsDraft}
                handleSubmitForReview={handleSubmitForReview}
                handleSubmitForApprove={handleSubmitForApprove}
                handleSendBack={handleSendBack}
                handleRejectAndCancel={handleRejectAndCancel}
                handleValidateAndSyndicate={handleValidateAndSyndicate}
                validateAllRows={validateAllRows}
                filteredButtons={filteredButtons}
                moduleName={module}
                showWfLevels = {showWfLevels}
                selectedLevel={selectedLevel}
                workFlowLevels={wfLevels}
                setSelectedLevel={setSelectedLevel}
              />
            </Box>
          </Box>
  )
}
      </>
    </div>
  );
};
export default RequestDetailsChangeCC;
