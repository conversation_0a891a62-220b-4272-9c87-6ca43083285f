import{r as l,c as D,b1 as h,j as t,d as g,O as d,B as u,F as f}from"./index-226a1e75.js";import{F as w}from"./FilterField-868050e3.js";const F=a=>{let i=(a==null?void 0:a.basicDataTabDetails)&&(Object==null?void 0:Object.entries(a==null?void 0:a.basicDataTabDetails));const[b,x]=l.useState([]);return l.useEffect(()=>{x(i==null?void 0:i.map(n=>{var c;return D(d,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...h},children:[t(d,{container:!0,children:t(g,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:n[0]})}),t(u,{children:t(d,{container:!0,spacing:1,paddingBottom:1,children:(c=[...n[1]].filter(e=>e.visibility!="Hidden").sort((e,m)=>e.sequenceNo-m.sequenceNo))==null?void 0:c.map(e=>t(w,{disabled:a==null?void 0:a.disabled,field:e,dropDownData:a.dropDownData,materialID:a==null?void 0:a.materialID,viewName:a==null?void 0:a.activeViewTab,plantData:a==null?void 0:a.plantData},e.fieldName))})})]},n[0])}))},[a==null?void 0:a.basicDataTabDetails,a.activeViewTab,a==null?void 0:a.materialID]),t(f,{children:b})};export{F as G};
