import { VALIDATION_STATUS } from "@constant/enum";
import { createSlice } from "@reduxjs/toolkit";
import { handleChangeLogData } from '@helper/helper';

const initialState = {
  changeFieldSelectiondata: {},
  lockIndicatorData:{},
  payloadData: {},
  errorFields: [],
  errorData: {},
  requiredFields: [],
  disableFields: [],
  additionalData: [
    {
      id: 1,
      materialDescriptionId: null,
      language: "EN",
      materialDescription: "",
    },
  ],
  unitsOfMeasureData: [],
  eanData: [],
  taxData: [],
  requiredFieldsGI: [],
  singleMatPayload: {},
  generalInformation: [],
  Ids:[],
  changeFieldRows:[],
  changeFieldRowsDisplay:{},
  fcRows:[],
  requestorPayload: {},
  changeLogData:{},
  filteredButtons:[],
  dynamicKeyValues: {},
  dataLoading: false,
  isSubmitDisabled: true,
  newRowIds: [],
  selectedRows: [],
  unselectedRows: [],
  templateArray: [],
  whseList: [],
  matNoList: [],
  plantList: [],
  tablesList: [],
  valuationClassData: {},
  isAnyMandatoryFieldEmpty:false,
};

export const payloadSlice = createSlice({
  name: "payload",
  initialState,
  reducers: {
    setChangeFieldSelectionData: (state, action) => {
      state.changeFieldSelectiondata = action.payload;
    },
    setLockIndicatorData: (state, action) => {
      state.lockIndicatorData = action.payload;
    },
    setRequiredFields: (state, action) => {
      if (state.requiredFields.findIndex((item) => item == action.payload) == -1) {
        state.requiredFields.push(action.payload);
      }
      return state;
    },
    setDisplayFields: (state, action) => {
      if (state.disableFields?.findIndex((item) => item == action?.payload) == -1) {
        state.disableFields?.push(action.payload);
      }
      return state;
    },
    setErrorFields: (state, action) => {
      state.errorFields = action.payload;
      return state;
    },
    setErrorData: (state, action) => {
      state.errorData = action.payload;
      return state;
    },
    setWhseData: (state, action) => {
      state.whseList = action.payload;
      return state;
    },
    setMatlNoData: (state, action) => {
      state.matNoList = action.payload;
      return state;
    },
    setPlantData: (state, action) => {
      state.plantList = action.payload;
      return state;
    },
    setChangeTableData: (state, action) => {
      state.tablesList = action.payload;
      return state;
    },
    // updateSelectedRows: (state, action) => {
    //   state.selectedRows = action.payload;
    //   return state;
    // },
    updateSelectedRows: (state, action) => {
      if (Array.isArray(action.payload)) {
        state.selectedRows = action.payload;
      } 
      else if (typeof action.payload === "object" && action.payload !== null) {
        if (Array.isArray(state.selectedRows)) {
          state.selectedRows = {};
        }
        state.selectedRows = action.payload;
      } else {
        console.error("Invalid payload type for updateSelectedRows");
      }
    },
    updateUnselectedRows: (state, action) => {
      state.unselectedRows = action.payload;
    },
    setPayload: (state, action) => {
      state.payloadData[action.payload.keyName] = action.payload.data;
      return state;
    },
    resetPayloadData: (state, action) => {
      state.payloadData = action.payload;
    },
    setDataLoading: (state, action) => {
      state.dataLoading = action.payload;
    },
    setIsSubmitDisabled: (state, action) => {
      state.isSubmitDisabled = action.payload;
    },
    setMultipleMaterialPayloadKey: (state, action) => {
      const { materialID, keyName, data } = action.payload;
      if (materialID) {
        // Ensure the materialID exists in the state
        if (!state[materialID]) {
          state[materialID] = {
            headerData: {
              id: materialID, // Initialize with a unique ID if needed
              included: false,
              lineNumber: "",
              materialType: "",
              materialNumber: "",
              globalMaterialDescription: "",
              validated: VALIDATION_STATUS.default,
            },
            payloadData: {},
          };
        }

        // Check if keyName is present, if not initialize it
        if (!state[materialID].payloadData) {
          state[materialID].payloadData = {};
        }

        // Handle null, undefined, or missing data values
        state[materialID].payloadData[keyName] = data ?? "";
      } else {
        try {
          if (state?.payloadData?.[action.payload.keyName]) {
            state.payloadData[keyName] = data;
          } else {
            state.payloadData = { ...state.payloadData, [keyName]: data };
          }
        } catch (error) {
          console.log(error, action.payload.keyName, action.payload.data, "payload");
        }
      }

      // Set data if provided, else set an empty string

      return state;
    },
    resetOnMatTypeChange: (state,action) =>{
      const materialId = action.payload.materialId;
      state[materialId].payloadData = {};
      state[materialId].headerData.views = [];
      state[materialId].headerData.Bom = false;
      state[materialId].headerData.sourceList = false;
      state[materialId].headerData.PIR = false;
      state[materialId].headerData.refMaterialData = '';
      // return state;
    },

    UpdateMaterialFieldsValue: (state, action) => {
      const { materialID, data } = action.payload;
      state[materialID].payloadData = data;
      return state;
    },

    setDisplayPayload: (state, action) => {
      state = action.payload.data;
      return state;
    },

    pushDisplayPayload: (state, action) => {
      const newPayload = action.payload.data || {};
      const filteredPayload = Object.keys(newPayload).reduce((acc, key) => {
        if (newPayload[key]?.headerData && newPayload[key]?.payloadData) {
          acc[key] = newPayload[key]; 
        }
        return acc;
      }, {});
      Object.assign(state, filteredPayload);
    },

    setMultipleMaterialHeaderKey: (state, action) => {
      const { materialID, keyName, data } = action.payload;
      // Ensure the materialID exists in the state
      if (!state[materialID]) {
        state[materialID] = {
          headerData: {},
          payloadData: {},
        };
      }

      // Ensure headerData object exists
      if (!state[materialID].headerData) {
        state[materialID].headerData = {};
      }

      // Update headerData with the provided keyName and data
      state[materialID].headerData[keyName] = data ?? ""; // Set data if provided, else set an empty string

      return state;
    },

    updateMaterialData: (state, action) => {
      const { materialID, viewID, itemID, keyName, data } = action.payload;
    
      if (materialID) {
        if (!state[materialID]) {
          state[materialID] = {
            headerData: {},
            payloadData: {},
          };
        }

        // Check if the viewID exists
        if (!state[materialID].payloadData[viewID]) {
          state[materialID].payloadData[viewID] = {};
        }

        // Check if the itemID exists
        if (!state[materialID].payloadData[viewID][itemID]) {
          state[materialID].payloadData[viewID][itemID] = {};
        }
        if (keyName) {
          state[materialID].payloadData[viewID][itemID][keyName] =
            data?.code ? data.code : data;
        }
        else if(viewID && itemID){
          state[materialID].payloadData[viewID][itemID] = data;
        }
        if (viewID === "sales" && keyName === "TaxDataSet") {
          data.forEach((entry, index) => {
            if (index < 9) {
              state[materialID].payloadData["sales"][itemID][`TaxType${index + 1}`] = entry.TaxType;
              state[materialID].payloadData["sales"][itemID][`Taxclass${index + 1}`] = entry.SelectedTaxClass?.TaxClass || "string";
            }
          });
        }
      } else {
        state.payloadData[action.payload.keyName] =
          action?.payload?.data?.code
            ? action?.payload?.data?.code
            : action?.payload?.data;
      }
    
      return state;
    },
    
    pushMaterialDisplayData: (state, action) => {
      const { materialID, viewID, itemID, data } = action.payload;
      if (materialID) {
        // Ensure materialID exists
        if (!state[materialID]) {
          state[materialID] = {
            headerData: {},
            payloadData: {},
          };
        }

        // Ensure viewID exists
        if (!state[materialID].payloadData[viewID]) {
          state[materialID].payloadData[viewID] = {};
        }

        if (itemID) {
          // Ensure itemID exists
          if (!state[materialID].payloadData[viewID][itemID]) {
            state[materialID].payloadData[viewID][itemID] = {};
          }

          // Push the object into the specific itemID
          Object.assign(state[materialID].payloadData[viewID][itemID], data);
        } else {
          // Push the object into the specific viewID
          Object.assign(state[materialID].payloadData[viewID], data);
        }
      } else {
        // Handle case where materialID is not provided
        state.payloadData = { ...state.payloadData, ...data };
      }

      return state;
    },

    setMultipleMaterialHeader: (state, action) => {
      const { materialID, data, payloadData } = action.payload;

      // Ensure the materialID exists in the state
      if (!state[materialID]) {
        state[materialID] = {
          headerData: data,
          payloadData: payloadData ? payloadData : {},
        };
      }
      return state;
    },

    clearRequiredFields: (state) => {
      state.requiredFields = [];
    },
    clearPayload: (state) => {
      state.payloadData = {};
      state.additionalData = initialState?.additionalData;
      state.unitsOfMeasureData = initialState?.unitsOfMeasureData;
      state.eanData = initialState?.eanData;
      state.errorFields = [];
    },
    setAdditionalData: (state, action) => {
      const { materialID, data } = action.payload;
      if (materialID) {
        state[materialID].additionalData = data;
      }
      //state.additionalData = action.payload;
      return state;
    },
    setUOmData: (state, action) => {
      const { materialID, data } = action.payload;
      if (materialID) {
        state[materialID].unitsOfMeasureData = data;
      }
      return state;
    },
    setEanData: (state, action) => {
      const { materialID, data } = action.payload;
      if (materialID) {
        state[materialID].eanData = data;
      }
      return state;
    },
    setManufacturerID: (state, action) => {
      const { materialID, data } = action.payload;
      if (materialID) {
        state[materialID].ManufacturerID = data;
      }
      return state;
    },
    setTaxData: (state, action) => {
      state.taxData = action.payload;
    },
    // reset:() =>{initialState}
    setMatRequiredFieldsGI: (state, action) => {
      if (state.requiredFieldsGI.findIndex((item) => item == action.payload) == -1) {
        state.requiredFieldsGI.push(action.payload);
      }
      return state;
    },
    setSingleMaterialPayload: (state, action) => {
      state.singleMatPayload[action.payload.keyName] = action.payload.data;
      return state;
    },
    setGeneralInformation: (state, action) => {
      state.generalInformation = action.payload;
    },

    addMandatoryField: (state, action) => {
      if (!state.mandatoryFields.includes(action.payload)) {
        state.mandatoryFields.push(action.payload); // Add a new field if it doesn't already exist
      }
    },

    clearMandatoryFields: (state) => {
      state.mandatoryFields = []; // Clear all mandatory fields
    },

    addMaterialData: (state, action) => {
      const { uniqueId, data } = action.payload;

      // Add or update the object with the given uniqueId
      state[uniqueId] = {
        ...state[uniqueId], // Keep existing data for the uniqueId
        ...data, // Merge new data (headerData, payloadData)
      };
    },
    setIds: (state, action) => {
      state.Ids = action.payload;
    },

    clearAllExceptInitialState: (state) => {
      // Iterate over the keys of the current state
      Object.keys(state).forEach((key) => {
        if (initialState.hasOwnProperty(key)) {
          // Retain the structure and data for keys present in initialState
          if (Array.isArray(initialState[key])) {
            state[key] = [...initialState[key]]; // Clone arrays
          } else if (typeof initialState[key] === "object" && initialState[key] !== null) {
            state[key] = { ...initialState[key] }; // Clone objects
          } else {
            state[key] = initialState[key]; // Primitive values
          }
        } else {
          // Remove keys not present in initialState
          delete state[key];
        }
      });
    },
    removeMaterialRow: (state, action) => {
      delete state[action.payload];
      return state;
    },
    removeAccordionCombination: (state,action) =>{
      const { materialID, viewID, itemID } = action.payload;
      delete state[materialID].payloadData[viewID][itemID];
      return state;
    },
    setChangeFieldRows: (state, action) => {
      if (Array.isArray(action.payload)) {
        state.changeFieldRows = action.payload;
      } 
      else if (typeof action.payload === "object" && action.payload !== null) {
        if (Array.isArray(state.changeFieldRows)) {
          state.changeFieldRows = {};
        }
        state.changeFieldRows = action.payload;
      } else {
        console.error("Invalid payload type for setChangeFieldRows");
      }
    },
    setChangeFieldRowsDisplay: (state, action) => {
      state.changeFieldRowsDisplay = action.payload;
    },
    setFCRows: (state, action) => {
      state.fcRows = action.payload;
    },
    setRequestorPayload: (state, action) => {
      state.requestorPayload = action.payload;
    },
    setChangeLogData: (state, action) => {
      state.changeLogData = handleChangeLogData(state.changeLogData, action.payload);
      return state;
    },
    setDirectChangeLog: (state, action) => {
      state.changeLogData = action.payload;
      return state;
    },
    clearChangeLogData: (state) => {
      state.changeLogData = initialState?.changeLogData;
    },
    updateNewRowIds: (state, action) => {
      state.newRowIds = action.payload; // Replace the entire array
    },
    updateFilteredButtons: (state, action) => {
      state.filteredButtons = action.payload; // Replace the entire array
    },
    setDynamicKeyValue: (state, action) => {
      const { keyName, data } = action.payload;
      state.dynamicKeyValues[keyName] = data; // Store the key-value pair
    },
    clearDynamicKeyValue: (state, action) => {
      state.dynamicKeyValues = {};
    },
    setTemplateArray: (state, action) => {
      const obj = action.payload;
      const index = state.templateArray.findIndex(
        item => item.ObjectNo === obj.ObjectNo && item.FieldName === obj.FieldName
      );
      
      if (index !== -1) {
        state.templateArray[index] = { ...state.templateArray[index], ...obj };
      } else {
        state.templateArray.push(obj);
      }
    },
    clearTemplateArray: (state) => {
      state.templateArray = [];
    },
    setValuationClassData: (state, action) => {
      const { materialType, data } = action.payload;
      if (!state.valuationClassData) {
        state.valuationClassData = {};
      }
      state.valuationClassData[materialType] = data;
    },
    setUniqueAltUnit: (state, action) => {
      const { materialID, data } = action.payload;
      if (materialID) {
        state[materialID].UniqueAltUnit = data;
      }
      return state;
    },
    setOrgElementDefaultValues: (state, action) => {
      const { data } = action.payload;
      if (data) {
        state.OrgElementDefaultValues = data;
      }
      return state;
    },
    setIsAnyMandatoryFieldEmpty:(state, action) => {
      state.isAnyMandatoryFieldEmpty = action.payload
    }
  },
});

// Action creators are generated for each case reducer function
export const {
  setChangeFieldSelectionData,
  setLockIndicatorData,
  setIsSubmitDisabled,
  setErrorData,
  setWhseData,
  setMatlNoData,
  setPlantData,
  setChangeTableData,
  updateSelectedRows,
  updateUnselectedRows,
  updateMaterialData,
  setMandatoryFields,
  setMultipleMaterialHeaderKey,
  setMultipleMaterialPayloadKey, 
  setMultipleMaterialHeader, 
  setRequiredFields, 
  setDisplayFields,
  setPayload, 
  resetPayloadData, 
  clearRequiredFields, 
  setErrorFields, 
  clearPayload, 
  setAdditionalData, 
  setUOmData, 
  setEanData,
  setTaxData, 
  setMatRequiredFieldsGI, 
  setSingleMaterialPayload, 
  setGeneralInformation, 
  setDisplayPayload,
  pushDisplayPayload,
  addMaterialData, 
  pushMaterialDisplayData, 
  setIds, 
  clearAllExceptInitialState, 
  removeMaterialRow,
  removeAccordionCombination,
  setDynamicKeyValue,
  clearDynamicKeyValue, 
  updateFilteredButtons, 
  setChangeFieldRows,
  setChangeFieldRowsDisplay,
  setFCRows,
  setRequestorPayload,
  setChangeLogData,
  setDirectChangeLog,
  setDataLoading,
  updateNewRowIds,
  setTemplateArray,
  clearTemplateArray,
  clearChangeLogData,
  setValuationClassData,
  setManufacturerID,
  UpdateMaterialFieldsValue,
  setUniqueAltUnit,
  resetOnMatTypeChange,
  setOrgElementDefaultValues,
  setIsAnyMandatoryFieldEmpty
} = payloadSlice.actions;

export const payloadReducer = payloadSlice.reducer;
