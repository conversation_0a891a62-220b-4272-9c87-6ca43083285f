import React from "react";
import {
  <PERSON>,CardContent,<PERSON>po<PERSON>,Button,Chip,Collapse,Paper,Divider,Tooltip,useTheme,
} from "@mui/material";
import {
  Timeline,TimelineItem,TimelineSeparator,TimelineConnector,TimelineContent,
} from "@mui/lab";
import {
  Add as AddIcon,Edit as EditIcon,
  TrendingUp as ExtendIcon,Download as DownloadIcon,Person as PersonIcon,
Schedule as ScheduleIcon,CalendarToday as CalendarIcon,ExpandMore as ExpandMoreIcon,ExpandLess as ExpandLessIcon,
} from "@mui/icons-material";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import {
  StyledTimeline,StyledTimelineDot,StyledTimelineContent,RequestCard,
} from "./ConsolidatedRequestHistoryStyles";
import { REQUEST_TYPE } from "../../../constant/enum";

const ConsolidatedRequestTimeline = ({
  filteredRequests,
  selectedFilter,
  materialNumber,
  expandedRequest,
  setExpandedRequest,
  animateCards,
  handleDownloadPdf,
  setChangeLogModalOpen,
  setSelectedRequestForChangelog,
  module
}) => {
  const theme = useTheme();
  // Get icon for request type
  const getRequestIcon = (type) => {
    switch (type) {
      case "CREATE":
        return <AddIcon />;
      case "CHANGE":
        return <EditIcon />;
      case "EXTEND":
        return <ExtendIcon />;
      default:
        return <AddIcon />;
    }
  };
  // Calculate processing time
  const calculateProcessingTime = (createdOn, completedOn) => {
    if (!createdOn || !completedOn) return "N/A";
    const created = new Date(createdOn);
    const completed = new Date(completedOn);
    const diffTime = Math.abs(completed - created);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} day${diffDays !== 1 ? "s" : ""}`;
  };

  // Normalize request type
  const normalizeRequestType = (type) => {
    if (type?.includes("Extend")) return "EXTEND";
    if (type?.includes("Change")) return "CHANGE";
    if (type?.includes("Create")) return "CREATE";
    return type?.toUpperCase() || "";
  };

  return (
    <StyledTimeline>
      {(() => {
        // Check if there are any CREATE requests
        const createRequests = filteredRequests.filter(
          (request) =>
            request.requestType === REQUEST_TYPE?.CREATE ||
            request.requestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        );
        const timelineItems = [];
        // Only show external creation card on ALL and CREATE filter pages
        if (
          createRequests.length === 0 &&
          (selectedFilter === "ALL" || selectedFilter === "CREATE")
        ) {
          timelineItems.push(
            <TimelineItem key="default-creation">
              <TimelineSeparator>
                <StyledTimelineDot
                  requestType="CREATE"
                  sx={{ paddingLeft: "5px" }}
                >
                  <AddIcon />
                </StyledTimelineDot>
                <TimelineConnector />
              </TimelineSeparator>
              <StyledTimelineContent>
                <RequestCard
                  requestType="CREATE"
                  index={0}
                  animateCards={animateCards}
                >
                  <CardContent>
                    {/* Default Creation Header */}
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="flex-start"
                      mb={2}
                      flexWrap="wrap"
                      gap={1}
                    >
                      <Box display="flex" alignItems="center" gap={1.5}>
                        <Chip
                          label="EXTERNAL CREATION"
                          size="small"
                          sx={{
                            bgcolor: "#10B981",
                            color: "white",
                            fontWeight: 700,
                            fontSize: "0.75rem",
                            textTransform: "uppercase",
                            borderRadius: 2,
                          }}
                        />
                        <Typography
                          variant="h6"
                          component="span"
                          fontWeight={600}
                        >
                          {module} - {materialNumber}
                        </Typography>
                      </Box>
                      <Chip
                        label="External System"
                        color="default"
                        size="small"
                        variant="filled"
                        sx={{
                          fontWeight: 600,
                          bgcolor: "#F3F4F6",
                          color: "#6B7280",
                        }}
                      />
                    </Box>
                    {/* Default Creation Content */}
                    <Box mb={2}>
                      <Typography
                        variant="body1"
                        fontWeight={600}
                        color="text.primary"
                        gutterBottom
                      >
                        {module} created outside our system
                      </Typography>
                      <Paper
                        sx={{
                          mt: 1.5,
                          p: 2,
                          bgcolor: "grey.50",
                          border: "1px solid #E5E7EB",
                        }}
                      >
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          lineHeight={1.6}
                        >
                          This {module} was created in an external system or
                          through a different process. The creation details
                          are not available in our request history. All
                          subsequent requests shown below are modifications
                          to this externally created {module}.
                        </Typography>
                      </Paper>
                    </Box>
                    {/* Default Creation Footer */}
                    <Box>
                      <Divider sx={{ mb: 2 }} />
                      <Box
                        display="flex"
                        justifyContent="space-between"
                        alignItems="center"
                        flexWrap="wrap"
                        gap={1.5}
                      >
                        <Box display="flex" gap={3} flexWrap="wrap">
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <PersonIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Created By:
                              </strong>{" "}
                              External System
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <CalendarIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Date:
                              </strong>{" "}
                              Unknown
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <ScheduleIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Processing:
                              </strong>{" "}
                              N/A
                            </Typography>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            bgcolor: "#F3F4F6",
                            color: "#6B7280",
                            px: 2,
                            py: 0.5,
                            borderRadius: 1,
                            fontSize: "0.75rem",
                            fontWeight: 600,
                          }}
                        >
                          EXTERNAL CREATION
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </RequestCard>
              </StyledTimelineContent>
            </TimelineItem>
          );
        }
        // Add all filtered requests
        filteredRequests.forEach((request, index) => {
          const requestObject=request;
          const adjustedIndex =
            createRequests.length === 0 &&
            (selectedFilter === "ALL" || selectedFilter === "CREATE")
              ? index + 1
              : index;
          const isExpanded = expandedRequest === request.requestId;
          const isLastItem = index === filteredRequests.length - 1;
          const displayRequestType = normalizeRequestType(
            request.requestType
          );
          timelineItems.push(
            <TimelineItem key={request.requestId}>
              <TimelineSeparator>
                <StyledTimelineDot
                  requestType={displayRequestType}
                  sx={{ paddingLeft: "5px" }}
                >
                  {getRequestIcon(displayRequestType)}
                </StyledTimelineDot>
                {!isLastItem && <TimelineConnector />}
              </TimelineSeparator>
              <StyledTimelineContent>
                <RequestCard
                  requestType={displayRequestType}
                  index={adjustedIndex}
                  animateCards={animateCards}
                >
                  <CardContent>
                    {/* Request Header */}
                    <Box
                      display="flex"
                      justifyContent="space-between"
                      alignItems="flex-start"
                      mb={2}
                      flexWrap="wrap"
                      gap={1}
                    >
                      <Box display="flex" alignItems="center" gap={1.5}>
                        <Chip
                          label={displayRequestType}
                          size="small"
                          sx={{
                            bgcolor: (theme) => {
                              const colors = {
                                CREATE: theme.palette.success.main,
                                CHANGE: theme.palette.primary.main,
                                EXTEND: theme.palette.warning.main,
                              };
                              return colors[displayRequestType];
                            },
                            color: "white",
                            fontWeight: 700,
                            fontSize: "0.75rem",
                            textTransform: "uppercase",
                            height: 28,
                            borderRadius: 2,
                          }}
                        />
                        <Typography
                          variant="h6"
                          component="span"
                          fontWeight={600}
                        >
                          Parent Request Id - {request.requestId}
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1.5}>
                        <Chip
                          label={request.requestStatus}
                          color="success"
                          size="small"
                          variant="filled"
                          sx={{
                            fontWeight: 600,
                            fontSize: "0.75rem",
                            height: 32,
                            borderRadius: 2,
                            color: "white",
                          }}
                        />
                        {/* View Changelog Button - To be uncommented after the logic is done*/}
                        
                        {/* <Box
                          sx={{
                            position: "relative",
                            display: "inline-block",
                          }}
                        >
                          <Tooltip title="View Changelog">
                            <Button
                              onClick={() => {
                                setChangeLogModalOpen(true);
                                setSelectedRequestForChangelog(
                                  request
                                );
                                
                              }}
                              variant="outlined"
                              color="primary"
                              size="small"
                              sx={{
                                fontWeight: 600,
                                textTransform: "none",
                                fontSize: "0.75rem",
                                height: 28,
                                borderRadius: 2,
                                px: 1,
                                minWidth: "auto",
                                width: 32,
                                borderWidth: "2px",
                                "&:hover": {
                                  "& + .button-tooltip": {
                                    opacity: 0.75,
                                    visibility: "visible",
                                  },
                                },
                                transition: "all 0.2s ease-in-out",
                              }}
                            >
                              <TrackChangesTwoToneIcon sx={{ fontSize: "1.25rem" }} />
                            </Button>
                          </Tooltip>
                        </Box> */}
                        {/* Consolidated PDF Button with Tooltip */}
                        <Box sx={{
                            position: "relative",
                            display: "inline-block",
                          }}
                        >
                          <Tooltip title="Export Consolidated PDF">
                            <Button
                              onClick={() => {
                                handleDownloadPdf(request);
                              }}
                              variant="outlined"
                              color="primary"
                              size="small"
                              sx={{
                                fontWeight: 600,
                                textTransform: "none",
                                fontSize: "0.75rem",
                                height: 28,
                                borderRadius: 2,
                                px: 1,
                                minWidth: "auto",
                                width: 32,
                                borderWidth: "2px",
                                "&:hover": {
                                  "& + .button-tooltip": {
                                    opacity: 0.75,
                                    visibility: "visible",
                                  },
                                },
                                transition: "all 0.2s ease-in-out",
                              }}
                            >
                              <DownloadIcon sx={{ fontSize: "1.25rem" }} />
                            </Button>
                          </Tooltip>
                        </Box>
                      </Box>
                    </Box>
                    <Box mb={2} > {/* Request Content */}
                      <Typography
                        variant="body1"
                        fontWeight={600}
                        color="text.primary"
                        gutterBottom
                      >
                        Request Type : {request.requestType}
                      </Typography>
                      <Typography
                        variant="body1"
                        fontWeight={600}
                        color="text.primary"
                        gutterBottom
                      >
                        Request Description :{" "}
                        {request?.requestDescription || ""}
                      </Typography>
                      <Collapse in={isExpanded}>
                        <Paper
                          sx={{
                            mt: 1.5,
                            p: 2,
                            bgcolor: "grey.50",
                            border: (theme) => {
                              const colors = {
                                CREATE: theme.palette.success.light,
                                CHANGE: theme.palette.primary.light,
                                EXTEND: theme.palette.warning.light,
                              };
                              return `1px solid ${colors[displayRequestType]}40`;
                            },
                          }}
                        >
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            lineHeight={1.6}
                          >
                            <strong>{module} :</strong>{" "}
                            {request?.materialNumber || request?.objectNumber || ""}
                            <br />
                            <strong>{request?.secondaryFieldName || "Material Type"}:</strong>{" "}
                            {request?.secondaryFieldValue || ""}
                            <br />
                            <strong>Child Request ID:</strong>{" "}
                            {request?.childRequestId || ""}
                            <br />
                            <strong>Request Priority:</strong>{" "}
                            {request?.requestPriority || "Medium"}
                            <br />
                            <strong>Region:</strong>{" "}
                            {request?.requestRegion || "US"}
                            <br />
                            <strong>Template:</strong>{" "}
                            {request?.template || "NA"}
                            <br />
                          </Typography>
                        </Paper>
                      </Collapse>
                    </Box>
                    <Box>                    {/* Request Footer */}
                      <Divider sx={{ mb: 2 }} />
                      <Box
                        display="flex"
                        justifyContent="space-between"
                        alignItems="center"
                        flexWrap="wrap"
                        gap={1.5}
                      >
                        <Box display="flex" gap={3} flexWrap="wrap">
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <PersonIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Created By:
                              </strong>{" "}
                              {request.requestCreatedBy}
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <CalendarIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Created On:
                              </strong>{" "}
                              {new Date(
                                request.requestCreatedOn
                              ).toLocaleString()}
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <ScheduleIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Completed On:
                              </strong>{" "}
                              {new Date(
                                request.requestCompletedOn
                              ).toLocaleString()}
                            </Typography>
                          </Box>
                          <Box display="flex" alignItems="center" gap={0.5}>
                            <ScheduleIcon fontSize="small" color="action" />
                            <Typography
                              variant="body2"
                              color="text.secondary"
                            >
                              <strong
                                style={{
                                  color: theme.palette.text.primary,
                                }}
                              >
                                Processing Time:
                              </strong>{" "}
                              {calculateProcessingTime(
                                request.requestCreatedOn,
                                request.requestCompletedOn
                              )}
                            </Typography>
                          </Box>
                        </Box>
                        <Button
                          size="small"
                          endIcon={
                            isExpanded ? (
                              <ExpandLessIcon />
                            ) : (
                              <ExpandMoreIcon />
                            )
                          }
                          onClick={() =>
                            setExpandedRequest(
                              isExpanded ? null : request.requestId
                            )
                          }
                        >
                          {isExpanded
                            ? "Show Less Details"
                            : "Show More Details"}
                        </Button>
                      </Box>
                    </Box>
                  </CardContent>
                </RequestCard>
              </StyledTimelineContent>
            </TimelineItem>
          );
        });
        return timelineItems;
      })()}
    </StyledTimeline>
  );
};
export default ConsolidatedRequestTimeline;
