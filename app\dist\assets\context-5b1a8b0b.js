import{r as o,fc as Ne,ef as ee,f5 as Q,_ as ue,t as U,fi as J,f6 as V,fn as pe}from"./index-226a1e75.js";import{_ as w,f as ge}from"./EyeOutlined-6bec9589.js";import{p as Re,A as Ae,O as _e,P as Pe}from"./EditOutlined-5e4d9326.js";var Le=o.forwardRef(function(e,r){var d=e.prefixCls,c=e.style,l=e.className,g=e.duration,C=g===void 0?4.5:g,R=e.showProgress,L=e.pauseOnHover,A=L===void 0?!0:L,F=e.eventKey,D=e.content,m=e.closable,N=e.closeIcon,b=N===void 0?"x":N,y=e.props,f=e.onClick,j=e.onNoticeClose,H=e.times,x=e.hovering,k=o.useState(!1),E=w(k,2),u=E[0],O=E[1],a=o.useState(0),t=w(a,2),n=t[0],s=t[1],p=o.useState(0),_=w(p,2),S=_[0],q=_[1],v=x||u,P=C>0&&R,T=function(){j(F)},$=function(i){(i.key==="Enter"||i.code==="Enter"||i.keyCode===Ae.ENTER)&&T()};o.useEffect(function(){if(!v&&C>0){var M=Date.now()-S,i=setTimeout(function(){T()},C*1e3-S);return function(){A&&clearTimeout(i),q(Date.now()-M)}}},[C,v,H]),o.useEffect(function(){if(!v&&P&&(A||S===0)){var M=performance.now(),i,h=function ne(){cancelAnimationFrame(i),i=requestAnimationFrame(function(oe){var G=oe+S-M,K=Math.min(G/(C*1e3),1);s(K*100),K<1&&ne()})};return h(),function(){A&&cancelAnimationFrame(i)}}},[C,S,v,P,H]);var X=o.useMemo(function(){return Ne(m)==="object"&&m!==null?m:m?{closeIcon:b}:{}},[m,b]),te=Re(X,!0),z=100-(!n||n<0?0:n>100?100:n),I="".concat(d,"-notice");return o.createElement("div",ee({},y,{ref:r,className:Q(I,l,ue({},"".concat(I,"-closable"),m)),style:c,onMouseEnter:function(i){var h;O(!0),y==null||(h=y.onMouseEnter)===null||h===void 0||h.call(y,i)},onMouseLeave:function(i){var h;O(!1),y==null||(h=y.onMouseLeave)===null||h===void 0||h.call(y,i)},onClick:f}),o.createElement("div",{className:"".concat(I,"-content")},D),m&&o.createElement("a",ee({tabIndex:0,className:"".concat(I,"-close"),onKeyDown:$,"aria-label":"Close"},te,{onClick:function(i){i.preventDefault(),i.stopPropagation(),T()}}),X.closeIcon),P&&o.createElement("progress",{className:"".concat(I,"-progress"),max:"100",value:z},z+"%"))}),he=U.createContext({}),Ke=function(r){var d=r.children,c=r.classNames;return U.createElement(he.Provider,{value:{classNames:c}},d)},de=8,me=3,ye=16,be=function(r){var d={offset:de,threshold:me,gap:ye};if(r&&Ne(r)==="object"){var c,l,g;d.offset=(c=r.offset)!==null&&c!==void 0?c:de,d.threshold=(l=r.threshold)!==null&&l!==void 0?l:me,d.gap=(g=r.gap)!==null&&g!==void 0?g:ye}return[!!r,d]},Me=["className","style","classNames","styles"],we=function(r){var d=r.configList,c=r.placement,l=r.prefixCls,g=r.className,C=r.style,R=r.motion,L=r.onAllNoticeRemoved,A=r.onNoticeClose,F=r.stack,D=o.useContext(he),m=D.classNames,N=o.useRef({}),b=o.useState(null),y=w(b,2),f=y[0],j=y[1],H=o.useState([]),x=w(H,2),k=x[0],E=x[1],u=d.map(function(v){return{config:v,key:String(v.key)}}),O=be(F),a=w(O,2),t=a[0],n=a[1],s=n.offset,p=n.threshold,_=n.gap,S=t&&(k.length>0||u.length<=p),q=typeof R=="function"?R(c):R;return o.useEffect(function(){t&&k.length>1&&E(function(v){return v.filter(function(P){return u.some(function(T){var $=T.key;return P===$})})})},[k,u,t]),o.useEffect(function(){var v;if(t&&N.current[(v=u[u.length-1])===null||v===void 0?void 0:v.key]){var P;j(N.current[(P=u[u.length-1])===null||P===void 0?void 0:P.key])}},[u,t]),U.createElement(_e,ee({key:c,className:Q(l,"".concat(l,"-").concat(c),m==null?void 0:m.list,g,ue(ue({},"".concat(l,"-stack"),!!t),"".concat(l,"-stack-expanded"),S)),style:C,keys:u,motionAppear:!0},q,{onAllRemoved:function(){L(c)}}),function(v,P){var T=v.config,$=v.className,X=v.style,te=v.index,z=T,I=z.key,M=z.times,i=String(I),h=T,ne=h.className,oe=h.style,G=h.classNames,K=h.styles,ke=ge(h,Me),ae=u.findIndex(function(Z){return Z.key===i}),Y={};if(t){var B=u.length-1-(ae>-1?ae:te-1),fe=c==="top"||c==="bottom"?"-50%":"0";if(B>0){var re,se,ie;Y.height=S?(re=N.current[i])===null||re===void 0?void 0:re.offsetHeight:f==null?void 0:f.offsetHeight;for(var ve=0,ce=0;ce<B;ce++){var le;ve+=((le=N.current[u[u.length-1-ce].key])===null||le===void 0?void 0:le.offsetHeight)+_}var Se=(S?ve:B*s)*(c.startsWith("top")?1:-1),xe=!S&&f!==null&&f!==void 0&&f.offsetWidth&&(se=N.current[i])!==null&&se!==void 0&&se.offsetWidth?((f==null?void 0:f.offsetWidth)-s*2*(B<3?B:3))/((ie=N.current[i])===null||ie===void 0?void 0:ie.offsetWidth):1;Y.transform="translate3d(".concat(fe,", ").concat(Se,"px, 0) scaleX(").concat(xe,")")}else Y.transform="translate3d(".concat(fe,", 0, 0)")}return U.createElement("div",{ref:P,className:Q("".concat(l,"-notice-wrapper"),$,G==null?void 0:G.wrapper),style:J(J(J({},X),Y),K==null?void 0:K.wrapper),onMouseEnter:function(){return E(function(W){return W.includes(i)?W:[].concat(V(W),[i])})},onMouseLeave:function(){return E(function(W){return W.filter(function(Ee){return Ee!==i})})}},U.createElement(Le,ee({},ke,{ref:function(W){ae>-1?N.current[i]=W:delete N.current[i]},prefixCls:l,classNames:G,styles:K,className:Q(ne,m==null?void 0:m.notice),style:oe,times:M,key:I,eventKey:I,onNoticeClose:A,hovering:t&&k.length>0})))})},He=o.forwardRef(function(e,r){var d=e.prefixCls,c=d===void 0?"rc-notification":d,l=e.container,g=e.motion,C=e.maxCount,R=e.className,L=e.style,A=e.onAllRemoved,F=e.stack,D=e.renderNotifications,m=o.useState([]),N=w(m,2),b=N[0],y=N[1],f=function(t){var n,s=b.find(function(p){return p.key===t});s==null||(n=s.onClose)===null||n===void 0||n.call(s),y(function(p){return p.filter(function(_){return _.key!==t})})};o.useImperativeHandle(r,function(){return{open:function(t){y(function(n){var s=V(n),p=s.findIndex(function(q){return q.key===t.key}),_=J({},t);if(p>=0){var S;_.times=(((S=n[p])===null||S===void 0?void 0:S.times)||0)+1,s[p]=_}else _.times=0,s.push(_);return C>0&&s.length>C&&(s=s.slice(-C)),s})},close:function(t){f(t)},destroy:function(){y([])}}});var j=o.useState({}),H=w(j,2),x=H[0],k=H[1];o.useEffect(function(){var a={};b.forEach(function(t){var n=t.placement,s=n===void 0?"topRight":n;s&&(a[s]=a[s]||[],a[s].push(t))}),Object.keys(x).forEach(function(t){a[t]=a[t]||[]}),k(a)},[b]);var E=function(t){k(function(n){var s=J({},n),p=s[t]||[];return p.length||delete s[t],s})},u=o.useRef(!1);if(o.useEffect(function(){Object.keys(x).length>0?u.current=!0:u.current&&(A==null||A(),u.current=!1)},[x]),!l)return null;var O=Object.keys(x);return pe.createPortal(o.createElement(o.Fragment,null,O.map(function(a){var t=x[a],n=o.createElement(we,{key:a,configList:t,placement:a,prefixCls:c,className:R==null?void 0:R(a),style:L==null?void 0:L(a),motion:g,onNoticeClose:f,onAllNoticeRemoved:E,stack:F});return D?D(n,{prefixCls:c,key:a}):n})),l)}),Ie=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],De=function(){return document.body},Ce=0;function Oe(){for(var e={},r=arguments.length,d=new Array(r),c=0;c<r;c++)d[c]=arguments[c];return d.forEach(function(l){l&&Object.keys(l).forEach(function(g){var C=l[g];C!==void 0&&(e[g]=C)})}),e}function We(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=e.getContainer,d=r===void 0?De:r,c=e.motion,l=e.prefixCls,g=e.maxCount,C=e.className,R=e.style,L=e.onAllRemoved,A=e.stack,F=e.renderNotifications,D=ge(e,Ie),m=o.useState(),N=w(m,2),b=N[0],y=N[1],f=o.useRef(),j=o.createElement(He,{container:b,ref:f,prefixCls:l,motion:c,maxCount:g,className:C,style:R,onAllRemoved:L,stack:A,renderNotifications:F}),H=o.useState([]),x=w(H,2),k=x[0],E=x[1],u=Pe(function(a){var t=Oe(D,a);(t.key===null||t.key===void 0)&&(t.key="rc-notification-".concat(Ce),Ce+=1),E(function(n){return[].concat(V(n),[{type:"open",config:t}])})}),O=o.useMemo(function(){return{open:u,close:function(t){E(function(n){return[].concat(V(n),[{type:"close",key:t}])})},destroy:function(){E(function(t){return[].concat(V(t),[{type:"destroy"}])})}}},[]);return o.useEffect(function(){y(d())}),o.useEffect(function(){if(f.current&&k.length){k.forEach(function(n){switch(n.type){case"open":f.current.open(n.config);break;case"close":f.current.close(n.key);break;case"destroy":f.current.destroy();break}});var a,t;E(function(n){return(a!==n||!t)&&(a=n,t=n.filter(function(s){return!k.includes(s)})),t})}},[k]),[O,j]}const Ge=U.createContext({});export{Ge as A,Le as N,Ke as a,We as u};
