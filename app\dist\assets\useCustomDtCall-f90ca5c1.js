import{n as ce,r as p,a as ue,c as S,j as a,aa as O,T as Se,ai as pe,aj as De,al as Ee,d as $,eb as h,ec as t,Z as le,F as ae,am as Ce,an as re,ae as _,aJ as ge,C as oe,bI as ie,aT as ne}from"./index-226a1e75.js";const Re=({params:e,disabled:M=!1,handleCellEdit:R,isAdd:I=!0})=>{var L,N,P,b,w,m,Z,j,W,k,F,z,Y,G,K,q,B,H,J;const d=ce(o=>o.payload.payloadData),i=d==null?void 0:d.Region,[u,D]=p.useState(!1),[r,E]=p.useState(""),[l,x]=p.useState(""),[v,T]=p.useState(""),{t:f}=ue(),A=I?(L=e==null?void 0:e.row)==null?void 0:L.materialDescription:((N=e==null?void 0:e.row)==null?void 0:N.globalMaterialDescription)||"",he=()=>{var o;E(A.slice(0,(o=t)==null?void 0:o.US).trim()),x(A.slice(27).trim()),T(""),D(!0)},y=()=>{D(!1)},de=()=>{var C,g,U,Q,V,X,ee,te,se;if(!r.trim()){T((C=_)==null?void 0:C.MAIN_DESCR_MANDATORY);return}let o=r.toUpperCase().padEnd((g=t)==null?void 0:g.US," "),s=l.toUpperCase(),n=s?`${o} ${s}`:o.trim(),c;if(i===((U=h)==null?void 0:U.US)?c=/^[A-Z0-9\/"\-\s]*$/:i===((Q=h)==null?void 0:Q.EUR)&&(c=/^[A-Z0-9"\-\s]*$/),!c.test(n)){T(i===((V=h)==null?void 0:V.US)?(X=_)==null?void 0:X.DESCRIPTION_VALIDITY_US:(ee=_)==null?void 0:ee.DESCRIPTION_VALIDITY_EUR);return}(te=e==null?void 0:e.row)!=null&&te.id&&(I?R((se=e==null?void 0:e.row)==null?void 0:se.id,n):R({id:e.row.id,field:"globalMaterialDescription",value:n})),D(!1)};return S(ae,{children:[a(Se,{title:a("span",{style:{whiteSpace:"pre"},children:A}),arrow:!0,children:a("span",{children:a(O,{fullWidth:!0,variant:"outlined",disabled:M,size:"small",value:A,placeholder:f("ENTER MATERIAL DESCRIPTION"),onClick:he,InputProps:{readOnly:!0}})})}),S(pe,{open:u,onClose:y,maxWidth:"sm",fullWidth:!0,children:[a(De,{children:f("Enter Material Description")}),S(Ee,{children:[S($,{children:[f("Material Description")," (",i===((P=h)==null?void 0:P.EUR)?`Max ${(b=t)==null?void 0:b.EUR} Chars, Only Alphabets`:`Max ${(w=t)==null?void 0:w.US} Chars`,") ",a("span",{style:{color:(Z=(m=le)==null?void 0:m.error)==null?void 0:Z.red},children:"*"})]}),a(O,{variant:"outlined",fullWidth:!0,size:"small",placeholder:i===((j=h)==null?void 0:j.EUR)?`Enter description (Max ${(W=t)==null?void 0:W.EUR} chars)`:`Enter First Part (Max ${(k=t)==null?void 0:k.US} chars)`,value:r,onChange:o=>{var n,c,C,g,U;const s=o.target.value.toUpperCase();E(i===((n=h)==null?void 0:n.EUR)?(C=s==null?void 0:s.replace(/[^A-Z0-9"\-\s]/g,""))==null?void 0:C.slice(0,(c=t)==null?void 0:c.EUR):(U=s==null?void 0:s.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:U.slice(0,(g=t)==null?void 0:g.US))},helperText:i===((F=h)==null?void 0:F.EUR)?`${r.length}/${(z=t)==null?void 0:z.EUR} characters used (Only letters, numbers, quotes, hyphen and spaces allowed)`:`${r.length}/${(Y=t)==null?void 0:Y.US} characters used (Only letters, numbers, /, ", - and spaces allowed)`}),i===((G=h)==null?void 0:G.US)&&S(ae,{children:[a($,{sx:{marginTop:2},children:`Special Material Description (Max ${(K=t)==null?void 0:K.US_SPECIAL} Chars)`}),a(O,{variant:"outlined",fullWidth:!0,size:"small",placeholder:`Enter special Description (Max ${(q=t)==null?void 0:q.US_SPECIAL} chars)`,value:l,onChange:o=>{var n,c;const s=o.target.value.toUpperCase();x((c=s==null?void 0:s.replace(/[^A-Z0-9\/"\-\s]/g,""))==null?void 0:c.slice(0,(n=t)==null?void 0:n.US_SPECIAL))},helperText:`${l.length}/${(B=t)==null?void 0:B.US_SPECIAL} characters used (Optional)`})]}),v&&a($,{color:(J=(H=le)==null?void 0:H.error)==null?void 0:J.dark,sx:{marginTop:1},children:v})]}),S(Ce,{children:[a(re,{onClick:y,color:"secondary",children:"Cancel"}),a(re,{onClick:de,color:"primary",variant:"contained",disabled:!r.trim(),children:"Save"})]})]})]})},Ie=()=>{const e=ce(u=>u.applicationConfig),[M,R]=p.useState(null),[I,d]=p.useState(null);return{getDtCall:async(u,D="")=>{try{const r=l=>{(l==null?void 0:l.statusCode)===ge.STATUS_200&&R({data:l==null?void 0:l.data,customParam:D})},E=l=>{d(l)};e.environment==="localhost"?await oe(`/${ie}${ne.INVOKE_RULES.LOCAL}`,"post",r,E,u):await oe(`/${ie}${ne.INVOKE_RULES.PROD}`,"post",r,E,u)}catch(r){d(r)}},dtData:M,error:I}};export{Re as M,Ie as u};
