import React, { useEffect, useRef, useState } from 'react';
import {
  Box, Chip, Grid, Stack, Typography, Paper,
  styled, Button, Divider, Avatar, Fade,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  AccessTime, ArrowForwardIos, Cancel, CheckCircle, HourglassTop,
  PersonOutline,AssignmentTurnedIn
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import CommentOutlinedIcon from "@mui/icons-material/CommentOutlined";
import RequestHistorySkeleton from './ReqestSkeleton';
import ReusableSnackBar from '@components/Common/ReusableSnackBar';
import { colors } from '@constant/colors';
import TaskTimeline from '@components/RequestHistory/TaskTimeline';
import TruncatedText from './TruncatedText';

const STATUS = {
  COMPLETED: "COMPLETED",
  CANCELED: "CANCELED",
  READY: "READY",
  PENDING: "PENDING",
  RESERVED:"RESERVED"
};

const StatusBadge = styled(Chip)(({ status }) => ({
  fontWeight: 600,
  fontSize: '0.7rem',
  height: '24px',
  borderRadius: '12px',
  backgroundColor:
    status === STATUS.COMPLETED ? colors.success.completedBackground :
      status === STATUS.CANCELED ? colors.background.canceled :
        status === STATUS.READY ? colors.warning.light : status === STATUS.RESERVED ? colors.secondary.lightYellow: colors.background.subtle,
  color:
    status === STATUS.COMPLETED ? colors.success.completedDark :
      status === STATUS.CANCELED ? colors.error.deepRed :
        status === STATUS.READY ? colors.warning.orange : status === STATUS.RESERVED ? colors.secondary.yellow : colors.text.darkGrey,
  border:
    status === STATUS.COMPLETED ? `1px solid ${colors.success.pale}` :
      status === STATUS.CANCELED ? `1px solid ${colors.error.pale}` :
        status === STATUS.READY ? `1px solid ${colors.warning.orange}` : status === STATUS.READY ? `1px solid ${colors.secondary.teal}` : `1px solid ${colors.border.light}`,
  '& .MuiChip-label': {
    letterSpacing: '1.4px',
  }
}));

const ProcessCard = ({ item, index, setOpenSnackbar }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
    >
      <Paper
        elevation={0}
        sx={{
          pl: 2,
          mb: 2,
          borderRadius: '12px',
          border: '1px solid',
          borderColor:
            item.status === STATUS.COMPLETED ? colors.success.pale :
              item.status === STATUS.CANCELED ? colors.error.pale : item.status === STATUS.RESERVED ? colors.secondary.amber : colors.border.cardBorder,
          backgroundColor: colors.basic.white,
          position: 'relative',
          overflow: 'hidden',
          transition: 'all 0.3s ease-in-out',
          '&::before': {
            content: '""',
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: '5px',
            backgroundColor:
              item.status === STATUS.COMPLETED ? colors.success.deepGreen :
                item.status === STATUS.CANCELED ? colors.error.deepRed :
                  item.status === STATUS.READY ? colors.warning.orange : item.status === STATUS.RESERVED ? colors.secondary.lightAmber : colors.neutral[400],
          }
        }}
      >
        {/* Header */}

        <Box sx={{ display: 'flex', flexDirection: 'row' }}>
          <Box sx={{ width: '80%', pr: 2, pt: 2 }}>
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography
                variant="h6"
                sx={{
                  fontSize: "1rem",
                  fontWeight: 700,
                  color: colors.text.darkBlue,
                  pl: 1,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {item?.attributesDtos?.[0]?.approverGroup}
                {item?.attributesDtos?.[0]?.approverGroup &&
                <span
                  style={{
                    fontWeight: 600,
                    color: colors.primary.accent,
                    marginLeft: '4px',
                  }}
                >
                   - {item.subject}
                </span>}
              </Typography>
              <Divider orientation="vertical" flexItem sx={{ height: 20, borderColor: colors.border.cardBorder }} />
              <Stack direction="row" alignItems="center" spacing={0.5}>
                <PersonOutline fontSize="small" sx={{ color: colors.text.darkGrey, pb: '2px' }} />
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color: colors.text.greyishBlue,
                    fontSize: '0.805rem',
                  }}
                >
                  Initiated by
                </Typography>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 600,
                    color: colors.info.dark,
                    fontSize: '0.755rem',
                    letterSpacing: '0.5px',
                  }}
                >
                  {item.createdBy}
                </Typography>
              </Stack>
            </Stack>

            <Box sx={{ pt: 2 }}>
              <Grid container spacing={3} alignItems="flex-start">
                {/* Recipients */}
                <Grid item xs={12} md={4}>
                  <Typography
                    sx={{
                      fontWeight: 600,
                      color: colors.text.darkBlue,
                      fontSize: '0.755rem',
                      mb: 1,
                    }}
                  >
                    {item?.status === "READY"
                      ? "Recipient Users"
                      : item?.status === "RESERVED"
                        ? "Claimed by"
                        : item?.status === "COMPLETED"
                          ? "Completed by"
                          : "Recipient Users"
                    }
                  </Typography>
                  <Stack direction="row" flexWrap="wrap" useFlexGap gap={1}>
                    {/* For completed tasks, show the processor */}
                    {item?.status === "COMPLETED" && item?.processor ? (
                      <Tooltip title={item?.processor}>
                        <Chip
                          label={item?.processor}
                          avatar={
                            <Avatar
                              sx={{
                                width: 18,
                                height: 18,
                                bgcolor: colors.background.subtle,
                                color: colors.text.darkBlue,
                                fontSize: '0.55rem',
                              }}
                            >
                              {item?.processor?.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </Avatar>
                          }
                          sx={{
                            bgcolor: '#F9FAFB',
                            color: '#334155',
                            fontSize: '0.775rem',
                            '& .MuiChip-label': { px: 1 },
                            boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
                            border: '1px solid #E2E8F0',
                          }}
                        />
                      </Tooltip>
                    ) : (
                      /* For non-completed tasks, show recipient users */
                      <>
                        {item?.recipientUsers?.length > 1 ? (
                          <>
                            <Tooltip title={item?.recipientUsers[0]}>
                              <Chip
                                label={item?.recipientUsers[0]}
                                avatar={
                                  <Avatar
                                    sx={{
                                      width: 18,
                                      height: 18,
                                      bgcolor: colors.background.subtle,
                                      color: colors.text.darkBlue,
                                      fontSize: '0.55rem',
                                    }}
                                  >
                                    {item?.recipientUsers[0]?.split(' ').map(n => n[0]).join('').toUpperCase()}
                                  </Avatar>
                                }
                                sx={{
                                  bgcolor: '#F9FAFB',
                                  color: '#334155',
                                  fontSize: '0.775rem',
                                  '& .MuiChip-label': { px: 1 },
                                  boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
                                  border: '1px solid #E2E8F0',
                                }}
                              />
                            </Tooltip>

                            {/* +N More */}
                            <Tooltip
                              title={
                                <Stack spacing={0.5}>
                                  {item?.recipientUsers?.slice(1).map((user, i) => (
                                    <Typography key={i} variant="caption" color="inherit">
                                      {user}
                                    </Typography>
                                  ))}
                                </Stack>
                              }
                              arrow
                              placement="top"
                            >
                              <Chip
                                label={`+ ${item?.recipientUsers?.length - 1}`}
                                sx={{
                                  bgcolor: '#F9FAFB',
                                  color: '#334155',
                                  fontSize: '0.775rem',
                                  '& .MuiChip-label': { px: 1 },
                                  boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
                                  border: '1px solid #E2E8F0',
                                  cursor: 'pointer',
                                }}
                              />
                            </Tooltip>
                          </>
                        ) : (
                          // Just one recipient user
                          item?.recipientUsers?.map((user, index) => (
                            <Tooltip key={index} title={user}>
                              <Chip
                                label={user}
                                avatar={
                                  <Avatar
                                    sx={{
                                      width: 18,
                                      height: 18,
                                      bgcolor: colors.background.subtle,
                                      color: colors.text.darkBlue,
                                      fontSize: '0.55rem',
                                    }}
                                  >
                                    {user?.split(' ').map(n => n[0]).join('').toUpperCase()}
                                  </Avatar>
                                }
                                sx={{
                                  bgcolor: '#F9FAFB',
                                  color: '#334155',
                                  fontSize: '0.775rem',
                                  '& .MuiChip-label': { px: 1 },
                                  boxShadow: '0 1px 2px rgba(0,0,0,0.04)',
                                  border: '1px solid #E2E8F0',
                                }}
                              />
                            </Tooltip>
                          ))
                        )}
                      </>
                    )}
                  </Stack>
                </Grid>

                <Grid item xs={12} md={2}>
                  <Box>
                    <Typography
                      variant="caption"
                      sx={{
                        color: "#64748B",
                        fontWeight: 600,
                        fontSize: "0.75rem",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <CommentOutlinedIcon sx={{ fontSize: 14, mr: 0.5 }} />
                        Remarks
                      </Box>
                    </Typography>

                    {/* Scrollable Recipients List */}
                    <Box
                      sx={{
                        maxHeight: 120,
                        overflowY: "auto",
                        display: "flex",
                        flexWrap: "wrap",
                        gap: 0.5,
                        p: 0.5,
                        "&::-webkit-scrollbar": {
                          height: "4px",
                        },
                        "&::-webkit-scrollbar-thumb": {
                          background: colors.box.scrollBackground,
                          borderRadius: "4px",
                        },
                      }}
                    >
                      {item?.attributesDtos?.[0]?.remarks ? (
                        <TruncatedText
                          text={item?.attributesDtos?.[0]?.remarks}
                          maxChars={20}
                          variant="body2"
                          color="text.secondary"
                          sx={{ fontSize: "0.75rem" }}
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary" sx={{ fontSize: "0.75rem", fontStyle: "italic" }}>
                          No remarks available
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Grid>

                {/* Status Info - Show for completed or canceled tasks */}
                {((item?.status === STATUS.COMPLETED && item?.processor) || item?.status === STATUS.CANCELED) && (
                  <Grid item xs={12} md={6}>
                    <Typography
                      sx={{
                        fontWeight: 600,
                        color: colors.text.darkBlue,
                        fontSize: '0.755rem',
                      }}
                    >
                      Status Info
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        border: `1px solid ${colors.border.cardBorder}`,
                        borderRadius: '10px',
                        p: 2,
                        bgcolor: colors.chip.background,
                        gap: 2,
                      }}
                    >
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor:
                            item.status === STATUS.COMPLETED
                              ? "#D1FAE5"
                              : item.status === STATUS.CANCELED
                                ? "#FEE2E2"
                                : "#E2E8F0",
                          color:
                            item.status === STATUS.COMPLETED
                              ? "#059669"
                              : item.status === STATUS.CANCELED
                                ? "#DC2626"
                                : "#64748B",
                        }}
                      >
                        {item?.status === STATUS.COMPLETED ? (
                          <CheckCircle fontSize="small" />
                        ) : (
                          <Cancel fontSize="small" />
                        )}
                      </Box>
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: 500, color: '#334155', mb: 0.5 }}
                        >
                          {item.status === STATUS.COMPLETED ? (
                            <>
                              Completed by{' '}
                              <Box component="span" sx={{ color: '#1D4ED8', fontWeight: 500 }}>
                                {item.processor}
                              </Box>
                            </>
                          ) : item.status === STATUS.CANCELED ? (
                            <>
                              Canceled
                            </>
                          ) : ''}
                        </Typography>

                        <Typography
                          variant="caption"
                          sx={{ color: '#64748B', fontSize: '0.75rem' }}
                        >
                          {new Date(item.completedAt || item.createdAt).toLocaleString('en-US', {
                            dateStyle: 'medium',
                            timeStyle: 'short',
                          })}
                        </Typography>
                      </Box>
                    </Box>
                  </Grid>
                )}

                {/* In Progress Badge */}
                {item?.status === STATUS.READY && (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        py: 1.5,
                        px: 2.5,
                        borderRadius: '10px',
                        backgroundColor: colors.warning.pale,
                        border: `1px dashed ${colors.warning.orange}`,
                        color: `${colors.warning.orange} !important`,
                        fontSize: '0.9375rem',
                        letterSpacing: '0.5px',
                        fontWeight: 500,
                        mt: 1,
                        mb: 1
                      }}
                    >
                      <AccessTime fontSize="small" sx={{ mr: 1.5 }} />
                      In Progress - Awaiting Completion
                    </Box>
                  </Grid>
                )}
              </Grid>
            </Box>
          </Box>

          <Box sx={{ width: '20%', pl: 2 }}>
            <TaskTimeline item={item} />
          </Box>
        </Box>
      </Paper>
    </motion.div>
  );
};

const transformTaskData = async (apiData) => {
  if (!apiData) return [];

  const taskData = [];
  let levelOrderMap = {};

  if (Array.isArray(apiData)) {
    // New format: array of objects where each object has a single key
    apiData.forEach((item, index) => {
      const levelKey = Object.keys(item)[0];
      levelOrderMap[levelKey] = index;

      const levelTasks = item[levelKey] || [];
      levelTasks.forEach(task => {
        const level = task?.attributesDtos?.[0]?.currentLevel;
        const levelName = task?.attributesDtos?.[0]?.currentLevelName || levelKey;
        let status = task?.status || STATUS.PENDING;

        taskData.push({
          id: task.id,
          level: parseInt(level, 10),
          levelName: levelName,
          status: status,
          subject: task.subject?.replace(/ & \d+$/, '') || '',
          createdBy: task.createdBy,
          createdAt: task.createdAt,
          performedBy: task.processor,
          completedAt: task.completedAt,
          comment: task.description,
          recipients: task.recipientUsers || [],
          originalLevelKey: levelKey,
          displayOrder: index
        });
      });
    });
  } else if (apiData && typeof apiData === 'object') {
    // Old format: single object with level keys
    const levelKeys = Object.keys(apiData);
    levelKeys.forEach((key, index) => {
      levelOrderMap[key] = index;
    });

    levelKeys.forEach(levelKey => {
      const levelTasks = apiData[levelKey] || [];
      levelTasks.forEach(task => {
        const level = task?.attributesDtos?.[0]?.currentLevel;
        const levelName = task?.attributesDtos?.[0]?.currentLevelName || levelKey;
        let status = task?.status || STATUS.PENDING;

        taskData.push({
          id: task.id,
          level: parseInt(level, 10),
          levelName: levelName,
          status: status,
          subject: task.subject?.replace(/ & \d+$/, '') || '',
          createdBy: task.createdBy,
          createdAt: task.createdAt,
          performedBy: task.processor,
          completedAt: task.completedAt,
          comment: task.description,
          recipients: task.recipientUsers || [],
          originalLevelKey: levelKey,
          displayOrder: levelOrderMap[levelKey]
        });
      });
    });
  }

  return taskData;
};

const getLevelCompletedCount = (levelItems => {
  if (!levelItems || levelItems.length === 0) return 0;
  return levelItems.filter(item => item.status !== STATUS.READY && item.status !== STATUS.PENDING && item.status !== STATUS.RESERVED).length;

})

const getLevelStatus = (levelItems) => {
  if (!levelItems || levelItems?.length === 0) return STATUS.PENDING;

  const allCanceled = levelItems?.every(item => item.status === STATUS.CANCELED);
  if (allCanceled) return STATUS.CANCELED;

  const anyInProgress = levelItems?.some(item => item.status === STATUS.READY);
  if (anyInProgress) return STATUS.READY;

  const allCompleted = levelItems?.every(item => item.status === STATUS.COMPLETED);
  if (allCompleted) return STATUS.COMPLETED;

  const allReserved = levelItems?.every(item => item.status === STATUS.RESERVED);
  if (allReserved) return STATUS.RESERVED;

  return STATUS.PENDING;
};



const ActivityTimeline = ({ data,childRequestID }) => {
  const [activityLogData, setActivityLogData] = useState([]);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [loader, setLoader] = useState(true);

  useEffect(() => {
    if (activityLogData?.length == 0) {
      setLoader(true);
    } else {
      setLoader(false);
    }

  }, [activityLogData])

  const getLevelsFromApiData = () => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    const result = data?.map((levelObj, index) => {
      const [levelName, tasks] = Object.entries(levelObj)[0];
      return {
        levelName,
        items: Array.isArray(tasks) ? tasks : [],
        status: getLevelStatus(Array.isArray(tasks) ? tasks : []),
        completedCount: getLevelCompletedCount(Array.isArray(tasks) ? tasks : []),
        totalCount: Array.isArray(tasks) ? tasks.length : 0,
        originalLevelNumber: index,
        displayOrder: index
      };
    });
    return result;
  };

  const levels = getLevelsFromApiData()
  if (data && Object.keys(data).length > 0) {
    const apiKeysOrder = Object.keys(data);
    const levelOrderMap = {};
    apiKeysOrder.forEach((key, index) => {
      const levelNum = parseInt(key, 10);
      levelOrderMap[levelNum] = index;
    });
    levels.sort((a, b) => {
      const orderA = levelOrderMap[a.originalLevelNumber] !== undefined ?
        levelOrderMap[a.originalLevelNumber] : 999;
      const orderB = levelOrderMap[b.originalLevelNumber] !== undefined ?
        levelOrderMap[b.originalLevelNumber] : 999;
      return orderA - orderB;
    });
  }
  const [selectedLevel, setSelectedLevel] = useState(null);

  useEffect(() => {
    if (levels?.length && selectedLevel === null) {
      setSelectedLevel(levels[levels.length - 1]?.originalLevelNumber);
    }
  }, [levels, selectedLevel]);
  const levelRefs = useRef({});


  useEffect(() => {
    const transform = async () => {
      const transformed = await transformTaskData(data);
      setActivityLogData(transformed);
    };
    transform();
  }, [data]);

  const handleLevelSelect = (levelNumber) => {
    setSelectedLevel(levelNumber);
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case STATUS.COMPLETED: return "Completed";
      case STATUS.CANCELED: return "Canceled";
      case STATUS.READY: return "Ready";
      case STATUS.RESERVED: return "Reserved";
      default: return "Pending";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case STATUS.COMPLETED: return <CheckCircle fontSize="small" sx={{ color: colors.success.deepGreen }} />;
      case STATUS.CANCELED: return <Cancel fontSize="small" sx={{ color: colors.error.deepRed }} />;
      case STATUS.READY: return <AccessTime fontSize="small" sx={{ color: colors.warning.orange }} />;
      case STATUS.RESERVED: return <AssignmentTurnedIn fontSize="small" sx={{ color: colors.secondary.yellow }} />;
      default: return <HourglassTop fontSize="small" sx={{ color: colors.text.darkGrey }} />;
    }
  };

  // Get the currently selected level
  const currentLevel = levels.find(level => level.originalLevelNumber === selectedLevel);
  useEffect(() => {
    if (selectedLevel && levelRefs.current[selectedLevel]) {
      levelRefs.current[selectedLevel].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }, [selectedLevel]);

  if (loader === true) {
    return <RequestHistorySkeleton />
  }

  return (
    <Box
      sx={{
        pt: 2,
        height: '78vh',
        overflow: 'hidden !important',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
      }}
    >
      <Grid container >
        <Grid container sx={{
          border: '1px solid #E2E8F0',
          overflow: 'hidden',
          height: '100%',
          borderRadius: 2,
          position: 'relative',
          backgroundColor: '#FFFFFF'
        }}>
          <Grid item xs={12} md={4} lg={2}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                overflow: 'hidden',
                height: '100%',
                backgroundColor: '#FFFFFF'
              }}
            >

              <Box
                sx={{
                  p: 2,
                  borderRight: '1px solid #E2E8F0',
                  borderRadius: 2,
                  bgcolor: '#F8FAFC',
                  height: '73vh',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  '&::-webkit-scrollbar': {
                    width: '3.5px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#CBD5E1',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#F1F5F9',
                  },
                }}
              >  {levels?.length > 0 && levels.map((level, index) => {
                const isSelected = selectedLevel === level.originalLevelNumber;
                const isPrevCompleted = index > 0 && levels[index - 1].status === STATUS.COMPLETED;
                const isActive =
                  level.status === STATUS.READY ||
                  (level.status === STATUS.PENDING && (index === 0 || isPrevCompleted));

                return (
                  <Box
                    key={level.originalLevelNumber}
                    ref={el => levelRefs.current[level.originalLevelNumber] = el}
                    sx={{
                      mb: 2,
                      position: 'relative'
                    }}
                  >
                    <Box
                      onClick={() => handleLevelSelect(level.originalLevelNumber)}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        borderRadius: 2,
                        px: 2,
                        py: 2,
                        height: 'auto',
                        bgcolor:
                          level.status === STATUS.COMPLETED
                            ? isSelected ? colors.success.completedBackground : colors.success.pale
                            : level.status === STATUS.CANCELED
                              ? isSelected ? colors.background.canceled : colors.error.pale
                              : level.status === STATUS.READY
                                ? isSelected ? colors.warning.light : colors.warning.pale
                                : level.status === STATUS.RESERVED ? isSelected ? colors.secondary.lightYellow : colors.secondary.lightYellow
                                : colors.background.subtle,
                        border: isSelected ? '2px solid' : '1px solid',
                        borderColor:
                          level.status === STATUS.COMPLETED
                            ? colors.success.deepGreen
                            : level.status === STATUS.CANCELED
                              ? colors.error.deepRed
                              : level.status === STATUS.READY
                                ? colors.warning.orange
                                : level.status === STATUS.RESERVED ?  colors.secondary.yellow : colors.border.cardBorder,
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        boxShadow: isSelected ? '0 0 0 2px rgba(59, 130, 246, 0.2)' : 'none',
                        position: 'relative', // Add position relative for absolute positioning of the selection icon
                        '&:hover': {
                                    bgcolor:
                                      level.status === STATUS.COMPLETED
                                        ? "#D1FAE5" // Slightly darker green on hover
                                        : level.status === STATUS.CANCELED
                                          ? "#FEE2E2" // Slightly darker red on hover
                                          : level.status === STATUS.READY
                                            ? "#FFEDD5" // Slightly darker orange on hover
                                            : level.status === STATUS.RESERVED
                                              ? "#FEF9C3" // Slightly darker blue on hover (suggested for "reserved")
                                              : "#E2E8F0", // Slightly darker grey on hover
                                  },
                      }}
                    >
                      {/* Circle Icon */}
                      <Box
                        sx={{
                          width: 40,
                          height: 40,
                          borderRadius: '50%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center',
                          mr: 2,
                          bgcolor:
                            level.status === STATUS.COMPLETED
                              ? "#ECFDF5"
                              : level.status === STATUS.CANCELED
                                ? "#FEF2F2"
                                : level.status === STATUS.READY
                                  ? "#FFF7ED"
                                  : "#F1F5F9",
                          border: '2px solid',
                          borderColor:
                            level.status === STATUS.COMPLETED
                              ? "#10B981"
                              : level.status === STATUS.CANCELED
                                ? "#EF4444"
                                : level.status === STATUS.READY
                                  ? "orange"
                                  : "#CBD5E1",
                          color:
                            level.status === STATUS.COMPLETED
                              ? "#059669"
                              : level.status === STATUS.CANCELED
                                ? "#DC2626"
                                : level.status === STATUS.READY
                                  ? "orange"
                                  : "#64748B",
                          boxShadow: isSelected ? '0 0 0 2px rgba(59, 130, 246, 0.15)' : 'none',
                          transform: isSelected ? 'scale(1.1)' : 'scale(1)',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {level.status === STATUS.COMPLETED ? (
                          <CheckCircle fontSize="small" />
                        ) : level.status === STATUS.CANCELED ? (
                          <Cancel fontSize="small" />
                        ) : (
                          getStatusIcon(level.status)
                        )}
                      </Box>

                      {/* Text Info */}
                      <Box sx={{ flex: 1 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 600,
                            color: '#334155',
                            fontSize: '0.95rem',
                          }}
                        >
                          {level?.levelName || 'undefined'}
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center' }}>

                        </Box>


                        <Box sx={{ mt: 0.7, height: '30px' }}>
                          <LinearProgress
                            key={index}
                            variant="determinate"
                            value={(level.completedCount / level.totalCount) * 100}
                            sx={{

                              borderRadius: 6,
                              height: 0.07,
                              backgroundColor: '#e6e0d3',
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: '#3B82F6',

                              },
                            }}
                          />
                          <Typography variant="caption" sx={{ mt: 0.5, color: '#475569', }}>
                            {level.completedCount} / {level.totalCount} completed
                          </Typography>
                        </Box>

                      </Box>
                      {isSelected && (
                        <Box
                          sx={{
                            position: 'absolute',
                            right: 12,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 24,
                            height: 24,
                            borderRadius: '50%',
                            bgcolor: colors.primary.main,
                            color: colors.basic.white,
                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                            animation: 'fadeIn 0.3s ease-in-out',
                            '@keyframes fadeIn': {
                              '0%': { opacity: 0, transform: 'scale(0.8)' },
                              '100%': { opacity: 1, transform: 'scale(1)' }
                            }
                          }}
                        >
                          <ArrowForwardIos fontSize="small" sx={{ fontSize: 12 }} />
                        </Box>
                      )}
                    </Box>
                    {/* Vertical line between levels */}
                    {index < levels.length - 1 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 35,
                          height: 37,
                          top: 96,
                          bottom: -18,
                          width: 2,
                          bgcolor: level.status === STATUS.COMPLETED ? colors.success.deepGreen : colors.border.cardBorder,
                          zIndex: 0
                        }}
                      />
                    )}
                  </Box>

                );
              })}
              </Box>

            </Paper>
          </Grid>

          <Grid item xs={12} md={8} lg={10}>
            <Fade in={true} timeout={300}>
              <Paper
                elevation={0}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                  overflow: 'hidden',
                }}
              >
                {currentLevel && (
                  <>
                    {/* Fixed Header */}
                    <Box
                      sx={{
                        p: 3,
                        borderBottom: '1px solid #F1F5F9',
                        bgcolor: '#F8FAFC',
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        flexShrink: 0
                      }}
                    >
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="h6" sx={{ fontWeight: 600, color: '#334155' }}>
                            {currentLevel?.levelName || 'undefined'}
                          </Typography>

                          <StatusBadge
                            label={getStatusLabel(currentLevel.status)}
                            status={currentLevel.status}
                            size="small"
                            sx={{ ml: 2 }}
                          />
                        </Box>

                        <Typography variant="body2" sx={{ mt: 1, color: '#64748B' }}>
                          {currentLevel.items.length} {currentLevel.items.length === 1 ? 'activity' : 'activities'}
                        </Typography>
                      </Box>

                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {levels.length > 1 && levels.findIndex(level => level.originalLevelNumber === selectedLevel) > 0 && (
                          <Button
                            size="small"
                            onClick={() => {
                              const currentIndex = levels.findIndex(level => level.originalLevelNumber === selectedLevel);
                              if (currentIndex > 0) {
                                handleLevelSelect(levels[currentIndex - 1].originalLevelNumber);
                              }
                            }}
                            variant="outlined"
                            sx={{
                              mr: 1,
                              borderColor: colors.primary.main,
                              color: colors.primary.main,
                              '&:hover': {
                                borderColor: colors.primary.dark,
                                bgcolor: colors.primary.light,
                              }
                            }}
                          >
                            Previous Level
                          </Button>
                        )}

                        {levels.length > 1 &&
                          levels.findIndex(level => level.originalLevelNumber === selectedLevel) < levels.length - 1 && (
                            <Button
                              size="small"
                              onClick={() => {
                                const currentIndex = levels.findIndex(level => level.originalLevelNumber === selectedLevel);
                                if (currentIndex < levels.length - 1) {
                                  handleLevelSelect(levels[currentIndex + 1].originalLevelNumber);
                                }
                              }}
                              variant="contained"
                              sx={{
                                bgcolor: '#3B82F6',
                                '&:hover': {
                                  bgcolor: '#2563EB'
                                }
                              }}
                            >
                              Next Level
                            </Button>
                          )}
                      </Box>
                    </Box>

                    {/* Scrollable Content Area */}
                    <Box
                      key={currentLevel.levelNumber}
                      sx={{
                        p: 3,
                        flexGrow: 1,
                        height: '70px',
                        overflowY: 'auto'
                      }}
                    >
                      {currentLevel.items.map((item, index) => (
                        <ProcessCard key={item.id} item={item} index={index} setOpenSnackbar={setOpenSnackbar} />
                      ))}

                      {currentLevel.items.length === 0 && (
                        <Box
                          sx={{
                            textAlign: 'center',
                            py: 8,
                            color: '#94A3B8'
                          }}
                        >
                          <Typography variant="body1">
                            No activities found for this level
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </>
                )}
              </Paper>

            </Fade>
          </Grid>
        </Grid>
      </Grid>
      <ReusableSnackBar
        openSnackBar={openSnackbar}
        alertMsg={'Task ID Copied Successfully'}
        alertType={'Success'}
        handleSnackBarClose={() => setOpenSnackbar(false)}
      />
    </Box>
  );
};

export default ActivityTimeline;
