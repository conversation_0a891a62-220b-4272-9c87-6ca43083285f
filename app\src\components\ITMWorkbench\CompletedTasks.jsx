import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import WorkspaceComponent from "@cw/cherrywork-iwm-workspace/Workspace";
import { userPermissions } from "../../data/propData";
import { APP_END_POINTS } from "@constant/appEndPoints";
import { useNavigate } from "react-router-dom";
import { baseUrl_ITMJava } from "../../data/baseUrl";
import { doAjax } from "../Common/fetchService";
import { destination_IWA_NPI } from "../../destinationVariables";
import configData from "../../data/configData";
import { commonSearchBarUpdate } from "@app/commonSearchBarSlice";
import { setLocalStorage } from "@helper/helper";
import { LOCAL_STORAGE_KEYS } from "@constant/enum";

export default function CompletedTasks() {
    let userData = useSelector((state) => state.userManagement?.userData);
    const applicationConfig = useSelector((state) => state.applicationConfig);
    const [userList, setUserList] = useState(null);
    let dispatch = useDispatch()
    const navigate = useNavigate();
    const [userRawData, setUserRawData] = useState(null);
    const [userGroupRawData, setUserGroupRawData] = useState(null);

   const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
      }, 
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };



    const onTaskClick = (task) => {
      const requestid = task?.ATTRIBUTE_1 || task?.requestId
      let row = {
        childRequestIds:requestid
      }
      setLocalStorage(LOCAL_STORAGE_KEYS.REQUEST_BENCH_TASK, row);
      dispatch(
        commonSearchBarUpdate({
          module: "RequestHistory",
          filterData: {
            reqId: requestid,
          },
        })
      );
      navigate(APP_END_POINTS.REQUEST_HISTORY,{state: {
        requestId: requestid,
        module: task?.processDisplayName, 
      },});
    }
            
    const fetchFilterViewList = () => {
        console.log("fetchFilterView")
    }
    const clearFilterView = () => {
        console.log("clearFilterView")
    }
    const fetchUserRawData = () => {
        doAjax(
            `/${destination_IWA_NPI}/api/v1/usersMDG/getUsersMDG`,
            "get",
            (resData) => {
                var tempData = resData.data;
                var tempUserData = tempData?.map((udata) => {
                    return { ...udata, userId: udata?.emailId };
                });
                var finalData = { ...resData, data: tempUserData };
                setUserRawData(finalData);
            }
        );
    };

    const fetchUserGroupRawData = () => {
        doAjax(`/${destination_IWA_NPI}/api/v1/groupsMDG/getAllGroupsMDG`, "get", (resData) => {
            var tempData = resData.data;
            var tempGroupData = tempData?.map((gData) => {
                return { ...gData, groupName: gData?.name };
            });
            var finalData = { ...resData, data: tempGroupData };
            setUserGroupRawData(finalData);
        });
    };

    const onActionComplete = (successFlag, taskPayload) => {
        console.log("Success flag.", successFlag);
        console.log("Task Payload.", taskPayload);
    };


    useEffect(() => {
        fetchUserRawData();
        fetchUserGroupRawData();
    }, []);
    return (
        <div
            style={{ width: "calc(100vw - 105px)", height: "calc(100vh-130px)" }}
            className={"workspaceOverride"}
        >
            {/* {userRawData && userGroupRawData && ( */}
                <>
                    <WorkspaceComponent
                        token={"********************************************************************************************************************************************************************************************************************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aSH3V0LAja0fT0nDxjQukU2kgLEE-WkmelwCHj2UOhzuhuO7ZAJJmlzKPtb7SRxiAqpNozbHPuaYDkjfl-gsOPCr45RKGToKtN4D0UafHCySeMdAZIRyCkKvXhzY0RXr6YWVHAUePgKFDRWINCPP78BDNp1LCX1PgxP5XtrDYg6F_CcaR0qa1I2YXvueRr2SMyhphztpJbwZF8hanV18c6B0gS0AcKCNhQIimXaClaTnIdU93pUgUX3OeDS9IRcw1rIaqMNiePcRYyfNU-hn1gT9AgHriXNDo2PcOkD-IJ9txjsg8fmbs0m0-13xQc75Sfv0qncfoHTJXiR9xYQzGA"}
                        configData={configData} 
                        destinationData={DestinationConfig}
                        userData={{ ...userData, user_id: userData?.emailId }}
                        userPermissions={userPermissions}
                        userList={userList}
                        groupList={{}} 
                        useWorkAccess={
                          applicationConfig.environment === "localhost" ? true : false
                        } 
                        useConfigServerDestination={
                          applicationConfig.environment === "localhost" ? true : false
                        }
                        inboxTypeKey={"MY_COMPLETED_TASKS"} 
                        workspaceLabel={"Completed Tasks"}
                        subInboxTypeKey={null}
                        onTaskClick={onTaskClick}
                        onActionComplete={onActionComplete}
                        workspaceFiltersByAPIDriven={false}
                        isFilterView={false}
                        savedFilterViewData={[]}
                        selectedFilterView={null}
                        fetchFilterViewList={fetchFilterViewList}
                        clearFilterView={clearFilterView}
                        cachingBaseUrl={
                            baseUrl_ITMJava
                        }
                        selectedTabId={null}
                        externalSystems={[]}
                    />
                </>
                {/* )} */}
        </div>
    );
}
