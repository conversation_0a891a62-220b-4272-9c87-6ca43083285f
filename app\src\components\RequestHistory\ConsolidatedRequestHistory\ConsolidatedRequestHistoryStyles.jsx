import { styled, keyframes } from "@mui/material/styles";
import {
  Timeline,
  TimelineContent,
  TimelineDot,
} from "@mui/lab";
import {
  <PERSON>,
  Card,
  Chip,
  Button,
} from "@mui/material";
import { Download as DownloadIcon } from "@mui/icons-material";


const fadeInUp = keyframes`
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

// FIXED: Perfect alignment with container width
export const StyledTimeline = styled(Timeline)(({ theme }) => ({
  width: "100%",
  maxWidth: "100%",
  margin: 0,
  padding: 0,
  "& .MuiTimelineItem-root": {
    minHeight: "auto",
    width: "100%",
    "&:before": {
      content: "none", // Remove default padding
    },
  },
  "& .MuiTimelineSeparator-root": {
    "& .MuiTimelineConnector-root": {
      background:
        "linear-gradient(135deg, rgb(137, 155, 233) 0%, rgb(171, 114, 228) 100%)",
      width: "3px",
    },
  },
  // FIXED: Ensure perfect alignment with zero padding
  "& .MuiTimelineContent-root": {
    flex: 1,
    padding: "0 !important",
    margin: "0 !important",
    width: "100%",
    maxWidth: "100%",
  },
}));

export const StyledTimelineDot = styled(TimelineDot)(({ requestType }) => {
  const getColors = (type) => {
    const colorMap = {
      CREATE: { primary: "#10B981", secondary: "#D1FAE5" },
      CHANGE: { primary: "#3B82F6", secondary: "#DBEAFE" },
      EXTEND: { primary: "#F59E0B", secondary: "#FEF3C7" },
    };
    return colorMap[type] || colorMap.CREATE;
  };

  const colors = getColors(requestType);

  return {
    backgroundColor: colors.primary,
    border: `3px solid white`,
    boxShadow: `0 0 0 3px ${colors.secondary}`,
    width: 40,
    height: 40,
    margin: 0,
  };
});

// FIXED: Zero padding timeline content
export const StyledTimelineContent = styled(TimelineContent)(({ theme }) => ({
  padding: "0 !important",
  margin: "0 !important",
  flex: 1,
  width: "100%",
  maxWidth: "100%",
  "&.MuiTimelineContent-root": {
    padding: "0 !important",
    margin: "0 !important",
  },
}));

export const FilterChip = styled(Chip)(({ theme, selected, requestType }) => {
  const getColors = (type) => {
    const colorMap = {
      CREATE: "#10B981",
      CHANGE: "#3B82F6",
      EXTEND: "#F59E0B",
      ALL: "#6B7280",
    };
    return colorMap[type] || colorMap.ALL;
  };

  return {
    transition: "all 0.2s ease",
    "&:hover": {
      transform: "scale(1.05)",
    },
    ...(selected && {
      backgroundColor: getColors(requestType),
      color: "white",
      "&:hover": {
        backgroundColor: getColors(requestType),
      },
    }),
  };
});

export const FloatingBackground = styled(Box)({
  position: "fixed", // Changed from "absolute" to "fixed"
  top: "30%",
  left: 0,
  right: 0,
  bottom: 0,
  width: "100vw", // Explicit viewport width
  height: "100vh", // Explicit viewport height
  overflow: "hidden",
  pointerEvents: "none",
  zIndex: 0,
  "& svg": {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
});

// FIXED: Perfect RequestCard alignment with container
export const RequestCard = styled(Card)(
  ({ theme, requestType, index, animateCards }) => {
    const colors = {
      CREATE: theme.palette.success.light,
      CHANGE: theme.palette.primary.light,
      EXTEND: theme.palette.warning.light,
    };

    return {
      border: `2px solid ${colors[requestType]}20`,
      borderRadius: theme.spacing(1.5),
      transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
      animation: animateCards
        ? `${fadeInUp} 0.6s ease-out ${index * 0.1}s both`
        : "none",
      // FIXED: Perfect width alignment with spacing for timeline dot
      width: "calc(100% - 16px)",
      maxWidth: "calc(100% - 16px)",
      marginLeft: theme.spacing(2), // Space from timeline dot
      marginBottom: theme.spacing(3),
    };
  }
);

export const StyledDownloadButton = ({
  onClick,
  text = "Download Excel",
  disabled = false,
  variant = "contained",
  fileExtension = "",
  ...props
}) => {
  return (
    <Button
      variant={variant}
      disabled={disabled}
      startIcon={
        <DownloadIcon
          sx={{
            fontSize: 28,
            animation: "downloadBounce 2s ease-in-out infinite",
            filter: "drop-shadow(0 2px 4px rgba(255,255,255,0.3))",
          }}
        />
      }
      onClick={onClick}
      sx={{
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        position: "relative",
        overflow: "hidden",
        "&::before": {
          content: '""',
          position: "absolute",
          top: 0,
          left: "-100%",
          width: "100%",
          height: "100%",
          background:
            "linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)",
          transition: "left 0.5s",
        },
        "&:hover::before": {
          left: "100%",
        },
        "&:hover": {
          background: "linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)",
          transform: "translateY(-3px) scale(1.02)",
          boxShadow:
            "0 12px 25px rgba(102, 126, 234, 0.4), 0 0 20px rgba(118, 75, 162, 0.3)",
        },
        "&:active": {
          transform: "translateY(-1px) scale(0.98)",
          boxShadow: "0 6px 15px rgba(102, 126, 234, 0.3)",
        },
        "&:disabled": {
          background: "linear-gradient(135deg, #cccccc 0%, #999999 100%)",
          color: "#666666",
          transform: "none",
          boxShadow: "none",
        },
        transition: "all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275)",
        borderRadius: 3,
        py: 1.8,
        px: 3,
        textTransform: "none",
        fontSize: "1.1rem",
        fontWeight: 600,
        boxShadow:
          "0 8px 20px rgba(102, 126, 234, 0.3), 0 0 15px rgba(118, 75, 162, 0.2)",
        display: "flex",
        alignItems: "center",
        gap: 1.5,
        border: "1px solid rgba(255, 255, 255, 0.1)",
        backdropFilter: "blur(10px)",
        color: "#ffffff",
        letterSpacing: "0.5px",
        minWidth: "180px",
        "@keyframes downloadBounce": {
          "0%, 100%": {
            transform: "translateY(0) rotate(0deg)",
            filter: "drop-shadow(0 2px 4px rgba(255,255,255,0.3))",
          },
          "25%": {
            transform: "translateY(-3px) rotate(-2deg)",
            filter: "drop-shadow(0 4px 8px rgba(255,255,255,0.4))",
          },
          "50%": {
            transform: "translateY(-6px) rotate(0deg)",
            filter: "drop-shadow(0 6px 12px rgba(255,255,255,0.5))",
          },
          "75%": {
            transform: "translateY(-3px) rotate(2deg)",
            filter: "drop-shadow(0 4px 8px rgba(255,255,255,0.4))",
          },
        },
        "&::after": {
          content: '""',
          position: "absolute",
          top: "50%",
          left: "50%",
          width: "0",
          height: "0",
          background:
            "radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%)",
          borderRadius: "50%",
          transform: "translate(-50%, -50%)",
          transition: "width 0.6s, height 0.6s",
          pointerEvents: "none",
        },
        "&:active::after": {
          width: "300px",
          height: "300px",
        },
        ...props.sx, // Allow custom sx overrides
      }}
      {...props}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 1,
          position: "relative",
          zIndex: 1,
        }}
      >
        {text}
        {fileExtension && (
          <Box
            component="span"
            sx={{
              fontSize: "0.8rem",
              opacity: 0.8,
              fontWeight: 400,
              ml: 0.5,
            }}
          >
            ({fileExtension})
          </Box>
        )}
      </Box>
    </Button>
  );
};
