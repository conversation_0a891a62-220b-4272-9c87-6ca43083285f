import React from "react";
import { Route } from "react-router-dom";
const Dashboard = React.lazy(() => import("../../components/Dashboard/Dashboard"));
const Home = React.lazy(() => import("../../components/BroadcastManagement/Home"));
const Document = React.lazy(() => import("../../components/DocumentManagement/Document"));
const Version = React.lazy(() => import("@components/Common/ui/Version"))
export const CommonRoutes = [
  <Route path="/dashboard" element={<Dashboard />} />, 
  <Route path="/" element={<Home />} />, 
  <Route path="/documentManagement" element={<Document />} />,
  <Route path="/version" element={<Version />} />
];