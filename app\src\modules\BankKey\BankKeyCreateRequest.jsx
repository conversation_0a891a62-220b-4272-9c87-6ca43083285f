//COMMENTED CODES WILL BE NEEDED AND WILL REMOVE THEM IN THE FUTURE
import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  Grid,
  Icon<PERSON><PERSON>on,
  Step,
  StepButton,
  Stepper,
  Typography,
} from "@mui/material";
import React, { useEffect, useMemo, useRef, useState } from "react";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { ArrowCircleLeftOutlined } from "@mui/icons-material";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import useLang from "@hooks/useLang";
import { APP_END_POINTS } from "@constant/appEndPoints";
import CustomDialog from "@components/Common/ui/CustomDialog";
import { WarningOutlined } from "@mui/icons-material";
import {
  button_Outlined,
  button_Primary,
} from "@components/common/commonStyles";
import { colors } from "@constant/colors";
import {
  API_CODE,
  ARTIFACTNAMES,
  BK_HEADER_MANDATORY,
  DASHBOARD_REPORT_LABELS,
  DECISION_TABLE_NAME,
  DIALOUGE_BOX_MESSAGES,
  FAILURE_DIALOG_MESSAGE,
  LOADING_MESSAGE,
  LOCAL_STORAGE_KEYS,
  MODULE_MAP,
  REGION,
  REQUEST_STATUS,
  REQUEST_TYPE,
  SUCCESS_DIALOG_MESSAGE,
  TASK_NAME,
} from "@constant/enum";
import {
  setHeaderFieldsBnky,
  setTabValue,
  setDropDownDataBNKY,
  setOdataApiCall,
  setRequestHeaderPayloadData,
  resetPayloadData,
  resetValidationStatus,
  resetBankKeyStateBk,
  setBankKeyPayload,
  setSelectedRowID,
  setRowsHeaderData
} from "./bnkySlice";
import { clearCreateChangeLogData, setCreateChangeLogDataBK, setCreatePayloadCopyForChangeLog } from "@app/changeLogReducer"
import { setRequestHeader } from "@app/requestDataSlice";
import { setDynamicKeyValue } from "@app/payloadSlice";
import RequestHeaderBankKey from "./RequestHeaderBankKey";
import useGenericDtCall from "@hooks/useGenericDtCall";
import BankKeyListDetails from "./BankKeyListDetails";
import { doAjax } from "@components/Common/fetchService";
import { destination_BankKey, destination_BOM, destination_IDM } from "../../destinationVariables";
import { appendPrefixByJavaKey, clearLocalStorageItem, filterButtonsBasedOnTab, filterButtonsBasedOnTabDynDT, setLocalStorage } from "@helper/helper.js";
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData";
import AttachmentsCommentsTab from "@components/RequestBench/RequestPages/AttachmentsCommentsTab";
import PreviewPage from "@components/RequestBench/PreviewPage";
import { createPayloadForBK, idGenerator, transformApiResponseToReduxPayloadBk, fetchCountryBasedOnRegion, fetchTransportationZone } from "../../functions";
import ExcelOperationsCard from "@components/Common/ExcelOperationsCard";
import useDownloadExcel from "@hooks/useDownloadExcel";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import { END_POINTS } from "@constant/apiEndPoints";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";
import ChangeLogGlobal from "@components/Changelog/ChangeLogGlobal"
import BottomNavGlobal from "@components/RequestBench/RequestPages/BottomNavGlobal";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import useDynamicWorkflowDT from "@hooks/useDynamicWorkflowDT";
import { BUTTONS_TAB_MAP } from "@constant/buttonPriority";
import { useSnackbar } from "@hooks/useSnackbar";
import FlexibleValidationDialog from "@components/Common/FlexibleValidationDialog";
import SuccessDialog from "@components/Common/SubmitDialog";

const BankKeyCreateRequest = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useGenericDtCall();
  const { t } = useLang();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const requestId = urlSearchParams.get("RequestId");
  const steps = [
    t("Request Header"),
    t("Bank Key List"),
    t("Attachments & Remarks"),
    t("Preview"),
  ];
  const rowData = location.state;

  const requestHeaderResponse = useSelector((state) => state.bankKey.requestHeaderResponse)
  const requestIdHeader = useSelector((state) => state.bankKey.requestHeaderResponse?.requestId || "");
  const requestType = useSelector((state) => state.bankKey.requestHeaderResponse?.requestType || "");
  const payloadFields = useSelector((state) => state.bankKey.payload);
  const tabValue = useSelector((state) => state.bankKey.tabValue);
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");

  const isBankKeyApiCalled = useSelector((state) => state.bankKey?.isOdataApiCalled)
  const { fetchAllDropdownFMD } = useDropdownFMDData(destination_BankKey, setDropDownDataBNKY);
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const reduxPayload = useSelector((state => state.bankKey));

  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [completed, setCompleted] = useState([false]);
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [apiResponses, setApiResponses] = useState([]);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [blurLoading, setBlurLoading] = useState(false);
  const { handleUploadMaterial } = useDownloadExcel(MODULE_MAP?.BK);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [pcNumber, setPcNumber] = useState("");
  const [isSaveAsDraftEnabled, setIsSaveAsDraftEnabled] = useState(false);
  const { getButtonsDisplayGlobal, showWfLevels } = useButtonDTConfig();
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedLevelName, setSelectedLevelName] = useState('');
  const [wfLevels, setWfLevels] = useState([]);
  const initialPayload = useSelector((state) => state.bankKey.payload?.requestHeaderData || {});
  const isrequestId = queryParams.get("RequestId");
  let task = useSelector((state) => state?.userManagement.taskData);
  const changeLogBK = useSelector((state) => state.changeLog.createChangeLogDataBK || {});
  const bankKeyData = useSelector((state => state.bankKey));
  const { getDynamicWorkflowDT } = useDynamicWorkflowDT();
  let taskData = useSelector((state) => state.userManagement.taskData);
  const [tabButton, setTabButton] = useState([]);
  const filteredButtons = useSelector((state) => state.payload.filteredButtons);
  const { showSnackbar } = useSnackbar();
  const rowsHeaderData = useSelector((state) => state.bankKey.payload.rowsHeaderData);
  const rowsBodyData = useSelector((state) => state.bankKey.payload?.rowsBodyData || {});
  const { selectedRowID, mandatoryFields } = useSelector((state) => state.bankKey);
  const [missingFieldsDialogOpen, setMissingFieldsDialogOpen] = useState(false);
  const [missingFields, setMissingFields] = useState([]);
  // Tab Management and Scroll Enhancement
  const [selectedTab, setSelectedTab] = useState(0); // Currently selected tab index for switching
  const childRef = useRef(null);
  const [customMessageDialog, setCustomMessageDialog] = useState({
    open: false,
    message: "",
    title: ""
  });
  const [selectedRow, setSelectedRow] = useState(null);
  const [errorFieldMap, setErrorFieldMap] = useState({});
  const isChildPresent = rowData?.childRequestIds !== "Not Available";
  const bankKeyPayload = createPayloadForBK(reduxPayload?.payload, requestHeaderResponse)
  const [alertMsg, setAlertMsg] = useState("");
  const [dialogData, setDialogData] = useState({
    title: "",
    message: "",
    subText: "",
    buttonText: "",
    redirectTo: "",
  });
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const payloadForPreviewDownloadExcel = {
    region: payloadFields?.Region || "US",
    scenario:
      payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
        ? "Change with Upload"
        : "Create with Upload",
    bankCtry: payloadFields?.BankCtry || "US",
    dtName:
      payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ||
        payloadFields?.RequestType === REQUEST_TYPE?.CREATE ||
        payloadFields?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "MDG_BNKY_FIELD_CONFIG"
        : "MDG_BNKY_FIELD_CONFIG",
    version:
      payloadFields?.RequestType === REQUEST_TYPE?.CREATE ||
        payloadFields?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "v2"
        : "v2",
    rolePrefix: TASK_NAME?.REQ_INITIATE_DOWNLOAD,
    requestId: reqBench && !isChildPresent ? requestId : "",
    templateName: payloadFields?.TemplateName || "",
    bankKeyDetails:
      bankKeyPayload
  };

  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);

  useEffect(() => {
    if (!requestId) {
      dispatch(setTabValue(0));
    }
    fetchHeaderFieldsFromDt();
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BK);
    getAttachmentsIDM();
    setPcNumber(idGenerator("BK"));

  }, []);

  useEffect(() => {
    if (!isBankKeyApiCalled) {
      fetchAllDropdownFMD("bankKey")
      dispatch(setOdataApiCall(true))
    }
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE, MODULE_MAP.BK)
    return () => {
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.MODULE)
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.CURRENT_TASK);
      clearLocalStorageItem(LOCAL_STORAGE_KEYS.ROLE);
    }
  }, []);

  useEffect(() => {
    if (dtData) {
      let responseData = dtData?.result[0]?.MDG_MAT_REQUEST_HEADER_CONFIG
        ;
      const formattedData = responseData
        .sort((a, b) => a.MDG_MAT_SEQUENCE_NO - b.MDG_MAT_SEQUENCE_NO)
        .map((item) => ({
          fieldName: item.MDG_MAT_UI_FIELD_NAME,
          sequenceNo: item.MDG_MAT_SEQUENCE_NO,
          fieldType: item.MDG_MAT_FIELD_TYPE,
          maxLength: item.MDG_MAT_MAX_LENGTH,
          value: item.MDG_MAT_DEFAULT_VALUE,
          visibility: item.MDG_MAT_VISIBILITY,
          jsonName: item.MDG_MAT_JSON_FIELD_NAME,
        }));

      const requestHeaderObj = { "Header Data": formattedData };
      dispatch(setHeaderFieldsBnky(requestHeaderObj));
    }
  }, [dtData]);

  const handleTabChange = (index) => {
    dispatch(setTabValue(index));
  };

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    } else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    } else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.BANKKEY);
    }
  };

  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const handleDownload = () => {
    setDownloadClicked(true);
  };

  const handleUploadMaterialBankKey = (file) => {
    handleUploadMaterial(file, setLoaderMessage, setBlurLoading, payloadFields, MODULE_MAP?.BK, RequestType, requestId, rowData);
  }

  const handleCancel = () => {
    setisDialogVisible(false);
  };

  const onlyDigits = (val) => String(val || "").replace(/\D/g, "");

  const getAttachmentsIDM = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };
    const hSuccess = (data) => {
      if (data.statusCode === API_CODE?.STATUS_200) {
        let responseData = data?.data?.result[0]?.MDG_ATTACHMENTS_ACTION_TYPE;
        const attachmentNames = responseData || [];
        setAttachmentsData(attachmentNames);
      }
    };

    const hError = (error) => {
    };

    if (applicationConfig.environment === "localhost") {
      doAjax(`/${destination_IDM}${END_POINTS?.INVOKE_RULES?.LOCAL}`, "post", hSuccess, hError, payload);
    } else {
      doAjax(`/${destination_IDM}${END_POINTS?.INVOKE_RULES?.PROD}`, "post", hSuccess, hError, payload);
    }
  };

  const handleExportTemplateExcel = () => {
    setBlurLoading(true);
    setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
    const url = RequestId
      ? END_POINTS.EXCEL.EXPORT_EXCEL_BK
      : END_POINTS.EXCEL.EXPORT_EXCEL_BK;
    const isChildPresent = rowData?.childRequestIds !== "Not Available";
    let payload = {
      dtName:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "MDG_BNKY_FIELD_CONFIG"
          : "MDG_BNKY_FIELD_CONFIG",
      version:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD
          ? "v2"
          : "v2",
      parentRequestId: reqBench && !isChildPresent ? requestId : "",
      childRequestId: (!reqBench && requestId) || (reqBench && isChildPresent) ? requestId : "",
      scenario:
        payloadFields?.RequestType === REQUEST_TYPE?.CHANGE ||
          payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ||
          payloadFields?.RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
          ? "Change with Upload"
          : "Create with Upload",
      templateName: payloadFields?.TemplateName || "",
      region: payloadFields?.Region || "US",
      bankCtry: payloadFields?.BankCtry || "US",
      rolePrefix: TASK_NAME?.REQ_INITIATE_DOWNLOAD,
      templateName: "",
    };

    const hSuccess = (response) => {
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute(
        "download",
        `Bank Key_Data Export.xlsx`
      );
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      setBlurLoading(false);
      setLoaderMessage("");

      setSuccessMsg(true);
      setAlertType("success");
      handleSnackBarOpen();
    };
    const hError = () => { };
    doAjax(
      `/${destination_BankKey}${url}`,
      "postandgetblob",
      hSuccess,
      hError,
      payload
    );
  };

  const getDisplayDataBK = async (requestId) => {
    const isChildPresent = rowData?.childRequestIds !== "Not Available";

    const payload = reqBench ? {

      sort: "id,asc",
      childRequestId: isChildPresent ? requestId : "",
      parentRequestId: !isChildPresent ? requestId : "",
      page: 0,
      size: 10,

    } : {

      sort: "id,asc",
      childRequestId: isChildPresent ? requestId : "",
      parentRequestId: !isChildPresent ? requestId : "",
      page: 0,
      size: 10,

    }

    const hSuccess = (response) => {
      const apiResponse = response?.body || [];
      let requestHeaderData = response?.body[0]?.Torequestheaderdata;
      let TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

      dispatch(
        setRequestHeaderPayloadData({
          RequestId: requestHeaderData.RequestId,
          RequestPrefix: requestHeaderData.RequestPrefix,
          ReqCreatedBy: requestHeaderData.ReqCreatedBy,
          ReqCreatedOn: requestHeaderData.ReqCreatedOn,
          ReqUpdatedOn: requestHeaderData.ReqUpdatedOn,
          RequestType: requestHeaderData.RequestType,
          RequestDesc: requestHeaderData.RequestDesc,
          RequestStatus: requestHeaderData.RequestStatus,
          RequestPriority: requestHeaderData.RequestPriority,
          FieldName: requestHeaderData.FieldName,
          TemplateName: requestHeaderData.TemplateName,
          Division: requestHeaderData.Division,
          region: requestHeaderData.region,
          leadingCat: requestHeaderData.leadingCat,
          firstProd: requestHeaderData.firstProd,
          launchDate: requestHeaderData.launchDate,
          isBifurcated: requestHeaderData.isBifurcated,
          screenName: requestHeaderData.screenName,
          TotalIntermediateTasks: TotalIntermediateTasks,
        })
      );

      setApiResponses(apiResponse);
      const reqType = requestHeaderData?.RequestType;
      if (
        reqType === REQUEST_TYPE.CHANGE ||
        reqType === REQUEST_TYPE.CHANGE_WITH_UPLOAD
      ) {
        getChangeTemplate();
      }
      const transformedPayload =
        transformApiResponseToReduxPayloadBk(apiResponse);
      dispatch(setSelectedRowID(transformedPayload?.payload?.rowsHeaderData[0]?.id))
      dispatch(setBankKeyPayload(transformedPayload?.payload));
      dispatch(
        setDynamicKeyValue({
          keyName: "childRequestHeaderData",
          data: transformedPayload?.payload?.childRequestHeaderData
        })
      )
      dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload?.rowsBodyData));
      apiResponse.forEach((item) => {
        if (item.Tobankaddress.Country) {
          fetchCountryBasedOnRegion(
            item.Tobankaddress.Country,
            dispatch,
            item.Tobankaddress.AddrRegion,
            item.Tobankaddress.Transpzone,
          );
          fetchTransportationZone(
            item.Tobankaddress.Country,
            dispatch
          );
        }
      })
    }
    const hError = (error) => {
    };

    doAjax(
      `/${destination_BankKey}/${END_POINTS?.CHG_DISPLAY_REQUESTOR?.DISPLAY_BK}`,
      "post",
      hSuccess,
      hError,
      payload
    );

  };

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataBK(RequestId);
        if (
          ((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD &&
            !rowData?.length) ||
            RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
          (rowData?.reqStatus === REQUEST_STATUS.DRAFT ||
            rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)
        ) {
          dispatch(setTabValue(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setTabValue(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }
      } else {
        dispatch(setTabValue(0));
      }
    };

    loadData();
    return () => {

      dispatch(resetPayloadData());
      dispatch(setRequestHeader({}));
      dispatch(resetValidationStatus());
      dispatch(resetBankKeyStateBk());
      dispatch(setCreatePayloadCopyForChangeLog({}));
      dispatch(clearCreateChangeLogData());
    };
  }, [requestId, dispatch]);

  const fetchHeaderFieldsFromDt = () => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DECISION_TABLE_NAME.MDG_FMD_REQUEST_HEADER_CONFIG,
      version: "v2",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SCENARIO": REQUEST_TYPE?.CREATE,
          "MDG_CONDITIONS.MDG_MAT_MODULE_NAME": MODULE_MAP?.BK,
        },
      ],
    };
    getDtCall(payload);
  };
  const handleButtonClick = async (type, remarks) => {
    if (type === "VALIDATE") {
      const validationSuccess = await handleMassValidation(type);
      if (!validationSuccess) {
        return;
      }
    }
    setBlurLoading(true);
    let apiEndpoint =
      END_POINTS?.MASTER_BUTTON_APIS?.[MODULE_MAP?.BK]?.[
      bankKeyData?.payload?.requestHeaderData?.RequestType
      ]?.[type];
    const finalPayload = createPayloadForBK(
      payloadFields,
      bankKeyData?.requestHeaderResponse,
      isrequestId,
      task,
      remarks,
      changeLogBK,
      selectedLevel,
      selectedLevelName,
    );
    const hSuccess = (data) => {
      setBlurLoading(false);
      let redirect;
      if (requestId && !reqBench) {
          redirect=APP_END_POINTS?.MY_TASK;
        }
        else{
          redirect=SUCCESS_DIALOG_MESSAGE.REDIRECT
        }
      if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
        setDialogData({
          title: SUCCESS_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
          buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: redirect,

        });
        setSuccessDialogOpen(true);
      } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
        setDialogData({
          title: FAILURE_DIALOG_MESSAGE.TITLE,
          message: data.message,
          subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
          buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
          redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
        });
        setSuccessDialogOpen(true);
      } else {
        setAlertMsg("Unexpected response received.");
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      showSnackbar(error?.error, "error");
    };

    doAjax(apiEndpoint?.URL, "POST", hSuccess, hError, finalPayload);
  };

  const validateAllRows = async (type) => {
    // Step 1: Mandatory Field Check for all rows
    const validateMandatoryFields = () => {
      const allMissingFields = [];
      const errorFieldsForRow = [];
      let firstErrorField = null;
      let firstErrorTabIndex = null;
      let firstErrorRowId = null;

      rowsHeaderData.forEach((row, rowIndex) => {
        const missingForThisRow = [];
        const errorFieldsForThisRow = [];

        // Check BK_HEADER_MANDATORY fields (assume they belong to first tab, index 0)
        BK_HEADER_MANDATORY?.forEach((key) => {
          let value = row[key.jsonName];
          if (
            value === null ||
            value === undefined ||
            (typeof value === "string" && value.trim() === "")
          ) {
            missingForThisRow.push(key.fieldName);
            errorFieldsForThisRow.push(key.jsonName);

            // Capture first error field for highlighting and scrolling
            if (!firstErrorField) {
              firstErrorField = key.jsonName;
              firstErrorTabIndex = 0; // header tab index
              firstErrorRowId = row.id;
            }
          }
        });

        // Check object level mandatory fields (grouped by tab index in mandatoryFields)
        const objectLevelMandatoryFields = mandatoryFields?.[`${initialPayload?.Region}-${row?.BankCtry}`] || {};

        Object.keys(objectLevelMandatoryFields)?.forEach((tabKey, tabIndex) => {
          const fields = objectLevelMandatoryFields[tabKey];
          fields.forEach((field) => {
            let value = rowsBodyData?.[row.id]?.[field.jsonName];
            if (
              value === null ||
              value === undefined ||
              (typeof value === "string" && value.trim() === "")
            ) {
              errorFieldsForRow.push(field.jsonName);
              errorFieldsForThisRow.push(field.jsonName);
              missingForThisRow.push(field.fieldName);

              // Capture first error field for highlighting and scrolling
              if (!firstErrorField) {
                firstErrorField = field.jsonName;
                firstErrorTabIndex = tabIndex; // tab index automatically
                firstErrorRowId = row.id;
              }
            }
          });
        });

        // Update errorFieldMap for this row
        if (errorFieldsForThisRow.length > 0) {
          setErrorFieldMap((prev) => ({
            ...prev,
            [row.id]: errorFieldsForThisRow,
          }));
        }

        if (missingForThisRow.length > 0) {
          allMissingFields.push({
            rowIndex: rowIndex + 1,
            rowId: row.id,
            missingFields: missingForThisRow
          });
        }
      });

      return {
        allMissingFields,
        firstErrorField,
        firstErrorTabIndex,
        firstErrorRowId
      };
    };

    // Step 2: Local Duplicacy Check
    const checkLocalDuplicacy = () => {
      const duplicates = [];
      const seen = new Map();

      rowsHeaderData.forEach((row, index) => {
        const bankCtry = row.BankCtry?.trim();
        const bankKey = row.BankKey?.trim();

        if (bankCtry && bankKey) {
          const duplicateKey = `${bankCtry}-${bankKey}`;

          if (seen.has(duplicateKey)) {
            const firstOccurrence = seen.get(duplicateKey);
            duplicates.push({
              rowIndex: index + 1,
              firstRowIndex: firstOccurrence.index + 1,
              bankCtry,
              bankKey,
              message: `Duplicate BankCtry-BankKey found: ${bankCtry} - ${bankKey} (Rows ${firstOccurrence.index + 1} & ${index + 1})`
            });
          } else {
            seen.set(duplicateKey, { index, bankCtry, bankKey });
          }
        }
      });

      return duplicates;
    };

    // Step 3: Server-side duplicate check for all rows
    const checkServerSideDuplicates = async () => {
      const duplicateErrors = [];

      for (const [index, row] of rowsHeaderData.entries()) {
        const bankCtry = row.BankCtry;
        const bankKey = row.BankKey;
        const bankName = row.BankName;

        if (bankCtry && bankKey) {
          try {
            const duplicateResult = await checkDuplicateBankDetails(bankCtry, bankKey, bankName);

            if (duplicateResult.isDuplicate) {
              duplicateErrors.push({
                rowIndex: index + 1,
                rowId: row.id,
                message: `Row ${index + 1}: ${duplicateResult.message}`
              });
            }
          } catch (error) {
            duplicateErrors.push({
              rowIndex: index + 1,
              rowId: row.id,
              message: `Row ${index + 1}: Error checking duplicates for ${bankCtry}-${bankKey}`
            });
          }
        }
      }

      return duplicateErrors;
    };

    // Perform all validation checks
    const mandatoryValidation = validateMandatoryFields();
    const { allMissingFields, firstErrorField, firstErrorTabIndex, firstErrorRowId } = mandatoryValidation;
    const localDuplicates = checkLocalDuplicacy();

    // Collect all errors
    let allErrors = [];

    // Add mandatory field errors
    if (allMissingFields.length > 0) {
      const errorMessages = allMissingFields.map(error =>
        `Row ${error.rowIndex}: Missing fields - ${error.missingFields.join(', ')}`
      );
      allErrors.push(...errorMessages);
    }

    // Add local duplicate errors
    if (localDuplicates.length > 0) {
      const errorMessages = localDuplicates.map(dup => dup.message);
      allErrors.push(...errorMessages);
    }

    // If there are mandatory field or local duplicate errors, show them and stop
    if (allErrors.length > 0) {
      setMissingFields(allErrors);
      setMissingFieldsDialogOpen(true);

      // 🔹 Enhanced: Set tab and field for highlighting/scrolling (for mandatory field errors)
      if (firstErrorField !== null && firstErrorTabIndex !== null) {
        setSelectedTab(firstErrorTabIndex); // switch tab
        // setFieldToScroll(firstErrorField);  // mark field to scroll after dialog closes
      }

      // Reset validation status for rows with errors
      const updatedRows = rowsHeaderData.map((row, index) => {
        const hasMandatoryError = allMissingFields.some(error => error.rowId === row.id);
        const hasDuplicateError = localDuplicates.some(dup =>
          dup.rowIndex - 1 === index || dup.firstRowIndex - 1 === index
        );

        return (hasMandatoryError || hasDuplicateError)
          ? { ...row, validated: false }
          : { ...row, validated: true };
      });

      dispatch(setRowsHeaderData(updatedRows));
      return; // Stop execution - DO NOT proceed to API call
    }

    // Step 4: Check server-side duplicates if local validations pass
    try {
      const serverDuplicates = await checkServerSideDuplicates();

      if (serverDuplicates.length > 0) {
        const errorMessages = serverDuplicates.map(dup => dup.message);
        setMissingFields(errorMessages);
        setMissingFieldsDialogOpen(true);

        // Reset validation status for rows with server-side duplicate errors
        const updatedRows = rowsHeaderData.map((row, index) => {
          const hasServerError = serverDuplicates.some(dup => dup.rowId === row.id);
          return hasServerError
            ? { ...row, validated: false }
            : { ...row, validated: true };
        });

        dispatch(setRowsHeaderData(updatedRows));
        return; // Stop execution - DO NOT proceed to API call
      }

      // If all validations pass, mark all rows as validated and proceed with API call
      const updatedRows = rowsHeaderData.map((row) => ({ ...row, validated: true }));
      dispatch(setRowsHeaderData(updatedRows));

      // 🔹 Enhanced: Clear error field map for all rows when validation passes
      setErrorFieldMap({});

    } catch (error) {
      setMissingFields(["Error occurred while checking for duplicate bank details"]);
      setMissingFieldsDialogOpen(true);
      return;
    }

    // Step 5: All validations passed, proceed with final API call
    try {
      setBlurLoading(true);
      const finalPayload = createPayloadForBK(
        payloadFields,
        bankKeyData?.requestHeaderResponse,
        isrequestId,
        task,
        "",
        changeLogBK,
        selectedLevel,
        selectedLevelName,
      );

      const hSuccess = (data) => {
        setBlurLoading(false);

        if (data?.statusCode === API_CODE?.STATUS_200 || data?.statusCode === API_CODE?.STATUS_201) {
          setDialogData({
            title: SUCCESS_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: SUCCESS_DIALOG_MESSAGE.SUBTEXT,
            buttonText: SUCCESS_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: SUCCESS_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else if (data?.statusCode === API_CODE?.STATUS_500 || data?.statusCode === API_CODE?.STATUS_501) {
          setDialogData({
            title: FAILURE_DIALOG_MESSAGE.TITLE,
            message: data.message,
            subText: FAILURE_DIALOG_MESSAGE.SUBTEXT,
            buttonText: FAILURE_DIALOG_MESSAGE.BUTTONTEXT,
            redirectTo: FAILURE_DIALOG_MESSAGE.REDIRECT,
          });
          setSuccessDialogOpen(true);
        } else {
          setAlertMsg("Unexpected response received.");
        }
      };

      const hError = (error) => {
        setBlurLoading(false);
        setMissingFields([DASHBOARD_REPORT_LABELS.ERROR_VALIDATING]);
        setMissingFieldsDialogOpen(true);
      };

      doAjax(
        `/${destination_BankKey}/massAction/validateBankKey`,
        "POST",
        hSuccess,
        hError,
        finalPayload
      );
    } catch (error) {
      setBlurLoading(false);
      setMissingFields([DASHBOARD_REPORT_LABELS.VALIDATION_FAILED_UNEXPECTED_ERROR]);
      setMissingFieldsDialogOpen(true);
    }
  };
  //  const handleMassValidation = async () => {
  //     if (!childRef.current) return;
  //     let allValidated = true;

  //     // let firstError = null;
  //     // const updatedRows = [];

  //     for (const row of rowsHeaderData) {
  //       // Call child function
  //       debugger
  //       const result = await childRef.current.validateRow(row);
  //       console.log("Result from child:", result);
  //       // updatedRows.push({
  //       //   ...row,
  //       //   validated: result.validated
  //       // });

  //       // Track first error
  //       if (!firstError && !result.validated) {
  //         firstError = result;
  //       }
  //     }

  //     // dispatch(setRowsHeaderData(updatedRows));

  //     // if (firstError) {
  //     //   // Scroll/highlight first error like single-row validation
  //     //   setSelectedTab(firstError.firstErrorTabIndex);

  //     //   setMissingFields([`Row ${firstError.row.id}: Missing fields - ${firstError.missing.join(', ')}`]);
  //     //   setMissingFieldsDialogOpen(true);
  //     // } else {
  //     //   showSnackbar("All rows validated successfully", "success");
  //     // }
  //   };
  // const handleMassValidation = async () => {
  //   if (!childRef.current) return;

  //   let allValidated = true;

  //   for (const row of rowsHeaderData) {
  //     const result = await childRef.current.validateRow(row);
  //     console.log("Result from child:", result);

  //     // Update row validation status in redux
  //     dispatch(setRowsHeaderData(rowsHeaderData.map(r =>
  //       r.id === row.id ? { ...r, validated: result.validated } : r
  //     )));

  //     // Handle invalid row
  //     if (!result.validated) {
  //       allValidated = false;

  //       setSelectedTab(result.errorTabIndex);
  //       // setMissingFields([`Row ${result.rowId}: Missing fields - ${result.missing.join(', ')}`]);
  //       // setMissingFieldsDialogOpen(true);

  //       // Wait until user closes the dialog before continuing
  //       await new Promise(resolve => {
  //         const interval = setInterval(() => {
  //           if (!missingFieldsDialogOpen) {
  //             clearInterval(interval);
  //             resolve();
  //           }
  //         }, 100);
  //       });
  //     }
  //   }

  //   // Show final success snackbar after all rows validated
  //   if (allValidated) {
  //     showSnackbar("All rows validated successfully", "success");
  //   }
  // };

  const [currentError, setCurrentError] = useState(null);
  useEffect(() => {
    if (currentError && missingFieldsDialogOpen) {
      setSelectedTab(currentError.errorTabIndex); // switch tab
      // setFieldToScroll(currentError.errorField);  // scroll to field
    }
  }, [currentError, missingFieldsDialogOpen]);

  // const handleMassValidation = async () => {
  //   if (!childRef.current) return;

  //   for (const row of rowsHeaderData) {
  //     const result = await childRef.current.validateRow(row);

  //     // Update row validation status
  //     dispatch(setRowsHeaderData(rowsHeaderData.map(r =>
  //       r.id === row.id ? { ...r, validated: result.validated } : r
  //     )));

  //     if (!result.validated) {
  //       // Pass the error to state
  //       setCurrentError(result);

  //       // Open dialog
  //       // setMissingFields([`Row ${result.rowId}: Missing fields - ${result.missing.join(', ')}`]);
  //       // setMissingFieldsDialogOpen(true);

  //       // Wait until dialog closes
  //       await new Promise(resolve => {
  //         const interval = setInterval(() => {
  //           if (!missingFieldsDialogOpen) {
  //             clearInterval(interval);
  //             resolve();
  //           }
  //         }, 100);
  //       });
  //     }
  //   }

  //   // After all rows
  //   showSnackbar("All rows validated successfully", "success");
  // };

  const handleMassValidation = async () => {
    if (!childRef.current) return;

    let allValidated = true;

    for (const row of rowsHeaderData) {
      const result = await childRef.current.validateRow(row);

      // Update row validation status in redux
      dispatch(setRowsHeaderData(rowsHeaderData.map(r =>
        r.id === row.id ? { ...r, validated: result.validated } : r
      )));

      if (!result.validated) {
        allValidated = false;

        // Set current error for effect to handle tab & scroll
        setCurrentError(result);

        // Open dialog
        // setMissingFields([`Row ${result.rowId}: Missing fields - ${result.missing.join(', ')}`]);
        // setMissingFieldsDialogOpen(true);

        // Wait until dialog closes
        await new Promise(resolve => {
          const interval = setInterval(() => {
            if (!missingFieldsDialogOpen) {
              clearInterval(interval);
              resolve();
            }
          }, 100);
        });
      }
    }

    if (allValidated) {
      showSnackbar("All rows validated successfully", "success");
    }
    return allValidated;
  };

  const checkDuplicateBankDetails = (bankCtry, bankKey, bankName) => {
    return new Promise((resolve, reject) => {
      const payload = [
        {
          bankCtry: bankCtry,
          bankKey: bankKey,
          requestNo: bankKeyData?.requestHeaderResponse?.requestId || initialPayload?.RequestId || "",
          bankName: bankName,
        },
      ];

      const successHandler = (data) => {
        if (data?.body?.length) {
          const errorMessage = `Duplicate bank details found: ${data.body[0].split("$^$")[0]} (${data.body[0].split("$^$")[1]})`;
          resolve({ isDuplicate: true, message: errorMessage });
        } else {
          resolve({ isDuplicate: false, message: "" });
        }
      };

      const errorHandler = (error) => {
        customError(error);
        resolve({ isDuplicate: false, message: "" });
      };

      let localDuplicateCount = 0;
      let duplicateMessage = "";

      rowsHeaderData?.forEach((key) => {
        if (key?.BankCtry && key?.BankKey) {
          if (key?.BankCtry === bankCtry && key?.BankKey === bankKey) {
            localDuplicateCount++;
          }
        }
      });

      if (localDuplicateCount > 1) {
        duplicateMessage = `Duplicate bank details found locally: ${bankCtry} - ${bankKey}`;
        resolve({ isDuplicate: true, message: duplicateMessage });
      } else {
        doAjax(
          `/${destination_BankKey}${END_POINTS.MASS_ACTION?.BANK_DUPLICATE_CHECK}`,
          "post",
          successHandler,
          errorHandler,
          payload
        );
      }
    });
  };
  useEffect(() => {
    const fetchWorkflowLevels = async () => {
      try {
       
        const workflowLevelsDtData = await getDynamicWorkflowDT(
          RequestType,
          initialPayload?.Region,
          '',
          reduxPayload?.Tochildrequestheaderdata?.BankKeyGroupType,
          taskData?.ATTRIBUTE_3,
          "v1",
          "MDG_BNKY_DYNAMIC_WORKFLOW_DT",
          MODULE_MAP?.BK
        );
        setWfLevels(workflowLevelsDtData);
      } catch (err) {
        customError(err);
      }
    };
    if (RequestType && initialPayload?.Region && taskData?.ATTRIBUTE_3) {
      fetchWorkflowLevels();
    }
  }, [RequestType, initialPayload?.Region, taskData?.ATTRIBUTE_3]);

  useEffect(() => {
    let actionTypesToFilter = BUTTONS_TAB_MAP?.[tabValue]
    const updatedButtons = filterButtonsBasedOnTabDynDT(
      filteredButtons,
      actionTypesToFilter
    );
    setTabButton(updatedButtons);
  }, [tabValue, filteredButtons]); // runs when tab changes

  useEffect(() => {
    getButtonsDisplayGlobal("Bank Key", "MDG_DYN_BTN_DT", "v3", requestType || RequestType);
  }, [requestType, RequestType]);

  return (
    <>
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestIdHeader || requestId ? (
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                textAlign: "left",
                display: "flex",
                alignItems: "center",
                gap: 1,
              }}
            >
              <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
              {t("Request Header ID")}:{" "}
              <span>{requestIdHeader ? requestIdHeader : requestId}</span>
            </Typography>
          ) : (
            <div style={{ flex: 1 }} />
          )}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Download Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={false}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>
              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={handleExportTemplateExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>

        {isChangeLogopen &&
          <ChangeLogGlobal
            open={true}
            closeModal={() => setisChangeLogopen(false)}
            requestId={requestIdHeader || requestId}
            requestType={RequestType || requestType}
            module={MODULE_MAP?.BK}
          />
        }

        {payloadFields?.TemplateName && (
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              textAlign: "left",
              display: "flex",
              alignItems: "center",
              gap: 1,
            }}
          >
            <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
            {t("Template Name")}: <span>{payloadFields?.TemplateName}</span>
          </Typography>
        )}
        <IconButton
          onClick={() => {
            if (reqBench) {
              navigate(APP_END_POINTS?.REQUEST_BENCH);
              return;
            }
            setisDialogVisible(true);
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{ left: "-10px" }}
          title={t("Back")}
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper
          nonLinear
          activeStep={tabValue}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "25px 14%",
            marginTop: "-35px",
          }}
        >
          {steps.map((label, index) => (
            <Step key={label}>
              <StepButton
                color="error"
                disabled={
                  (index === 1 && !isSecondTabEnabled) ||
                  (index === 2 && !isAttachmentTabEnabled) ||
                  (index === 3 && !isAttachmentTabEnabled)
                }
                onClick={() => handleTabChange(index)}
                sx={{ fontSize: "50px", fontWeight: "bold" }}
              >
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                  {label}
                </span>
              </StepButton>
            </Step>
          ))}
        </Stepper>

        <ErrorReportDialog
          dialogState={dialogOpen}
          closeReusableDialog={() => setDialogOpen(false)}
          module={MODULE_MAP?.BK}
        />

        {tabValue === 0 && (
          <>
            <RequestHeaderBankKey
              setIsSecondTabEnabled={setIsSecondTabEnabled}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
            />
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD ||
              RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD) &&
              ((rowData?.reqStatus == REQUEST_STATUS.DRAFT &&
                !rowData?.objectNumbers !== "Not Available") ||
                rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
                <ExcelOperationsCard
                  handleDownload={handleDownload}
                  setEnableDocumentUpload={setEnableDocumentUpload}
                  enableDocumentUpload={enableDocumentUpload}
                  handleUploadMaterial={handleUploadMaterialBankKey}
                />
              )}
          </>
        )}
        {tabValue === 1 && (
            <BankKeyListDetails
              ref={childRef}
              setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
              setCompleted={setCompleted}
              selectedTabParent={selectedTab}
              downloadClicked={downloadClicked}
              setDownloadClicked={setDownloadClicked}
              requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME}

            />
          )}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestIdHeader
                : requestId
            }
            pcNumber={pcNumber}
            childRequestIds={rowData?.childRequestIds ? rowData?.childRequestIds : "Not Available"}
            module={MODULE_MAP?.BK}
            artifactName={ARTIFACTNAMES.BK}
          />
        )}
        {tabValue === 3 && (
          <Box
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage requestStatus={rowData?.reqStatus ? rowData?.reqStatus : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME} module={MODULE_MAP?.BK} payloadData={reduxPayload}
              payloadForDownloadExcel={""} payloadForPreviewDownloadExcel={payloadForPreviewDownloadExcel} />
          </Box>
        )}
      </Box>
      {isDialogVisible && (
        <CustomDialog
          isOpen={isDialogVisible}
          titleIcon={
            <WarningOutlined
              size="small"
              sx={{ color: colors?.secondary?.amber, fontSize: "20px" }}
            />
          }
          Title={t("Warning")}
          handleClose={handleCancel}
        >
          <DialogContent sx={{ mt: 2 }}>
            {t(DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE)}
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              size="small"
              sx={{ ...button_Outlined }}
              onClick={handleCancel}
            >
              {t("No")}
            </Button>
            <Button
              variant="contained"
              size="small"
              sx={{ ...button_Primary }}
              onClick={handleYes}
            >
              {t("Yes")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
      {tabValue != 0 && (
        <BottomNavGlobal
          handleSaveAsDraft={handleButtonClick}
          handleSubmitForReview={handleButtonClick}
          handleSubmitForApprove={handleButtonClick}
          handleSendBack={handleButtonClick}
          handleCorrection={handleButtonClick}
          handleRejectAndCancel={handleButtonClick}
          handleValidateAndSyndicate={handleButtonClick}
          validateAllRows={handleButtonClick}
          isSaveAsDraftEnabled={isSaveAsDraftEnabled}
          validateEnabled={true}
          filteredButtons={tabButton}
          moduleName={MODULE_MAP?.BK}
          showWfLevels={showWfLevels}
          selectedLevel={selectedLevel}
          workFlowLevels={wfLevels}
          setSelectedLevel={setSelectedLevel}
          activeTab={tabValue}
          selectedLevelName={selectedLevelName}
          setSelectedLevelName={setSelectedLevelName}
        />)}
      {/* Missing Fields Dialog */}
      <FlexibleValidationDialog
        open={missingFieldsDialogOpen}
        onClose={() => setMissingFieldsDialogOpen(false)}
        missingFields={missingFields}
        t={t}
      />

      {/* Custom Message Dialog for Duplicacy */}
      <FlexibleValidationDialog
        open={customMessageDialog.open}
        onClose={() => setCustomMessageDialog({ open: false, message: "", title: "" })}
        customMessage={customMessageDialog.message}
        title={customMessageDialog.title}
        t={t}
      />
      <SuccessDialog
        open={successDialogOpen}
        onClose={() => setSuccessDialogOpen(false)}
        title={dialogData.title}
        message={dialogData.message}
        subText={dialogData.subText}
        buttonText={dialogData.buttonText}
        redirectTo={dialogData.redirectTo}
      />
    </>
  );
};

export default BankKeyCreateRequest;
