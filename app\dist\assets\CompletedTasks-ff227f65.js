import{n,r,s as w,g as S,j as c,F as v,r1 as J,bc as X,x5 as Y,bK as L,C as p,bf as D,x6 as m}from"./index-226a1e75.js";import{W as k,u as C}from"./propData-3de5c575.js";import{c as b}from"./configData-978802d4.js";import"./redux-49302a50.js";import"./index-2a7424d8.js";import"./react-beautiful-dnd.esm-0de399b3.js";import"./index-9c81b930.js";import"./asyncToGenerator-88583e02.js";import"./_baseDelay-5448b93c.js";import"./Chip-a06f5bd7.js";import"./Paper-164eb9eb.js";import"./Dropdown-6cea9e1f.js";import"./TextField-17bdd2f4.js";import"./Tooltip-d0e36572.js";import"./TableContainer-debf0374.js";import"./CheckBox-e52b9f98.js";import"./Autocomplete-b446b668.js";import"./AccordionDetails-2418f9ae.js";import"./Box-06e0824d.js";import"./InputAdornment-19c51729.js";import"./Backdrop-ef004339.js";import"./DialogActions-98850990.js";import"./DialogContentText-e4d806a4.js";import"./CircularProgress-1acedaf0.js";import"./FormControlLabel-57cd7a82.js";import"./DashboardSetting-970ae243.js";import"./Switch-a9aa0e31.js";import"./Grid-97e89306.js";import"./Zoom-f387e7b2.js";function Ii(){let t=n(i=>{var e;return(e=i.userManagement)==null?void 0:e.userData});const l=n(i=>i.applicationConfig),[d,O]=r.useState(null);let I=w();const N=S(),[Z,h]=r.useState(null),[j,u]=r.useState(null),R={APPLICATION_NAME:"1784",CRUD_API_ENV:"itm",DB_TYPE:"hana",SERVICE_BASE_URL:[{Description:"",Name:"ITMJavaServices",URL:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ConfigServer",URL:"https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkNetServices",URL:"https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"CrudApiServices",URL:"https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"WorkFormsServices",URL:"https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms"},{Description:"",Name:"NotificationServices",URL:"https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com"},{Description:"",Name:"ITMGraphServices",URL:"https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow Services",Name:"NativeWorkflowServices",URL:"https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com"},{Description:"Native Workflow UI URL",Name:"NativeWorkflowUiUrl",URL:"https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui"},{Description:"",Name:"OnboardingServices",URL:"https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com"}]},U=i=>{const e=(i==null?void 0:i.ATTRIBUTE_1)||(i==null?void 0:i.requestId);let o={childRequestIds:e};X(D.REQUEST_BENCH_TASK,o),I(Y({module:"RequestHistory",filterData:{reqId:e}})),N(L.REQUEST_HISTORY,{state:{requestId:e,module:i==null?void 0:i.processDisplayName}})},T=()=>{console.log("fetchFilterView")},W=()=>{console.log("clearFilterView")},f=()=>{p(`/${m}/api/v1/usersMDG/getUsersMDG`,"get",i=>{var e=i.data,o=e==null?void 0:e.map(a=>({...a,userId:a==null?void 0:a.emailId})),s={...i,data:o};h(s)})},V=()=>{p(`/${m}/api/v1/groupsMDG/getAllGroupsMDG`,"get",i=>{var e=i.data,o=e==null?void 0:e.map(a=>({...a,groupName:a==null?void 0:a.name})),s={...i,data:o};u(s)})},M=(i,e)=>{console.log("Success flag.",i),console.log("Task Payload.",e)};return r.useEffect(()=>{f(),V()},[]),c("div",{style:{width:"calc(100vw - 105px)",height:"calc(100vh-130px)"},className:"workspaceOverride",children:c(v,{children:c(k,{token:"********************************************************************************************************************************************************************************************************************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aSH3V0LAja0fT0nDxjQukU2kgLEE-WkmelwCHj2UOhzuhuO7ZAJJmlzKPtb7SRxiAqpNozbHPuaYDkjfl-gsOPCr45RKGToKtN4D0UafHCySeMdAZIRyCkKvXhzY0RXr6YWVHAUePgKFDRWINCPP78BDNp1LCX1PgxP5XtrDYg6F_CcaR0qa1I2YXvueRr2SMyhphztpJbwZF8hanV18c6B0gS0AcKCNhQIimXaClaTnIdU93pUgUX3OeDS9IRcw1rIaqMNiePcRYyfNU-hn1gT9AgHriXNDo2PcOkD-IJ9txjsg8fmbs0m0-13xQc75Sfv0qncfoHTJXiR9xYQzGA",configData:b,destinationData:R,userData:{...t,user_id:t==null?void 0:t.emailId},userPermissions:C,userList:d,groupList:{},useWorkAccess:l.environment==="localhost",useConfigServerDestination:l.environment==="localhost",inboxTypeKey:"MY_COMPLETED_TASKS",workspaceLabel:"Completed Tasks",subInboxTypeKey:null,onTaskClick:U,onActionComplete:M,workspaceFiltersByAPIDriven:!1,isFilterView:!1,savedFilterViewData:[],selectedFilterView:null,fetchFilterViewList:T,clearFilterView:W,cachingBaseUrl:J,selectedTabId:null,externalSystems:[]})})})}export{Ii as default};
