import { useSnackbar } from "./useSnackbar";
import { ACTIVE_TAB_MODULE_MAP, API_CODE, ERROR_MESSAGES, LOADING_MESSAGE, MODULE, MODULE_MAP, REQUEST_TYPE, SUCCESS_MESSAGES } from "../constant/enum";
import { END_POINTS } from "../constant/apiEndPoints";
import { APP_END_POINTS } from "../constant/appEndPoints";
import { doAjax } from "../components/Common/fetchService";
import { useNavigate } from "react-router-dom";
import { destination_BankKey, destination_InternalOrder, destination_Admin } from "../destinationVariables";
import { getKeyByValue } from "@helper/helper";

// Module configuration for download functionality
const MODULE_DOWNLOAD_CONFIG = {
  [MODULE_MAP?.BK]: {
    destination: destination_BankKey,
    dtName: "MDG_BNKY_FIELD_CONFIG",
    version: "v2",
    rolePrefix: "Z_FIN_REQ_DOWNLOAD",
    scenario: "Create with Upload",
    getPayload: (payloadFields) => ({
      requestId: payloadFields?.RequestId || "",
      dtName: "MDG_BNKY_FIELD_CONFIG",
      version: "v2",
      region: payloadFields?.Region || "US",
      bankCtry: payloadFields?.BankCtry || "US",
      rolePrefix: "Z_FIN_REQ_DOWNLOAD",
      scenario: "Create with Upload",
    }),
    downloadEndpoints: {
      create: END_POINTS.EXCEL.DOWNLOAD_EXCEL,
      change: END_POINTS.EXCEL.DOWNLOAD_EXCEL_WITH_DATA,
      createMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAIL,
      extendMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL,
    },
    uploadEndpoint: "getAllBankKeysFromExcel", // BankKey uses massAction pattern
    method: "postandgetblob",
    mailMethod: "post",
  },
  [MODULE_MAP?.IO]: {
    destination: destination_InternalOrder,
    dtName: "MDG_INTORD_FIELD_CONFIG",
    version: "v2",
    scenario: "Create with Upload",
    getPayload: (payloadFields) => ({
      dtName: "MDG_INTORD_FIELD_CONFIG",
      version: "v2",
      requestId: payloadFields?.requestHeaderData?.RequestId || "",
      scenario: "Create with Upload",
      orderType: "ALL",
      region: payloadFields?.Region || "US",
    }),
    downloadEndpoints: {
      create: "/api/v1/excel/downloadExcel",
      change: "/api/v1/excel/downloadExcel",
      createMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_MAIL,
      extendMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL,
    },
    uploadEndpoint: "/api/v1/massAction/getAllInternalOrdersFromExcel",
    method: "postandgetblob",
    mailMethod: "post",
  },
  // Default configuration for other modules (PCG, CCG, etc.)
  DEFAULT: {
    getPayload: () => ({}),
    downloadEndpoints: {
      create: END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD,
      change: END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD,
      createMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CREATE_WITH_UPLOAD_MAIL,
      changeMail: END_POINTS.EXCEL.DOWNLOAD_EXCEL_PCG_CHANGE_WITH_UPLOAD_MAIL,
    },
    method: "getblobfile",
    mailMethod: "get",
  },
};

const useDownloadExcel = (module) => {
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const destination = END_POINTS?.MODULE_DESTINATION_MAP?.[module];

  const handleUploadMaterial = (file, setLoaderMessage, setBlurLoading, payloadFields, moduleKey, RequestType, requestId, rowData) => {
    // Get module configuration
    const config = MODULE_DOWNLOAD_CONFIG[moduleKey] || MODULE_DOWNLOAD_CONFIG.DEFAULT;
    const moduleDestination = config.destination || destination;

    if (RequestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
      showSnackbar("Upload is only supported for Create with Upload request type", "error");
      return;
    }

    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);

    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("requestId", requestId ? requestId : "");
    formData.append("dtName", config.dtName);
    formData.append("version", config.version);

    // Add module-specific form data
    if (moduleKey === MODULE_MAP?.BK) {
      formData.append("region", rowData?.Region || "US");
      formData.append("bankCtry", rowData?.bankCtry || "US");
      formData.append("role", config.rolePrefix);
    } else if (moduleKey === MODULE_MAP?.IO) {
      formData.append("region", rowData?.Region || "US");
      formData.append("orderType", "ALL");
      formData.append("scenario", config.scenario);
    }

    const hSuccess = (data) => {
      setBlurLoading(false);
      setLoaderMessage("");
      if (data.statusCode === 200) {
        showSnackbar(SUCCESS_MESSAGES?.EXCEL_UPLOAD_SUCCESS || "Excel uploaded successfully", "success");
      } else {
        showSnackbar(ERROR_MESSAGES?.EXCEL_UPLOAD_ERROR || "Error uploading excel", "error");
      }
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(ERROR_MESSAGES?.EXCEL_UPLOAD_ERROR || "Error uploading excel", "error");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    // Construct upload URL based on module configuration
    let uploadUrl;
    if (moduleKey === MODULE_MAP?.IO) {
      // Internal Order uses full endpoint path: /{destination}{fullEndpoint}
      uploadUrl = `/${moduleDestination}${config.uploadEndpoint}`;
    } else {
      // Default pattern for other modules
      uploadUrl = `/${moduleDestination}/massAction/${config.uploadEndpoint || "getAllFromExcel"}`;
    }

    doAjax(uploadUrl, "postformdata", hSuccess, hError, formData);
  };

  const handleDownload = (setLoaderMessage, setBlurLoading, payloadFields, moduleKey) => {
    setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
    setBlurLoading(true);

    // Get module configuration
    const config = MODULE_DOWNLOAD_CONFIG[moduleKey] || MODULE_DOWNLOAD_CONFIG.DEFAULT;
    const moduleDestination = config.destination || destination;

    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      const fileName = payloadFields?.RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? `${moduleKey}_Mass Change.xlsx` : `${moduleKey}_Mass Create.xlsx`;

      link.href = href;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`${fileName} has been downloaded successfully.`, "success");
      setTimeout(() => {
        navigate(`${APP_END_POINTS?.REQUEST_BENCH}`);
      }, 2600);
    };

    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error");
    };

    // Determine download endpoint based on request type
    const isChangeRequest = payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD;
    const endpoint = isChangeRequest ? config.downloadEndpoints.change : config.downloadEndpoints.create;
    const downloadUrl = `/${moduleDestination}${endpoint}`;

    // Get payload if configuration provides it
    const payload = config.getPayload ? config.getPayload(payloadFields) : {};
    const method = config.method || "getblobfile";

    doAjax(downloadUrl, method, hSuccess, hError, payload);
  };

  const handleEmailDownload = (setLoaderMessage, setBlurLoading, payloadFields, moduleKey) => {
    setBlurLoading(true);
    setLoaderMessage("Initiating email download...");

    // Get module configuration
    const config = MODULE_DOWNLOAD_CONFIG[moduleKey] || MODULE_DOWNLOAD_CONFIG.DEFAULT;
    const moduleDestination = config.destination || destination;

    const hSuccess = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(SUCCESS_MESSAGES?.DOWNLOAD_MAIL_INITIATED, "success");
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };

    const hError = () => {
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(ERROR_MESSAGES?.ERR_DOWNLOADING_EXCEL, "error");
      setTimeout(() => {
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }, 2600);
    };

    // Determine email endpoint based on request type
    const isExtendRequest = payloadFields?.RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD;
    const isChangeRequest = payloadFields?.RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD;

    let endpoint;
    if (isExtendRequest && config.downloadEndpoints.extendMail) {
      endpoint = config.downloadEndpoints.extendMail;
    } else if (isChangeRequest && config.downloadEndpoints.changeMail) {
      endpoint = config.downloadEndpoints.changeMail;
    } else {
      endpoint = config.downloadEndpoints.createMail;
    }

    const downloadUrl = `/${moduleDestination}${endpoint}`;

    // Get payload if configuration provides it
    const payload = config.getPayload ? config.getPayload(payloadFields) : {};
    const method = config.mailMethod || "get";

    doAjax(downloadUrl, method, hSuccess, hError, payload);
  };

  const handleDownloadWF = (setLoaderMessage, setBlurLoading, module, type) => {
    setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
    setBlurLoading(true);

    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", type === "create" ? `Workflow_Create.xlsx` : `All_Workflows_Data_${module}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(type === "create" ? `Workflow_Create.xlsx has been downloaded successfully.` : `All_Workflows_Data.xlsx has been downloaded successfully.`, "success");
    };
    const hError = () => {
      setBlurLoading(false);
    };

    const downloadUrl = type === "create" ? `/${destination_Admin}${END_POINTS.EXCEL.EXPORT_WF_EXCEL}` : `/${destination_Admin}${END_POINTS.EXCEL.DOWNLOAD_ALL_WF_DATA}`;

    if (type !== "create") {
      const payload = { module };
      doAjax(downloadUrl, "postandgetblob", hSuccess, hError, payload);
    } else {
      doAjax(downloadUrl, "getblobfile", hSuccess, hError);
    }
  };

  const handleUploadWF = (file, setLoaderMessage, setBlurLoading, module, setEnableDocumentUpload, handleClose, setActiveTab, scenario) => {
      setBlurLoading(true)
      const formData = new FormData();
      [...file].forEach((item) => formData.append("files", item));
      formData.append("module", module);
      formData.append("scenario", scenario);

      const hSuccess = (data) => {
          if (data.statusCode === API_CODE?.STATUS_200) {
              setBlurLoading(false);
              setLoaderMessage("");
              showSnackbar(Array.isArray(data.data) || !data?.data ? data?.message : data?.data, "success");
              setActiveTab(getKeyByValue(ACTIVE_TAB_MODULE_MAP, module))
              setEnableDocumentUpload(false)
              handleClose()
          } else {
              setBlurLoading(false);
              setLoaderMessage("");
              showSnackbar(Array.isArray(data.data) || !data?.data ? data?.message : data?.data, "error");
          }
      };
      const hError = (error) => {
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(error.message, "error");
      };
      const uploadUrl = `/${destination_Admin}${END_POINTS.EXCEL.UPLOAD_WORKFLOW_EXCEL}`;
      doAjax(
          uploadUrl,
          "postformdata",
          hSuccess,
          hError,
          formData
      );
  };

  const handleMassCancelWF = (selectedWorkflowIds, setLoaderMessage, setBlurLoading, setSelectedRows, activeTab, getAllWorkflows) => {
      setBlurLoading(true);
      setLoaderMessage("Cancelling selected workflows...");

      const payload = {
          workflowIds: selectedWorkflowIds
      };

      const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.MASS_CANCEL_WF}`;

      const hSuccess = (data) => {
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(data?.message, "success");
          setSelectedRows([]);
          getAllWorkflows(activeTab)
      };

      const hError = (error) => {
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(error?.message || "Error cancelling workflows", "error");
      };

      doAjax(newUrl, "post", hSuccess, hError, payload);
  };

  const handleMassDownloadWF = (selectedWorkflowIds, setLoaderMessage, setBlurLoading, setSelectedRows, activeTab) => {
      setBlurLoading(true);
      setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);

      const payload = {
          workflowIds: selectedWorkflowIds,
      };

      const newUrl = `/${destination_Admin}${END_POINTS?.WORKFLOW_APIS?.MASS_DOWNLOAD_WF}`;

      const hSuccess = (response) => {
          if (response?.size == 0) {
              setBlurLoading(false);
              setLoaderMessage("");
              showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
              return;
          }
          const href = URL.createObjectURL(response);
          const link = document.createElement("a");

          link.href = href;
          link.setAttribute("download", `Workflows_${ACTIVE_TAB_MODULE_MAP?.[activeTab]}_${new Date().toISOString().split('T')[0]}.xlsx`);
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(`Workflows_${ACTIVE_TAB_MODULE_MAP?.[activeTab]}_${new Date().toISOString().split('T')[0]}.xlsx has been downloaded successfully.`, "success");
          setSelectedRows([])
      };

      const hError = (error) => {
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(error?.message || "Error downloading workflows", "error");
      };

      doAjax(newUrl, "postandgetblob", hSuccess, hError, payload);
  };

  const handleUploadHolidays = (file, setLoaderMessage, setBlurLoading, setEnableHolidayUpload, fetchHolidays) => {
      setBlurLoading(true)
      const formData = new FormData();
      [...file].forEach((item) => formData.append("files", item));

      const hSuccess = (data) => {
          if (data.statusCode === API_CODE?.STATUS_200) {
              setBlurLoading(false);
              setLoaderMessage("");
              showSnackbar(Array.isArray(data.data) || !data?.data ? data?.message : data?.data, "success");
              setEnableHolidayUpload(false)
              fetchHolidays()
          } else {
              setBlurLoading(false);
              setLoaderMessage("");
              showSnackbar(Array.isArray(data.data) || !data?.data ? data?.message : data?.data, "error");
          }
      };
      const hError = (error) => {
          setBlurLoading(false);
          setLoaderMessage("");
          showSnackbar(error.message, "error");
      };
      const uploadUrl = `/${destination_Admin}/api/holidays/getAllHolidaysFromExcel`;
      doAjax(
          uploadUrl,
          "postformdata",
          hSuccess,
          hError,
          formData
      );
  };

  const handleDownloadHolidays = (setLoaderMessage, setBlurLoading) => {
    setLoaderMessage(LOADING_MESSAGE?.REPORT_LOADING);
    setBlurLoading(true);

    const hSuccess = (response) => {
      if (response?.size == 0) {
        setBlurLoading(false);
        setLoaderMessage("");
        showSnackbar(ERROR_MESSAGES?.DATA_NOT_FOUND_FOR_SEARCH, "error");
        return;
      }
      const href = URL.createObjectURL(response);
      const link = document.createElement("a");

      link.href = href;
      link.setAttribute("download", `All_Holidays.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(href);
      setBlurLoading(false);
      setLoaderMessage("");
      showSnackbar(`All_Holidays.xlsx has been downloaded successfully.`, "success");
    };
    const hError = () => {
      setBlurLoading(false);
    };

    doAjax(`/${destination_Admin}/excel/downloadHolidayExcel`, "getblobfile", hSuccess, hError);
  };
  

  return {
      handleDownload,
      handleEmailDownload,
      handleUploadMaterial,
      handleDownloadWF,
      handleUploadWF,
      handleMassCancelWF,
      handleMassDownloadWF,
      handleUploadHolidays,
      handleDownloadHolidays
  };
};

export default useDownloadExcel;
