import React from "react";
import { useNavigate } from "react-router-dom";
import { GroupSummary } from "@cw/groupsummary";
import {APP_END_POINTS} from "@constant/appEndPoints";

const GroupSummaryContainer = () => {
    const navigate = useNavigate();

    const onGroupSummaryActionClick = (action, groupId) => {
        const actionMap = {
            view: () => groupId && navigate(`${APP_END_POINTS.IWA_USER_MANAGEMENT.VIEW_GROUP}?groupId=${groupId}`),
            edit: () => groupId && navigate(`${APP_END_POINTS.IWA_USER_MANAGEMENT.EDIT_GROUP}?groupId=${groupId}`),
            addgroup: () => navigate(APP_END_POINTS.IWA_USER_MANAGEMENT.CREATE_GROUP)
        };

        const handler = actionMap[action.trim()];
        if (handler) {
            handler();
        }
    };

    const dateTimeConfig = {
        dateFormat: "DD-MMM-YYYY",
        timeFormat: "24hr",
    };

    return (
        <>
            <GroupSummary onGroupSummaryActionClick={onGroupSummaryActionClick} app={"IWA"} dateTimeConfig={dateTimeConfig} />
        </>
    );
};

export default GroupSummaryContainer;
