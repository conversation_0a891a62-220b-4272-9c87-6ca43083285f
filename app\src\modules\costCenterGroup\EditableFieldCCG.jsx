import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Box,
  TextField,
  CircularProgress,
  Al<PERSON>,
  Tooltip,
} from "@mui/material";
import moment from "moment";
import { updateDisplayRecords } from "@app/hierarchyDataSlice";
import { logFieldChanges, validateDuplicateEntry } from "@costCenterGroup/hooks/changeLogUtilsCCG";
import { ENABLE_STATUSES } from "@constant/enum";
import { useLocation } from "react-router-dom";

// Steps table configuration
const stepsTable = [
  { label: "New Nodes", value: "1", key: "NEW NODES" },
  { label: "Description Change", value: "2", key: "DESCRIPTIONS" },
  { label: "Add Cost Centers", value: "3", key: "COST CENTERS" },
  { label: "Move Node", value: "4", key: "MOVE NODE" },
  { label: "Move Cost Center", value: "5", key: "MOVE COST CENTER" },
  { label: "Remove Cost Center", value: "6", key: "REMOVE COST CENTER" },
  { label: "Delete Node", value: "7", key: "DELETE NODE" },
  {
    label: "Change Person Responsible",
    value: "8",
    key: "PERSON RESPONSIBLE",
  },
];

const EditableTextFieldCCG = ({
  value,
  placeholder,
  maxLength = 10,
  disabled = false,
  rowId,
  fieldName,
  tableKey,
  fetchOldData = false,
  fetchFunction = null,
  oldDataField = null,
  onNodeValueChange = null,
  addToChangeLog, // Pass this function from parent
  updateDuplicateCheckLists = null, // Pass duplicate check functions
  isDuplicateCheckField = false, // New prop to indicate if this field should be checked for duplicates
  duplicateCheckType = null, // New prop to specify the type of duplicate check ('node' or 'desc')
}) => {
  const dispatch = useDispatch();
  const changeWithUploadRecords = useSelector(
    (state) => state.hierarchyData.DisplayRecords
  );
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const reqBench = queryParams.get("reqBench");
  const userData = useSelector((state) => state.userManagement.userData);
  const [localValue, setLocalValue] = useState(value || "");
  const [charCount, setCharCount] = useState(value?.length || 0);
  const [isFocused, setIsFocused] = useState(false);
  const [loadingOldData, setLoadingOldData] = useState({});
  const [duplicateError, setDuplicateError] = useState("");
  const [showDuplicateWarning, setShowDuplicateWarning] = useState(false);
  const requestStatus = useSelector((state) => state.hierarchyData.requestHeaderData?.RequestStatus);
  const disableCheck = (reqBench && !ENABLE_STATUSES.includes(requestStatus) ) 
  useEffect(() => {
    if (!isFocused) {
      setLocalValue(value || "");
      setCharCount(value?.length || 0);
      setDuplicateError("");
      setShowDuplicateWarning(false);
    }
  }, [value, isFocused]);

  // Determine if this field should be checked for duplicates
  const shouldCheckDuplicates = () => {
    return isDuplicateCheckField && duplicateCheckType && updateDuplicateCheckLists?.checkForDuplicates;
  };

  // Get the duplicate check type for this field
  const getDuplicateCheckType = () => {
    return duplicateCheckType;
  };

  const updateRowData = (updatedFields) => {
    const currentRecords = changeWithUploadRecords[tableKey] || [];
    const oldRow = currentRecords.find(row => row.Id === rowId);
    
    const updatedRecords = currentRecords.map((row) => {
      if (row.Id === rowId) {
        return {
          ...row,
          ...updatedFields,
          "Updated By": userData?.emailId,
          "Updated On": moment().utc().format("YYYY-MM-DD HH:mm:ss.SSS"),
        };
      }
      return row;
    });

    dispatch(
      updateDisplayRecords({
        ...changeWithUploadRecords,
        [tableKey]: updatedRecords,
      })
    );

    // Log the field change if addToChangeLog is provided
    if (addToChangeLog && oldRow) {
      const updatedRow = updatedRecords.find(row => row.Id === rowId);
      if (updatedRow) {
        // Always log field changes, even if other fields are empty
        logFieldChanges(oldRow, updatedRow, tableKey, stepsTable, addToChangeLog, updateDuplicateCheckLists);
      }
    }
  };

  const handleChange = (e) => {
    const newValue = e.target.value.trimStart();
    setLocalValue(newValue);
    setCharCount(newValue.length);

    // Check for duplicates in real-time if applicable
    if (shouldCheckDuplicates() && updateDuplicateCheckLists?.checkForDuplicates) {
      const duplicateType = getDuplicateCheckType();
      if (duplicateType && newValue.trim() !== '') {
        const validation = validateDuplicateEntry(
          newValue.trim(),
          duplicateType,
          updateDuplicateCheckLists.checkForDuplicates
        );
        
        if (!validation.isValid) {
          setDuplicateError(validation.message);
          setShowDuplicateWarning(true);
        } else {
          setDuplicateError("");
          setShowDuplicateWarning(false);
        }
      } else {
        setDuplicateError("");
        setShowDuplicateWarning(false);
      }
    }

    if (onNodeValueChange) {
      onNodeValueChange(newValue);
    }
  };

  const handleBlur = async () => {
    setIsFocused(false);
    const finalValue = localValue.trim();
    const currentFieldValue = localValue;

    // Final duplicate check before saving
    if (shouldCheckDuplicates() && updateDuplicateCheckLists?.checkForDuplicates && finalValue !== '') {
      const duplicateType = getDuplicateCheckType();
      if (duplicateType) {
        const validation = validateDuplicateEntry(
          finalValue,
          duplicateType,
          updateDuplicateCheckLists.checkForDuplicates
        );
        
        if (!validation.isValid) {
          setDuplicateError(validation.message);
          setShowDuplicateWarning(true);
          // Still save the value but with warning
        }
      }
    }

    updateRowData({ [fieldName]: finalValue.toUpperCase() });

    if (fetchOldData && finalValue && fetchFunction && oldDataField) {
      setLoadingOldData(prev => ({ ...prev, [rowId]: true }));
      try {
        const oldValue = await fetchFunction(finalValue);
        if (oldValue !== undefined && oldValue !== null) {
          updateRowData({
            [oldDataField]: oldValue,
            [fieldName]: currentFieldValue.toUpperCase(),
          });
        }
      } catch (error) {
        console.error(`Error fetching old ${oldDataField}:`, error);
      } finally {
        setLoadingOldData(prev => ({ ...prev, [rowId]: false }));
      }
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    setDuplicateError("");
    setShowDuplicateWarning(false);
  };

  // Determine field border color based on validation state
  const getBorderColor = () => {
    if (showDuplicateWarning) return "#ff9800"; // Orange for warning
    if (isFocused && charCount >= maxLength) return "red";
    return "";
  };

  // Get helper text
  const getHelperText = () => {
    if (duplicateError && showDuplicateWarning) {
      return duplicateError;
    }
    if (isFocused) {
      return charCount === maxLength
        ? "Max Length Reached"
        : `${charCount}/${maxLength}`;
    }
    return "";
  };

  // Get helper text color
  const getHelperTextColor = () => {
    if (duplicateError && showDuplicateWarning) return "#ff9800";
    if (isFocused && charCount === maxLength) return "red";
    return "blue";
  };

  const textField = (
    <TextField
      variant="outlined"
      size="small"
      fullWidth
      disabled={disableCheck || disabled || loadingOldData[rowId]}
      value={localValue}
      placeholder={placeholder}
      error={showDuplicateWarning}
      inputProps={{
        style: { textTransform: "uppercase" },
        maxLength: maxLength,
      }}
      onChange={handleChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
      helperText={getHelperText()}
      FormHelperTextProps={{
        sx: {
          color: getHelperTextColor(),
          position: "absolute",
          bottom: "-20px",
          fontSize: "0.75rem",
        },
      }}
      sx={{
        "& .MuiOutlinedInput-root": {
          "&.Mui-focused fieldset": {
            borderColor: getBorderColor(),
          },
          "& fieldset": {
            borderColor: getBorderColor(),
          },
          "&.Mui-error fieldset": {
            borderColor: "#ff9800", // Orange instead of red for warnings
          },
        },
      }}
      onKeyDown={(event) => event.stopPropagation()}
    />
  );

  return (
    <Box sx={{ position: "relative" }}>
      {showDuplicateWarning ? (
        <Tooltip 
          title={duplicateError} 
          arrow 
          placement="top"
          open={showDuplicateWarning}
        >
          {textField}
        </Tooltip>
      ) : (
        textField
      )}
      
      {loadingOldData[rowId] && (
        <CircularProgress
          size={24}
          sx={{
            position: "absolute",
            top: "50%",
            right: 8,
            marginTop: "-12px",
          }}
        />
      )}
      
      {/* Duplicate warning indicator */}
      {showDuplicateWarning && (
        <Box 
          sx={{
            position: "absolute",
            top: -2,
            right: -2,
            width: 8,
            height: 8,
            borderRadius: "50%",
            backgroundColor: "#ff9800",
            border: "2px solid white",
            zIndex: 1,
          }}
        />
      )}
    </Box>
  );
};

export default EditableTextFieldCCG;