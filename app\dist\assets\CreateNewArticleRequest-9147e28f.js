import{c9 as Nc,ca as Cc,cb as _c,aP as Os,n as Z,s as as,u as ol,aX as o,C as We,bI as Fs,aT as Ot,dG as Oc,xi as Ic,r as i,g as Jl,a as Cl,o as to,cz as Gr,ep as Ur,xj as bc,fw as Pt,f1 as fl,j as e,c as f,O as ve,b1 as al,d as st,B as Be,an as nt,bD as Ii,aH as Xl,ai as Hs,aj as qs,al as es,b3 as hl,aa as mn,am as ts,aG as gl,aF as _l,aZ as Vs,aD as ys,bK as ns,aQ as m,xk as Rc,xl as Mc,xm as xc,g3 as Zs,aY as Dc,ae as tl,ap as no,xn as Nl,aC as Lc,wV as yc,H as so,wW as vc,wX as Gc,aJ as Wt,dT as kr,J as lo,xo as $r,be as S,xp as Pr,xq as io,xr as Rl,Z as He,xs as bi,d7 as zl,xt as Uc,fN as kc,aO as Ds,cZ as Gl,bf as Ks,cw as Ri,ah as $c,cI as cs,F as kt,cH as Ls,fR as Vn,fY as el,T as Rn,f_ as ro,ad as Yl,ag as Kl,aE as Ql,ak as Ll,a6 as cn,$ as oo,ez as Pc,fS as pl,au as zt,g1 as Ai,aK as ml,g2 as Ml,fx as pr,g4 as Dl,bE as Mi,dy as pc,bs as Wc,bG as Si,a9 as dl,a5 as Wr,xz as xi,em as pn,ch as yl,ek as zs,xB as xl,k as co,V as ao,A as uo,i as ho,b9 as go,g5 as Di,xu as To,xv as Eo,d0 as Ao,ge as El,M as li,xw as Hc,yt as qc,xx as Bc,xy as Fc,xA as Ws,bi as Hr,eb as _s,yu as qr,xG as Vc,xH as wc,b5 as So,b6 as wl,d5 as mo,d3 as mi,d4 as fi,xE as xs,bm as Wl,bn as Hl,bo as il,bp as je,bq as ql,bl as fo,xJ as ii,er as Pn,bO as jc,bP as Jc,bQ as Br,br as Li,qT as jl,yv as ri,xD as No,xL as oi,xM as Bl,xN as Xc,aU as Cs,xO as Co,xP as zc,xQ as _o,xR as Sl,xS as Al,xT as Fr,dJ as yi,xC as Ni,bh as Qs,fy as Yc,xF as Oo,xI as Kc,xU as Qc,xV as Zc,xW as ea,ao as Io,e3 as Vr,yf as ta,y8 as na,wS as Ci,da as wr,a$ as _i,yg as jr,aA as Fl,aB as Vl,wY as bo,yh as sa,yi as ci,cq as la,a1 as ia,a2 as ra,af as oa,yj as ca,eL as aa,y9 as da,ya as ua,yk as ha,yl as ga,gp as Ta,y4 as Ea,wP as ai,wQ as Aa,wR as Jr,r0 as Sa,wT as ma,xX as di,xY as fa,xZ as Na,I as Ca,x_ as _a,x$ as Oa,wZ as Ia,w_ as ba,y0 as Ra,y1 as Ma,bd as ui,K as xa,aW as Da,bc as La,cA as ya,y2 as va,y3 as Ga,d8 as Ua,d6 as ka,d9 as $a}from"./index-226a1e75.js";import{s as Ys,u as Pa}from"./useMaterialFetchDropdownAndDispatch-8d76dc35.js";import{F as rl}from"./FilterField-868050e3.js";import{u as pa}from"./useProfitcenterRequestHeaderConfig-b7f65f7d.js";import{u as Ro,a as Wa}from"./useChangeMaterialRows-cd4e01b9.js";import{R as Ha,T as Xr,i as qa,u as vi,a as Mo,b as xo,c as Do,d as Oi,S as Ba,o as hi,e as Lo,E as Fa,f as zr,G as Va,j as wa,k as Yr,l as ja,m as Ja,C as Xa,g as za,h as Ya}from"./RequestDetailsForFC-07159606.js";import{D as Ka,C as Kr,e as yo,d as vo,T as gi,M as Qa,f as Za,a as ed,b as Bs,c as td,A as nd,P as sd}from"./PreviewPage-262cf4cb.js";import{u as Gi}from"./useArticleFieldConfig-fe5d02bf.js";import{d as ul}from"./DeleteOutlineOutlined-d41ebb56.js";import{d as vl}from"./Description-d98685cc.js";import{d as ld}from"./TaskAlt-9d2cabf1.js";import{A as Go}from"./AdditionalData-c315b09a.js";import{S as Qt}from"./SingleSelectDropdown-ee61a6b7.js";import{d as Qr,a as Zr}from"./CloseFullscreen-e3b32947.js";import{u as Uo}from"./useDynamicWorkflowDT-7ae52689.js";import{u as ko}from"./useCustomDtCall-f90ca5c1.js";import{d as id}from"./LibraryAdd-cbd81e59.js";import{a as $o,d as rd}from"./FileUploadOutlined-3ff8ee58.js";import{d as od}from"./Remove-98bfc4ee.js";import{d as cd}from"./Edit-3af3a8b3.js";import{G as Ti}from"./GenericViewGeneral-3e4c8862.js";import{d as Po}from"./Delete-3f2fc9ef.js";import{u as po}from"./useFinanceCostingRows-699f667f.js";import{d as ad}from"./PermIdentityOutlined-842d404f.js";import{d as dd}from"./FeedOutlined-2c089703.js";import{D as ud}from"./DatePicker-e5574363.js";import{d as eo}from"./TrackChangesTwoTone-f7d7fb26.js";import{E as hd}from"./ExcelOperationsCard-3cc40005.js";import"./useChangeLogUpdate-23c3e0f8.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./AutoCompleteType-63e88d3d.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./useChangeMaterialRowsRequestor-9caa254c.js";import"./FilterChangeDropdown-2d228e28.js";import"./createChangeLogTemplate-774d7b1c.js";import"./AttachFile-fd8e4fbe.js";import"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";import"./CloudUpload-17ed0189.js";import"./utilityImages-067c3dc2.js";import"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";import"./DataObject-2e0c0294.js";import"./Download-f2e7dedd.js";import"./CheckCircleOutline-70edf41f.js";import"./DeleteOutline-259b9549.js";import"./CloudDownload-23cede9e.js";import"./AttachmentUploadDialog-5b2112e0.js";var Ui={},gd=Cc;Object.defineProperty(Ui,"__esModule",{value:!0});var Wo=Ui.default=void 0,Td=gd(Nc()),Ed=_c;Wo=Ui.default=(0,Td.default)((0,Ed.jsx)("path",{d:"m19.07 4.93-1.41 1.41C19.1 7.79 20 9.79 20 12c0 4.42-3.58 8-8 8s-8-3.58-8-8c0-4.08 3.05-7.44 7-7.93v2.02C8.16 6.57 6 9.03 6 12c0 3.31 2.69 6 6 6s6-2.69 6-6c0-1.66-.67-3.16-1.76-4.24l-1.41 1.41C15.55 9.9 16 10.9 16 12c0 2.21-1.79 4-4 4s-4-1.79-4-4c0-1.86 1.28-3.41 3-3.86v2.14c-.6.35-1 .98-1 1.72 0 1.1.9 2 2 2s2-.9 2-2c0-.74-.4-1.38-1-1.72V2h-1C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10c0-2.76-1.12-5.26-2.93-7.07"}),"TrackChanges");const Ho=()=>{const{customError:n}=Os(),N=Z(Se=>Se.payload.payloadData),oe=Z(Se=>Se.applicationConfig),I=Z(Se=>{var ce;return(ce=Se.userManagement)==null?void 0:ce.taskData}),M=as(),x=ol(),D=new URLSearchParams(x.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var R,_,j;let Se={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(I==null?void 0:I.ATTRIBUTE_2)===((R=o)==null?void 0:R.FINANCE_COSTING)?(_=o)==null?void 0:_.FINANCE_COSTING:D||(N==null?void 0:N.RequestType)||((j=o)==null?void 0:j.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(N==null?void 0:N.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Article"}],systemFilters:null,systemOrders:null,filterString:null};const ce=u=>{var ie,W;if(u.statusCode===200){const K={"Header Data":((W=(ie=u==null?void 0:u.data)==null?void 0:ie.result[0])==null?void 0:W.MDG_MAT_REQUEST_HEADER_CONFIG).sort((De,Fe)=>De.MDG_MAT_SEQUENCE_NO-Fe.MDG_MAT_SEQUENCE_NO).map(De=>({fieldName:De.MDG_MAT_UI_FIELD_NAME,sequenceNo:De.MDG_MAT_SEQUENCE_NO,fieldType:De.MDG_MAT_FIELD_TYPE,maxLength:De.MDG_MAT_MAX_LENGTH,value:De.MDG_MAT_DEFAULT_VALUE,visibility:De.MDG_MAT_VISIBILITY,jsonName:De.MDG_MAT_JSON_FIELD_NAME}))};M(Oc({tab:"Request Header",data:K})),M(Ic(K))}},z=u=>{n(u)};oe.environment==="localhost"?We(`/${Fs}${Ot.INVOKE_RULES.LOCAL}`,"post",ce,z,Se):We(`/${Fs}${Ot.INVOKE_RULES.PROD}`,"post",ce,z,Se)}}},Ad=({setIsSecondTabEnabled:n,setIsAttachmentTabEnabled:N,requestStatus:oe,downloadClicked:I,setDownloadClicked:M})=>{var an,de,vt,Jn,Xn,Tn,Et,fn;const[x,ue]=i.useState({}),[D,E]=i.useState(!1),[Se,ce]=i.useState(!1),[z,R]=i.useState("success"),[_,j]=i.useState(!1),[u,ie]=i.useState([]),[W,V]=i.useState(),[a,K]=i.useState({}),[De,Fe]=i.useState(!1),[ze,ge]=i.useState("systemGenerated"),[Te,ee]=i.useState(""),[re,H]=i.useState(""),[fe,Q]=i.useState([]),[Re,he]=i.useState(!1),te=as(),Mt=Jl(),T=Z(P=>P.payload.payloadData),J=Z(P=>P.tabsData.requestHeaderData),p=Z(P=>P.tabsData.changeFieldsDT);let Ce=Z(P=>P.userManagement.roles);const L=Z(P=>P.payload.payloadData),q=Z(P=>P.userManagement.userData),b=Z(P=>{var A,Ae;return(Ae=(A=P.userManagement)==null?void 0:A.entitiesAndActivities)==null?void 0:Ae.Material}),se=Z(P=>P.request.requestHeader),O=Z(P=>P.request.salesOrgDTData),mt=ol(),Le=new URLSearchParams(mt.search),Oe=Le.get("reqBench"),ht=Le.get("RequestId"),{t:It}=Cl(),{getRequestHeaderTemplate:bt}=Ho(),{getChangeTemplate:dt}=Ro();pa();const{fetchOrgData:tn}=Gi(),{getDtCall:Ct}=to(),{customError:ft}=Os(),Jt=[{code:"Create",desc:"Create New Article in Application"},{code:"Change",desc:"Modify Existing Article in Application"},{code:"Extend",desc:"Extend Existing Article in Application"},{code:"Create with Upload",desc:"Create New Article with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Article with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Article with Excel Upload"}].filter(P=>b==null?void 0:b.includes(P.code)),Rt=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],gt=[{code:(an=Pt)==null?void 0:an.LOGISTIC,desc:""},{code:(de=Pt)==null?void 0:de.MRP,desc:""},{code:(vt=Pt)==null?void 0:vt.WARE_VIEW_2,desc:""},{code:(Jn=Pt)==null?void 0:Jn.ITEM_CAT,desc:""},{code:(Xn=Pt)==null?void 0:Xn.SET_DNU,desc:""},{code:(Tn=Pt)==null?void 0:Tn.UPD_DESC,desc:""},{code:(Et=Pt)==null?void 0:Et.CHG_STAT,desc:""}],Ie=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];te(Ys({keyName:(fn=Gr)==null?void 0:fn.REQUEST_TYPE,data:Jt})),te(Ys({keyName:"LeadingCat",data:Rt})),te(Ys({keyName:"RequestPriority",data:Ie})),te(Ys({keyName:"TemplateName",data:gt})),!ht&&!Oe&&(te(Ur({keyName:"ReqCreatedBy",data:q==null?void 0:q.user_id})),te(Ur({keyName:"RequestStatus",data:"DRAFT"})));const pe="Basic Data",[Y,ne]=i.useState([pe]);i.useState(""),i.useState("");const[Ge,Je]=i.useState(!0);i.useEffect(()=>{te(bc(Y))},[te,Y]);const Tt=()=>{var A,Ae;let P=!0;return L&&((A=J[Object.keys(J)])!=null&&A.length)?(Ae=J[Object.keys(J)[0]])==null||Ae.forEach(Me=>{var le;!L[Me.jsonName]&&Me.visibility===((le=ys)==null?void 0:le.MANDATORY)&&(P=!1)}):P=!1,P},v=()=>{j(!0)},ke=()=>{j(!1)},yt=()=>{var P;M(!1),Fe(!1),ge("systemGenerated"),ht||Mt((P=ns)==null?void 0:P.REQUEST_BENCH)},Ln=P=>{var A;ge((A=P==null?void 0:P.target)==null?void 0:A.value)},Zt=()=>{ze==="systemGenerated"&&(nn(),yt()),ze==="mailGenerated"&&(Ht(),yt())},nn=()=>{ee("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),H(!0);let P={region:T==null?void 0:T.Region,scenario:T==null?void 0:T.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:se!=null&&se.requestId?se==null?void 0:se.requestId:T!=null&&T.RequestId?T==null?void 0:T.RequestId:""};const A=le=>{if((le==null?void 0:le.size)==0){H(!1),ee(""),ce(!0),V("No data found for the selected criteria."),R("danger"),v();return}const Ve=URL.createObjectURL(le),$e=document.createElement("a");$e.href=Ve,$e.setAttribute("download",`${(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild($e),$e.click(),document.body.removeChild($e),URL.revokeObjectURL(Ve),H(!1),ee(""),ce(!0),V(`${T!=null&&T.TemplateName?`${T.TemplateName}_Mass Change`:(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),R("success"),v(),setTimeout(()=>{Mt("/requestBench")},2600)},Ae=()=>{H(!1)},Me=`/${m}${(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD?Ot.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:Ot.EXCEL.DOWNLOAD_EXCEL}`;We(Me,"postandgetblob",A,Ae,P)},Ht=()=>{H(!0);let P={region:T==null?void 0:T.Region,scenario:T==null?void 0:T.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:se!=null&&se.requestId?se==null?void 0:se.requestId:T!=null&&T.RequestId?T==null?void 0:T.RequestId:""};const A=()=>{var le;H(!1),ee(""),ce(!0),V((le=Dc)==null?void 0:le.DOWNLOAD_MAIL_INITIATED),R("success"),v(),setTimeout(()=>{var Ve;Mt((Ve=ns)==null?void 0:Ve.REQUEST_BENCH)},2600)},Ae=()=>{var le;H(!1),ce(!0),V((le=tl)==null?void 0:le.ERR_DOWNLOADING_EXCEL),R("danger"),v(),setTimeout(()=>{var Ve;Mt((Ve=ns)==null?void 0:Ve.REQUEST_BENCH)},2600)},Me=`/${m}${(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD?Ot.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:Ot.EXCEL.DOWNLOAD_EXCEL_MAIL}`;We(Me,"post",A,Ae,P)},lt=()=>E(!1),$t=P=>{if(u.includes("Distribution Channel")){const A=Me=>Q(Me==null?void 0:Me.body),Ae=Me=>ft(Me);We(`/${m}/data/getDistrChan?salesOrg=${P.code}`,"get",A,Ae)}},Ft={orgData:["Plant","Sales Organization","Distribution Channel"].map(P=>({info:a[P]||{code:"",desc:""},desc:P})),selectedViews:{selectedSections:Y}},yn=(P,A)=>{K(Ae=>({...Ae,[P]:A})),P==="Sales Organization"&&$t(A)},xt=`/Date(${Date.now()})/`,[rn,vn]=i.useState(!1),Gn=()=>{var Ve;vn(!0);let P=Rc(L==null?void 0:L.Region,Ce);te(Mc({...q,role:P})),he(!1);const A=new Date(L==null?void 0:L.ReqCreatedOn).getTime(),Ae={RequestId:se!=null&&se.requestId?se==null?void 0:se.requestId:"",Region:(L==null?void 0:L.Region)||"",MatlType:(L==null?void 0:L.MatlType)||"",ReqCreatedBy:(q==null?void 0:q.user_id)||"",ReqCreatedOn:A?`/Date(${A})/`:xt,ReqUpdatedOn:A?`/Date(${A})/`:xt,RequestType:(L==null?void 0:L.RequestType)||"",RequestDesc:(L==null?void 0:L.RequestDesc)||"",Division:(L==null?void 0:L.Division)||"",RequestStatus:"DRAFT",RequestPriority:(L==null?void 0:L.RequestPriority)||"",LeadingCat:(L==null?void 0:L.LeadingCat)||"",FieldName:((Ve=L==null?void 0:L.FieldName)==null?void 0:Ve.join("$^$"))||"",TemplateName:(L==null?void 0:L.TemplateName)||"",Json301:(L==null?void 0:L.Json301)||""},Me=$e=>{var Vt,_e,Nn;if(vn(!1),ce(!0),V($e==null?void 0:$e.message),R("success"),v(),te(no($e.body)),te(fl({keyName:Gr.REQUEST_ID,data:(Vt=$e==null?void 0:$e.body)==null?void 0:Vt.requestId})),N(!0),Je(!1),te(Nl({})),te(Lc({})),(T==null?void 0:T.RequestType)===o.CREATE_WITH_UPLOAD||(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD){Fe(!0);return}if((T==null?void 0:T.RequestType)===((_e=o)==null?void 0:_e.CHANGE_WITH_UPLOAD)){he(!0);return}if((T==null?void 0:T.RequestType)===((Nn=o)==null?void 0:Nn.CHANGE)){const be=yc(p==null?void 0:p["Config Data"],T==null?void 0:T.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);te(so({...p,"Config Data":be}));const wt=vc(p==null?void 0:p[T==null?void 0:T.TemplateName],T==null?void 0:T.FieldName);te(Gc([...wt]))}setTimeout(()=>{te(Zs(1)),n(!0)},2500)},le=()=>{vn(!1),ce(!0),R("error"),V("Error occured while saving Request Header"),v()};We(`/${m}/alter/createRequestHeader`,"post",Me,le,Ae)};i.useEffect(()=>{var P;if(I){if((T==null?void 0:T.RequestType)===o.CREATE_WITH_UPLOAD||(T==null?void 0:T.RequestType)===o.EXTEND_WITH_UPLOAD){Fe(!0);return}if((T==null?void 0:T.RequestType)===((P=o)==null?void 0:P.CHANGE_WITH_UPLOAD)){he(!0);return}}},[I]);function Xt(P){return P.every(A=>A.info.code&&A.info.desc)}const we=()=>{if(!Xt(Ft.orgData))ce(!0),R("error"),V("Please choose all mandatory fields"),v();else{const A={label:"Attachments & Comments",value:"attachments&comments"},Me=[{label:"General Information",value:"generalInformation"},...Y,A];Ft.selectedViews=Me,te(xc(Ft)),te(Zs(1)),n(!0)}};i.useEffect(()=>{bt()},[T==null?void 0:T.RequestType]);const on=(P="")=>{var le,Ve,$e,Vt;const A={materialNo:P??"",top:500,skip:0,salesOrg:((Ve=(le=O==null?void 0:O.uniqueSalesOrgList)==null?void 0:le.map(_e=>_e.code))==null?void 0:Ve.join("$^$"))||""},Ae=_e=>{(_e==null?void 0:_e.statusCode)===Wt.STATUS_200&&(te(Ys({keyName:kr.RETURN_MAT_NUMBER,data:_e==null?void 0:_e.body})),te(Ys({keyName:kr.PARENT_MAT_NUMBER,data:_e==null?void 0:_e.body})))},Me=_e=>{ft(_e)};We(`/${m}${(Vt=($e=Ot)==null?void 0:$e.DATA)==null?void 0:Vt.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",Ae,Me,A)};i.useEffect(()=>{O!=null&&O.uniqueSalesOrgList&&on()},[]);const Qn=P=>{let A={decisionTableId:null,decisionTableName:lo.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":P||""}]};Ct(A)};return i.useEffect(()=>{T!=null&&T.Region&&(tn(),Qn(T==null?void 0:T.Region))},[T==null?void 0:T.Region]),i.useEffect(()=>{T!=null&&T.TemplateName&&(((T==null?void 0:T.TemplateName)===Pt.MRP||(T==null?void 0:T.TemplateName)===Pt.WARE_VIEW_2)&&te(fl({keyName:"FieldName",data:void 0})),dt())},[T==null?void 0:T.TemplateName]),e("div",{children:f(Vs,{spacing:2,children:[Object.entries(J).map(([P,A])=>f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:It(P)}),e(Be,{children:e(ve,{container:!0,spacing:1,children:A.filter(Ae=>Ae.visibility!=="Hidden").sort((Ae,Me)=>Ae.sequenceNo-Me.sequenceNo).map(Ae=>e(rl,{isHeader:!0,field:Ae,dropDownData:x,disabled:ht||(se==null?void 0:se.requestId),requestHeader:!0},Ae.id))})}),!ht&&!(se!=null&&se.requestId)&&e(Be,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:e(nt,{variant:"contained",color:"primary",disabled:rn||!Tt(),onClick:Gn,startIcon:rn?e(Ii,{size:20,color:"inherit"}):null,children:It("Save Request Header")})}),e(Xl,{})]},P)),f(Hs,{open:D,onClose:lt,children:[e(qs,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),e(es,{children:e(ve,{container:!0,columnSpacing:1,children:u.map((P,A)=>f(i.Fragment,{children:[e(ve,{item:!0,md:4,children:f(st,{children:[P,e("span",{style:{color:"red"},children:"*"})]})}),e(ve,{item:!0,md:8,children:e(hl,{options:P==="Distribution Channel"?fe:x[P]||[],size:"small",getOptionLabel:Ae=>`${Ae.code} - ${Ae.desc}`,renderOption:(Ae,Me)=>e("li",{...Ae,children:e(st,{children:`${Me.code} - ${Me.desc}`})}),onChange:(Ae,Me)=>yn(P,Me),renderInput:Ae=>e(mn,{...Ae,placeholder:`Select ${P}`})})})]},A))})}),f(ts,{children:[e(nt,{onClick:lt,variant:"outlined",children:It("Cancel")}),e(nt,{variant:"contained",onClick:()=>{we()},children:It("Proceed")})]})]}),Re&&e(Ha,{downloadClicked:I,setDownloadClicked:M}),e(Ka,{onDownloadTypeChange:Zt,open:De,downloadType:ze,handleDownloadTypeChange:Ln,onClose:yt}),e(gl,{blurLoading:re,loaderMessage:Te}),Se&&e(_l,{openSnackBar:_,alertMsg:W,alertType:z,handleSnackBarClose:ke})]})})},Sd=(n,N,oe,I,M)=>{let x=Z(D=>D.userManagement.taskData);return{checkValidation:(D,E,Se,ce,z)=>{var Fe,ze,ge,Te,ee,re,H,fe,Q,Re,he,te,Mt,T,J,p,Ce,L;const R=(Fe=n==null?void 0:n[D])==null?void 0:Fe.payloadData,_=(ze=n==null?void 0:n[D])==null?void 0:ze.headerData;(ge=n==null?void 0:n[D])==null||ge.ManufacturerID;const j=(Te=n==null?void 0:n.payloadData)==null?void 0:Te.Region,u=(x==null?void 0:x.taskDesc)!=="Generic Article Activation Task"||!x||Object.keys(x).length===0;if(!(_!=null&&_.materialNumber)&&!u)return{missingFields:["Article number"],isValid:!1};if(!(_!=null&&_.materialType))return{missingFields:["Article Type"],isValid:!1};if(!(_!=null&&_.articleCategory))return{missingFields:["Article Category"],isValid:!1};if(!((ee=_==null?void 0:_.apparelMcat)!=null&&ee.code))return{missingFields:["Merchandising Category"],isValid:!1};if(!(_!=null&&_.globalMaterialDescription)||!R)return{missingFields:["Article Description"],isValid:!1};if((_.articleCategory.code=="11"||_.articleCategory=="11")&&!(Object.keys((_==null?void 0:_.articleComponents)??{}).length>0)||!R)return{missingFields:["Components"],isValid:!1};const ie=$r(R[S.BASIC_DATA]),W=$r(R[S.SUPPLIER_FORM]);ie.Material=_==null?void 0:_.materialNumber,ie.MatlDesc=_==null?void 0:_.globalMaterialDescription;const V=N==null?void 0:N.find(q=>{var b;return(q==null?void 0:q[j])&&(q==null?void 0:q[j][(b=_==null?void 0:_.materialType)==null?void 0:b.code])}),a=V&&V[j]&&((H=V[j][(re=_==null?void 0:_.materialType)==null?void 0:re.code])==null?void 0:H.mandatoryFields),K=a==null?void 0:a[S.BASIC_DATA];if((K==null?void 0:K.length)>0){for(const q of K)if(!ie[q==null?void 0:q.jsonName])return{missingFields:Pr(K,ie),viewType:S.BASIC_DATA,isValid:!1,plant:[S.BASIC_DATA]}}const De=a==null?void 0:a[S.SUPPLIER_FORM];if(oe.includes(S.SUPPLIER_FORM)&&(De==null?void 0:De.length)>0){for(const q of De)if(!W[q==null?void 0:q.jsonName])return{missingFields:Pr(De,W),viewType:S.SUPPLIER_FORM,isValid:!1,plant:[S.SUPPLIER_FORM]}}for(const q of oe){const b=io(E),{displayCombinations:se}=(b==null?void 0:b[q])||{};if(se&&se[0]&&(se==null?void 0:se.length)>0){const O=a==null?void 0:a[q];if(O){let mt={};for(const Le of se){const Oe=(fe=R[q])==null?void 0:fe[Le];if(Oe){const ht=Rl(O,Oe);((Q=Object.keys(ht))==null?void 0:Q.length)>0&&(mt[Le]=Object.keys(ht))}else mt[Le]=O.map(ht=>ht.fieldName);if(((Re=Object.keys(mt))==null?void 0:Re.length)>0)return{missingFields:mt,viewType:q,isValid:!1,plant:Object.keys(mt)}}}}}if(oe.includes(S.SALES)){const q=a==null?void 0:a[S.SALES_GENERAL];let b={};if(q&&R[S.SALES_GENERAL]){const se=Rl(q,(he=R[S.SALES_GENERAL])==null?void 0:he[S.SALES_GENERAL]);Object.keys(se).length>0&&(b[S.SALES_GENERAL]=Object.keys(se))}else q&&(b[S.SALES_GENERAL]=q.map(se=>se.fieldName));if(((te=Object.keys(b))==null?void 0:te.length)>0)return{missingFields:b,viewType:S.SALES,isValid:!1,plant:[S.SALES_GENERAL]}}if(oe.includes(S.SUPPLIER_FORM)){const q=a==null?void 0:a[S.SUPPLIER_FORM];let b={};if(q&&R[S.SUPPLIER_FORM]){const se=Rl(q,(Mt=R[S.SUPPLIER_FORM])==null?void 0:Mt.basic);Object.keys(se).length>0&&(b[S.SUPPLIER_FORM]=Object.keys(se))}else q&&(b[S.SUPPLIER_FORM]=q.map(se=>se.fieldName));if(((T=Object.keys(b))==null?void 0:T.length)>0)return{missingFields:b,viewType:S.SUPPLIER_FORM,isValid:!1,plant:[S.SUPPLIER_FORM]}}if(oe.includes(S.PURCHASING)){const q=a==null?void 0:a[S.PURCHASING_GENERAL];let b={};if(q&&R[S.PURCHASING_GENERAL]){const se=Rl(q,(J=R[S.PURCHASING_GENERAL])==null?void 0:J[S.PURCHASING_GENERAL]);Object.keys(se).length>0&&(b[S.PURCHASING_GENERAL]=Object.keys(se))}else q&&(b[S.PURCHASING_GENERAL]=q.map(se=>se.fieldName));if(((p=Object.keys(b))==null?void 0:p.length)>0)return{missingFields:b,viewType:S.PURCHASING,isValid:!1,plant:[S.PURCHASING_GENERAL]}}if(oe.includes(S.STORAGE)){const q=a==null?void 0:a[S.STORAGE_GENERAL];let b={};if(q&&R[S.STORAGE_GENERAL]){const se=Rl(q,(Ce=R[S.STORAGE_GENERAL])==null?void 0:Ce[S.STORAGE_GENERAL]);Object.keys(se).length>0&&(b[S.STORAGE_GENERAL]=Object.keys(se))}else q&&(b[S.STORAGE_GENERAL]=q.map(se=>se.fieldName));if(((L=Object.keys(b))==null?void 0:L.length)>0)return{missingFields:b,viewType:S.STORAGE,isValid:!1,plant:[S.STORAGE_GENERAL]}}return{missingFields:null,isValid:!0}}}},qo=({open:n,onClose:N,title:oe,lengthOfOrgRow:I,selectedMaterialPayload:M,materialID:x,orgRows:ue})=>{var R,_;const[D,E]=i.useState({}),Se=as(),ce=()=>{const j=[];return ue&&ue.length>0&&(ue==null||ue.forEach((u,ie)=>{var W,V,a,K,De,Fe,ze,ge,Te,ee,re,H,fe,Q;if(ie!==(I==null?void 0:I.copyFor)){const Re=(V=(W=u.plant)==null?void 0:W.value)==null?void 0:V.code,he=((K=(a=u.plant)==null?void 0:a.value)==null?void 0:K.desc)||Re,te=(De=u.salesOrg)==null?void 0:De.code,Mt=((Fe=u.salesOrg)==null?void 0:Fe.desc)||te,T=(ge=(ze=u.dc)==null?void 0:ze.value)==null?void 0:ge.code,J=((ee=(Te=u.dc)==null?void 0:Te.value)==null?void 0:ee.desc)||T,p=(H=(re=u.warehouse)==null?void 0:re.value)==null?void 0:H.code,Ce=((Q=(fe=u.warehouse)==null?void 0:fe.value)==null?void 0:Q.desc)||p;if(Re){let L=`Plant: ${he||"N/A"}`;te&&(L+=` | SalesOrg: ${Mt||"N/A"}`),T&&(L+=` | DC: ${J||"N/A"}`),p&&(L+=` | Warehouse: ${Ce||"N/A"}`);let q=Re;te&&(q+=`-${te}`),T&&(q+=`-${T}`),p&&(q+=`-${p}`),j==null||j.push({code:q,desc:L,index:ie,plant:Re,salesOrg:te,dc:T,warehouse:p})}}})),j},z=()=>{var K,De,Fe,ze,ge,Te,ee,re;if(!D.code)return;const j=ue[I.copyFor],u=(De=(K=j==null?void 0:j.plant)==null?void 0:K.value)==null?void 0:De.code,ie=(Fe=j==null?void 0:j.salesOrg)==null?void 0:Fe.code,W=(ge=(ze=j==null?void 0:j.dc)==null?void 0:ze.value)==null?void 0:ge.code,V=(ee=(Te=j==null?void 0:j.warehouse)==null?void 0:Te.value)==null?void 0:ee.code;if(!u)return;const a=JSON.parse(JSON.stringify(M));(re=Object.keys(a))==null||re.forEach(H=>{const fe=a[H];if(!(H===S.BASIC_DATA||H===S.SALES_GENERAL||H===S.PURCHASING_GENERAL||H===S.TAX_DATA)&&typeof fe=="object"){const Q=Object.keys(fe);if(H===S.WAREHOUSE){const Re=Q==null?void 0:Q.find(te=>te.includes(D.warehouse)),he=Q==null?void 0:Q.find(te=>te.includes(V));if(Re&&he&&he!==Re){const te=JSON.parse(JSON.stringify(fe[Re]));delete te.WarehouseId,a[H][he]={...JSON.parse(JSON.stringify(a[H][he]||{})),...te}}}else if(H===S.SALES){const Re=`${D.salesOrg}-${D.dc}`,he=`${ie}-${W}`,te=Q==null?void 0:Q.find(T=>T===Re),Mt=Q==null?void 0:Q.find(T=>T===he);if(te&&Mt&&Mt!==te){const T=JSON.parse(JSON.stringify(fe[te]));delete T.SalesId,a[H][Mt]={...JSON.parse(JSON.stringify(a[H][Mt]||{})),...T}}}else{const Re=Q==null?void 0:Q.find(te=>te.includes(D.plant)),he=Q==null?void 0:Q.find(te=>te.includes(u));if(Re&&he&&he!==Re){const te=JSON.parse(JSON.stringify(fe[Re]));te&&(delete te.SalesId,delete te.PlantId,delete te.StorageLocationId,delete te.AccountingId,he&&(a[H][he]={...JSON.parse(JSON.stringify(a[H][he]||{})),...te}))}}}}),Se(Uc({materialID:x,data:a})),N()};return f(zl,{isOpen:n,titleIcon:e(vl,{size:"small",sx:{color:(_=(R=He)==null?void 0:R.primary)==null?void 0:_.dark,fontSize:"20px"}}),Title:oe,handleClose:()=>N(),children:[f(es,{sx:{mt:2},children:[e(st,{sx:{mb:2},children:bi.COPY_ORG_DATA_VALUES}),e(Qt,{options:ce(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:j=>E(j),value:D})]}),e(ts,{children:e(nt,{variant:"contained",size:"small",onClick:()=>z(),children:"Ok"})})]})},md=(n,N,oe,I,M)=>{const{customError:x}=Os(),[ue,D]=i.useState([]),[E,Se]=i.useState([]),[ce,z]=i.useState(!1),R=new URLSearchParams(location.search),_=kc[M]||(()=>({})),j=Z(_),u=Z(W=>{var V,a;return(a=(V=W==null?void 0:W.userManagement)==null?void 0:V.taskData)==null?void 0:a.ATTRIBUTE_2}),ie=R.get("RequestType");return i.useEffect(()=>{let W={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":M===Ds.BOM?"BOM":"Article","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":ie||u||(j==null?void 0:j.RequestType)}],systemFilters:null,systemOrders:null,filterString:null};const V=De=>{var Fe,ze;De.statusCode===200&&D((ze=(Fe=De==null?void 0:De.data)==null?void 0:Fe.result[0])==null?void 0:ze.MDG_MAT_DYN_BUTTON_CONFIG)},a=De=>{x(De)},K=N.environment==="localhost"?`/${oe}/rest/v1/invoke-rules`:`/${oe}/v1/invoke-rules`;We(K,"post",V,a,W)},[n]),i.useEffect(()=>{const W=Gl(Ks.CURRENT_TASK,!0,{}),V=(W==null?void 0:W.taskDesc)||(n==null?void 0:n.taskDesc),K=ue.filter(De=>De.MDG_MAT_DYN_BTN_TASK_NAME===V).sort((De,Fe)=>{const ze=Kr[De.MDG_MAT_DYN_BTN_ACTION_TYPE]??999,ge=Kr[Fe.MDG_MAT_DYN_BTN_ACTION_TYPE]??999;return ze-ge});Se(K),(K.find(De=>De.MDG_MAT_DYN_BTN_BUTTON_NAME===I.SEND_BACK)||K.find(De=>De.MDG_MAT_DYN_BTN_BUTTON_NAME===I.CORRECTION))&&z(!0)},[ue]),{filteredButtons:E,showWfLevels:ce}},Bo=n=>{var is,sn,En,Ye,it,pt,Yt,ln,jt,wn,Yn,jn,hs,ms,_n,gs,Zn,kn,fs,bs,Rs,ws,Bn,ut,On,vs;const N=Z($=>$.payload),oe=Z($=>$.payload.dynamicKeyValues),I=Z($=>$.payload.changeFieldRows),M=Z($=>$.payload.selectedRows),x=(is=N==null?void 0:N.payloadData)!=null&&is.data?(En=(sn=N==null?void 0:N.payloadData)==null?void 0:sn.data)==null?void 0:En.RequestType:(Ye=N==null?void 0:N.payloadData)==null?void 0:Ye.RequestType,[ue,D]=i.useState(!1),[E,Se]=i.useState("success"),[ce,z]=i.useState(!1),[R,_]=i.useState(""),[j,u]=i.useState(""),[ie,W]=i.useState(!1),[V,a]=i.useState(""),[K,De]=i.useState(!1),[Fe,ze]=i.useState(""),[ge,Te]=i.useState(""),[ee,re]=i.useState(!1),[H,fe]=i.useState([]),[Q,Re]=i.useState([]),[he,te]=i.useState(!1),[Mt,T]=i.useState(!1),{t:J}=Cl(),[p,Ce]=i.useState(!1),[L,q]=i.useState(!1),[b,se]=i.useState(""),[O,mt]=i.useState(!1),[Le,Oe]=i.useState(!1),[ht,It]=i.useState(""),[bt,dt]=i.useState(""),[tn,Ct]=i.useState(!1),[ft,gn]=i.useState({}),[Jt,Rt]=i.useState(""),[gt,Ie]=i.useState("");let pe=Z($=>$.userManagement.userData),Y=Z($=>$.userManagement.taskData);const ne=Jl(),Ge=as(),Je=ol(),Tt=Je.state,v=Z($=>$.payload.payloadData),ke=Z($=>$.payload.requestorPayload),yt=Z($=>$.tabsData.changeFieldsDT),Ln=(v==null?void 0:v.TemplateName)||"",{changePayloadForTemplate:Zt}=yo(Ln),nn=new URLSearchParams(Je.search.split("?")[1]),Ht=nn.get("RequestId"),lt=nn.get("reqBench"),$t=Gl(Ks.CURRENT_TASK),Ft=typeof $t=="string"?JSON.parse($t):$t,yn=!(Y!=null&&Y.taskId||Ft!=null&&Ft.ATTRIBUTE_5)&&!lt,[xt,rn]=i.useState(!1),{customError:vn}=Os(),{createFCPayload:Gn}=po(),{createPayloadFromReduxState:Xt}=vo({initialReqScreen:yn,isReqBench:lt,remarks:Fe,userInput:b,selectedLevel:ge}),[we,on]=i.useState(!1),[Qn,an]=i.useState(!1),[de,vt]=i.useState(!1),[Jn,Xn]=i.useState(""),Tn=((it=N==null?void 0:N.payloadData)==null?void 0:it.RequestType)===o.CREATE_WITH_UPLOAD||((pt=N==null?void 0:N.payloadData)==null?void 0:pt.RequestType)===o.EXTEND_WITH_UPLOAD||((Yt=N==null?void 0:N.payloadData)==null?void 0:Yt.RequestType)===o.CHANGE_WITH_UPLOAD,{showSnackbar:Et}=Ri(),fn=Z($=>$.request.tabValue),P=200,A=i.useRef(),Ae=()=>{z(!0)},Me=()=>{z(!1)},le=()=>{T(!0)},Ve=()=>{T(!1)},$e=()=>{var $;gt===Vn.SAVE?(Ve(),wt()):gt===(($=Vn)==null?void 0:$.VALIDATE)&&(Ve(),_t())},Vt=()=>{De(!0)},_e=()=>{ze(""),De(!1)},Nn=($,Ke)=>{const Qe=$.target.value;if(on(Qe.length>=P),Qe.length>0&&Qe[0]===" ")ze(Qe.trimStart()),Ge(pl({keyName:"Comments",data:Qe.trimStart()}));else{let Ze=Qe;ze(Ze),Ge(pl({keyName:"Comments",data:Ze}))}},be=$=>{Te($.target.value),Ge(pl({keyName:"Level",data:$.target.value}))},wt=()=>{var U,et,qe,rt,Kt,In,At,St,Fn,Ts,Es,Gs,Us;_e(),W(!0);var $;((U=N==null?void 0:N.payloadData)==null?void 0:U.RequestType)===o.CREATE||((et=N==null?void 0:N.payloadData)==null?void 0:et.RequestType)===o.CREATE_WITH_UPLOAD?gt===Vn.SAVE?$=`/${zt}/massAction/createMaterialSaveAsDraft`:$=(pe==null?void 0:pe.role)==="Approver"?`/${zt}/massAction/createBasicMaterialsApproved`:`/${zt}/massAction/createMaterialSubmitForReview`:((qe=N==null?void 0:N.payloadData)==null?void 0:qe.RequestType)===o.EXTEND_WITH_UPLOAD?gt===Vn.SAVE?$=`/${zt}${Ot.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`:$=`/${zt}${Ot.MASS_ACTION.EXTEND_MATERIAL_DIRECT_APPROVED}`:(x===o.CHANGE||x===o.CHANGE_WITH_UPLOAD)&&(gt===Vn.SAVE?$=`/${zt}/massAction/changeMaterialSaveAsDraft`:$=(pe==null?void 0:pe.role)==="Approver"?`/${zt}/massAction/changeBasicMaterialsApproved`:`/${zt}/massAction/changeMaterialSubmitForReview`);const Ke=l=>{if(l.statusCode>=Wt.STATUS_200&&l.statusCode<Wt.STATUS_300){W(!1);let c;(pe==null?void 0:pe.role)==="Approver"?c=`Article Syndicated successfully in SAP with Article ID : ${l==null?void 0:l.body.join(", ")}`:gt===Vn.SAVE?c=l==null?void 0:l.message:c=`Request Submitted for Approval with Request ID ${l==null?void 0:l.body}`,Et(c,"success"),Ae(),ne("/masterDataCockpit/materialMaster/material")}else W(!1),Et(l==null?void 0:l.message,"error");Ie("")},Qe=l=>{Et(l==null?void 0:l.message,"error"),W(!1),Ie("")};var Ze;Ze=((rt=N==null?void 0:N.payloadData)==null?void 0:rt.RequestType)===o.CREATE||((Kt=N==null?void 0:N.payloadData)==null?void 0:Kt.RequestType)===o.CREATE_WITH_UPLOAD||((In=N==null?void 0:N.payloadData)==null?void 0:In.RequestType)===o.EXTEND_WITH_UPLOAD?Xt(N):((At=N==null?void 0:N.payloadData)==null?void 0:At.RequestType)===o.CHANGE?Zt(!!lt):(Fn=(St=N==null?void 0:N.payloadData)==null?void 0:St.data)!=null&&Fn.RequestType||((Ts=N==null?void 0:N.payloadData)==null?void 0:Ts.RequestType)===o.CHANGE_WITH_UPLOAD?Zt(!0):(Gs=(Es=N==null?void 0:N.payloadData)==null?void 0:Es.data)!=null&&Gs.RequestType||((Us=N==null?void 0:N.payloadData)==null?void 0:Us.RequestType)===o.CHANGE?Xt(N):[],We($,"post",Ke,Qe,Ze)},Gt=async $=>{var Ke,Qe,Ze,U,et;if((($==null?void 0:$.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate"||($==null?void 0:$.MDG_MAT_DYN_BTN_ACTION_TYPE)==="handleValidate1")&&(((Ke=N==null?void 0:N.payloadData)==null?void 0:Ke.RequestType)===o.CREATE||((Qe=N==null?void 0:N.payloadData)==null?void 0:Qe.RequestType)===o.CREATE_WITH_UPLOAD))try{const qe=await n.validateMaterials();rn(qe)}catch(qe){vn(qe);return}u(""),Oe("success"),gn($),dt($.MDG_MAT_DYN_BTN_COMMENT_BOX_NAME),q($.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((Ze=ys)==null?void 0:Ze.MANDATORY)),mt($.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((U=ys)==null?void 0:U.MANDATORY)||$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"),Rt($.MDG_MAT_DYN_BTN_ACTION_TYPE),$.MDG_MAT_DYN_BTN_BUTTON_NAME===Vn.SEND_BACK||$.MDG_MAT_DYN_BTN_BUTTON_NAME===Vn.CORRECTION?an(!0):an(!1),$.MDG_MAT_DYN_BTN_BUTTON_NAME===Vn.SAP_SYNDICATE?vt(!0):vt(!1),$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT===((et=ys)==null?void 0:et.MANDATORY)||$.MDG_MAT_DYN_BTN_COMMENT_BOX_INPUT==="Optional"?Cn():Hn($.MDG_MAT_DYN_BTN_ACTION_TYPE,$)},tt=()=>{_e(),W(!0);const $=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}/massAction/createMaterialApprovalSubmit`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}/massAction/extendMaterialApprovalSubmit`:`/${zt}/massAction/changeMaterialApprovalSubmit`,Ke=U=>{U.statusCode>=200&&U.statusCode<300?(W(!1),Et(`Request Submitted for Approval with Request ID ${U==null?void 0:U.body}`,"success"),Ge(Zs(0)),ne("/masterDataCockpit/materialMaster/material")):(W(!1),Et(U==null?void 0:U.message,"error"))},Qe=()=>{W(!1),Et("Failed Submitting Request.","error")};var Ze;Ze=x===o.CREATE||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD||x===o.CREATE_WITH_UPLOAD?Xt(N):Zt(!0),We($,"post",Ke,Qe,Ze)},zn=$=>{var qe;_e(),W(!0);var Ke=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}/massAction/createMaterialApproved`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}/massAction/extendMaterialApproved`:Y.ATTRIBUTE_2===o.FINANCE_COSTING?`/${zt}/${Ot.MASS_ACTION.FINANCE_COSTING_APPROVED}`:`/${zt}/massAction/changeMaterialApproved`;const Qe=rt=>{rt.statusCode>=200&&rt.statusCode<300?(W(!1),Et($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG,"success"),Ge(Zs(0)),ne("/masterDataCockpit/materialMaster/material")):(W(!1),Et($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"))},Ze=rt=>{Et((rt==null?void 0:rt.message)||"Failed Submitting Request.","error"),W(!1)};var U;const et={requestId:(qe=N==null?void 0:N.payloadData)==null?void 0:qe.RequestId,taskId:(Y==null?void 0:Y.taskId)||"",taskName:(Y==null?void 0:Y.taskDesc)||"",comments:Fe||b,creationDate:Y!=null&&Y.createdOn?pr(Y==null?void 0:Y.createdOn):null,dueDate:Y!=null&&Y.criticalDeadline?pr(Y==null?void 0:Y.criticalDeadline):null};U=x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0),We(Ke,"post",Qe,Ze,Y.ATTRIBUTE_2===o.FINANCE_COSTING?et:U)},Wn=()=>{_e(),W(!0);const $=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ke=x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);We($,"post",U=>{(U==null?void 0:U.statusCode)===Wt.STATUS_200?(Et(U.message,"success"),Ge(Dl({data:{}})),ne(ns.MY_TASK)):Et(U.error,"error"),W(!1)},U=>{Et(U.error,"error"),W(!1)},Ke)},ds=()=>{_e(),W(!0);const $=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`:`/${zt}${Ot.MASS_ACTION.MATERIAL_SEND_TO_LEVEL}`,Ke=x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);We($,"post",U=>{(U==null?void 0:U.statusCode)===Wt.STATUS_200?(Et(U.message,"success"),Ge(Dl({data:{}})),ne(ns.MY_TASK)):Et(U.error,"error"),W(!1)},U=>{Et(U.error,"error"),W(!1)},Ke)},ss=()=>{_e(),W(!0);const $=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.CREATE_MATERIAL_REJECTION}`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}${Ot.MASS_ACTION.EXTEND_MATERIAL_REJECTION}`:`/${zt}${Ot.MASS_ACTION.CHANGE_MATERIAL_REJECTION}`,Ke=x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0);We($,"post",U=>{(U==null?void 0:U.statusCode)===Wt.STATUS_200?(Et(U.message,"success"),Ge(Dl({data:{}})),ne(ns.MY_TASK)):Et(U.error,"error"),W(!1)},U=>{Et(U.error,"error"),W(!1)},Ke)},_t=$=>{W(!0);const Ke=(Y==null?void 0:Y.ATTRIBUTE_2)===o.FINANCE_COSTING?`/${m}${Ot.MASS_ACTION.VALIDATE_FINANCE_COSTING}?requestId=${Ht==null?void 0:Ht.slice(3)}`:`/${m}${Ot.MASS_ACTION.VALIDATE_MATERIAL}`,Qe=(Y==null?void 0:Y.ATTRIBUTE_2)===o.FINANCE_COSTING?Gn():x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):x===o.CHANGE||x===o.CHANGE_WITH_UPLOAD?lt&&Ht?Zt(!0):!lt&&!Ht?Zt(!1):!lt&&Ht?Zt(!0):[]:[];We(Ke,"post",et=>{if(W(!1),(et==null?void 0:et.statusCode)===Wt.STATUS_200){if(Et(($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_SUCC_MSG)||(et==null?void 0:et.message),"success"),(yn||lt)&&(x===o.CHANGE||x===o.CHANGE_WITH_UPLOAD)||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND_WITH_UPLOAD||x===o.EXTEND){ne(ns.REQUEST_BENCH);return}ne(ns.MY_TASK)}else Et(($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG)||"Validation failed.","error")},()=>{Et($==null?void 0:$.MDG_MAT_DYN_BTN_SNACKBAR_FAIL_MSG,"error"),W(!1)},Qe)},dn=()=>{_e(),W(!0);const $=x===o.CREATE||x===o.CREATE_WITH_UPLOAD?`/${zt}/massAction/createMaterialApprovalSubmit`:x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?`/${zt}/massAction/extendMaterialSubmitForReview`:`/${zt}/massAction/changeMaterialApprovalSubmit`,Ke=U=>{U.statusCode>=200&&U.statusCode<300?(W(!1),Et(`Request Submitted for Approval with Request ID ${U==null?void 0:U.body}`,"success"),Ae(),Ge(Zs(0)),ne("/masterDataCockpit/materialMaster/material")):Et(U==null?void 0:U.message,"error")},Qe=U=>{Et((U==null?void 0:U.error)||"Failed Submitting Request.","error"),W(!1)};var Ze;Ze=x===o.CREATE||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND||x===o.EXTEND_WITH_UPLOAD?Xt(N):Zt(!0),We($,"post",Ke,Qe,Ze),D(!0),Ae()},qt=()=>{_e(),W(!0);const $=`/${zt}${Ot.MASS_ACTION.EXTEND_MATERIAL_SAVE_AS_DRAFT}`,Ke=U=>{U.statusCode===Wt.STATUS_200?(W(!1),Et(U==null?void 0:U.message,"success"),ne(ns.REQUEST_BENCH)):(W(!1),Et(U==null?void 0:U.message,"error"))},Qe=U=>{Et(U==null?void 0:U.error,"error"),W(!1)};let Ze;Ze=Xt(N),We($,"post",Ke,Qe,Ze)},Hn=($,Ke)=>{switch($){case"handleSubmitForApproval":tt();break;case"handleSubmitForReview":dn();break;case"handleSendBack":Wn();break;case"handleCorrection":ds();break;case"handleReject":ss();break;case"Validate":Mn(Ke);break;case"handleValidate":Mn(Ke);break;case"handleSAPSyndication":zn(Ke);break;case"handleDraft":qt();break;case"handleSubmit":dn();break;case"handleReview":G();break;default:console.log("Unknown action type")}},G=()=>{_e(),n.setIsAccepted&&n.setIsAccepted(!0)},Mn=$=>{var Ke,Qe;Ie((Ke=Vn)==null?void 0:Ke.VALIDATE),_(J((Qe=el)==null?void 0:Qe.VALIDATE_MSG)),x===o.CREATE||x===o.EXTEND||x===o.CREATE_WITH_UPLOAD||x===o.EXTEND_WITH_UPLOAD||(Y==null?void 0:Y.ATTRIBUTE_2)===o.FINANCE_COSTING?_t($):Ss($)},Ss=$=>{Array.isArray(I)?Is($):typeof I=="object"&&B($)},en=()=>{const $=yt==null?void 0:yt["Config Data"],Ke={};return Object.entries($).forEach(([Qe,Ze])=>{const U=Ze.filter(et=>{var qe;return et.visibility===((qe=ys)==null?void 0:qe.MANDATORY)}).map(et=>({jsonName:et.jsonName,fieldName:et.fieldName}));if(!(U!=null&&U.some(et=>et.jsonName==="Material"))){const et=Ze.find(qe=>qe.jsonName==="Material");et&&U.push({jsonName:et.jsonName,fieldName:et.fieldName})}(U==null?void 0:U.length)>0&&(Ke[Qe]=U)}),Ke},us=($,Ke)=>{var Qe,Ze;if(Array.isArray(I)){const U=Ln===((Qe=Pt)==null?void 0:Qe.LOGISTIC)?[...Ke,{jsonName:"AltUnit",fieldName:"Alternative Unit of Measure"}]:Ln===((Ze=Pt)==null?void 0:Ze.UPD_DESC)?[...Ke,{jsonName:"Langu",fieldName:"Language"}]:Ke,et={};return $==null||$.forEach((qe,rt)=>{var In;const Kt=(In=U==null?void 0:U.filter(At=>!qe[At==null?void 0:At.jsonName]||qe[At==null?void 0:At.jsonName]===""))==null?void 0:In.map(At=>At==null?void 0:At.fieldName);(Kt==null?void 0:Kt.length)>0&&(et[rt]={id:qe.id,slNo:qe.slNo,missingFields:Kt})}),et}else if(typeof I=="object"){let U={},et=0;return Object.keys($).forEach(qe=>{$[qe].forEach(rt=>{var In;const Kt=(In=Ke[qe])==null?void 0:In.filter(At=>!rt[At.jsonName]||rt[At.jsonName]==="").map(At=>At.fieldName);Kt.length>0&&(U[et]={id:rt.id,slNo:rt.slNo,type:rt.type,missingFields:Kt},et++)})}),U}},B=$=>{var U,et,qe,rt;const Ke=Object.fromEntries(Object.entries(I).map(([Kt,In])=>[Kt,In.filter(At=>{var St;return(St=M==null?void 0:M[Kt])==null?void 0:St.includes(At.id)})])),Qe=en(),Ze=us(Ke,Qe);if(Ge(Ai(Ze)),Object.keys(Ze).length>0){const Kt=Object.keys(Ze).map(St=>{var Fn,Ts,Es;return{"Table Name":(Fn=Ze[St])==null?void 0:Fn.type,"Sl. No":(Ts=Ze[St])==null?void 0:Ts.slNo,"Missing Fields":(Es=Ze[St].missingFields)==null?void 0:Es.join(", ")}});te(!0),Oe("danger"),dt("Please Fill All the Mandatory Fields : ");const In=(U=Object.keys(Kt[0]))==null?void 0:U.map(St=>({field:St,headerName:(St==null?void 0:St.charAt(0).toUpperCase())+(St==null?void 0:St.slice(1)),flex:St==="Sl. No"?.5:St==="Missing Fields"?3:1.5,align:"center",headerAlign:"center"}));Re(In);const At=Kt==null?void 0:Kt.map(St=>({...St,id:ml()}));fe(At),re(!0),Cn(),Ge(Ml(!0))}else{if(x===((et=o)==null?void 0:et.CHANGE)||x===((qe=o)==null?void 0:qe.CHANGE_WITH_UPLOAD)){if(!Ht||Ht&&ke&&((rt=Object==null?void 0:Object.keys(ke))!=null&&rt.length)){le();return}_t($);return}Se("success"),u("Data Validated Successfully"),Ae(),Ge(Ml(!1)),n==null||n.setCompleted([!0,!0])}},Is=$=>{var Ze,U,et,qe;const Ke=I==null?void 0:I.filter(rt=>M==null?void 0:M.includes(rt.id)),Qe=us(Ke,yt==null?void 0:yt["Mandatory Fields"]);if(Ge(Ai(Qe)),Object.keys(Qe).length>0){const rt=Object.keys(Qe).map(At=>{var St,Fn;return{"Sl. No":(St=Qe[At])==null?void 0:St.slNo,"Missing Fields":(Fn=Qe[At].missingFields)==null?void 0:Fn.join(", ")}});te(!0),Oe("danger"),dt("Please Fill All the Mandatory Fields : ");const Kt=(Ze=Object.keys(rt[0]))==null?void 0:Ze.map(At=>({field:At,headerName:(At==null?void 0:At.charAt(0).toUpperCase())+(At==null?void 0:At.slice(1)),flex:At==="Sl. No"?.5:At==="Missing Fields"?3:1,align:"center",headerAlign:"center"}));Re(Kt);const In=rt==null?void 0:rt.map(At=>({...At,id:ml()}));fe(In),re(!0),Cn(),Ge(Ml(!0))}else{if(x===((U=o)==null?void 0:U.CHANGE)||x===((et=o)==null?void 0:et.CHANGE_WITH_UPLOAD)){if(!Ht||Ht&&ke&&((qe=Object==null?void 0:Object.keys(ke))!=null&&qe.length)){le();return}_t($);return}Se("success"),u("Data Validated Successfully"),Ae(),Ge(Ml(!1)),n==null||n.setCompleted([!0,!0])}},qn=()=>{var $;if(L&&!b){Ce(!0);return}else Jn==="scheduleSyndication"?A!=null&&A.current&&(($=A==null?void 0:A.current)==null||$.handlePriorityDialogClickOpen()):Hn(Jt,ft);Ne()},Ne=()=>{Ct(!1),se(""),Ce(!1),q(!1)},Cn=()=>{Ct(!0)};function ls(){Vt(),Ie("")}const Un=()=>{ls()};return f(Vs,{children:[e(Kl,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:f($c,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"space-between",alignItems:"center",gap:1,width:"100%"},children:[(!Ht||lt&&(Tt==null?void 0:Tt.reqStatus)===((ln=cs)==null?void 0:ln.DRAFT))&&(v==null?void 0:v.RequestType)===o.CHANGE&&(v==null?void 0:v.TemplateName)===((jt=Pt)==null?void 0:jt.SET_DNU)&&e(Be,{sx:{flex:2,marginLeft:"90px"},children:f("span",{children:[e("strong",{children:"Note"}),": All default values for ",e("strong",{children:"Set To DNU"})," template will be fetched after ",e("strong",{children:"Validation"}),"."]})}),f(Be,{sx:{display:"flex",gap:1},children:[x!==o.EXTEND_WITH_UPLOAD&&x!==o.EXTEND&&e(kt,{children:!Ht||lt&&((wn=Ls)!=null&&wn.includes(Tt==null?void 0:Tt.reqStatus))?f(kt,{children:[e(nt,{variant:"contained",color:"primary",onClick:()=>{var $,Ke;if(Ie(Vn.SAVE),_(J(($=el)==null?void 0:$.SAVE_AS_DRAFT_MSG)),(v==null?void 0:v.RequestType)===o.CHANGE&&(!Ht||Ht&&ke&&((Ke=Object==null?void 0:Object.keys(ke))!=null&&Ke.length))){le();return}Vt()},children:J("Save As Draft")}),((Yn=N==null?void 0:N.payloadData)==null?void 0:Yn.RequestType)===o.CREATE&&fn===gi.REQUEST_DETAILS&&e(Rn,{title:ro.VALIDATE_MANDATORY,children:e(nt,{variant:"contained",color:"primary",onClick:n==null?void 0:n.validateMaterials,children:J("Validate")})}),fn===gi.REQUEST_DETAILS&&((v==null?void 0:v.RequestType)===o.CHANGE||(v==null?void 0:v.RequestType)===o.CHANGE_WITH_UPLOAD||(v==null?void 0:v.RequestType)===o.CREATE_WITH_UPLOAD||(v==null?void 0:v.RequestType)===o.EXTEND_WITH_UPLOAD)?e(kt,{children:e(nt,{variant:"contained",color:"primary",onClick:Mn,children:J("Validate")})}):e(kt,{children:fn===gi.PREVIEW&&e(nt,{variant:"contained",color:"primary",onClick:Un,disabled:(v==null?void 0:v.RequestType)===((jn=o)==null?void 0:jn.CHANGE)||(v==null?void 0:v.RequestType)===((hs=o)==null?void 0:hs.CHANGE_WITH_UPLOAD)||(v==null?void 0:v.RequestType)===o.CREATE_WITH_UPLOAD||(v==null?void 0:v.RequestType)===o.EXTEND_WITH_UPLOAD?(v==null?void 0:v.RequestStatus)!==((ms=cs)==null?void 0:ms.VALIDATED_REQUESTOR):n==null?void 0:n.submitForApprovalDisabled,children:J("Submit")})})]}):null}),((v==null?void 0:v.RequestType)===o.EXTEND||(v==null?void 0:v.RequestType)===o.EXTEND_WITH_UPLOAD&&(!Ht||lt&&((_n=Ls)==null?void 0:_n.includes(Tt==null?void 0:Tt.reqStatus))||!lt&&Ht)||!lt&&Ht)&&((gs=n==null?void 0:n.filteredButtons)==null?void 0:gs.map(($,Ke)=>{var Kt,In,At,St,Fn,Ts,Es,Gs,Us,l,c;const{MDG_MAT_DYN_BTN_BUTTON_NAME:Qe,MDG_MAT_DYN_BTN_BUTTON_STATUS:Ze}=$,U=Qe==="SAP Syndication"||Qe==="Submit",et=Qe==="Forward"||Qe==="Submit",qe=((Kt=oe==null?void 0:oe.requestHeaderData)==null?void 0:Kt.RequestStatus)==="Validated-MDM"||(v==null?void 0:v.RequestStatus)==="Validated-MDM"||(v==null?void 0:v.RequestStatus)==="Validated-Requestor"||((In=oe==null?void 0:oe.childRequestHeaderData)==null?void 0:In.RequestStatus)==="Validated-MDM"||((At=oe==null?void 0:oe.childRequestHeaderData)==null?void 0:At.RequestStatus)==="Validated-Requestor"||((St=n==null?void 0:n.childRequestHeaderData)==null?void 0:St.RequestStatus)==="Validated-MDM"||((Fn=n==null?void 0:n.childRequestHeaderData)==null?void 0:Fn.RequestStatus)==="Validated-Requestor";let rt=Ze==="DISABLED";return U&&qe&&(rt=!1),(et&&((v==null?void 0:v.RequestType)===((Ts=o)==null?void 0:Ts.CREATE)||(v==null?void 0:v.RequestType)===((Es=o)==null?void 0:Es.CREATE_WITH_UPLOAD))&&!(n!=null&&n.submitForApprovalDisabled)||((v==null?void 0:v.RequestType)===((Gs=o)==null?void 0:Gs.CHANGE)||(v==null?void 0:v.RequestType)===((Us=o)==null?void 0:Us.CHANGE_WITH_UPLOAD)||(v==null?void 0:v.RequestType)===((l=o)==null?void 0:l.EXTEND)||(v==null?void 0:v.RequestType)===((c=o)==null?void 0:c.EXTEND_WITH_UPLOAD))&&et&&qe)&&(rt=!1),e(nt,{variant:"contained",size:"small",sx:{...Yl,mr:1},disabled:rt||ie,onClick:()=>Gt($),children:$.MDG_MAT_DYN_BTN_BUTTON_NAME},Ke)}))]})]})}),e(Ql,{dialogState:tn,openReusableDialog:Cn,closeReusableDialog:Ne,dialogTitle:bt,dialogMessage:ht,handleDialogConfirm:qn,dialogOkText:"OK",dialogSeverity:Le,showCancelButton:!0,showInputText:O,inputText:b,blurLoading:ie,setInputText:se,mandatoryTextInput:L,remarksError:p,isTable:ee,tableColumns:Q,tableRows:H,isShowWFLevel:(n==null?void 0:n.showWfLevels)&&Qn,isSyndicationBtn:de,selectedLevel:ge,handleLevelChange:be,workFlowLevels:n.workFlowLevels,setSyndicationType:Xn,syndicationType:Jn,isMassSyndication:Tn}),e(Qa,{ref:A,dialogTitle:bt,setDialogTitle:dt,messageDialogMessage:j,setMessageDialogMessage:u,messageDialogSeverity:Le,setMessageDialogSeverity:Oe,handleMessageDialogClickOpen:Cn,blurLoading:ie,setBlurLoading:W,handleMessageDialogClose:Ne,createPayloadFromReduxState:Xt,successMsg:ue,setSuccessMsg:D,setTextInput:mt,inputText:O,handleSnackBarOpen:Ae,taskData:Y,userData:pe,currentButtonState:ft,requestType:x,module:Ds.MAT}),e(Za,{open:Mt,onClose:Ve,handleOk:$e,message:R}),j&&e(_l,{openSnackBar:ce,alertMsg:j,alertType:E,handleSnackBarClose:Me}),e(gl,{blurLoading:ie,loaderMessage:V}),f(Hs,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none"}},open:K,onClose:_e,maxWidth:"xl",children:[f(qs,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:[e(st,{variant:"h6",children:gt===Vn.SAVE?"Save As Draft":"Remarks"}),e(cn,{sx:{width:"max-content"},onClick:_e,children:e(Ll,{})})]}),e(es,{sx:{padding:".5rem 1rem"},children:gt!==Vn.SAVE?e(Vs,{sx:{marginTop:"16px"},children:e(Be,{sx:{minWidth:400},children:f(oo,{sx:{height:"auto"},fullWidth:!0,children:[e(mn,{sx:{backgroundColor:"#F5F5F5","& .MuiOutlinedInput-root":{"& fieldset":{borderColor:we?(kn=(Zn=He)==null?void 0:Zn.error)==null?void 0:kn.dark:"rgba(0, 0, 0, 0.23)"},"&:hover fieldset":{borderColor:we?(bs=(fs=He)==null?void 0:fs.error)==null?void 0:bs.dark:"rgba(0, 0, 0, 0.23)"},"&.Mui-focused fieldset":{borderColor:we?(ws=(Rs=He)==null?void 0:Rs.error)==null?void 0:ws.dark:(ut=(Bn=He)==null?void 0:Bn.primary)==null?void 0:ut.dark}}},value:Fe,onChange:Nn,inputProps:{maxLength:P},multiline:!0,placeholder:"Enter Remarks"}),e(Pc,{sx:{textAlign:"right",color:we?(vs=(On=He)==null?void 0:On.error)==null?void 0:vs.dark:"rgba(0, 0, 0, 0.6)",marginTop:"4px"},children:`${(Fe==null?void 0:Fe.length)||0}/${P}`})]})})}):e(Be,{sx:{margin:"15px"},children:e(st,{sx:{fontWeight:"200"},children:el.DRAFT_MESSAGE})})}),f(ts,{sx:{display:"flex",justifyContent:"end"},children:[e(nt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:_e,children:J("Cancel")}),e(nt,{className:"button_primary--normal",type:"save",disabled:ie,onClick:wt,variant:"contained",children:gt===Vn.SAVE?"Yes":"Submit"})]})]})]})},$l=[{code:"5000083",desc:"Linen Mens Shirt"},{code:"GEN001",desc:"Beverage Base"},{code:"GEN002",desc:"Snack Mix"},{code:"GEN003",desc:"Cosmetic Foundation"},{code:"GEN004",desc:"Pharmaceutical Tablet"},{code:"GEN005",desc:"Electronic Component"}],Ei={5000083:[{code:"5000083001",desc:"Linen Mens Shirt, Small-38, Deep Blue"},{code:"5000083002",desc:"Linen Mens Shirt, Medium-40, Deep Blue"},{code:"5000083003",desc:"Linen Mens Shirt, Large-42, Deep Blue"}],GEN001:[{code:"VAR001",desc:"Coca Cola - 330ml Can"},{code:"VAR002",desc:"Coca Cola - 500ml Bottle"},{code:"VAR003",desc:"Pepsi - 330ml Can"},{code:"VAR004",desc:"Sprite - 330ml Can"},{code:"VAR005",desc:"Fanta - 330ml Can"}],GEN002:[{code:"VAR006",desc:"Trail Mix - Nuts & Raisins"},{code:"VAR007",desc:"Trail Mix - Chocolate Mix"},{code:"VAR008",desc:"Popcorn - Salted"},{code:"VAR009",desc:"Crackers - Cheese"},{code:"VAR010",desc:"Chips - BBQ Flavor"}],GEN003:[{code:"VAR011",desc:"Foundation - Light Beige"},{code:"VAR012",desc:"Foundation - Medium Tan"},{code:"VAR013",desc:"Foundation - Dark Brown"},{code:"VAR014",desc:"Foundation - Fair Ivory"},{code:"VAR015",desc:"Foundation - Olive Tone"}]},fd=({open:n,onClose:N,articleNumber:oe,articleDetails:I,title:M="Add Components",handleSave:x,...ue})=>{const[D,E]=i.useState(null),[Se,ce]=i.useState(null),[z,R]=i.useState([]),[_,j]=i.useState(!1),[u,ie]=i.useState([]),W=u.reduce((ee,re)=>ee+re.quantity,0);i.useEffect(()=>{if(I&&n&&I.GenericMaterial&&I.GenericMaterialDescription){const ee={code:I.GenericMaterial,desc:I.GenericMaterialDescription};E(ee),ce(ee);const re=Ei[I.GenericMaterial]||[];if(R(re),I.Tocomponentsdata&&I.Tocomponentsdata.length>0){const H=I.Tocomponentsdata.map(fe=>{const Q=re.find(Re=>Re.code===fe.VariantCode);return{code:fe.VariantCode,desc:fe.VariantDescription||(Q?Q.desc:""),quantity:fe.Quantity||1,uom:fe.Uom||"EA - Each"}});ie(H)}}},[I,n]);const V=async(ee,re)=>{if(E(re),re){const H=$l.find(fe=>fe.code===re.code);H&&ce(H),j(!0),R([]),setTimeout(()=>{const fe=Ei[re.code]||[];R(fe),j(!1)},1500)}else ce(null),R([])},a=async(ee,re)=>{if(ce(re),re){const H=$l.find(fe=>fe.desc===re.desc);H&&E(H),j(!0),R([]),setTimeout(()=>{const fe=Ei[re.code]||[];R(fe),j(!1)},1500)}else E(null),R([])},K=(ee,re)=>{ie(re?H=>[...H,{...ee,quantity:1,uom:"EA - Each"}]:H=>H.filter(fe=>fe.code!==ee.code))},De=(ee,re)=>{re<1||ie(H=>H.map(fe=>fe.code===ee?{...fe,quantity:re}:fe))},Fe=ee=>{ie(re=>re.map(H=>H.code===ee?{...H,quantity:H.quantity+1}:H))},ze=ee=>{ie(re=>re.map(H=>H.code===ee?{...H,quantity:Math.max(1,H.quantity-1)}:H))},ge=()=>{const ee={ArticleComponentsId:(I==null?void 0:I.ArticleComponentsId)||"",GenericMaterial:(D==null?void 0:D.code)||null,GenericMaterialDescription:(Se==null?void 0:Se.desc)||null,Tocomponentsdata:u.map(re=>{var fe;const H=(fe=I==null?void 0:I.Tocomponentsdata)==null?void 0:fe.find(Q=>Q.VariantCode===re.code);return{ComponentsId:(H==null?void 0:H.ComponentsId)||"",VariantCode:re.code,VariantDescription:re.desc,Quantity:re.quantity,Uom:re.uom||"EA - Each"}})};x&&x({id:oe,field:"articleComponents",value:ee}),N()},Te=()=>{E(null),ce(null),R([]),ie([]),j(!1),N()};return f(Hs,{open:n,onClose:Te,maxWidth:!1,PaperProps:{sx:{width:"900px",maxWidth:"90vw"}},...ue,children:[e(qs,{sx:{pb:1},children:f(Be,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[e(Be,{children:e(st,{variant:"h6",sx:{fontWeight:600},children:M})}),e(Be,{display:"flex",alignItems:"center",gap:2,children:e(cn,{onClick:Te,size:"small",children:e(Ll,{})})})]})}),f(es,{sx:{pt:2,pb:1},children:[e(Kl,{elevation:0,sx:{p:2,mb:2,bgcolor:"#f8f9fa",border:"1px solid #e0e0e0"},children:f(ve,{container:!0,spacing:2,children:[f(ve,{item:!0,xs:6,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"GENERIC MATERIAL CODE"}),e(hl,{options:$l,getOptionLabel:ee=>ee.code,value:D,onChange:V,renderInput:ee=>e(mn,{...ee,variant:"outlined",size:"small",placeholder:"Select code...",sx:{"& .MuiInputBase-root":{bgcolor:"white",height:"36px"}}}),isOptionEqualToValue:(ee,re)=>ee.code===re.code})]}),f(ve,{item:!0,xs:6,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"DESCRIPTION"}),e(hl,{options:$l,getOptionLabel:ee=>ee.desc,value:Se,onChange:a,renderInput:ee=>e(mn,{...ee,variant:"outlined",size:"small",placeholder:"Select description...",sx:{"& .MuiInputBase-root":{bgcolor:"white",height:"36px"}}}),isOptionEqualToValue:(ee,re)=>ee.desc===re.desc})]})]})}),_&&f(Be,{display:"flex",justifyContent:"center",alignItems:"center",py:2,children:[e(Ii,{size:24}),e(st,{variant:"body2",ml:1.5,color:"text.secondary",children:"Loading variants..."})]}),z.length>0&&!_&&f(Be,{children:[f(Be,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1.5,children:[e(st,{variant:"subtitle1",sx:{fontWeight:600},children:"Available Variants"}),e(Be,{display:"flex",alignItems:"center",gap:1,children:u.length>0&&e(Mi,{label:`Total Quantity: ${W}`,color:"primary",size:"small",variant:"filled"})})]}),e(Be,{sx:{maxHeight:"400px",overflowY:"auto"},children:z.map((ee,re)=>{var Q;const H=u.find(Re=>Re.code===ee.code),fe=u.find(Re=>Re.code===ee.code);return e(pc,{elevation:0,sx:{border:H?"2px solid #1976d2":"1px solid #e0e0e0",mb:1,transition:"all 0.2s ease","&:hover":{boxShadow:1}},children:e(Wc,{sx:{p:1.5,"&:last-child":{pb:1.5}},children:f(ve,{container:!0,spacing:1.5,alignItems:"center",children:[e(ve,{item:!0,xs:12,sm:6,children:e(Si,{sx:{m:0},control:e(dl,{checked:!!H,onChange:Re=>K(ee,Re.target.checked),color:"primary",size:"small"}),label:f(Be,{ml:1,children:[e(st,{variant:"body2",sx:{fontWeight:500,lineHeight:1.2},children:ee.code}),e(st,{variant:"body2",color:"text.secondary",sx:{lineHeight:1.2},children:ee.desc})]})})}),H&&f(kt,{children:[f(ve,{item:!0,xs:6,sm:3,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"Quantity"}),e(mn,{type:"number",size:"small",value:(fe==null?void 0:fe.quantity)||1,onChange:Re=>De(ee.code,parseInt(Re.target.value)||1),InputProps:{startAdornment:e(Wr,{position:"start",children:e(cn,{size:"small",onClick:()=>ze(ee.code),disabled:(fe==null?void 0:fe.quantity)<=1,sx:{p:.3},children:e(od,{fontSize:"small"})})}),endAdornment:e(Wr,{position:"end",children:e(cn,{size:"small",onClick:()=>Fe(ee.code),sx:{p:.3},children:e($o,{fontSize:"small"})})})},inputProps:{min:1,style:{textAlign:"center",padding:"6px 8px"}},sx:{"& .MuiInputBase-root":{height:"32px"}}})]}),f(ve,{item:!0,xs:6,sm:3,children:[e(st,{variant:"caption",display:"block",sx:{fontWeight:600,mb:.5,color:"#666"},children:"UOM"}),e(mn,{value:((Q=fe==null?void 0:fe.uom)==null?void 0:Q.split(" - ")[0])||"EA",size:"small",disabled:!0,fullWidth:!0,inputProps:{style:{textAlign:"center",padding:"6px 8px"}},sx:{"& .MuiInputBase-root":{height:"32px"}}})]})]})]})})},ee.code)})})]})]}),f(ts,{sx:{px:3,py:2,gap:1.5},children:[e(nt,{onClick:Te,variant:"outlined",sx:{minWidth:"100px"},children:"Close"}),f(nt,{onClick:ge,variant:"contained",disabled:u.length===0||!D||!Se,sx:{minWidth:"140px",fontWeight:600},children:["Save (",u.length,")"]})]})]})},Fo=n=>{var a,K,De,Fe,ze,ge,Te,ee,re,H,fe;const{customError:N}=Os(),[oe,I]=i.useState(!1),[M,x]=i.useState(null),ue=Z(Q=>Q.payload),[D,E]=i.useState(((De=(K=(a=ue==null?void 0:ue[n.materialID])==null?void 0:a.payloadData)==null?void 0:K.Classification)==null?void 0:De.classification)||[]),[Se,ce]=i.useState([]),z=as();i.useEffect(()=>{var Q,Re,he,te,Mt,T,J,p;(te=(he=(Re=(Q=ue==null?void 0:ue[n.materialID])==null?void 0:Q.payloadData)==null?void 0:Re.Classification)==null?void 0:he.basic)!=null&&te.Classtype&&xi((p=(J=(T=(Mt=ue==null?void 0:ue[n.materialID])==null?void 0:Mt.payloadData)==null?void 0:T.Classification)==null?void 0:J.basic)==null?void 0:p.Classtype,z)},[(Te=(ge=(ze=(Fe=ue==null?void 0:ue[n.materialID])==null?void 0:Fe.payloadData)==null?void 0:ze.Classification)==null?void 0:ge.basic)==null?void 0:Te.Classtype]),i.useEffect(()=>{var Q,Re,he,te,Mt,T,J,p,Ce,L,q,b,se;(te=(he=(Re=(Q=ue==null?void 0:ue[n.materialID])==null?void 0:Q.payloadData)==null?void 0:Re.Classification)==null?void 0:he.basic)!=null&&te.Classnum&&(!(D!=null&&D.length)||D!=null&&D.length&&((Mt=D[0])==null?void 0:Mt.className)!=((Ce=(p=(J=(T=ue==null?void 0:ue[n.materialID])==null?void 0:T.payloadData)==null?void 0:J.Classification)==null?void 0:p.basic)==null?void 0:Ce.Classnum))&&R((se=(b=(q=(L=ue==null?void 0:ue[n.materialID])==null?void 0:L.payloadData)==null?void 0:q.Classification)==null?void 0:b.basic)==null?void 0:se.Classnum)},[(fe=(H=(re=(ee=ue==null?void 0:ue[n.materialID])==null?void 0:ee.payloadData)==null?void 0:re.Classification)==null?void 0:H.basic)==null?void 0:fe.Classnum]),i.useEffect(()=>{z(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:"",data:D??null,viewID:n==null?void 0:n.activeViewTab,itemID:"classification"}))},[D]);const R=Q=>{const Re=te=>{if((te==null?void 0:te.statusCode)===Wt.STATUS_200){const Mt=te.body.map((T,J)=>({id:J+1,characteristic:T.code,description:T.desc,value:"",className:Q}));E(Mt)}},he=te=>{N(te)};We(`/${m}${Ot.DATA.GET_CHARACTERISTICS_BY_CLASS}?className=${Q}`,"get",Re,he)},_=Q=>{x(Q),I(!0),j(Q.characteristic)},j=Q=>{const Re=te=>{(te==null?void 0:te.statusCode)===Wt.STATUS_200&&ce(te.body)},he=te=>{N(te)};We(`/${m}${Ot.DATA.GET_CHARACTERISTIC_VALUES}?characteristics=${Q}`,"get",Re,he)},u=()=>{I(!1),x(null)},ie=Q=>{x(Re=>({...Re,value:Q}))},W=()=>{E(Q=>Q.map(Re=>Re.id===M.id?{...Re,value:M.value}:Re)),I(!1)},V=[{field:"characteristic",headerName:"Characteristic",flex:1,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Characteristic"})},{field:"description",headerName:"Description",flex:2,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Description"})},{field:"value",flex:1,headerAlign:"left",align:"left",headerClassName:"super-app-theme--header",renderHeader:()=>f(Be,{sx:{display:"flex",alignItems:"center"},children:[e(st,{variant:"body2",fontWeight:"bold",children:"Value"}),e(st,{color:"error",sx:{ml:.5},children:"*"})]}),renderCell:Q=>e("span",{children:Array.isArray(Q.value)&&Q.value.length>1?f(kt,{children:[Q.value[0],"...",e("span",{style:{verticalAlign:"middle"},children:e("span",{style:{cursor:"pointer",color:"#888"},title:Q.value.join(", "),children:f("svg",{xmlns:"http://www.w3.org/2000/svg",height:"22",width:"22",viewBox:"0 0 24 24",style:{verticalAlign:"middle"},children:[e("circle",{cx:"12",cy:"12",r:"10",fill:"#e0e0e0"}),e("text",{x:"12",y:"17",textAnchor:"middle",fontSize:"16",fill:"#555",fontFamily:"Arial",fontWeight:"bold",children:"i"})]})})})]}):Q.value.length===1?Q.value:"--"})},{field:"actions",headerName:"Actions",width:100,sortable:!1,headerClassName:"super-app-theme--header",renderHeader:()=>e(st,{variant:"body2",fontWeight:"bold",children:"Actions"}),renderCell:Q=>e(cn,{color:"primary",size:"small",onClick:()=>_(Q.row),children:e(cd,{})})}];return f(Be,{sx:{backgroundColor:"white",border:`1px solid ${He.hover.hoverbg}`,borderRadius:"8px",boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",px:3,py:2,mb:3,mt:2},children:[e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:n==null?void 0:n.characteristicDetails[0]}),D.length>0?f("div",{style:{width:"100%",height:D.length*53+56},children:[" ",e(yl,{rows:D,columns:V,hideFooter:!0,disableColumnMenu:!0,disableSelectionOnClick:!0,sx:{fontFamily:"'Roboto','Helvetica','Arial',sans-serif",fontSize:"0.875rem","& .MuiDataGrid-columnHeaders":{backgroundColor:"#f3f3fc",borderBottom:"1px solid #dcdcdc"},"& .MuiDataGrid-columnHeaderTitle":{fontWeight:600,fontSize:"0.875rem"},"& .MuiDataGrid-cell":{color:"#333",fontSize:"0.875rem"}}})]}):e(st,{variant:"body2",sx:{color:"#888"},children:"No characteristic data available."}),f(Hs,{open:oe,onClose:u,fullWidth:!0,maxWidth:"sm",children:[e(qs,{children:"Edit Entry"}),f(es,{children:[e(mn,{margin:"dense",label:"Characteristic",fullWidth:!0,value:(M==null?void 0:M.characteristic)||"",disabled:!0}),e(mn,{margin:"dense",label:"Description",fullWidth:!0,value:(M==null?void 0:M.description)||"",disabled:!0}),e(hl,{multiple:!0,freeSolo:!1,options:Se,getOptionLabel:Q=>Q.code||"",value:Se.filter(Q=>((M==null?void 0:M.value)||[]).includes(Q.code)),onChange:(Q,Re)=>{const he=Re.map(te=>te.code);ie(he)},renderInput:Q=>e(mn,{...Q,margin:"dense",label:"Value",fullWidth:!0,variant:"outlined"})})]}),f(ts,{children:[e(nt,{onClick:u,color:"secondary",children:"Cancel"}),e(nt,{onClick:W,variant:"contained",children:"Save"})]})]})]})},Nd="/assets/UOM_BOX-89f6ce52.png",Cd=n=>{const N=n.materialID||"SupplierForm",oe=Z(Se=>{var ce,z,R,_,j;return((j=(_=(R=(z=(ce=Se.payload)==null?void 0:ce[N])==null?void 0:z.payloadData)==null?void 0:R[S.SUPPLIER_FORM])==null?void 0:_.basic)==null?void 0:j.Togenericarraydata)||[]}),[I,M]=i.useState([{Json401:"",Json402:"",Json403:"",GenericArrayId:null}]),x=as();i.useEffect(()=>{oe&&oe.length>0&&M(oe)},[oe]);const ue=(Se,ce,z)=>{let R=JSON.parse(JSON.stringify(I));R[Se][ce]=z,M(R),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:R}))},D=()=>{const Se=[...I,{Json401:"",Json402:"",Json403:""}];M(Se),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:Se}))},E=Se=>{const ce=I.filter((z,R)=>R!==Se);M(ce),x(pn({materialID:N,viewID:S.SUPPLIER_FORM,itemID:"basic",keyName:"Togenericarraydata",data:ce}))};return f(Be,{sx:{mt:3},children:[e(st,{fontWeight:700,mb:2,children:"Prepack Article Details"}),f(ve,{container:!0,spacing:2,sx:{fontWeight:600,mb:1},children:[e(ve,{item:!0,xs:4,children:e(st,{children:"Shirt Size"})}),e(ve,{item:!0,xs:4,children:e(st,{children:"Shirt Colour"})}),e(ve,{item:!0,xs:3,children:e(st,{children:"Shirt Quantity"})}),e(ve,{item:!0,xs:1})," "]}),I.map((Se,ce)=>f(ve,{container:!0,spacing:2,alignItems:"center",mb:1,children:[e(ve,{item:!0,xs:4,children:e(mn,{fullWidth:!0,size:"small",placeholder:"Size",value:Se.Json401,onChange:z=>ue(ce,"Json401",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:4,children:e(mn,{fullWidth:!0,size:"small",placeholder:"Colour",value:Se.Json402,onChange:z=>ue(ce,"Json402",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:3,children:e(mn,{fullWidth:!0,size:"small",placeholder:"Quantity",value:Se.Json403,onChange:z=>ue(ce,"Json403",z.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:1,children:ce!==0&&e(Rn,{title:"Remove Row",children:e(cn,{onClick:()=>E(ce),color:"error",children:e(Po,{})})})})]},ce)),e(nt,{variant:"outlined",onClick:D,children:"+ Add Row"})]})},_d=n=>{var R,_,j,u;const N=n.materialID||"SupplierForm",oe=Z(ie=>{var W,V,a,K,De;return((De=(K=(a=(V=(W=ie.payload)==null?void 0:W[N])==null?void 0:V.payloadData)==null?void 0:a[S.CHARACTERISTIC])==null?void 0:K.basic)==null?void 0:De.ToCharCreationValueData)||[]}),I=Z(ie=>ie.payload),M=(u=(j=(_=(R=I==null?void 0:I[N])==null?void 0:R.payloadData)==null?void 0:_[S.CHARACTERISTIC])==null?void 0:j.basic)==null?void 0:u.Json451,x=[{Json466:"WHITE",Json467:"White",CharCreationValueId:null},{Json466:"BROWN",Json467:"Brown",CharCreationValueId:null},{Json466:"GREY",Json467:"Grey",CharCreationValueId:null},{Json466:"MAROON",Json467:"maroon",CharCreationValueId:null}],[ue,D]=i.useState([{Json466:"",Json467:"",CharCreationValueId:null}]),E=as();i.useEffect(()=>{oe&&oe.length>0&&D(oe)},[oe]),i.useEffect(()=>{M&&(ue==null?void 0:ue.length)<2&&!(oe!=null&&oe.length)&&(D(x),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:x})))},[M]);const Se=(ie,W,V)=>{let a=JSON.parse(JSON.stringify(ue));a[ie][W]=V,D(a),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:a}))},ce=()=>{const ie=[...ue,{Json466:"",Json467:"",CharCreationValueId:null}];D(ie),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:ie}))},z=ie=>{const W=ue.filter((V,a)=>a!==ie);D(W),E(pn({materialID:N,viewID:S.CHARACTERISTIC,itemID:"basic",keyName:"ToCharCreationValueData",data:W}))};return f(Be,{sx:{mt:3},children:[e(st,{fontWeight:700,mb:2,children:"Characteristic Details"}),f(ve,{container:!0,spacing:2,sx:{fontWeight:600,mb:1},children:[e(ve,{item:!0,xs:4,children:e(st,{children:"Characteristic Value"})}),e(ve,{item:!0,xs:4,children:e(st,{children:"Description"})}),e(ve,{item:!0,xs:1})," "]}),ue.map((ie,W)=>f(ve,{container:!0,spacing:2,alignItems:"center",mb:1,children:[e(ve,{item:!0,xs:4,children:e(mn,{fullWidth:!0,size:"small",placeholder:"Size",value:ie.Json466,onChange:V=>Se(W,"Json466",V.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:4,children:e(mn,{fullWidth:!0,size:"small",placeholder:"Colour",value:ie.Json467,onChange:V=>Se(W,"Json467",V.target.value),disabled:n.disabled})}),e(ve,{item:!0,xs:1,children:W!==0&&e(Rn,{title:"Remove Row",children:e(cn,{onClick:()=>z(W),color:"error",children:e(Po,{})})})})]},W)),e(nt,{variant:"outlined",onClick:ce,children:"+ Add Row"})]})},Od=n=>{var te,Mt;const N=Z(T=>T.payload),oe=Z(T=>{var J;return(J=T==null?void 0:T.request)==null?void 0:J.materialRows}),I=(Mt=(te=N==null?void 0:N[n.materialID])==null?void 0:te.headerData)==null?void 0:Mt.orgData;Z(T=>T.userManagement.taskData);const[M,x]=i.useState({}),ue=Z(T=>{var J;return(J=T.payload.payloadData)==null?void 0:J.RequestType}),[D,E]=i.useState([]),[Se,ce]=i.useState([]),[z,R]=i.useState(!1),_=as(),{getDtCall:j,dtData:u}=ko(),{customError:ie}=Os(),{t:W}=Cl(),V=i.useRef({});function a(T,J){const p={plant:"Plant",salesOrg:"SalesOrg",dc:"Distribution Channel",sloc:"Storage Location",mrpProfile:"MRP Profile",warehouse:"Warehouse",purchasingOrg:"Purchasing Org",store:"Store",distributionCenter:"Distribution Center",supplier:"Supplier"},Ce=T.split("-");return J.map((L,q)=>{const b=p[L],se=Ce[q]||"N/A";return`${b} - ${se}`}).join(", ")}const K=T=>[...new Set(T)].join("$^$"),De=T=>{const J=new Map;return T.forEach(({CountryName:p,Country:Ce})=>{J.set(Ce,p)}),Array.from(J,([p,Ce])=>({Country:p,CountryName:Ce}))},Fe=T=>T.map(({Country:J})=>J).join("$^$"),ze=T=>{const J=`/${zt}${Ot.TAX_DATA.GET_COUNTRY_SALESORG}`,p={salesOrg:"I00X"};R(!1),We(J,"post",q=>{const b=q==null?void 0:q.body,se=De(b),O=Fe(se);ge(O)},q=>{R(!1),ie(tl.NO_DATA_AVAILABLE)},p)},ge=T=>{const J=`/${zt}${Ot.TAX_DATA.GET_TAX_COUNTRY}`;We(J,"post",q=>{var Oe,ht,It,bt;R(!1);const b=q==null?void 0:q.body,se=((bt=(It=(ht=(Oe=N[n==null?void 0:n.materialID])==null?void 0:Oe.payloadData)==null?void 0:ht.TaxData)==null?void 0:It.TaxData)==null?void 0:bt.TaxDataSet)||[],O={},mt=b.filter(dt=>dt.TaxType);mt.forEach(({TaxClass:dt,TaxClassDesc:tn})=>{O[dt]=tn});const Le=se.map(dt=>{const tn=mt.filter(ft=>ft.TaxType===dt.TaxType&&ft.Country===dt.Country).map(ft=>({code:ft.TaxClass,desc:ft.TaxClassDesc}));let Ct=dt.SelectedTaxClass;return Ct&&O[Ct.TaxClass]&&(Ct={...Ct,TaxClassDesc:O[Ct.TaxClass]}),{...dt,options:tn,SelectedTaxClass:Ct}});mt.forEach(({TaxType:dt,SequenceNo:tn,Country:Ct,TaxClass:ft,TaxClassDesc:gn})=>{if(!Le.some(Rt=>Rt.TaxType===dt&&Rt.Country===Ct)){const Rt=mt.filter(gt=>gt.TaxType===dt&&gt.Country===Ct).map(gt=>({code:gt.TaxClass,desc:gt.TaxClassDesc}));Le.push({TaxType:dt,SequenceNo:tn,Country:Ct,options:Rt,SelectedTaxClass:null})}}),_(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:"TaxDataSet",data:Le,viewID:"TaxData",itemID:"TaxData"}))},q=>{R(!1),ie(tl.NO_DATA_AVAILABLE)},{country:T})},Te=oe==null?void 0:oe.find(T=>(T==null?void 0:T.id)===n.materialID);i.useEffect(()=>{var T,J,p,Ce,L,q,b,se,O,mt,Le,Oe;if(I){const ht=!!((J=(T=N[n.materialID])==null?void 0:T.headerData)!=null&&J.refMaterialData),It=io(I,(p=N==null?void 0:N[n.materialID])==null?void 0:p.payloadData,n==null?void 0:n.materialID,_);if(x(It),!ht&&!n.isDisplay&&It.hasOwnProperty(S.SALES)&&((Ce=n==null?void 0:n.selectedViews)!=null&&Ce.includes(S.SALES))&&It[S.SALES].reduxCombinations.forEach((bt,dt)=>{ue!==o.EXTEND&&re({comb:bt,dt:zs.SALES_DIV_PRICE_MAPPING},I[dt])}),(!ht&&((L=n==null?void 0:n.selectedViews)!=null&&L.includes(S.SALES))||(q=n==null?void 0:n.selectedViews)!=null&&q.includes(S.ACCOUNTING)||(b=n==null?void 0:n.selectedViews)!=null&&b.includes(S.COSTING))&&I.forEach((bt,dt)=>{ue!==o.EXTEND&&!n.isDisplay&&H({combinations:It,index:dt,dt:zs.REG_PLNT_INSPSTK_MAPPING},bt)}),ue===o.EXTEND){let bt={copyPayload:{payloadData:(se=N[n.materialID])==null?void 0:se.payloadData,unitsOfMeasureData:(O=N[n.materialID])==null?void 0:O.unitsOfMeasureData,additionalData:(mt=N[n.materialID])==null?void 0:mt.additionalData}};N!=null&&N.OrgElementDefaultValues&&!n.isDisplay&&ee(It,bt)}else ue===o.CREATE&&(ht||N!=null&&N.OrgElementDefaultValues)&&!n.isDisplay&&ee(It,(Oe=(Le=N[n.materialID])==null?void 0:Le.headerData)==null?void 0:Oe.refMaterialData)}else x({})},[I]),i.useEffect(()=>{if(I){const T=[...new Set(I==null?void 0:I.map(J=>{var p;return(p=J.salesOrg)==null?void 0:p.code}))];K(T),ze()}else(n==null?void 0:n.moduleName)==="Article"&&n.activeViewTab==="Basic Data"&&ze()},[I,n==null?void 0:n.callGetCountryBasedonSalesOrg,n==null?void 0:n.moduleName,n.activeViewTab]),i.useEffect(()=>{var T,J,p,Ce,L,q,b,se,O,mt,Le,Oe,ht,It,bt,dt,tn,Ct,ft,gn,Jt,Rt,gt,Ie,pe,Y,ne,Ge,Je,Tt,v,ke,yt,Ln,Zt,nn,Ht,lt,$t,Ft,yn,xt,rn,vn,Gn,Xt,we,on,Qn;if(u){if(((T=u.customParam)==null?void 0:T.dt)===zs.SALES_DIV_PRICE_MAPPING&&((J=n==null?void 0:n.selectedViews)!=null&&J.includes(S.SALES))){const an=(Ce=Object.keys((p=u==null?void 0:u.data)==null?void 0:p.result[0]))!=null&&Ce.length?(se=(b=(q=(L=u==null?void 0:u.data)==null?void 0:L.result)==null?void 0:q[0])==null?void 0:b.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0])==null?void 0:se.MDG_MAT_MATERIAL_PRICING_GROUP:"";ue!==o.EXTEND&&ue!==o.CREATE_WITH_UPLOAD&&an&&Q((O=u.customParam)==null?void 0:O.comb,"MatPrGrp",an,"Sales")}else if(((mt=u.customParam)==null?void 0:mt.dt)===zs.REG_PLNT_INSPSTK_MAPPING){let an=(Le=u.customParam)==null?void 0:Le.combinations,de=(Oe=u.customParam)==null?void 0:Oe.org;if(an!=null&&an.hasOwnProperty(S.SALES)&&((ht=n==null?void 0:n.selectedViews)!=null&&ht.includes(S.SALES))){const vt=(bt=Object.keys((It=u==null?void 0:u.data)==null?void 0:It.result[0]))!=null&&bt.length?(ft=(Ct=(tn=(dt=u==null?void 0:u.data)==null?void 0:dt.result)==null?void 0:tn[0])==null?void 0:Ct.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:ft.MDG_MAT_ITEM_CAT_GROUP:"";ue!==o.EXTEND&&ue!==o.CREATE_WITH_UPLOAD&&vt&&Q(((gn=de==null?void 0:de.salesOrg)==null?void 0:gn.code)+"-"+((Rt=(Jt=de==null?void 0:de.dc)==null?void 0:Jt.value)==null?void 0:Rt.code),"ItemCat",vt,"Sales")}if(an.hasOwnProperty(S.PURCHASING)&&((gt=n==null?void 0:n.selectedViews)!=null&&gt.includes(S.PURCHASING))){const vt=(pe=Object.keys((Ie=u==null?void 0:u.data)==null?void 0:Ie.result[0]))!=null&&pe.length?(Je=(Ge=(ne=(Y=u==null?void 0:u.data)==null?void 0:Y.result)==null?void 0:ne[0])==null?void 0:Ge.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:Je.MDG_MAT_POST_TO_INSP_STOCK:"";ue!==o.EXTEND&&ue!==o.CREATE_WITH_UPLOAD&&vt&&Q((v=(Tt=de==null?void 0:de.plant)==null?void 0:Tt.value)==null?void 0:v.code,"IndPostToInspStock",vt,"Purchasing")}if(an.hasOwnProperty(S.ACCOUNTING)&&((ke=n==null?void 0:n.selectedViews)!=null&&ke.includes(S.ACCOUNTING))){const vt=(Ln=Object.keys((yt=u==null?void 0:u.data)==null?void 0:yt.result[0]))!=null&&Ln.length?(lt=(Ht=(nn=(Zt=u==null?void 0:u.data)==null?void 0:Zt.result)==null?void 0:nn[0])==null?void 0:Ht.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:lt.MDG_MAT_PRICE_UNIT:"";ue!==o.EXTEND&&ue!==o.CREATE_WITH_UPLOAD&&vt&&Q((Ft=($t=de==null?void 0:de.plant)==null?void 0:$t.value)==null?void 0:Ft.code,"PriceUnit",vt,"Accounting")}if(an.hasOwnProperty(S.COSTING)&&((yn=n==null?void 0:n.selectedViews)!=null&&yn.includes(S.COSTING))){const vt=(rn=Object.keys((xt=u==null?void 0:u.data)==null?void 0:xt.result[0]))!=null&&rn.length?(we=(Xt=(Gn=(vn=u==null?void 0:u.data)==null?void 0:vn.result)==null?void 0:Gn[0])==null?void 0:Xt.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0])==null?void 0:we.MDG_MAT_COSTING_LOT_SIZE:"";ue!==o.EXTEND&&ue!==o.CREATE_WITH_UPLOAD&&vt&&Q((Qn=(on=de==null?void 0:de.plant)==null?void 0:on.value)==null?void 0:Qn.code,"Lotsizekey",vt,"Costing")}}}},[u]);const ee=(T,J)=>{var L;let p=(L=N[n.materialID])==null?void 0:L.payloadData,Ce=N==null?void 0:N.OrgElementDefaultValues;Object.keys(T).forEach(q=>{var se;let b=(se=T[q])==null?void 0:se.reduxCombinations;b==null||b.forEach(O=>{var Oe,ht,It,bt,dt,tn,Ct,ft,gn,Jt;const mt=(Oe=p==null?void 0:p[q])==null?void 0:Oe[O],Le=!mt||Object.keys(mt).length===0;q!==S.BASIC_DATA&&((ht=J==null?void 0:J.copyPayload)!=null&&ht.payloadData[q]||Ce)&&Le&&(n!=null&&n.allTabsData[q])&&(Object.keys(n==null?void 0:n.allTabsData[q]).forEach(Rt=>{const gt=n==null?void 0:n.allTabsData[q][Rt];Array.isArray(gt)&&gt.forEach(Ie=>{var Y,ne,Ge,Je;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const Tt=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[q])==null?void 0:ne[pe],v=((Je=(Ge=Ce==null?void 0:Ce[q])==null?void 0:Ge[O])==null?void 0:Je[pe])||"";let ke=xl(pe,Tt,n==null?void 0:n.allTabsData[q],v);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:q,itemID:O,keyName:pe,data:ke}))}})}),q===S.SALES&&(_(pn({materialID:n==null?void 0:n.materialID,viewID:S.TAX_DATA,itemID:S.TAX_DATA,data:(dt=(bt=(It=J==null?void 0:J.copyPayload)==null?void 0:It.payloadData)==null?void 0:bt.TaxData)==null?void 0:dt.TaxData})),Object.keys((n==null?void 0:n.allTabsData[S.SALES_GENERAL])||{}).forEach(Rt=>{const gt=n==null?void 0:n.allTabsData[S.SALES_GENERAL][Rt];Array.isArray(gt)&&gt.forEach(Ie=>{var Y,ne,Ge,Je,Tt;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const v=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.SALES_GENERAL])==null?void 0:ne[S.SALES_GENERAL])==null?void 0:Ge[pe];let ke=xl(pe,v,n==null?void 0:n.allTabsData[S.SALES_GENERAL],(Tt=(Je=Ce==null?void 0:Ce[S.SALES_GENERAL])==null?void 0:Je[S.SALES_GENERAL])==null?void 0:Tt[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.SALES_GENERAL,itemID:S.SALES_GENERAL,keyName:pe,data:ke}))}})})),q===S.PURCHASING&&((Ct=(tn=J==null?void 0:J.copyPayload)==null?void 0:tn.payloadData)!=null&&Ct[S.PURCHASING_GENERAL])&&Object.keys((n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL])||{}).forEach(Rt=>{const gt=n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL][Rt];Array.isArray(gt)&&gt.forEach(Ie=>{var Y,ne,Ge,Je,Tt;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const v=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.PURCHASING_GENERAL])==null?void 0:ne[S.PURCHASING_GENERAL])==null?void 0:Ge[pe];let ke=xl(pe,v,n==null?void 0:n.allTabsData[S.PURCHASING_GENERAL],(Tt=(Je=Ce==null?void 0:Ce[S.PURCHASING_GENERAL])==null?void 0:Je[S.PURCHASING_GENERAL])==null?void 0:Tt[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.PURCHASING_GENERAL,itemID:S.PURCHASING_GENERAL,keyName:pe,data:ke}))}})}),q===S.STORAGE&&((Jt=(gn=(ft=J==null?void 0:J.copyPayload)==null?void 0:ft.payloadData)==null?void 0:gn[S.STORAGE_GENERAL])!=null&&Jt[S.STORAGE_GENERAL])&&Object.keys((n==null?void 0:n.allTabsData[S.STORAGE_GENERAL])||{}).forEach(Rt=>{const gt=n==null?void 0:n.allTabsData[S.STORAGE_GENERAL][Rt];Array.isArray(gt)&&gt.forEach(Ie=>{var Y,ne,Ge,Je,Tt;const pe=Ie==null?void 0:Ie.jsonName;if(pe){const v=(Ge=(ne=(Y=J==null?void 0:J.copyPayload)==null?void 0:Y.payloadData[S.STORAGE_GENERAL])==null?void 0:ne[S.STORAGE_GENERAL])==null?void 0:Ge[pe];let ke=xl(pe,v,n==null?void 0:n.allTabsData[S.STORAGE_GENERAL],(Tt=(Je=Ce==null?void 0:Ce[S.STORAGE_GENERAL])==null?void 0:Je[S.STORAGE_GENERAL])==null?void 0:Tt[pe]);ke&&_(pn({materialID:n==null?void 0:n.materialID,viewID:S.STORAGE_GENERAL,itemID:S.STORAGE_GENERAL,keyName:pe,data:ke}))}})}))})})},re=(T,J)=>{var Ce,L;let p={decisionTableId:null,decisionTableName:zs.SALES_DIV_PRICE_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_SALES_ORG":(Ce=J==null?void 0:J.salesOrg)==null?void 0:Ce.code,"MDG_CONDITIONS.MDG_MAT_DIVISION":(L=N==null?void 0:N.payloadData)==null?void 0:L.Division}]};T.org=J,j(p,T)},H=(T,J)=>{var Ce,L,q;let p={decisionTableId:null,decisionTableName:zs.REG_PLNT_INSPSTK_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(Ce=N==null?void 0:N.payloadData)==null?void 0:Ce.Region,"MDG_CONDITIONS.MDG_MAT_PLANT":(q=(L=J==null?void 0:J.plant)==null?void 0:L.value)==null?void 0:q.code}]};T.org=J,j(p,T)};i.useEffect(()=>{var p;if(!I||!M[n.activeViewTab])return;const{reduxCombinations:T=[]}=M[n.activeViewTab],J=T==null?void 0:T.map(Ce=>{var L;return((L=n==null?void 0:n.missingValidationPlant)==null?void 0:L.includes(Ce))&&n.mandatoryFailedView===n.activeViewTab});if(ce(J),n.missingValidationPlant||(p=n.missingValidationPlant)!=null&&p.length){const Ce=n.missingValidationPlant[0],L=V==null?void 0:V.current[Ce];L&&(L!=null&&L.scrollIntoView)&&setTimeout(()=>L.scrollIntoView({behavior:"smooth",block:"center"}),700)}},[n.activeViewTab,I,n==null?void 0:n.missingValidationPlant,M]);const fe=(T,J,p)=>(Ce,L)=>{ce(q=>({...q,[p]:L}))},Q=(T,J,p,Ce)=>{_(pn({materialID:(n==null?void 0:n.materialID)||"",keyName:J||"",data:p??null,viewID:Ce,itemID:T}))},Re=(T,J)=>T.some(p=>J.includes(p.fieldName)),he=i.useMemo(()=>{var O,mt,Le,Oe,ht,It,bt,dt,tn,Ct,ft,gn,Jt,Rt,gt;const T=M[n.activeViewTab]||{},{displayCombinations:J=[],reduxCombinations:p=[],requiredKeys:Ce=[]}=T,L=Object.entries((n==null?void 0:n.basicDataTabDetails)||{}),q=(O=n.allTabsData)!=null&&O.hasOwnProperty(S.SALES_GENERAL)?Object.entries(n.allTabsData[S.SALES_GENERAL]):[],b=(mt=n.allTabsData)!=null&&mt.hasOwnProperty(S.PURCHASING_GENERAL)?Object.entries(n.allTabsData[S.PURCHASING_GENERAL]):[],se=(Le=n.allTabsData)!=null&&Le.hasOwnProperty(S.STORAGE_GENERAL)?Object.entries(n.allTabsData[S.STORAGE_GENERAL]):[];return n.activeViewTab==="Basic Data"?f(kt,{children:[L.map(Ie=>{var pe;return f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(pe=n==null?void 0:n.missingValidationPlant)!=null&&pe.includes(S.BASIC_DATA)&&Re(Ie[1],n.missingFields)&&!(Te!=null&&Te.validated)?He.error.dark:He.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Ie[0]})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...Ie[1]].filter(Y=>Y.visibility!=="Hidden").sort((Y,ne)=>Y.sequenceNo-ne.sequenceNo).map(Y=>e(rl,{disabled:n==null?void 0:n.disabled,field:Y,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Y.fieldName))})})]},Ie[0])}),(n==null?void 0:n.moduleName)==="Article"&&e(Xr,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,loading:z})]}):n.activeViewTab===S.SUPPLIER_FORM||n.activeViewTab==="Characteristic"||n.activeViewTab==="Merchant Input"?f(kt,{children:[L.map(Ie=>{var pe;return f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${(pe=n==null?void 0:n.missingValidationPlant)!=null&&pe.includes(S.SUPPLIER_FORM)&&!(Te!=null&&Te.validated)?He.error.dark:He.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Ie[0]})}),f(Be,{children:[e(ve,{container:!0,spacing:2,children:[...Ie[1]].filter(Y=>Y.visibility!=="Hidden").filter(Y=>Y.fieldType!=="Table").sort((Y,ne)=>Y.sequenceNo-ne.sequenceNo).reduce((Y,ne,Ge,Je)=>{if(Ge%2===0){const Tt=[ne];Ge+1<Je.length&&Tt.push(Je[Ge+1]),Y.push(Tt)}return Y},[]).map((Y,ne)=>{var Ge,Je,Tt,v;return e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,alignItems:"center",children:[e(ve,{item:!0,xs:Y[1]?5:12,children:e(Rn,{title:(Ge=Y[0])!=null&&Ge.fieldTooltip?(Je=Y[0])==null?void 0:Je.fieldTooltip:"",placement:"top",children:e(Be,{sx:{width:"100%"},children:e(rl,{disabled:n==null?void 0:n.disabled,field:Y[0],dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",width:"35vw"},Y[0].fieldName)})})}),Y[1]&&f(kt,{children:[e(ve,{item:!0,xs:2,sx:{textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center"},children:e(st,{sx:{color:"gray",fontSize:"16px",fontWeight:"bold"},children:"|"})}),e(ve,{item:!0,xs:5,children:e(Rn,{title:(Tt=Y[1])!=null&&Tt.fieldTooltip?(v=Y[1])==null?void 0:v.fieldTooltip:"",placement:"top",children:e(Be,{sx:{width:"100%"},children:e(rl,{disabled:n==null?void 0:n.disabled,field:Y[1],dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",width:"35vw"},Y[1].fieldName)})})})]})]})},`row-${ne}`)})}),n.activeViewTab==="Supplier Form"&&Ie[0]==="All of the standard SAP UOM information"&&e(Be,{sx:{display:"flex",justifyContent:"start",alignItems:"center",mt:2,mb:2},children:e("img",{src:Nd,alt:"UOM Box",style:{maxWidth:"80%",height:"auto",borderRadius:"8px",boxShadow:"0px 2px 8px rgba(0, 0, 0, 0.1)"}})})]})]},Ie[0])}),e(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${He.hover.hoverbg}`,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:f(Be,{children:[n.activeViewTab===S.SUPPLIER_FORM&&e(Cd,{disabled:n==null?void 0:n.disabled,materialID:n.materialID}),n.activeViewTab==="Characteristic"&&e(_d,{disabled:n==null?void 0:n.disabled,materialID:n.materialID})]})},"supplierTable")]}):n.activeViewTab===S.CLASSIFICATION?f(kt,{children:[f(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${He.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:(Oe=L[0])==null?void 0:Oe[0]})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...(ht=L[0])==null?void 0:ht[1]].filter(Ie=>Ie.visibility!==ys.HIDDEN1).sort((Ie,pe)=>Ie.sequenceNo-pe.sequenceNo).map(Ie=>e(kt,{children:(Ie==null?void 0:Ie.visibility)==ys.HIDDEN?e(rl,{classNum:n==null?void 0:n.classNum,disabled:n==null?void 0:n.disabled,field:Ie,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",matType:n==null?void 0:n.matType,missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Ie.fieldName):e(rl,{classNum:n==null?void 0:n.classNum,disabled:n==null?void 0:n.disabled,field:Ie,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:"basic",matType:n==null?void 0:n.matType,missingFields:Array.isArray(n.missingFields)?n.missingFields:[]},Ie.fieldName)}))})})]},(It=L[0])==null?void 0:It[0]),(n==null?void 0:n.moduleName)===Ds.MAT&&e(qa,{characteristicDetails:L[1],materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,classNum:n==null?void 0:n.classNum,disabled:n.disabled,dropDownData:n.dropDownData,activeViewTab:n.activeViewTab}),(n==null?void 0:n.moduleName)===Ds.ART&&e(Fo,{characteristicDetails:L[1],materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,classNum:n==null?void 0:n.classNum,disabled:n.disabled,dropDownData:n.dropDownData,activeViewTab:n.activeViewTab})]}):J.length?f(kt,{children:[n.activeViewTab===S.SALES&&f(kt,{children:[e(Xr,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,loading:z}),(q==null?void 0:q.length)>0&&e(Ti,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:q,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(bt=S)==null?void 0:bt.SALES_GENERAL,isMandatoryFailed:((dt=n==null?void 0:n.missingValidationPlant)==null?void 0:dt.includes(S.SALES_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(tn=n.missingFields)==null?void 0:tn[S.SALES_GENERAL]})]}),n.activeViewTab===S.PURCHASING&&f(kt,{children:[" ",(b==null?void 0:b.length)>0&&e(Ti,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:b,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(Ct=S)==null?void 0:Ct.PURCHASING_GENERAL,isMandatoryFailed:((ft=n==null?void 0:n.missingValidationPlant)==null?void 0:ft.includes(S.PURCHASING_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(gn=n.missingFields)==null?void 0:gn[S.PURCHASING_GENERAL]})]}),n.activeViewTab===S.STORAGE&&f(kt,{children:[" ",(se==null?void 0:se.length)>0&&e(Ti,{materialID:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,GeneralFields:se,disabled:n.disabled,dropDownData:n.dropDownData,viewName:(Jt=S)==null?void 0:Jt.STORAGE_GENERAL,isMandatoryFailed:((Rt=n==null?void 0:n.missingValidationPlant)==null?void 0:Rt.includes(S.STORAGE_GENERAL))&&!(Te!=null&&Te.validated),missingFields:(gt=n.missingFields)==null?void 0:gt[S.STORAGE_GENERAL]})]}),J.map((Ie,pe)=>{var Y,ne,Ge,Je,Tt;return f(co,{ref:v=>{V.current[Ie]=v},sx:{marginBottom:"20px",boxShadow:3,borderRadius:"10px",borderColor:(Y=n==null?void 0:n.missingValidationPlant)!=null&&Y.includes(Ie)&&n.mandatoryFailedView===n.activeViewTab&&!(Te!=null&&Te.validated)?(Ge=(ne=He)==null?void 0:ne.error)==null?void 0:Ge.dark:(Je=He)==null?void 0:Je.primary.white},onChange:fe(Ie,Ce,pe),expanded:Se[pe]===!0,children:[e(uo,{expandIcon:e(ao,{}),sx:{backgroundColor:He.primary.whiteSmoke,borderRadius:"10px",padding:"8px 16px","&:hover":{backgroundColor:He.hover.hoverbg}},children:e(st,{variant:"h6",sx:{fontWeight:"bold"},children:a(Ie,Ce)})}),e(ho,{children:((Tt=D[pe])==null?void 0:Tt.value)===1?f(Be,{sx:{display:"flex",alignItems:"center",justifyContent:"center",minHeight:"200px"},children:[" ",e(Ii,{})]}):L.map(v=>f(ve,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:W(v[0])})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:[...v[1]].filter(ke=>ke.visibility!=="Hidden").sort((ke,yt)=>ke.sequenceNo-yt.sequenceNo).map(ke=>{var yt;return e(rl,{disabled:n==null?void 0:n.disabled,field:ke,dropDownData:n.dropDownData,materialID:n==null?void 0:n.materialID,selectedMaterialNumber:n==null?void 0:n.selectedMaterialNumber,viewName:n==null?void 0:n.activeViewTab,plantData:p[pe],missingFields:(yt=n.missingFields)==null?void 0:yt[p[pe]]},ke.fieldName)})})})]},v[0]))})]},pe)})]}):e(st,{variant:"body2",sx:{margin:"20px",color:"gray"},children:"No Org Data selected."})},[M,n.activeViewTab,n.basicDataTabDetails,D,n.materialID,n.missingValidationPlant,Se]);return e(kt,{children:he})},Id=go(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),Pl=[{code:"00",desc:"Single article"},{code:"01",desc:"Generic article"},{code:"10",desc:"Sales set"},{code:"11",desc:"Prepack"},{code:"12",desc:"Display"},{code:"22",desc:"Group Article"}],cl=[],bd=[{code:"I00X",desc:"Purchasing Org 1"},{code:"1710",desc:"Purchasing Org 2"},{code:"C01",desc:"Purchasing Org 3"}],Rd=[],Md=[],xd=[],Dd=n=>{var qi,Bi,Fi,Vi,wi,ji,Ji,Xi,zi,Yi,Ki,Qi,Zi,er,tr,nr,sr,lr,ir,rr,or,cr,ar,dr,ur,hr;const N=Id(),{customError:oe}=Os(),I=as(),{getDynamicWorkflowDT:M}=Uo(),{fetchMaterialFieldConfig:x}=Gi(),{getNextDisplayDataForCreate:ue}=vi(),{fetchValuationClassData:D}=Mo(),E=Z(t=>t.payload.payloadData),Se=E==null?void 0:E.RequestType,ce=Z(t=>t.request.salesOrgDTData),z=Z(t=>t.applicationConfig),R=Z(t=>t.paginationData),_=Z(t=>t.payload),j=Z(t=>t.request.requestHeader),u=Z(t=>t.request.materialRows),ie=Z(t=>t.payload.payloadData),W=Z(t=>{var s;return((s=t.AllDropDown)==null?void 0:s.dropDown)||{}}),V=Z(t=>t.tabsData.allTabsData);Z(t=>t.userManagement.userData),Z(t=>t.userManagement.roles);let a=Z(t=>t.userManagement.taskData);const K=Z(t=>t.tabsData.allMaterialFieldConfigDT),De=ol(),Fe=new URLSearchParams(De.search),ze=Fe.get("reqBench"),ge=Fe.get("RequestId"),[Te,ee]=i.useState(!1),[re,H]=i.useState(!1),[fe,Q]=i.useState(!1),[Re,he]=i.useState(0),[te,Mt]=i.useState(null),[T,J]=i.useState(null),[p,Ce]=i.useState(cl),[L,q]=i.useState({data:{},isVisible:!1}),[b,se]=i.useState(u||[]),O=Z(t=>t.selectedSections.selectedSections),[mt,Le]=i.useState(!!(b!=null&&b.length)),[Oe,ht]=i.useState(!1),[It,bt]=i.useState(!1),[dt,tn]=i.useState(""),{fetchTabSpecificData:Ct}=xo(),[ft,gn]=i.useState([]),[Jt,Rt]=i.useState(0),[gt,Ie]=i.useState(null),[pe,Y]=i.useState(!1),[ne,Ge]=i.useState(!0),[Je,Tt]=i.useState(!1),[v,ke]=i.useState(b.length+1),[yt,Ln]=i.useState(0),[Zt,nn]=i.useState(u.length>0),[Ht,lt]=i.useState({}),[$t,Ft]=i.useState({}),[yn,xt]=i.useState(0),[rn,vn]=i.useState([]),[Gn,Xt]=i.useState({}),[we,on]=i.useState({}),[Qn,an]=i.useState(!1),[de,vt]=i.useState(""),[Jn,Xn]=i.useState("Supplier Form"),[Tn,Et]=i.useState(!1);let fn={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null,distributionCenter:{value:null,options:[]},purchasingOrg:{value:null,options:[]},store:{value:null,options:[]},supplier:{value:null,options:[]}};const[P,A]=i.useState([fn]),[Ae,Me]=i.useState(!1),[le,Ve]=i.useState(null),[$e,Vt]=i.useState("yes"),[_e,Nn]=i.useState([]),[be,wt]=i.useState(null),Gt=(qi=_==null?void 0:_[be])==null?void 0:qi.headerData,[tt,zn]=i.useState("success"),[Wn,ds]=i.useState(!1),[ss,_t]=i.useState([]),[dn,qt]=i.useState(""),[Hn,G]=i.useState(""),[Mn,Ss]=i.useState(""),en=Z(t=>t.tabsData.articleViews),{checkValidation:us}=Sd(_,K,p),{t:B}=Cl(),{getDtCall:Is,dtData:qn}=ko(),[Ne,Cn]=i.useState(!1),[ls,Un]=i.useState({}),[is,sn]=i.useState({}),[En,Ye]=i.useState("10"),it="403055",[pt,Yt]=i.useState(0),[ln,jt]=i.useState(!1),wn=Z(t=>{var s;return((s=t.materialDropDownData)==null?void 0:s.dropDown)||{}}),Yn=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[jn,hs]=i.useState(null),[ms,_n]=i.useState(""),[gs,Zn]=i.useState(""),kn=i.useRef(P),[fs,bs]=i.useState(!1),Rs=(Bi=_==null?void 0:_[be])==null?void 0:Bi.payloadData,{fetchDataAndDispatch:ws}=Do(),Bn=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[ut,On]=i.useState({}),[vs,$]=i.useState(!1),[Ke,Qe]=i.useState(0),[Ze,U]=i.useState({"Material No":!1}),{getContryBasedOnPlant:et}=Lo({doAjax:We,customError:oe,fetchDataAndDispatch:ws,destination_ArticleMgmt:m}),[qe,rt]=i.useState([]),{filteredButtons:Kt,showWfLevels:In}=md(a,z,Fs,Vn),At=Di(Kt,[Bs.HANDLE_SUBMIT_FOR_APPROVAL,Bs.HANDLE_SAP_SYNDICATION,Bs.HANDLE_SUBMIT_FOR_REVIEW]),{showSnackbar:St}=Ri(),Fn=40;i.useEffect(()=>{var t,s,r,d,h,g,k,X,Pe,ae,me,w;if(se(u),nn((u==null?void 0:u.length)>0),be&&vt(be),(u==null?void 0:u.length)>0&&ge&&!(be||Mn)){wt((t=u==null?void 0:u[0])==null?void 0:t.id),Ss((s=u==null?void 0:u[0])==null?void 0:s.materialNumber),Tl((d=(r=u==null?void 0:u[0])==null?void 0:r.materialType)==null?void 0:d.code),vt((h=u==null?void 0:u[0])==null?void 0:h.id);const Ue=To(_),at=Eo(Ue);let Nt=JSON.parse(JSON.stringify(at));I(Ao(Nt)),I(fl({keyName:"selectedMaterialID",data:(g=u==null?void 0:u[0])==null?void 0:g.id})),(Pe=(X=_==null?void 0:_[(k=u==null?void 0:u[0])==null?void 0:k.id])==null?void 0:X.Tochildrequestheaderdata)!=null&&Pe.ChildRequestId&&I(fl({keyName:"childRequestId",data:(w=(me=_==null?void 0:_[(ae=u==null?void 0:u[0])==null?void 0:ae.id])==null?void 0:me.Tochildrequestheaderdata)==null?void 0:w.ChildRequestId}))}},[be,u]),i.useEffect(()=>{var t,s,r;(t=u==null?void 0:u[0])!=null&&t.materialType&&(As((s=u==null?void 0:u[0])==null?void 0:s.materialType),ei({row:u[0]}),El(u)&&(Ge(!1),Le(!1))),u!=null&&u.length&&xt((r=u==null?void 0:u.at(-1))==null?void 0:r.lineNumber),I(li({keyName:"VarOrdUn",data:Hc})),!(u!=null&&u.length)&&!ge&&Ts(),I(li({keyName:"Json452",data:qc})),I(li({keyName:"Json451",data:[{code:"COLOUR",desc:"TSHIRT_COLOUR"}]}))},[]),i.useEffect(()=>{var t,s,r,d;if(qn&&((t=qn==null?void 0:qn.customParam)==null?void 0:t.dt)===zs.MDG_ORG_ELEMENT_DEFAULT_VALUE){const h=Bc((d=(r=(s=qn==null?void 0:qn.data)==null?void 0:s.result)==null?void 0:r[0])==null?void 0:d.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);I(Fc({data:h}))}},[qn]);const Ts=()=>{let t={decisionTableId:null,decisionTableName:zs.MDG_ORG_ELEMENT_DEFAULT_VALUE,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_REGION":E==null?void 0:E.Region,"MDG_CONDITIONS.MDG_SCENARIO":E==null?void 0:E.RequestType}]};Is(t,{dt:zs.MDG_ORG_ELEMENT_DEFAULT_VALUE})};i.useEffect(()=>{const t=async()=>{var s,r;try{const d=await M(Se,E==null?void 0:E.Region,"",(r=(s=_[be])==null?void 0:s.Tochildrequestheaderdata)==null?void 0:r.MaterialGroupType,a==null?void 0:a.ATTRIBUTE_3,"v1","MDG_ARTICLE_DYNAMIC_WF_DT",Ds.ART);rt(d)}catch(d){oe(d)}};Se&&(E!=null&&E.Region)&&be&&(a!=null&&a.ATTRIBUTE_3)&&t()},[Se,E==null?void 0:E.Region,be,a==null?void 0:a.ATTRIBUTE_3]),i.useEffect(()=>{$e==="no"&&(On({}),Ve(null),Ie(null))},[$e]),i.useEffect(()=>{var t,s,r,d,h,g;be&&(V!=null&&V[S.BASIC_DATA])&&((t=_[be])!=null&&t.headerData.refMaterialData||_!=null&&_.OrgElementDefaultValues)&&!((d=(r=(s=_[be])==null?void 0:s.payloadData)==null?void 0:r["Basic Data"])!=null&&d.basic)&&!ge&&Es((g=(h=_[be])==null?void 0:h.headerData)==null?void 0:g.refMaterialData)},[be,V]),i.useEffect(()=>{(b==null?void 0:b.length)===0&&Le(!1)},[b,Ae]),i.useEffect(()=>{le!=null&&le.code?Gs(le==null?void 0:le.code,"extended"):Ft(t=>({...t,"Sales Org":[]})),On(t=>({...t,[Ws.SALES_ORG]:null}))},[le]),i.useEffect(()=>{var t;(t=ut==null?void 0:ut["Material Type"])!=null&&t.code&&(Sn(),Ve(null),Ie(null))},[(Fi=ut==null?void 0:ut["Material Type"])==null?void 0:Fi.code]),i.useEffect(()=>{["Distribution Channel","Plant"].forEach(s=>{On(r=>({...r,[s]:""})),$t[s]&&Ft(r=>({...r,[s]:[]}))})},[(Vi=ut==null?void 0:ut["Sales Org"])==null?void 0:Vi.code]),i.useEffect(()=>{["Storage Location","Warehouse"].forEach(s=>{On(r=>({...r,[s]:""})),$t[s]&&Ft(r=>({...r,[s]:[]}))})},[(wi=ut==null?void 0:ut.Plant)==null?void 0:wi.code]),i.useEffect(()=>{ut[Ws.SALES_ORG]&&(C(),On(t=>({...t,[Ws.DIST_CHNL]:null,[Ws.PLANT]:null})))},[ut[Ws.SALES_ORG]]);const Es=t=>{var g,k,X,Pe,ae,me;const s=((X=(k=(g=t==null?void 0:t.copyPayload)==null?void 0:g.payloadData)==null?void 0:k["Basic Data"])==null?void 0:X.basic)||{},r=((ae=(Pe=_==null?void 0:_.OrgElementDefaultValues)==null?void 0:Pe[S.BASIC_DATA])==null?void 0:ae[S.BASIC_DATA])||{};new Set([...Object.keys(r),...Object.keys(s)]).forEach(w=>{const Ue=(s==null?void 0:s[w])||"",at=(r==null?void 0:r[w])||"",Nt=w==="Division"?E==null?void 0:E.Division:xl(w,Ue,V["Basic Data"],at);I(pn({materialID:be,viewID:"Basic Data",itemID:"basic",keyName:w,data:Nt}))});let h=(me=t==null?void 0:t.copyPayload)==null?void 0:me.unitsOfMeasureData;if(h!=null&&h.length){let w=[];h==null||h.forEach(Ue=>{w.push({...Ue,id:(Ue==null?void 0:Ue.id)||w.length+1})}),I(Hr({materialID:be,data:w}))}},Gs=(t,s)=>{const r=h=>{U(g=>({...g,"Sales Org":!1})),(h==null?void 0:h.statusCode)===Wt.STATUS_200&&Ft(s==="notExtended"?g=>({...g,"Sales Org":h.body}):g=>({...g,"Sales Org":(h==null?void 0:h.body.length)>0?h.body:[]}))},d=()=>{U(h=>({...h,"Sales Org":!1}))};U(h=>({...h,"Sales Org":!0})),We(`/${m}/data/${s==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}`,"get",r,d)},Us=(t,s,r)=>{U(k=>({...k,Plant:!0}));const d=k=>{U(X=>({...X,Plant:!1})),(k==null?void 0:k.statusCode)===Wt.STATUS_200&&Ft(s==="notExtended"?X=>({...X,Plant:k.body}):X=>({...X,Plant:(k==null?void 0:k.body.length)>0?k.body:[]}))},h=()=>{U(k=>({...k,Plant:!1}))},g=r?`&salesOrg=${r.code}`:"";We(`/${m}/data/${s==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}${g}`,"get",d,h)},l=(t,s,r)=>{U(k=>({...k,Warehouse:!0}));const d=k=>{U(X=>({...X,Warehouse:!1})),(k==null?void 0:k.statusCode)===Wt.STATUS_200&&Ft(s==="notExtended"?X=>({...X,Warehouse:k.body}):X=>({...X,Warehouse:(k==null?void 0:k.body.length)>0?k.body:[]}))},h=()=>{U(k=>({...k,Warehouse:!1}))},g=r?`&plant=${r.code}`:"";We(`/${m}/data/${s==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${t}&region=${E==null?void 0:E.Region}${g}`,"get",d,h)},c=(t,s,r)=>{U(g=>({...g,"Storage Location":!0}));const d=g=>{U(k=>({...k,"Storage Location":!1})),(g==null?void 0:g.statusCode)===Wt.STATUS_200&&Ft(k=>{var X;return{...k,[(X=Ws)==null?void 0:X.STORAGE_LOC]:g.body||[]}})},h=g=>{oe(g),U(k=>({...k,"Storage Location":!1}))};We(`/${m}/data/getStorageLocationExtended?plant=${s==null?void 0:s.code}&materialNo=${t}&region=${E==null?void 0:E.Region}&salesOrg=${r==null?void 0:r.code}`,"get",d,h)},C=()=>{var r;U(d=>({...d,"Distribution Channel":!0}));const t=d=>{U(h=>({...h,"Distribution Channel":!1})),(d==null?void 0:d.statusCode)===Wt.STATUS_200&&Ft(h=>{var g;return{...h,[(g=Ws)==null?void 0:g.DIST_CHNL]:d.body&&(d==null?void 0:d.body)}})},s=d=>{oe(d),U(h=>({...h,"Distribution Channel":!1}))};We(`/${m}/data/getDistributionChannelExtended?materialNo=${le==null?void 0:le.code}&salesOrg=${(r=ut[Ws.SALES_ORG])==null?void 0:r.code}`,"get",t,s)};i.useEffect(()=>{["Mrp Profile"].forEach(Ko),(u==null?void 0:u.length)===0&&(Se===o.CREATE||Se===o.CREATE_WITH_UPLOAD)&&Me(!0),y(),F()},[]),i.useEffect(()=>{var s,r,d,h,g,k,X,Pe,ae,me,w,Ue,at,Nt,un,$n,Bt,rs,Ps,ps,nl,sl,ll,Js,Xs,hn;kn.current=P,P.some(Ms=>{var Dn,os,Ol;return((Dn=Ms==null?void 0:Ms.salesOrg)==null?void 0:Dn.code)&&!((Ol=(os=Ms==null?void 0:Ms.dc)==null?void 0:os.value)!=null&&Ol.code)})?bs(!1):((r=(s=P[0])==null?void 0:s.salesOrg)!=null&&r.code&&((g=(h=(d=P[0])==null?void 0:d.dc)==null?void 0:h.value)!=null&&g.code)||!((ae=(X=(k=_[be])==null?void 0:k.headerData)==null?void 0:X.views)!=null&&ae.includes((Pe=S)==null?void 0:Pe.SALES)))&&((Ue=(w=(me=P[0])==null?void 0:me.plant)==null?void 0:w.value)!=null&&Ue.code)&&((un=(Nt=(at=P[0])==null?void 0:at.sloc)==null?void 0:Nt.value)!=null&&un.code||!((Ps=(Bt=($n=_[be])==null?void 0:$n.headerData)==null?void 0:Bt.views)!=null&&Ps.includes((rs=S)==null?void 0:rs.STORAGE)))&&((sl=(nl=(ps=P[0])==null?void 0:ps.warehouse)==null?void 0:nl.value)!=null&&sl.code||(E==null?void 0:E.Region)==="EUR"||!((hn=(Js=(ll=_[be])==null?void 0:ll.headerData)==null?void 0:Js.views)!=null&&hn.includes((Xs=S)==null?void 0:Xs.WAREHOUSE)))?bs(!0):bs(!1)},[P]),i.useEffect(()=>{Le(!0),Ge(!0)},[(ji=_[be])==null?void 0:ji.headerData,(Ji=_[be])==null?void 0:Ji.payloadData]),i.useEffect(()=>{var t,s,r,d,h,g,k,X;(d=(r=(s=(t=_==null?void 0:_[be])==null?void 0:t.payloadData)==null?void 0:s.Classification)==null?void 0:r.basic)!=null&&d.Classtype&&xi((X=(k=(g=(h=_==null?void 0:_[be])==null?void 0:h.payloadData)==null?void 0:g.Classification)==null?void 0:k.basic)==null?void 0:X.Classtype,I)},[(Ki=(Yi=(zi=(Xi=_==null?void 0:_[be])==null?void 0:Xi.payloadData)==null?void 0:zi.Classification)==null?void 0:Yi.basic)==null?void 0:Ki.Classtype]),i.useEffect(()=>{var t,s,r,d,h,g;(s=(t=_[be])==null?void 0:t.headerData)!=null&&s.globalMaterialDescription&&(I(pn({materialID:be,viewID:"Supplier Form",itemID:"basic",keyName:"Json1",data:(d=(r=_[be])==null?void 0:r.headerData)==null?void 0:d.globalMaterialDescription})),I(pn({materialID:be,viewID:S.MERCHANT_INPUT,itemID:"basic",keyName:"Json1",data:(g=(h=_[be])==null?void 0:h.headerData)==null?void 0:g.globalMaterialDescription})))},[(Zi=(Qi=_[be])==null?void 0:Qi.headerData)==null?void 0:Zi.globalMaterialDescription]);const y=()=>{if(E!=null&&E.Region){const t=r=>{(r==null?void 0:r.statusCode)===Wt.STATUS_200&&Ft(d=>({...d,"Sales Organization":r.body?r==null?void 0:r.body:[]}))},s=r=>{oe(r)};We(`/${m}${Ot.DATA.GET_SALES_ORG}?region=${E==null?void 0:E.Region}`,"get",t,s)}},F=()=>{if(E!=null&&E.Region){const t=r=>{if((r==null?void 0:r.statusCode)===Wt.STATUS_200){let d=kn.current?JSON.parse(JSON.stringify(kn.current)):JSON.parse(JSON.stringify(P));Ft(h=>({...h,Plant:r.body?r==null?void 0:r.body:[]})),A(d),kn.current=d}},s=r=>{oe(r)};We(`/${m}${Ot.DATA.GET_PLANT}?region=${E==null?void 0:E.Region}`,"get",t,s)}},Ee=(t,s)=>{if(t){U(h=>({...h,[Al.STORAGE_LOCATION]:{...h[Al.STORAGE_LOCATION],[s]:!0}}));const r=h=>{if(U(g=>({...g,[Al.STORAGE_LOCATION]:{...g[Al.STORAGE_LOCATION],[s]:!1}})),(h==null?void 0:h.statusCode)===Wt.STATUS_200){let g=kn.current?JSON.parse(JSON.stringify(kn.current)):JSON.parse(JSON.stringify(P));s!==-1&&(g[s].sloc.options=Cs(h.body)),A(g),kn.current=g}},d=h=>{oe(h),U(g=>({...g,[Al.STORAGE_LOCATION]:{...g[Al.STORAGE_LOCATION],[s]:!1}}))};We(`/${m}${Ot.DATA.GET_STORAGE_LOCATION}?region=${E==null?void 0:E.Region}&plant=${t==null?void 0:t.code}`,"get",r,d)}},xe=(t,s="",r)=>new Promise((d,h)=>{var me;const g=[{materialNo:t,requestNo:s||(j==null?void 0:j.requestId)}],k=w=>{var Ue;(Ue=w==null?void 0:w.body)!=null&&Ue.length?(St(`Duplicate article number ${w.body[0].split("$^$")[0]} (${w.body[0].split("$^$")[1]})`,"error"),d(!0)):d(!1)},X=w=>{oe(w),d(!1)};let Pe=0;Object.keys(_).forEach((w,Ue)=>{var at,Nt;(w.includes("-")||/\d/.test(w))&&((Nt=(at=_[w])==null?void 0:at.headerData)==null?void 0:Nt.materialNumber)===t&&Pe++});let ae=0;Object.keys(_).forEach(w=>{var Ue,at,Nt,un;(w.includes("-")||/\d/.test(w))&&(at=(Ue=_[w])==null?void 0:Ue.headerData)!=null&&at.globalMaterialDescription&&((un=(Nt=_[w])==null?void 0:Nt.headerData)==null?void 0:un.globalMaterialDescription)===r&&ae++}),Pe>1?(St(`${tl.DUPLICATE_MATERIAL}${t}`,"error"),d(!0)):ae>1?(St(`${tl.DUPLICATE_MATERIAL_DESCRIPTION}${r}`,"error"),d(!0)):We(`/${m}${(me=Ot.MASS_ACTION)==null?void 0:me.MAT_NO_DUPLICATE_CHECK}`,"post",k,X,g)}),ye=async()=>{let t=[...b],s=!0;return _n(!0),Zn(ro.VALIDATING_MATS),new Promise(async(r,d)=>{for(let g=0;g<(b==null?void 0:b.length);g++){const k=b[g],{missingFields:X,viewType:Pe,isValid:ae,plant:me=[]}=us(k.id,(k==null?void 0:k.orgData)||[],!1,!1,!1);if(_t(me),ae){let w=!1;ae&&(!ge||k!=null&&k.isMatNoChanged)&&(w=await xe(k.materialNumber,ge,k==null?void 0:k.globalMaterialDescription)),w&&(s=!1),t=t==null?void 0:t.map(Ue=>Ue.id===k.id?{...Ue,validated:!w}:Ue),I(Pn(t))}else{if(s=!1,t=t.map(w=>w.id===k.id?{...w,validated:!1}:w),I(Pn(t)),(X==null?void 0:X.length)>0)if(typeof X=="object"&&!Array.isArray(X)){const w=Object.entries(X).map(([Ue,at])=>`Combination ${Ue}: ${at.join(", ")}`);St(`Line No ${k.lineNumber} : Please fill all the Mandatory fields in ${Pe||""}: ${w.join(" | ")}`,"error")}else St(`Line No ${k.lineNumber} : Please fill all the Mandatory fields in ${Pe||""}: ${X.join(", ")}`,"error");break}}s?r(!0):d(),_n(!1);const h=El(t);Le(!h),Ge(!h),s&&St("Validation successful for all articles.","success")})},ot=t=>{var s,r;if(t){let d=JSON.parse(JSON.stringify(((r=(s=_==null?void 0:_[be])==null?void 0:s.headerData)==null?void 0:r.calledMrpCodes)||[]))||[];t.forEach((g,k)=>{var X,Pe,ae,me,w,Ue,at,Nt,un;(X=g==null?void 0:g.mrpProfile)!=null&&X.code&&!((w=(ae=(Pe=_==null?void 0:_[be])==null?void 0:Pe.headerData)==null?void 0:ae.calledMrpCodes)!=null&&w.includes((me=g==null?void 0:g.mrpProfile)==null?void 0:me.code))&&(Xe((at=(Ue=g==null?void 0:g.plant)==null?void 0:Ue.value)==null?void 0:at.code,(Nt=g==null?void 0:g.mrpProfile)==null?void 0:Nt.code),d.push((un=g==null?void 0:g.mrpProfile)==null?void 0:un.code))}),I(xs({materialID:be,keyName:"calledMrpCodes",data:d}));const h=b==null?void 0:b.map(g=>g.id===be?{...g,calledMrpCodes:d}:g);I(Pn(h))}},Xe=(t,s,r)=>{var k;const d={mrpProfile:s},h=X=>{X.body[0]&&Object.keys(X==null?void 0:X.body[0]).filter(ae=>X==null?void 0:X.body[0][ae]).forEach(ae=>{Lt(t,ae,X==null?void 0:X.body[0][ae],S.MRP)})},g=X=>{oe(X)};We(`/${m}${(k=Ot.MASS_ACTION)==null?void 0:k.MRP_DEFAULT_VALUES}`,"post",h,g,d)},Lt=(t,s,r,d)=>{I(pn({materialID:be||"",keyName:s||"",data:r??null,viewID:d,itemID:t}))},ct=(t,s)=>{const r=h=>{(h==null?void 0:h.statusCode)===200?(Xt(g=>({...g,globalViews:h==null?void 0:h.body})),on(g=>({...g,[t]:h==null?void 0:h.body})),I(xs({materialID:t,keyName:"globalViews",data:h==null?void 0:h.body}))):(Xt(g=>({...g,globalViews:[]})),on(g=>({...g,[t]:[]})),I(xs({materialID:t,keyName:"globalViews",data:[]})))},d=h=>{oe(h)};We(`/${m}/data/getViewForMaterialType?materialType=${s}`,"get",r,d)};i.useEffect(()=>{El(u)&&(u!=null&&u.length)||ze||ge?(n.setCompleted([!0,!0]),n==null||n.setIsAttachmentTabEnabled(!0)):(n.setCompleted([!0,!1]),n==null||n.setIsAttachmentTabEnabled(!1))},[u]);const Ut=yn+10,An=()=>{var r,d,h;const t=ml(),s={id:t,included:!0,lineNumber:Ut,industrySector:(r=Yc)==null?void 0:r.DEFAULT_IND_SECTOR,articleCategory:((d=ie==null?void 0:ie.ArticleCategory)==null?void 0:d.code)??"",materialType:((h=ie==null?void 0:ie.MatlType)==null?void 0:h.code)??"",materialNumber:(le==null?void 0:le.code)||"",globalMaterialDescription:"",views:[],orgData:"",validated:oi.default,withReference:$e};I(Bl({materialID:t,data:s})),I(Pn([...b,s])),ke(v+1),xt(Ut),nn(!0),Le(!0),Ge(!0),wt(t),Tl("")},Dt=()=>{Me(!1),(E==null?void 0:E.RequestType)==="Create"?An():(E==null?void 0:E.RequestType)==="Change"&&ht(!0)},xn=()=>{ti()},Sn=(t="",s=!0)=>{var g,k;const r={materialNo:t??"",salesOrg:((g=ce==null?void 0:ce.uniqueSalesOrgList)==null?void 0:g.map(X=>X.code).join("$^$"))||"",top:500,skip:s?0:Jt,matlType:((k=ut==null?void 0:ut["Material Type"])==null?void 0:k.code)??""};U(X=>({...X,"Material No":!0}));const d=X=>{(X==null?void 0:X.statusCode)===Wt.STATUS_200&&(X!=null&&X.body)&&gn(s?X==null?void 0:X.body:Pe=>[...Pe,...X==null?void 0:X.body]),Y(!1),U(Pe=>({...Pe,"Material No":!1}))},h=()=>{Y(!1),U(X=>({...X,"Material No":!1}))};Y(!0),We(`/${m}/data/getSearchParamsMaterialNo`,"post",d,h,r)},As=t=>{const s=d=>{(d==null?void 0:d.statusCode)===Wt.STATUS_200&&vn(d==null?void 0:d.body)},r=d=>{oe(d,"while fetching the validation data of article number")};We(`/${m}/data/getNumberRangeForMaterialType?materialType=${t==null?void 0:t.code}`,"get",s,r)},js=((er=rn==null?void 0:rn[0])==null?void 0:er.External)==="X",Ul=((tr=rn==null?void 0:rn[1])==null?void 0:tr.External)==="X",Zl=rn==null?void 0:rn.some(t=>t.ExtNAwock==="X");(t=>{const s=new Set;let r=null;t==null||t.forEach(h=>{h.External==="X"&&h.ExtNAwock==="X"?(s.add(`External Number Range: Allowed (${h.FromNumber}-${h.ToNumber})`),s.add("Ext W/O Check: Allowed")):h.External!=="X"&&h.ExtNAwock==="X"?(s.add("Internal Number Range: Allowed"),s.add("Ext W/O Check: Allowed")):h.External==="X"&&h.ExtNAwock!=="X"?(s.add(`External Number Range: Allowed (${h.FromNumber}-${h.ToNumber})`),r="Ext W/O Check: Not Allowed"):h.External!=="X"&&h.ExtNAwock!=="X"&&(s.add("Internal Number Range: Allowed"),r="Ext W/O Check: Not Allowed")});const d=Array.from(s);return r&&d.push(r),d.map((h,g)=>e("div",{children:e(st,{children:h})},g))})(rn);function Tl(t){var d;const s=(E==null?void 0:E.Region)||_s.US;if(!K.some(h=>h[s]&&h[s][t])&&t)x(t,s);else if(!t)I(Nl({}));else{const h=K==null?void 0:K.find(g=>(g==null?void 0:g[s])&&(g==null?void 0:g[s][t]));h&&I(Nl((d=h[s][t])==null?void 0:d.allfields))}t&&D(t)}const Kn=t=>{const{id:s,field:r,value:d}=t;vt(s);let h=b.map(g=>g.id===s?{...g,[r]:d}:g);Xt(g=>({...g,[r]:d})),r===ri.MATERIALTYPE&&(As(d),No([fn]),I(xs({materialID:s,keyName:"orgData",data:""})),h=h.map(g=>g.id===s?{...g,orgData:""}:g),Tl(d==null?void 0:d.code)),r===ri.INCLUDED&&(El(h)?(Le(!1),Ge(!1)):(Le(!0),Ge(!0))),r===ri.VIEWS&&(Le(!0),Ge(!0)),se(h),I(xs({materialID:s,keyName:r,data:d})),I(Pn(h))},ei=t=>{var s,r,d,h,g;wt(t.row.id),Ss(t.row.materialNumber),Tl((r=(s=t==null?void 0:t.row)==null?void 0:s.materialType)==null?void 0:r.code),A((h=(d=t==null?void 0:t.row)==null?void 0:d.orgData)!=null&&h.length?(g=t.row)==null?void 0:g.orgData:[fn])},Vo=()=>{bt(!0)},ti=()=>{bt(!1)},ni=(t,s)=>{s==="backdropClick"||s==="escapeKeyDown"||Et(!1)},wo=()=>{Ce((we==null?void 0:we[de])||cl),Ln(0),Xn((we==null?void 0:we[de][0])||cl[0])},jo=()=>{if(Me(!1),$e==="yes")if(_e!=null&&_e.length){let t=[...b];_e==null||_e.forEach(s=>{var k,X;const r=ml();let d=JSON.parse(JSON.stringify(s));d!=null&&d.refMaterialData&&delete d.refMaterialData;let h=JSON.parse(JSON.stringify((k=_==null?void 0:_[s.id])==null?void 0:k.payloadData));d.id=r,d.lineNumber=Ut,d.globalMaterialDescription="",d.materialNumber="",d.validated=oi.default,I(Bl({materialID:r,data:d,payloadData:h})),t.push(d),se(t),I(Pn(t)),ke(v+1),xt(Ut),nn(!0),Le(!0),Ge(!0);let g=(X=_==null?void 0:_[s.id])==null?void 0:X.unitsOfMeasureData;if(g!=null&&g.length){let Pe=[];g==null||g.forEach(ae=>{var me,w,Ue;Pe.push({...ae,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(E==null?void 0:E.Region)===((me=_s)==null?void 0:me.US)?ae==null?void 0:ae.EanCat:"",eanUpc:(ae==null?void 0:ae.EanCat)==="MB"&&(E==null?void 0:E.Region)===((w=_s)==null?void 0:w.US)||(E==null?void 0:E.Region)===((Ue=_s)==null?void 0:Ue.EUR)?"":ae==null?void 0:ae.EanUpc,id:(ae==null?void 0:ae.id)||Pe.length+1})}),I(Hr({materialID:r,data:Pe}))}}),Nn([])}else le&&Jo();else Dt()},Jo=()=>{var d,h,g,k,X,Pe,ae;_n(!0);let t={material:le==null?void 0:le.code,wareHouseNumber:(d=ut==null?void 0:ut.Warehouse)==null?void 0:d.code,storageLocation:(h=ut==null?void 0:ut["Storage Location"])==null?void 0:h.code,salesOrg:(g=ut==null?void 0:ut["Sales Org"])==null?void 0:g.code,distributionChannel:(k=ut==null?void 0:ut["Distribution Channel"])==null?void 0:k.code,valArea:(X=ut==null?void 0:ut.Plant)==null?void 0:X.code,plant:(Pe=ut==null?void 0:ut.Plant)==null?void 0:Pe.code};const s=me=>{var w,Ue,at,Nt,un,$n,Bt,rs,Ps,ps,nl,sl,ll;if(_n(!1),On({}),me!=null&&me.body[0]){Fr(me==null?void 0:me.body,E);let Js=[...b];const Xs=ml();let hn={},Ms=[...cl,...(Ue=(((w=me.body[0])==null?void 0:w.Views)||"").split(",").map(Dn=>Dn.trim()==="Storage"?S.STORAGE:Dn.trim()))==null?void 0:Ue.filter(Dn=>!yi.includes(Dn)).filter(Dn=>!cl.includes(Dn))];hn.id=Xs,hn.included=!0,hn.lineNumber=Ut,hn.globalMaterialDescription="",hn.materialType={code:((at=me.body[0])==null?void 0:at.MatlType)||"",desc:((un=(Nt=W==null?void 0:W.MatlType)==null?void 0:Nt.find(Dn=>{var os;return Dn.code===((os=me.body[0])==null?void 0:os.MatlType)}))==null?void 0:un.desc)||""},hn.industrySector={code:(($n=me.body[0])==null?void 0:$n.IndSector)||"",desc:((rs=(Bt=W==null?void 0:W.IndSector)==null?void 0:Bt.find(Dn=>{var os;return Dn.code===((os=me.body[0])==null?void 0:os.IndSector)}))==null?void 0:rs.desc)||""},hn.articleCategory={code:((Ps=me.body[0])==null?void 0:Ps.ArticleCategory)||"01",desc:((ps=Pl==null?void 0:Pl.find(Dn=>{var os;return Dn.code===((os=me.body[0])==null?void 0:os.IndSector)}))==null?void 0:ps.desc)||""},hn.materialNumber="",hn.views=Ms,(E==null?void 0:E.Region)===((nl=_s)==null?void 0:nl.EUR)&&(hn.views=((sl=hn==null?void 0:hn.views)==null?void 0:sl.filter(Dn=>Dn!==S.WAREHOUSE))||[]),hn.validated=oi.default,hn.withReference=$e,hn.refMaterialData=Fr(me.body,E),I(Bl({materialID:Xs,data:hn,payloadData:{}})),Js.push(hn),se(Js),I(Pn(Js)),ke(v+1),xt(Ut),nn(!0),Le(!0),Ge(!0),Tl((ll=me.body[0])==null?void 0:ll.MatlType),wt(Xs)}else _n(!1),St(tl.NO_MATERIAL_FOUND,"warning"),Me(!0)},r=me=>{oe(me),_n(!1),Me(!0)};On({}),Ve(null),Ie(null),We(`/${m}${(ae=Ot.DATA)==null?void 0:ae.GET_COPY_MATERIAL}`,"post",s,r,t)},kl=!Ls.includes(n==null?void 0:n.requestStatus)||ge&&!ze,ks=(a==null?void 0:a.taskDesc)!=="Generic Article Activation Task"||!a||Object.keys(a).length===0,Xo=(t,s)=>{var r;Ba.fire({title:B("Are you sure?"),text:B("Changing the article type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(r=He.primary)==null?void 0:r.main,cancelButtonColor:He.error.red,confirmButtonText:B("Yes, do it!"),cancelButtonText:B("Cancel"),reverseButtons:!0}).then(d=>{d.isConfirmed&&(Kn({id:de,field:Ni.VIEWS,value:t}),I(Xc({materialId:s})),Kn({id:s,field:"materialType",value:t}))})},zo=t=>{var s,r;if(_){const d=(r=(s=_==null?void 0:_[be])==null?void 0:s.headerData)==null?void 0:r.TovariantData,h=Array.isArray(d)?d.some(g=>g&&g.id&&(g==null?void 0:g.id)!==""):!1;sn(g=>({...g,[t]:d})),Un(g=>({...g,[t]:d})),jt(h)}},ki=[{field:"included",headerName:B("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:t=>e(dl,{checked:t.row.included,disabled:kl,onChange:s=>Kn({id:t.row.id,field:"included",value:s.target.checked})})},{field:"lineNumber",headerName:B("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"materialType",headerName:B("Article Type"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Article Type"),e("span",{style:{color:"red"},children:"*"})]}),...Se===o.CREATE||Se===o.CREATE_WITH_UPLOAD?{renderCell:t=>e(Qt,{options:qr||[],value:t.row.materialType,onChange:s=>{t.row.materialType?Xo(s,t.row.id):Kn({id:t.row.id,field:"materialType",value:s})},placeholder:B("Select Article Type"),minWidth:"90%",listWidth:235})}:{editable:!1,renderCell:t=>{var s,r;return((r=(s=_==null?void 0:_[t.row.id])==null?void 0:s.headerData)==null?void 0:r.materialType)||""}}},{field:"articleCategory",headerName:B("Article Category"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Article Category"),e("span",{style:{color:"red"},children:"*"})]}),...Se===o.CREATE||Se===o.CREATE_WITH_UPLOAD?{renderCell:t=>e(Qt,{options:Pl||[],value:t.row.articleCategory,onChange:s=>Kn({id:t.row.id,field:"articleCategory",value:s}),placeholder:B("Select Article Category"),minWidth:"90%",listWidth:235})}:{editable:!1,renderCell:t=>{var s,r;return((r=(s=_==null?void 0:_[t.row.id])==null?void 0:s.headerData)==null?void 0:r.articleCategory)||""}}},{field:"materialNumber",headerName:B("Article Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",hide:ks,renderHeader:()=>f("span",{children:[B("Article Number"),!ks&&e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var g,k;const[s,r]=i.useState({[(g=t==null?void 0:t.row)==null?void 0:g.id]:t.row.materialNumber}),d=t.row.id,h=X=>{const Pe=X.target.value.toUpperCase(),ae=(E==null?void 0:E.Region)==="US"?Pe.replace(/[^A-Z0-9-]/g,"").replace(/-{2,}/g,"-"):Pe.replace(/[^A-Z0-9]/g,"");r(w=>({...w,[d]:ae})),Kn({id:t.row.id,field:"materialNumber",value:ae});const me=b.map(w=>w.id===t.row.id?{...w,isMatNoChanged:!0,materialNumber:ae}:w);I(Pn(me))};return e(kt,{children:(E==null?void 0:E.RequestType)===o.CREATE||(E==null?void 0:E.RequestType)===o.CREATE_WITH_UPLOAD?e(mn,{fullWidth:!0,placeholder:B("ENTER ARTICLE NUMBER"),variant:"outlined",size:"small",name:"article number",value:s[d]||((k=t==null?void 0:t.row)==null?void 0:k.materialNumber)||"",onChange:X=>{h(X)},sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:He.black.dark,color:He.black.dark}}},disabled:ks}):t.row.materialNumber})}},{field:"apparelMcat",headerName:B("Merchandising Category"),flex:.8,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:[B("Merchandising Category"),e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var r;const s=wn.MatlGroup||[];return e(Qt,{options:s.length>0?s:[],value:(r=t==null?void 0:t.row)==null?void 0:r.apparelMcat,onChange:d=>{Kn({id:t.row.id,field:"apparelMcat",value:d})},placeholder:B("Select Merchandising Category"),minWidth:"90%",listWidth:235})}},{field:"globalMaterialDescription",flex:.7,headerName:B("Article Description"),renderHeader:()=>f("span",{children:[B("Article Description"),e("span",{style:{color:"red"},children:"*"})]}),renderCell:t=>{var X,Pe;const[s,r]=i.useState({[(X=t==null?void 0:t.row)==null?void 0:X.id]:t.row.globalMaterialDescription}),d=t.row.id,h=i.useRef(null),g=ae=>{var Ue,at;const me=(at=(Ue=ae.target)==null?void 0:Ue.value)==null?void 0:at.toUpperCase();(me==null?void 0:me.length)>Fn&&(h.current||(h.current=setTimeout(()=>{St(`Article Description cannot exceed ${Fn} characters`,"error"),h.current=null},1e3)));const w=me.replace(/[^A-Z0-9-]/g,"").slice(0,Fn);r(Nt=>({...Nt,[d]:w})),Kn({id:t.row.id,field:"globalMaterialDescription",value:w})},k=(((Pe=s[d])==null?void 0:Pe.length)||0)===40;return e(Be,{sx:{display:"flex",alignItems:"center",width:"100%"},children:e(Rn,{title:s[d]||"",arrow:!0,placement:"top",children:e(mn,{fullWidth:!0,variant:"outlined",size:"small",placeholder:B("ENTER ARTICLE DESCRIPTION"),value:s[d]||"",onChange:g,error:k,sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:k?He.error.dark:void 0},"&:hover fieldset":{borderColor:k?He.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:k?He.error.dark:void 0}}}})})})},align:"center",headerAlign:"center",editable:!1},{...Se===o.CREATE||Se===o.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:1,align:"center",headerAlign:"center",hide:ks,renderCell:t=>{var s,r,d,h,g,k,X,Pe;return f(kt,{children:[e(nt,{variant:"contained",size:"small",disabled:!((s=t==null?void 0:t.row)!=null&&s.materialType)||ks,onClick:()=>{an(!0),vt(t.row.id)},children:B("Views")}),e(nt,{variant:"contained",disabled:!(((d=(r=t==null?void 0:t.row)==null?void 0:r.views)==null?void 0:d.length)>1)||ks,size:"small",sx:{marginLeft:"4px"},onClick:()=>{var ae,me,w;Et(!0),vt(t.row.id),A((me=(ae=t==null?void 0:t.row)==null?void 0:ae.orgData)!=null&&me.length?(w=t.row)==null?void 0:w.orgData:[fn])},children:B("Area of Validity")}),(((g=(h=t.row)==null?void 0:h.articleCategory)==null?void 0:g.code)==="01"||((k=t.row)==null?void 0:k.articleCategory)==="01")&&e(nt,{variant:"contained",disabled:ks,size:"small",sx:{marginLeft:"4px"},onClick:()=>{var ae,me;ge&&zo((ae=t==null?void 0:t.row)==null?void 0:ae.lineNumber),Cn(!0),Ye((me=t==null?void 0:t.row)==null?void 0:me.lineNumber)},children:B("Variant")}),(((Pe=(X=t.row)==null?void 0:X.articleCategory)==null?void 0:Pe.code)=="11"||t.row.articleCategory=="11")&&e(nt,{variant:"contained",size:"small",sx:{marginLeft:"4px"},onClick:()=>{Tt(!0),vt(t.row.id)},children:B("Components")})]})}}:{}},{field:"action",headerName:B("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:t=>{let s=Vc(t==null?void 0:t.row);const r=async d=>{var w,Ue,at;d.stopPropagation();const{missingFields:h,viewType:g,isValid:k,plant:X=[]}=us(t.row.id,((w=t==null?void 0:t.row)==null?void 0:w.orgData)||[],js,Ul,Zl);if(_t(X),h)if(typeof h=="object"&&!Array.isArray(h)){const Nt=Object.entries(h).map(([un,$n])=>`Combination ${un}: ${$n.join(", ")}`);St(`${B("Line No")} ${t.row.lineNumber} : ${B("Please fill all the Mandatory fields in")} ${g||""}: ${Nt.join(" | ")}`,"error")}else St(`${B("Line No")} ${t.row.lineNumber} : ${B("Please fill all the Mandatory fields in")} ${g||""}: ${h.join(", ")}`,"error");let Pe=!1;k&&(!ge||(Ue=t.row)!=null&&Ue.isMatNoChanged)&&!ks&&(Pe=await xe(t.row.materialNumber,ge,(at=t==null?void 0:t.row)==null?void 0:at.globalMaterialDescription)),s=k&&!Pe?"success":"error",s==="success"&&St("Validation successful","success");const ae=b.map(Nt=>Nt.id===t.row.id?{...Nt,validated:k&&!Pe}:Nt);I(Pn(ae));const me=El(ae);Le(!me),Ge(!me)};return f(Vs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[e(Rn,{title:s==="success"?"Validated Successfully":B(s==="error"?"Validation Failed":"Click to Validate"),children:e(cn,{onClick:r,color:s==="success"?"success":s==="error"?"error":"default",children:s==="error"?e(wc,{}):e(ld,{})})}),!kl&&e(Rn,{title:B("Delete Row"),children:e(cn,{onClick:()=>{q({...L,data:t,isVisible:!0})},color:"error",children:e(ul,{})})})]})}}],Yo=(t,s)=>{var r,d;Ln(s),Xn(((r=t==null?void 0:t.target)==null?void 0:r.id)==="AdditionalKey"?"Additional Data":(d=p==null?void 0:p.filter(h=>{var g;return(g=en==null?void 0:en[$s])==null?void 0:g.includes(h)}))==null?void 0:d[s])},Ko=t=>{const s={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},r=h=>{const g=Cs(h.body);Ft(k=>({...k,[t]:g}))},d=h=>oe(h);We(`/${m}/data${s[t]}`,"get",r,d)},Qo=t=>{Co(t,p,Rs,be,P,I,Qs)},Zo=t=>{zc(t,p,Rs,be,P,I,Qs,S)},ec=(t,s,r)=>(d,h)=>{var w,Ue,at;let g={},k="",X="";r==="Purchasing"||r==="Costing"?(g={materialNo:s==null?void 0:s.Material,plant:s==null?void 0:s.Plant},X=s==null?void 0:s.Plant,k=`/${m}/data/displayLimitedPlantData`):r==="Accounting"?(g={materialNo:s==null?void 0:s.Material,valArea:s==null?void 0:s.ValArea},X=s==null?void 0:s.ValArea,k=`/${m}/data/displayLimitedAccountingData`):r==="Sales"&&(g={materialNo:s==null?void 0:s.Material,salesOrg:s==null?void 0:s.SalesOrg,distChnl:s==null?void 0:s.DistrChan},X=`${s==null?void 0:s.SalesOrg}-${s==null?void 0:s.DistrChan}`,k=`/${m}/data/displayLimitedSalesData`);const Pe=Nt=>{var un,$n,Bt;r==="Purchasing"||r==="Costing"?I(Qs({materialID:be,viewID:r,itemID:s==null?void 0:s.Plant,data:(un=Nt==null?void 0:Nt.body)==null?void 0:un.SpecificPlantDataViewDto[0]})):r==="Accounting"?I(Qs({materialID:be,viewID:r,itemID:s==null?void 0:s.ValArea,data:($n=Nt==null?void 0:Nt.body)==null?void 0:$n.SpecificAccountingDataViewDto[0]})):r==="Sales"&&I(Qs({materialID:be,viewID:r,itemID:`${s==null?void 0:s.SalesOrg}-${s==null?void 0:s.DistrChan}`,data:(Bt=Nt==null?void 0:Nt.body)==null?void 0:Bt.SpecificSalesDataViewDto[0]}))},ae=()=>{};!((at=(Ue=(w=_==null?void 0:_[be])==null?void 0:w.payloadData)==null?void 0:Ue[r])!=null&&at[X])&&We(k,"post",Pe,ae,g),J(h?t:null)};function tc(){const t=p&&(P==null?void 0:P.length)>0&&(p==null?void 0:p.length)>0&&(p==null?void 0:p.filter(s=>{var r;return(r=en==null?void 0:en[$s])==null?void 0:r.includes(s)})[yt]);return V&&Jn&&(V[Jn]||Jn==="Additional Data")?Jn==="Additional Data"&&!ks?[e(Go,{disableCheck:ze&&!Ls.includes(n==null?void 0:n.requestStatus),materialID:be,selectedMaterialNumber:Mn})]:[e(Od,{disabled:ze&&!Ls.includes(n==null?void 0:n.requestStatus),selectedMaterialNumber:Mn,materialID:be,basicData:Ht,setBasicData:lt,dropDownData:$t,basicDataTabDetails:V[t],allTabsData:V,activeViewTab:t,selectedViews:p,handleAccordionClick:ec,missingValidationPlant:ss,isDisplay:ge||ze,moduleName:"Article"})]:e(kt,{})}const $i=t=>{var d,h;const s=((h=(d=t==null?void 0:t.target)==null?void 0:d.value)==null?void 0:h.toUpperCase())||"";Ie(null),Rt(0),te&&clearTimeout(te);const r=setTimeout(()=>{Sn(s,!0)},500);Mt(r)},nc=(t,s)=>{const r=le==null?void 0:le.code,d=$e==="yes"?"extended":"notExtended";On(h=>({...h,[t]:s})),t==="Sales Org"&&s?Us(r,d,s):t==="Plant"&&s&&(l(r,d,s),c(r,s,ut["Sales Org"]))},sc=(t,s,r)=>{t==="Sales Organization"&&(s?(A(d=>d.map((h,g)=>g===r?{...h,salesOrg:s}:h)),Pi(s,r).then(d=>{})):A(d=>d.map((h,g)=>g===r?{...h,salesOrg:null}:h)))},Pi=(t,s,r="",d="")=>new Promise((h,g)=>{U(ae=>({...ae,"Distribution Channel":{...ae["Distribution Channel"],[s]:!0}}));let k={salesOrg:t==null?void 0:t.code};const X=ae=>{U(w=>({...w,"Distribution Channel":{...w["Distribution Channel"],[s]:!1}}));let me=JSON.parse(JSON.stringify(r||kn.current));if(me[s].dc.options=Cs(ae.body),A(me),kn.current=me,d){I(xs({materialID:d==null?void 0:d.id,keyName:"orgData",data:me}));let w=(b==null?void 0:b.length)||[JSON.parse(JSON.stringify(d))],Ue=w.findIndex(at=>at.id===(d==null?void 0:d.id));w[Ue].orgData=me,I(Pn(w)),h({org:me,material:w[Ue]})}else h(""),U(w=>({...w,"Distribution Channel":{...w["Distribution Channel"],[s]:!1}}))},Pe=ae=>{oe(ae),U(me=>({...me,"Distribution Channel":{...me["Distribution Channel"],[s]:!1}}))};We(`/${m}/data/getDistrChan`,"post",X,Pe,k)}),lc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].dc.value=t,A(r)},ic=t=>{let s=JSON.parse(JSON.stringify(P));s.splice(t,1),A(s)},rc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].plant.value=t,r[s].sloc.value={},r[s].sloc.options=[],r[s].warehouse.value={},r[s].warehouse.options=[],A(r),Ee(t,s),kn.current=r},oc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].distributionCenter.value=t,A(r)},cc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].purchasingOrg.value=t,A(r)},ac=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].supplier.value=t,A(r)},dc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].store.value=t,A(r)},uc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].salesUnit.value=t,A(r)},hc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].plantGroup.value=t,A(r)},gc=(t,s)=>{let r=JSON.parse(JSON.stringify(P));r[s].priceList.value=t,A(r)},Tc=()=>{let t=JSON.parse(JSON.stringify(P));t.push(fn),A(t)},Ec=t=>{if(!(t!=null&&t.temp)||(t==null?void 0:t.temp)===(jn==null?void 0:jn.temp))return;_n(!0);let s={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(E==null?void 0:E.Region)||_s.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":t.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const r=h=>{var g,k;if(h.statusCode===Wt.STATUS_200){_n(!1);let X=(k=(g=h==null?void 0:h.data)==null?void 0:g.result[0])==null?void 0:k.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,Pe=[];X==null||X.forEach((ae,me)=>{var $n;let w=JSON.parse(JSON.stringify(fn));w.salesOrg={},w.salesOrg.code=ae.MDG_MAT_SALES_ORG,w.salesOrg.desc=ae.MDG_MAT_SALES_ORG_DESC,w.plant.value={},w.plant.value.code=ae.MDG_MAT_PLANT,w.plant.value.desc=ae.MDG_MAT_PLANT_DESC;let Ue=($n=Hn==null?void 0:Hn.filter(Bt=>Bt.MDG_MAT_SALES_ORG===ae.MDG_MAT_SALES_ORG))==null?void 0:$n.map(Bt=>({code:Bt.MDG_MAT_PLANT,desc:Bt.MDG_MAT_PLANT_DESC}));Ue=Ue==null?void 0:Ue.filter((Bt,rs,Ps)=>rs===Ps.findIndex(ps=>ps.code===Bt.code)),w.plant.options=Ue==null?void 0:Ue.sort((Bt,rs)=>Bt.code-rs.code);let at=Hn==null?void 0:Hn.filter(Bt=>Bt.MDG_MAT_SALES_ORG===ae.MDG_MAT_SALES_ORG&&Bt.MDG_MAT_PLANT===ae.MDG_MAT_PLANT),Nt=at==null?void 0:at.map(Bt=>({code:Bt.MDG_MAT_STORAGE_LOCATION,desc:Bt.MDG_MAT_STORE_LOC_DESC})),un=at==null?void 0:at.map(Bt=>Bt.MDG_MAT_WAREHOUSE?{code:Bt.MDG_MAT_WAREHOUSE,desc:Bt.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);ae.MDG_MAT_STORAGE_LOCATION&&(w.sloc.value={},w.sloc.value.code=ae.MDG_MAT_STORAGE_LOCATION,w.sloc.value.desc=ae.MDG_MAT_STORE_LOC_DESC),w.sloc.options=Nt,ae.MDG_MAT_WAREHOUSE&&(w.warehouse.value={},w.warehouse.value.code=ae.MDG_MAT_WAREHOUSE||"",w.warehouse.value.desc=ae.MDG_MAT_WAREHOUSE_DESC||""),w.warehouse.options=un,Pe.push(w)}),kn.current=Pe,A(Pe),pi(Pe,0)}else oe("Something went wrong"),_n(!1),St("No Area of Validity found","error")},d=h=>{oe("Something went wrong"),_n(!1),St("No Area of Validity found","error")};z.environment==="localhost"?We(`/${Fs}${Ot.INVOKE_RULES.LOCAL}`,"post",r,d,s):We(`/${Fs}${Ot.INVOKE_RULES.PROD}`,"post",r,d,s)},pi=async(t,s)=>{s<(t==null?void 0:t.length)&&(await Pi(t[s].salesOrg,s),s++,pi(t,s))},Ac=()=>{const t=L==null?void 0:L.data;se(b==null?void 0:b.filter(s=>{var r;return s.id!==((r=t==null?void 0:t.row)==null?void 0:r.id)})),I(_o(t==null?void 0:t.row.id)),Tl(""),I(Pn(b==null?void 0:b.filter(s=>{var r;return s.id!==((r=t==null?void 0:t.row)==null?void 0:r.id)}))),b!=null&&b.length?b.filter(s=>{var r,d;return((r=s.params)==null?void 0:r.id)!==((d=t==null?void 0:t.row)==null?void 0:d.id)}).every(s=>s.validated)&&Le(!1):Le(!1),q({...L,isVisible:!1})};i.useEffect(()=>{var g,k,X,Pe;const t=p==null?void 0:p.includes((g=S)==null?void 0:g.SALES),s=p==null?void 0:p.includes((k=S)==null?void 0:k.SALES_PLANT),r=p==null?void 0:p.includes((X=S)==null?void 0:X.STORAGE),d=p==null?void 0:p.includes((Pe=S)==null?void 0:Pe.STORAGE_PLANT),h=P==null?void 0:P.some(ae=>{var me,w;return(w=(me=ae==null?void 0:ae.plant)==null?void 0:me.value)==null?void 0:w.code});t&&!s&&h&&Ce(ae=>{var Ue,at;const me=[...ae],w=me.indexOf((Ue=S)==null?void 0:Ue.SALES);return me.splice(w+1,0,(at=S)==null?void 0:at.SALES_PLANT),me}),r&&!d&&Ce(ae=>{var Ue,at;const me=[...ae],w=me.indexOf((Ue=S)==null?void 0:Ue.STORAGE);return me.splice(w+1,0,(at=S)==null?void 0:at.STORAGE_PLANT),me})},[p,P]);const Wi=t=>{!t||!Array.isArray(t)||t.forEach(s=>{var r,d,h,g,k,X,Pe,ae,me,w,Ue,at,Nt,un,$n,Bt;if((d=(r=s.plant)==null?void 0:r.value)!=null&&d.code){if(Ct((g=(h=s.plant)==null?void 0:h.value)==null?void 0:g.code,S.PLANT),(k=s.salesOrg)!=null&&k.code||(Pe=(X=s.dc)==null?void 0:X.value)!=null&&Pe.code){const rs=`${((ae=s.salesOrg)==null?void 0:ae.code)||""}-${((w=(me=s.dc)==null?void 0:me.value)==null?void 0:w.code)||""}`;Ct(rs,S.SALES)}(at=(Ue=s.warehouse)==null?void 0:Ue.value)!=null&&at.code&&Ct((un=(Nt=s.warehouse)==null?void 0:Nt.value)==null?void 0:un.code,S.WAREHOUSE),et((Bt=($n=s.plant)==null?void 0:$n.value)==null?void 0:Bt.code)}})};i.useEffect(()=>{if(ge){const t=Gt==null?void 0:Gt.orgData;(t==null?void 0:t.length)>0&&t.some(s=>{var r,d,h,g,k;return((d=(r=s.plant)==null?void 0:r.value)==null?void 0:d.code)&&(((h=s.salesOrg)==null?void 0:h.code)||((k=(g=s.dc)==null?void 0:g.value)==null?void 0:k.code))})&&Wi(t)}},[Gt==null?void 0:Gt.orgData]);const Hi=t=>{I(Sl(t)),he(t)};i.useEffect(()=>{var t,s;(R==null?void 0:R.page)!==0&&(Se===((t=o)==null?void 0:t.CREATE_WITH_UPLOAD)||Se===((s=o)==null?void 0:s.CREATE))&&ue(),he((R==null?void 0:R.page)||0)},[R==null?void 0:R.page]);const Sc=()=>{ee(!Te),re&&H(!1)},mc=()=>{H(!re),Te&&ee(!1)},fc=()=>{var s,r;const t=(r=(s=_==null?void 0:_[be])==null?void 0:s.payloadData.Classification)==null?void 0:r.classification;if(Array.isArray(t)&&t.length>0){const d=[];t[1].value.map(h=>{t[0].value.map(g=>{d.push({size:g,item:!1,color:h,id:""})})}),Un(h=>({...h,[En]:d}))}Yt(d=>d+1)},$s=i.useMemo(()=>{var s;const t=b==null?void 0:b.find(r=>r.id===de);return((s=t==null?void 0:t.materialType)==null?void 0:s.code)??(t==null?void 0:t.materialType)},[b,de]);return i.useEffect(()=>{var t,s;if(de&&$s){const r=we==null?void 0:we[de],d=b==null?void 0:b.find(g=>g.id===de),h=d==null?void 0:d.views;if(r||((h==null?void 0:h.length)>0?on(g=>({...g,[de]:h})):ct(de,$s)),de&&((t=we[de])==null?void 0:t.length)>0&&((s=en[$s])==null?void 0:s.length)>0){const g=[...en[$s].filter(k=>we[de].includes(k)),...we[de].filter(k=>!en[$s].includes(k))];Ce(g),Ln(0),Xn(g[0]),Kn({id:de,field:"views",value:g})}}},[$s,de,we,en]),f("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:f(Be,{sx:{position:Te?"fixed":"relative",top:Te?0:"auto",left:Te?0:"auto",right:Te?0:"auto",bottom:Te?0:"auto",width:Te?"100vw":"100%",height:Te?"100vh":"auto",zIndex:Te?1004:void 0,backgroundColor:Te?"white":"transparent",padding:Te?"20px":"0",display:"flex",flexDirection:"column",boxShadow:Te?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[f(Be,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[e(st,{variant:"h6",children:B("Article Data")}),f(Be,{sx:{display:"flex",alignItems:"center",gap:1},children:[f(nt,{variant:"contained",color:"primary",size:"small",onClick:()=>{Se===o.CREATE&&(Me(!0),Nn([]),Ve(null),On({}),gn([]))},children:["+ ",B("Add")]}),e(Rn,{title:B(Te?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(cn,{onClick:Sc,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:Te?e(Qr,{}):e(Zr,{})})})]})]}),ge&&b&&(b==null?void 0:b.length)>0?e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e(yl,{rows:b,columns:ki,pageSize:50,autoHeight:!1,page:Re,rowCount:(R==null?void 0:R.totalElements)||0,rowsPerPageOptions:[50],onRowClick:ei,onCellEditCommit:Kn,onPageChange:t=>Hi(t),pagination:!0,disableSelectionOnClick:!0,getRowClassName:t=>t.id===be?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:Te?"calc(100vh - 150px)":`${Math.min(b.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):e(kt,{children:e("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:e("div",{style:{height:"100%"},children:e(yl,{autoHeight:!1,rows:b,columns:ki,pageSize:50,page:Re,rowsPerPageOptions:[50],onRowClick:ei,onCellEditCommit:Kn,onPageChange:t=>Hi(t),disableSelectionOnClick:!0,getRowClassName:t=>t.id===be?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:Te?"calc(100vh - 150px)":`${Math.min(b.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),Se===o.CREATE||Se===o.CREATE_WITH_UPLOAD||a!=null&&a.ATTRIBUTE_1?be&&Zt&&(b==null?void 0:b.length)>0&&(O==null?void 0:O.length)>0&&V&&((nr=Object.getOwnPropertyNames(V))==null?void 0:nr.length)>0&&f(Be,{sx:{position:re?"fixed":"relative",top:re?0:"auto",left:re?0:"auto",right:re?0:"auto",bottom:re?0:"auto",width:re?"100vw":"100%",height:re?"100vh":"auto",zIndex:re?1004:void 0,backgroundColor:re?"white":"transparent",padding:re?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:re?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[f(Be,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[e(st,{variant:"h6",children:B("View Details")}),e(Rn,{title:B(re?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:e(cn,{onClick:mc,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:re?e(Qr,{}):e(Zr,{})})})]}),f(Be,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[f(So,{value:yt,onChange:Yo,className:N.customTabs,"aria-label":"article tabs",sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:He.background.container,borderBottom:`1px solid ${He.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:He.black.graphite,"&.Mui-selected":{color:He.primary.main,fontWeight:700},"&:hover":{color:He.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:He.primary.main,height:"3px"}},children:[p&&(P==null?void 0:P.length)>0&&(p==null?void 0:p.length)>0&&((sr=en==null?void 0:en[$s])==null?void 0:sr.length)>0?(()=>{const t=(a==null?void 0:a.taskDesc)==="Supplier Data Review Task";let s=p.filter(r=>{var d;return(d=en==null?void 0:en[$s])==null?void 0:d.includes(r)});return t&&!fe&&(s=s.filter(r=>r==="Supplier Form")),s.map((r,d)=>e(wl,{label:B(r)},d))})():null,!ks&&e(wl,{label:B("Additional Data"),id:"AdditionalKey"},"Additional data")]}),e(Be,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:re?"calc(100vh - 180px)":"auto"},children:tc()}),(!kl||ge&&!ze||ze&&Ls.includes(n==null?void 0:n.requestStatus))&&e(Be,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:e(ed,{activeTab:yt,submitForApprovalDisabled:!El(u),filteredButtons:At,validateMaterials:ye,workFlowLevels:qe,showWfLevels:In,childRequestHeaderData:(lr=_==null?void 0:_[be])==null?void 0:lr.Tochildrequestheaderdata,setIsAccepted:Q,module:(ir=Ds)==null?void 0:ir.ART})})]})]}):e(kt,{}),e(Ql,{dialogState:It,openReusableDialog:Vo,closeReusableDialog:ti,dialogTitle:"Warning",dialogMessage:dt,showCancelButton:!1,handleOk:xn,handleDialogConfirm:ti,dialogOkText:"OK",dialogSeverity:"danger"}),Ne&&f(Hs,{open:Ne,onClose:()=>Cn(!1),maxWidth:"lg",fullWidth:!0,children:[e(qs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem",display:"flex",alignItems:"center",justifyContent:"space-between"},children:f(ve,{container:!0,alignItems:"center",children:[f(ve,{item:!0,xs:10,children:[B("Variants"),f(st,{children:["Generic Article: ",it]})]}),e(ve,{item:!0,xs:2,sx:{display:"flex",justifyContent:"flex-end"},children:e(cn,{"aria-label":"close",onClick:()=>Cn(!1),sx:{color:t=>t.palette.grey[500]},size:"large",children:e(Ll,{})})})]})}),e(Be,{sx:{px:10,py:1},children:f(mo,{activeStep:pt,sx:{justifyContent:"center"},children:[e(mi,{children:e(fi,{onClick:()=>Yt(0),children:B("Classification")})}),e(mi,{children:e(fi,{onClick:()=>Yt(1),children:B("Variants")})})]})}),e(Be,{sx:{display:"flex",justifyContent:"end",mr:4},children:pt===1&&e(Rn,{title:B("Generate Variant IDs"),disableInteractive:!0,placement:"top",children:e("span",{children:e(nt,{onClick:()=>{const t=(ls[En]||[]).map((s,r)=>({...s,id:s.item?`${it}${(r+1).toString().padStart(2,"0")}`:""}));Un(s=>({...s,[En]:t})),I(xs({materialID:be,keyName:"TovariantData",data:t}))},variant:"outlined",color:"primary",children:e(Wo,{sx:{verticalAlign:"middle"}})})})})}),pt===0&&f(kt,{children:[f(es,{children:[f(ve,{md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:`1px solid ${He.hover.hoverbg} `,mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...al},children:[e(ve,{container:!0,children:e(st,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:V.Classification&&Object.keys(V.Classification).length>0?(rr=Object.entries(V.Classification||{})[0])==null?void 0:rr[0]:{}})}),e(Be,{children:e(ve,{container:!0,spacing:1,children:(cr=(or=Object.entries(V.Classification||{})[0])==null?void 0:or[1])==null?void 0:cr.map(t=>e(kt,{children:e(rl,{field:t,dropDownData:$t,materialID:be,selectedMaterialNumber:Mn,viewName:"Classification",plantData:"basic",matType:"Article"},t.fieldName)}))})})]},V.Classification&&Object.keys(V.Classification).length>0?(ar=Object.entries(V.Classification||{})[0])==null?void 0:ar[0]:{}),e(Fo,{characteristicDetails:["Characteristic Details"],materialID:be,selectedMaterialNumber:Mn,disabled:ze&&!Ls.includes(n==null?void 0:n.requestStatus),dropDownData:$t,activeViewTab:"Classification"})]}),e(ts,{children:e(nt,{onClick:()=>fc(),variant:"contained",color:"primary",children:B("Next")})})]}),pt===1&&f(kt,{children:[e(es,{children:ge&&ln?Array.isArray(is[En])&&e(Be,{mb:2,children:f(Wl,{children:[e(Hl,{children:f(il,{children:[e(je,{children:e("b",{children:B("Size Ch. Value")})}),e(je,{children:e("b",{children:B("Colour Ch. Value")})}),e(je,{children:B("")}),e(je,{children:e("b",{children:B("Generic ID")})})]})}),e(ql,{children:(dr=is[En])==null?void 0:dr.map((t,s)=>f(il,{children:[e(je,{children:t==null?void 0:t.size}),e(je,{children:t==null?void 0:t.color}),e(je,{children:e(dl,{checked:t==null?void 0:t.item,onChange:r=>{const d=ls[En].map((h,g)=>g===s?{...h,item:r.target.checked}:h);Un(h=>({...h,[En]:d}))}})}),e(je,{children:t==null?void 0:t.id})]},t.id))})]})}):Array.isArray(ls[En])&&e(Be,{mb:2,children:f(Wl,{children:[e(Hl,{children:f(il,{children:[e(je,{children:e("b",{children:B("Size Ch. Value")})}),e(je,{children:e("b",{children:B("Colour Ch. Value")})}),e(je,{children:B("")}),e(je,{children:e("b",{children:B("Generic ID")})})]})}),e(ql,{children:(ur=ls[En])==null?void 0:ur.map((t,s)=>f(il,{children:[e(je,{children:t==null?void 0:t.size}),e(je,{children:t==null?void 0:t.color}),e(je,{children:e(dl,{checked:t==null?void 0:t.item,onChange:r=>{const d=ls[En].map((h,g)=>g===s?{...h,item:r.target.checked}:h);Un(h=>({...h,[En]:d}))}})}),e(je,{children:t==null?void 0:t.id})]},s))})]})})}),f(ts,{children:[e(nt,{onClick:()=>Yt(t=>t-1),variant:"outlined",color:"primary",children:B("Back")}),e(nt,{onClick:()=>{Cn(!1),Kn({id:de,field:"materialNumber",value:it})},variant:"contained",color:"primary",children:B("Save")})]})]})]}),Qn&&e(Hs,{fullWidth:!0,maxWidth:!1,open:!0,onClose:ni,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:f(Be,{sx:{width:"600px !important"},children:[f(qs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[e(vl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),e("span",{children:B("Select Views")})]}),e(es,{sx:{paddingBottom:".5rem"},children:f(Be,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[e(hl,{size:"small",multiple:!0,fullWidth:!0,options:(we==null?void 0:we[de])||[],disableCloseOnSelect:!0,value:p,slotProps:{paper:{sx:{maxHeight:250,overflow:"hidden"}}},onChange:(t,s)=>{a!=null&&a.requestId||(Ce([...cl,...s.filter(r=>!cl.includes(r))]),Kn({id:de,field:Ni.VIEWS,value:s}))},renderOption:(t,s,{selected:r})=>f("li",{...t,children:[e(dl,{checked:r,sx:{marginRight:1}}),s]}),renderTags:(t,s)=>t.map((r,d)=>{const{key:h,...g}=s({index:d});return e(Mi,{label:r,...g},h)}),renderInput:t=>e(mn,{...t,label:B("Select Views")})}),e(nt,{variant:"contained",size:"small",onClick:()=>wo(),children:B("Select all")})]})}),e(ts,{children:e(nt,{onClick:()=>{an(!1),Kn({id:de,field:"views",value:p})},variant:"contained",children:B("Ok")})})]})}),Tn&&f(Hs,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:ni,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[f(qs,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[e(vl,{fontSize:"small"}),e("span",{children:B("Select Area of Validity")}),e(Be,{sx:{position:"absolute",right:"7%",width:"15%"},children:e(hl,{options:Yn.filter(t=>t.region===(E==null?void 0:E.Region)),value:jn,size:"small",disabled:kl,isOptionEqualToValue:(t,s)=>t.region===s.region,onChange:(t,s)=>{hs(s),Ec(s)},getOptionLabel:t=>t==null?void 0:t.temp,renderInput:t=>e(mn,{...t,label:B("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),e(cn,{onClick:ni,sx:{position:"absolute",right:15},children:e(Ll,{})})]}),e(es,{sx:{padding:0},children:e(fo,{component:Kl,children:f(Wl,{children:[e(Hl,{children:f(il,{children:[e(je,{align:"center",children:B("S NO.")}),e(je,{align:"center",children:B("Sales Org")}),e(je,{align:"center",children:B("Distribution Channel")}),e(je,{align:"center",children:B("Plant")}),e(je,{align:"center",children:B("Distribution Center")}),e(je,{align:"center",children:B("Purchasing Org")}),e(je,{align:"center",children:B("Supplier")}),e(je,{align:"center",children:B("Store")}),e(je,{align:"center",children:B("Sales Unit")}),e(je,{align:"center",children:B("Plant Group")}),e(je,{align:"center",children:B("Price List")}),P.length>1&&e(je,{align:"center",children:B("Action")})]})}),e(ql,{children:P.map((t,s)=>{var r,d,h,g,k,X,Pe,ae,me,w,Ue,at,Nt,un,$n,Bt,rs,Ps,ps,nl,sl,ll,Js,Xs,hn,Ms,Dn,os,Ol,gr,Tr,Er,Ar,Sr,mr,fr,Nr,Cr,_r,Or,Ir,br,Rr,Mr,xr,Dr,Lr,yr,vr;return f(il,{sx:{padding:"12px"},children:[e(je,{children:e(st,{variant:"body2",children:s+1})}),e(je,{children:e(Qt,{options:$t["Sales Organization"],value:t.salesOrg,onChange:bn=>sc("Sales Organization",bn,s),placeholder:B("Select Sales Org"),minWidth:165,listWidth:215,title:((r=t==null?void 0:t.salesOrg)==null?void 0:r.code)+` - ${(d=t==null?void 0:t.salesOrg)==null?void 0:d.desc}`,disabled:!ii(hi.salesOrg,p)})}),e(je,{children:e(Qt,{options:((h=t.dc)==null?void 0:h.options)||[],isLoading:((g=Ze["Distribution Channel"])==null?void 0:g[s])||!1,value:(k=t.dc)==null?void 0:k.value,onChange:bn=>lc(bn,s),placeholder:B("Select DC"),disabled:!ii(hi.distributionChannel,p),minWidth:165,listWidth:215,title:((Pe=(X=t==null?void 0:t.dc)==null?void 0:X.value)==null?void 0:Pe.code)+` - ${(me=(ae=t==null?void 0:t.dc)==null?void 0:ae.value)==null?void 0:me.desc}`})}),e(je,{children:e(Qt,{options:$t.Plant||[],value:(w=t.plant)==null?void 0:w.value,onChange:bn=>rc(bn,s),placeholder:B("Select Plant"),disabled:!ii(hi.plant,p),minWidth:165,listWidth:215,title:((at=(Ue=t==null?void 0:t.plant)==null?void 0:Ue.value)==null?void 0:at.code)+` - ${(un=(Nt=t==null?void 0:t.plant)==null?void 0:Nt.value)==null?void 0:un.desc}`})}),e(je,{children:e(Qt,{options:$t.Plant||[],value:($n=t.distributionCenter)==null?void 0:$n.value,onChange:bn=>oc(bn,s),placeholder:B("Select Distribution Center"),minWidth:165,listWidth:215,title:((rs=(Bt=t==null?void 0:t.distributionCenter)==null?void 0:Bt.value)==null?void 0:rs.code)+` - ${(ps=(Ps=t==null?void 0:t.distributionCenter)==null?void 0:Ps.value)==null?void 0:ps.desc}`})}),e(je,{children:e(Qt,{options:bd||[],value:(nl=t.purchasingOrg)==null?void 0:nl.value,onChange:bn=>cc(bn,s),placeholder:B("Select Purchasing Org"),minWidth:165,listWidth:215,title:((ll=(sl=t==null?void 0:t.purchasingOrg)==null?void 0:sl.value)==null?void 0:ll.code)+` - ${(Xs=(Js=t==null?void 0:t.purchasingOrg)==null?void 0:Js.value)==null?void 0:Xs.desc}`})}),e(je,{children:e(Qt,{options:wn.Json301||[],value:(hn=t.supplier)==null?void 0:hn.value,onChange:bn=>ac(bn,s),placeholder:B("Select Supplier"),minWidth:165,listWidth:215,title:((Dn=(Ms=t==null?void 0:t.supplier)==null?void 0:Ms.value)==null?void 0:Dn.code)+` - ${(Ol=(os=t==null?void 0:t.supplier)==null?void 0:os.value)==null?void 0:Ol.desc}`})}),e(je,{children:e(Qt,{options:$t.Plant||[],value:(gr=t.store)==null?void 0:gr.value,onChange:bn=>dc(bn,s),placeholder:B("Select Store"),minWidth:165,listWidth:215,title:((Er=(Tr=t==null?void 0:t.store)==null?void 0:Tr.value)==null?void 0:Er.code)+` - ${(Sr=(Ar=t==null?void 0:t.store)==null?void 0:Ar.value)==null?void 0:Sr.desc}`})}),e(je,{children:e(Qt,{options:Rd||[],value:(mr=t.salesUnit)==null?void 0:mr.value,onChange:bn=>uc(bn,s),placeholder:B("Select Sales Unit"),minWidth:165,listWidth:215,title:((Nr=(fr=t==null?void 0:t.salesUnit)==null?void 0:fr.value)==null?void 0:Nr.code)+` - ${(_r=(Cr=t==null?void 0:t.salesUnit)==null?void 0:Cr.value)==null?void 0:_r.desc}`})}),e(je,{children:e(Qt,{options:Md||[],value:(Or=t.plantGroup)==null?void 0:Or.value,onChange:bn=>hc(bn,s),placeholder:B("Select Plant Group"),minWidth:165,listWidth:215,title:((br=(Ir=t==null?void 0:t.plantGroup)==null?void 0:Ir.value)==null?void 0:br.code)+` - ${(Mr=(Rr=t==null?void 0:t.plantGroup)==null?void 0:Rr.value)==null?void 0:Mr.desc}`})}),e(je,{children:e(Qt,{options:xd||[],value:(xr=t.priceList)==null?void 0:xr.value,onChange:bn=>gc(bn,s),placeholder:B("Select Price List"),minWidth:165,listWidth:215,title:((Lr=(Dr=t==null?void 0:t.priceList)==null?void 0:Dr.value)==null?void 0:Lr.code)+` - ${(vr=(yr=t==null?void 0:t.priceList)==null?void 0:yr.value)==null?void 0:vr.desc}`})}),P.length>1&&f(je,{align:"right",children:[e(cn,{size:"small",color:"primary",onClick:()=>{$(!0),Qe({orgRowLength:P.length,copyFor:s});const bn=P.filter(si=>{var Il,bl;return(bl=(Il=si.plant)==null?void 0:Il.value)==null?void 0:bl.code}).map(si=>{var Il,bl;return(bl=(Il=si.plant)==null?void 0:Il.value)==null?void 0:bl.code});$e==="yes"&&Zo(bn)},style:{display:s===0?"none":"inline-flex"},children:e(Oi,{})}),e(cn,{size:"small",color:"error",onClick:()=>ic(s),children:e(ul,{})})]})]},s)})})]})})}),f(ts,{sx:{justifyContent:"flex-end",gap:.5},children:[f(nt,{onClick:Tc,variant:"contained",disabled:!fs,children:["+ ",B("Add")]}),e(Rn,{title:fs?"":B("Please fill all the fields of first row at least"),arrow:!0,children:e("span",{children:e(nt,{onClick:()=>{var t,s;if(Et(!1),(s=(t=P[0].plant)==null?void 0:t.value)!=null&&s.code){Wi(P),Kn({id:de,field:"orgData",value:P}),ot(P);const r=b==null?void 0:b.map(d=>d.id===be?{...d,orgData:P}:d);if(I(Pn(r)),$e==="no"){const d=P.filter(h=>{var g,k;return(k=(g=h.plant)==null?void 0:g.value)==null?void 0:k.code}).map(h=>{var g,k;return(k=(g=h.plant)==null?void 0:g.value)==null?void 0:k.code});d.length>0&&Qo(d)}hs(null)}},variant:"contained",disabled:!fs,tooltip:fs?"":B("Please fill all the fields of first row at least"),children:B("Ok")})})})]})]}),Ae&&f(Hs,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[e(qs,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0.75rem 1rem",backgroundColor:"#EAE9FF",borderBottom:"1px solid #d6d6f0"},children:f(Be,{sx:{display:"flex",alignItems:"center"},children:[e(id,{sx:{mr:1,color:"#3C3C66"}}),e(st,{variant:"h6",sx:{fontWeight:600,color:"#3C3C66"},children:B("Add New Article")})]})}),f(es,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[f(oo,{component:"fieldset",sx:{paddingBottom:"2%"},children:[e(jc,{component:"legend",sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px"},children:B("How would you like to proceed?")}),f(Jc,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:$e,onChange:t=>Vt(t.target.value),children:[e(Si,{value:"yes",control:e(Br,{}),label:B("With Reference")}),e(Si,{value:"no",control:e(Br,{}),label:B("Without Reference")})]})]}),f(ve,{container:!0,spacing:2,children:[e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,children:[e(ve,{item:!0,xs:3,children:e(Qt,{options:qr||[],value:ut[Ws.MATERIAL_TYPE]||"",onChange:t=>{On(s=>({...s,[Ws.MATERIAL_TYPE]:t}))},placeholder:B("Select Article Type"),minWidth:180,listWidth:266,disabled:(_e==null?void 0:_e.length)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]})})}),e(ve,{item:!0,xs:3,children:e(Qt,{options:ft,value:gt||le,onChange:t=>{Ve(t),Ie(t),t||$i(t)},minWidth:180,listWidth:266,placeholder:B("Select Article"),disabled:(_e==null?void 0:_e.length)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]}),handleInputChange:$i,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:Ze["Material No"]})}),Bn==null?void 0:Bn.slice(0,2).map(t=>e(ve,{item:!0,xs:3,children:e(Qt,{options:($t==null?void 0:$t[t])||[],value:ut[t]||"",onChange:s=>{nc(t,s)},placeholder:B(`Select ${t}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze[t]})},t))]})}),e(ve,{item:!0,xs:12,children:f(ve,{container:!0,spacing:2,alignItems:"center",children:[e(ve,{item:!0,xs:3,children:e(Qt,{options:($t==null?void 0:$t[Bn[2]])||[],value:ut[Bn[2]]||"",onChange:t=>{On(s=>({...s,[Bn[2]]:t}))},placeholder:B(`Select ${Bn[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze["Distribution Channel"]===!0})}),Bn==null?void 0:Bn.slice(3).map(t=>e(ve,{item:!0,xs:3,children:e(Qt,{options:($t==null?void 0:$t[t])||[],value:ut[t]||"",onChange:s=>{On(r=>({...r,[t]:s}))},placeholder:B(`Select ${t}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(_e==null?void 0:_e.length)||$e==="no",isLoading:Ze[t]})},t)),(u==null?void 0:u.length)>0&&f(kt,{children:[e(ve,{item:!0,xs:1,sx:{textAlign:"center"},children:e(st,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),e(ve,{item:!0,xs:3,children:e(Qt,{options:u.map(t=>({...t,code:t.lineNumber,desc:""})),value:_e[0],onChange:t=>{Nn(t?[t]:[]),On({}),Ve(null),Ie(null)},minWidth:180,listWidth:266,placeholder:B("Select Article Line Number"),disabled:(le==null?void 0:le.code)||$e==="no",getOptionLabel:t=>t!=null&&t.desc?`${t.code} - ${t.desc}`:(t==null?void 0:t.code)||"",renderOption:(t,s)=>f("li",{...t,children:[e("strong",{children:s==null?void 0:s.code}),s!=null&&s.desc?` - ${s==null?void 0:s.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),f(ts,{sx:{display:"flex",justifyContent:"end"},children:[e(nt,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>Me(!1),variant:"outlined",children:B("Cancel")}),e(nt,{className:"button_primary--normal",type:"save",disabled:!(_e!=null&&_e.length||le!=null&&le.code)&&$e==="yes",onClick:jo,variant:"contained",children:B("Proceed")})]})]}),(L==null?void 0:L.isVisible)&&f(zl,{isOpen:L==null?void 0:L.isVisible,titleIcon:e(ul,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:B("Delete Row")+"!",handleClose:()=>q({...L,isVisible:!1}),children:[e(es,{sx:{mt:2},children:B(el.DELETE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Li},onClick:()=>q({...L,isVisible:!1}),children:B(jl.CANCEL)}),e(nt,{variant:"contained",size:"small",sx:{...Yl},onClick:Ac,children:B(jl.DELETE)})]})]}),vs&&e(qo,{open:vs,onClose:()=>$(!1),title:bi.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:Rs,lengthOfOrgRow:Ke,materialID:be,orgRows:P}),dn&&e(_l,{openSnackBar:Wn,alertMsg:dn,alertType:tt,handleSnackBarClose:()=>ds(!1)}),de&&Je&&e(fd,{open:Je,onClose:()=>{Tt(!1)},articleNumber:de,articleDetails:(hr=b==null?void 0:b.filter(t=>t.id===de)[0])==null?void 0:hr.articleComponents,handleSave:Kn}),e(gl,{blurLoading:ms,loaderMessage:gs}),e(Xl,{})]})},Ld=({openSearchMat:n,setOpenSearchMat:N,AddCopiedMaterial:oe})=>{const[I,M]=i.useState(!1),x=Z(E=>E.AllDropDown.dropDown),ue={Extend:[{key:"Material Type",options:Oo},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},D=(E,Se="0",ce)=>{var j,u,ie,W,V,a,K,De,Fe,ze,ge,Te;const z={materialNo:((u=(j=E==null?void 0:E["Material Number"])==null?void 0:j.map(ee=>ee.code))==null?void 0:u.join(","))??"",division:((W=(ie=E==null?void 0:E.Division)==null?void 0:ie.map(ee=>ee.code))==null?void 0:W.join(","))??"",plant:((a=(V=E==null?void 0:E.Plant)==null?void 0:V.map(ee=>ee.code))==null?void 0:a.join(","))??"",salesOrg:((De=(K=E==null?void 0:E["Sales Org"])==null?void 0:K.map(ee=>ee.code))==null?void 0:De.join(","))??"",distrChan:((ze=(Fe=E==null?void 0:E["Distribution Channel"])==null?void 0:Fe.map(ee=>ee.code))==null?void 0:ze.join(","))??"",storageLocation:((Te=(ge=E==null?void 0:E["Storage Location"])==null?void 0:ge.map(ee=>ee.code))==null?void 0:Te.join(","))??"",top:200,skip:Se},R=ee=>{var re;if((ee==null?void 0:ee.statusCode)===Wt.STATUS_200){const H=(re=ee==null?void 0:ee.body)==null?void 0:re.map(fe=>{if(fe.Views){const Q=fe.Views.split(",").map(Re=>Re.trim()).filter(Re=>!yi.includes(Re)).join(",");return{...fe,Views:Q}}return fe});oe(H||[]),ce==null||ce(H||[]),M(!1)}},_=()=>{M(!1),ce==null||ce([])};M(!0),We(`/${m}${Ot.DATA.GET_EXTEND_SEARCH_SET}`,"post",R,_,z)};return f(kt,{children:[e(Fa,{open:n,onClose:()=>N(!1),parameters:ue.Extend,onSearch:(E,Se,ce)=>D(E,Se,ce),templateName:"Extend",name:"Extend",allDropDownData:x,buttonName:"Search"}),e(gl,{blurLoading:I})]})},yd=go(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),Ns={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},vd=n=>{var Fn,Ts,Es,Gs,Us;const N=yd(),{customError:oe}=Os(),I=as(),{fetchMaterialFieldConfig:M}=Gi(),{getNextDisplayDataForCreate:x}=vi(),{fetchValuationClassData:ue}=Mo(),D=Z(l=>l.payload.payloadData),E=D==null?void 0:D.RequestType,Se=Z(l=>l.applicationConfig),ce=Z(l=>l.paginationData),z=Z(l=>l.payload),R=Z(l=>l.request.materialRows),_=Z(l=>{var c;return((c=l.AllDropDown)==null?void 0:c.dropDown)||{}}),j=Z(l=>l.tabsData.allTabsData);let u=Z(l=>l.userManagement.taskData);const ie=Z(l=>l.tabsData.allMaterialFieldConfigDT),W=ol(),V=new URLSearchParams(W.search),a=V.get("RequestId"),K=V.get("RequestType"),[De,Fe]=i.useState(0),[ze,ge]=i.useState(null),[Te,ee]=i.useState(null),re="Basic Data",[H,fe]=i.useState([re]),[Q,Re]=i.useState([]),[he,te]=i.useState(R||[]),Mt=Z(l=>l.selectedSections.selectedSections),[T,J]=i.useState(!1),[p,Ce]=i.useState(!1),[L,q]=i.useState(""),[b,se]=i.useState([]),[O,mt]=i.useState(0),[Le,Oe]=i.useState({code:"",desc:""}),[ht,It]=i.useState(!1),{fetchDataAndDispatch:bt}=Do(),[dt,tn]=i.useState(!0),[Ct,ft]=i.useState(he.length+1),[gn,Jt]=i.useState(0),[Rt,gt]=i.useState(R.length>0),[Ie,pe]=i.useState({}),[Y,ne]=i.useState({}),[Ge,Je]=i.useState([]),[Tt,v]=i.useState({}),[ke,yt]=i.useState([]),[Ln,Zt]=i.useState(!1),[nn,Ht]=i.useState(""),[lt,$t]=i.useState("Basic Data"),[Ft,yn]=i.useState(!1),[xt,rn]=i.useState(null),vn=Z(l=>l.request.salesOrgDTData),Gn=(Fn=z==null?void 0:z[xt])==null?void 0:Fn.headerData,Xt=(D==null?void 0:D.Region)===_s.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[we,on]=i.useState([Xt]),[Qn,an]=i.useState([]),[de,vt]=i.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[Jn,Xn]=i.useState(!1),[Tn,Et]=i.useState({}),[fn,P]=i.useState("success"),A=(Ts=z==null?void 0:z[xt])==null?void 0:Ts.payloadData,[Ae,Me]=i.useState(!1),[le,Ve]=i.useState([]),[$e,Vt]=i.useState(""),[_e,Nn]=i.useState([]),{getDynamicWorkflowDT:be}=Uo(),[wt,Gt]=i.useState(!1),[tt,zn]=i.useState(!1),[Wn,ds]=i.useState(!1),[ss,_t]=i.useState(""),[dn,qt]=i.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[Hn,G]=i.useState(!1),[Mn,Ss]=i.useState(0),{fetchTabSpecificData:en}=xo(),{getContryBasedOnPlant:us}=Lo({doAjax:We,customError:oe,fetchDataAndDispatch:bt,destination_ArticleMgmt:m}),{extendFilteredButtons:B,showWfLevels:Is}=td(u,Se,Fs,Vn),qn=Di(B,[Bs.HANDLE_SUBMIT_FOR_APPROVAL,Bs.HANDLE_SAP_SYNDICATION,Bs.HANDLE_SUBMIT_FOR_REVIEW,Bs.HANDLE_SUBMIT]),Ne=l=>{!l||!Array.isArray(l)||l.forEach(c=>{var C,y,F,Ee,xe,ye,ot,Xe,Lt,ct,Ut,An,Dt,xn,Sn,As;if((y=(C=c.plant)==null?void 0:C.value)!=null&&y.code){if(en((Ee=(F=c.plant)==null?void 0:F.value)==null?void 0:Ee.code,S.PLANT),(xe=c.salesOrg)!=null&&xe.code||(ot=(ye=c.dc)==null?void 0:ye.value)!=null&&ot.code){const js=`${((Xe=c.salesOrg)==null?void 0:Xe.code)||""}-${((ct=(Lt=c.dc)==null?void 0:Lt.value)==null?void 0:ct.code)||""}`;en(js,S.SALES)}(An=(Ut=c.warehouse)==null?void 0:Ut.value)!=null&&An.code&&en((xn=(Dt=c.warehouse)==null?void 0:Dt.value)==null?void 0:xn.code,S.WAREHOUSE),us((As=(Sn=c.plant)==null?void 0:Sn.value)==null?void 0:As.code)}})},Cn=l=>{if(!l||!Array.isArray(l))return[];let c=(D==null?void 0:D.Region)===_s.EUR?l==null?void 0:l.filter(C=>C!==S.WAREHOUSE&&C!==S.WORKSCHEDULING&&C!==S.WORK_SCHEDULING):[...l];return c.sort((C,y)=>C===S.BASIC_DATA?-1:y===S.BASIC_DATA?1:0),c},ls=async()=>{var l,c;try{const C=await be(E,D==null?void 0:D.Region,"",(c=(l=z[xt])==null?void 0:l.Tochildrequestheaderdata)==null?void 0:c.MaterialGroupType,u==null?void 0:u.ATTRIBUTE_3);Nn(C)}catch(C){oe(C)}};i.useEffect(()=>{E&&(D!=null&&D.Region)&&xt&&(u!=null&&u.ATTRIBUTE_3)&&ls()},[E,D==null?void 0:D.Region,xt,u==null?void 0:u.ATTRIBUTE_3]),i.useEffect(()=>{var l,c,C,y,F,Ee,xe,ye;(y=(C=(c=(l=z[xt])==null?void 0:l.payloadData)==null?void 0:c[S.CLASSIFICATION])==null?void 0:C.basic)!=null&&y.Classtype&&xi((ye=(xe=(Ee=(F=z[xt])==null?void 0:F.payloadData)==null?void 0:Ee[S.CLASSIFICATION])==null?void 0:xe.basic)==null?void 0:ye.Classtype,I)},[xt]),i.useEffect(()=>{var l,c,C,y,F,Ee,xe,ye,ot,Xe,Lt,ct,Ut,An,Dt;if(te(R),gt((R==null?void 0:R.length)>0),(R==null?void 0:R.length)>0){rn((l=R==null?void 0:R[0])==null?void 0:l.id),Yt(((C=(c=R==null?void 0:R[0])==null?void 0:c.materialType)==null?void 0:C.code)||((y=R==null?void 0:R[0])==null?void 0:y.materialType)),Jt(0),_t((F=R==null?void 0:R[0])==null?void 0:F.materialNumber),$t(S.BASIC_DATA),fe((xe=(Ee=R==null?void 0:R[0])==null?void 0:Ee.views)!=null&&xe.length?Cn((ye=R==null?void 0:R[0])==null?void 0:ye.views):Cn([re]));const xn=To(z),Sn=Eo(xn);let As=JSON.parse(JSON.stringify(Sn));I(Ao(As)),I(fl({keyName:"selectedMaterialID",data:(ot=R==null?void 0:R[0])==null?void 0:ot.id})),(ct=(Lt=z==null?void 0:z[(Xe=R==null?void 0:R[0])==null?void 0:Xe.id])==null?void 0:Lt.Tochildrequestheaderdata)!=null&&ct.ChildRequestId&&I(fl({keyName:"childRequestId",data:(Dt=(An=z==null?void 0:z[(Ut=R==null?void 0:R[0])==null?void 0:Ut.id])==null?void 0:An.Tochildrequestheaderdata)==null?void 0:Dt.ChildRequestId}))}},[R]),i.useEffect(()=>{(he==null?void 0:he.length)===0&&J(!1)},[he]),i.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(bs)},[]),i.useEffect(()=>{if(a){const l=Gn==null?void 0:Gn.orgData;(l==null?void 0:l.length)>0&&l.some(c=>{var C,y,F,Ee,xe;return((y=(C=c.plant)==null?void 0:C.value)==null?void 0:y.code)&&(((F=c.salesOrg)==null?void 0:F.code)||((xe=(Ee=c.dc)==null?void 0:Ee.value)==null?void 0:xe.code))})&&Ne(l)}},[Gn==null?void 0:Gn.orgData]),i.useEffect(()=>{var F,Ee,xe,ye;const l=H==null?void 0:H.includes((F=S)==null?void 0:F.SALES),c=H==null?void 0:H.includes((Ee=S)==null?void 0:Ee.SALES_PLANT),C=H==null?void 0:H.includes((xe=S)==null?void 0:xe.STORAGE),y=H==null?void 0:H.includes((ye=S)==null?void 0:ye.STORAGE_PLANT);l&&!c&&fe(ot=>{var ct,Ut;const Xe=[...ot],Lt=Xe.indexOf((ct=S)==null?void 0:ct.SALES);return Xe.splice(Lt+1,0,(Ut=S)==null?void 0:Ut.SALES_PLANT),Xe}),C&&!y&&fe(ot=>{var ct,Ut;const Xe=[...ot],Lt=Xe.indexOf((ct=S)==null?void 0:ct.STORAGE);return Xe.splice(Lt+1,0,(Ut=S)==null?void 0:Ut.STORAGE_PLANT),Xe})},[H]);const Un=()=>{Yn()},is=(l="",c=!1)=>{var Ee,xe,ye,ot;const C={materialNo:l??"",top:500,skip:c?0:O,salesOrg:((xe=(Ee=vn==null?void 0:vn.uniqueSalesOrgList)==null?void 0:Ee.map(Xe=>Xe.code))==null?void 0:xe.join("$^$"))||""},y=Xe=>{(Xe==null?void 0:Xe.statusCode)===Wt.STATUS_200&&(se(c?Xe==null?void 0:Xe.body:Lt=>[...Lt,...Xe==null?void 0:Xe.body]),It(!1))},F=()=>{It(!1)};It(!0),We(`/${m}${(ot=(ye=Ot)==null?void 0:ye.DATA)==null?void 0:ot.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",y,F,C)},sn=!Ls.includes(n==null?void 0:n.requestStatus),En=l=>{const c=y=>{(y==null?void 0:y.statusCode)===Wt.STATUS_200&&Je(y==null?void 0:y.body)},C=y=>{oe(y,"while fetching the validation data of material number")};We(`/${m}/data/getNumberRangeForMaterialType?materialType=${l==null?void 0:l.code}`,"get",c,C)};function Ye(l){const c=y=>{var F;if((y==null?void 0:y.statusCode)===Wt.STATUS_200){let Ee=(F=y==null?void 0:y.body)==null?void 0:F.filter(xe=>!yi.includes(xe));Ee=Ee==null?void 0:Ee.map(xe=>xe==="Storage"?S.STORAGE:xe),(D==null?void 0:D.Region)===_s.EUR&&(Ee=Ee==null?void 0:Ee.filter(xe=>xe!==S.WAREHOUSE&&xe!==S.WORK_SCHEDULING&&xe!==S.WORKSCHEDULING)),yt(Ee)}},C=y=>{oe(y)};We(`/${m}/data/getViewForMaterialType?materialType=${l}`,"get",c,C)}i.useEffect(()=>{is()},[]);const it=((Es=Ge==null?void 0:Ge[1])==null?void 0:Es.External)==="X",pt=Ge==null?void 0:Ge.some(l=>l.ExtNAwock==="X");function Yt(l){var y;const c=(D==null?void 0:D.Region)||_s.US;if(!ie.some(F=>F[c]&&F[c][l])&&l)M(l,c);else if(!l)I(Nl({}));else{const F=ie==null?void 0:ie.find(Ee=>(Ee==null?void 0:Ee[c])&&(Ee==null?void 0:Ee[c][l]));F&&I(Nl((y=F[c][l])==null?void 0:y.allfields))}l&&ue(l)}const ln=l=>{const{id:c,field:C,value:y}=l,F=he.map(Ee=>Ee.id===c?{...Ee,[C]:y}:Ee);v({...Tt,[C]:y}),C===Ni.MATERIALTYPE&&(En(y),Ye(y),fe([re]),No([Xt]),I(xs({materialID:c,keyName:"views",data:[re]})),I(xs({materialID:c,keyName:"orgData",data:""})),Yt(y==null?void 0:y.code)),te(F),I(xs({materialID:c,keyName:C,data:y}))},jt=l=>{var c,C,y,F,Ee,xe,ye,ot,Xe;rn(l.row.id),Et(l.row),_t(l.row.materialNumber),yt((c=l==null?void 0:l.row)==null?void 0:c.views),Yt(((y=(C=l==null?void 0:l.row)==null?void 0:C.materialType)==null?void 0:y.code)||((F=l.row)==null?void 0:F.materialType)),fe((Ee=l==null?void 0:l.row)!=null&&Ee.views?(xe=l.row)==null?void 0:xe.views:[re]),on((ot=(ye=l==null?void 0:l.row)==null?void 0:ye.orgData)!=null&&ot.length?(Xe=l.row)==null?void 0:Xe.orgData:[Xt]),Jt(0),$t("Basic Data")},wn=()=>{Ce(!0)},Yn=()=>{Ce(!1)},jn=(l,c)=>{c==="backdropClick"||c==="escapeKeyDown"||yn(!1)},hs=()=>fe(Cn(ke)),ms=l=>{if(Xn(!1),l!=null&&l.length){let c=[...he];l==null||l.forEach(C=>{var xe,ye,ot;const y=C==null?void 0:C.Material;let F={...C},Ee=(xe=z==null?void 0:z[C.id])!=null&&xe.payloadData?JSON.parse(JSON.stringify((ye=z==null?void 0:z[C.id])==null?void 0:ye.payloadData)):"";F.id=y,F.globalMaterialDescription="",F.materialNumber="",F.included=!0,F.industrySector=C==null?void 0:C.IndSector,F.materialType=C==null?void 0:C.MatlType,F.materialNumber=C==null?void 0:C.Material,F.globalMaterialDescription=C==null?void 0:C.MaterialDescrption,F.views=C!=null&&C.Views?(ot=C==null?void 0:C.Views.split(","))==null?void 0:ot.map(Xe=>Xe.trim()==="Storage"?S.STORAGE:Xe.trim()):[re],F.validated=!1,c.push(F),I(Bl({materialID:y,data:F,payloadData:Ee}))}),Re(C=>[...C,...c.map(y=>({material:y==null?void 0:y.Material,views:y==null?void 0:y.views}))]),te(c),I(Pn(c)),ft(Ct+1),gt(!0),tn(!0)}},_n=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:l=>{var c;return l!=null&&l.row?e(dl,{checked:(c=l==null?void 0:l.row)==null?void 0:c.included,disabled:sn,onChange:C=>{var y;(y=l==null?void 0:l.row)!=null&&y.id&&ln({id:l.row.id,field:"included",value:C.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:E==="Create",align:"center",headerAlign:"center",renderCell:l=>{const C=((R==null?void 0:R.findIndex(y=>{var F;return(y==null?void 0:y.id)===((F=l==null?void 0:l.row)==null?void 0:F.id)}))+1)*10;return e("div",{children:C})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:l=>{var c,C,y,F,Ee;return e(Qt,{options:(_==null?void 0:_.IndSector)||[],value:(c=l==null?void 0:l.row)==null?void 0:c.industrySector,onChange:xe=>ln({id:l.row.id,field:"industrySector",value:xe}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((y=(C=l.row)==null?void 0:C.industrySector)==null?void 0:y.code)||""} - ${((Ee=(F=l.row)==null?void 0:F.industrySector)==null?void 0:Ee.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:l=>{var c,C,y,F,Ee;return e(Qt,{options:Oo||[],value:(c=l==null?void 0:l.row)==null?void 0:c.materialType,onChange:xe=>ln({id:l.row.id,field:"materialType",value:xe}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((y=(C=l.row)==null?void 0:C.materialType)==null?void 0:y.code)||""} - ${((Ee=(F=l.row)==null?void 0:F.materialType)==null?void 0:Ee.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:E==="Extend"?.8:1,editable:!(!it&&!pt),align:"center",headerAlign:"center",renderHeader:()=>f("span",{children:["Material Number",e("span",{style:{color:"red"},children:"*"})]}),renderCell:l=>{var c,C;return e(kt,{children:(D==null?void 0:D.RequestType)===o.EXTEND?e(mn,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(c=l==null?void 0:l.row)==null?void 0:c.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:He.black.dark,color:He.black.dark}}},onChange:(y,F)=>ln({id:l.row.id,field:"materialNumber",value:F}),disabled:!it&&!pt}):(C=l==null?void 0:l.row)==null?void 0:C.materialNumber})}},{field:"globalMaterialDescription",flex:E==="Extend"?.8:1,headerName:"Material Description",renderHeader:()=>f("span",{children:["Material Description",e("span",{style:{color:"red"},children:"*"})]}),renderCell:l=>{var c,C;return e(kt,{children:(D==null?void 0:D.RequestType)===o.EXTEND?e(mn,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:He.black.dark,color:He.black.dark}}},onChange:(y,F)=>ln({id:l.row.id,field:"globalMaterialDescription",value:F}),value:(c=l==null?void 0:l.row)==null?void 0:c.globalMaterialDescription}):(C=l==null?void 0:l.row)==null?void 0:C.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:E==="Extend"?1.5:1,align:"center",headerAlign:"center",renderCell:l=>f(Vs,{direction:"row",spacing:0,alignItems:"center",children:[e(nt,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var c,C;Ye(l.row.materialType),Zt(!0),Ht(l.row.id),Et(l.row),fe((c=l==null?void 0:l.row)!=null&&c.Views?(C=l==null?void 0:l.row)==null?void 0:C.Views:[re])},children:"Views"}),e(zr,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(nt,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var c,C,y,F;yn(!0),Ht(l.row.id),on((C=(c=l==null?void 0:l.row)==null?void 0:c.orgData)!=null&&C.length?(y=l.row)==null?void 0:y.orgData:[Xt]),Et(l.row),gs((F=l==null?void 0:l.row)==null?void 0:F.materialNumber,Ns.NOT_EXTENDED),zn(!1)},children:"ORG Data"}),e(zr,{color:"disabled",fontSize:"small",sx:{mx:.5}}),e(Rn,{title:"Click after changing Views or ORG Data",children:e(cn,{onClick:()=>{var c,C;yn(!0),Ht(l.row.id),zn(!0),Et(l.row),gs((c=l==null?void 0:l.row)==null?void 0:c.materialNumber,Ns.EXTENDED),vt(Qn.find(y=>{var F;return y.id===((F=l.row)==null?void 0:F.id)})||{id:(C=l.row)==null?void 0:C.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:sn,color:"primary",size:"small",children:e(Oi,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:l=>e(Vs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:!a&&e(Rn,{title:"Delete Row",children:e(cn,{onClick:()=>{te(he.filter(c=>c.id!==l.row.id)),I(_o(l.row.id)),I(Pn(he.filter(c=>c.id!==l.row.id))),he!=null&&he.length||J(!1)},color:"error",children:e(ul,{})})})})}],gs=(l,c)=>{qt(F=>({...F,"Sales Organization":!0}));const C=F=>{if((F==null?void 0:F.statusCode)===Wt.STATUS_200){let Ee;c===Ns.NOT_EXTENDED?Ee=Cs(F.body):Ee=F.body.length>0?Cs(F.body):[],ne(xe=>({...xe,"Sales Organization":Ee}))}qt(Ee=>({...Ee,"Sales Organization":!1}))},y=()=>{qt(F=>({...F,"Sales Organization":!1}))};We(`/${m}/data/${c===Ns.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}`,"get",C,y)},Zn=(l,c,C,y)=>{qt(ye=>({...ye,Plant:{...ye.Plant,[y]:!0}}));const F=ye=>{if((ye==null?void 0:ye.statusCode)===Wt.STATUS_200){let ot;c===Ns.NOT_EXTENDED?ot=Cs(ye.body):ot=ye.body.length>0?Cs(ye.body||[]):[],ne(Xe=>({...Xe,Plant:ot}))}qt(ot=>({...ot,Plant:{...ot.Plant,[y]:!1}}))},Ee=()=>{qt(ye=>({...ye,Plant:{...ye.Plant,[y]:!1}}))},xe=C?`&salesOrg=${C.code}`:"";We(`/${m}/data/${c===Ns.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}${xe}`,"get",F,Ee)},kn=(l,c,C,y)=>{qt(ye=>({...ye,warehouse:{...ye.warehouse,[y]:!0}}));const F=ye=>{if((ye==null?void 0:ye.statusCode)===Wt.STATUS_200){let ot;c===Ns.NOT_EXTENDED?ot=Cs(ye.body):ot=ye.body.length>0?Cs(ye.body||[]):[],ne(Xe=>({...Xe,warehouse:ot}))}qt(ot=>({...ot,warehouse:{...ot.warehouse,[y]:!1}}))},Ee=()=>{qt(ye=>({...ye,warehouse:{...ye.warehouse,[y]:!1}}))},xe=C?`&plant=${C.code}`:"";We(`/${m}/data/${c===Ns.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${l}&region=${D==null?void 0:D.Region}${xe}`,"get",F,Ee)},fs=(l,c)=>{var C;Jt(c),$t(((C=l==null?void 0:l.target)==null?void 0:C.id)==="AdditionalKey"?"Additional Data":H==null?void 0:H[c])},bs=l=>{qt(F=>({...F,[l]:!0}));const c={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},C=F=>{if((F==null?void 0:F.statusCode)===Wt.STATUS_200){const Ee=Cs(F.body);ne(xe=>({...xe,[l]:Ee}))}qt(Ee=>({...Ee,[l]:!1}))},y=F=>{oe(F),qt(Ee=>({...Ee,[l]:!1}))};We(`/${m}/data${c[l]}`,"get",C,y)},Rs=l=>{Co(l,H,A,xt,we,I,Qs)},ws=(l,c,C)=>(y,F)=>{var ct,Ut,An;let Ee={},xe="",ye="";C==="Purchasing"||C==="Costing"?(Ee={materialNo:c==null?void 0:c.Material,plant:c==null?void 0:c.Plant},ye=c==null?void 0:c.Plant,xe=`/${m}/data/displayLimitedPlantData`):C==="Accounting"?(Ee={materialNo:c==null?void 0:c.Material,valArea:c==null?void 0:c.ValArea},ye=c==null?void 0:c.ValArea,xe=`/${m}/data/displayLimitedAccountingData`):C==="Sales"&&(Ee={materialNo:c==null?void 0:c.Material,salesOrg:c==null?void 0:c.SalesOrg,distChnl:c==null?void 0:c.DistrChan},ye=`${c==null?void 0:c.SalesOrg}-${c==null?void 0:c.DistrChan}`,xe=`/${m}/data/displayLimitedSalesData`);const ot=Dt=>{var xn,Sn,As;(Dt==null?void 0:Dt.statusCode)===Wt.STATUS_200&&(C==="Purchasing"||C==="Costing"?I(Qs({materialID:xt,viewID:C,itemID:c==null?void 0:c.Plant,data:(xn=Dt==null?void 0:Dt.body)==null?void 0:xn.SpecificPlantDataViewDto[0]})):C==="Accounting"?I(Qs({materialID:xt,viewID:C,itemID:c==null?void 0:c.ValArea,data:(Sn=Dt==null?void 0:Dt.body)==null?void 0:Sn.SpecificAccountingDataViewDto[0]})):C==="Sales"&&I(Qs({materialID:xt,viewID:C,itemID:`${c==null?void 0:c.SalesOrg}-${c==null?void 0:c.DistrChan}`,data:(As=Dt==null?void 0:Dt.body)==null?void 0:As.SpecificSalesDataViewDto[0]})))},Xe=()=>{};!((An=(Ut=(ct=z==null?void 0:z[xt])==null?void 0:ct.payloadData)==null?void 0:Ut[C])!=null&&An[ye])&&We(xe,"post",ot,Xe,Ee),ee(F?l:null)},Bn=()=>j&&lt&&(j[lt]||lt==="Additional Data")?lt==="Additional Data"?[e(Go,{disabled:sn,materialID:xt,selectedMaterialNumber:ss})]:[e(Va,{disabled:sn,materialID:xt,basicData:Ie,setBasicData:pe,dropDownData:Y,allTabsData:j,basicDataTabDetails:j[lt],activeViewTab:lt,selectedViews:H,handleAccordionClick:ws,missingValidationPlant:le,selectedMaterialNumber:ss,callGetCountryBasedonSalesOrg:Wn})]:e(kt,{}),ut=l=>{const c=l.target.value;Oe({code:c,desc:""}),mt(0),ze&&clearTimeout(ze);const C=setTimeout(()=>{is(c,!0)},500);ge(C)};i.useEffect(()=>{O>0&&is(Le==null?void 0:Le.code)},[O]);const On=(l,c,C)=>{var y;if(l==="Sales Organization"){vs(c,C);const F=(y=he==null?void 0:he.find(Ee=>Ee.id===nn))==null?void 0:y.materialNumber;Zn(F,tt?Ns.EXTENDED:Ns.NOT_EXTENDED,c,C)}},vs=(l,c,C="",y="")=>(qt(F=>({...F,"Distribution Channel":{...F["Distribution Channel"],[c]:!0}})),new Promise((F,Ee)=>{var Xe;const xe=Lt=>{if(Lt.statusCode===Wt.STATUS_200){const ct=Cs(Lt.body);let Ut=JSON.parse(JSON.stringify(C||we));tt?vt(An=>({...An,salesOrg:l,dc:{value:null,options:(ct==null?void 0:ct.length)>0?ct:[]}})):(Ut[c].salesOrg=l,Ut[c].dc.options=ct,on(Ut))}qt(ct=>({...ct,"Distribution Channel":{...ct["Distribution Channel"],[c]:!1}})),F(Lt)},ye=Lt=>{qt(ct=>({...ct,"Distribution Channel":{...ct["Distribution Channel"],[c]:!1}})),Ee(Lt)};let ot=(Xe=he==null?void 0:he.find(Lt=>Lt.id===nn))==null?void 0:Xe.materialNumber;ot&&We(`/${m}/data/${tt?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${ot}&salesOrg=${l==null?void 0:l.code}`,"get",xe,ye)})),$=(l,c)=>{var y;Ke(l,c);const C=(y=he==null?void 0:he.find(F=>F.id===nn))==null?void 0:y.materialNumber;kn(C,tt?Ns.EXTENDED:Ns.NOT_EXTENDED,l,c)},Ke=(l,c,C="",y)=>{var Xe;qt(Lt=>({...Lt,"Storage Location":{...Lt["Storage Location"],[c]:!0}}));const F=Lt=>{if(qt(ct=>({...ct,"Storage Location":{...ct["Storage Location"],[c]:!1}})),Lt.statusCode===Wt.STATUS_200){const ct=Cs(Lt.body);let Ut=JSON.parse(JSON.stringify(C||we));tt?vt(An=>({...An,plant:{value:l,options:[]},sloc:{value:null,options:(ct==null?void 0:ct.length)>0?ct:[]}})):(Ut[c].plant.value=l,Ut[c].sloc.options=ct,on(Ut))}if(y){I(xs({materialID:y==null?void 0:y.id,keyName:"orgData",data:rowOption}));let ct=(he==null?void 0:he.length)||[JSON.parse(JSON.stringify(y))],Ut=ct.findIndex(An=>An.id===(y==null?void 0:y.id));ct[Ut].orgData=rowOption,I(Pn(ct))}},Ee=Lt=>{oe(Lt),qt(ct=>({...ct,"Storage Location":{...ct["Storage Location"],[c]:!1}}))};let xe=(Xe=he.find(Lt=>Lt.id===nn))==null?void 0:Xe.materialNumber;const ye=we[c],ot=ye!=null&&ye.salesOrg?`&salesOrg=${ye.salesOrg.code}`:"";xe&&We(`/${m}/data/${tt?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${xe}&region=${D==null?void 0:D.Region}&plant=${l==null?void 0:l.code}${ot}`,"get",F,Ee)},Qe=(l,c)=>{let C=JSON.parse(JSON.stringify(we));C[c].dc.value=l,on(C)},Ze=l=>{let c=JSON.parse(JSON.stringify(we));c.splice(l,1),on(c)},U=(l,c)=>{let C=JSON.parse(JSON.stringify(we));C[c].sloc.value=l,on(C)},et=(l,c)=>{let C=JSON.parse(JSON.stringify(we));C[c].warehouse.value=l,on(C)},qe=(l,c)=>{let C=JSON.parse(JSON.stringify(we));C[c].mrpProfile=l,on(C)},rt=()=>{let l=JSON.parse(JSON.stringify(we));l.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),on(l)},Kt=(l,c,C,y)=>{var ye,ot,Xe,Lt,ct,Ut,An,Dt,xn,Sn,As;const F={material:Tn==null?void 0:Tn.materialNumber,wareHouseNumber:((ot=(ye=l==null?void 0:l.warehouse)==null?void 0:ye.value)==null?void 0:ot.code)??"",plant:((Lt=(Xe=l==null?void 0:l.plant)==null?void 0:Xe.value)==null?void 0:Lt.code)??"",salesOrg:((ct=l==null?void 0:l.salesOrg)==null?void 0:ct.code)??"",storageLocation:((An=(Ut=l==null?void 0:l.sloc)==null?void 0:Ut.value)==null?void 0:An.code)??"",distributionChannel:((xn=(Dt=l==null?void 0:l.dc)==null?void 0:Dt.value)==null?void 0:xn.code)??"",valArea:((As=(Sn=l==null?void 0:l.plant)==null?void 0:Sn.value)==null?void 0:As.code)??""},Ee=js=>{const Ul=Qc(js==null?void 0:js.body,c,C,y,Tn),Zl=E===o.EXTEND_WITH_UPLOAD||K===o.EXTEND_WITH_UPLOAD?Zc(z,Ul):ea(z,Ul);I(Io({data:Zl})),ds(!Wn)},xe=js=>{oe(js)};We(`/${m}${Ot.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",Ee,xe,F)},In=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],At=l=>{I(Sl(l)),Fe(l)};i.useEffect(()=>{var l,c;(ce==null?void 0:ce.page)!==0&&(E===((l=o)==null?void 0:l.EXTEND_WITH_UPLOAD)||E===((c=o)==null?void 0:c.EXTEND))&&x(),Fe((ce==null?void 0:ce.page)||0)},[ce==null?void 0:ce.page]);const St=tt?In:we;return f("div",{children:[e("div",{style:{padding:"0",width:"100%",margin:"0"},children:f("div",{style:{height:300,width:"100%"},children:[e(Be,{sx:{display:"flex",justifyContent:"flex-end",marginBottom:"10px"},children:e(nt,{variant:"contained",color:"primary",onClick:()=>{Gt(!0)},disabled:T||sn||a&&(n==null?void 0:n.requestStatus)!==cs.DRAFT,children:"+ Add"})}),he&&(he==null?void 0:he.length)>0?e(kt,{children:e(yl,{rows:he,columns:_n,pageSize:50,page:De,rowsPerPageOptions:[50],rowCount:(ce==null?void 0:ce.totalElements)||0,onRowClick:jt,onCellEditCommit:ln,onPageChange:l=>At(l),disableSelectionOnClick:!0,getRowClassName:l=>l.id===xt?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})}):e(kt,{children:e(yl,{rows:he,columns:_n,pageSize:5,rowsPerPageOptions:[5],page:De,onRowClick:jt,onCellEditCommit:ln,onPageChange:l=>At(l),disableSelectionOnClick:!0,getRowClassName:l=>l.id===xt?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})]})}),xt&&Rt&&(he==null?void 0:he.length)>0&&Mt.length>0&&j&&((Gs=Object.getOwnPropertyNames(j))==null?void 0:Gs.length)>0&&f(Be,{sx:{marginTop:"45px"},children:[f(So,{sx:{top:0,position:"sticky",zIndex:1e3,backgroundColor:He.background.container,borderBottom:`1px solid ${He.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:He.black.graphite,"&.Mui-selected":{color:He.primary.main,fontWeight:700},"&:hover":{color:He.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:He.primary.main,height:"3px"}},value:gn,onChange:fs,className:N.customTabs,"aria-label":"material tabs",children:[H&&we.length>0&&(H==null?void 0:H.length)>0?H==null?void 0:H.map((l,c)=>e(wl,{label:l},c)):e(kt,{}),e(wl,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(he==null?void 0:he.length)>0&&e(Be,{sx:{padding:2,marginTop:2},children:Bn()}),e(Bo,{activeTab:gn,submitForApprovalDisabled:dt,filteredButtons:qn,workFlowLevels:_e,showWfLevels:Is,childRequestHeaderData:(Us=z==null?void 0:z[xt])==null?void 0:Us.Tochildrequestheaderdata})]}),e("div",{}),e(Ql,{dialogState:p,openReusableDialog:wn,closeReusableDialog:Yn,dialogTitle:"Warning",dialogMessage:L,showCancelButton:!1,handleOk:Un,handleDialogConfirm:Yn,dialogOkText:"OK",dialogSeverity:"danger"}),Ln&&e(Hs,{fullWidth:!0,maxWidth:!1,open:!0,onClose:jn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:f(Be,{sx:{width:"600px !important"},children:[f(qs,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[e(vl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),e("span",{children:"Select Views"})]}),e(es,{sx:{paddingBottom:".5rem"},children:f(Be,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[e(hl,{size:"small",multiple:!0,fullWidth:!0,options:ke,disabled:sn,disableCloseOnSelect:!0,value:H==null?void 0:H.filter(l=>!Kc.includes(l)),onChange:(l,c)=>{fe([re,...c.filter(C=>C!==re)]),ln({id:nn,field:"views",value:c})},getOptionDisabled:l=>l===re,renderOption:(l,c,{selected:C})=>{var Ee;const y=Q.find(xe=>(xe==null?void 0:xe.material)===(Tn==null?void 0:Tn.materialNumber)),F=((Ee=y==null?void 0:y.views)==null?void 0:Ee.includes(c))||!1;return f("li",{...l,children:[e(dl,{checked:C||c=="Basic Data",sx:{marginRight:1}}),c," ",F?"(extended)":""]})},renderTags:(l,c)=>l.map((C,y)=>{var ot;const{key:F,...Ee}=c({index:y}),xe=Q.find(Xe=>(Xe==null?void 0:Xe.material)===(Tn==null?void 0:Tn.materialNumber)),ye=((ot=xe==null?void 0:xe.views)==null?void 0:ot.includes(C))||!1;return e(Mi,{label:`${C} ${ye?"(extended)":""}`,...Ee,disabled:C===re},F)}),renderInput:l=>e(mn,{...l,label:"Select Views"})}),e(nt,{variant:"contained",disabled:sn,size:"small",onClick:()=>hs(),children:"Select all"})]})}),f(ts,{children:[e(nt,{onClick:()=>{Zt(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),e(nt,{onClick:()=>{Zt(!1),ln({id:nn,field:"views",value:H})},variant:"contained",children:"OK"})]})]})}),Ft&&f(Hs,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:jn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[f(qs,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[e(vl,{fontSize:"small"}),tt?e("span",{children:"Select org data for copy"}):e("span",{children:"Select org data to be extended"}),e(cn,{onClick:jn,sx:{position:"absolute",right:15},children:e(Ll,{})})]}),e(es,{sx:{padding:0},children:e(fo,{component:Kl,children:f(Wl,{children:[e(Hl,{children:f(il,{children:[!tt&&e(je,{align:"center",children:"S NO."}),e(je,{align:"center",children:"Sales Org"}),e(je,{align:"center",children:"Distribution Channel"}),e(je,{align:"center",children:"Plant"}),e(je,{align:"center",children:"Storage Location"}),(D==null?void 0:D.Region)!==_s.EUR&&e(je,{align:"center",children:"Warehouse"}),e(je,{align:"center",children:"MRP Profile"}),(we==null?void 0:we.length)>1&&!tt&&e(je,{align:"center",children:"Action"})]})}),e(ql,{children:St==null?void 0:St.map((l,c)=>{var C,y,F,Ee,xe,ye,ot,Xe,Lt,ct,Ut,An;return f(il,{sx:{padding:"12px"},children:[!tt&&e(je,{children:e(st,{variant:"body2",children:c+1})}),e(je,{children:e(Qt,{options:Y["Sales Organization"],value:tt?de==null?void 0:de.salesOrg:l==null?void 0:l.salesOrg,onChange:Dt=>On("Sales Organization",Dt,c),placeholder:"Select Sales Org",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Sales Organization"]})}),e(je,{children:e(Qt,{options:tt?(y=de==null?void 0:de.dc)==null?void 0:y.options:(C=l.dc)==null?void 0:C.options,value:tt?(Ee=de==null?void 0:de.dc)==null?void 0:Ee.value:(F=l.dc)==null?void 0:F.value,onChange:Dt=>tt?vt(xn=>{var Sn;return{...xn,dc:{value:Dt,options:(Sn=de==null?void 0:de.dc)==null?void 0:Sn.options}}}):Qe(Dt,c),placeholder:"Select DC",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Distribution Channel"][c]})}),e(je,{children:e(Qt,{options:Y.Plant||[],value:tt?(ye=de==null?void 0:de.plant)==null?void 0:ye.value:(xe=l.plant)==null?void 0:xe.value,onChange:Dt=>$(Dt,c),placeholder:"Select Plant",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn.Plant[c]})}),e(je,{children:e(Qt,{options:tt?(Xe=de==null?void 0:de.sloc)==null?void 0:Xe.options:(ot=l==null?void 0:l.sloc)==null?void 0:ot.options,value:tt?(ct=de==null?void 0:de.sloc)==null?void 0:ct.value:(Lt=l==null?void 0:l.sloc)==null?void 0:Lt.value,onChange:Dt=>tt?vt(xn=>{var Sn;return{...xn,sloc:{value:Dt,options:(Sn=de==null?void 0:de.sloc)==null?void 0:Sn.options}}}):U(Dt,c),placeholder:"Select Sloc",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Storage Location"][c]})}),(D==null?void 0:D.Region)!==_s.EUR&&e(je,{children:e(Qt,{options:Y.warehouse||[],value:tt?(An=de==null?void 0:de.warehouse)==null?void 0:An.value:(Ut=l==null?void 0:l.warehouse)==null?void 0:Ut.value,onChange:Dt=>tt?vt(xn=>{var Sn;return{...xn,warehouse:{value:Dt,options:(Sn=de==null?void 0:de.warehouse)==null?void 0:Sn.options}}}):et(Dt,c),placeholder:"Select Warehouse",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn.warehouse[c]})}),e(je,{children:e(Qt,{options:Y["Mrp Profile"]||[],value:tt?de==null?void 0:de.mrpProfile:l.mrpProfile,onChange:Dt=>tt?vt(xn=>({...xn,mrpProfile:Dt})):qe(Dt,c),placeholder:"Select MRP Profile",disabled:sn,isFieldError:!1,minWidth:165,isLoading:dn["Mrp Profile"]})}),we.length>1&&f(je,{align:"right",children:[e(cn,{size:"small",color:"primary",disabled:sn,onClick:()=>{G(!0),Ss({orgRowLength:we.length,copyFor:c})},style:{display:c===0?"none":"inline-flex"},children:e(Oi,{})}),e(cn,{style:{display:c===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>Ze(c),children:e(ul,{})})]})]},c)})})]})})}),f(ts,{sx:{justifyContent:"flex-end",gap:.5},children:[!tt&&e(nt,{onClick:rt,disabled:sn,variant:"contained",children:"+ Add"}),e(nt,{onClick:()=>{if(yn(!1),we[0].plant&&(ln({id:nn,field:"orgData",value:we}),!tt)){Ne(we);const c=he==null?void 0:he.map(C=>(C==null?void 0:C.id)===nn?{...C,orgData:we}:C);I(Pn(c))}const l=we.filter(c=>{var C,y;return(y=(C=c.plant)==null?void 0:C.value)==null?void 0:y.code}).map(c=>{var C,y;return(y=(C=c.plant)==null?void 0:C.value)==null?void 0:y.code});l.length>0&&Rs(l),tt&&(an(c=>{const C=c.findIndex(y=>y.id===de.id);return C!==-1?c.map((y,F)=>F===C?{...y,...de}:y):[...c,de]}),Kt(de,we,D,H))},variant:"contained",children:"Ok"})]})]}),Hn&&e(qo,{open:Hn,onClose:()=>G(!1),title:bi.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:A,lengthOfOrgRow:Mn,materialID:xt,orgRows:we}),$e&&e(_l,{openSnackBar:Ae,alertMsg:$e,alertType:fn,handleSnackBarClose:()=>Me(!1)}),e(Ld,{openSearchMat:wt,materialOptions:b,handleMatInputChange:ut,inputState:Le,setOpenSearchMat:Gt,dropDownData:Y,AddCopiedMaterial:ms}),e(Xl,{})]})},Gd=({params:n,field:N,isFieldError:oe,isFieldDisable:I,isNewRow:M,keyName:x,handleChangeValue:ue,handleRemoveError:D,charCount:E,setCharCount:Se,isFocused:ce,setIsFocused:z})=>{var W;const[R,_]=i.useState(n.row[N.jsonName]||""),j=n.row.id,u=E[x]===(N==null?void 0:N.maxLength),ie=V=>{const a=V.target.value;_(a),ue(n.row,j,N.jsonName,(a==null?void 0:a.toUpperCase())||"",N.viewName,N.fieldName,x),Se(K=>({...K,[x]:a.length}))};return e(Rn,{title:(W=n.row[N.jsonName])==null?void 0:W.toUpperCase(),arrow:!0,placement:"top",children:e(mn,{fullWidth:!0,placeholder:`ENTER ${N.fieldName.toUpperCase()}`,variant:"outlined",size:"small",value:R,disabled:I||!M&&N.visibility===ys.DISPLAY,inputProps:{maxLength:N.maxLength,style:{textTransform:"uppercase"}},InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:oe?He.error.dark:void 0},"&.Mui-disabled":{"& input":{WebkitTextFillColor:He.text.primary,color:He.text.primary}}}},onFocus:V=>{V.stopPropagation(),z({...ce,[x]:!0}),Se(a=>({...a,[x]:V.target.value.length})),oe&&D(j,N.fieldName)},onKeyDown:V=>V.key===" "&&V.stopPropagation(),onClick:V=>V.stopPropagation(),onChange:ie,onBlur:()=>z({...ce,[x]:!1}),helperText:ce[x]&&(u?"Max Length Reached":`${E[x]||0}/${N.maxLength}`),FormHelperTextProps:{sx:{color:u?He.error.dark:He.primary.darkPlus,position:"absolute",bottom:"-20px"}},sx:{"& .MuiInputBase-root":{height:"34px"},"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:u?He.error.dark:""}}}})})},Ud=n=>{var yt,Ln,Zt,nn,Ht,lt,$t,Ft,yn,xt,rn,vn,Gn,Xt,we,on,Qn,an,de,vt,Jn,Xn,Tn,Et,fn,P;const{customError:N}=Os(),{getNextDisplayDataForChange:oe}=vi(),I=Z(A=>A.tabsData.changeFieldsDT),M=Z(A=>A.payload.payloadData),x=I==null?void 0:I["Config Data"],ue=Z(A=>A.payload.tablesList),D=Z(A=>A.payload.changeFieldRows),E=Z(A=>A.payload.changeFieldRowsDisplay),Se=Z(A=>A.payload.changeLogData),ce=Z(A=>A.payload.matNoList),z=Z(A=>A.payload.newRowIds),R=Z(A=>A.materialDropDownData.dropDown||{}),_=Z(A=>A.payload.dataLoading),j=Z(A=>A.payload.errorData),u=Z(A=>A.payload.selectedRows),ie=Z(A=>{var Ae;return(Ae=A.request.requestHeader)==null?void 0:Ae.requestId}),W=Z(A=>A.userManagement.userData),V=Z(A=>A.userManagement.taskData),a=Z(A=>A.paginationData),K=Z(A=>A.payload.templateArray),De=Z(A=>A.payload.requestorPayload),[Fe,ze]=i.useState([]),[ge,Te]=i.useState({}),[ee,re]=i.useState({}),[H,fe]=i.useState(""),[Q,Re]=i.useState("success"),[he,te]=i.useState(!1),[Mt,T]=i.useState(""),J=Z(A=>A.tabsData.dataLoading),[p,Ce]=i.useState({data:{},isVisible:!1}),L=as(),q=ol(),b=new URLSearchParams(q.search),se=b.get("reqBench"),O=b.get("RequestId"),{t:mt}=Cl();let Le=q.state;i.useEffect(()=>{D&&(Fe==null?void 0:Fe.length)===0&&ze(JSON.parse(JSON.stringify(D)))},[D]);const[Oe,ht]=i.useState(0),[It,bt]=i.useState(10),dt=(A,Ae)=>{ht(isNaN(Ae)?0:Ae)},tn=A=>{const Ae=A.target.value;bt(Ae),ht(0)},Ct=()=>{te(!0)},ft=()=>{te(!1)},gn=()=>{const A=(D==null?void 0:D.length)>0?Object.keys(D[0]):[],Ae=A==null?void 0:A.reduce((Ve,$e)=>(Ve[$e]=$e==="id"?ml():$e==="slNo"?1:"",Ve),{}),Me=[Ae,...D].map((Ve,$e)=>({...Ve,slNo:$e+1})),le=[Ae,...E[a==null?void 0:a.page]||[]].map((Ve,$e)=>({...Ve,slNo:$e+1}));L(jr([Ae==null?void 0:Ae.id,...z])),L(Fl(Me)),L(Vl({...E,[a==null?void 0:a.page]:le})),L(bo([Ae==null?void 0:Ae.id,...u])),L(Ml(!0)),n==null||n.setCompleted([!0,!1])},Jt=Vr(ta,M==null?void 0:M.TemplateName),Rt=Vr(na,Jt),gt=(Rt==null?void 0:Rt.length)>1,Ie=(A,Ae)=>{const Me=Fe==null?void 0:Fe.find(le=>{const Ve=le.Material===(A==null?void 0:A.Material)&&(le==null?void 0:le[Rt[0]])===(A==null?void 0:A[Rt[0]]);return gt?Ve&&(le==null?void 0:le[Rt[1]])===(A==null?void 0:A[Rt[1]]):Ve});if(Me)return Me[Ae]},pe=(A,Ae,Me,le)=>{var $e;const Ve=($e=Fe==null?void 0:Fe[Me])==null?void 0:$e.find(Vt=>{let _e=Vt.Material===(A==null?void 0:A.Material);return(le==null?void 0:le.length)>0&&(_e=_e&&(Vt==null?void 0:Vt[le[0]])===(A==null?void 0:A[le[0]]),(le==null?void 0:le.length)>1&&(_e=_e&&(Vt==null?void 0:Vt[le[1]])===(A==null?void 0:A[le[1]]))),_e});return Ve?Ve[Ae]:"-"},Y=A=>{L(sa(A))},{handleObjectChangeFieldRows:ne}=wa(D,E,a,Jt,W,ie,K,Y,pe,n==null?void 0:n.RequestId),Ge=(A,Ae,Me,le,Ve,$e)=>{var Vt,_e,Nn,be;if(Array.isArray(D)){if(Me==="AltUnit"||Me==="Langu"){const _t=ca(A,E==null?void 0:E[a==null?void 0:a.page],ce,le,M==null?void 0:M.TemplateName);if(_t==="matError"){Re("error"),T(mt((Vt=el)==null?void 0:Vt.MATL_ERROR_MSG)),Ct();return}else if(_t==="altUnitError"){Re("error"),T(mt((_e=el)==null?void 0:_e.ALTUNIT_ERROR_MSG)),Ct();return}else if(_t==="languError"){Re("error"),T(mt((Nn=el)==null?void 0:Nn.LANG_ERROR_MSG)),Ct();return}}const wt=D==null?void 0:D.map(_t=>{var dn,qt;return(_t==null?void 0:_t.id)===Ae?{..._t,[Me]:le,...Me==="Material"?{...(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.UPD_DESC)?{Langu:""}:{},...(M==null?void 0:M.TemplateName)===((qt=Pt)==null?void 0:qt.LOGISTIC)?{AltUnit:""}:{}}:{}}:_t});L(Fl(wt));const Gt=(be=E==null?void 0:E[a==null?void 0:a.page])==null?void 0:be.map(_t=>{var dn,qt;return(_t==null?void 0:_t.id)===Ae?{..._t,[Me]:le,...Me==="Material"?{...(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.UPD_DESC)?{Langu:""}:{},...(M==null?void 0:M.TemplateName)===((qt=Pt)==null?void 0:qt.LOGISTIC)?{AltUnit:""}:{}}:{}}:_t});L(Vl({...E,[a==null?void 0:a.page]:Gt}));const tt=aa(),zn=_t=>_t!=null&&_t.toString().startsWith("/Date(")&&(_t!=null&&_t.toString().endsWith(")/"))?ga(_t):_t;let Wn={ObjectNo:`${A==null?void 0:A.Material}$$${A==null?void 0:A[Rt[0]]}${gt?`$$${A==null?void 0:A[Rt[1]]}`:""}`,ChangedBy:W.emailId,ChangedOn:tt.sapFormat,FieldName:$e??Me,PreviousValue:Ie(A,Me)??"-",SAPValue:Ie(A,Me)??"-",CurrentValue:zn(le)??""};Y(Wn);let ds={RequestId:ie||(n==null?void 0:n.RequestId),changeLogId:(A==null?void 0:A.ChangeLogId)??null,[Jt]:[...K,Wn]};const ss=da(ds,Jt);L(ua(ss))}else typeof D=="object"&&D[Ve]&&ne(Ve,Ae,Me,le,$e)},Je=(A,Ae)=>{const Me={};Object.keys(j).forEach(le=>{var $e;const Ve=j[le];if(Ve.id===A){const Vt=($e=Ve==null?void 0:Ve.missingFields)==null?void 0:$e.filter(_e=>_e!==Ae);Vt.length>0&&(Me[le]={...Ve,missingFields:Vt})}else Me[le]={...Ve}}),L(Ai(Me))},Tt=()=>{var Me,le,Ve,$e;const A=p==null?void 0:p.data,Ae=(Me=A==null?void 0:A.row)==null?void 0:Me.id;if(Array.isArray(D)){const _e=D.filter(Gt=>(Gt==null?void 0:Gt.id)!==Ae).map((Gt,tt)=>({...Gt,slNo:tt+1}));L(Fl(_e));const Nn={...E,[a==null?void 0:a.page]:(Ve=(le=E[a==null?void 0:a.page])==null?void 0:le.filter(Gt=>(Gt==null?void 0:Gt.id)!==Ae))==null?void 0:Ve.map((Gt,tt)=>({...Gt,slNo:tt+1}))};L(Vl(Nn));const be=z==null?void 0:z.filter(Gt=>Gt!==Ae);L(jr(be));const wt=D.find(Gt=>Gt.id===Ae);if(wt){const Gt=`${wt.Material}$$${wt[Rt[0]]}${gt?`$$${wt[Rt[1]]}`:""}`,tt=JSON.parse(JSON.stringify(Se));if(($e=tt[wt.Material])!=null&&$e[Jt]){const zn=tt[wt.Material][Jt].filter(Wn=>Wn.ObjectNo!==Gt&&Wn.ObjectNo!==`${wt.Material}$$`);zn.length===0?(delete tt[wt.Material][Jt],Object.keys(tt[wt.Material]).length===0&&(delete tt[wt.Material],delete tt[""])):tt[wt.Material][Jt]=zn}L(ha(tt))}}Ce({...p,isVisible:!1})},v=(A,Ae)=>{var $e,Vt,_e,Nn,be,wt,Gt,tt,zn,Wn,ds,ss,_t,dn,qt,Hn;const Me=[{headerName:"Sl. No.",field:"slNo",align:"center",flex:(M==null?void 0:M.TemplateName)===(($e=Pt)==null?void 0:$e.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Vt=Pt)==null?void 0:Vt.MRP)&&A==="Plant Data"?void 0:.1,width:(M==null?void 0:M.TemplateName)===((_e=Pt)==null?void 0:_e.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Nn=Pt)==null?void 0:Nn.MRP)&&A==="Plant Data"?1:void 0},...Ae.map(G=>{var Mn,Ss,en,us,B,Is,qn;return{headerName:f("span",{children:[G.fieldName,G.visibility===((Mn=ys)==null?void 0:Mn.MANDATORY)&&e("span",{style:{color:(en=(Ss=He)==null?void 0:Ss.error)==null?void 0:en.dark,marginLeft:4},children:"*"})]}),field:G.jsonName,flex:(M==null?void 0:M.TemplateName)===((us=Pt)==null?void 0:us.LOGISTIC)||(M==null?void 0:M.TemplateName)===((B=Pt)==null?void 0:B.MRP)&&(G==null?void 0:G.viewName)==="Plant Data"?void 0:1,width:(M==null?void 0:M.TemplateName)===((Is=Pt)==null?void 0:Is.LOGISTIC)||(M==null?void 0:M.TemplateName)===((qn=Pt)==null?void 0:qn.MRP)&&(G==null?void 0:G.viewName)==="Plant Data"?200:void 0,renderCell:Ne=>{var En,Ye,it,pt,Yt,ln,jt,wn,Yn,jn,hs,ms,_n,gs,Zn,kn,fs,bs,Rs,ws,Bn,ut,On,vs,$,Ke,Qe,Ze;const Cn=(En=Object==null?void 0:Object.values(j))==null?void 0:En.find(U=>{var et;return(U==null?void 0:U.id)===((et=Ne==null?void 0:Ne.row)==null?void 0:et.id)}),ls=`${(Ye=Ne==null?void 0:Ne.row)==null?void 0:Ye.id}-${G==null?void 0:G.jsonName}`,Un=(it=Cn==null?void 0:Cn.missingFields)==null?void 0:it.includes(G==null?void 0:G.fieldName),is=z==null?void 0:z.includes((pt=Ne==null?void 0:Ne.row)==null?void 0:pt.id),sn=!!(se&&!((Yt=Ls)!=null&&Yt.includes(Le==null?void 0:Le.reqStatus)));if(G.fieldType===ci.INPUT)return e(Gd,{params:Ne,field:G,isFieldError:Un,isFieldDisable:sn,isNewRow:is,keyName:ls,handleChangeValue:Ge,handleRemoveError:Je,charCount:ee,setCharCount:re,isFocused:ge,setIsFocused:Te});if(G.fieldType===ci.DROPDOWN){const U=z==null?void 0:z.includes((ln=Ne==null?void 0:Ne.row)==null?void 0:ln.id),et=(G==null?void 0:G.jsonName)!=="Unittype1"&&(G==null?void 0:G.jsonName)!=="Spproctype"&&(G==null?void 0:G.jsonName)!=="MrpCtrler"?(jt=R==null?void 0:R[G==null?void 0:G.jsonName])==null?void 0:jt.find(qe=>{var rt;return qe.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[G==null?void 0:G.jsonName])}):(G==null?void 0:G.jsonName)==="Spproctype"||(G==null?void 0:G.jsonName)==="MrpCtrler"?(jn=(Yn=R==null?void 0:R[G==null?void 0:G.jsonName])==null?void 0:Yn[(wn=Ne==null?void 0:Ne.row)==null?void 0:wn.Plant])==null?void 0:jn.find(qe=>{var rt;return qe.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[G==null?void 0:G.jsonName])}):(_n=(ms=R==null?void 0:R[G==null?void 0:G.jsonName])==null?void 0:ms[(hs=Ne==null?void 0:Ne.row)==null?void 0:hs.WhseNo])==null?void 0:_n.find(qe=>{var rt;return qe.code===((rt=Ne==null?void 0:Ne.row)==null?void 0:rt[G==null?void 0:G.jsonName])});return e(Qt,{options:(G==null?void 0:G.jsonName)==="Unittype1"?(Zn=R==null?void 0:R.Unittype1)==null?void 0:Zn[(gs=Ne==null?void 0:Ne.row)==null?void 0:gs.WhseNo]:(G==null?void 0:G.jsonName)==="Spproctype"?(fs=R==null?void 0:R.Spproctype)==null?void 0:fs[(kn=Ne==null?void 0:Ne.row)==null?void 0:kn.Plant]:(G==null?void 0:G.jsonName)==="MrpCtrler"?(Rs=R==null?void 0:R.MrpCtrler)==null?void 0:Rs[(bs=Ne==null?void 0:Ne.row)==null?void 0:bs.Plant]:R!=null&&R[G==null?void 0:G.jsonName]?R==null?void 0:R[G==null?void 0:G.jsonName]:[],value:et||((ws=Ne==null?void 0:Ne.row)!=null&&ws[G==null?void 0:G.jsonName]?{code:(Bn=Ne==null?void 0:Ne.row)==null?void 0:Bn[G==null?void 0:G.jsonName],desc:""}:null),onChange:qe=>{Ge(Ne.row,Ne.row.id,G==null?void 0:G.jsonName,qe==null?void 0:qe.code,A,G==null?void 0:G.fieldName),Un&&Je(Ne.row.id,G==null?void 0:G.fieldName)},listWidth:150,placeholder:`Select ${G.fieldName}`,disabled:sn?!0:U?!1:(G==null?void 0:G.visibility)===((ut=ys)==null?void 0:ut.DISPLAY),isFieldError:Un})}else if(G.fieldType===ci.DATE_FIELD){const U=z==null?void 0:z.includes((On=Ne==null?void 0:Ne.row)==null?void 0:On.id),et=(vs=Ne==null?void 0:Ne.row)!=null&&vs[G==null?void 0:G.jsonName]?(()=>{var rt;const qe=(rt=Ne==null?void 0:Ne.row)==null?void 0:rt[G==null?void 0:G.jsonName];if(qe.startsWith("/Date(")&&qe.endsWith(")/")){const Kt=parseInt(qe.slice(6,-2));return new Date(Kt)}return typeof qe=="string"&&qe.match(/^\d{4}-\d{2}-\d{2}/)?new Date(qe):la(qe,["YYYY-MM-DD HH:mm:ss.S","DD MMM YYYY HH:mm:ss UTC"]).toDate()})():null;return e(Rn,{title:($=Ne==null?void 0:Ne.row)==null?void 0:$[G==null?void 0:G.jsonName],arrow:!0,placement:"top",children:e(ia,{dateAdapter:ra,children:e(ud,{disabled:sn?!0:U?!1:(G==null?void 0:G.visibility)===((Ke=ys)==null?void 0:Ke.DISPLAY),slotProps:{textField:{size:"small",fullWidth:!0,InputProps:{sx:{"& .MuiOutlinedInput-notchedOutline":{borderColor:Un?(Ze=(Qe=He)==null?void 0:Qe.error)==null?void 0:Ze.dark:void 0}}}}},value:et,onChange:qe=>{if(qe){const rt=`/Date(${Date.parse(qe)})/`;Ge(Ne.row,Ne.row.id,G==null?void 0:G.jsonName,rt,A,G==null?void 0:G.fieldName)}else Ge(Ne.row,Ne.row.id,G==null?void 0:G.jsonName,null,A,G==null?void 0:G.fieldName);Un&&Je(Ne.row.id,G==null?void 0:G.fieldName)},onError:qe=>{qe&&!Un&&N(tl.DATE_VALIDATION_ERROR,qe)},maxDate:new Date(9999,11,31)})})})}else return Ne.value||"-"}}}),{...(((M==null?void 0:M.TemplateName)===((be=Pt)==null?void 0:be.LOGISTIC)||(M==null?void 0:M.TemplateName)===((wt=Pt)==null?void 0:wt.UPD_DESC))&&!O||((M==null?void 0:M.TemplateName)===((Gt=Pt)==null?void 0:Gt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((tt=Pt)==null?void 0:tt.UPD_DESC))&&O&&((V==null?void 0:V.taskDesc)===((zn=wr)==null?void 0:zn.REQUESTOR)||(Le==null?void 0:Le.reqStatus)===((Wn=cs)==null?void 0:Wn.DRAFT)))&&{field:"action",headerName:"Action",flex:(M==null?void 0:M.TemplateName)===((ds=Pt)==null?void 0:ds.LOGISTIC)||(M==null?void 0:M.TemplateName)===((ss=Pt)==null?void 0:ss.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?void 0:1,width:(M==null?void 0:M.TemplateName)===((_t=Pt)==null?void 0:_t.LOGISTIC)||(M==null?void 0:M.TemplateName)===((dn=Pt)==null?void 0:dn.MRP)&&(field==null?void 0:field.viewName)==="Plant Data"?200:void 0,align:"center",headerAlign:"center",renderCell:G=>{var Mn;return e(Vs,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:e(Rn,{title:"Delete Row",children:e(cn,{disabled:!(z!=null&&z.includes((Mn=G==null?void 0:G.row)==null?void 0:Mn.id)),onClick:()=>{Ce({data:G,isVisible:!0})},color:"error",children:e(ul,{})})})})}}}],le=Array.isArray(D)?(E==null?void 0:E[a==null?void 0:a.page])||[]:((qt=E==null?void 0:E[a==null?void 0:a.page])==null?void 0:qt[A])||[],Ve=Array.isArray(u)?u:u[A];return f("div",{style:{height:400,width:"100%"},children:[e(oa,{paginationLoading:_,rows:le,rowCount:(le==null?void 0:le.length)??0,columns:Me,getRowIdValue:"id",rowHeight:70,isLoading:_,tempheight:"calc(100vh - 380px)",page:Oe,pageSize:It,selectionModel:Ve,onPageChange:dt,onPageSizeChange:tn,onCellEditCommit:Ge,checkboxSelection:!(se&&!((Hn=Ls)!=null&&Hn.includes(Le==null?void 0:Le.reqStatus))),disableSelectionOnClick:!0,showCustomNavigation:!0,hideFooter:!0}),(p==null?void 0:p.isVisible)&&f(zl,{isOpen:p==null?void 0:p.isVisible,titleIcon:e(ul,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:mt("Delete Row")+"!",handleClose:()=>Ce({...p,isVisible:!1}),children:[e(es,{sx:{mt:2},children:mt(el.DELETE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Li},onClick:()=>Ce({...p,isVisible:!1}),children:mt(jl.CANCEL)}),e(nt,{variant:"contained",size:"small",sx:{...Yl},onClick:Tt,children:mt(jl.DELETE)})]})]})]})},ke=x&&Object.keys(x);if(i.useEffect(()=>{var A,Ae,Me;(a==null?void 0:a.page)+1&&((M==null?void 0:M.RequestType)===((A=o)==null?void 0:A.CHANGE)||(M==null?void 0:M.RequestType)===((Ae=o)==null?void 0:Ae.CHANGE_WITH_UPLOAD))&&(O&&(!De||((Me=Object==null?void 0:Object.keys(De))==null?void 0:Me.length)===0)?(oe("display"),ht(0)):(oe("requestor"),ht(0)),H==="prev"?L(Ci(!1)):H==="next"&&(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)&&L(Ci(!0)))},[a==null?void 0:a.page]),(ke==null?void 0:ke.length)===1){const A=ke[0],Ae=x[A];return f(Vs,{children:[f(Vs,{direction:"row",justifyContent:"space-between",mb:1.5,children:[((M==null?void 0:M.TemplateName)===((yt=Pt)==null?void 0:yt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((Ln=Pt)==null?void 0:Ln.UPD_DESC))&&!O||((M==null?void 0:M.TemplateName)===((Zt=Pt)==null?void 0:Zt.LOGISTIC)||(M==null?void 0:M.TemplateName)===((nn=Pt)==null?void 0:nn.UPD_DESC))&&O&&((V==null?void 0:V.taskDesc)===((Ht=wr)==null?void 0:Ht.REQUESTOR)||(Le==null?void 0:Le.reqStatus)===((lt=cs)==null?void 0:lt.DRAFT))?e(nt,{variant:"contained",color:"primary",onClick:gn,startIcon:e($o,{}),sx:{borderRadius:"10px",boxShadow:"0px 4px 8px rgba(0, 0, 0, 0.15)"},children:"Add Row"}):e(Be,{sx:{width:0,height:0}}),f(Be,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",padding:"5px",borderRadius:"10px",mt:-1,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[e(Rn,{title:"Previous",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.page)===0||!1,onClick:()=>{fe("prev"),L(Sl((a==null?void 0:a.page)-1))},children:e(_i,{sx:{color:(a==null?void 0:a.page)===0?(Ft=($t=He)==null?void 0:$t.secondary)==null?void 0:Ft.grey:(xt=(yn=He)==null?void 0:yn.primary)==null?void 0:xt.main,fontSize:"1.5rem",marginRight:"2px"}})})}),f("span",{style:{marginRight:"2px"},children:[e("strong",{style:{color:(vn=(rn=He)==null?void 0:rn.primary)==null?void 0:vn.main},children:"Materials :"})," ",f("strong",{children:[(a==null?void 0:a.page)*(a==null?void 0:a.size)+1," -"," ",a==null?void 0:a.currentElements]})," ",e("span",{children:"of"})," ",e("strong",{children:a==null?void 0:a.totalElements})]}),e(Rn,{title:"Next",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)||!1,onClick:()=>{fe("next"),L(Sl((a==null?void 0:a.page)+1))},children:e(Yr,{sx:{color:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)?(Xt=(Gn=He)==null?void 0:Gn.secondary)==null?void 0:Xt.grey:(on=(we=He)==null?void 0:we.primary)==null?void 0:on.main,fontSize:"1.5rem"}})})})]})]}),e("div",{children:v(A,Ae)}),e(_l,{openSnackBar:he,alertMsg:Mt,alertType:Q,handleSnackBarClose:ft})]})}return f(kt,{children:[e(gl,{blurLoading:J}),!J&&e(kt,{children:x?f("div",{children:[f(Be,{sx:{display:"flex",justifyContent:"center",alignItems:"center",backgroundImage:"linear-gradient(180deg, rgb(242, 241, 255) 0%, rgb(255, 255, 255) 100%)",border:"1px solid #E0E0E0",borderRadius:"10px",padding:"5px",width:"fit-content",marginLeft:"auto",mt:-1,mb:2,boxShadow:"0px 2px 10px rgba(0, 0, 0, 0.08)"},children:[e(Rn,{title:"Previous",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.page)===0||!1,onClick:()=>{L(Sl((a==null?void 0:a.page)-1))},children:e(_i,{sx:{color:(a==null?void 0:a.page)===0?(an=(Qn=He)==null?void 0:Qn.secondary)==null?void 0:an.grey:(vt=(de=He)==null?void 0:de.primary)==null?void 0:vt.main,fontSize:"1.5rem",marginRight:"2px"}})})}),f("span",{style:{marginRight:"2px"},children:[e("strong",{style:{color:(Xn=(Jn=He)==null?void 0:Jn.primary)==null?void 0:Xn.main},children:"Materials :"})," ",f("strong",{children:[(a==null?void 0:a.page)*(a==null?void 0:a.size)+1," -"," ",a==null?void 0:a.currentElements]})," ",e("span",{children:"of"})," ",e("strong",{children:a==null?void 0:a.totalElements})]}),e(Rn,{title:"Next",placement:"top",arrow:!0,children:e(cn,{disabled:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)||!1,onClick:()=>{L(Sl((a==null?void 0:a.page)+1))},children:e(Yr,{sx:{color:(a==null?void 0:a.currentElements)>=(a==null?void 0:a.totalElements)?(Et=(Tn=He)==null?void 0:Tn.secondary)==null?void 0:Et.grey:(P=(fn=He)==null?void 0:fn.primary)==null?void 0:P.main,fontSize:"1.5rem"}})})})]}),ke==null?void 0:ke.map(A=>ue!=null&&ue.includes(A)?f(co,{sx:{marginBottom:"20px",boxShadow:3},children:[e(uo,{expandIcon:e(ao,{}),"aria-controls":`${A}-content`,id:`${A}-header`,sx:{backgroundImage:"linear-gradient(180deg,rgb(242, 241, 255) 0%,rgb(255, 255, 255) 100%)",padding:"8px 16px","&:hover":{backgroundImage:"linear-gradient(90deg,rgb(242, 242, 255) 0%,rgb(239, 232, 255) 100%)"}},children:e(st,{variant:"h6",sx:{fontWeight:"bold"},children:A})}),e(ho,{sx:{height:"calc(100vh - 300px)"},children:v(A,x[A])})]},A):null)]}):e(st,{children:"No data available"})})]})},kd=()=>{const{customError:n}=Os(),[N,oe]=i.useState([]),[I,M]=i.useState(!1),x=Z(_=>_.userManagement.taskData),[ue,D]=i.useState([]),E=Z(_=>_.applicationConfig),Se=as();let ce={handleSubmitForApproval:6,handleSendBack:1,handleReject:3,handleValidate:5,handleSAPSyndication:8,handleIdGenerator:4,handleSubmitForReview:7,handleCorrection:2};const z=Gl(Ks.CURRENT_TASK,!0,{});return i.useEffect(()=>{const _=(x==null?void 0:x.taskDesc)||(z==null?void 0:z.taskDesc),j=N==null?void 0:N.filter(ie=>ie.MDG_MAT_DYN_BTN_TASK_NAME===_),u=j==null?void 0:j.sort((ie,W)=>{const V=ce[ie.MDG_MAT_DYN_BTN_ACTION_TYPE],a=ce[W.MDG_MAT_DYN_BTN_ACTION_TYPE];return V-a});D(u),Se(Ta(u)),(u.find(ie=>ie.MDG_MAT_DYN_BTN_BUTTON_NAME===Vn.SEND_BACK)||u.find(ie=>ie.MDG_MAT_DYN_BTN_BUTTON_NAME===Vn.CORRECTION))&&M(!0)},[N]),{getButtonsDisplay:()=>{let _={decisionTableId:null,decisionTableName:"MDG_MAT_DYN_BUTTON_CONFIG",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_DYN_BTN_MODULE_NAME":"Article","MDG_CONDITIONS.MDG_MAT_DYN_BTN_REQUEST_TYPE":(x==null?void 0:x.ATTRIBUTE_2)||(z==null?void 0:z.ATTRIBUTE_2)}],systemFilters:null,systemOrders:null,filterString:null};const j=ie=>{var W,V;if(ie.statusCode===200){let a=(V=(W=ie==null?void 0:ie.data)==null?void 0:W.result[0])==null?void 0:V.MDG_MAT_DYN_BUTTON_CONFIG;oe(a)}},u=ie=>{n(ie)};E.environment==="localhost"?We(`/${Fs}${Ot.INVOKE_RULES.LOCAL}`,"post",j,u,_):We(`/${Fs}${Ot.INVOKE_RULES.PROD}`,"post",j,u,_)},showWfLevels:I}},$d=n=>{const[N,oe]=i.useState(!0),I=Z(Te=>Te.materialDropDownData.dropDown),M=Z(Te=>Te.payload),x=Z(Te=>Te.payload.payloadData),ue=x==null?void 0:x.RequestType,[D,E]=i.useState(!1),[Se,ce]=i.useState(!1),z=Z(Te=>Te.userManagement.taskData),R=Z(Te=>Te.payload.filteredButtons),_=ol(),j=new URLSearchParams(_.search),u=j.get("RequestType"),ie=j.get("RequestId"),W=Z(Te=>Te.payload.changeFieldRows),V=Z(Te=>Te.payload.dynamicKeyValues),a=Jl(),{getButtonsDisplay:K,showWfLevels:De}=kd(),{wfLevels:Fe}=ja({initialPayloadRequestType:ue,initialPayload:x,dynamicData:V,taskData:z,singlePayloadData:M}),ze=Di(R,[Bs.HANDLE_SUBMIT_FOR_APPROVAL,Bs.HANDLE_SAP_SYNDICATION,Bs.HANDLE_SUBMIT_FOR_REVIEW]);i.useEffect(()=>{(z!=null&&z.ATTRIBUTE_1||u)&&K()},[z]),i.useEffect(()=>{((W==null?void 0:W.length)!==0&&(W==null?void 0:W.length)!==void 0||!ge())&&(ce(!0),E(!0))},[W]);const ge=()=>{var Te;return(Te=Object==null?void 0:Object.values(W))==null?void 0:Te.every(ee=>(Array==null?void 0:Array.isArray(ee))&&(ee==null?void 0:ee.length)===0)};return i.useEffect(()=>{n.downloadClicked&&oe(!0)},[n.downloadClicked]),f("div",{children:[((x==null?void 0:x.TemplateName)&&(W&&(W==null?void 0:W.length)===0||ge())||n.downloadClicked)&&e(Ja,{open:N,onClose:()=>{var Te;oe(!1),n==null||n.setDownloadClicked(!1),ie||a((Te=ns)==null?void 0:Te.REQUEST_BENCH)},parameters:Ea[x==null?void 0:x.TemplateName],templateName:x==null?void 0:x.TemplateName,setShowTable:E,allDropDownData:I,setDownloadClicked:n==null?void 0:n.setDownloadClicked}),(D||Se)&&!(n!=null&&n.downloadClicked)&&f(kt,{children:[e(Ud,{setCompleted:n==null?void 0:n.setCompleted,RequestId:ie}),e(Bo,{filteredButtons:ze,setCompleted:n==null?void 0:n.setCompleted,showWfLevels:De,workFlowLevels:Fe})]}),e(Xl,{})]})},Pd=()=>{const{fetchDataAndDispatch:n}=Pa();return{fetchAllDropdownMasterData:()=>{[{url:`/${m}/data/getEanCat`,keyName:"CategoryOfInternationalArticleNumberEAN"},{url:`/${m}/data/getGenItemCatGroup`,keyName:"ItemCat"},{url:`/${m}/data/getTaxInd`,keyName:"Json121"},{url:`/${m}/data/getMatlGrp4`,keyName:"MatlGrp4"},{url:`/${m}/data/getMatlGrp5`,keyName:"MatlGrp5"},{url:`/${m}/data/getMatfrgtgrp`,keyName:"Matfrgtgrp"},{url:`/${m}/data/getBaseUom`,keyName:"CompUom"},{url:`/${m}/data/getBaseUom`,keyName:"CompUom"},{url:`/${m}/data/getBaseUom`,keyName:"DelyUom"},{url:`/${m}/data/getBaseUom`,keyName:"AltUnit"},{url:`/${m}/data/getBaseUom`,keyName:"UnitDim"},{url:`/${m}/data/getBaseUom`,keyName:"CommCoUn"},{url:`/${m}/data/getBaseUom`,keyName:"LeqUnit1"},{url:`/${m}/data/getBaseUom`,keyName:"Json103"},{url:`/${m}/data/getBaseUom`,keyName:"Json75"},{url:`/${m}/data/getBaseUom`,keyName:"Json156"},{url:`/${m}/data/getUnitType`,keyName:"Unittype1"},{url:`/${m}/data/getSproctype`,keyName:"Sproctype"},{url:`/${m}/data/getVarOrdUn`,keyName:"VarOrdUn"},{url:`/${m}/data/getMatlGroup`,keyName:"MatlGroup"},{url:`/${m}/data/getMatlGroup`,keyName:"MatlGrp"},{url:`/${m}/data/getUnitOfWt`,keyName:"UnitOfWt"},{url:`/${m}/data/getVolumeUnit`,keyName:"Volumeunit"},{url:`/${m}/data/getHazMatProf`,keyName:"Hazmatprof"},{url:`/${m}/data/getItemCat`,keyName:"Json213"},{url:`/${m}/data/getSalesUnit`,keyName:"Json201"},{url:`/${m}/data/getPurValkey`,keyName:"PurValkey"},{url:`/${m}/data/getBasicMatl`,keyName:"BasicMatl"},{url:`/${m}/data/getXDistChainStatus`,keyName:"SalStatus"},{url:`/${m}/data/getEanCat`,keyName:"EanCat"},{url:`/${m}/data/getLangu`,keyName:"Langu"},{url:`/${m}/data/getSalesOrg`,keyName:"SalesOrg"},{url:`/${m}/data/getMatlStats`,keyName:"MatlStats"},{url:`/${m}/data/getMatPrGrp`,keyName:"MatPrGrp"},{url:`/${m}/data/getAcctAssgt`,keyName:"AcctAssgt"},{url:`/${m}/data/getDepcountry`,keyName:"Depcountry"},{url:`/${m}/data/getXPlant`,keyName:"PurStatus"},{url:`/${m}/data/getPlantSpMatlStatus`,keyName:"PurStstus"},{url:`/${m}/data/getPlantSpMatlStatus`,keyName:"PlantSpMatlStatus"},{url:`/${m}/data/getXPlant`,keyName:"CrossPlantMaterialStatus"},{url:`/${m}/data/getExtMatlGrp`,keyName:"Extmatgrp"},{url:`/${m}/data/getLoadingGroup`,keyName:"Loadinggrp"},{url:`/${m}/data/getAvailCheck`,keyName:"Availcheck"},{url:`/${m}/data/getCountryOfOrigin`,keyName:"Countryori"},{url:`/${m}/data/getRebateGrp`,keyName:"RebateGrp"},{url:`/${m}/data/getMRPType`,keyName:"MrpType"},{url:`/${m}/data/getMRPType`,keyName:"Json151"},{url:`/${m}/data/getLotSizingProcedure`,keyName:"Lotsizekey"},{url:`/${m}/data/getProcurementType`,keyName:"ProcType"},{url:`/${m}/data/getBackflush`,keyName:"Backflush"},{url:`/${m}/data/getPeriodInd`,keyName:"PeriodInd"},{url:`/${m}/data/getPlanningStrategyGroup`,keyName:"PlanStrgp"},{url:`/${m}/data/getConsumptionMode`,keyName:"Consummode"},{url:`/${m}/data/getConsumptionPeriodBkwd`,keyName:"BwdCons"},{url:`/${m}/data/getConsumptionPeriodFwd`,keyName:"FwdCons"},{url:`/${m}/data/getIndividualColl`,keyName:"DepReqId"},{url:`/${m}/data/getSaftyTimeIndicator`,keyName:"SaftyTId"},{url:`/${m}/data/getMixedMRP`,keyName:"MixedMrp"},{url:`/${m}/data/getRequirementGroup`,keyName:"GrpReqmts"},{url:`/${m}/data/getPriceUnit`,keyName:"PriceUnit"},{url:`/${m}/data/getMatlType`,keyName:"MatlType"},{url:`/${m}/data/getIndSector`,keyName:"IndSector"},{url:`/${m}/data/getTransGrp`,keyName:"TransGrp"},{url:`/${m}/data/getProfitCenter`,keyName:"ProfitCtr"},{url:`/${m}/data/getMatlGrp2`,keyName:"MatlGrp2"},{url:`/${m}/data/getProdAllocation`,keyName:"ProdAlloc"},{url:`/${m}/data/getVarianceKey`,keyName:"VarianceKey"},{url:`/${m}/data/getConsumptionPeriodFwd`,keyName:"ConsumptionPeriodFwd"},{url:`/${m}/data/getPrimaryVendor`,keyName:"PryVendor"},{url:`/${m}/data/getVendorDetails`,keyName:"Supplier"},{url:`/${m}/data/getVendorDetails`,keyName:"Json215"},{url:`/${m}/data/getBomUsage`,keyName:"BomUsage"},{url:`/${m}/data/getBomItemCategory`,keyName:"Category"},{url:`/${m}/data/getPlant`,keyName:"ProcurementPlant"},{url:`/${m}/data/getPurchaseOrg`,keyName:"PurchaseOrg"},{url:`/${m}/data/getTemperatureCondition`,keyName:"TempConds"},{url:`/${m}/data/getLabelType`,keyName:"LabelType"},{url:`/${m}/data/getLabelForm`,keyName:"LabelForm"},{url:`/${m}/data/getRoundingRuleSLED`,keyName:"RoundUpRuleExpirationDate"},{url:`/${m}/data/getExpirationDate`,keyName:"SledBbd"},{url:`/${m}/data/getSerialNumberLevel`,keyName:"SerializationLevel"},{url:`/${m}/data/getUnitOfIssue`,keyName:"IssueUnit"},{url:`/${m}/data/getTimeUnit`,keyName:"StgePdUn"},{url:`/${m}/data/getSerialNumberProfile`,keyName:"SernoProf"},{url:`/${m}/data/getDistributionProfile`,keyName:"Json125"},{url:`/${m}/data/getRndingProfile`,keyName:"Json209"},{url:`/${m}/data/getStockDeterminationGroup`,keyName:"DetermGrp"},{url:`/${m}/data/getIUIDType`,keyName:"IuidType"},{url:`/${m}/data/getClassType`,keyName:"Classtype"},{url:`/${m}/data/getVendorDetails`,keyName:"Json301"},{url:`/${m}/data/getPurGroup`,keyName:"Json13"},{url:`/${m}/data/getOrderUnit`,keyName:"Json45"},{url:`/${m}/data/getOrderUnit`,keyName:"Json252"},{url:`/${m}/data/getGeneric2`,keyName:"Json16"},{url:`/${m}/data/getGeneric3`,keyName:"Json26"},{url:`/${m}/data/getGeneric4`,keyName:"Json2"},{url:`/${m}/data/getCountryOfOrigin`,keyName:"Json4"},{url:`/${m}/data/getBaseUom`,keyName:"BaseUom"},{url:`/${m}/data/getProdHier`,keyName:"ProdHier"},{url:`/${m}/data/getExtMatlGrp`,keyName:"Extmatlgrp"},{url:`/${m}/data/getDivision`,keyName:"Division"},{url:`/${m}/data/getStorageCondition`,keyName:"StorConds"},{url:`/${m}/data/getContainerRequirements`,keyName:"Container"},{url:`/${m}/data/getHazMatNo`,keyName:"HazMatNo"},{url:`/${m}/data/getTransGrp`,keyName:"TransGrp"},{url:`/${m}/data/getMaterialGroupPack`,keyName:"MatGroupPackagingMat"},{url:`/${m}/data/getGenItemCatGroup`,keyName:"GItemCat"},{url:`/${m}/data/getXDistChainStatus`,keyName:"XSalStatus"},{url:`/${m}/data/getCSalStatus`,keyName:"CSalStatus"},{url:`/${m}/data/getPeriodIndicatorSLED`,keyName:"PeriodIndExpirationDate"},{url:`/${m}/data/getMatlGroup`,keyName:"MatlGroup"},{url:`/${m}/data/getLoadingGroup`,keyName:"Json302"},{url:`/${m}/data/getPurGroup`,keyName:"Json303"},{url:`/${m}/data/getCountryOfOrigin`,keyName:"Json304"},{url:`/${m}/data/getCountryOfOrigin`,keyName:"Json305"},{url:`/${m}/data/getGeneric1`,keyName:"Json306"},{url:`/${m}/data/getIndSector`,keyName:"Json307"},{url:`/${m}/data/getGeneric5`,keyName:"Json51"},{url:`/${m}/data/getGeneric6`,keyName:"Json60"},{url:`/${m}/data/getGeneric6`,keyName:"Json61"},{url:`/${m}/data/getBaseUom`,keyName:"Json75"},{url:`/${m}/data/getLangu`,keyName:"Json74"},{url:`/${m}/data/getDChainSpecStatus`,keyName:"Json71"},{url:`/${m}/data/getVendorDetails`,keyName:"Json301"},{url:`/${m}/data/getGeneric7`,keyName:"Json48"},{url:`/${m}/data/getBaseUom`,keyName:"Json45"},{url:`/${m}/data/getLoadingGroup`,keyName:"Json127"}].forEach(({url:I,keyName:M})=>{n(I,M)})}}},pd=()=>{const n=as(),[N,oe]=i.useState(!1),[I,M]=i.useState(null),{getChangeTemplate:x}=Ro(),{fetchDisplayDataRows:ue}=Wa(),{createFCRows:D}=po(),{customError:E}=Os(),{showSnackbar:Se}=Ri();return{getDisplayData:i.useCallback(async(z,R,_,j,u)=>new Promise((ie,W)=>{oe(!0),M(null),n(ai(!0));const V=z,a=Gl(Ks.CURRENT_TASK,!0,{}),K=R||(j==null?void 0:j.ATTRIBUTE_2)||(a==null?void 0:a.ATTRIBUTE_2);let De=_?{massCreationId:u!=null&&u.isBifurcated?"":K===o.CREATE||K===o.CREATE_WITH_UPLOAD?V:"",massChildCreationId:u!=null&&u.isBifurcated&&(K===o.CREATE||K===o.CREATE_WITH_UPLOAD)?V:"",massChangeId:u!=null&&u.isBifurcated?"":K===o.CHANGE||K===o.CHANGE_WITH_UPLOAD?V:"",massExtendId:u!=null&&u.isBifurcated?"":K===o.EXTEND||K===o.EXTEND_WITH_UPLOAD?V:"",massSchedulingId:u!=null&&u.isBifurcated?"":K===o.FINANCE_COSTING?V:"",screenName:K===o.FINANCE_COSTING?"":K,dtName:K===o.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:K===o.FINANCE_COSTING?"":"v2",page:0,size:K===o.FINANCE_COSTING?100:K===o.CHANGE||K===o.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:j==null?void 0:j.ATTRIBUTE_5,Region:"",massChildSchedulingId:u!=null&&u.isBifurcated&&K===o.FINANCE_COSTING?V:"",massChildExtendId:u!=null&&u.isBifurcated&&(K===o.EXTEND||K===o.EXTEND_WITH_UPLOAD)?V:"",massChildChangeId:u!=null&&u.isBifurcated&&(K===o.CHANGE||K===o.CHANGE_WITH_UPLOAD)?V:""}:{massCreationId:"",massChangeId:"",massSchedulingId:K===o.FINANCE_COSTING||K==="Finance Costing"?V:"",massExtendId:"",screenName:K==="MASS_CREATE"||K==="Mass Create"||K===o.CREATE?o.CREATE:K===o.FINANCE_COSTING?"":o.CHANGE,dtName:K===o.FINANCE_COSTING?"":"MDG_MAT_MATERIAL_FIELD_CONFIG",version:K===o.FINANCE_COSTING?"":"v2",page:0,size:K===o.FINANCE_COSTING||R===o.FINANCE_COSTING?100:R===o.CHANGE||R===o.CHANGE_WITH_UPLOAD||K===o.CHANGE||K===o.CHANGE_WITH_UPLOAD?10:50,sort:"",ApproverGroup:j==null?void 0:j.ATTRIBUTE_5,Region:"",massChildCreationId:K==="MASS_CREATE"||K==="Mass Create"||K===o.CREATE||K===o.CREATE_WITH_UPLOAD?V:"",massChildSchedulingId:"",massChildExtendId:K===o.EXTEND||K===o.EXTEND_WITH_UPLOAD?V:"",massChildChangeId:K==="MASS_CHANGE"||K==="Mass Change"||K===o.CHANGE||K===o.CHANGE_WITH_UPLOAD?V:""};const Fe=async ge=>{var Te,ee,re,H,fe,Q,Re,he,te,Mt,T,J,p,Ce,L,q,b,se,O,mt,Le;try{if((ge==null?void 0:ge.statusCode)===Wt.STATUS_200){n(ai(!1)),oe(!1);const Oe=ge.body;if(n(Aa(ge==null?void 0:ge.totalElements)),(ge==null?void 0:ge.totalPages)===1||(ge==null?void 0:ge.currentPage)+1===(ge==null?void 0:ge.totalPages)?(n(Jr(ge==null?void 0:ge.totalElements)),n(Ci(!0))):n(Jr(((ge==null?void 0:ge.currentPage)+1)*(ge==null?void 0:ge.pageSize))),(j==null?void 0:j.ATTRIBUTE_2)===o.CHANGE||(j==null?void 0:j.ATTRIBUTE_2)===o.CHANGE_WITH_UPLOAD||R===o.CHANGE_WITH_UPLOAD||R===o.CHANGE){n(pl({keyName:"requestHeaderData",data:(Te=Oe[0])==null?void 0:Te.Torequestheaderdata})),x(((ee=Oe[0])==null?void 0:ee.Torequestheaderdata)||"",Oe[0]||{}),ue(Oe),ie(ge);return}if(R===o.FINANCE_COSTING||(j==null?void 0:j.ATTRIBUTE_2)===o.FINANCE_COSTING){const dt={ReqCreatedBy:(H=(re=Oe[0])==null?void 0:re.Torequestheaderdata)==null?void 0:H.ReqCreatedBy,RequestStatus:(Q=(fe=Oe[0])==null?void 0:fe.Torequestheaderdata)==null?void 0:Q.RequestStatus,Region:(he=(Re=Oe[0])==null?void 0:Re.Torequestheaderdata)==null?void 0:he.Region,ReqCreatedOn:new Date().toISOString(),ReqUpdatedOn:new Date().toISOString(),RequestType:(Mt=(te=Oe[0])==null?void 0:te.Torequestheaderdata)==null?void 0:Mt.RequestType,RequestDesc:(J=(T=Oe[0])==null?void 0:T.Torequestheaderdata)==null?void 0:J.RequestDesc,RequestPriority:(Ce=(p=Oe[0])==null?void 0:p.Torequestheaderdata)==null?void 0:Ce.RequestPriority,LeadingCat:(q=(L=Oe[0])==null?void 0:L.Torequestheaderdata)==null?void 0:q.LeadingCat,RequestId:(se=(b=Oe[0])==null?void 0:b.Torequestheaderdata)==null?void 0:se.RequestId,TemplateName:(mt=(O=Oe[0])==null?void 0:O.Torequestheaderdata)==null?void 0:mt.TemplateName};n(Dl({data:dt}));const tn=await D(Oe);n(Sa(tn)),ie(ge);return}const ht=await ma(Oe);await n(Io({data:ht==null?void 0:ht.payload}));const It=Object.keys(ht==null?void 0:ht.payload).filter(dt=>!isNaN(Number(dt))),bt={};It.forEach(dt=>{bt[dt]=ht==null?void 0:ht.payload[dt]}),n(Pn((Le=Object.values(bt))==null?void 0:Le.map(dt=>dt.headerData))),ie(ge)}else Se(ge==null?void 0:ge.message,"error")}catch(Oe){E(tl.ERROR_GET_DISPLAY_DATA),M(Oe),oe(!1),W(Oe)}},ze=ge=>{E(tl.ERROR_FETCHING_DATA),M(ge),oe(!1),n(ai(!1)),W(ge)};We(`/${m}/data/displayMassMaterialDTO`,"post",Fe,ze,De)}),[n]),loading:N,error:I,clearError:()=>M(null)}},Hu=()=>{var Tn,Et,fn,P,A,Ae,Me,le,Ve,$e,Vt,_e,Nn,be,wt,Gt,tt,zn,Wn,ds,ss,_t,dn,qt,Hn,G,Mn,Ss,en,us,B,Is,qn,Ne,Cn,ls,Un,is,sn,En;const{customError:n}=Os(),[N,oe]=i.useState(!1),[I,M]=i.useState([]),[x,ue]=i.useState(!1),[D,E]=i.useState(!1),[Se,ce]=i.useState(!1),[z,R]=i.useState(""),[_,j]=i.useState(!1),[u,ie]=i.useState([]),[W,V]=i.useState(!1),[a,K]=i.useState(!1),[De,Fe]=i.useState(""),[ze,ge]=i.useState(),[Te,ee]=i.useState(""),[re,H]=i.useState(!1),[fe,Q]=i.useState(""),[Re,he]=i.useState("success"),[te,Mt]=i.useState(!1),[T,J]=i.useState(!1),[p,Ce]=i.useState(!1),[L,q]=i.useState(!1),b=as(),se=Z(Ye=>Ye.applicationConfig),O=Z(Ye=>Ye.payload.payloadData),mt=Z(Ye=>Ye.payload),Le=Z(Ye=>{var it;return(it=Ye.request.requestHeader)==null?void 0:it.requestId});Z(Ye=>Ye.request.requestHeader.requestType);const Oe=Z(Ye=>{var it;return(it=Ye.userManagement)==null?void 0:it.taskData});Z(Ye=>{var it;return(it=Ye.materialDropDownData)==null?void 0:it.isOdataApiCalled});const{getDtCall:ht,dtData:It}=to(),bt=Jl(),[dt,tn]=i.useState(!0),Ct=Z(Ye=>Ye.request.tabValue),{t:ft}=Cl(),{fetchAllDropdownMasterData:gn}=Pd(),{getRequestHeaderTemplate:Jt}=Ho(),Rt=[ft("Request Header"),ft("Article List"),ft("Attachments & Remarks"),ft("Preview")],[gt,Ie]=i.useState([!1]),pe=Ye=>{b(Zs(Ye))},Y=ol(),ne=Y.state,Je=new URLSearchParams(Y.search.split("?")[1]).get("RequestId"),Tt=new URLSearchParams(Y.search),v=Tt.get("RequestId"),ke=Tt.get("RequestType"),yt=Tt.get("reqBench"),Ln=!(Oe!=null&&Oe.taskId)&&!yt,{createPayloadFromReduxState:Zt}=vo({initialReqScreen:Ln,isReqBench:yt}),{changePayloadForTemplate:nn}=yo(O==null?void 0:O.TemplateName),Ht=((Tn=Y.state)==null?void 0:Tn.isChildRequest)??(v&&!yt)??!1,lt=(O==null?void 0:O.RequestType)===((Et=o)==null?void 0:Et.CHANGE)||(O==null?void 0:O.RequestType)===((fn=o)==null?void 0:fn.CHANGE_WITH_UPLOAD)?nn(!!v):Zt(mt),$t={materialDetails:lt,dtName:di((A=(P=lt==null?void 0:lt[0])==null?void 0:P.Torequestheaderdata)==null?void 0:A.RequestType).dtName,version:di((Me=(Ae=lt==null?void 0:lt[0])==null?void 0:Ae.Torequestheaderdata)==null?void 0:Me.RequestType).version,requestId:((Ve=(le=lt==null?void 0:lt[0])==null?void 0:le.Torequestheaderdata)==null?void 0:Ve.RequestId)||"",scenario:(_e=di((Vt=($e=lt==null?void 0:lt[0])==null?void 0:$e.Torequestheaderdata)==null?void 0:Vt.RequestType))==null?void 0:_e.scenario,templateName:(O==null?void 0:O.RequestType)===((Nn=o)==null?void 0:Nn.CHANGE)||(O==null?void 0:O.RequestType)===((be=o)==null?void 0:be.CHANGE_WITH_UPLOAD)?(Gt=(wt=lt==null?void 0:lt[0])==null?void 0:wt.Torequestheaderdata)==null?void 0:Gt.TemplateName:"",matlType:"ALL",region:((zn=(tt=lt==null?void 0:lt[0])==null?void 0:tt.Torequestheaderdata)==null?void 0:zn.Region)||""},{getDisplayData:Ft}=pd(),yn=()=>{V(!0)},xt=()=>{V(!1)},rn=()=>{K(!0)},vn=Ye=>{K(Ye)},Gn=()=>{Te==="success"?bt("/requestBench"):xt()},Xt=()=>{ue(!0)},we=Ye=>{let it="";ke===o.CREATE_WITH_UPLOAD?it="getAllMaterialsFromExcel":ke===o.EXTEND_WITH_UPLOAD?it="getAllMaterialsFromExcelForMassExtend":it="getAllMaterialsFromExcelForMassChange",Q("Initiating Excel Upload"),H(!0);const pt=new FormData;[...Ye].forEach(jt=>pt.append("files",jt)),pt.append("dtName",ke===o.CREATE_WITH_UPLOAD||ke===o.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),pt.append("version",ke===o.CREATE_WITH_UPLOAD||ke===o.EXTEND_WITH_UPLOAD?"v1":"v5"),pt.append("requestId",Je||""),pt.append("region",O!=null&&O.Region?O==null?void 0:O.Region:"US"),pt.append("matlType","ALL");const Yt=jt=>{var wn;(jt==null?void 0:jt.statusCode)===Wt.STATUS_200?(j(!1),H(!1),Q(""),bt((wn=ns)==null?void 0:wn.REQUEST_BENCH)):(j(!1),H(!1),ge(jt==null?void 0:jt.message),Q(""),he("error"),de())},ln=jt=>{H(!1),ge(jt==null?void 0:jt.message),Q(""),he("error"),de()};We(`/${m}/massAction/${it}`,"postformdata",Yt,ln,pt)};i.useEffect(()=>((async()=>{var it;if(Je){const pt=Gl(Ks.CURRENT_TASK,!0,{}),Yt=ke||(Oe==null?void 0:Oe.ATTRIBUTE_2)||(pt==null?void 0:pt.ATTRIBUTE_2);await Ft(v,Yt,yt,Oe,ne,"Article"),(ke===o.CHANGE_WITH_UPLOAD&&!((it=ne==null?void 0:ne.material)!=null&&it.length)||ke===o.CREATE_WITH_UPLOAD||ke===o.EXTEND_WITH_UPLOAD)&&((ne==null?void 0:ne.reqStatus)===cs.DRAFT||(ne==null?void 0:ne.reqStatus)===cs.UPLOAD_FAILED)?(b(Zs(0)),E(!1),ce(!1)):(b(Zs(1)),E(!0),ce(!0)),J(!0)}else b(Zs(0))})(),()=>{b(so([])),b(fa()),b(Na()),b(Ca()),b(_a()),b(Oa()),b(Nl({})),b(Dl({data:{}})),b(Ia([])),b(ba([])),b(Ra({})),b(Ma()),b(bo([])),b(Fl([])),b(Pn([])),b(Vl({})),ui(Ks.CURRENT_TASK),ui(Ks.ROLE)}),[Je,b]);function on(Ye){let it={decisionTableId:null,decisionTableName:lo.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":Ye}]};ht(it)}i.useEffect(()=>{O!=null&&O.Region&&on(O==null?void 0:O.Region)},[O==null?void 0:O.Region]),i.useEffect(()=>{var Ye,it;if(It){const Yt=[...xa((it=(Ye=It==null?void 0:It.result)==null?void 0:Ye[0])==null?void 0:it.MDG_MAT_REGION_DIVISION_MAPPING)].sort((ln,jt)=>ln.code.localeCompare(jt.code));b(Ys({keyName:"Division",data:Yt})),tn(!1),Q(Da.DT_LOADING)}},[It]),i.useEffect(()=>(gn(),La(Ks.MODULE,Ds.ART),Jt(),Qn(),b(Pn([])),b(Ys({keyName:"Region",data:ya})),b(Ys({keyName:"DiversionControlFlag",data:va})),R(Ga("MAT")),()=>{b(no({})),ui(Ks.MODULE)}),[]),i.useEffect(()=>{D&&Ie([!0])},[D]);const Qn=()=>{let Ye={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Article","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};oe(!0);const it=Yt=>{var ln,jt;if(oe(!1),Yt.statusCode===200){const Yn=((jt=(ln=Yt==null?void 0:Yt.data)==null?void 0:ln.result[0])==null?void 0:jt.MDG_ATTACHMENTS_ACTION_TYPE)||[];ie(Yn)}},pt=Yt=>{n(Yt)};se.environment==="localhost"?We(`/${Fs}/rest/v1/invoke-rules`,"post",it,pt,Ye):We(`/${Fs}/v1/invoke-rules`,"post",it,pt,Ye)},an=()=>{var jt,wn,Yn,jn,hs,ms;const Ye=v!=null&&v.includes("FCA")?Ot.EXCEL.DOWNLOAD_EXCEL_FINANCE:Ot.EXCEL.DOWNLOAD_EXCEL_MAT;Q("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),H(!0);let it={massSchedulingId:O==null?void 0:O.RequestId},pt={dtName:(O==null?void 0:O.RequestType)===((jt=o)==null?void 0:jt.CHANGE)||(O==null?void 0:O.RequestType)===((wn=o)==null?void 0:wn.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(O==null?void 0:O.RequestType)===((Yn=o)==null?void 0:Yn.CHANGE)||(O==null?void 0:O.RequestType)===((jn=o)==null?void 0:jn.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(O==null?void 0:O.RequestId)||Le||"",scenario:(O==null?void 0:O.RequestType)===((hs=o)==null?void 0:hs.CHANGE)||(O==null?void 0:O.RequestType)===((ms=o)==null?void 0:ms.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(O==null?void 0:O.TemplateName)||"",region:(O==null?void 0:O.Region)||"",matlType:"ALL"};const Yt=_n=>{const gs=URL.createObjectURL(_n),Zn=document.createElement("a");Zn.href=gs,Zn.setAttribute("download",`${O!=null&&O.TemplateName?O==null?void 0:O.TemplateName:v!=null&&v.includes("FCA")?o.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(Zn),Zn.click(),document.body.removeChild(Zn),URL.revokeObjectURL(gs),H(!1),Q(""),ge(`${O!=null&&O.TemplateName?O==null?void 0:O.TemplateName:v!=null&&v.includes("FCA")?o.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),he("success"),de()},ln=()=>{};We(`/${m}${Ye}`,"postandgetblob",Yt,ln,v!=null&&v.includes("FCA")?it:pt)},de=()=>{Mt(!0)},vt=()=>{Mt(!1)},Jn=()=>{var Ye,it,pt;Je&&!yt?bt((Ye=ns)==null?void 0:Ye.MY_TASK):yt?bt((it=ns)==null?void 0:it.REQUEST_BENCH):!Je&&!yt&&bt((pt=ns)==null?void 0:pt.ARTICLE_MASTER_DATA)},Xn=()=>{q(!1)};return f(kt,{children:[dt&&e(gl,{blurLoading:re,loaderMessage:fe}),f(Be,{sx:{padding:2},children:[f(ve,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[Le||Je?f(Vs,{direction:"row",spacing:1,children:[f(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(ad,{sx:{fontSize:"1.5rem"}}),ft("Request Header ID"),": ",e("span",{children:Le||Je})]}),(Oe==null?void 0:Oe.taskDesc)&&e(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1,color:"#3B30C8"},children:`- ${Oe==null?void 0:Oe.taskDesc}`})]}):e("div",{style:{flex:1}}),Ct===1&&f(Be,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[e(nt,{variant:"outlined",size:"small",title:ft("Download Error Report"),disabled:!v,onClick:()=>{bt(`/requestBench/errorHistory?RequestId=${v||""}`,{state:{display:!0,childRequest:Ht,module:Ds.ART}})},color:"primary",children:e(Ua,{sx:{padding:"2px"}})}),(O==null?void 0:O.RequestType)===o.CREATE||(O==null?void 0:O.RequestType)===o.EXTEND||(O==null?void 0:O.RequestType)===o.EXTEND_WITH_UPLOAD||(O==null?void 0:O.RequestType)===o.CREATE_WITH_UPLOAD||Je!=null&&Je.includes("FCA")?e(nt,{variant:"outlined",disabled:!v,size:"small",onClick:()=>Ce(!0),title:Je!=null&&Je.includes("FCA")?ft("Finance Costing Change Log"):ft("Create Change Log"),children:e(eo,{sx:{padding:"2px"}})}):e(nt,{variant:"outlined",disabled:!v,size:"small",onClick:rn,title:ft("Change Log"),children:e(eo,{sx:{padding:"2px"}})}),e(nt,{variant:"outlined",disabled:!v,size:"small",onClick:an,title:ft("Export Excel"),children:e(rd,{sx:{padding:"2px"}})})]}),a&&e(Xa,{open:!0,closeModal:vn,requestId:Le||Je,requestType:O==null?void 0:O.RequestType}),p&&e(za,{module:(Wn=Ds)==null?void 0:Wn.ART,open:!0,closeModal:()=>Ce(!1),requestId:Le||Je,requestType:O==null?void 0:O.RequestType})]}),(O==null?void 0:O.TemplateName)&&f(st,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[e(dd,{sx:{fontSize:"1.5rem"}}),ft("Template Name"),": ",e("span",{children:O==null?void 0:O.TemplateName})]}),e(cn,{onClick:()=>{var Ye,it;if(yt&&!((Ye=Ls)!=null&&Ye.includes(O==null?void 0:O.RequestStatus))){bt((it=ns)==null?void 0:it.REQUEST_BENCH);return}q(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:ft("Back"),children:e(_i,{sx:{fontSize:"25px",color:"#000000"}})}),e(mo,{nonLinear:!0,activeStep:Ct,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Rt.map((Ye,it)=>e(mi,{children:e(fi,{color:"error",disabled:it===1&&!D||it===2&&!Se||it===3&&!Se,onClick:()=>pe(it),sx:{fontSize:"50px",fontWeight:"bold"},children:e("span",{style:{fontSize:"15px",fontWeight:"bold"},children:Ye})})},Ye))}),e(Ql,{dialogState:W,openReusableDialog:yn,closeReusableDialog:xt,dialogTitle:De,dialogMessage:ze,handleDialogConfirm:xt,dialogOkText:"OK",handleOk:Gn,dialogSeverity:Te}),e(gl,{blurLoading:re,loaderMessage:fe}),Ct===0&&f(kt,{children:[e(Ad,{setIsSecondTabEnabled:E,setIsAttachmentTabEnabled:ce,requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,downloadClicked:x,setDownloadClicked:ue}),(ke===o.CHANGE_WITH_UPLOAD||ke===o.CREATE_WITH_UPLOAD||ke===o.EXTEND_WITH_UPLOAD)&&((ne==null?void 0:ne.reqStatus)==cs.DRAFT&&!((ds=ne==null?void 0:ne.material)!=null&&ds.length)||(ne==null?void 0:ne.reqStatus)==cs.UPLOAD_FAILED)&&e(hd,{handleDownload:Xt,setEnableDocumentUpload:j,enableDocumentUpload:_,handleUploadMaterial:we}),((O==null?void 0:O.RequestType)===((ss=o)==null?void 0:ss.CHANGE)||(O==null?void 0:O.RequestType)===((_t=o)==null?void 0:_t.CHANGE_WITH_UPLOAD))&&!v&&(O==null?void 0:O.DirectAllowed)!=="X"&&(O==null?void 0:O.DirectAllowed)!==void 0&&f(st,{sx:{fontSize:"13px",fontWeight:"500",color:(qt=(dn=He)==null?void 0:dn.error)==null?void 0:qt.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[e(Be,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",f(Be,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),Ct===1&&((O==null?void 0:O.RequestType)===((Hn=o)==null?void 0:Hn.CREATE)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((G=o)==null?void 0:G.CREATE)||ke===((Mn=o)==null?void 0:Mn.CREATE)||ke===((Ss=o)==null?void 0:Ss.CREATE_WITH_UPLOAD)?e(Dd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,mandFields:I,addHardCodeData:T,setIsAttachmentTabEnabled:ce,setCompleted:Ie}):(O==null?void 0:O.RequestType)===((en=o)==null?void 0:en.EXTEND)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((us=o)==null?void 0:us.EXTEND)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((B=o)==null?void 0:B.EXTEND_WITH_UPLOAD)||ke===((Is=o)==null?void 0:Is.EXTEND)||ke===((qn=o)==null?void 0:qn.EXTEND_WITH_UPLOAD)?e(vd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,mandFields:I,addHardCodeData:T,setIsAttachmentTabEnabled:ce,setCompleted:Ie}):(O==null?void 0:O.RequestType)===((Ne=o)==null?void 0:Ne.FINANCE_COSTING)||(Oe==null?void 0:Oe.ATTRIBUTE_2)===((Cn=o)==null?void 0:Cn.FINANCE_COSTING)||ke===((ls=o)==null?void 0:ls.FINANCE_COSTING)?e(Ya,{setCompleted:Ie}):e($d,{setIsAttachmentTabEnabled:!0,setCompleted:Ie,downloadClicked:x,setDownloadClicked:ue})),Ct===2&&e(nd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,attachmentsData:u,requestIdHeader:Le||Je,pcNumber:z,module:(Un=Ds)==null?void 0:Un.ART,artifactName:ka.ART}),Ct===3&&e(Be,{sx:{width:"100%",overflow:"auto"},children:e(sd,{requestStatus:ne!=null&&ne.reqStatus?ne==null?void 0:ne.reqStatus:cs.ENABLE_FOR_FIRST_TIME,module:(is=Ds)==null?void 0:is.ART,payloadData:mt,payloadForDownloadExcel:$t})})]}),e(_l,{openSnackBar:te,alertMsg:ze,alertType:Re,handleSnackBarClose:vt}),L&&f(zl,{isOpen:L,titleIcon:e($a,{size:"small",sx:{color:(En=(sn=He)==null?void 0:sn.secondary)==null?void 0:En.amber,fontSize:"20px"}}),Title:ft("Warning"),handleClose:Xn,children:[e(es,{sx:{mt:2},children:ft(el.LEAVE_PAGE_MESSAGE)}),f(ts,{children:[e(nt,{variant:"outlined",size:"small",sx:{...Li},onClick:Xn,children:ft("No")}),e(nt,{variant:"contained",size:"small",sx:{...Yl},onClick:Jn,children:ft("Yes")})]})]})]})};export{Hu as default};
