import { useState,useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { setEntitiesAndActivities } from "../app/userManagementSlice";
import { doAjax } from "../components/Common/fetchService";
import { API_CODE, ERROR_MESSAGES, LOADING_MESSAGE } from "@constant/enum";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_MaterialMgmt, destination_IWA_NEW } from "../destinationVariables";

// Fallback entities for localhost environment
const fallbackEntities = {
  "Request Bench": ["Request Bench", "Manage Scheduler"],
  "Config Cockpit": ["User Management", "Email Template Configurations", "Business Rules", "Broadcast Configurations","Document Configurations","Workflow Configurations", "SLA Configurations"],
  Dashboard: ["Dashboard"],
  Home: ["Home"],
  Material: ["Extend with Upload", "Change with Upload", "Create", "Change", "Extend", "Create with Upload"],
  Article: ["Create", "Change", "Create with Upload"],
  "Master Data": ["Retail","Material Management","Finance"],
  "Retail": ["Article"],
  "Material Management": ["Material", "BOM"],
  "Finance": ["Cost Center", "Profit Center", "General Ledger", "Internal Order", "Cost Center Hierarchy", "Profit Center Hierarchy", "General Ledger Hierarchy", "Bank Key"],
  "Cost Center": ["Create", "Change", "Display"],
  "Profit Center": ["Create", "Change", "Display"],
  "General Ledger": ["Create", "Change", "Extend", "Display"],
  "Internal Order": ["Create", "Change", "Display"],
  "Cost Center Hierarchy": ["Create", "Change", "Display"],
  "Profit Center Hierarchy": ["Create", "Change", "Display"],
  "General Ledger Hierarchy": ["Create", "Change", "Display"],
  "Bank Key": ["Create", "Change", "Display"],
  "BOM": ["Create", "Change", "Display"],
  "Document Management": ["Document Management"],
  Workspace: ["My Tasks", "Completed Tasks", "Admin Completed Tasks", "Admin Tasks","Substitution"],
  "My Tasks": ["My Tasks"],
  "Completed Tasks": ["Completed Tasks"],
  "Admin Tasks": ["Admin Tasks"],
  "Admin Completed Tasks": ["Admin Completed Tasks"],
  "Substitution": ["Substitution"],
  "Data Cleanse": ["Material Master", "Cost Center", "Profit Center", "Bank Key", "General Ledger"],
  // Config Cockpit Child Items
  "Business Rules": ["Authoring", "Modelling"],
  "User Management": ["Users", "Roles", "Groups"],
  "Email Template Configurations": ["Email Template Configurations"],
  "Broadcast Configurations": ["Broadcast Configurations"],
  "Document Configurations": ["Document Configurations"],
  "Workflow Configurations": ["Workflow Configurations"],
  "SLA Configurations": ["SLA Configurations"],
  // Config Cockpit Sub-Items
  "Authoring": ["Authoring"],
  "Modelling": ["Modelling"],
  "Users": ["Users"],
  "Roles": ["Roles"],
  "Groups": ["Groups"],
};

const useModuleAccess = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const applicationConfig = useSelector((state) => state.applicationConfig);
  const { customError, log } = useLogger();

  const fetchModuleAccess = useCallback(() => {
    setLoading(true);
    setLoaderMessage(LOADING_MESSAGE.LOADING_MODULES);

    const isLocalEnv = ["localhost", "127.0.0.1"].includes(applicationConfig.environment);

    // Hardcoded API response to be removed later
    const hardcodedApiResponse = {
      "statusCode": 200,
      "data": {
        "Request Bench": {
          "Request Bench": true,
          "Manage Scheduler": true
        },
        "Config Cockpit": {
          "User Management": true,
          "Email Template Configurations": true,
          "Business Rules": true,
          "Broadcast Configurations": true,
          "Document Configurations": true,
          "Workflow Configurations": true
        },
        "Dashboard": {
          "Dashboard": true
        },
        "Home": {
          "Home": true
        },
        "Material": {
          "Extend with Upload": true,
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Extend": true,
          "Create with Upload": true
        },
        "Article": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Master Data": {
          "Retail": true,
          "Material Management": true,
          "Finance": true,
          "Vendor":true,
        },
        "Vendor":{
          "Vendor":true,
        },
        "Retail": {
          "Article": true,
        },
        "Material Management": {
          "Material": true,
          "BOM": true
        },
        "Finance": {
          "Cost Center": true,
          "Profit Center": true,
          "General Ledger": true,
          "Internal Order": true,
          "Cost Center Hierarchy": true,
          "Profit Center Hierarchy": true,
          "General Ledger Hierarchy": true,
          "Bank Key": true
        },
        "Cost Center": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Profit Center": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "General Ledger": {
          "Create": true,
          "Change": true,
          "Extend": true,
          "Display": true
        },
        "Internal Order": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Cost Center Hierarchy": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Profit Center Hierarchy": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "General Ledger Hierarchy": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Bank Key": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "BOM": {
          "Change with Upload": true,
          "Create": true,
          "Change": true,
          "Create with Upload": true
        },
        "Document Management": {
          "Document Management": true
        },
        "Workspace": {
          "My Tasks": true,
          "Completed Tasks": true,
          "Admin Completed Tasks": true,
          "Admin Tasks": true,
          "Substitution": true
        },
        "My Tasks": {
          "My Tasks": true
        },
        "Completed Tasks": {
          "Completed Tasks": true
        },
        "Admin Tasks": {
          "Admin Tasks": true
        },
        "Admin Completed Tasks": {
          "Admin Completed Tasks": true
        },
        "Substitution": {
          "Substitution": true
        },
        "Data Cleanse": {
          "Material Master": true,
          "Cost Center": true,
          "Profit Center": true,
          "Bank Key": true,
          "General Ledger": true
        },
        "Business Rules": {
          "Authoring": true,
          "Modelling": true
        },
        "User Management": {
          "Users": true,
          "Roles": true,
          "Groups": true
        },
        "Email Template Configurations": {
          "Email Template Configurations": true
        },
        "Broadcast Configurations": {
          "Broadcast Configurations": true
        },
        "Document Configurations": {
          "Document Configurations": true
        },
        "Workflow Configurations": {
          "Workflow Configurations": true
        },
        "Authoring": {
          "Authoring": true
        },
        "Modelling": {
          "Modelling": true
        },
        "Users": {
          "Users": true
        },
        "Roles": {
          "Roles": true
        },
        "Groups": {
          "Groups": true
        }
      }
    };

    if (isLocalEnv) {
      // Use fallback entities for localhost
      dispatch(setEntitiesAndActivities(fallbackEntities));
      setLoading(false);
      setLoaderMessage("");
    }
    const url = isLocalEnv ? `/${destination_MaterialMgmt}${END_POINTS.API.MODULE_FEATURE_ACCESS}` : `/${destination_IWA_NEW}${END_POINTS.API.MODULE_FEATURE_ACCESS_PROD}`;
    doAjax(
      url,
      "get",
      (res) => {
        if(res?.status === API_CODE?.STATUS_SUCCESS && Object.keys(res?.data)?.length > 0){
          const modules = res?.data;
          const cleaned = {};
          for (const moduleName in modules) {
            cleaned[moduleName] = Object.keys(modules[moduleName]).filter((feature) => modules[moduleName][feature] === true);
          }
          dispatch(setEntitiesAndActivities(cleaned));
        }else{
          dispatch(setEntitiesAndActivities(fallbackEntities));
        }
        if (!isLocalEnv) {
          setLoading(false);
          setLoaderMessage("");
        }
      },
      isLocalEnv
        ? () => {
          log(ERROR_MESSAGES?.ERROR_FETCHING_ROLES);
        }
        : (err) => {
          customError(ERROR_MESSAGES?.ERROR_FETCHING_ROLES, err);
          // Use fallback data even in non-local environments if API fails
          dispatch(setEntitiesAndActivities(fallbackEntities));
          setLoading(false);
          setLoaderMessage("");
        }
    );
  }, []);

  return {
    fetchModuleAccess,
    loading,
    loaderMessage,
  };
};

export default useModuleAccess;
