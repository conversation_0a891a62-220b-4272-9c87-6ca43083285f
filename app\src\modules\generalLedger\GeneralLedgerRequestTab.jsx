import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom"; 
import axios from "axios"; 
import RequestHeaderGl from "./RequestHeaderGl";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ArrowCircleLeftOutlined from "@mui/icons-material/ArrowCircleLeftOutlined";
import { setActiveStep } from "@app/redux/stepperSlice";
import RequestDetailsChangeGL from "./RequestDetailsChangeGL.jsx";
import ExcelOperationsCard from "../../components/Common/ExcelOperationsCard";
import { setRequestHeader } from "@app/requestDataSlice";
import CustomDialog from "@components/Common/ui/CustomDialog";
import {
  destination_DocumentManagement,
  destination_GeneralLedger,
  destination_GeneralLedger_Mass,
  destination_IDM,
  destination_MaterialMgmt,
  destination_ProfitCenter_Mass,
} from "../../destinationVariables";
import { doAjax } from "../../components/Common/fetchService";
import {
  transformApiResponseToReduxPayloadPc,
  fetchRegionBasedOnCountry,
  transformApiResponseToReduxPayloadGl,
  changePayloadForGL,
  createPayloadForGL
} from "../../functions";
import {
  setProfitCenterRows,
  setProfitCenterTabs,
  setSelectedRowId,
} from "../../app/redux/profitCenterTabSlice";
import { setProfitCenterApiData } from "../../app/redux/profitCenterTabSlice";
import { Step, StepButton, Stepper, IconButton, Button, DialogContent, DialogActions } from "@mui/material";
import { Box, Grid, Typography } from "@mui/material";
import PermIdentityOutlinedIcon from "@mui/icons-material/PermIdentityOutlined";
import { DIALOUGE_BOX_MESSAGES, ENABLE_STATUSES, LOCAL_STORAGE_KEYS, MODULE_MAP, REGION, REQUEST_STATUS, REQUEST_TYPE } from "@constant/enum";
import { APP_END_POINTS } from "@constant/appEndPoints";
import RequestDetailsGL from "./RequestDetailsGL";
import { setRequestHeaderPayloadData ,setdropdownDataForExtendedCode,setSelecteddropdownDataForExtendedCode,resetPayloadDataGL, setSelectedRowIdGL, setOpenDialog} from "@app/generalLedgerTabSlice";
import { clearGLrowBodyData, setCopyFromCompanyCode, setGLPayload, updateModuleFieldDataGL,setSelectedCopyFromCompanyCode } from "@app/generalLedgerTabSlice";
import useLang from "@hooks/useLang";
import { setCreatePayloadCopyForChangeLog } from "../../app/changeLogReducer";
import SummarizeOutlinedIcon from "@mui/icons-material/SummarizeOutlined";
import TrackChangesTwoToneIcon from "@mui/icons-material/TrackChangesTwoTone";
import FileUploadOutlinedIcon from "@mui/icons-material/FileUploadOutlined";
import ChangeLogGL from "@components/Changelog/ChangeLogGL";
import { clearChangeLogData } from "../../app/payloadslice";
import { clearCreateChangeLogDataGL, clearCreateTemplateArray } from "@app/changeLogReducer";
import { WarningOutlined } from "@mui/icons-material";
import { colors } from "@constant/colors";
import { usePDF } from "react-to-pdf";
import AttachmentsCommentsTab from "../../components/RequestBench/RequestPages/AttachmentsCommentsTab";
import { idGenerator } from "../../functions";
import FeedOutlinedIcon from "@mui/icons-material/FeedOutlined";
import PreviewPage from "@components/RequestBench/PreviewPage";
import { button_Outlined, button_Primary } from "../../components/Common/commonStyles.jsx"
import useDropdownFMDData from "../modulesHooks/useDropdownFMDData.js";
import { setDropDown as setDropDownAction, setOdataApiCall } from "@generalLedger/slice/generalLedgerDropDownSlice";
import { setLocalStorage,clearLocalStorageItem } from "@helper/helper.js";
import { setDependentDropdown, setDropDown } from "./slice/generalLedgerDropDownSlice";
import RequestDetailsExtendGL from "./RequestDetailsExtendGL";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "@constant/apiEndPoints";
import { ARTIFACTNAMES } from "@constant/enum.js";
import ErrorReportDialog from "@components/Common/ErrorReportDialog";


  

const steps = ["Request Header", "General Ledger List", "Attachments & Comments", "Preview"];

const GeneralLedgerRequestTab = () => {
  const {t} = useLang();
  const tabValue = useSelector((state) => state.CommonStepper.activeStep);
  const requestHeaderData = useSelector((state) => state.generalLedger.payload.requestHeaderData);
  const requestIdHeader = useSelector((state) => state.request.requestHeader?.requestId);
  const requestHeaderSlice = useSelector((state) => state.request.requestHeader);
  const {
      fetchedGeneralLedgerData,
      fetchReqBenchData,
      
    } = useSelector((state) => state.generalLedger);
  const initialPayload = useSelector((state) => state.request.requestHeader);
  
  const dispatch = useDispatch();
  const [isSecondTabEnabled, setIsSecondTabEnabled] = useState(false);
  const [addHardCodeData, setAddHardCodeData] = useState(false);
  const [isAttachmentTabEnabled, setIsAttachmentTabEnabled] = useState(false);
  const [downloadClicked, setDownloadClicked] = useState(false);
  const location = useLocation();
  const [apiResponses, setApiResponses] = useState([]);
  const [completed, setCompleted] = useState([]);
  const navigate = useNavigate();
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [isDialogVisible, setisDialogVisible] = useState(false);
  const [enableDocumentUpload, setEnableDocumentUpload] = useState(false);
  const [isGLPayloadSet, setIsGLPayloadSet] = useState(false);
  const [isChangeLogopen, setisChangeLogopen] = useState(false);
  const [attachmentsData, setAttachmentsData] = useState([]);
  const [glNumber, setGlNumber] = useState("");
  const [messageDialogMessage, setMessageDialogMessage] = useState();
  const [dialogOpen, setDialogOpen] = useState(false);
  const { customError } = useLogger()  
  
  const handleTabChange = (index) => {
    dispatch(setActiveStep(index));
  };

  const rowData = location.state;
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const RequestId = queryParams.get("RequestId");
  const RequestType = queryParams.get("RequestType");
  const allDropDownData = useSelector((state) => state.AllDropDown?.dropDown || {});
  const applicationConfig = useSelector((state) => state.applicationConfig);

  
  const handleDownload = () => {
    setDownloadClicked(true);
  }
    const reduxPayload = useSelector((state) => state.generalLedger.payload);
    const templateName = reduxPayload?.requestHeaderData?.TemplateName;
    const { toPDF, targetRef } = usePDF({ filename: 'my-component.pdf' });
  
  const { selectedRowId, tabs } = useSelector((state) => state.profitCenterTab);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  let task = useSelector((state) => state?.userManagement.taskData);
  const reqBench = (queryParams.get("reqBench") === "true" || (task && Object.keys(task).length > 0)) ? "true" : "false";
  const isChildRequest =
      rowData?.childRequestIds !== "Not Available" &&
      typeof task === "object" &&
      task !== null &&
      Object.keys(task).length === 0 &&
      reqBench === "true";



  const openChangeLog = () => {
    setisChangeLogopen(true);
  };

  const downloadErrorReport=()=>{

    let payloadData = ''

    if(RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD 
    || RequestType === REQUEST_TYPE.CREATE
    )
      payloadData={
      scenario: REQUEST_TYPE?.CREATE_WITH_UPLOAD,
    }
    if(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD 
    || RequestType === REQUEST_TYPE.CHANGE)
        payloadData=
    {
     
      scenario: REQUEST_TYPE?.CHANGE_WITH_UPLOAD,
    }
      const payloadDataChild = {
        requestId: requestId,
        isChild: rowData?.isChildRequest? "True": Object.keys(task).length ? "True" : "False"
      };
  
      const hSuccess = (response) => {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
        "download",
          `${
            payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
          }_Data Export.xlsx`
  
        link.href = href;
        link.setAttribute(
          "download",
          `${
             "Mass_Create"
          }_Data Export.xlsx`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
  
        setBlurLoading(false);
        setLoaderMessage("");
        setMessageDialogMessage(
          `${
            payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
          }_Data Export.xlsx has been exported successfully.`
        );
      };
  
      const hError = (error) => {
        customError(error)
      };
    
      doAjax(
        `/${destination_GeneralLedger_Mass}/${END_POINTS.ERROR_HISTORY.DOWNLOAD_ERROR_LOG_CHILD}`,
        "postandgetblob",
        hSuccess,
        hError,
        payloadDataChild
      )
      
    }
  

   const handleClosemodalData = (data) => {
    setisChangeLogopen(data);
  };


  const handleUploadGL = (file) => {
    let url = RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD ? "getAllGLFromExcelWithLimitedFields" : RequestType ===  REQUEST_TYPE.CHANGE_WITH_UPLOAD ? "getAllGLFromExcelWithLimitedFieldsForMassChange":'';

    setLoaderMessage("Initiating Excel Upload");
    setBlurLoading(true);
    const formData = new FormData();
    [...file].forEach((item) => formData.append("files", item));
    formData.append("dtName", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) ? "MDG_GL_FIELD_CONFIG" : "MDG_GL_CHANGE_TEMPLATE_DT");
    formData.append("version", (RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD || RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD) ? "v3" : "v4");
    formData.append("requestId", requestId ? requestId : "");
    formData.append("IsSunoco", "false");
    formData.append("screenName", RequestType ? RequestType : "");

    const hSuccess = (data) => {
      if (data.statusCode === 200) {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      } else {
        setEnableDocumentUpload(false);
        setBlurLoading(false);
        setLoaderMessage("");
        navigate(APP_END_POINTS?.REQUEST_BENCH);
      }
    };
    const hError = (error) => {
      setBlurLoading(false);
      setLoaderMessage("");
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    };

    doAjax(`/${destination_GeneralLedger_Mass}/massAction/${url}`, "postformdata", hSuccess, hError, formData);
  };


  const getCompanyCode = (coa,glID,CompanyCode,coCodetoExtend) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "CompanyCode", data: data.body }));
      let filteredCompanyCodeForExtend=data?.body?.filter(
      (item) => item.code !== CompanyCode)
        const selectedCodes = coCodetoExtend?.split(",");

        if(selectedCodes?.length > 0){

          const preselectedOptions = data?.body?.filter(option =>
            selectedCodes.includes(option.code)
          );
          dispatch(setSelecteddropdownDataForExtendedCode({
            uniqueId: glID, 
            data: preselectedOptions
          }));
        }

      dispatch(setdropdownDataForExtendedCode({
        uniqueId: glID, 
        data: filteredCompanyCodeForExtend
      }));
      
      
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCompanyCode?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getCopyFromCompanyCode = (coa, glID, glAccount) => {

    let payload = {
      glAccount: glAccount,
      chartOfAccount: coa
    }
    const hSuccess = (data) =>{
      let copyfromcompcodeArr=[]

      data?.body?.map((item)=>{
        let compcodehash={}
        compcodehash["code"]=item?.CompanyCode
        copyfromcompcodeArr?.push(compcodehash)
      })
     
      dispatch(setCopyFromCompanyCode({
        uniqueId: glID,
        data: copyfromcompcodeArr
      }));
    }

    const hError = (error) => {
      customError(error)
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}${END_POINTS.DATA.GET_COMPANY_CODE_EXTEND_TO_GL_ACCOUNT}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };


   const getSortKey = (glID) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "Sortkey",
            data: data.body || [],
            keyName2: glID,
          })
        )
      };
  
      const hError = (error) => {
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getSortKey`,
        "get",
        hSuccess,
        hError
      );
    };



  const getAccountType = (glID) => {
    const hSuccess = (data) => {
      dispatch(setDropDown({ keyName: "accountType", data: data.body }));
      dispatch(
        setDependentDropdown({
          keyName: "Accounttype",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccountCurrency = (compCode, glId) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountCurrency",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getTaxCategory = (compCode, glId) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Taxcategory",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getHouseBank = (compCode, glId) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "HouseBank",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getAccontId = (compCode, glId) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "AccountId",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
      "get",
      hSuccess,
      hError
    );
  };


  const getCostElementCategory = (accType, glId) => {

    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "CostEleCategory",
          data: data.body || [],
          keyName2: glId,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
      "get",
      hSuccess,
      hError
    );
  };

  const getreconAccountType = (compCode, glID) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "ReconAcc",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
      "get",
      hSuccess,
      hError
    );
  };

  const getPlanningLevel = (compCode, glID) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Planninglevel",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
      "get",
      hSuccess,
      hError
    );
  };

    const getFiledStatusGroup = (compCode, glID) => {
      const hSuccess = (data) => {
        dispatch(
          setDependentDropdown({
            keyName: "FieldStsGrp",
            data: data.body || [],
            keyName2: glID,
          })
        )
      };
  
      const hError = (error) => {
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
        "get",
        hSuccess,
        hError
      );
    };

  const getAccountGroup = (coa, glID) => {

    const hSuccess = (data) => {

      let accGrparr = []
      data?.body?.map((item) => {
        let hash = {}
        hash["code"] = item?.AccountGroup
        hash["desc"] = item?.Description
        hash["FromAcct"] = item?.FromAcct
        hash["ToAcct"] = item?.ToAcct
        accGrparr?.push(hash)
      })
      dispatch(
        setDependentDropdown({
          keyName: "AccountGroup",
          data: accGrparr || [],
          keyName2: glID,
        })
      )

      dispatch(setDropDown({ keyName: "accountGroup", data: accGrparr }));
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
      "get",
      hSuccess,
      hError
    );
  };

  const glPayLoad =
    RequestType === REQUEST_TYPE?.CREATE ||
    RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
      ? 
      createPayloadForGL(
      reduxPayload,
      requestHeaderSlice,
      requestId,
      task,
      dynamicData,
      '',
      ''
    ) : changePayloadForGL(initialPayload,
        initialPayload,
        task,
        reqBench,
        fetchReqBenchData,
        fetchedGeneralLedgerData);
 
  const payloadForPreviewDownloadExcel = {
    generalLedgerDetails: glPayLoad,
    dtName:
      RequestType === REQUEST_TYPE?.CREATE ||
      RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "MDG_GL_FIELD_CONFIG"
        : "MDG_CHANGE_TEMPLATE_DT",
    version:
      RequestType === REQUEST_TYPE?.CREATE ||
      RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD
        ? "v3"
        : "v3",
    requestId: requestId || "",
    scenario: RequestType === REQUEST_TYPE?.CREATE ||
      RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD ? REQUEST_TYPE?.CREATE_WITH_UPLOAD : REQUEST_TYPE?.CHANGE_WITH_UPLOAD,
    templateName: "",
    region: "US",
  };

  const getLanguage = (glID) => {
    const hSuccess = (data) => {
      dispatch(
        setDependentDropdown({
          keyName: "Language",
          data: data.body || [],
          keyName2: glID,
        })
      )
    };

    const hError = (error) => {
    };

    doAjax(
      `/${destination_GeneralLedger_Mass}/data/getLanguageKey`,
      "get",
      hSuccess,
      hError
    );
  };

  const getDisplayDataGL = async (requestId) => {

  const isChildPresent = rowData?.childRequestIds !== "Not Available";

  if (reqBench === "true") {
      const payload = {
        sort: "id,asc",
        parentId: !isChildPresent ? requestId : "",
        massCreationId:
          isChildPresent &&
          (RequestType === "Create" ||
            RequestType === "Mass Create" ||
            RequestType === REQUEST_TYPE?.CREATE_WITH_UPLOAD)
            ? requestId
            : "",
        massChangeId:
          isChildPresent &&
          (RequestType === "Change" ||
            RequestType === "Mass Change" ||
            RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD)
            ? requestId
            : "",
        massExtendId:
          isChildPresent &&
          (RequestType === REQUEST_TYPE?.EXTEND)
            ? requestId
            : "",
        page: 0,
        size: 10,
      };

    return new Promise((resolve, reject) => {
      const hSuccess = async (response) => {
        const apiResponse = response?.body || [];
        dispatch(setSelectedRowIdGL(apiResponse[0]?.GeneralLedgerID));
        if(apiResponse[0]?.GeneralLedgerID == null){

          dispatch(setOpenDialog(true));
        }else{
          dispatch(setOpenDialog(false));
        }

        
        const requestHeaderData = response?.body[0]?.Torequestheaderdata;
        const TotalIntermediateTasks = response?.body[0]?.TotalIntermediateTasks;

        dispatch(
          setRequestHeaderPayloadData({
            RequestId: requestHeaderData?.RequestId,
            RequestPrefix: requestHeaderData?.RequestPrefix,
            ReqCreatedBy: requestHeaderData?.ReqCreatedBy,
            ReqCreatedOn: requestHeaderData?.ReqCreatedOn,
            ReqUpdatedOn: requestHeaderData?.ReqUpdatedOn,
            RequestType: requestHeaderData?.RequestType,
            RequestDesc: requestHeaderData?.RequestDesc,
            RequestStatus: requestHeaderData?.RequestStatus,
            RequestPriority: requestHeaderData?.RequestPriority,
            FieldName: requestHeaderData?.FieldName,
            TemplateName: requestHeaderData?.TemplateName,
            Division: requestHeaderData?.Division,
            region: requestHeaderData?.region,
            leadingCat: requestHeaderData?.leadingCat,
            firstProd: requestHeaderData?.firstProd,
            launchDate: requestHeaderData?.launchDate,
            isBifurcated: requestHeaderData?.isBifurcated,
            screenName: requestHeaderData?.screenName,
            TotalIntermediateTasks: TotalIntermediateTasks,
          })
        );

        setApiResponses(apiResponse);


        const transformedPayload = transformApiResponseToReduxPayloadGl(apiResponse);
        const allPromises = apiResponse.map(async (item) => {
          const glID = item?.GeneralLedgerID;

          const calls = [];

          if (item?.COA) {
         
            calls.push(getCompanyCode(item?.COA, glID, item?.CompanyCode, item?.CoCodeToExtend));
            calls.push(getCopyFromCompanyCode(item?.COA, glID, item?.GLAccount));
            calls.push(getAccountType(glID));
            calls.push(getAccountGroup(item?.COA, glID));
            calls.push(getLanguage(glID));
            calls.push(getSortKey(glID));
          }

          if (item?.CompanyCode) {

            calls.push(getAccountCurrency(item?.CompanyCode, glID));
            calls.push(getTaxCategory(item?.CompanyCode, glID));
            calls.push(getHouseBank(item?.CompanyCode, glID));
            calls.push(getAccontId(item?.CompanyCode, glID));
            calls.push(getreconAccountType(item?.CompanyCode, glID));
            calls.push(getPlanningLevel(item?.CompanyCode, glID));
            calls.push(getFiledStatusGroup(item?.CompanyCode, glID));
            const preselectedOptionsforCopy = [{
              code:item?.CompanyCode
            }]
            dispatch(setSelectedCopyFromCompanyCode({
            uniqueId: glID, 
            data: preselectedOptionsforCopy
          }));
          }

          if (item?.Accounttype) {
            calls.push(getCostElementCategory(item?.Accounttype, glID));
          }

          return Promise.all(calls);
        });

        await Promise.all(allPromises); 


        setTimeout(()=>{
          dispatch(setGLPayload(transformedPayload?.payload));
        },2000)

        dispatch(setCreatePayloadCopyForChangeLog(transformedPayload?.payload));
        setIsGLPayloadSet(true)
        resolve();
      };

      const hError = (error) => {
        reject(error);
      };

      doAjax(
        `/${destination_GeneralLedger_Mass}/data/displayMassGeneralLedger`,
        "post",
        hSuccess,
        hError,
        payload
      );
    });
  }
};

 const getAttachmentsIDM = () => {
    const payload = {
      decisionTableId: null,
      decisionTableName: "MDG_ATTACHMENTS_LIST_DT",
      version: "v1",
      rulePolicy: null,
      validityDate: null,
      conditions: [
        {
          "MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE": "Material",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO": "Create",
          "MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE": 1,
        },
      ],
      systemFilters: null,
      systemOrders: null,
      filterString: null,
    };

    const hSuccess = (data) => {

      if (data?.statusCode === 200) {
        const attachmentList =
          data?.data?.result?.[0]?.MDG_ATTACHMENTS_ACTION_TYPE ?? [];
        const templateData = attachmentList.map((element, index) => ({
          id: index,
          attachmentName: element?.MDG_ATTACHMENTS_NAME,
          changeEntryFields: element?.MDG_ATTACH_CHNG_ENT_FIELDS,
        }));
        setAttachmentsData(attachmentList);
      } else {
      }
    };

    const hError = (error) => {
    };

    const url =
      applicationConfig.environment === "localhost"
        ? `/${destination_IDM}/rest/v1/invoke-rules`
        : `/${destination_IDM}/v1/invoke-rules`;

    doAjax(url, "post", hSuccess, hError, payload);
  };

   const exportExcel = () => {

    let payloadData = ''

    if(RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD 
    || RequestType === REQUEST_TYPE.CREATE
    )
      payloadData={
      dtName: "MDG_GL_FIELD_CONFIG",
      version: "v3",
      requestId: RequestId,
      scenario: REQUEST_TYPE?.CREATE_WITH_UPLOAD,
      isChild: rowData?.isChildRequest ?  true : isChildRequest ? true :false,
    }
    if(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD 
    || RequestType === REQUEST_TYPE.CHANGE){
        payloadData=
    {
      dtName: "MDG_GL_CHANGE_TEMPLATE_DT",
      version: "v3",
      requestId: RequestId,
      scenario: REQUEST_TYPE?.CHANGE_WITH_UPLOAD,
      isChild: rowData?.isChildRequest ? true : Object.keys(task).length ? true:false,
    }
    }


  
      const hSuccess = (response) => {
        const href = URL.createObjectURL(response);
        const link = document.createElement("a");
  
        link.href = href;
        link.setAttribute(
          "download",
          `${
            payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
          }_Data Export.xlsx`
        );
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(href);
  
        setBlurLoading(false);
        setLoaderMessage("");
        setMessageDialogMessage(
          `${
            payloadData?.scenario ? payloadData?.scenario : "Mass_Create"
          }_Data Export.xlsx has been exported successfully.`
        );
        
      };
  
      const hError = (error) => {
       customError(error);
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}${END_POINTS.EXCEL.DOWNLOAD_EXCEL_GL}`,
        "postandgetblob",
        hSuccess,
        hError,
        payloadData
      );
    };
  
  useEffect(() => {
    if (isSecondTabEnabled) {
      setCompleted([true]);
    }
  }, [isSecondTabEnabled]);
   useEffect(() => {
      setGlNumber(idGenerator("GL"));
    }, []);

   useEffect(() => {
      setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.GL)
      getAttachmentsIDM();
      dispatch(setDropDown({ keyName: "Region", data: REGION}));
    }, []);

  useEffect(() => {

  }, [reduxPayload]);

  useEffect(() => {
    const loadData = async () => {
      if (RequestId) {
        await getDisplayDataGL(RequestId);
        if (((RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD && !rowData?.length) || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && (rowData?.reqStatus === REQUEST_STATUS.DRAFT || rowData?.reqStatus === REQUEST_STATUS.UPLOAD_FAILED)) {
          dispatch(setActiveStep(0));
          setIsSecondTabEnabled(false);
          setIsAttachmentTabEnabled(false);
        } else {
          dispatch(setActiveStep(1));
          setIsSecondTabEnabled(true);
          setIsAttachmentTabEnabled(true);
        }

        setAddHardCodeData(true);
      } else {
        dispatch(setActiveStep(0));
      }
    };

    loadData();
    return () => {
      dispatch(clearChangeLogData());
      dispatch(resetPayloadDataGL())
      dispatch(setRequestHeader({}))
      dispatch(clearCreateTemplateArray());
      dispatch(clearCreateChangeLogDataGL());
      dispatch(clearGLrowBodyData());
      
      dispatch(setDropDown({ keyName: "FieldName", data: [] }))
    };
  }, [requestId, dispatch]);

  const handleYes = () => {
    if (requestId && !reqBench) {
      navigate(APP_END_POINTS?.MY_TASK);
    }
    else if (reqBench) {
      navigate(APP_END_POINTS?.REQUEST_BENCH);
    }
    else if (!requestId && !reqBench) {
      navigate(APP_END_POINTS?.MASTER_DATA_GL);
    }
  };

  const handleCancel = () => {
    setisDialogVisible(false)
  };

  return (
    <div>
       <ErrorReportDialog
          dialogState={dialogOpen}
          closeReusableDialog={() => setDialogOpen(false)}
          module={MODULE_MAP?.GL}
          isHierarchyCheck={false}
        />
      <Box sx={{ padding: 2 }}>
        <Grid
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {requestId || requestIdHeader  ? (
            <Box>
              <Typography
                variant="h6"
                sx={{
                  mb: 1,
                  textAlign: "left",
                  display: "flex",
                  alignItems: "center",
                  gap: 1,
                }}
              >
                <PermIdentityOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                {t("Request Header ID:")}{" "}
                <span>
                  {requestIdHeader
                    ? 
                      requestHeaderSlice?.requestId
                    : `${requestId}`}
                </span>
              </Typography>

              {templateName && (
                <Typography
                  variant="h6"
                  sx={{
                    mb: 1,
                    textAlign: "left",
                    display: "flex",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <FeedOutlinedIcon sx={{ fontSize: "1.5rem" }} />
                  Template Name: <span>{templateName}</span>
                </Typography>
              )}
            </Box>
          ) : (
            <div style={{ flex: 1 }} />
          )}
           {isChangeLogopen && (
            <ChangeLogGL
              module={MODULE_MAP.GL}
              open={true}
              closeModal={handleClosemodalData}
              requestId={ requestId}
              requestType={RequestType}
            />
          )}
          {/* {createChangeLogIsOpen && <CreateChangeLog open={true} closeModal={() => setCreateChangeLogIsOpen(false)} requestId={requestIdHeader || requestId} requestType={payloadData?.RequestType} />} */}
          {tabValue === 1 && (
            <Box
              sx={{ display: "flex", justifyContent: "flex-end", gap: "1rem" }}
            >
              <Button
                variant="outlined"
                size="small"
                title="Error Report"
                disabled={!RequestId}
                onClick={() => setDialogOpen(true)}
                color="primary"
              >
                <SummarizeOutlinedIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={openChangeLog}
                title="Change Log"
              >
                <TrackChangesTwoToneIcon sx={{ padding: "2px" }} />
              </Button>

              <Button
                variant="outlined"
                disabled={!RequestId}
                size="small"
                onClick={exportExcel}
                title="Export Excel"
              >
                <FileUploadOutlinedIcon sx={{ padding: "2px" }} />
              </Button>
            </Box>
          )}
        </Grid>
        <IconButton
          onClick={() => {
            if(reqBench && !ENABLE_STATUSES?.includes(reduxPayload?.requestHeaderData?.RequestStatus)) {
              navigate(APP_END_POINTS?.MASTER_DATA_GL);
              return;
            }
            setisDialogVisible(true)
          }}
          color="primary"
          aria-label="upload picture"
          component="label"
          sx={{left: "-10px",}}
          title="Back"
        >
          <ArrowCircleLeftOutlined
            sx={{ fontSize: "25px", color: "#000000" }}
          />
        </IconButton>

        <Stepper nonLinear activeStep={tabValue} sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          margin: "25px 14%",
          marginTop: "-35px"
        }}>
          {steps.map((label, index) => (
            <Step key={label} completed={completed[index]}>
              <StepButton color="error" disabled={
                      (index === 1 && !isSecondTabEnabled) ||
                      (index === 2 && !isAttachmentTabEnabled)
                    } onClick={() => handleTabChange(index)} sx={{ fontSize: "50px", fontWeight: "bold" }}>
                <span style={{ fontSize: "15px", fontWeight: "bold" }}>{t(label)}</span>
              </StepButton>
            </Step>
          ))}
        </Stepper>
        {tabValue === 0 && (
          <>
            <RequestHeaderGl apiResponse={apiResponses} reqBench={reqBench} downloadClicked={downloadClicked} setDownloadClicked={setDownloadClicked} setIsSecondTabEnabled={setIsSecondTabEnabled} setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}/>
            {(RequestType === REQUEST_TYPE.CHANGE_WITH_UPLOAD || RequestType === REQUEST_TYPE.CREATE_WITH_UPLOAD || RequestType === REQUEST_TYPE.EXTEND_WITH_UPLOAD) && ((rowData?.reqStatus == REQUEST_STATUS.DRAFT && !rowData?.material?.length) || rowData?.reqStatus == REQUEST_STATUS.UPLOAD_FAILED) && (
              <ExcelOperationsCard
                handleDownload={handleDownload}
                setEnableDocumentUpload={setEnableDocumentUpload}
                enableDocumentUpload={enableDocumentUpload}
                handleUploadMaterial={handleUploadGL}
              />
            )}
          </>)}
        {tabValue === 1 && 
          requestHeaderData.RequestType &&
          (requestHeaderData.RequestType === "Change" || requestHeaderData.RequestType === "Change with Upload" ? (
            <RequestDetailsChangeGL
            reqBench={reqBench} 
            requestId={requestId} 
            apiResponses={apiResponses} 
            setIsAttachmentTabEnabled={setIsAttachmentTabEnabled} 
            setCompleted={setCompleted} 
            downloadClicked={downloadClicked} 
            setDownloadClicked={setDownloadClicked}
            moduleName={MODULE_MAP.GL}
            isDisabled />
          ) : requestHeaderData.RequestType === "Extend" || requestHeaderData.RequestType === "Extend with Upload" ? (<RequestDetailsExtendGL
            reqBench={reqBench} 
            requestId={requestId} 
            apiResponses={apiResponses} 
            setIsAttachmentTabEnabled={true} 
            setCompleted={setCompleted} 
            downloadClicked={downloadClicked} 
            setDownloadClicked={setDownloadClicked} 
            moduleName={MODULE_MAP.GL}
            isDisabled={isChildRequest}/>)
          :
          <>
            <RequestDetailsGL 
                reqBench={reqBench}
                apiResponses={apiResponses}
                setCompleted={setCompleted}
                setIsAttachmentTabEnabled={setIsAttachmentTabEnabled}
                moduleName={MODULE_MAP.GL}
                isDisabled={isChildRequest}
            />
            </>
          )}
        {tabValue === 2 && (
          <AttachmentsCommentsTab
            requestStatus={
              rowData?.reqStatus
                ? rowData?.reqStatus
                : REQUEST_STATUS.ENABLE_FOR_FIRST_TIME
            }
            attachmentsData={attachmentsData}
            requestIdHeader={
              requestIdHeader
                ? requestIdHeader
                : requestId
            }
            pcNumber={glNumber}
            module={MODULE_MAP.GL}
            childRequestIds={rowData?.childRequestIds}
            artifactName={ARTIFACTNAMES.GL}
          />
        )}
        {tabValue === 3 && (
          <Box
            ref={targetRef}
            sx={{
              width: "100%",
              overflow: "auto",
            }}
          >
            <PreviewPage module={MODULE_MAP?.GL} 
            payloadForPreviewDownloadExcel={payloadForPreviewDownloadExcel} 
            
            />
          </Box>
        )}
      </Box>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
      {isDialogVisible && (
        <CustomDialog isOpen={isDialogVisible} titleIcon={<WarningOutlined size="small" sx={{ color: colors?.secondary?.amber, fontSize: "20px" }} />} Title={"Warning"} handleClose={handleCancel}>
          <DialogContent sx={{ mt: 2 }}>{DIALOUGE_BOX_MESSAGES.LEAVE_PAGE_MESSAGE}</DialogContent>
          <DialogActions>
            <Button variant="outlined" size="small" sx={{ ...button_Outlined }} onClick={handleCancel}>
              No
            </Button>
            <Button variant="contained" size="small" sx={{ ...button_Primary }} onClick={handleYes}>
              Yes
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </div>
  );
};

export default GeneralLedgerRequestTab;

              