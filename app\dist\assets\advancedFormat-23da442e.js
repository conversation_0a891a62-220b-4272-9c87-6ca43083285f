import{l,m as w}from"./index-226a1e75.js";var h={exports:{}};(function(c,m){(function(n,r){c.exports=r()})(l,function(){var n="week",r="year";return function(u,i,a){var e=i.prototype;e.week=function(t){if(t===void 0&&(t=null),t!==null)return this.add(7*(t-this.week()),"day");var o=this.$locale().yearStart||1;if(this.month()===11&&this.date()>25){var f=a(this).startOf(r).add(1,r).date(o),s=a(this).endOf(n);if(f.isBefore(s))return 1}var p=a(this).startOf(r).date(o).startOf(n).subtract(1,"millisecond"),d=this.diff(p,n,!0);return d<0?a(this).startOf("week").week():Math.ceil(d)},e.weeks=function(t){return t===void 0&&(t=null),this.week(t)}}})})(h);var v=h.exports;const $=w(v);var k={exports:{}};(function(c,m){(function(n,r){c.exports=r()})(l,function(){return function(n,r){var u=r.prototype,i=u.format;u.format=function(a){var e=this,t=this.$locale();if(!this.isValid())return i.bind(this)(a);var o=this.$utils(),f=(a||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(s){switch(s){case"Q":return Math.ceil((e.$M+1)/3);case"Do":return t.ordinal(e.$D);case"gggg":return e.weekYear();case"GGGG":return e.isoWeekYear();case"wo":return t.ordinal(e.week(),"W");case"w":case"ww":return o.s(e.week(),s==="w"?1:2,"0");case"W":case"WW":return o.s(e.isoWeek(),s==="W"?1:2,"0");case"k":case"kk":return o.s(String(e.$H===0?24:e.$H),s==="k"?1:2,"0");case"X":return Math.floor(e.$d.getTime()/1e3);case"x":return e.$d.getTime();case"z":return"["+e.offsetName()+"]";case"zzz":return"["+e.offsetName("long")+"]";default:return s}});return i.bind(this)(f)}}})})(k);var g=k.exports;const W=w(g);export{W as a,$ as w};
