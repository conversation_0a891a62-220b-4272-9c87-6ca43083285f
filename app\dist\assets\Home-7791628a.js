import{m as Xe,ei as I,t as ie,r as b,a as Ze,c as s,ai as Ve,Z as p,dr as Pt,aj as Je,B as h,j as r,d as v,bE as ye,ft as ce,a6 as Y,bS as me,F as se,dy as de,AX as ue,AY as St,AZ as Ct,an as be,A_ as kt,A$ as Et,yx as ve,B0 as _t,al as Ke,aP as It,gy as ze,B1 as At,O as ne,gd as We,B2 as Rt,ag as Ye,bs as Fe,B3 as Ue,B4 as ge,B5 as je,B6 as He,aa as Vt,a5 as Dt,B7 as Tt,f as Ot,L as Nt,D as Mt,B8 as Bt,gw as Lt,B9 as zt,AP as Wt,Ba as Yt,Bb as $e,Bc as Ft,Bd as Ut,Be as jt,Bf as Ht}from"./index-226a1e75.js";import{l as $t}from"./index-d550e3b0.js";var qt=function a(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var o,d,l;if(Array.isArray(e)){if(o=e.length,o!=t.length)return!1;for(d=o;d--!==0;)if(!a(e[d],t[d]))return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(l=Object.keys(e),o=l.length,o!==Object.keys(t).length)return!1;for(d=o;d--!==0;)if(!Object.prototype.hasOwnProperty.call(t,l[d]))return!1;for(d=o;d--!==0;){var m=l[d];if(!a(e[m],t[m]))return!1}return!0}return e!==e&&t!==t};const Gt=Xe(qt);var xe={exports:{}},et;/**
* @link https://github.com/gajus/sister for the canonical source repository
* @license https://github.com/gajus/sister/blob/master/LICENSE BSD 3-Clause
*/et=function(){var a={},e={};return a.on=function(t,o){var d={name:t,handler:o};return e[t]=e[t]||[],e[t].unshift(d),d},a.off=function(t){var o=e[t.name].indexOf(t);o!==-1&&e[t.name].splice(o,1)},a.trigger=function(t,o){var d=e[t],l;if(d)for(l=d.length;l--;)d[l].handler(o)},a};var Qt=et,we={exports:{}};(function(a,e){Object.defineProperty(e,"__esModule",{value:!0});var t=$t,o=d(t);function d(l){return l&&l.__esModule?l:{default:l}}e.default=function(l){var m=new Promise(function(P){if(window.YT&&window.YT.Player&&window.YT.Player instanceof Function){P(window.YT);return}else{var f=window.location.protocol==="http:"?"http:":"https:";(0,o.default)(f+"//www.youtube.com/iframe_api",function(i){i&&l.trigger("error",i)})}var c=window.onYouTubeIframeAPIReady;window.onYouTubeIframeAPIReady=function(){c&&c(),P(window.YT)}});return m},a.exports=e.default})(we,we.exports);var Xt=we.exports,Pe={exports:{}},Se={exports:{}},Ce={exports:{}},Q=1e3,X=Q*60,Z=X*60,J=Z*24,Zt=J*365.25,Jt=function(a,e){e=e||{};var t=typeof a;if(t==="string"&&a.length>0)return Kt(a);if(t==="number"&&isNaN(a)===!1)return e.long?tr(a):er(a);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(a))};function Kt(a){if(a=String(a),!(a.length>100)){var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(a);if(e){var t=parseFloat(e[1]),o=(e[2]||"ms").toLowerCase();switch(o){case"years":case"year":case"yrs":case"yr":case"y":return t*Zt;case"days":case"day":case"d":return t*J;case"hours":case"hour":case"hrs":case"hr":case"h":return t*Z;case"minutes":case"minute":case"mins":case"min":case"m":return t*X;case"seconds":case"second":case"secs":case"sec":case"s":return t*Q;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function er(a){return a>=J?Math.round(a/J)+"d":a>=Z?Math.round(a/Z)+"h":a>=X?Math.round(a/X)+"m":a>=Q?Math.round(a/Q)+"s":a+"ms"}function tr(a){return oe(a,J,"day")||oe(a,Z,"hour")||oe(a,X,"minute")||oe(a,Q,"second")||a+" ms"}function oe(a,e,t){if(!(a<e))return a<e*1.5?Math.floor(a/e)+" "+t:Math.ceil(a/e)+" "+t+"s"}(function(a,e){e=a.exports=d.debug=d.default=d,e.coerce=f,e.disable=m,e.enable=l,e.enabled=P,e.humanize=Jt,e.names=[],e.skips=[],e.formatters={};var t;function o(c){var i=0,u;for(u in c)i=(i<<5)-i+c.charCodeAt(u),i|=0;return e.colors[Math.abs(i)%e.colors.length]}function d(c){function i(){if(i.enabled){var u=i,x=+new Date,w=x-(t||x);u.diff=w,u.prev=t,u.curr=x,t=x;for(var y=new Array(arguments.length),k=0;k<y.length;k++)y[k]=arguments[k];y[0]=e.coerce(y[0]),typeof y[0]!="string"&&y.unshift("%O");var C=0;y[0]=y[0].replace(/%([a-zA-Z%])/g,function(A,V){if(A==="%%")return A;C++;var _=e.formatters[V];if(typeof _=="function"){var L=y[C];A=_.call(u,L),y.splice(C,1),C--}return A}),e.formatArgs.call(u,y);var E=i.log||e.log||console.log.bind(console);E.apply(u,y)}}return i.namespace=c,i.enabled=e.enabled(c),i.useColors=e.useColors(),i.color=o(c),typeof e.init=="function"&&e.init(i),i}function l(c){e.save(c),e.names=[],e.skips=[];for(var i=(typeof c=="string"?c:"").split(/[\s,]+/),u=i.length,x=0;x<u;x++)i[x]&&(c=i[x].replace(/\*/g,".*?"),c[0]==="-"?e.skips.push(new RegExp("^"+c.substr(1)+"$")):e.names.push(new RegExp("^"+c+"$")))}function m(){e.enable("")}function P(c){var i,u;for(i=0,u=e.skips.length;i<u;i++)if(e.skips[i].test(c))return!1;for(i=0,u=e.names.length;i<u;i++)if(e.names[i].test(c))return!0;return!1}function f(c){return c instanceof Error?c.stack||c.message:c}})(Ce,Ce.exports);var rr=Ce.exports;(function(a,e){e=a.exports=rr,e.log=d,e.formatArgs=o,e.save=l,e.load=m,e.useColors=t,e.storage=typeof chrome<"u"&&typeof chrome.storage<"u"?chrome.storage.local:P(),e.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"];function t(){return typeof window<"u"&&window.process&&window.process.type==="renderer"?!0:typeof document<"u"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window<"u"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}e.formatters.j=function(f){try{return JSON.stringify(f)}catch(c){return"[UnexpectedJSONParseError]: "+c.message}};function o(f){var c=this.useColors;if(f[0]=(c?"%c":"")+this.namespace+(c?" %c":" ")+f[0]+(c?"%c ":" ")+"+"+e.humanize(this.diff),!!c){var i="color: "+this.color;f.splice(1,0,i,"color: inherit");var u=0,x=0;f[0].replace(/%[a-zA-Z%]/g,function(w){w!=="%%"&&(u++,w==="%c"&&(x=u))}),f.splice(x,0,i)}}function d(){return typeof console=="object"&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function l(f){try{f==null?e.storage.removeItem("debug"):e.storage.debug=f}catch{}}function m(){var f;try{f=e.storage.debug}catch{}return!f&&typeof process<"u"&&"env"in process&&(f={}.DEBUG),f}e.enable(m());function P(){try{return window.localStorage}catch{}}})(Se,Se.exports);var ar=Se.exports,ke={exports:{}};(function(a,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=["cueVideoById","loadVideoById","cueVideoByUrl","loadVideoByUrl","playVideo","pauseVideo","stopVideo","getVideoLoadedFraction","cuePlaylist","loadPlaylist","nextVideo","previousVideo","playVideoAt","setShuffle","setLoop","getPlaylist","getPlaylistIndex","setOption","mute","unMute","isMuted","setVolume","getVolume","seekTo","getPlayerState","getPlaybackRate","setPlaybackRate","getAvailablePlaybackRates","getPlaybackQuality","setPlaybackQuality","getAvailableQualityLevels","getCurrentTime","getDuration","removeEventListener","getVideoUrl","getVideoEmbedCode","getOptions","getOption","addEventListener","destroy","setSize","getIframe"],a.exports=e.default})(ke,ke.exports);var nr=ke.exports,Ee={exports:{}};(function(a,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=["ready","stateChange","playbackQualityChange","playbackRateChange","error","apiChange","volumeChange"],a.exports=e.default})(Ee,Ee.exports);var or=Ee.exports,_e={exports:{}},Ie={exports:{}};(function(a,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default={BUFFERING:3,ENDED:0,PAUSED:2,PLAYING:1,UNSTARTED:-1,VIDEO_CUED:5},a.exports=e.default})(Ie,Ie.exports);var ir=Ie.exports;(function(a,e){Object.defineProperty(e,"__esModule",{value:!0});var t=ir,o=d(t);function d(l){return l&&l.__esModule?l:{default:l}}e.default={pauseVideo:{acceptableStates:[o.default.ENDED,o.default.PAUSED],stateChangeRequired:!1},playVideo:{acceptableStates:[o.default.ENDED,o.default.PLAYING],stateChangeRequired:!1},seekTo:{acceptableStates:[o.default.ENDED,o.default.PLAYING,o.default.PAUSED],stateChangeRequired:!0,timeout:3e3}},a.exports=e.default})(_e,_e.exports);var sr=_e.exports;(function(a,e){Object.defineProperty(e,"__esModule",{value:!0});var t=ar,o=i(t),d=nr,l=i(d),m=or,P=i(m),f=sr,c=i(f);function i(w){return w&&w.__esModule?w:{default:w}}var u=(0,o.default)("youtube-player"),x={};x.proxyEvents=function(w){var y={},k=function(F){var R="on"+F.slice(0,1).toUpperCase()+F.slice(1);y[R]=function(D){u('event "%s"',R,D),w.trigger(F,D)}},C=!0,E=!1,A=void 0;try{for(var V=P.default[Symbol.iterator](),_;!(C=(_=V.next()).done);C=!0){var L=_.value;k(L)}}catch(z){E=!0,A=z}finally{try{!C&&V.return&&V.return()}finally{if(E)throw A}}return y},x.promisifyPlayer=function(w){var y=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,k={},C=function(R){y&&c.default[R]?k[R]=function(){for(var D=arguments.length,U=Array(D),M=0;M<D;M++)U[M]=arguments[M];return w.then(function(T){var O=c.default[R],K=T.getPlayerState(),ee=T[R].apply(T,U);return O.stateChangeRequired||Array.isArray(O.acceptableStates)&&O.acceptableStates.indexOf(K)===-1?new Promise(function(j){var te=function B(){var re=T.getPlayerState(),ae=void 0;typeof O.timeout=="number"&&(ae=setTimeout(function(){T.removeEventListener("onStateChange",B),j()},O.timeout)),Array.isArray(O.acceptableStates)&&O.acceptableStates.indexOf(re)!==-1&&(T.removeEventListener("onStateChange",B),clearTimeout(ae),j())};T.addEventListener("onStateChange",te)}).then(function(){return ee}):ee})}:k[R]=function(){for(var D=arguments.length,U=Array(D),M=0;M<D;M++)U[M]=arguments[M];return w.then(function(T){return T[R].apply(T,U)})}},E=!0,A=!1,V=void 0;try{for(var _=l.default[Symbol.iterator](),L;!(E=(L=_.next()).done);E=!0){var z=L.value;C(z)}}catch(F){A=!0,V=F}finally{try{!E&&_.return&&_.return()}finally{if(A)throw V}}return k},e.default=x,a.exports=e.default})(Pe,Pe.exports);var lr=Pe.exports;(function(a,e){Object.defineProperty(e,"__esModule",{value:!0});var t=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(u){return typeof u}:function(u){return u&&typeof Symbol=="function"&&u.constructor===Symbol&&u!==Symbol.prototype?"symbol":typeof u},o=Qt,d=c(o),l=Xt,m=c(l),P=lr,f=c(P);function c(u){return u&&u.__esModule?u:{default:u}}var i=void 0;e.default=function(u){var x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},w=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,y=(0,d.default)();if(i||(i=(0,m.default)(y)),x.events)throw new Error("Event handlers cannot be overwritten.");if(typeof u=="string"&&!document.getElementById(u))throw new Error('Element "'+u+'" does not exist.');x.events=f.default.proxyEvents(y);var k=new Promise(function(E){if((typeof u>"u"?"undefined":t(u))==="object"&&u.playVideo instanceof Function){var A=u;E(A)}else i.then(function(V){var _=new V.Player(u,x);return y.on("ready",function(){E(_)}),null})}),C=f.default.promisifyPlayer(k,w);return C.on=y.on,C.off=y.off,C},a.exports=e.default})(xe,xe.exports);var cr=xe.exports;const dr=Xe(cr);var ur=Object.defineProperty,hr=Object.defineProperties,fr=Object.getOwnPropertyDescriptors,qe=Object.getOwnPropertySymbols,pr=Object.prototype.hasOwnProperty,gr=Object.prototype.propertyIsEnumerable,Ge=(a,e,t)=>e in a?ur(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t,Ae=(a,e)=>{for(var t in e||(e={}))pr.call(e,t)&&Ge(a,t,e[t]);if(qe)for(var t of qe(e))gr.call(e,t)&&Ge(a,t,e[t]);return a},Re=(a,e)=>hr(a,fr(e)),yr=(a,e,t)=>new Promise((o,d)=>{var l=f=>{try{P(t.next(f))}catch(c){d(c)}},m=f=>{try{P(t.throw(f))}catch(c){d(c)}},P=f=>f.done?o(f.value):Promise.resolve(f.value).then(l,m);P((t=t.apply(a,e)).next())});function mr(a,e){var t,o;if(a.videoId!==e.videoId)return!0;const d=((t=a.opts)==null?void 0:t.playerVars)||{},l=((o=e.opts)==null?void 0:o.playerVars)||{};return d.start!==l.start||d.end!==l.end}function Qe(a={}){return Re(Ae({},a),{height:0,width:0,playerVars:Re(Ae({},a.playerVars),{autoplay:0,start:0,end:0})})}function br(a,e){return a.videoId!==e.videoId||!Gt(Qe(a.opts),Qe(e.opts))}function vr(a,e){var t,o,d,l;return a.id!==e.id||a.className!==e.className||((t=a.opts)==null?void 0:t.width)!==((o=e.opts)==null?void 0:o.width)||((d=a.opts)==null?void 0:d.height)!==((l=e.opts)==null?void 0:l.height)||a.iframeClassName!==e.iframeClassName||a.title!==e.title}var xr={videoId:"",id:"",className:"",iframeClassName:"",style:{},title:"",loading:void 0,opts:{},onReady:()=>{},onError:()=>{},onPlay:()=>{},onPause:()=>{},onEnd:()=>{},onStateChange:()=>{},onPlaybackRateChange:()=>{},onPlaybackQualityChange:()=>{}},wr={videoId:I.string,id:I.string,className:I.string,iframeClassName:I.string,style:I.object,title:I.string,loading:I.oneOf(["lazy","eager"]),opts:I.objectOf(I.any),onReady:I.func,onError:I.func,onPlay:I.func,onPause:I.func,onEnd:I.func,onStateChange:I.func,onPlaybackRateChange:I.func,onPlaybackQualityChange:I.func},le=class extends ie.Component{constructor(a){super(a),this.destroyPlayerPromise=void 0,this.onPlayerReady=e=>{var t,o;return(o=(t=this.props).onReady)==null?void 0:o.call(t,e)},this.onPlayerError=e=>{var t,o;return(o=(t=this.props).onError)==null?void 0:o.call(t,e)},this.onPlayerStateChange=e=>{var t,o,d,l,m,P,f,c;switch((o=(t=this.props).onStateChange)==null||o.call(t,e),e.data){case le.PlayerState.ENDED:(l=(d=this.props).onEnd)==null||l.call(d,e);break;case le.PlayerState.PLAYING:(P=(m=this.props).onPlay)==null||P.call(m,e);break;case le.PlayerState.PAUSED:(c=(f=this.props).onPause)==null||c.call(f,e);break}},this.onPlayerPlaybackRateChange=e=>{var t,o;return(o=(t=this.props).onPlaybackRateChange)==null?void 0:o.call(t,e)},this.onPlayerPlaybackQualityChange=e=>{var t,o;return(o=(t=this.props).onPlaybackQualityChange)==null?void 0:o.call(t,e)},this.destroyPlayer=()=>this.internalPlayer?(this.destroyPlayerPromise=this.internalPlayer.destroy().then(()=>this.destroyPlayerPromise=void 0),this.destroyPlayerPromise):Promise.resolve(),this.createPlayer=()=>{if(typeof document>"u")return;if(this.destroyPlayerPromise){this.destroyPlayerPromise.then(this.createPlayer);return}const e=Re(Ae({},this.props.opts),{videoId:this.props.videoId});this.internalPlayer=dr(this.container,e),this.internalPlayer.on("ready",this.onPlayerReady),this.internalPlayer.on("error",this.onPlayerError),this.internalPlayer.on("stateChange",this.onPlayerStateChange),this.internalPlayer.on("playbackRateChange",this.onPlayerPlaybackRateChange),this.internalPlayer.on("playbackQualityChange",this.onPlayerPlaybackQualityChange),(this.props.title||this.props.loading)&&this.internalPlayer.getIframe().then(t=>{this.props.title&&t.setAttribute("title",this.props.title),this.props.loading&&t.setAttribute("loading",this.props.loading)})},this.resetPlayer=()=>this.destroyPlayer().then(this.createPlayer),this.updatePlayer=()=>{var e;(e=this.internalPlayer)==null||e.getIframe().then(t=>{this.props.id?t.setAttribute("id",this.props.id):t.removeAttribute("id"),this.props.iframeClassName?t.setAttribute("class",this.props.iframeClassName):t.removeAttribute("class"),this.props.opts&&this.props.opts.width?t.setAttribute("width",this.props.opts.width.toString()):t.removeAttribute("width"),this.props.opts&&this.props.opts.height?t.setAttribute("height",this.props.opts.height.toString()):t.removeAttribute("height"),this.props.title?t.setAttribute("title",this.props.title):t.setAttribute("title","YouTube video player"),this.props.loading?t.setAttribute("loading",this.props.loading):t.removeAttribute("loading")})},this.getInternalPlayer=()=>this.internalPlayer,this.updateVideo=()=>{var e,t,o,d;if(typeof this.props.videoId>"u"||this.props.videoId===null){(e=this.internalPlayer)==null||e.stopVideo();return}let l=!1;const m={videoId:this.props.videoId};if((t=this.props.opts)!=null&&t.playerVars&&(l=this.props.opts.playerVars.autoplay===1,"start"in this.props.opts.playerVars&&(m.startSeconds=this.props.opts.playerVars.start),"end"in this.props.opts.playerVars&&(m.endSeconds=this.props.opts.playerVars.end)),l){(o=this.internalPlayer)==null||o.loadVideoById(m);return}(d=this.internalPlayer)==null||d.cueVideoById(m)},this.refContainer=e=>{this.container=e},this.container=null,this.internalPlayer=null}componentDidMount(){this.createPlayer()}componentDidUpdate(a){return yr(this,null,function*(){vr(a,this.props)&&this.updatePlayer(),br(a,this.props)&&(yield this.resetPlayer()),mr(a,this.props)&&this.updateVideo()})}componentWillUnmount(){this.destroyPlayer()}render(){return ie.createElement("div",{className:this.props.className,style:this.props.style},ie.createElement("div",{id:this.props.id,className:this.props.iframeClassName,ref:this.refContainer}))}},he=le;he.propTypes=wr;he.defaultProps=xr;he.PlayerState={UNSTARTED:-1,ENDED:0,PLAYING:1,PAUSED:2,BUFFERING:3,CUED:5};var Pr=he;const Sr=({open:a,onClose:e,media:t,type:o})=>r(Ve,{open:a,onClose:e,maxWidth:"lg",fullWidth:!0,PaperProps:{sx:{backgroundColor:p.announcement.mediaViewerBackground,borderRadius:2,maxHeight:"90vh"}},children:s(h,{sx:{position:"relative",p:2},children:[r(Y,{onClick:e,sx:{position:"absolute",right:8,top:8,color:p.basic.white,zIndex:1,backgroundColor:p.announcement.mediaOverlayBackground,"&:hover":{backgroundColor:p.announcement.mediaOverlayHover}},children:r(ce,{})}),o==="image"?r("img",{src:t,alt:"Enlarged view",style:{width:"100%",height:"auto",maxHeight:"80vh",objectFit:"contain",borderRadius:8}}):r("video",{src:t,controls:!0,autoPlay:!0,style:{width:"100%",height:"auto",maxHeight:"80vh",borderRadius:8}})]})}),Cr=({open:a,onClose:e,announcement:t})=>{var x,w;const[o,d]=b.useState({open:!1,media:null,type:null}),{t:l}=Ze();if(!t)return null;const m=(y,k)=>{d({open:!0,media:y,type:k})},P=y=>{window.open(y,"_blank","noopener,noreferrer")},f=y=>new Date(y).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),c=t.fileName&&((x=t.fileType)==null?void 0:x.startsWith("image/")),i=t.fileName&&((w=t.fileType)==null?void 0:w.startsWith("video/")),u=t.externalUrl&&t.externalUrl.trim()!=="";return s(se,{children:[s(Ve,{open:a,onClose:e,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:3,background:p.announcement.dialogBackground,maxHeight:"100vh",width:"40%",border:`2px solid ${p.announcement.dialogBorder}`}},TransitionComponent:Pt,TransitionProps:{timeout:300},children:[s(Je,{sx:{color:p.basic.white,position:"relative",pb:3},children:[s(h,{sx:{pr:6},children:[r(v,{variant:"h3",sx:{fontWeight:700,mb:1,color:p.announcement.headerText},children:t.broadcastTitle}),s(h,{sx:{display:"flex",alignItems:"center",gap:2,flexWrap:"wrap",mt:2},children:[r(ye,{label:t.module,size:"small",sx:{background:p.announcement.chipBackground,color:p.basic.white,fontWeight:600}}),r(ye,{label:t.status,size:"small",sx:{background:t.status==="Active"?p.announcement.chipActive:p.announcement.chipInactive,color:p.basic.white,fontWeight:600}})]})]}),r(Y,{onClick:e,sx:{position:"absolute",right:8,top:8,color:p.basic.white,background:p.announcement.headerBackground,"&:hover":{background:p.announcement.headerHover}},children:r(ce,{})})]}),r(me,{sx:{mx:3}}),r(Ke,{sx:{p:0},children:s(h,{sx:{p:3},children:[r(v,{variant:"h6",sx:{mb:2,fontWeight:600,color:p.announcement.textPrimary},children:l("Description")}),r(v,{variant:"body1",sx:{lineHeight:1.7,color:p.announcement.textSecondary,fontSize:"1rem",mb:3,p:2,backgroundColor:p.announcement.descriptionBackground,borderRadius:2,border:`1px solid ${p.announcement.descriptionBorder}`},children:t.description}),(c||i)&&s(se,{children:[r(v,{variant:"h6",sx:{mb:2,fontWeight:600,color:p.announcement.textPrimary},children:l("Media Attachments")}),s(h,{sx:{display:"flex",gap:2,mb:3,flexWrap:"wrap"},children:[c&&r(de,{sx:{cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"scale(1.05)",boxShadow:p.announcement.cardShadow},borderRadius:2,overflow:"hidden",position:"relative"},onClick:()=>m(`/api/files/${t.fileName}`,"image"),children:s(h,{sx:{position:"relative",width:200,height:150},children:[r(ue,{component:"div",sx:{height:"100%",background:p.announcement.mediaGradient,display:"flex",alignItems:"center",justifyContent:"center"},children:r(St,{sx:{fontSize:40,color:p.basic.white}})}),r(h,{sx:{position:"absolute",bottom:0,left:0,right:0,background:p.announcement.mediaOverlayGradient,color:p.basic.white,p:1,textAlign:"center"},children:r(v,{variant:"caption",sx:{fontWeight:600},children:t.fileName})})]})}),i&&r(de,{sx:{cursor:"pointer",transition:"all 0.3s ease","&:hover":{transform:"scale(1.05)",boxShadow:p.announcement.cardShadow},borderRadius:2,overflow:"hidden",position:"relative"},onClick:()=>m(`/api/files/${t.fileName}`,"video"),children:s(h,{sx:{position:"relative",width:200,height:150},children:[r(ue,{component:"div",sx:{height:"100%",background:p.announcement.mediaGradient,display:"flex",alignItems:"center",justifyContent:"center"},children:r(Ct,{sx:{fontSize:50,color:p.basic.white}})}),r(h,{sx:{position:"absolute",bottom:0,left:0,right:0,background:p.announcement.mediaOverlayGradient,color:p.basic.white,p:1,textAlign:"center"},children:r(v,{variant:"caption",sx:{fontWeight:600},children:t.fileName})})]})})]})]}),u&&s(se,{children:[r(v,{variant:"h6",sx:{mb:2,fontWeight:600,color:p.announcement.textPrimary},children:l("External Link")}),r(be,{variant:"outlined",startIcon:r(kt,{}),endIcon:r(Et,{}),onClick:()=>P(t.externalUrl),sx:{mb:3,borderColor:p.announcement.linkBorder,color:p.announcement.linkColor,"&:hover":{borderColor:p.announcement.linkBorderHover,backgroundColor:p.announcement.linkBackgroundHover},borderRadius:2,textTransform:"none",fontSize:"0.95rem"},children:l("Open External Link")})]}),r(me,{sx:{my:3}}),s(h,{sx:{display:"flex",flexDirection:"column",gap:2},children:[s(h,{sx:{display:"flex",alignItems:"center",gap:1},children:[r(ve,{sx:{fontSize:18,color:p.announcement.metaIconColor}}),s(v,{variant:"body2",color:"text.secondary",children:[s("strong",{children:[l("Start Date"),":"]})," ",f(t.startDate)]})]}),s(h,{sx:{display:"flex",alignItems:"center",gap:1},children:[r(ve,{sx:{fontSize:18,color:p.announcement.metaIconColor}}),s(v,{variant:"body2",color:"text.secondary",children:[s("strong",{children:[l("End Date"),":"]})," ",f(t.endDate)]})]}),t.createdBy&&s(h,{sx:{display:"flex",alignItems:"center",gap:1},children:[r(_t,{sx:{fontSize:18,color:p.announcement.metaIconColor}}),s(v,{variant:"body2",color:"text.secondary",children:[s("strong",{children:[l("Created by"),":"]})," ",t.createdBy]})]})]})]})})]}),r(Sr,{open:o.open,onClose:()=>d({open:!1,media:null,type:null}),media:o.media,type:o.type})]})},Ar=()=>{const[a,e]=b.useState(0),[t,o]=b.useState(null),[d,l]=b.useState(!1),[m,P]=b.useState(""),[f,c]=b.useState(0),[i,u]=b.useState(null),[x,w]=b.useState(!1),[y,k]=b.useState(null),[C,E]=b.useState(!1),[A,V]=b.useState(0),[_,L]=b.useState(0),[z,F]=b.useState(50),[R,D]=b.useState(!1),[U,M]=b.useState(1),[T,O]=b.useState(!0),[K,ee]=b.useState(!1),[j,te]=b.useState([]),[B,re]=b.useState([]),[ae,tt]=b.useState({}),[rt,De]=b.useState(!1),S=b.useRef(null),fe=b.useRef(null),q=b.useRef(null),{customError:at,warn:H}=It(),{t:N}=Ze(),$=Yt;b.useEffect(()=>{j.length===0&&nt()},[]),b.useEffect(()=>{B.length===0&&ot()},[]);const nt=async()=>{try{const n=await fetch(`${ze}/api/videos/all`);if(!n.ok)throw new Error("Failed to fetch videos");const g=await n.json();te(g)}catch{te([])}},ot=async()=>{try{const n=await fetch(`${ze}/broadcastManagement/getAll/category/Announcements`);if(!n.ok)throw new Error("Failed to fetch announcements");const g=await n.json();re(g==null?void 0:g.broadcastDetailsDtoList)}catch{re([])}},pe=j.slice(0,4),Te=j.filter(n=>n.title.toLowerCase().includes(m.toLowerCase())||n.description.toLowerCase().includes(m.toLowerCase())||n.category.toLowerCase().includes(m.toLowerCase())),it={height:"100%",width:"100%",playerVars:{autoplay:1,controls:0,disablekb:1,modestbranding:1,rel:0,showinfo:0,iv_load_policy:3,cc_load_policy:0,fs:0,playsinline:1,origin:window.location.origin}},st=n=>{S.current=n.target,n.target.setVolume(z),R&&n.target.mute(),Oe()},lt=n=>{const{YT:g}=window;if(g)switch(n.data){case g.PlayerState.PLAYING:E(!0),Oe();break;case g.PlayerState.PAUSED:E(!1),G();break;case g.PlayerState.ENDED:E(!1),G();break}},ct=n=>{at("YouTube Player Error:",n.data)},Oe=b.useCallback(()=>{q.current&&cancelAnimationFrame(q.current);const n=()=>{if(S.current&&typeof S.current.getCurrentTime=="function")try{const g=S.current.getCurrentTime(),W=S.current.getDuration();!isNaN(g)&&!isNaN(W)&&(V(g),L(W))}catch(g){H("Error updating progress:",g)}C&&S.current&&(q.current=requestAnimationFrame(n))};q.current=requestAnimationFrame(n)},[C]),G=b.useCallback(()=>{q.current&&cancelAnimationFrame(q.current)},[]),dt=()=>{if(S.current)try{C?S.current.pauseVideo():S.current.playVideo()}catch(n){H("Error toggling play:",n)}},ut=n=>{if(S.current)try{const W=n.currentTarget.getBoundingClientRect(),Le=(n.clientX-W.left)/W.width*_;!isNaN(Le)&&_>0&&S.current.seekTo(Le,!0)}catch(g){H("Error seeking:",g)}},ht=(n,g)=>{if(F(g),S.current)try{S.current.setVolume(g),g>0&&R&&(S.current.unMute(),D(!1))}catch(W){H("Error setting volume:",W)}},ft=()=>{if(S.current)try{R?(S.current.unMute(),D(!1)):(S.current.mute(),D(!0))}catch(n){H("Error toggling mute:",n)}},pt=n=>{if(M(n),S.current)try{S.current.setPlaybackRate(n)}catch(g){H("Error setting playback rate:",g)}},gt=()=>{ee(!K)},yt=()=>{O(!0),fe.current&&clearTimeout(fe.current),fe.current=setTimeout(()=>{O(!1)},3e3)},Ne=n=>{const g=Math.floor(n/60),W=Math.floor(n%60);return`${g}:${W.toString().padStart(2,"0")}`},Me=n=>{k(n),w(!0),E(!1),V(0),L(0)},Be=()=>{if(S.current)try{S.current.pauseVideo()}catch(n){H("Error pausing video:",n)}w(!1),k(null),E(!1),G()},mt=()=>{e(n=>(n+1)%$.length)},bt=()=>{e(n=>(n-1+$.length)%$.length)};b.useEffect(()=>{const n=setInterval(()=>{e(g=>(g+1)%$.length)},5e3);return()=>clearInterval(n)},[$.length]);const vt=()=>{f<pe.length-2&&c(n=>n+1)},xt=()=>{f>0&&c(n=>n-1)};b.useEffect(()=>()=>{G()},[G]);const wt=(()=>R||z===0?Ut:z<50?jt:Ht)();return s(ne,{maxHeight:"100%",children:[s(h,{sx:{position:"relative",height:300,overflow:"hidden"},children:[r(h,{sx:{display:"flex",transition:"transform 0.7s cubic-bezier(0.4, 0.0, 0.2, 1)",transform:`translateX(-${a*100}%)`,height:"100%"},children:$.map(n=>s(h,{sx:{minWidth:"100%",position:"relative"},children:[r(h,{sx:{position:"absolute",inset:0,background:n.gradient,opacity:.85}}),r(h,{sx:{position:"absolute",inset:0,backgroundImage:`url(${n.image})`,backgroundSize:"cover",backgroundPosition:"center",mixBlendMode:"overlay"}}),r(At,{maxWidth:"lg",sx:{position:"relative",zIndex:1,height:"100%",display:"flex",alignItems:"center"},children:s(h,{sx:{maxWidth:"60%",color:"white"},children:[r(v,{variant:"h2",component:"h2",sx:{fontWeight:800,mb:2,fontSize:{xs:"2.5rem",md:"3.5rem"},lineHeight:1.2,textShadow:"0 2px 10px rgba(0,0,0,0.3)"},children:N(n.title)}),r(v,{variant:"h5",sx:{mb:3,opacity:.95,fontWeight:400,textShadow:"0 1px 5px rgba(0,0,0,0.3)"},children:N(n.subtitle)}),r(v,{variant:"body1",sx:{mb:4,opacity:.9,fontSize:"1.1rem",lineHeight:1.6,textShadow:"0 1px 3px rgba(0,0,0,0.3)"},children:N(n.description)})]})})]},n.id))}),r(ge,{size:"medium",onClick:bt,sx:{zIndex:0,position:"absolute",left:20,top:"50%",transform:"translateY(-50%)",background:"rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",color:"white","&:hover":{background:"rgba(255,255,255,0.3)",transform:"translateY(-50%) scale(1.1)"},transition:"all 0.3s ease"},children:r($e,{})}),r(ge,{size:"medium",onClick:mt,sx:{zIndex:0,position:"absolute",right:20,top:"50%",transform:"translateY(-50%)",background:"rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",color:"white","&:hover":{background:"rgba(255,255,255,0.3)",transform:"translateY(-50%) scale(1.1)"},transition:"all 0.3s ease"},children:r(He,{})}),r(h,{sx:{position:"absolute",bottom:20,left:"50%",transform:"translateX(-50%)",display:"flex",gap:1},children:$.map((n,g)=>r(h,{onClick:()=>e(g),sx:{width:12,height:12,borderRadius:"50%",background:g===a?"white":"rgba(255,255,255,0.5)",cursor:"pointer",transition:"all 0.3s ease",transform:g===a?"scale(1.3)":"scale(1)","&:hover":{background:"rgba(255,255,255,0.8)"}}},g))})]}),s(ne,{container:!0,spacing:4,sx:{p:4},children:[s(ne,{item:!0,xs:12,md:6,xl:6,lg:6,children:[r(Ye,{elevation:8,sx:{borderRadius:4,overflow:"hidden",background:n=>n.palette.gradient.default,mb:3},children:r(h,{sx:{p:3,color:"white"},children:s(h,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s(h,{children:[r(v,{variant:"h4",sx:{fontWeight:700,mb:1,color:"white"},children:N("Announcements")}),r(v,{variant:"body1",sx:{opacity:.9},children:N("Stay updated with latest news")})]}),r(We,{sx:{background:"rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",width:56,height:56},children:r(Rt,{sx:{fontSize:28}})})]})})}),s(h,{sx:{height:500,overflowY:"auto",pr:1,"&::-webkit-scrollbar":{width:"8px"},"&::-webkit-scrollbar-track":{background:"rgba(0,0,0,0.1)",borderRadius:"4px"},"&::-webkit-scrollbar-thumb":{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",borderRadius:"4px"}},children:[B!=null&&B.length?B==null?void 0:B.map(n=>r(de,{onMouseEnter:()=>o(n.broadcastId),onMouseLeave:()=>o(null),onClick:()=>{tt(n),De(!0)},sx:{mb:2,borderRadius:3,background:t===n.broadcastId?"linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%)":"white",border:t===n.broadcastId?"2px solid #667eea":"2px solid transparent",cursor:"pointer"},children:s(Fe,{sx:{p:3},children:[r(v,{variant:"h6",sx:{fontWeight:700,color:t===n.broadcastId?"#667eea":"text.primary",transition:"color 0.3s ease",fontSize:"1.2rem",mb:2,lineHeight:1.3},children:n.broadcastTitle}),r(v,{variant:"body2",color:"text.secondary",sx:{mb:2,lineHeight:1.6,fontSize:"0.95rem"},children:n.description}),s(h,{sx:{display:"flex",alignItems:"center",gap:.5,justifyContent:"space-between"},children:[s(h,{sx:{display:"flex",alignItems:"center",gap:.5},children:[r(ve,{sx:{fontSize:16,color:"text.secondary"}}),s(v,{variant:"caption",color:"text.secondary",sx:{fontWeight:500},children:[N("Updated on")," ",n.startDate]})]}),r(ye,{label:n.module,size:"small",sx:{background:"#f5576c",color:"white",fontSize:"0.7rem"}})]})]})},n.broadcastId)):s(h,{display:"flex",flexDirection:"column",alignItems:"center",pl:5,pt:.5,children:[r("img",{src:Ft,alt:"No Notifications",style:{width:"500px",height:"auto",marginBottom:"16px",opacity:.7,borderRadius:"50px"}}),r(v,{variant:"body1",align:"center",sx:{color:void 0},children:N("No Announcements available")})]}),r(Cr,{open:rt,onClose:()=>De(!1),announcement:ae})]})]}),s(ne,{item:!0,xs:12,md:6,children:[r(Ye,{elevation:8,sx:{borderRadius:4,overflow:"hidden",background:n=>n.palette.gradient.default,mb:3},children:r(h,{sx:{p:3,color:"white"},children:s(h,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[s(h,{children:[r(v,{variant:"h4",sx:{fontWeight:700,mb:1,color:"white"},children:N("Videos")}),r(v,{variant:"body1",sx:{opacity:.9},children:N("Learn with our video library")})]}),r(We,{sx:{background:"rgba(255,255,255,0.2)",backdropFilter:"blur(10px)",width:56,height:56},children:r(Ue,{sx:{fontSize:28}})})]})})}),s(be,{variant:"contained",startIcon:r(Ue,{}),onClick:()=>l(!0),sx:{background:n=>n.palette.gradient.default,color:"white",py:1.5,borderRadius:3,fontWeight:600,alignItems:"center",marginBottom:"20px",fontSize:"1rem","&:hover":{background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",transform:"translateY(-2px)",boxShadow:"0 8px 25px rgba(240, 147, 251, 0.4)"},transition:"all 0.3s ease"},children:[N("View All Videos")," (",j.length,")"]}),s(h,{sx:{position:"relative",mb:-5,height:320,overflow:"hidden"},children:[r(h,{sx:{display:"flex",transition:"transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)",transform:`translateX(-${f*50}%)`,gap:2,height:"100%",width:"fit-content"},children:pe.map(n=>s(de,{elevation:i===n.id?20:8,onMouseEnter:()=>u(n.id),onMouseLeave:()=>u(null),onClick:()=>Me(n),sx:{minWidth:"48%",maxWidth:"48%",transition:"all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1)",borderRadius:3,cursor:"pointer",background:i===n.id?"linear-gradient(135deg, #fff8f8 0%, #fce4ec 100%)":"white",border:i===n.id?"3px solid #f5576c":"3px solid transparent",height:"fit-content",boxShadow:i===n.id?"0 20px 40px rgba(245, 87, 108, 0.3)":"0 8px 20px rgba(0,0,0,0.1)"},children:[s(h,{sx:{position:"relative"},children:[r(ue,{component:"img",height:"160",image:`https://img.youtube.com/vi/${n.videoId}/mqdefault.jpg`,alt:n.title,sx:{transition:"all 0.3s ease",filter:i===n.id?"brightness(1.1)":"brightness(1)"}}),r(h,{sx:{position:"absolute",inset:0,background:i===n.id?"rgba(0,0,0,0.4)":"rgba(0,0,0,0.2)",display:"flex",alignItems:"center",justifyContent:"center",transition:"all 0.3s ease",opacity:i===n.id?1:0},children:r(ge,{size:"large",style:{zIndex:"0 !important",background:"rgba(255,255,255,0.95)",color:"#f5576c",transform:i===n.id?"scale(1.1)":"scale(1)","&:hover":{transform:"scale(1.2)",background:"white"}},children:r(je,{sx:{fontSize:36}})})})]}),r(Fe,{sx:{p:2},children:r(v,{variant:"h6",sx:{fontWeight:600,fontSize:"1rem",mb:1,color:i===n.id?"#f5576c":"text.primary",transition:"color 0.3s ease",lineHeight:1.3,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden"},children:n.title})})]},n.id))}),r(Y,{onClick:xt,disabled:f===0,sx:{position:"absolute",left:0,top:"40%",transform:"translateY(-50%)",background:"white",boxShadow:"0 4px 12px rgba(0,0,0,0.15)","&:hover":{background:"#f5f5f5"},"&:disabled":{opacity:.3}},children:r($e,{})}),r(Y,{onClick:vt,disabled:f>=pe.length-2,sx:{position:"absolute",right:0,top:"40%",transform:"translateY(-50%)",background:"white",boxShadow:"0 4px 12px rgba(0,0,0,0.15)","&:hover":{background:"#f5f5f5"},"&:disabled":{opacity:.3}},children:r(He,{})})]})]})]}),r(Mt,{anchor:"right",open:d,onClose:()=>l(!1),sx:{"& .MuiDrawer-paper":{width:{xs:"100%",sm:500},background:"linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%)"}},children:s(h,{sx:{p:3},children:[s(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[r(v,{variant:"h5",sx:{fontWeight:700,color:"#667eea"},children:N("Videos")}),r(Y,{onClick:()=>l(!1),children:r(ce,{})})]}),r(Vt,{fullWidth:!0,variant:"outlined",placeholder:N("Search videos")+"...",value:m,onChange:n=>P(n.target.value),InputProps:{startAdornment:r(Dt,{position:"start",children:r(Tt,{})})},sx:{mb:3}}),r(Ot,{sx:{maxHeight:"calc(100vh - 200px)",overflow:"auto"},children:Te.map((n,g)=>s(ie.Fragment,{children:[r(Nt,{sx:{borderRadius:2,mb:1,cursor:"pointer","&:hover":{background:"rgba(102, 126, 234, 0.1)"}},onClick:()=>{Me(n),l(!1)},children:s(h,{sx:{display:"flex",gap:2,width:"100%"},children:[r(h,{sx:{position:"relative"},children:r(ue,{component:"img",sx:{width:120,height:90,borderRadius:1},image:`https://img.youtube.com/vi/${n.videoId}/mqdefault.jpg`,alt:n.title})}),s(h,{sx:{flex:1},children:[r(v,{variant:"subtitle1",sx:{fontWeight:600,mb:.5},children:n.title}),r(v,{variant:"body2",color:"text.secondary",sx:{mb:1},children:n.description})]})]})}),g<Te.length-1&&r(me,{})]},n.id))})]})}),r(Ve,{open:x,onClose:Be,maxWidth:"lg",fullWidth:!0,sx:{"& .MuiDialog-paper":{borderRadius:3,background:"#000",overflow:"hidden"}},children:y&&s(se,{children:[r(Je,{sx:{color:"white",p:2},children:s(h,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[r(v,{variant:"h6",sx:{fontWeight:600},children:y.title}),r(Y,{onClick:Be,sx:{color:"white"},children:r(ce,{})})]})}),r(Ke,{sx:{p:0,position:"relative",aspectRatio:"16/9"},children:s(h,{sx:{width:"100%",height:"100%",position:"relative"},onMouseMove:yt,onMouseEnter:()=>O(!0),onMouseLeave:()=>O(!1),children:[r(Pr,{videoId:y.videoId,opts:it,onReady:st,onStateChange:lt,onError:ct,style:{width:"100%",height:"100%"}}),s(h,{sx:{position:"absolute",bottom:0,left:0,right:0,background:"linear-gradient(transparent, rgba(0,0,0,0.8))",color:"white",p:2,transform:T||!C?"translateY(0)":"translateY(100%)",transition:"transform 0.3s ease",zIndex:10},children:[r(h,{sx:{height:6,background:"rgba(255,255,255,0.3)",borderRadius:3,mb:2,cursor:"pointer",overflow:"hidden"},onClick:ut,children:r(h,{sx:{height:"100%",background:"#f5576c",borderRadius:3,width:_>0?`${A/_*100}%`:"0%",transition:"width 0.1s ease"}})}),s(h,{sx:{display:"flex",alignItems:"center",gap:2},children:[r(Y,{onClick:dt,sx:{color:"white"},children:C?r(Bt,{}):r(je,{})}),s(v,{variant:"caption",children:[Ne(A)," / ",Ne(_)]}),s(h,{sx:{display:"flex",alignItems:"center",gap:1,ml:"auto"},children:[r(Y,{onClick:ft,sx:{color:"white"},children:r(wt,{})}),r(Lt,{value:z,onChange:ht,sx:{width:80,color:"#f5576c","& .MuiSlider-thumb":{width:16,height:16}}}),s(be,{size:"small",onClick:()=>pt(U===1?1.5:1),sx:{color:"white",minWidth:40},children:[U,"x"]}),r(Y,{onClick:gt,sx:{color:"white"},children:K?r(zt,{}):r(Wt,{})})]})]})]})]})})]})})]})};export{Ar as default};
