import { useState } from "react";
import {
  destination_IDM,
  destination_ProfitCenter_Mass,
  destination_CostCenter_Mass,
  destination_GeneralLedger_Mass,
} from "./../destinationVariables";

import { API_CODE, CHANGE_KEYS } from "@constant/enum";
import { doAjax, promiseAjax } from "@components/Common/fetchService";
import { useSelector } from "react-redux";

const useDuplicateCheckHierarchy = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const requestorPayload = useSelector(
    (state) => state.payload.requestorPayload
  );
  const reduxPayload = useSelector((state) => state.hierarchyData);
  const getDuplicityResponseForNode = async (
    newNodeTitle,
    module,
    controllingArea,
    group = "",
    coa,
    reqId
  ) => {
    var PCG_NODE_DUPLICATECHECK = {
      requestId: "",
      controllingArea,
      hierarchyGrp: group,
      node: newNodeTitle,
    };

    var CCG_NODE_DUPLICATECHECK = {
      requestId: reqId,
      controllingArea: controllingArea,
      hierarchyGrp: group,
      node: newNodeTitle,
    };

    var CEG_NODE_DUPLICATECHECK = {
      requestId: reqId,
      chartOfAccount: coa,
      hierarchyGrp: group,
      node: newNodeTitle,
    };

    var urlMap = {
      "PCG": `/${destination_ProfitCenter_Mass}/node/nodeDuplicacyCheckForPCG`,
      "CCG": `/${destination_CostCenter_Mass}/node/nodeDuplicacyCheckForCCG`,
      "CEG": `/${destination_GeneralLedger_Mass}/node/nodeDuplicacyCheckForCEG`,
    };

    var nodePayloadMap = {
      "PCG": PCG_NODE_DUPLICATECHECK,
      "CCG": CCG_NODE_DUPLICATECHECK,
      "CEG": CEG_NODE_DUPLICATECHECK,
    };

    try {
      setLoading(true);
      const response = await promiseAjax(
        urlMap[module],
        "post",
        nodePayloadMap[module]
      );
      const data = await response.json();

      if (data.statusCode !== API_CODE.STATUS_200) {
        throw new Error(data.message || "Duplicate check failed");
      }
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getDuplicityResponseForDesc = async (
    newDescription,
    module,
    controllingArea,
    coa,
    reqId
  ) => {
    var PCG_DESC_DUPLICATECHECK = {
      requestId: "",
      classValue: "0106",
      controllingArea: controllingArea,
      desc: newDescription,
    };

    var CCG_DESC_DUPLICATECHECK = {
      requestId: reqId,
      classValue: "0101",
      controllingArea: controllingArea,
      desc: newDescription,
    };

    var CEG_DESC_DUPLICATECHECK = {
      requestId: reqId,
      classValue: "0102",
      chartOfAccount: coa,
      desc: newDescription,
    };

    var urlMap = {
      "PCG": `/${destination_ProfitCenter_Mass}/node/descDuplicacyCheckForPCG`,
      "CCG": `/${destination_CostCenter_Mass}/node/descDuplicacyCheckForCCG`,
      "CEG": `/${destination_GeneralLedger_Mass}/node/descDuplicacyCheckForCEG`,
    };

    var nodePayloadMap = {
      "PCG": PCG_DESC_DUPLICATECHECK,
      "CCG": CCG_DESC_DUPLICATECHECK,
      "CEG": CEG_DESC_DUPLICATECHECK,
    };

    try {
      setLoading(true);
      const response = await promiseAjax(
        urlMap[module],
        "post",
        nodePayloadMap[module]
      );
      const data = await response.json();

      if (data.statusCode !== API_CODE.STATUS_200) {
        throw new Error(data.message || "Duplicate check failed");
      }
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const getDuplicityResponseForObject = async (
    newTag,
    module,
    controllingArea,
    coa,
    group
  ) => {
    var PCG_OBJ_DUPLICATECHECK = {
      classValue: "0106",
      controllingArea: controllingArea,
      hierarchyGrp: group,
      pc: newTag,
    };

    var CCG_OBJ_DUPLICATECHECK = {
      classValue: "0101",
      controllingArea: controllingArea,
      hierarchyGrp: group,
      cc: newTag,
    };

    var CEG_OBJ_DUPLICATECHECK = {
      classValue: "0102",
      chartOfAccount: coa,
      hierarchyGrp: group,
      gl: newTag,
    };

    var urlMap = {
      "PCG": `/${destination_ProfitCenter_Mass}/node/pcDuplicacyCheckForPCG`,
      "CCG": `/${destination_CostCenter_Mass}/node/ccDuplicacyCheckForCCG`,
      "CEG": `/${destination_GeneralLedger_Mass}/node/glDuplicacyCheckForCEG`,
    };

    var nodePayloadMap = {
      "PCG": PCG_OBJ_DUPLICATECHECK,
      "CCG": CCG_OBJ_DUPLICATECHECK,
      "CEG": CEG_OBJ_DUPLICATECHECK,
    };

    try {
      setLoading(true);
      const response = await promiseAjax(
        urlMap[module],
        "post",
        nodePayloadMap[module]
      );
      const data = await response.json();

      if (data.statusCode !== API_CODE.STATUS_200) {
        throw new Error(data.message || "Duplicate check failed");
      }
      return data;
    } catch (err) {
      setError(err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const checkForNodeDuplicacy = async (
    newNodeTitle,
    module,
    controllingArea,
    group = "",
    coa = "",
    reqId = ""
  ) => {
    return await getDuplicityResponseForNode(
      newNodeTitle,
      module,
      controllingArea,
      group,
      coa,
      reqId
    );
  };

  const checkForDescriptionDuplicacy = async (
    newDescription,
    module,
    controllingArea,
    coa,
    reqId
  ) => {
    return await getDuplicityResponseForDesc(
      newDescription,
      module,
      controllingArea,
      coa,
      reqId
    );
  };

  const checkForObjectDuplicacy = async (
    newObject,
    module,
    controllingArea,
    coa,
    group
  ) => {
    return await getDuplicityResponseForObject(
      newObject,
      module,
      controllingArea,
      coa,
      group
    );
  };

  return {
    checkForNodeDuplicacy,
    checkForDescriptionDuplicacy,
    checkForObjectDuplicacy,
  };
};

export default useDuplicateCheckHierarchy;
