import{cs as Oe,k as r2,A as s2,b as i2,n as X,a as be,r as u,x as pe,s as ze,j as e,O as E,c as m,V as l2,W as o2,d as B,i as c2,Y as _e,qN as Fe,AO as Re,$ as fe,a1 as d2,a2 as u2,a3 as p2,F as Me,an as Ee,br as h2,w as C2,ej as y2,bs as m2,aZ as Ce,a6 as De,gD as M2,AP as L2,dy as g2,ai as f2,aj as D2,Z as he,a4 as ye,AQ as A2,a8 as xe,AR as T2,ft as K2,al as b2,AS as ne,AT as G2,bD as Le,B as $,c9 as v2,ca as N2,cb as B2,C as ie,AU as ae,cq as qe,AV as we,aG as q2,aW as w2,ae as se,b5 as S2,b6 as je,AW as x2,hG as O2,g as k2,aT as ge,ws as H2,du as U2,bM as V2,bN as Ye,dv as Ae,f as Te,dw as Ke,t as Ge,bS as ve,L as Ne,pG as Ie,am as Z2,aP as F2}from"./index-226a1e75.js";import{F as j2}from"./FilterField-868050e3.js";import{B as ke,c as $2}from"./DashboardSetting-970ae243.js";import{L as z2}from"./LargeDropdown-b7ffdbd5.js";import{d as Be}from"./DragIndicator-35596e1a.js";import"./useChangeLogUpdate-23c3e0f8.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./AutoCompleteType-63e88d3d.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./TableContainer-debf0374.js";import"./CircularProgress-1acedaf0.js";const W2=Oe(r2)(({theme:s})=>({marginTop:"0px !important",border:`1px solid ${s.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),Y2=Oe(s2)(({theme:s})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:s.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:s.palette.primary.light}})),Q2=({handleSearch:s,company:p,supplier:x,clearFunction:d})=>{var J;const h=i2(),_=X(g=>g.commonFilter.RequestBench),D=X(g=>g.commonFilter.Dashboard),{t:P}=be(),[A,L]=u.useState({}),[M,K]=u.useState({}),[O,U]=u.useState([]),[I,w]=u.useState([]),[y,v]=u.useState([]),[q,W]=u.useState([]),[Z,f]=u.useState([]),[T,F]=u.useState([...D.dashboardDate]);u.useEffect(()=>{var g=Z.map(de=>de).join("$^$");let N={..._,selectedRegion:g};j(pe({module:"Dashboard",filterData:N}))},[Z]),u.useEffect(()=>{let g="";g={...D,dashBoardModuleName:q},j(pe({module:"Dashboard",filterData:g}))},[q]),u.useEffect(()=>{let g=[];y==null||y.map(N=>{g.push(N==null?void 0:N.code)}),j(pe({module:"Dashboard",filterData:{...D,selectedusersId:g}}))},[y]),u.useEffect(()=>{let g=[];I==null||I.map(N=>{g.push(N==null?void 0:N.lable)}),j(pe({module:"Dashboard",filterData:{...D,selectedRequestStatus:g}}))},[I]),u.useEffect(()=>{let g=[];O==null||O.map(N=>{g.push(N==null?void 0:N.lable)}),j(pe({module:"Dashboard",filterData:{...D,selectedRequestType:g}}))},[O]);const k=new Date,R=new Date;R.setDate(R.getDate()-7);const j=ze(),H=X(g=>g.appSettings),Q=g=>{j(pe({module:"Dashboard",filterData:{...D,dashboardDate:g}}))};u.useEffect(()=>{if(D!=null&&D.dashboardDate){const g=new Date(D==null?void 0:D.dashboardDate[0]),N=new Date(D==null?void 0:D.dashboardDate[1]);F([g,N])}},[D==null?void 0:D.dashboardDate]);const z=[{type:"singleSelect",filterName:"companyCode",filterData:A,filterTitle:"Company Code"},{type:"singleSelect",filterName:"vendorNo",filterData:M,filterTitle:"Business Partner"},{type:"dateRange",filterName:"dashboardDate",filterTitle:"Date Range"}],re=["US","EUR"],le=()=>{Z.length===(re==null?void 0:re.length)?f([]):f(re)};return e(Me,{children:e(E,{item:!0,md:12,children:m(W2,{defaultExpanded:!1,children:[m(Y2,{expandIcon:e(l2,{sx:{fontSize:"1.25rem",color:h.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",className:"filterDashBoard",children:[e(o2,{sx:{fontSize:"1.25rem",marginRight:1,color:h.palette.primary.dark}}),e(B,{sx:{fontSize:"0.875rem",fontWeight:600,color:h.palette.primary.dark},children:P("Filter Dashboard")})]}),m(c2,{children:[m(E,{container:!0,rowSpacing:1,spacing:2,children:[m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:P("Module")}),e(Fe,{options:[...Re.filter(g=>g!=="Select All").sort((g,N)=>g.localeCompare(N))],value:((J=D==null?void 0:D.dashBoardModuleName)==null?void 0:J.length)>0&&(D==null?void 0:D.dashBoardModuleName)||(q==null?void 0:q.length)>0&&q||[Re[0]],onChange:g=>{var N;g.length>0&&((N=g[g.length-1])==null?void 0:N.label)==="Select All"?handleSelectAllModule():W(g)},placeholder:"Select Module Name"})]}),m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:P("Region")}),e(Fe,{options:[...re.filter(g=>g!=="Select All").sort((g,N)=>g.localeCompare(N))],value:Z,onChange:g=>{var N;g.length>0&&((N=g[g.length-1])==null?void 0:N.label)==="Select All"?le():f(g)},placeholder:"Select Region"})]}),m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:P("Date Range")}),e(fe,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(d2,{dateAdapter:u2,children:e(p2,{handleDate:Q,cleanDate:!1,date:T})})})]}),z==null?void 0:z.map(g=>g!=null&&g.hideFilter?e(Me,{}):e(E,{item:!0,md:3,children:e(j2,{type:g.type,filterName:g.filterName,filterData:g.filterData,moduleName:"Dashboard",onChangeFilter:g.onChangeFilter,filterTitle:g.filterTitle})},g.filterTitle))]}),e(E,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(E,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:e(Ee,{variant:"outlined",sx:{...h2},onClick:()=>{F([R,k]),w([]),U([]),f([]),v([]),W([]),d(),j(C2({module:"Dashboard",days:H.range})),j(pe({module:"Dashboard",filterData:{...D,selectedRequestType:[],selectedRequestStatus:[],selectedusersId:[],selectedRegion:"",dashboardDate:[R,k]}}))},children:P("Clear")})})})]})]})})})},X2=()=>e(E,{md:12,sx:{paddingTop:"1%"},children:e(Q2,{})}),J2="data:image/svg+xml,%3csvg%20width='350'%20height='230'%20viewBox='0%200%20350%20230'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M137.416%2059.0001L198.175%2059.1176L223.314%2084.3395L223.181%20143.452C220.557%20142.408%20217.695%20141.835%20214.699%20141.835C202.011%20141.835%20191.725%20152.121%20191.725%20164.809C191.725%20177.379%20201.821%20187.592%20214.346%20187.78L137.158%20187.632C136.084%20187.631%20135.021%20187.419%20134.029%20187.006C133.038%20186.594%20132.137%20185.99%20131.379%20185.229C130.621%20184.468%20130.021%20183.565%20129.613%20182.572C129.205%20181.579%20128.996%20180.514%20129%20179.441L129.219%2067.144C129.221%2066.0707%20129.436%2065.0085%20129.849%2064.018C130.263%2063.0276%20130.867%2062.1285%20131.629%2061.372C132.39%2060.6156%20133.293%2060.0167%20134.286%2059.6097C135.279%2059.2027%20136.343%2058.9955%20137.416%2059.0001ZM214.965%20187.781C215.045%20187.781%20215.125%20187.78%20215.205%20187.777C215.125%20187.779%20215.045%20187.78%20214.965%20187.781Z'%20fill='url(%23paint0_linear_12419_6513)'/%3e%3cpath%20d='M198.175%2059.1176L198.53%2058.7646L198.383%2058.618L198.176%2058.6176L198.175%2059.1176ZM137.416%2059.0001L137.414%2059.5001L137.415%2059.5001L137.416%2059.0001ZM223.314%2084.3395L223.814%2084.3406L223.815%2084.1334L223.669%2083.9865L223.314%2084.3395ZM223.181%20143.452L222.996%20143.916L223.679%20144.188L223.681%20143.453L223.181%20143.452ZM214.346%20187.78L214.345%20188.28L214.354%20187.28L214.346%20187.78ZM137.158%20187.632L137.159%20187.132H137.159L137.158%20187.632ZM131.379%20185.229L131.734%20184.876H131.734L131.379%20185.229ZM129.613%20182.572L129.15%20182.762L129.613%20182.572ZM129%20179.441L129.5%20179.442L129.5%20179.442L129%20179.441ZM129.219%2067.144L128.719%2067.1428L128.719%2067.143L129.219%2067.144ZM129.849%2064.018L129.388%2063.8254H129.388L129.849%2064.018ZM131.629%2061.372L131.981%2061.7267H131.981L131.629%2061.372ZM134.286%2059.6097L134.476%2060.0724L134.286%2059.6097ZM214.965%20187.781L214.959%20187.281L214.965%20188.281L214.965%20187.781ZM215.205%20187.777L215.22%20188.277L215.194%20187.277L215.205%20187.777ZM198.176%2058.6176L137.417%2058.5001L137.415%2059.5001L198.174%2059.6176L198.176%2058.6176ZM223.669%2083.9865L198.53%2058.7646L197.821%2059.4705L222.96%2084.6925L223.669%2083.9865ZM223.681%20143.453L223.814%2084.3406L222.814%2084.3384L222.681%20143.451L223.681%20143.453ZM214.699%20142.335C217.63%20142.335%20220.43%20142.896%20222.996%20143.916L223.366%20142.987C220.683%20141.921%20217.759%20141.335%20214.699%20141.335V142.335ZM192.225%20164.809C192.225%20152.397%20202.287%20142.335%20214.699%20142.335V141.335C201.734%20141.335%20191.225%20151.845%20191.225%20164.809H192.225ZM214.354%20187.28C202.101%20187.096%20192.225%20177.106%20192.225%20164.809H191.225C191.225%20177.653%20201.54%20188.088%20214.339%20188.28L214.354%20187.28ZM137.157%20188.132L214.345%20188.28L214.347%20187.28L137.159%20187.132L137.157%20188.132ZM133.838%20187.468C134.89%20187.906%20136.018%20188.131%20137.158%20188.132L137.159%20187.132C136.151%20187.131%20135.152%20186.932%20134.221%20186.545L133.838%20187.468ZM131.025%20185.582C131.829%20186.39%20132.785%20187.031%20133.838%20187.468L134.221%20186.545C133.29%20186.158%20132.445%20185.591%20131.734%20184.876L131.025%20185.582ZM129.15%20182.762C129.584%20183.816%20130.221%20184.775%20131.025%20185.582L131.734%20184.876C131.022%20184.162%20130.459%20183.314%20130.075%20182.382L129.15%20182.762ZM128.5%20179.439C128.496%20180.579%20128.717%20181.708%20129.15%20182.762L130.075%20182.382C129.692%20181.449%20129.497%20180.45%20129.5%20179.442L128.5%20179.439ZM128.719%2067.143L128.5%20179.44L129.5%20179.442L129.719%2067.145L128.719%2067.143ZM129.388%2063.8254C128.949%2064.8765%20128.722%2066.0038%20128.719%2067.1428L129.719%2067.1451C129.721%2066.1376%20129.922%2065.1404%20130.31%2064.2107L129.388%2063.8254ZM131.276%2061.0173C130.468%2061.8201%20129.826%2062.7743%20129.388%2063.8254L130.31%2064.2107C130.699%2063.2809%20131.266%2062.4368%20131.981%2061.7267L131.276%2061.0173ZM134.097%2059.1471C133.043%2059.579%20132.084%2060.2146%20131.276%2061.0173L131.981%2061.7267C132.696%2061.0166%20133.544%2060.4544%20134.476%2060.0724L134.097%2059.1471ZM137.418%2058.5001C136.279%2058.4953%20135.151%2058.7151%20134.097%2059.1471L134.476%2060.0724C135.408%2059.6903%20136.407%2059.4958%20137.414%2059.5001L137.418%2058.5001ZM214.965%20188.281C215.05%20188.281%20215.135%20188.28%20215.22%20188.277L215.189%20187.278C215.114%20187.28%20215.039%20187.281%20214.964%20187.281L214.965%20188.281ZM215.194%20187.277C215.116%20187.279%20215.037%20187.28%20214.959%20187.281L214.97%20188.281C215.052%20188.28%20215.134%20188.279%20215.216%20188.277L215.194%20187.277Z'%20fill='white'/%3e%3cpath%20d='M63.3267%2078.8821L61.5771%2074.0909L62.5083%2075.1425V73.5061L64.4931%2076.5479L62.7435%2065.6732L65.0762%2071.6339L65.3066%2066.3759L65.6594%2068.1302L66.125%2062.1646L66.9385%2071.285L68.1048%2068.1302L67.8745%2072.2187L71.0207%2062.8673L69.5064%2074.5577L71.7215%2072.9214L70.4376%2075.0246L72.0695%2073.855L70.2121%2079.2359L63.4443%2079.3538'%20fill='url(%23paint1_linear_12419_6513)'/%3e%3cpath%20d='M70.8099%2090.6904H63.5177L61.5525%2078.027H72.7701L70.8099%2090.6904Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M90.7654%2090.6069H53.9712V188.759H90.7654V90.6069Z'%20fill='white'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M88.2612%2093.8255H56.1079V117.467H88.2612V93.8255Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20103.501H65.7524V107.801H78.6118V103.501Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20121.413H56.1079V145.054H88.2612V121.413Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20131.084H65.7524V135.383H78.6118V131.084Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M88.2612%20148.99H56.1079V172.631H88.2612V148.99Z'%20stroke='%23EBEBF2'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M78.6118%20158.661H65.7524V162.961H78.6118V158.661Z'%20fill='%23ADA9BF'%20stroke='%23EBEBF2'%20stroke-miterlimit='10'/%3e%3cpath%20d='M54.7358%20141.025C54.2163%20140.946%2053.6871%20140.926%2053.1578%20140.97C53.0941%20140.97%2053.099%20141.079%2053.1578%20141.084C53.6772%20141.084%2054.2016%20141.059%2054.7211%20141.084C54.7407%20141.084%2054.7505%20141.059%2054.7456%20141.044C54.7456%20141.039%2054.7407%20141.029%2054.7358%20141.025Z'%20fill='white'/%3e%3cpath%20d='M49.046%20151.319C48.208%20151.334%2047.3847%20151.501%2046.6055%20151.806V151.845C47.4288%20151.649%2048.2276%20151.57%2049.0411%20151.432C49.0705%20151.418%2049.0852%20151.383%2049.0754%20151.349C49.0705%20151.334%2049.0607%20151.324%2049.046%20151.319Z'%20fill='white'/%3e%3cpath%20d='M47.3406%20147.762C46.9926%20147.762%2046.6447%20147.791%2046.3065%20147.855C46.2918%20147.86%2046.282%20147.88%2046.2869%20147.899C46.2869%20147.909%2046.2967%20147.914%2046.3065%20147.919C46.6545%20147.939%2046.9975%20147.919%2047.3406%20147.875C47.37%20147.875%2047.3945%20147.85%2047.3945%20147.821C47.3945%20147.791%2047.37%20147.767%2047.3406%20147.767V147.762Z'%20fill='white'/%3e%3cpath%20d='M272.355%20163.806C272.497%20160.115%20272.448%20156.415%20272.203%20152.73C271.659%20145.118%20270.502%20137.56%20268.738%20130.135C267.773%20125.958%20266.582%20121.845%20265.342%20117.742C265.303%20117.614%20265.107%20117.649%20265.141%20117.781C268.978%20132.258%20272.124%20147.192%20271.904%20162.238C271.845%20166.44%20271.355%20170.582%20270.958%20174.769C270.958%20174.882%20271.13%20174.926%20271.154%20174.813C271.943%20171.221%20272.203%20167.467%20272.35%20163.811L272.355%20163.806Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M265.141%20117.762C265.141%20117.762%20259.334%20132.543%20260.632%20136.002C261.931%20139.462%20264.612%20140.204%20264.612%20140.204C264.612%20140.204%20261.059%20145.29%20263.543%20150.061C266.028%20154.833%20271.541%20154.607%20271.924%20160.017C271.914%20160.042%20272.218%20137.428%20265.141%20117.762Z'%20fill='url(%23paint2_linear_12419_6513)'/%3e%3cpath%20d='M271.281%20150.705C271.291%20150.681%20271.291%20150.651%20271.281%20150.627C270.522%20140.533%20268.63%20130.563%20265.651%20120.892C265.651%20120.867%20265.592%20120.872%20265.602%20120.892C266.753%20125.221%20267.768%20129.565%20268.65%20133.934C267.871%20133.192%20266.915%20132.661%20265.876%20132.381C265.876%20132.381%20265.847%20132.405%20265.876%20132.41C266.969%20132.813%20267.949%20133.467%20268.743%20134.322C268.91%20135.182%20269.076%20136.037%20269.233%20136.897C267.934%20135.442%20266.136%20134.72%20264.332%20134.032C264.322%20134.037%20264.318%20134.052%20264.322%20134.061C264.322%20134.066%20264.327%20134.071%20264.332%20134.071C266.209%20134.833%20268.076%20135.821%20269.351%20137.442C269.792%20139.894%20270.179%20142.356%20270.517%20144.823C269.581%20143.84%20268.375%20143.147%20267.052%20142.833C267.008%20142.833%20266.988%20142.882%20267.028%20142.892C268.385%20143.295%20269.596%20144.081%20270.517%20145.162H270.541C270.654%20146.027%20270.767%20146.897%20270.87%20147.771C270.144%20147.157%20269.326%20146.661%20268.444%20146.307C268.419%20146.307%20268.405%20146.332%20268.444%20146.342C269.346%20146.735%20270.169%20147.28%20270.88%20147.958H270.909C271.002%20148.789%20271.1%20149.614%20271.183%20150.445C269.875%20148.857%20268.179%20147.644%20266.258%20146.916C266.244%20146.916%20266.229%20146.931%20266.229%20146.946C266.229%20146.961%20266.244%20146.975%20266.258%20146.975C268.155%20147.811%20269.821%20149.098%20271.11%20150.72C271.139%20150.754%20271.188%20150.769%20271.232%20150.759C271.36%20152.032%20271.473%20153.305%20271.566%20154.577C271.566%20154.597%20271.595%20154.597%20271.595%20154.577C271.497%20153.295%20271.394%20152.002%20271.281%20150.695V150.705Z'%20fill='white'/%3e%3cpath%20d='M267.317%20146.577C266.508%20146.111%20265.621%20145.806%20264.695%20145.678C264.666%20145.678%20264.661%20145.722%20264.695%20145.727C265.597%20145.909%20266.469%20146.214%20267.288%20146.636C267.322%20146.656%20267.351%20146.592%20267.317%20146.577Z'%20fill='white'/%3e%3cpath%20d='M287.62%20136.263C285.165%20137.963%20282.911%20139.939%20280.897%20142.155C278.824%20144.499%20277.221%20147.221%20276.187%20150.174C273.982%20156.332%20272.796%20162.808%20272.673%20169.349C272.6%20172.971%20272.948%20176.587%20273.707%20180.13C273.722%20180.184%20273.776%20180.219%20273.83%20180.209C273.879%20180.194%20273.913%20180.145%20273.908%20180.096C272.963%20173.845%20272.997%20167.486%20274.016%20161.246C275.016%20155.133%20276.579%20148.479%20280.456%20143.511C282.568%20140.803%20285.2%20138.676%20287.802%20136.474C287.939%20136.351%20287.763%20136.165%20287.62%20136.263Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M273.526%20164.941C274.923%20162.676%20276.565%20160.568%20278.427%20158.661C281.333%20155.747%20283.778%20153.678%20284.161%20151.752C284.543%20149.826%20281.25%20148.405%20281.25%20148.405C281.25%20148.405%20285.268%20149.133%20286.371%20147.437C287.473%20145.747%20288.003%20135.943%20288.003%20135.943C288.003%20135.943%20280.691%20141.182%20277.28%20148.523C273.869%20155.865%20273.531%20164.941%20273.531%20164.941H273.526Z'%20fill='url(%23paint3_linear_12419_6513)'/%3e%3cpath%20d='M285.469%20138.459C282.391%20141.486%20279.294%20144.72%20277.471%20148.705C276.849%20150.081%20276.31%20151.486%20275.854%20152.926C275.761%20153.216%20275.668%20153.506%20275.579%20153.796C275.574%20153.811%20275.574%20153.826%20275.579%20153.84C274.717%20156.818%20274.026%20159.84%20273.501%20162.897C273.501%20162.916%20273.526%20162.921%20273.531%20162.897C273.849%20161.369%20274.192%20159.84%20274.585%20158.287C274.599%20158.287%20274.614%20158.287%20274.629%20158.287C275.236%20158.027%20275.878%20157.865%20276.535%20157.801C276.579%20157.801%20276.574%20157.722%20276.535%20157.722C275.888%20157.752%20275.246%20157.87%20274.629%20158.071C274.981%20156.686%20275.369%20155.3%20275.8%20153.934C277.221%20153.59%20278.677%20153.398%20280.137%20153.364M280.137%20153.339C278.706%20153.27%20277.27%20153.393%20275.868%20153.703C275.947%20153.472%20276.025%20153.236%20276.099%20153.005C277.05%20152.779%20278.015%20152.612%20278.985%20152.514V152.484C278.035%20152.504%20277.089%20152.617%20276.158%20152.828C276.54%20151.649%20276.986%20150.484%20277.461%20149.354C277.858%20148.43%20278.319%20147.536%20278.843%20146.681C279.613%20146.489%20280.397%20146.346%20281.186%20146.263V146.224C280.451%20146.17%20279.706%20146.243%20278.995%20146.44C279.377%20145.816%20279.784%20145.206%20280.22%20144.612C281.862%20144.248%20283.523%20143.909%20285.204%20144.229C285.219%20144.224%20285.224%20144.209%20285.219%20144.194C285.219%20144.189%20285.209%20144.184%20285.204%20144.179C283.607%20143.757%20282.024%20143.998%20280.436%20144.312C280.607%20144.081%20280.789%20143.855%20280.965%20143.629C281.95%20143.324%20282.979%20143.157%20284.013%20143.143M284.013%20143.108C283.053%20143.02%20282.087%20143.108%20281.156%20143.364C282.514%20141.673%20284.008%20140.086%20285.493%20138.518C285.596%20138.43%20285.542%20138.391%20285.469%20138.459L284.013%20143.108Z'%20fill='white'/%3e%3cpath%20d='M252.086%20146.661C254.467%20147.472%20256.746%20148.548%20258.883%20149.875C261.103%20151.29%20263.024%20153.118%20264.548%20155.265C267.753%20159.737%20270.203%20164.7%20271.806%20169.968C272.703%20172.882%20273.252%20175.894%20273.453%20178.936C273.443%20178.985%20273.394%20179.015%20273.35%20179.005C273.315%20179%20273.291%20178.971%20273.281%20178.936C272.61%20173.658%20271.115%20168.518%20268.856%20163.708C266.636%20158.99%20263.837%20153.973%20259.554%20150.848C257.221%20149.147%20254.605%20148.032%20251.988%20146.857C251.85%20146.813%20251.943%20146.617%20252.081%20146.661H252.086Z'%20fill='%23EBEBEB'/%3e%3cpath%20d='M270.1%20166.602C268.444%20165.093%20266.631%20163.772%20264.69%20162.656C261.666%20160.971%20259.211%20159.865%20258.456%20158.396C257.707%20156.926%20260.039%20155.015%20260.039%20155.015C260.039%20155.015%20256.952%20156.543%20255.673%20155.418C254.394%20154.292%20251.703%20146.499%20251.703%20146.499C251.703%20146.499%20258.829%20149.039%20263.274%20154.189C267.719%20159.344%20270.096%20166.602%20270.096%20166.602H270.1Z'%20fill='url(%23paint4_linear_12419_6513)'/%3e%3cpath%20d='M269.728%20164.99C266.165%20158.283%20261.407%20152.214%20254.899%20148.229C256.599%20149.324%20258.197%20150.577%20259.667%20151.973C258.339%20151.899%20257.021%20152.179%20255.835%20152.784C255.825%20152.789%20255.82%20152.803%20255.825%20152.813C255.825%20152.818%20255.83%20152.823%20255.835%20152.823C257.094%20152.346%20258.432%20152.12%20259.775%20152.155C259.79%20152.155%20259.804%20152.155%20259.819%20152.155C260.049%20152.371%20260.275%20152.597%20260.505%20152.823C259.814%20152.853%20259.128%20152.946%20258.457%20153.093V153.123C259.167%20152.985%20259.892%20152.926%20260.618%20152.941C261.809%20154.15%20262.936%20155.423%20263.98%20156.759C263.499%20156.69%20263.014%20156.725%20262.549%20156.862C262.534%20156.862%20262.524%20156.872%20262.524%20156.887C262.524%20156.902%20262.534%20156.912%20262.549%20156.912C263.053%20156.853%20263.563%20156.848%20264.073%20156.892C264.269%20157.143%20264.455%20157.388%20264.656%20157.654C263.2%20157.56%20261.74%20157.703%20260.334%20158.071C260.309%20158.071%20260.334%20158.111%20260.334%20158.106C261.804%20157.796%20263.303%20157.683%20264.803%20157.762C264.984%20158.002%20265.156%20158.248%20265.337%20158.494C264.47%20158.474%20263.602%20158.572%20262.764%20158.794C262.75%20158.799%20262.745%20158.813%20262.75%20158.828C262.75%20158.833%20262.759%20158.838%20262.764%20158.843C263.641%20158.656%20264.538%20158.582%20265.43%20158.636C266.19%20159.693%20266.93%20160.779%20267.636%20161.87C267.253%20161.801%20266.861%20161.826%20266.494%20161.939M266.494%20161.988C266.9%20161.958%20267.312%20161.978%20267.719%20162.047C268.356%20163.034%20268.964%20164.032%20269.557%20165.044C269.65%20165.143%20269.787%20165.079%20269.738%20164.99L266.499%20161.983L266.494%20161.988Z'%20fill='white'/%3e%3cpath%20d='M264.185%20159.428C263.484%20159.378%20262.779%20159.467%20262.112%20159.683C262.112%20159.683%20262.112%20159.717%20262.112%20159.713C262.793%20159.595%20263.485%20159.526%20264.175%20159.496C264.225%20159.496%20264.234%20159.428%20264.185%20159.423V159.428Z'%20fill='white'/%3e%3cpath%20d='M279.294%20188.784H263.21L265.504%20171.934L266.087%20167.614H276.413L277.001%20171.934L279.294%20188.784Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M277.001%20171.934H265.504L266.087%20167.614H276.413L277.001%20171.934Z'%20fill='%23D9D3EA'/%3e%3cpath%20d='M277.893%20166.12H264.612V169.781H277.893V166.12Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M19%20188.509L56.25%20188.391L93.5%20188.346L168%20188.263L242.5%20188.346L279.75%20188.391L317%20188.509L279.75%20188.631L242.5%20188.671L168%20188.754L93.5%20188.671L56.25%20188.627L19%20188.509Z'%20fill='%23D9D9F4'/%3e%3cg%20opacity='0.93'%3e%3cpath%20d='M97.0649%2041C89.2974%2041%2083%2047.3145%2083%2055.1032C83%2062.8919%2089.2974%2069.2064%2097.0649%2069.2064C104.832%2069.2064%20111.13%2062.8919%20111.13%2055.1032C111.13%2047.3145%20104.832%2041%2097.0649%2041ZM97.0649%2066.4349C90.8215%2066.4349%2085.764%2061.3587%2085.7689%2055.0983C85.7689%2048.8378%2090.8313%2043.7666%2097.0747%2043.7715C103.313%2043.7715%20108.371%2048.8477%20108.371%2055.1032C108.381%2061.3587%20103.333%2066.4398%2097.0943%2066.4496C97.0845%2066.4496%2097.0747%2066.4496%2097.0649%2066.4496V66.4349Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M98.0694%2055.1179C98.0842%2055.6732%2097.6431%2056.1351%2097.0893%2056.1499C96.5355%2056.1646%2096.0749%2055.7224%2096.0602%2055.1671C96.0455%2054.6118%2096.4865%2054.1499%2097.0403%2054.1351C97.0501%2054.1351%2097.055%2054.1351%2097.0648%2054.1351C97.6088%2054.1351%2098.0547%2054.5725%2098.0694%2055.1179Z'%20fill='%23F0EDF7'/%3e%3cpath%20d='M93.6982%2060.2334L97.065%2055.1179L102.755%2059.8403'%20stroke='%23F0EDF7'%20stroke-miterlimit='10'/%3e%3c/g%3e%3cpath%20d='M128.944%2062.5124L120.215%2059.6301'%20stroke='black'/%3e%3cpath%20d='M132.995%2059.1223L134.128%2050'%20stroke='black'/%3e%3cpath%20d='M130.394%2059.4708L123.799%2053.068'%20stroke='black'/%3e%3cpath%20d='M151.766%20124.273C155.074%20124.075%20157.582%20121.01%20157.367%20117.426C157.152%20113.843%20154.297%20111.098%20150.989%20111.296C147.681%20111.494%20145.174%20114.56%20145.389%20118.143C145.603%20121.727%20148.459%20124.471%20151.766%20124.273Z'%20fill='%23263238'/%3e%3cpath%20d='M200.679%20121.335C203.986%20121.137%20206.494%20118.072%20206.279%20114.488C206.065%20110.905%20203.209%20108.161%20199.902%20108.359C196.594%20108.557%20194.086%20111.622%20194.301%20115.206C194.515%20118.789%20197.371%20121.533%20200.679%20121.335Z'%20fill='%23263238'/%3e%3cpath%20d='M185.42%20123.26L185.029%20122.64C184.995%20122.584%20181.333%20116.941%20174.674%20117.34C168.615%20117.703%20167.43%20123.292%20167.404%20123.531L167.264%20124.24L165.774%20123.991L165.909%20123.277C165.905%20123.209%20167.307%20116.33%20174.587%20115.894C182.16%20115.441%20186.164%20121.624%20186.331%20121.89L186.723%20122.505L185.42%20123.26Z'%20fill='%23263238'/%3e%3cpath%20opacity='0.22'%20d='M197.565%2060L197.533%2077.0053C197.531%2078.0775%20197.739%2079.1396%20198.147%2080.1312C198.555%2081.1227%20199.154%2082.0242%20199.911%2082.7841C200.667%2083.544%20201.566%2084.1475%20202.555%2084.56C203.545%2084.9726%20204.606%2085.1861%20205.678%2085.1885L222.704%2085.222L197.565%2060Z'%20fill='%23407BFF'/%3e%3cpath%20d='M232%20182L226.978%20176.978'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cpath%20d='M214.065%20179.13C222.386%20179.13%20229.13%20172.386%20229.13%20164.065C229.13%20155.745%20222.386%20149%20214.065%20149C205.745%20149%20199%20155.745%20199%20164.065C199%20172.386%20205.745%20179.13%20214.065%20179.13Z'%20stroke='%234B5768'%20stroke-width='4'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cdefs%3e%3clinearGradient%20id='paint0_linear_12419_6513'%20x1='180.324'%20y1='57.9809'%20x2='171.927'%20y2='189.862'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23CACAFC'/%3e%3cstop%20offset='1'%20stop-color='%237D7DD6'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint1_linear_12419_6513'%20x1='61.5771'%20y1='70.7592'%20x2='72.0695'%20y2='70.7592'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint2_linear_12419_6513'%20x1='260.446'%20y1='138.892'%20x2='271.924'%20y2='138.892'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint3_linear_12419_6513'%20x1='273.526'%20y1='150.44'%20x2='287.998'%20y2='150.44'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3clinearGradient%20id='paint4_linear_12419_6513'%20x1='251.708'%20y1='156.548'%20x2='270.1'%20y2='156.548'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23FFEFE9'/%3e%3cstop%20offset='0.34'%20stop-color='%23E1E1D0'/%3e%3cstop%20offset='0.76'%20stop-color='%23C3D3B8'/%3e%3cstop%20offset='0.99'%20stop-color='%23B8CEAF'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e",He=s=>he.colorPalleteDashboard[s]||he.colorPalleteDashboard.default,et=s=>s===ne.PIE||s===ne.DONUT,Qe=s=>G2.includes(s),Ue=(s,p)=>{var x,d;if(!s)return s;if(s.data&&Array.isArray(s.data)){const h=((d=(x=s.data[0])==null?void 0:x.data)==null?void 0:d.map(D=>D.x))||[],_=s.data.map(D=>({name:D.name,data:D.data.map(P=>P.y)}));return{...s,categories:h,series:_,isStacked:Qe(p),graphDetails:{...s.graphDetails,chartType:p}}}return{...s,isStacked:Qe(p),graphDetails:{...s.graphDetails,chartType:p}}},tt=({title:s,data:p=[],handleOpenDialog:x=()=>{},isLoading:d=!1,onRefresh:h=()=>{}})=>{var W,Z;const _=i2();y2(_.breakpoints.up("md"));const[D,P]=u.useState(!1),[A,L]=u.useState(null),{t:M}=be(),K=(W=p==null?void 0:p.graphDetails)==null?void 0:W.chartType;u.useMemo(()=>{D&&!A&&L(K)},[D,K,A]);const O=f=>!!f&&Object.keys(f).length>0,U=()=>{P(!0),L(K)},I=()=>{P(!1),L(null)},w=f=>{L(f.target.value)},y=u.useMemo(()=>{var T;if(!A||!p)return p;const f=Ue(p,A);return(T=f==null?void 0:f.graphDetails)!=null&&T.graphName&&(f.graphDetails.graphName=M(f.graphDetails.graphName)),f},[p,A,M]),v=()=>{var F,k,R,j;if(d){const H=Ue(p,K);(F=H==null?void 0:H.graphDetails)!=null&&F.graphName&&(H.graphDetails.graphName=M(H.graphDetails.graphName));const Q=He((k=H==null?void 0:H.graphDetails)==null?void 0:k.colorPallete);return e(ke,{values:H,isTable:!0,showDownload:!0,showGraphName:!0,graphColor:Q,isLoading:d})}if(!O(p))return q();const f=Ue(p,K);(R=f==null?void 0:f.graphDetails)!=null&&R.graphName&&(f.graphDetails.graphName=M(f.graphDetails.graphName));const T=He((j=f==null?void 0:f.graphDetails)==null?void 0:j.colorPallete);return e(ke,{values:f,isTable:!0,showDownload:!0,showGraphName:!0,graphColor:T,isLoading:d})},q=()=>m("div",{style:{textAlign:"center"},children:[e("img",{alt:M("No Data Found"),style:{height:"250px"},src:J2}),e(B,{variant:"h6",style:{marginTop:"10px"},children:M("No Data Found")})]});return m(Me,{children:[e(g2,{sx:{borderRadius:"10px",boxShadow:1,height:"auto",display:"flex",flexDirection:"column"},children:m(m2,{children:[m(Ce,{justifyContent:"flex-end",alignItems:"center",direction:"row",spacing:1,children:[e(De,{size:"small",onClick:h,title:"Refresh",disabled:d,sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:e(M2,{fontSize:"small"})}),e(De,{size:"small",onClick:U,title:"Maximize",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:e(L2,{fontSize:"small"})})]}),v()]})}),m(f2,{open:D,onClose:I,fullWidth:!0,maxWidth:"lg",PaperProps:{sx:{height:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},maxHeight:{xs:"100vh",sm:"100vh",md:"80vh",lg:"80vh",xl:"80vh"},borderRadius:"10px",margin:{xs:"0",md:"24px"}}},children:[m(D2,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"16px 24px",borderBottom:`1px solid ${he.dialog.borderBottom}`},children:[m(Ce,{direction:"row",spacing:2,alignItems:"center",children:[e(B,{variant:"h6",children:(Z=p==null?void 0:p.graphDetails)!=null&&Z.graphName?M(p.graphDetails.graphName):M("Chart View")}),O(p)&&e(fe,{size:"small",sx:{minWidth:120},children:e(ye,{value:A||"",onChange:w,displayEmpty:!0,variant:"outlined",sx:{height:"32px"},children:et(K)?A2.map(f=>e(xe,{value:f.value,children:f.label},f.value)):T2.map(f=>e(xe,{value:f.value,children:f.label},f.value))})})]}),e(De,{onClick:I,size:"small",children:e(K2,{})})]}),e(b2,{sx:{display:"flex",flexDirection:"column",padding:"24px",height:"calc(100% - 64px)",overflow:"hidden"},children:e("div",{style:{flex:1,display:"flex",flexDirection:"column",height:"100%",overflow:"hidden"},children:O(p)?(()=>{var T;const f=He((T=y==null?void 0:y.graphDetails)==null?void 0:T.colorPallete);return e(ke,{values:y,isTable:!0,showDownload:!0,showGraphName:!0,height:"100%",graphColor:f})})():q()})})]})]})},I2=({cards:s=[],loading:p,graphLoadingStates:x={},onRefreshGraph:d=()=>{},isTabbed:h=!1,userPreferences:_=[]})=>{if(p)return e($,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Le,{})});if(!s.length)return e($,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(B,{variant:"h6",color:"text.secondary",children:"No charts available. Please configure your dashboard."})});const D=[...s].sort((P,A)=>{var L,M;if(h){const K=P.kpiId||P.id||`KPI_${P.id}`,O=A.kpiId||A.id||`KPI_${A.id}`,U=_.find(v=>v.KpiId===K||v.KpiId===P.kpiId||v.KpiId===P.id||v.KpiId===`KPI_${P.id}`),I=_.find(v=>v.KpiId===O||v.KpiId===A.kpiId||v.KpiId===A.id||v.KpiId===`KPI_${A.id}`),w=(U==null?void 0:U.SecKpiSequence)??999,y=(I==null?void 0:I.SecKpiSequence)??999;return w-y}else{const K=P.GraphSequence||((L=P.graphDetails)==null?void 0:L.KpiSequence)||P.Sequence||0,O=A.GraphSequence||((M=A.graphDetails)==null?void 0:M.KpiSequence)||A.Sequence||0;return K-O}});return e(E,{container:!0,spacing:2,children:D.map((P,A)=>{var K;const L=P.kpiId||P.id,M=x[L]||!1;return e(E,{item:!0,xs:12,md:6,lg:4,children:e(tt,{title:(K=P.graphDetails)==null?void 0:K.graphName,data:P,isLoading:M,onRefresh:()=>d(L)})},P.id)})})};var We={},nt=N2;Object.defineProperty(We,"__esModule",{value:!0});var _2=We.default=void 0,at=nt(v2()),rt=B2;_2=We.default=(0,at.default)((0,rt.jsx)("path",{d:"m20.41 8.41-4.83-4.83c-.37-.37-.88-.58-1.41-.58H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V9.83c0-.53-.21-1.04-.59-1.42M7 7h7v2H7zm10 10H7v-2h10zm0-4H7v-2h10z"}),"TextSnippet");const st={display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:"1px solid #eee",padding:"15px 0",marginBottom:"10px",backgroundColor:"#f9f9f9",borderRadius:"8px",paddingLeft:"15px",paddingRight:"15px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",transition:"transform 0.2s, box-shadow 0.2s","&:hover":{transform:"translateY(-2px)",boxShadow:"0 4px 8px rgba(0,0,0,0.2)"}},it={display:"flex",justifyContent:"center",alignItems:"center",gap:1,fontWeight:"500",color:"#555"};he.primary.white,he.tab.background;const lt={marginLeft:"10px",transition:"all 0.3s ease-in-out","&:hover":{backgroundColor:"#1976d2",color:"#fff",transform:"scale(1.1)"}},Xe=["#4dc9f6","#f67019","#8549ba","#f53050","#537bc4","#acc236","#166a8f","#00a950","#58595b","#ff6384","#36a2eb","#ffce56","#cc65fe","#ff9f40","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"],ot=Oe(r2)(({theme:s})=>({marginTop:"0px !important",border:`1px solid ${he.primary.border}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}})),ct=Oe(s2)(({theme:s})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:s.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:s.palette.primary.light}})),R2=({reportConfig:s=[],kpiReportPrefs:p=[],loading:x})=>{var me;const d=X(S=>S.commonFilter.Reports),h=X(S=>S.commonFilter.RequestBench),_=X(S=>S.AllDropDown.dropDown),[D,P]=u.useState([]),[A,L]=u.useState([]),[M,K]=u.useState([...d.reportDate]),[O,U]=u.useState([]),[I,w]=u.useState([]),[y,v]=u.useState([]),[q,W]=u.useState(!1),[Z,f]=u.useState(""),T=ze(),F=X(S=>S.userManagement.userData),k=(F==null?void 0:F.user_id)||"",{t:R}=be(),j=X(S=>S.commonFilter.Dashboard);u.useEffect(()=>{if(d!=null&&d.reportDate){const S=new Date(d==null?void 0:d.reportDate[0]),t=new Date(d==null?void 0:d.reportDate[1]);K([S,t])}},[d==null?void 0:d.reportDate]),u.useEffect(()=>{J(),le()},[]);const H=["US","EUR"],[Q,z]=u.useState([]),re=()=>{z(Q.length===H.length?[]:H)},le=()=>{ie(`/${ae}/GraphConfig/getReqStatuses`,"get",S=>v((S==null?void 0:S.body)||[]),()=>{})},J=()=>{ie(`/${ae}/GraphConfig/getReqTypes`,"get",S=>w((S==null?void 0:S.body)||[]),()=>{})},g=(S,t,i)=>{f(w2.REPORT_LOADING),W(!0),ie(`/${ae}/excel${S}`,"postandgetblob",n=>{const a=URL.createObjectURL(n),l=document.createElement("a");l.href=a,l.setAttribute("download",t),document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(a),W(!1),f("")},()=>{W(!1),f("")},i)},N=S=>{T(pe({module:"Reports",filterData:{...d,reportDate:S}}))},de=()=>{z([]),U([]),P([]),L([]);const S=new Date,t=new Date;t.setDate(t.getDate()-3650),K([t,S]),T(C2({module:"Reports"}))};return m(Me,{children:[e(E,{item:!0,md:12,children:m(ot,{defaultExpanded:!1,sx:{marginTop:"5px !important",marginLeft:"25px !important",marginRight:"20px !important"},children:[m(ct,{expandIcon:e(l2,{sx:{fontSize:"1.25rem",color:he.primary.main}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[e(o2,{sx:{fontSize:"1.25rem",marginRight:1,color:he.primary.main}}),e(B,{sx:{fontSize:"0.875rem",fontWeight:600,color:he.primary.dark},children:R("Filter Reports")})]}),m(c2,{children:[m(E,{container:!0,rowSpacing:1,spacing:2,children:[m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:R("Date Range")}),e(fe,{fullWidth:!0,sx:{padding:0,height:"37px"},children:e(d2,{dateAdapter:u2,children:e(p2,{handleDate:N,cleanDate:!1,date:M})})})]}),m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:R("Region")}),e(Fe,{options:[...(me=H==null?void 0:H.filter(S=>S!=="Select All"))==null?void 0:me.sort((S,t)=>typeof S=="string"&&typeof t=="string"?S.localeCompare(t):0)],value:Q,onChange:S=>{var t;S.length>0&&((t=S[S.length-1])==null?void 0:t.label)==="Select All"?re():z(S)},placeholder:"Select Region"})]}),m(E,{item:!0,md:2,children:[e(B,{sx:_e,children:R("Division")}),e(z2,{matGroup:(_==null?void 0:_.Division)??[],selectedMaterialGroup:D,setSelectedMaterialGroup:S=>{if(!S||S.length===0){P([]);return}P(S)},placeholder:"Select Division"})]})]}),e(E,{container:!0,style:{display:"flex",justifyContent:"flex-end"},children:e(E,{item:!0,style:{display:"flex",justifyContent:"space-around"},children:e(Ee,{variant:"outlined",sx:{...h2},onClick:de,children:R("Clear")})})})]})]})}),e(E,{item:!0,md:12,children:e(Ce,{justifyContent:"space-between",direction:"row",children:e(E,{container:!0,spacing:2,children:e(E,{item:!0,md:12,children:e(g2,{sx:{borderRadius:"10px",boxShadow:4,height:{xs:"calc(70vh - 100px)",md:"calc(75vh - 100px)",lg:"calc(80vh - 130px)"},marginLeft:"25px",marginTop:"20px",marginRight:"20px"},children:m(m2,{children:[e(B,{variant:"h6",sx:{fontWeight:"bold",marginBottom:"20px"},children:R("Application Report List")}),m(Ce,{spacing:2,sx:{height:{xs:"calc(70vh - 160px)",md:"calc(75vh - 160px)",lg:"calc(80vh - 190px)"},overflowY:"auto",overflowX:"hidden","&::-webkit-scrollbar":{width:"4px"},"&::-webkit-scrollbar-thumb":{backgroundColor:"rgba(0,0,0,0.2)",borderRadius:"4px"}},children:[x&&e($,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Le,{})}),e(E,{container:!0,spacing:1,children:s.filter(S=>{const t=p.find(i=>i.KpiId===S.MDG_KPI_ID);return(t==null?void 0:t.KpiVisibility)===!0&&(t==null?void 0:t.IsActive)===!0}).sort((S,t)=>+S.MDG_KPI_GRAPH_SEQUENCE-+t.MDG_KPI_GRAPH_SEQUENCE).map((S,t)=>{var n,a,l,o,c;const i={FromDate:qe(((n=d==null?void 0:d.reportDate)==null?void 0:n[0])??((a=h==null?void 0:h.createdOn)==null?void 0:a[0])).format("YYYY-MM-DD"),ToDate:qe(((l=d==null?void 0:d.reportDate)==null?void 0:l[1])??((o=h==null?void 0:h.createdOn)==null?void 0:o[1])).format("YYYY-MM-DD"),Requestor:"",KpiId:S.MDG_KPI_ID,Module:we(j==null?void 0:j.dashBoardModuleName)||Re[0],UserId:k,Priority:"",Region:Q.join(","),ReqType:O.join(","),ReqStatus:A.join(","),GraphType:S.MDG_KPI_GRAPH_TYPE||"",KpiName:S.MDG_KPI_NAME||"",ColPallet:S.MDG_KPI_COLOR_PALLET||"",GraphColumn:((c=S.MDG_KPI_GRAPH_COLUMN)==null?void 0:c.toLowerCase())||"",GraphSequence:S.MDG_KPI_GRAPH_SEQUENCE||""};return e(E,{item:!0,xs:6,sx:{paddingRight:"8px"},children:m($,{sx:{...st,width:"100%",boxSizing:"border-box"},children:[m(B,{variant:"body1",sx:{...it,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[e(_2,{sx:{color:Xe[t%Xe.length],marginRight:"4px"}}),R(S.MDG_KPI_NAME)]}),e(Ee,{variant:"outlined",sx:lt,onClick:()=>g(S.MDG_KPI_ENDPOINT,`${S.MDG_KPI_NAME}.xlsx`,i),children:R("Download")})]})},S.MDG_KPI_ID)})})]})]})})})})})}),e(q2,{blurLoading:q,loaderMessage:Z})]})},dt=({children:s,value:p,index:x,...d})=>e("div",{role:"tabpanel",hidden:p!==x,id:`dashboard-tabpanel-${x}`,"aria-labelledby":`dashboard-tab-${x}`,...d,children:p===x&&e($,{sx:{p:3},children:s})}),ut=({sectionedCards:s={},sectionedReports:p={},loading:x=!1,showReports:d=!1,kpiReportPrefs:h=[],graphLoadingStates:_={},onRefreshGraph:D=()=>{},decisionTableConfig:P=[],userPreferences:A=[]})=>{const{t:L}=be(),[M,K]=u.useState(0),U=(()=>Object.keys(d?p:s).sort((q,W)=>{const Z=A.filter(k=>{const R=P.find(H=>H.MDG_KPI_ID===k.KpiId);return((R==null?void 0:R.MDG_KPI_SECTION_NAME)||"General")===q}),f=A.filter(k=>{const R=P.find(H=>H.MDG_KPI_ID===k.KpiId);return((R==null?void 0:R.MDG_KPI_SECTION_NAME)||"General")===W}),T=Z.length>0?Math.min(...Z.map(k=>k.SectionSequence||0)):0,F=f.length>0?Math.min(...f.map(k=>k.SectionSequence||0)):0;return T-F}))();if(U.length===0)return x?e($,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Le,{})}):e($,{sx:{p:2},children:e(B,{variant:"h6",color:"textSecondary",children:L(se.NO_DATA_AVAILABLE)})});const I=(y,v)=>{K(v)},w=y=>({id:`dashboard-tab-${y}`,"aria-controls":`dashboard-tabpanel-${y}`});return x?e($,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:e(Le,{})}):m($,{sx:{width:"100%"},children:[e($,{sx:{borderBottom:1,borderColor:"divider"},children:e(S2,{value:M,onChange:I,"aria-label":"dashboard sections",variant:"scrollable",scrollButtons:"auto",children:U.map((y,v)=>e(je,{label:L(y),...w(v)},y))})}),U.map((y,v)=>e(dt,{value:M,index:v,children:d?e(R2,{reportConfig:p[y]||[],kpiReportPrefs:h,loading:!1},`reports-${y}`):e(I2,{cards:s[y]||[],loading:!1,graphLoadingStates:_,onRefreshGraph:D,isTabbed:!0,userPreferences:A},`charts-${y}`)},y))]})},te={Open:"Open",PendingConfirmation:"PendingConfirmation",Confirmed:"Confirmed",Delivered:"Delivered",Blocked:"Blocked",ProductionInProgress:"ProductionInProgress",ProductionCompleted:"ProductionCompleted",PendingASN:"PendingASN",OverdueShipment:"OverdueShipment",PendingReturns:"PendingReturns",OnTimeDelivery:"OnTimeDelivery",OrderFillRate:"OrderFillRate",ReadyToINV:"ReadyToINV",ReadyToPostINV:"ReadyToPostINV",PostedINV:"PostedINV",PaidINV:"PaidINV",UnpaidINV:"UnpaidINV",RejectedINV:"RejectedINV",MaterialQuantity:"MaterialQuantity",MaterialValue:"MaterialValue",POStatus:"POStatus",INVStatus:"INVStatus",PaymentStatus:"PaymentStatus",ProductionStatus:"ProductionStatus",SRStatus:"SRStatus",SRPriority:"SRPriority",DeliveryDelay:"DeliveryDelay",PlanningTask:"PlanningTask",PendingAck:"PendingAck",PendingConsumption:"PendingConsumption",PendingPlanning:"PendingPlanning",SubmiitedAck:"SubmiitedAck",ConfirmationSubmitted:"ConfirmationSubmitted",SubmittedASN:"SubmittedASN",SubmittedConsumption:"SubmittedConsumption",ConsumptionSummary:"ConsumptionSummary",ConsumptionByPO:"ConsumptionByPO",ConsumptionByASN:"ConsumptionByASN",ConsumptionByMaterial:"ConsumptionByMaterial",MaterialGroup:"MaterialGroup",PlanningTable:"PlanningTable",Change:"Change",Create:"Create",Extend:"Extend",MassExtend:"MassExtend",MassChange:"MassChange",MassCreate:"MassCreate",pieStatus:"pieStatus",ExtendTable:"ExtendTable",ExtendTableHeader:"ExtendTableHeader",OnboardBar:"OnboardBar",FormToSupplier:"FormToSupplier",FinanceReview:"FinanceReview",ProcurementLeadReview:"ProcurementLeadReview",BuyerReview:"BuyerReview",ComplianceReview:"ComplianceReview",Completed:"Completed",dashboardDate:"dashboardDate",CycleTime:"CycleTime",BottleNeck:"BottleNeck",BottleNeckTable:"BottleNeckTable",BottleNeckTable2:"BottleNeckTable2",BottleNeckGraph:"BottleNeckGraph",ReviewPending:"ReviewPending",Approved:"Approved",CorrectionPending:"CorrectionPending",ApprovalPending:"ApprovalPending",PlanningTaskContent:"PlanningTaskContent",PlanningTaskLength:"PlanningTaskLength",PlanningTaskHeader:"PlanningTaskHeader",requestItemLength:"requestItemLength",requestTypeGraph:"requestTypeGraph",BasedOnGroupGraph:"BasedOnGroupGraph",topFiveSlaBreached:"topFiveSlaBreached",slaRequestType:"slaRequestType",slaRequestTypeContent:"slaRequestTypeContent",selectedRequestTypeSLATable:"selectedRequestTypeSLATable",selectedRequestTypeRole:"selectedRequestTypeRole"},Se=(s,p)=>{var x=p===void 0?.5:1-p;return x2(s).alpha(x).rgbString()},Je={BUPA:{product:!0,TabPanel:[{uid:0,name:"Status by Scenario",icon:"HowToReg",required:!0},{uid:1,name:"Bottleneck",icon:"RunningWithErrors",required:!0}],Tiles:[,{uid:0,name:"Create",count:te.Create,required:!0,width:2,type:"RBEC",status:"Create",color:Se("#4dc9f6",.7),borderColor:"#4dc9f6"},{uid:0,name:"Change",count:te.Change,required:!0,width:2,type:"RBEC",status:"Change",color:Se("#f6d55c",.7),borderColor:"#f6d55c"},{uid:0,name:"Extend",count:te.Extend,required:!0,width:2,type:"RBEC",status:"Extend",color:Se("#537bc4",.7),borderColor:"#537bc4"},{uid:0,name:"Create With Upload",count:te.MassCreate,required:!0,width:2,type:"RBEC",status:"Mass Create",color:Se("#00a950",.7),borderColor:"#00a950"},{uid:0,name:"Change With Upload",count:te.MassChange,required:!0,width:2,type:"RBEC",status:"Mass Change",color:Se("#8549ba",.7),borderColor:"#8549ba"},{uid:0,name:"Extend With Upload",count:te.MassExtend,required:!0,width:2,type:"RBEC",status:"Mass Extend",color:Se("#ff6384",.7),borderColor:"#ff6384"}],Graphs:[{uid:1,id:1,name:"Time Log Based on Roles",count:te.OnboardBar,required:!0,stackedBar:!0,isStacked:!0,xaxis:"status",yaxis:"statusCount",yaxisHeader:"Requests",type:"po",width:12},{uid:0,id:1,name:"Status ",count:te.pieStatus,required:!0,xaxis:"status",yaxis:"statusCount",type:"RBEC",isPie:!0,width:6},{uid:0,id:0,name:"Extend Table",count:te.ExtendTable,required:!0,width:6,isTable:!0},{uid:2,id:1010,name:"Extend Table",count:te.BottleNeckTable,required:!0,width:6,isTable2:!0,isTable:!1},{uid:2,id:1011,name:"Extend Table",count:te.BottleNeckTable2,required:!0,width:6,isTable3:!0,isTable2:!1,isTable:!1},{uid:2,id:1091,name:"",count:te.BottleNeckGraph,required:!0,width:12,isTable3:!1,isTable2:!1,isTable:!1,isgroup:!0}]}},pt=()=>{var U;const s=(((U=O2)==null?void 0:U.system)==="CHWSCP",Je.BUPA),p=s.Tiles.reduce((I,w)=>(I[w.count]=!0,I),{}),{t:x}=be(),d=s.Tiles.reduce((I,w)=>(I[w.count]=0,I),{}),h=X(I=>I==null?void 0:I.commonFilter.Dashboard),_=X(I=>I.commonFilter.RequestBench);u.useState(p),u.useState(d);const[D,P]=u.useState([]),A=ze(),L=k2(),M=(I,w)=>{var y=w===void 0?.5:1-w;return x2(I).alpha(y).rgbString()},K=I=>{let w={..._,requestType:I};A(pe({module:"RequestBench",filterData:w})),L("/requestBench")};u.useEffect(()=>{let I=new FormData;I.append("fromDate",qe(h!=null&&h.dashboardDate[0]?h==null?void 0:h.dashboardDate[0]:_==null?void 0:_.createdOn[0]).format("YYYY-MM-DD")+" 00:00:00"),I.append("toDate",qe(h!=null&&h.dashboardDate[1]?h==null?void 0:h.dashboardDate[1]:_==null?void 0:_.createdOn[1]).format("YYYY-MM-DD")+" 23:59:59"),I.append("module",we(h==null?void 0:h.dashBoardModuleName)||Re[0]),I.append("userId","");const w=v=>{P(v.body)},y=()=>{};ie(`/${ae}${ge.DASHBOARD_APIS.KPI_CARDS}`,"postformdata",w,y,I)},[h]);const O={Create:M("#4dc9f6",.7),Change:M("#f6d55c",.7),Extend:M("#537bc4",.7),"Create With Upload":M("#00a950",.7),"Change With Upload":M("#8549ba",.7),"Extend With Upload":M("#ff6384",.7)};return e(E,{container:!0,spacing:2,sx:{mt:2},className:"kpiCard",children:D==null?void 0:D.map(I=>e(Me,{children:e(E,{item:!0,xs:12,sm:6,md:4,lg:2,children:e(E,{onClick:()=>K(I.status),children:e(H2,{events:{allow:!0,type:"option"},value:I==null?void 0:I.statusCount,graphName:x(I==null?void 0:I.status),KPIColor:O[I.status]})})})}))})},E2=s=>s?s.split(",").map(p=>({value:p.trim(),label:p.trim()})):[],ht=s=>({Bar:ne.BAR,"Stacked Bar":ne.STACKED_BAR,Column:ne.COLUMN,"Stacked Column":ne.STACK_COLUMN,Line:ne.LINE,"Stacked Line":ne.STACKED_LINE,Area:ne.AREA,"Stacked Area":ne.STACKED_AREA,Pie:ne.PIE,Donut:ne.DONUT})[s]||s.toUpperCase().replace(/\s+/g,"_"),e2=(s,p)=>{const x=p==null?void 0:p.find(d=>d.MDG_KPI_ID===s);return x!=null&&x.MDG_KPI_GRAPH_OPTIONS?E2(x.MDG_KPI_GRAPH_OPTIONS).map(d=>({value:ht(d.value),label:d.label})):[]},t2=(s,p)=>{const x=p==null?void 0:p.find(d=>d.MDG_KPI_ID===s);return x!=null&&x.MDG_KPI_PALLET_OPTIONS?E2(x.MDG_KPI_PALLET_OPTIONS):[]};function n2(s){const{children:p,value:x,index:d,...h}=s;return e("div",{role:"tabpanel",hidden:x!==d,id:`dashboard-tabpanel-${d}`,"aria-labelledby":`dashboard-tab-${d}`,...h,children:x===d&&e($,{sx:{p:1},children:p})})}const Ct=({open:s,onClose:p,onSave:x,decisionTableConfig:d,userPreferences:h,reportConfig:_,onRefreshSpecificGraphs:D})=>{const[P,A]=u.useState(0),[L,M]=u.useState([]),[K,O]=u.useState([]),[U,I]=u.useState(!0),[w,y]=u.useState(!1),[v,q]=u.useState({}),[W,Z]=u.useState({}),[f,T]=u.useState({}),[F,k]=u.useState("section"),{t:R}=be(),j=X(t=>t.userManagement.userData),H=(j==null?void 0:j.user_id)||"",Q=(t,i)=>{A(i)},z=t=>{if(!t.destination)return;const{source:i,destination:n,type:a}=t;a==="KPI_METRICS"?re(i,n):a==="KPI_REPORTS"&&le(i,n)},re=(t,i)=>{if(F==="section"){const n=t.droppableId,a=i.droppableId;if(n===a){const o=L.filter(C=>(C.section||"General")===n).sort((C,G)=>(C.secKpiSequence||0)-(G.secKpiSequence||0)),[c]=o.splice(t.index,1);o.splice(i.index,0,c);const b=L.map(C=>{if((C.section||"General")===n){const G=o.findIndex(V=>V.id===C.id);return{...C,secKpiSequence:G+1}}return C});M(b);const r={};o.forEach(C=>{r[C.id]=!0}),q(C=>({...C,...r}))}}else{const n=[...L].sort((c,b)=>(c.sequence||0)-(b.sequence||0)),[a]=n.splice(t.index,1);n.splice(i.index,0,a);const l=n.map((c,b)=>({...c,sequence:b+1}));M(l);const o={};l.forEach(c=>{o[c.id]=!0}),q(c=>({...c,...o}))}},le=(t,i)=>{if(F==="section"){const n=t.droppableId,a=i.droppableId;if(n===a){const o=K.filter(C=>(C.section||"General")===n).sort((C,G)=>(C.secKpiSequence||0)-(G.secKpiSequence||0)),[c]=o.splice(t.index,1);o.splice(i.index,0,c);const b=K.map(C=>{if((C.section||"General")===n){const G=o.findIndex(V=>V.id===C.id);return{...C,secKpiSequence:G+1}}return C});O(b);const r={};o.forEach(C=>{r[C.id]=!0}),Z(C=>({...C,...r}))}}else{const n=[...K].sort((c,b)=>(c.sequence||0)-(b.sequence||0)),[a]=n.splice(t.index,1);n.splice(i.index,0,a);const l=n.map((c,b)=>({...c,sequence:b+1}));O(l);const o={};l.forEach(c=>{o[c.id]=!0}),Z(c=>({...c,...o}))}},J=u.useCallback(()=>{if((d==null?void 0:d.length)>0){const t=d.map(n=>{var l;const a=h==null?void 0:h.find(o=>o.KpiId===n.MDG_KPI_ID);return{id:n.MDG_KPI_ID,prefId:(a==null?void 0:a.Id)||null,name:n.MDG_KPI_NAME,enabled:String(n.MDG_KPI_VISIBILITY).toLowerCase()==="true",userEnabled:a?a.KpiVisibility===!0&&a.IsActive===!0:!1,sequence:(a==null?void 0:a.KpiSequence)||n.MDG_KPI_GRAPH_SEQUENCE||0,sectionSequence:(a==null?void 0:a.SectionSequence)||0,secKpiSequence:(a==null?void 0:a.SecKpiSequence)||0,chartType:(a==null?void 0:a.KpiChartType)||n.MDG_KPI_GRAPH_TYPE,column:(l=n.MDG_KPI_GRAPH_COLUMN)==null?void 0:l.toLowerCase(),colorPallet:(a==null?void 0:a.KpiColPallet)||n.MDG_KPI_COLOR_PALLET,section:n.MDG_KPI_SECTION_NAME||"General"}}),i=_.map(n=>{const a=h==null?void 0:h.find(l=>l.KpiId===n.MDG_KPI_ID);return{id:n.MDG_KPI_ID,prefId:(a==null?void 0:a.Id)||null,name:n.MDG_KPI_NAME,enabled:["true","enabled"].includes(String(n.MDG_KPI_VISIBILITY).toLowerCase()),userEnabled:a?a.KpiVisibility===!0&&a.IsActive===!0:!0,sequence:(a==null?void 0:a.KpiSequence)||n.MDG_KPI_GRAPH_SEQUENCE||0,sectionSequence:(a==null?void 0:a.SectionSequence)||0,secKpiSequence:(a==null?void 0:a.SecKpiSequence)||0,section:n.MDG_KPI_SECTION_NAME||"General"}});M(t),O(i),q({}),Z({}),T({}),I(!1)}},[d,h,_]);u.useEffect(()=>{s?(I(!0),J()):A(0)},[s,J]);const g=t=>{M(i=>i.map(n=>n.id===t?{...n,userEnabled:!n.userEnabled}:n)),T(i=>({...i,[t]:!0})),q(i=>({...i,[t]:!0}))},N=t=>{O(i=>i.map(n=>n.id===t?{...n,userEnabled:!n.userEnabled}:n)),Z(i=>({...i,[t]:!0}))},de=(t,i)=>{M(n=>n.map(a=>a.id===t?{...a,chartType:i}:a)),q(n=>({...n,[t]:!0}))},me=(t,i)=>{M(n=>n.map(a=>a.id===t?{...a,colorPallet:i}:a)),q(n=>({...n,[t]:!0}))},S=async()=>{y(!0);try{const t=Object.keys(v),i=Object.keys(W);if(t.length===0&&i.length===0){p();return}const n=L.filter(r=>t.includes(r.id)).map(r=>({Id:r.prefId,UserId:H,KpiId:r.id,KpiType:"KPI Metrics",KpiChartType:r.chartType,KpiChartName:r.name,KpiColPallet:r.colorPallet,KpiSequence:Number(r.sequence),SectionSequence:Number(r.sectionSequence),SecKpiSequence:Number(r.secKpiSequence),KpiColumn:r.column,KpiVisibility:r.userEnabled,IsActive:r.userEnabled})),a=K.filter(r=>i.includes(r.id)).map(r=>({Id:r.prefId,UserId:H,KpiId:r.id,KpiType:"KPI Reports",KpiChartType:"REPORT",KpiChartName:r.name,KpiColPallet:"",KpiSequence:0,SectionSequence:0,SecKpiSequence:0,KpiColumn:"",KpiVisibility:r.userEnabled,IsActive:r.userEnabled})),l=[...n,...a];await new Promise((r,C)=>{ie(`/${ae}${ge.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",r,C,l)});const o=Object.keys(f),c=t.filter(r=>!o.includes(r)),b=o.length>0;!b&&D&&c.length>0&&D(c),x?x(t,i,b):p()}catch{p()}finally{y(!1)}};return m(f2,{open:s,onClose:p,fullWidth:!0,maxWidth:"md",children:[e(D2,{children:R("Manage Dashboard")}),e(b2,{dividers:!0,children:U?e($,{sx:{display:"flex",justifyContent:"center",p:3},children:e(Le,{})}):m(U2,{onDragEnd:z,children:[m($,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e(B,{variant:"h6",children:R("Graph Sequencing")}),m(V2,{value:F,exclusive:!0,onChange:(t,i)=>{i!==null&&k(i)},"aria-label":"sequencing mode",size:"small",children:[e(Ye,{value:"section","aria-label":"section-based",children:R("Section-Based")}),e(Ye,{value:"overall","aria-label":"overall",children:R("Overall Sequence")})]})]}),e($,{sx:{borderBottom:1,borderColor:"divider",mb:2},children:m(S2,{value:P,onChange:Q,"aria-label":"dashboard management tabs",children:[e(je,{label:R("KPI Metrics"),id:"dashboard-tab-0","aria-controls":"dashboard-tabpanel-0"}),e(je,{label:R("KPI Reports"),id:"dashboard-tab-1","aria-controls":"dashboard-tabpanel-1"})]})}),e(n2,{value:P,index:0,children:F==="section"?(()=>{const t=L.reduce((n,a)=>{const l=a.section||"General";return n[l]||(n[l]=[]),n[l].push(a),n},{});return Object.keys(t).sort((n,a)=>{var c,b;const l=((c=L.find(r=>r.section===n))==null?void 0:c.sectionSequence)||0,o=((b=L.find(r=>r.section===a))==null?void 0:b.sectionSequence)||0;return l-o}).map(n=>m($,{sx:{mb:3},children:[m(B,{variant:"h6",sx:{mb:2,color:"primary.main",fontWeight:"bold"},children:[R(n)," ",R("Section")]}),e(Ae,{droppableId:n,type:"KPI_METRICS",children:a=>m(Te,{...a.droppableProps,ref:a.innerRef,children:[t[n].sort((l,o)=>(l.secKpiSequence||0)-(o.secKpiSequence||0)).map((l,o)=>e(Ke,{draggableId:l.id,index:o,children:(c,b)=>m(Ge.Fragment,{children:[o>0&&e(ve,{}),e(Ne,{ref:c.innerRef,...c.draggableProps,sx:{backgroundColor:b.isDragging?"rgba(0, 0, 0, 0.1)":o%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:m(E,{container:!0,spacing:2,alignItems:"center",children:[e(E,{item:!0,xs:1,sx:{textAlign:"center"},children:e(B,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:o+1})}),e(E,{item:!0,xs:1,children:e(De,{size:"small",sx:{cursor:"grab"},...c.dragHandleProps,children:e(Be,{})})}),e(E,{item:!0,xs:3,children:e(B,{variant:"body1",sx:{fontWeight:l.enabled?"normal":"light",color:l.enabled?"text.primary":"text.disabled",p:1},children:R(l.name)})}),e(E,{item:!0,xs:2,children:e(fe,{fullWidth:!0,size:"small",children:e(ye,{value:l.chartType,onChange:r=>de(l.id,r.target.value),displayEmpty:!0,sx:{"& .MuiSelect-select":{fontWeight:l.enabled?"normal":"light",color:l.enabled?"text.primary":"text.disabled"}},children:e2(l.id,d).map(r=>e(xe,{value:r.value,children:r.label},r.value))})})}),m(E,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(B,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Ie,{checked:l.userEnabled,onChange:()=>g(l.id),disabled:!l.enabled,size:"small"})]}),e(E,{item:!0,xs:3,children:e(fe,{fullWidth:!0,size:"small",children:e(ye,{value:l.colorPallet||"default",onChange:r=>me(l.id,r.target.value),renderValue:r=>r||"Color Palette",displayEmpty:!0,children:t2(l.id,d).map(r=>e(xe,{value:r.value,children:r.label},r.value))})})})]})})]})},l.id)),a.placeholder]})})]},n))})():e(Ae,{droppableId:"overall-metrics",type:"KPI_METRICS",children:t=>m(Te,{...t.droppableProps,ref:t.innerRef,children:[L.sort((i,n)=>(i.sequence||0)-(n.sequence||0)).map((i,n)=>e(Ke,{draggableId:i.id,index:n,children:(a,l)=>m(Ge.Fragment,{children:[n>0&&e(ve,{}),e(Ne,{ref:a.innerRef,...a.draggableProps,sx:{backgroundColor:l.isDragging?"rgba(0, 0, 0, 0.1)":n%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:m(E,{container:!0,spacing:2,alignItems:"center",children:[e(E,{item:!0,xs:1,sx:{textAlign:"center"},children:e(B,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:n+1})}),e(E,{item:!0,xs:1,children:e(De,{size:"small",sx:{cursor:"grab"},...a.dragHandleProps,children:e(Be,{})})}),e(E,{item:!0,xs:3,children:e(B,{variant:"body1",sx:{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled",p:1},children:R(i.name)})}),e(E,{item:!0,xs:2,children:e(fe,{fullWidth:!0,size:"small",children:e(ye,{value:i.chartType,onChange:o=>de(i.id,o.target.value),displayEmpty:!0,sx:{"& .MuiSelect-select":{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled"}},children:e2(i.id,d).map(o=>e(xe,{value:o.value,children:o.label},o.value))})})}),m(E,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(B,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Ie,{checked:i.userEnabled,onChange:()=>g(i.id),disabled:!i.enabled,size:"small"})]}),e(E,{item:!0,xs:3,children:e(fe,{fullWidth:!0,size:"small",children:e(ye,{value:i.colorPallet||"default",onChange:o=>me(i.id,o.target.value),renderValue:o=>o||"Color Palette",displayEmpty:!0,children:t2(i.id,d).map(o=>e(xe,{value:o.value,children:o.label},o.value))})})})]})})]})},i.id)),t.placeholder]})})}),e(n2,{value:P,index:1,children:F==="section"?(()=>{const t=K.reduce((n,a)=>{const l=a.section||"General";return n[l]||(n[l]=[]),n[l].push(a),n},{});return Object.keys(t).sort((n,a)=>{var c,b;const l=((c=K.find(r=>r.section===n))==null?void 0:c.sectionSequence)||0,o=((b=K.find(r=>r.section===a))==null?void 0:b.sectionSequence)||0;return l-o}).map(n=>m($,{sx:{mb:3},children:[m(B,{variant:"h6",sx:{mb:2,color:"primary.main",fontWeight:"bold"},children:[R(n)," ",R("Reports")]}),e(Ae,{droppableId:n,type:"KPI_REPORTS",children:a=>m(Te,{...a.droppableProps,ref:a.innerRef,children:[t[n].sort((l,o)=>(l.secKpiSequence||0)-(o.secKpiSequence||0)).map((l,o)=>e(Ke,{draggableId:l.id,index:o,children:(c,b)=>m(Ge.Fragment,{children:[o>0&&e(ve,{}),e(Ne,{ref:c.innerRef,...c.draggableProps,sx:{backgroundColor:b.isDragging?"rgba(0, 0, 0, 0.1)":o%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:m(E,{container:!0,spacing:2,alignItems:"center",children:[e(E,{item:!0,xs:1,sx:{textAlign:"center"},children:e(B,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:o+1})}),e(E,{item:!0,xs:1,children:e(De,{size:"small",sx:{cursor:"grab"},...c.dragHandleProps,children:e(Be,{})})}),e(E,{item:!0,xs:8,children:e(B,{variant:"body1",sx:{fontWeight:l.enabled?"normal":"light",color:l.enabled?"text.primary":"text.disabled",p:1},children:R(l.name)})}),m(E,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(B,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Ie,{checked:l.userEnabled,onChange:()=>N(l.id),disabled:!l.enabled,size:"small"})]})]})})]})},l.id)),a.placeholder]})})]},n))})():e(Ae,{droppableId:"overall-reports",type:"KPI_REPORTS",children:t=>m(Te,{...t.droppableProps,ref:t.innerRef,children:[K.sort((i,n)=>(i.sequence||0)-(n.sequence||0)).map((i,n)=>e(Ke,{draggableId:i.id,index:n,children:(a,l)=>m(Ge.Fragment,{children:[n>0&&e(ve,{}),e(Ne,{ref:a.innerRef,...a.draggableProps,sx:{backgroundColor:l.isDragging?"rgba(0, 0, 0, 0.1)":n%2===0?"transparent":"rgba(0, 0, 0, 0.02)",borderRadius:1,mb:1,border:"1px solid",borderColor:"divider"},children:m(E,{container:!0,spacing:2,alignItems:"center",children:[e(E,{item:!0,xs:1,sx:{textAlign:"center"},children:e(B,{variant:"h6",color:"text.secondary",sx:{fontWeight:"bold"},children:n+1})}),e(E,{item:!0,xs:1,children:e(De,{size:"small",sx:{cursor:"grab"},...a.dragHandleProps,children:e(Be,{})})}),e(E,{item:!0,xs:8,children:e(B,{variant:"body1",sx:{fontWeight:i.enabled?"normal":"light",color:i.enabled?"text.primary":"text.disabled",p:1},children:R(i.name)})}),m(E,{item:!0,xs:2,sx:{textAlign:"center"},children:[e(B,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:"Enabled"}),e(Ie,{checked:i.userEnabled,onChange:()=>N(i.id),disabled:!i.enabled,size:"small"})]})]})})]})},i.id)),t.placeholder]})})})]})}),m(Z2,{children:[e(Ee,{onClick:p,disabled:w,children:R("Cancel")}),e(Ee,{onClick:S,variant:"contained",color:"primary",disabled:w||Object.keys(v).length===0&&Object.keys(W).length===0,children:R(w?"Saving...":"Save Changes")})]})]})},$e=s=>["First","Second","Third"][s%3],Ve=(s,p="metrics")=>{const x={};return s.forEach(d=>{const h=d.MDG_KPI_SECTION_NAME||"General";x[h]||(x[h]=[]),x[h].push(d)}),x},Ze=(s,p)=>{const x={};return s.forEach(d=>{const h=p.find(D=>D.MDG_KPI_ID===d.id||D.MDG_KPI_ID===`KPI_${d.id}`),_=(h==null?void 0:h.MDG_KPI_SECTION_NAME)||"General";x[_]||(x[_]=[]),x[_].push(d)}),x},a2=(s,p,x,d=null,h=0)=>{var O,U,I,w,y;if(!s||typeof s!="object"||Array.isArray(s))return null;const _=(O=s==null?void 0:s.graphDetails)==null?void 0:O.chartType,D=(I=(U=_==null?void 0:_.toString())==null?void 0:U.trim())==null?void 0:I.toUpperCase(),P=((w=s==null?void 0:s.graphDetails)==null?void 0:w.graphName)||"Untitled Chart",A=((y=s==null?void 0:s.graphDetails)==null?void 0:y.color)||"Pallet 1",L=(s==null?void 0:s.Sequence)||h||0,M=(s==null?void 0:s.id)||p,K=(s==null?void 0:s.column)||$e(M);if(!D||!P)return null;if(["PIE","DONUT"].includes(D)){const v=s==null?void 0:s.label,q=s==null?void 0:s.series;return!Array.isArray(v)||!Array.isArray(q)?null:{id:M,column:K,kpiId:d,GraphSequence:L,graphDetails:{chartType:D,graphName:P,colorPallete:A,KpiSequence:L},label:v,series:q}}return Array.isArray(s==null?void 0:s.data)?{id:M,column:K,kpiId:d,GraphSequence:L,graphDetails:{chartType:D,graphName:P,colorPallete:A,KpiSequence:L},data:s.data}:null},mt=()=>{const{customError:s}=F2(),[p,x]=u.useState([]),[d,h]=u.useState(!0),[_,D]=u.useState([]),[P,A]=u.useState([]),[L,M]=u.useState([]),[K,O]=u.useState({}),[U,I]=u.useState([]),[w,y]=u.useState({}),[v,q]=u.useState({}),[W,Z]=u.useState({}),f=X(t=>t.commonFilter.Dashboard),T=X(t=>{var i;return((i=t.commonFilter)==null?void 0:i.Dashboard)||{}});X(t=>t.applicationConfig);const F=X(t=>t.userManagement.userData),k=(F==null?void 0:F.user_id)||"";let R=X(t=>t.userManagement.roles);const j=R.join(", "),H=()=>R&&Array.isArray(R)&&R.length>0&&R.some(t=>t&&t.trim()!==""),Q=u.useRef(!1),z=u.useRef(!1),re=u.useCallback(async()=>{if(!z.current&&H()){z.current=!0,h(!0),Q.current=!1;try{const t=await le("KPI Metrics"),i=await le("KPI Reports"),n=await J(t,"KPI Metrics"),a=await J(i,"KPI Reports"),l=await N(t,n),o=Ve(t),c=Ve(i),b=Ze(l,t);I(a),x(l),y(b),A(i),q(c),M([...n,...a]),Q.current=!0}catch(t){s(se.DASHBOARD_REFRESH_FAILED,t)}finally{z.current=!1,h(!1)}}},[R]),le=async(t="KPI Metrics")=>{if(!H())return[];const i={dtName:"MDG_MAT_DYNAMIC_DASHBOARD_DT",version:"v6",region:"US",role:`${j}`,kpiType:t};let n="";t==="KPI Metrics"?n=`/${ae}${ge.DASHBOARD_APIS.FETCH_DECISION_TABLE_METRICS}`:n=`/${ae}${ge.DASHBOARD_APIS.FETCH_DECISION_TABLE_REPORTS}`;try{const a=await new Promise((l,o)=>{ie(n,"post",c=>{const b=(c==null?void 0:c.body)||[];l(b)},o,i)});return t==="KPI Metrics"?D(a):A(a),a}catch(a){return s(t==="KPI Metrics"?se.DECISION_TABLE_FETCH_ERROR:se.REPORT_CONFIG_FETCH_ERROR,a),[]}},J=u.useCallback(async(t,i)=>{try{let n=await new Promise(a=>{ie(`/${ae}${ge.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${k}&kpiType=${encodeURIComponent(i)}`,"get",l=>a((l==null?void 0:l.body)||[]),()=>a([]))});if(!n.length&&t.length){const a=t.map(l=>{var o;return{Id:null,UserId:k,KpiId:l.MDG_KPI_ID,KpiChartType:i==="KPI Metrics"?l.MDG_KPI_GRAPH_TYPE:null,KpiChartName:l.MDG_KPI_NAME,KpiColPallet:i==="KPI Metrics"?l.MDG_KPI_COLOR_PALLET:null,KpiSequence:Number(l.MDG_KPI_GRAPH_SEQUENCE),KpiColumn:(o=l.MDG_KPI_GRAPH_COLUMN)==null?void 0:o.toLowerCase(),KpiVisibility:!0,IsActive:!0,KpiType:i}});await new Promise((l,o)=>{ie(`/${ae}${ge.DASHBOARD_APIS.SAVE_USER_CONFIG}`,"post",l,o,a)}),n=await new Promise(l=>{ie(`/${ae}${ge.DASHBOARD_APIS.FETCH_USER_CONFIG}?userId=${k}&kpiType=${encodeURIComponent(i)}`,"get",o=>l((o==null?void 0:o.body)||[]),()=>l(a))})}return n}catch(n){return s(se.USER_PREFERENCES_FETCH_ERROR,n),[]}},[k,s]),g=u.useCallback(async(t,i,n)=>{var a,l,o;try{const c=i.find(V=>V.MDG_KPI_ID===t);if(!c||!c.MDG_KPI_ENDPOINT)return null;const b=n.find(V=>V.KpiId===t),r=c.MDG_KPI_ENDPOINT.replace(/^\//,""),C=parseInt(t.split("_")[1])-1,G={FromDate:"2024-01-01",ToDate:"2025-12-31",Requestor:"",KpiId:t,Module:we(f==null?void 0:f.dashBoardModuleName)||Re[0],UserId:k,Priority:"",Region:(T==null?void 0:T.selectedRegion)||"",ReqType:((a=T==null?void 0:T.selectedRequestType)==null?void 0:a.join(","))||"",ReqStatus:((l=T==null?void 0:T.selectedRequestStatus)==null?void 0:l.join(","))||"",GraphType:(b==null?void 0:b.KpiChartType)||(c==null?void 0:c.MDG_KPI_GRAPH_TYPE)||"",KpiName:(b==null?void 0:b.KpiChartName)||(c==null?void 0:c.MDG_KPI_NAME)||"",ColPallet:(b==null?void 0:b.KpiColPallet)||(c==null?void 0:c.MDG_KPI_COLOR_PALLET)||"",GraphColumn:(b==null?void 0:b.KpiColumn)||((o=c==null?void 0:c.MDG_KPI_GRAPH_COLUMN)==null?void 0:o.toLowerCase())||$e(C),GraphSequence:(b==null?void 0:b.KpiSequence)||Number(c==null?void 0:c.MDG_KPI_GRAPH_SEQUENCE)||C+1};return new Promise(V=>{ie(`/${ae}/counts/${r}`,"post",Y=>V(a2(Y.body,C+1,C,t,G.GraphSequence)),()=>V(null),G)})}catch(c){return s(se.GRAPH_DATA_FETCH_ERROR,c),null}},[f,k,T,s]),N=async(t,i)=>{if(!t.length)return[];try{const n={};t.forEach(r=>{r.MDG_KPI_ID&&r.MDG_KPI_ENDPOINT&&(n[r.MDG_KPI_ID]=r.MDG_KPI_ENDPOINT.replace(/^\//,""))});const a=t.filter(r=>["TRUE","ENABLED"].includes((r.MDG_KPI_VISIBILITY||"").toString().toUpperCase())).map(r=>r.MDG_KPI_ID),l=i.filter(r=>r.KpiVisibility===!0&&r.IsActive===!0).map(r=>r.KpiId),o=l.length>0?a.filter(r=>l.includes(r)):a,c={},b=await Promise.all(o.map(r=>{var ue,oe,Pe;const C=i.find(ce=>ce.KpiId===r),G=t.find(ce=>ce.MDG_KPI_ID===r),V=n[r];if(!V)return Promise.resolve(null);const Y=parseInt(r.split("_")[1])-1,ee={FromDate:"2024-01-01",ToDate:"2025-12-31",Requestor:"",KpiId:r,Module:we(f==null?void 0:f.dashBoardModuleName)||Re[0],UserId:k,Priority:"",Region:(T==null?void 0:T.selectedRegion)||"",ReqType:((ue=T==null?void 0:T.selectedRequestType)==null?void 0:ue.join(","))||"",ReqStatus:((oe=T==null?void 0:T.selectedRequestStatus)==null?void 0:oe.join(","))||"",GraphType:(C==null?void 0:C.KpiChartType)||(G==null?void 0:G.MDG_KPI_GRAPH_TYPE)||"",KpiName:(C==null?void 0:C.KpiChartName)||(G==null?void 0:G.MDG_KPI_NAME)||"",ColPallet:(C==null?void 0:C.KpiColPallet)||(G==null?void 0:G.MDG_KPI_COLOR_PALLET)||"",GraphColumn:(C==null?void 0:C.KpiColumn)||((Pe=G==null?void 0:G.MDG_KPI_GRAPH_COLUMN)==null?void 0:Pe.toLowerCase())||$e(Y),GraphSequence:(C==null?void 0:C.KpiSequence)||Number(G==null?void 0:G.MDG_KPI_GRAPH_SEQUENCE)||Y+1};return c[r]=ee,new Promise(ce=>{ie(`/${ae}/counts/${V}`,"post",P2=>ce(a2(P2.body,Y+1,Y,r,ee.GraphSequence)),()=>ce(null),ee)})}));return O(c),b.filter(Boolean)}catch(n){return s(se.GRAPH_DATA_FETCH_ERROR,n),[]}},de=async()=>{if(!(z.current||Q.current)&&H()){z.current=!0,h(!0);try{const t=await le("KPI Metrics"),i=await le("KPI Reports"),n=await J(t,"KPI Metrics"),a=await J(i,"KPI Reports"),l=await N(t,n),o=Ve(i),c=Ze(l,t);x(l),y(c),I(a),A(i),q(o),M([...n,...a]),Q.current=!0}catch(t){s(se.DASHBOARD_INIT_FAILED,t)}finally{z.current=!1,h(!1)}}};u.useEffect(()=>{(k||k!=="")&&de()},[k]),u.useEffect(()=>{H()&&k&&!Q.current&&!z.current&&de()},[R,k]),u.useEffect(()=>{(async()=>{if(!(!Q.current||z.current)){z.current=!0,h(!0);try{const i=await N(_,L),n=Ze(i,_);x(i),y(n)}catch(i){s(se.FILTER_CHANGE_UPDATE_FAILED,i)}finally{z.current=!1,h(!1)}}})()},[T]);const me=u.useCallback(async t=>{if(!_.length||!L.length)return;const i=L.find(o=>o.KpiId===t),n=_.find(o=>o.MDG_KPI_ID===t),a=n&&["TRUE","ENABLED"].includes((n.MDG_KPI_VISIBILITY||"").toString().toUpperCase()),l=i&&i.KpiVisibility===!0&&i.IsActive===!0;if(!a||!l){x(o=>o.filter(c=>c.kpiId!==t)),y(o=>{const c={...o};return Object.keys(c).forEach(b=>{c[b]=c[b].filter(r=>r.kpiId!==t)}),c});return}Z(o=>({...o,[t]:!0}));try{const o=await g(t,_,L);o&&(x(c=>{const b=c.findIndex(r=>r.kpiId===t);if(b>=0){const r=[...c];return r[b]=o,r}else return[...c,o]}),y(c=>{const b=_.find(V=>V.MDG_KPI_ID===t),r=(b==null?void 0:b.MDG_KPI_SECTION_NAME)||"General",C={...c};C[r]||(C[r]=[]);const G=C[r].findIndex(V=>V.kpiId===t);return G>=0?C[r][G]=o:C[r].push(o),C}))}catch(o){s(se.GRAPH_DATA_FETCH_ERROR,o)}finally{Z(o=>({...o,[t]:!1}))}},[_,L,g,s]),S=u.useCallback(async t=>{if(!(!_.length||!t.length))try{const i=await J(_,"KPI Metrics");M(r=>{const C=r.filter(G=>G.KpiType==="KPI Reports");return[...i,...C]});const n=_.filter(r=>["TRUE","ENABLED"].includes((r.MDG_KPI_VISIBILITY||"").toString().toUpperCase())).map(r=>r.MDG_KPI_ID),a=i.filter(r=>r.KpiVisibility===!0&&r.IsActive===!0).map(r=>r.KpiId),l=a.length>0?n.filter(r=>a.includes(r)):n,o=t.filter(r=>l.includes(r)),c=t.filter(r=>!l.includes(r)),b={};if(o.forEach(r=>{b[r]=!0}),Z(r=>({...r,...b})),c.length>0&&(x(r=>r.filter(C=>!c.includes(C.kpiId))),y(r=>{const C={...r};return Object.keys(C).forEach(G=>{C[G]=C[G].filter(V=>!c.includes(V.kpiId))}),C})),o.length>0){const r=o.map(V=>g(V,_,i)),G=(await Promise.all(r)).filter(Boolean);G.length>0&&(x(V=>{let Y=[...V];return G.forEach(ee=>{const ue=Y.findIndex(oe=>oe.kpiId===ee.kpiId);ue>=0?Y[ue]=ee:Y.push(ee)}),Y}),y(V=>{const Y={...V};return G.forEach(ee=>{const ue=_.find(ce=>ce.MDG_KPI_ID===ee.kpiId),oe=(ue==null?void 0:ue.MDG_KPI_SECTION_NAME)||"General";Y[oe]||(Y[oe]=[]);const Pe=Y[oe].findIndex(ce=>ce.kpiId===ee.kpiId);Pe>=0?Y[oe][Pe]=ee:Y[oe].push(ee)}),Y}))}}catch(i){s(se.GRAPH_DATA_FETCH_ERROR,i)}finally{const i={};t.forEach(n=>{i[n]=!1}),Z(n=>({...n,...i}))}},[_,g,J,s]);return{cards:p,reportConfig:P,loading:d,decisionTableConfig:_,userPreferences:L,kpiPayloadMap:K,kpiReportPrefs:U,refreshDashboard:re,sectionedCards:w,sectionedReports:v,graphLoadingStates:W,refreshSingleGraph:me,refreshMultipleGraphs:S}},Tt=()=>{const[s,p]=u.useState(!1),[x,d]=u.useState(!1),[h,_]=u.useState(!0),{cards:D,loading:P,decisionTableConfig:A,userPreferences:L,reportConfig:M,kpiReportPrefs:K,refreshDashboard:O,sectionedCards:U,sectionedReports:I,graphLoadingStates:w,refreshSingleGraph:y,refreshMultipleGraphs:v}=mt(),{t:q}=be(),W=u.useCallback((f=[],T=[],F=!1)=>{F&&O(),d(!1)},[O]),Z=u.useCallback(f=>{v(f)},[v]);return m($,{sx:{height:"100vh",overflow:"hidden",display:"flex",flexDirection:"column"},children:[m($,{sx:{position:"sticky",top:0,bgcolor:"background.default",p:2},children:[m(Ce,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[m($,{children:[e(B,{variant:"h3",children:e("strong",{children:q("Dashboard")})}),e(B,{variant:"body2",color:"text.secondary",children:q("This view displays various metrics related to Master Data")})]}),m(Ce,{direction:"column",spacing:1,children:[m(Ce,{direction:"row",alignItems:"center",spacing:1,children:[e(Ee,{variant:"outlined",startIcon:e($2,{}),color:"primary",onClick:()=>d(!0),size:"small",sx:{mr:"20px !important"},className:"manageDashBoard",children:q("Manage Dashboard")}),m($,{sx:{display:"flex",alignItems:"center"},className:"parentChildSwitchDB",children:[e(B,{variant:"body2",children:q("KPI Metrics")}),e(Ie,{checked:s,onChange:()=>p(f=>!f),color:"primary"}),e(B,{variant:"body2",children:q("KPI Reports")})]})]}),e(Ce,{direction:"row",alignItems:"center",spacing:1,justifyContent:"flex-end",children:m($,{sx:{display:"flex",alignItems:"center"},className:"tabbed",children:[e(B,{variant:"body2",children:q("Tabbed View")}),e(Ie,{checked:h,onChange:()=>_(f=>!f),color:"primary"})]})})]})]}),!s&&m($,{mt:2,children:[e(pt,{}),e(X2,{})]})]}),e($,{sx:{flex:1,overflowY:"auto",p:2},children:h?e(ut,{sectionedCards:U,sectionedReports:I,loading:P,showReports:s,kpiReportPrefs:K,decisionTableConfig:A,userPreferences:L,graphLoadingStates:w,onRefreshGraph:y}):s?e(R2,{reportConfig:M,kpiReportPrefs:K,loading:P}):e(I2,{cards:D,loading:P,graphLoadingStates:w,onRefreshGraph:y})}),e(Ct,{open:x,onClose:()=>d(!1),onSave:W,onRefreshSpecificGraphs:Z,decisionTableConfig:A,userPreferences:L,reportConfig:M})]})};export{Tt as default};
