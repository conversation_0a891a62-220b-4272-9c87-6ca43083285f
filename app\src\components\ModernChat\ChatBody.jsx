import React, { useEffect } from 'react';
import { Box, Typography, Avatar, TextField, Button, IconButton, InputAdornment, Tooltip, Menu, MenuItem } from '@mui/material';
import { Message as MessageIcon, VideoCall, Call, Search, MoreVert, Circle, EmojiEmotions, AttachFile, Send, Edit, Delete, Clear, KeyboardArrowUp, KeyboardArrowDown } from '@mui/icons-material';
const ChatBody = ({
  currentChatId,
  chatUserEmail,
  messages,
  setMessages,
  messageInput,
  setMessageInput,
  sendMessage,
  currentUserId,
  currentUserEmail,
  messageListRef,
  editingMessage,
  setEditingMessage,
  editText,
  setEditText,
  messageMenuAnchor,
  setMessageMenuAnchor,
  selectedMessage,
  setSelectedMessage,
  messageSearchQuery,
  setMessageSearchQuery,
  searchResultIndices,
  setSearchResultIndices,
  currentSearchIndex,
  setCurrentSearchIndex,
  showMessageSearch,
  setShowMessageSearch,
  emojiAnchor,
  setEmojiAnchor,
  showSnackbar,
  isOnlyEmoji
}) => {
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleMessageMenuOpen = (event, message) => {
    event.preventDefault();
    setMessageMenuAnchor(event.currentTarget);
    setSelectedMessage(message);
  };

  const handleMessageMenuClose = () => {
    setMessageMenuAnchor(null);
    setSelectedMessage(null);
  };

  const handleEditMessage = () => {
    setEditingMessage(selectedMessage);
    setEditText(selectedMessage.content);
    handleMessageMenuClose();
  };

  const saveEditMessage = () => {
    setMessages(prev => prev.map(msg =>
      msg === editingMessage ? { ...msg, content: editText, edited: true } : msg
    ));
    setEditingMessage(null);
    setEditText('');
  };

  const cancelEdit = () => {
    setEditingMessage(null);
    setEditText('');
  };

  const handleDeleteMessage = () => {
    setMessages(prev => prev.filter(msg => msg !== selectedMessage));
    handleMessageMenuClose();
    showSnackbar('Message deleted', 'success');
  };

  const handleMessageSearch = (searchText) => {
    setMessageSearchQuery(searchText);
    if (!searchText.trim()) {
      setSearchResultIndices([]);
      setCurrentSearchIndex(-1);
      return;
    }
    const indices = [];
    messages.forEach((message, index) => {
      if (message.content.toLowerCase().includes(searchText.toLowerCase())) {
        indices.push(index);
      }
    });
    setSearchResultIndices(indices);
    setCurrentSearchIndex(indices.length > 0 ? 0 : -1);

    if (indices.length > 0) {
      scrollToMessage(indices[0]);
    }
  };

  const scrollToMessage = (messageIndex) => {
    const messageElement = document.getElementById(`message-${messageIndex}`);
    if (messageElement && messageListRef.current) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const navigateSearchResults = (direction) => {
    if (searchResultIndices.length === 0) return;
    let newIndex;
    if (direction === 'next') {
      newIndex = currentSearchIndex < searchResultIndices.length - 1 ? currentSearchIndex + 1 : 0;
    } else {
      newIndex = currentSearchIndex > 0 ? currentSearchIndex - 1 : searchResultIndices.length - 1;
    }
    setCurrentSearchIndex(newIndex);
    scrollToMessage(searchResultIndices[newIndex]);
  };

  const clearMessageSearch = () => {
    setMessageSearchQuery('');
    setSearchResultIndices([]);
    setCurrentSearchIndex(-1);
    setShowMessageSearch(false);
  };

  const toggleMessageSearch = () => {
    setShowMessageSearch(!showMessageSearch);
  };

  const highlightSearchText = (text, searchText) => {
    if (!searchText.trim()) return text;
    const regex = new RegExp(`(${searchText})`, 'gi');
    const parts = text.split(regex);
    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} style={{
          backgroundColor: '#ffeb3b',
          color: '#000',
          fontWeight: 'bold',
          padding: '2px 4px',
          borderRadius: '3px'
        }}>
          {part}
        </span>
      ) : part
    );
  };

  const isFromCurrentUser = (message) => {
    return (message.senderId === currentUserId?.id);
  };

  // Helper function to get sender email
  const getSenderEmail = (message) => {
    return message.senderEmail || message.sender?.email || 'Unknown User';
  };

  useEffect(() => {
    if (messageListRef.current) {
      messageListRef.current.scrollTop = messageListRef.current.scrollHeight;
    }
  }, [messages]);

  if (!currentChatId) {
    return (
      <Box sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#faf9f8',
        color: '#616161'
      }}>
        <MessageIcon sx={{ fontSize: 64, mb: 2, color: '#c4c4c4' }} />
        <Typography variant="h6" gutterBottom sx={{ fontWeight: 500 }}>
          Select a chat to start messaging
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Choose a conversation from the sidebar to begin chatting
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', backgroundColor: 'white' }}>
      {/* Chat Header */}
      <Box sx={{
        px: 3,
        py: 2,
        backgroundColor: '#faf9f8',
        borderBottom: '1px solid #e1dfdd',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ width: 32, height: 32, backgroundColor: (theme) => theme.palette.primary.dark }}>
            {chatUserEmail.charAt(0).toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#242424', fontSize: '16px' }}>
              {chatUserEmail}
            </Typography>
            <Typography variant="caption" sx={{ color: '#6bb700', display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Circle sx={{ fontSize: 6 }} />
              Active now
            </Typography>
          </Box>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <IconButton size="small" sx={{ color: '#616161', '&:hover': { backgroundColor: 'rgba(97, 97, 97, 0.1)', color: '#0078d4', transform: 'scale(1.1)', transition: 'all 0.2s ease-in-out' } }}>
            <VideoCall />
          </IconButton>
          <IconButton size="small" sx={{ color: '#616161', '&:hover': { backgroundColor: 'rgba(97, 97, 97, 0.1)', color: '#0078d4', transform: 'scale(1.1)', transition: 'all 0.2s ease-in-out' } }}>
            <Call />
          </IconButton>
          <Tooltip title="Search messages" arrow>
            <IconButton
              size="small"
              onClick={toggleMessageSearch}
              sx={{
                color: showMessageSearch ? '#0078d4' : '#616161',
                backgroundColor: showMessageSearch ? 'rgba(0, 120, 212, 0.1)' : 'transparent',
                '&:hover': { backgroundColor: 'rgba(0, 120, 212, 0.1)', color: '#0078d4', transform: 'scale(1.1)', transition: 'all 0.2s ease-in-out' }
              }}
            >
              <Search />
            </IconButton>
          </Tooltip>
          <IconButton size="small" sx={{ color: '#616161', '&:hover': { backgroundColor: 'rgba(97, 97, 97, 0.1)', color: '#0078d4', transform: 'scale(1.1)', transition: 'all 0.2s ease-in-out' } }}>
            <MoreVert />
          </IconButton>
        </Box>
      </Box>

      {/* Search Bar */}
      {showMessageSearch && (
        <Box sx={{
          px: 3,
          py: 1,
          backgroundColor: '#fff3cd',
          borderBottom: '1px solid #ffeaa7',
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <TextField
            size="small"
            placeholder="Search messages..."
            value={messageSearchQuery}
            onChange={(e) => handleMessageSearch(e.target.value)}
            sx={{
              flex: 1,
              '& .MuiOutlinedInput-root': {
                backgroundColor: 'white',
                height: '32px'
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search sx={{ color: '#616161', fontSize: 18 }} />
                </InputAdornment>
              ),
            }}
          />

          {searchResultIndices.length > 0 && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" sx={{ color: '#616161', minWidth: 'fit-content' }}>
                {currentSearchIndex + 1} of {searchResultIndices.length}
              </Typography>
              <IconButton size="small" onClick={() => navigateSearchResults('prev')} sx={{ color: '#616161', '&:hover': { color: '#0078d4' } }}>
                <KeyboardArrowUp sx={{ fontSize: 18 }} />
              </IconButton>
              <IconButton size="small" onClick={() => navigateSearchResults('next')} sx={{ color: '#616161', '&:hover': { color: '#0078d4' } }}>
                <KeyboardArrowDown sx={{ fontSize: 18 }} />
              </IconButton>
            </Box>
          )}

          <IconButton size="small" onClick={clearMessageSearch} sx={{ color: '#616161', '&:hover': { color: '#ff4444' } }}>
            <Clear sx={{ fontSize: 18 }} />
          </IconButton>
        </Box>
      )}

      <Box
        ref={messageListRef}
        sx={{
          flex: 1,
          overflow: 'auto',
          backgroundColor: '#ffffff',
          p: 2
        }}
      >
        {messages.map((message, index) => {
          const isCurrentUser = isFromCurrentUser(message);
          const senderEmail = getSenderEmail(message);
          const showAvatar = index === 0 || getSenderEmail(messages[index - 1]) !== senderEmail;
          const showSenderName = !isCurrentUser && showAvatar;
          const isEditing = editingMessage === message;
          const isSearchHighlighted = searchResultIndices.includes(index) && currentSearchIndex !== -1 && searchResultIndices[currentSearchIndex] === index;

          const messageIsEmojiOnly = isOnlyEmoji && isOnlyEmoji(message.content);

          if (messageIsEmojiOnly) {
            return (
              <Box
                key={index}
                id={`message-${index}`}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
                  mb: 2,
                  backgroundColor: isSearchHighlighted ? 'rgba(255, 193, 7, 0.2)' : 'transparent',
                  borderRadius: isSearchHighlighted ? '8px' : '0',
                  p: isSearchHighlighted ? 1 : 0,
                  transition: 'all 0.3s ease-in-out',
                  border: isSearchHighlighted ? '2px solid #ffc107' : '2px solid transparent'
                }}
              >
                {/* Sender name for other users */}
                {showSenderName && (
                  <Typography
                    variant="caption"
                    sx={{
                      color: '#616161',
                      fontWeight: 500,
                      mb: 0.5,
                      ml: 1
                    }}
                  >
                    {senderEmail}
                  </Typography>
                )}
                <Box sx={{
                  display: 'flex',
                  flexDirection: isCurrentUser ? 'row-reverse' : 'row',
                  alignItems: 'center',
                  gap: 1
                }}>
                  {!isCurrentUser && (
                    <Avatar
                      sx={{
                        width: 28,
                        height: 28,
                        backgroundColor: '#6264A7',
                        visibility: showAvatar ? 'visible' : 'hidden'
                      }}
                    >
                      {senderEmail.charAt(0).toUpperCase()}
                    </Avatar>
                  )}

                  {/* Emoji displayed directly without bubble */}
                  <Typography
                    variant="h4"
                    sx={{
                      fontSize: '2rem',
                      lineHeight: 1,
                      userSelect: 'none',
                      cursor: 'default'
                    }}
                  >
                    {messageSearchQuery ?
                      highlightSearchText(message.content, messageSearchQuery) :
                      message.content
                    }
                  </Typography>

                  <Typography
                    variant="caption"
                    sx={{
                      color: '#616161',
                      fontSize: '10px',
                      ml: isCurrentUser ? 0 : 1,
                      mr: isCurrentUser ? 1 : 0,
                    }}
                  >
                    {new Date(message.timestamp).toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </Typography>
                </Box>
              </Box>
            );
          }
          
          return (
            <Box
              key={index}
              id={`message-${index}`}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
                mb: 2,
                backgroundColor: isSearchHighlighted ? 'rgba(255, 193, 7, 0.2)' : 'transparent',
                borderRadius: isSearchHighlighted ? '8px' : '0',
                p: isSearchHighlighted ? 1 : 0,
                transition: 'all 0.3s ease-in-out',
                border: isSearchHighlighted ? '2px solid #ffc107' : '2px solid transparent'
              }}
            >
              {/* Sender name for other users */}
              {showSenderName && (
                <Typography
                  variant="caption"
                  sx={{
                    color: '#616161',
                    fontWeight: 500,
                    mb: 0.5,
                    ml: 1
                  }}
                >
                  {senderEmail}
                </Typography>
              )}

              <Box sx={{
                display: 'flex',
                flexDirection: isCurrentUser ? 'row-reverse' : 'row',
                alignItems: 'flex-end',
                gap: 1,
                maxWidth: '100%'
              }}>
                {!isCurrentUser && (
                  <Avatar
                    sx={{
                      width: 28,
                      height: 28,
                      backgroundColor: (theme) => theme.palette.primary.main,
                      visibility: showAvatar ? 'visible' : 'hidden'
                    }}
                  >
                    {senderEmail.charAt(0).toUpperCase()}
                  </Avatar>
                )}

                <Box
                  sx={{
                    display: 'inline-block', 
                    minWidth: '70%',
                    backgroundColor: (theme) => isCurrentUser
                      ? theme.palette.primary.main
                      : theme.palette.primary.light,
                    color: isCurrentUser ? 'white' : '#242424',
                    borderRadius: '12px',
                    p: 1.5,
                    position: 'relative',
                    wordBreak: 'break-word',
                    '&:hover .message-options': {
                      opacity: isCurrentUser ? 1 : 0
                    }
                  }}
                  onContextMenu={(e) => isCurrentUser && handleMessageMenuOpen(e, message)}
                >
                  {isEditing ? (
                    <Box>
                      <TextField
                        fullWidth
                        multiline
                        value={editText}
                        onChange={(e) => setEditText(e.target.value)}
                        variant="outlined"
                        size="small"
                        autoFocus
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: 'white',
                            '& fieldset': {
                              borderColor: '#0078d4'
                            }
                          }
                        }}
                      />
                      <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Button
                          size="small"
                          variant="contained"
                          onClick={saveEditMessage}
                          sx={{
                            backgroundColor: '#0078d4',
                            textTransform: 'none',
                            minWidth: 'auto',
                            px: 2
                          }}
                        >
                          Save
                        </Button>
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={cancelEdit}
                          sx={{
                            borderColor: '#616161',
                            color: '#616161',
                            textTransform: 'none',
                            minWidth: 'auto',
                            px: 2
                          }}
                        >
                          Cancel
                        </Button>
                      </Box>
                    </Box>
                  ) : (
                    <>
                      <Typography variant="body2" sx={{
                        fontSize: '1rem',
                        lineHeight: 1.5
                      }}>
                        {messageSearchQuery ?
                          highlightSearchText(message.content, messageSearchQuery) :
                          message.content
                        }
                        {message.edited && (
                          <Typography
                            component="span"
                            variant="caption"
                            sx={{
                              ml: 1,
                              opacity: 0.7,
                              fontStyle: 'italic',
                              fontSize: '10px'
                            }}
                          >
                            (edited)
                          </Typography>
                        )}
                      </Typography>
                      <Typography
                        variant="caption"
                        sx={{
                          opacity: 0.7,
                          fontSize: '10px',
                          mt: 0.5,
                          display: 'block'
                        }}
                      >
                        {new Date(message.timestamp).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Typography>

                      {isCurrentUser && (
                        <Box
                          className="message-options"
                          sx={{
                            position: 'absolute',
                            top: -10,
                            right: -10,
                            opacity: 0,
                            transition: 'opacity 0.2s',
                            display: 'flex',
                            gap: 0.5
                          }}
                        >
                          <IconButton
                            size="small"
                            onClick={() => handleEditMessage()}
                            sx={{
                              backgroundColor: 'white',
                              color: '#0078d4',
                              boxShadow: 1,
                              width: 24,
                              height: 24,
                              '&:hover': {
                                backgroundColor: '#f0f8ff',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Edit sx={{ fontSize: 14 }} />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => {
                              setSelectedMessage(message);
                              handleDeleteMessage();
                            }}
                            sx={{
                              backgroundColor: 'white',
                              color: '#ff4444',
                              boxShadow: 1,
                              width: 24,
                              height: 24,
                              '&:hover': {
                                backgroundColor: '#ffebee',
                                transform: 'scale(1.1)'
                              }
                            }}
                          >
                            <Delete sx={{ fontSize: 14 }} />
                          </IconButton>
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              </Box>
            </Box>
          );
        })}
      </Box>
      <Box sx={{
        p: 2,
        backgroundColor: '#faf9f8',
        borderTop: '1px solid #e1dfdd'
      }}>
        <Box sx={{
          backgroundColor: 'white',
          border: '2px solid #e1dfdd',
          borderRadius: '8px',
          '&:focus-within': {
            borderColor: '#0078d4'
          }
        }}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Type a new message"
            value={messageInput}
            onChange={(e) => setMessageInput(e.target.value)}
            onKeyPress={handleKeyPress}
            variant="outlined"
            sx={{
              '& .MuiOutlinedInput-root': {
                '& fieldset': {
                  border: 'none'
                }
              }
            }}
          />

          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            px: 2,
            py: 1,
            borderTop: '1px solid #e1dfdd'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Tooltip title="Add emoji" arrow>
                <IconButton
                  size="small"
                  onClick={(e) => setEmojiAnchor(e.currentTarget)}
                  sx={{
                    color: '#616161',
                    '&:hover': {
                      backgroundColor: 'rgba(97, 97, 97, 0.1)',
                      color: '#0078d4',
                      transform: 'scale(1.1)',
                      transition: 'all 0.2s ease-in-out'
                    }
                  }}
                >
                  <EmojiEmotions />
                </IconButton>
              </Tooltip>

              <Tooltip title="Attach file" arrow>
                <IconButton
                  size="small"
                  sx={{
                    color: '#616161',
                    '&:hover': {
                      backgroundColor: 'rgba(97, 97, 97, 0.1)',
                      color: '#0078d4',
                      transform: 'scale(1.1)',
                      transition: 'all 0.2s ease-in-out'
                    }
                  }}
                >
                  <AttachFile />
                </IconButton>
              </Tooltip>
            </Box>

            <Tooltip title="Send message" arrow>
              <IconButton
                onClick={sendMessage}
                disabled={!messageInput.trim()}
                sx={{
                  backgroundColor: messageInput.trim() ? '#0078d4' : '#e1dfdd',
                  color: messageInput.trim() ? 'white' : '#616161',
                  '&:hover': {
                    backgroundColor: messageInput.trim() ? '#106ebe' : '#d1cfcd',
                    transform: messageInput.trim() ? 'scale(1.1)' : 'none',
                    transition: 'all 0.2s ease-in-out'
                  },
                  '&:disabled': {
                   backgroundColor: (theme) => theme.palette.primary.dark ,
                    color: 'white'
                  }
                }}
              >
                <Send />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </Box>

      {/* Message Context Menu */}
      <Menu
        anchorEl={messageMenuAnchor}
        open={Boolean(messageMenuAnchor)}
        onClose={handleMessageMenuClose}
        PaperProps={{
          sx: {
            minWidth: 150,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            border: '1px solid #e1dfdd'
          }
        }}
      >
        <MenuItem
          onClick={handleEditMessage}
          sx={{
            py: 1,
            '&:hover': {
              backgroundColor: 'rgba(0, 120, 212, 0.1)'
            }
          }}
        >
          <Edit sx={{ mr: 2, fontSize: 18 }} />
          Edit
        </MenuItem>
        <MenuItem
          onClick={handleDeleteMessage}
          sx={{
            py: 1,
            '&:hover': {
              backgroundColor: 'rgba(255, 68, 68, 0.1)',
              color: '#ff4444'
            }
          }}
        >
          <Delete sx={{ mr: 2, fontSize: 18 }} />
          Delete
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default ChatBody;