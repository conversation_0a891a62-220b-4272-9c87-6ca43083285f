import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Popconfirm,
  Typography,
  Card,
  Row,
  Col,
  Tag,
  Tooltip
} from 'antd';
import {
  CalendarOutlined,
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  CloudDownloadOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import AddEditHolidayModal from './AddEditHolidayModal';
import AttachmentUploadDialog from "@components/Common/AttachmentUploadDialog";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import { doAjax } from '../../common/fetchService';
import { destination_Admin } from '../../../destinationVariables';
import { useSnackbar } from '@hooks/useSnackbar';
import { useTheme } from '@mui/material';
import useDownloadExcel from '@hooks/useDownloadExcel';

const { Title, Text } = Typography;

const HolidayManagement = ({ open, onClose, regionOptions }) => {
  const theme = useTheme()
  const [holidays, setHolidays] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [addEditModalOpen, setAddEditModalOpen] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState(null);
  const [enableHolidayUpload, setEnableHolidayUpload] = useState(false);
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");

  const { showSnackbar } = useSnackbar();
  const { handleUploadHolidays, handleDownloadHolidays } = useDownloadExcel()

  const fetchHolidays = () => {
    setIsLoading(true);
    
    const hSuccess = (res) => {
      const transformedData = res?.data?.map((holiday, index) => ({
        ...holiday,
        key: holiday?.id || `holiday_${index}`,
        id: holiday?.id || `holiday_${index}`,
      })) 
      || [];
      setHolidays(transformedData);
      setIsLoading(false);
    };

    const hError = () => {
      showSnackbar('Failed to fetch holidays', 'error');
      setIsLoading(false);
    };

    doAjax(`/${destination_Admin}/api/holidays`, 'get', hSuccess, hError);
  };

  const handleSaveHoliday = (holidayData) => {
    const isEdit = !!editingHoliday;
    const url = `/${destination_Admin}/api/holidays/upsert`
    const payload = [{
        id: isEdit ? editingHoliday?.id || holidayData?.id : null,
        date: holidayData?.date.toISOString(),
        holidayName: holidayData?.holidayName,
        description: holidayData?.description,
        type: holidayData?.type,
        region: holidayData?.region,
        status: holidayData?.status
    }];

    const hSuccess = () => {
      showSnackbar(
        `Holiday ${isEdit ? 'updated' : 'created'} successfully`,
        'success'
      );
      fetchHolidays();
      setAddEditModalOpen(false);
      setEditingHoliday(null);
    };

    const hError = () => {
      showSnackbar(
        `Failed to ${isEdit ? 'update' : 'create'} holiday`,
        'error'
      );
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const handleDeleteHoliday = (holiday) => {
    const hSuccess = () => {
      showSnackbar('Holiday deleted successfully', 'success');
      fetchHolidays();
    };

    const hError = () => {
      showSnackbar('Failed to delete holiday', 'error');
    };

    doAjax(`/${destination_Admin}/api/holidays/${holiday?.id}`, 'delete', hSuccess, hError);
  };

  const handleUpload = (files) => {
    handleUploadHolidays(files, setLoaderMessage, setBlurLoading, setEnableHolidayUpload, fetchHolidays)
  };

  const handleDownload = () => {
    handleDownloadHolidays(setLoaderMessage, setBlurLoading)
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
      render: (date) => {
        const holidayDate = new Date(date);
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {holidayDate.toLocaleDateString('en-US', {
                weekday: 'short',
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })}
            </div>
          </div>
        );
      }
    },
    {
      title: 'Holiday Name',
      dataIndex: 'holidayName',
      key: 'holidayName',
      sorter: (a, b) => a.holidayName.localeCompare(b.holidayName),
      render: (holidayName) => <Text strong>{holidayName}</Text>
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: {
        showTitle: false,
      },
      render: (description) => (
        <Tooltip placement="topLeft" title={description}>
          {description}
        </Tooltip>
      )
    },
    {
      title: 'Region',
      dataIndex: 'region',
      key: 'region',
      filters: [
        { text: 'Global', value: 'Global' },
        { text: 'US', value: 'US' },
        { text: 'EU', value: 'EU' },
        { text: 'APAC', value: 'APAC' },
      ],
      onFilter: (value, record) => record.region === value,
      render: (region) => (
        <Tag color={region === 'Global' ? 'blue' : 'green'}>
          {region}
        </Tag>
      )
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: 'National Holiday', value: 'National Holiday' },
        { text: 'Cultural Holiday', value: 'Cultural Holiday' },
      ],
      onFilter: (value, record) => record.type === value,
      render: (type) => (
        <Tag color={type === 'National Holiday' ? 'orange' : 'purple'}>
          {type}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: 'Active', value: true },
        { text: 'Inactive', value: false },
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Tag color={status ? 'success' : 'default'}>
          {status ? 'Active' : 'Inactive'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Holiday">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingHoliday(record);
                setAddEditModalOpen(true);
              }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Holiday"
            description="Are you sure you want to delete this holiday?"
            onConfirm={() => handleDeleteHoliday(record)}
            okText="Yes"
            cancelText="No"
            okButtonProps={{
                style: {
                backgroundColor: theme?.palette?.primary?.main,
                }
            }}
          >
            <Tooltip title="Delete Holiday">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  useEffect(() => {
    if (open) {
      fetchHolidays();
    }
  }, [open]);

  return (
    <>
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
              <CalendarOutlined />
              <span>Holiday Management</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginRight: 24 }}>
              <Tooltip title="Download Holidays">
                <Button
                  variant="outlined"
                  icon={<CloudDownloadOutlined />}
                  onClick={handleDownload}
                  size="medium"
                  style={{
                    borderColor: theme?.palette?.primary?.main,
                    color: theme?.palette?.primary?.main,
                  }}
                >
                  Download Excel
                </Button>
              </Tooltip>
              <Tooltip title="Upload Holidays">
                <Button
                  variant="outlined"
                  icon={<CloudUploadOutlined />}
                  onClick={() => setEnableHolidayUpload(true)}
                  size="medium"
                  style={{
                    borderColor: theme?.palette?.primary?.main,
                    color: theme?.palette?.primary?.main,
                  }}
                >
                  Upload Excel
                </Button>
              </Tooltip>
            </div>
          </div>
        }
        open={open}
        onCancel={onClose}
        width={1200}
        height={700}
        footer={null}
        destroyOnClose
        styles={{
          body: {
            height: 'calc(max(500px, 100vh - 300px))',
            padding: '20px',
            overflow: 'hidden'
          }
        }}
      >
        <div style={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          gap: '16px'
        }}>
          {/* Header Section */}
          <div style={{ flexShrink: 0 }}>
            <Row justify="space-between" align="middle">
              <Col>
                <Title level={4} style={{ margin: 0 }}>
                  Manage Holidays
                </Title>
                <Text type="secondary">
                  Configure holidays that affect business hours and SLA calculations
                </Text>
              </Col>
              <Col>
                <Space>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    style={{ backgroundColor: theme?.palette?.primary?.main }}
                    onClick={() => {
                      setEditingHoliday(null);
                      setAddEditModalOpen(true);
                    }}
                  >
                    Adhoc Holiday
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>

          <div style={{ flex: 1, overflow: 'hidden' }}>
            <Card style={{ height: '100%' }}>
              <Table
                rowSelection={null}
                columns={columns}
                dataSource={holidays}
                loading={isLoading}
                pagination={false}
                scroll={{ 
                  x: 800,
                  y: 'calc(100vh - 450px)'
                }}
                size="small"
                style={{ height: '90%' }}
              />
              {holidays.length > 0 && (
                <div style={{ 
                padding: '8px 16px',
                borderTop: '1px solid #f0f0f0',
                backgroundColor: '#fafafa',
                textAlign: 'right',
                fontSize: '14px',
                color: '#666'
                }}>
                Total: {holidays.length} items
                </div>
              )}
            </Card>
          </div>
        </div>
      </Modal>

      <AddEditHolidayModal
        open={addEditModalOpen}
        onClose={() => {
          setAddEditModalOpen(false);
          setEditingHoliday(null);
        }}
        onSubmit={handleSaveHoliday}
        editingHoliday={editingHoliday}
        regionOptions={regionOptions}
      />

      {enableHolidayUpload && (
            <AttachmentUploadDialog
                artifactId="" 
                artifactName="" 
                setOpen={setEnableHolidayUpload} 
                handleUpload={handleUpload}
                dialogTitle="Holiday List Upload"
            />
        )}
      
      {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}
    </>
  );
};

export default HolidayManagement;