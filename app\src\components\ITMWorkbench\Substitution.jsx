import React, { useEffect, useState } from "react";
import Substitution from "@cw/cherrywork-iwm-workspace/Substitution";
import configData from "../../data/configData";
import { useSelector } from "react-redux";
import { doAjax } from "../Common/fetchService";
import { destination_IWA_NEW, destination_MaterialMgmt } from "../../destinationVariables";
import { END_POINTS } from "../../constant/apiEndPoints";
const SubstitutionComponent = () => {
  const DestinationConfig = {
    APPLICATION_NAME: "1784",
    CRUD_API_ENV: "itm",
    DB_TYPE: "hana",
    SERVICE_BASE_URL: [
      {
        Description: "",
        Name: "ITMJavaServices",
        URL: "https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      // {
      //   Description: "",
      //   Name: "IWAServices",
      //   URL: "https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",
      // },
      {
        Description: "",
        Name: "ConfigServer",
        URL: "https://cherryworkproducts-config-server.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkNetServices",
        URL: "https://cherryworkproducts-worknet-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "CrudApiServices",
        URL: "https://cw-caf-crudapi-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "WorkFormsServices",
        URL: "https://cherrywork-wf-java-qas.cfapps.eu10-004.hana.ondemand.com/workforms",
      },
      {
        Description: "",
        Name: "NotificationServices",
        URL: "https://cherryworkproducts-messaging-dev.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "",
        Name: "ITMGraphServices",
        URL: "https://cherrywork-btp-qas-dashboard.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow Services",
        Name: "NativeWorkflowServices",
        URL: "https://cherryworkproducts-custom-wf-qas.cfapps.eu10-004.hana.ondemand.com",
      },
      {
        Description: "Native Workflow UI URL",
        Name: "NativeWorkflowUiUrl",
        URL: "https://cherrywork-native-workflow-qas.cfapps.eu10-004.hana.ondemand.com/native-ui",
      },
      {
        Description: "",
        Name: "OnboardingServices",
        URL: "https://cherryworkproducts-itm-java-qas.cfapps.eu10-004.hana.ondemand.com",
      },
    ],
  };

  const applicationConfig = useSelector((state) => state.applicationConfig);
  let userData = useSelector((state) => state.userManagement.userData);
  const { VITE_IWM_TOKEN } = import.meta.env;
  const accessToken = VITE_IWM_TOKEN;
  const [userList, setUserList] = useState(null);
  const [users, setUsers] = useState([]);

  const isLocalEnv = ["localhost", "127.0.0.1"].includes(applicationConfig.environment);
  const fetchUserList = () => {
    const url = isLocalEnv ? `/${destination_MaterialMgmt}${END_POINTS.API.USER_LIST_LOCAL}` : `/${destination_IWA_NEW}${END_POINTS.API.USER_LIST_PROD}`;

    doAjax(
      url,
      "get",
      (resData) => {
        // Transform resData.data into desired format
        const transformedUsers = {
          MDG: resData.data.map((user) => {
            const email = user.businessEmailId?.trim();
            const name = user.displayName?.trim();

            return {
              displayName: name,
              systemUserId: email,
              emailId: email,
              userName: email,
              applicationId: "MDG",
              userId: email,
              applicationName: "MDG",
            };
          }),
        };

        console.log("Formatted User List:", transformedUsers);

        setUserList(transformedUsers);
        setUsers(transformedUsers);
      },
      (err) => {
        console.error("Fetch error:", err);
      }
    );
  };

  useEffect(() => {
    fetchUserList();
  }, []);
  useEffect(() => {
    console.log(users, "userList");
  }, [users]);

  const usersMap = {
    MDG: [
      {
        displayName: "Anil  Burra",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Pramod Sudheendra Kumar  Kumar",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Mr. Kunal  Mallick",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Manas  Sahoo",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Mr. Bishwa  Das",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Abhisek  Tripathy",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Bijaya  Krushna Panda",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Shruti  Mohapatra",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
      {
        displayName: "Mr. Suvendu  Samantaray",
        systemUserId: "<EMAIL>",
        emailId: "<EMAIL>",
        userName: "<EMAIL>",
        applicationId: "MDG",
        userId: "<EMAIL>",
        applicationName: "MDG",
      },
    ],
  };
  return <Substitution 
   token={"********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.kDiY_3SCVxeA4bulteaFUDiffCncKQ7Enqb95Ji6hVgmaznSM_DpMSwPXzXoVjtJ2uMNY-JOR_XiwiVwf1Vp1zbACGVhNiAYnR4omX8sNxS5NS1DlhppZ_3Xfw4VwIIsDeXMEwjMYRWhxzgrCnqmBCg6N9Aa1u7hg6rBK6aGith2CyZc29nGOdXBanOgWfUGGuSJbqJZ4DarpJA_cWt403R9gwC30Sxla2wazS3BouljNumLOvp8zXUstaqhuRXiQFpvqs2Fj1BUNf7klfnLehyWwmW-s-8tlbqMM03I8HU_K4ROuMcEuLHiTaJaUeHY-vqPTFTdTvvWWAiKKA2GfQ"}
   configData={configData} 
   useWorkAccess={applicationConfig.environment === "localhost" ? true : false} 
   useConfigServerDestination={applicationConfig.environment === "localhost" ? true : false} 
   destinationData={DestinationConfig} 
   userData={{ ...userData, user_id: userData?.emailId }} 
   userList={[]} 
   groupList={[]} 
   userListBySystem={usersMap} />;
};

export default SubstitutionComponent;
