import{c9 as Ue,ca as Ze,cb as Ge,di as Qe,t as Oe,r as c,aP as Xe,a as et,n as Z,s as tt,c as o,an as D,j as t,Z as G,B as p,O as y,b5 as rt,b6 as Fe,F as Ce,a8 as $,dj as I,d as Q,T as Me,a6 as X,R as W,aZ as N,cq as S,aF as we,aE as Te,aa as nt,dk as at,dl as it,C as R,dm as j,w as st,x as Ye}from"./index-226a1e75.js";var ee={},ot=Ze;Object.defineProperty(ee,"__esModule",{value:!0});var ke=ee.default=void 0,lt=ot(Ue()),ct=Ge;ke=ee.default=(0,lt.default)((0,ct.jsx)("path",{d:"M14 4v5c0 1.12.37 2.16 1 3H9c.65-.86 1-1.9 1-3V4zm3-2H7c-.55 0-1 .45-1 1s.45 1 1 1h1v5c0 1.66-1.34 3-3 3v2h5.97v7l1 1 1-1v-7H19v-2c-1.66 0-3-1.34-3-3V4h1c.55 0 1-.45 1-1s-.45-1-1-1"}),"PushPinOutlined");const dt=Qe.injectEndpoints({endpoints:O=>({getPresetFilter:O.query({query:({moduleName:u,emailId:B})=>`/presetFilter/listOfFilters/${u}/${B}`})})}),{useGetPresetFilterQuery:ht}=dt,pt=O=>it[O]||O,ft=Oe.memo(function({moduleName:u,handleSearch:B=()=>{},disabled:L=!1,onPresetActiveChange:te=()=>{},onClearPreset:Ee=()=>{}}){var De,ye,Se,be,ve,Pe;const[re,ne]=Oe.useState(null),[C,ae]=c.useState(null),[M,ie]=c.useState(null),[Ae,F]=c.useState(!1),[$e,Ie]=c.useState(!0),{customError:We}=Xe(),{t:m}=et(),se=Z(e=>e.commonFilter[u]),z=tt(),H=!!re,b=()=>{ae(""),ne(null),_()};Z(e=>e.commonFilterUpdate);const g=Z(e=>{var r;return(r=e==null?void 0:e.userManagement)==null?void 0:r.userData}),{data:oe,refetch:le}=ht({moduleName:u,emailId:g==null?void 0:g.emailId});c.useEffect(()=>{if($e){Ie(!1);return}if(Ae){const e=setTimeout(()=>{B(),F(!1)},50);return()=>clearTimeout(e)}},[se]);const ce=()=>{F(!0),(r=>{var l;if(r&&r.length>0){const n=[...r].reverse();ie(n);var s=n.filter(a=>a.defaultPin===1);if(s.length>0){A(s[0].id),K(s[0].id);let a=JSON.parse((l=s[0])==null?void 0:l.presetDescription);if(a!=null&&a.createdOn){const f=new Date,i=new Date;i.setDate(i.getDate()-7);try{let d=i,h=f;if(typeof a.createdOn=="string"){if(a.createdOn.includes("[")||a.createdOn.includes('"')){const U=a.createdOn.replace(/\\"/g,'"').replace(/\\\\/g,"\\");try{const x=JSON.parse(U);Array.isArray(x)&&x.length===2&&(d=new Date(x[0]),h=new Date(x[1]))}catch(x){We("Failed to parse date string:",x)}}}else Array.isArray(a.createdOn)&&a.createdOn.length===2&&(d=new Date(a.createdOn[0]),h=new Date(a.createdOn[1]));!isNaN(d.getTime())&&!isNaN(h.getTime())?a.createdOn=[d,h]:a.createdOn=[i,f]}catch(d){console.error("Error processing date:",d),a.createdOn=[i,f]}}z(Ye({module:u,filterData:a}))}}F(!1)})(oe)};c.useEffect(()=>{ce()},[oe]);const[de,he]=c.useState(!1),J=()=>{he(!0)},_=()=>{he(!1)},Ne=()=>{P(!0);const e=new FormData;e.append("userId",g==null?void 0:g.emailId),e.append("filterName",C),e.append("module",u),e.append("presetDescription",JSON.stringify(se));let r=l=>{le(),T(!0),k(`Preset Filter ${C} Saved successfully`),J(),P(!1)},s=l=>{T(!0),Je("Error"),k(`Preset Filter ${C} Saving Failed `),_e("danger"),q()};R(`/${j}/presetFilter/savePresetFilter`,"postformdata",r,s,e)},Re=e=>{try{A(e.id);let r=JSON.parse(e.presetDescription);if(r.createdOn){const s=new Date,l=new Date;l.setDate(l.getDate()-7);try{let n=l,a=s;if(typeof r.createdOn=="string"){if(r.createdOn.includes("[")||r.createdOn.includes('"')){const f=r.createdOn.replace(/\\"/g,'"').replace(/\\\\/g,"\\");try{const i=JSON.parse(f);Array.isArray(i)&&i.length===2&&(n=new Date(i[0]),a=new Date(i[1]))}catch(i){console.log("Failed to parse date string:",i)}}}else Array.isArray(r.createdOn)&&r.createdOn.length===2&&(n=new Date(r.createdOn[0]),a=new Date(r.createdOn[1]));!isNaN(n.getTime())&&!isNaN(a.getTime())?r.createdOn=[n,a]:r.createdOn=[l,s]}catch(n){console.error("Error processing date:",n),r.createdOn=[l,s]}}z(Ye({module:u,filterData:r}))}catch(r){console.error("Error setting filter preset:",r)}},je=(e,r)=>{const s=new FormData;s.append("filterId",r),s.append("defaultPin",e?0:1),s.append("module",u),s.append("userId",g==null?void 0:g.emailId);let l=a=>le(),n=()=>{};R(`/${j}/presetFilter/pinFilter`,"putformdata",l,n,s)},pe=()=>{P(!0);let e=s=>{ie((s==null?void 0:s.reverse())||[]),P(!1)},r=()=>{P(!1)};R(`/${j}/presetFilter/listOfFilters/${u}/${g==null?void 0:g.emailId}`,"get",e,r)},[ge,P]=c.useState(!1),Be=e=>{P(!0);let r=l=>{pe()},s=l=>{pe()};R(`/${j}/presetFilter/delete/id/${e}`,"delete",r,s)},Le=()=>{A(null),K(null),F(!0),z(st({module:u})),Ee(),T(!0),k("Preset Filter Cleared successfully"),J(),b()};c.useEffect(()=>{ce()},[]);const[V,ze]=c.useState("SelectPreFilter"),He=(e,r)=>{ze(r)},[w,T]=c.useState(!1),[fe,ue]=c.useState(!1),[me,Je]=c.useState(""),[Y,k]=c.useState(""),[xe,_e]=c.useState(""),q=()=>{ue(!0)},E=()=>{ue(!1),b()},Ve=e=>{ne(e.currentTarget)},[qe,K]=c.useState(null),[v,A]=c.useState(null);c.useEffect(()=>{te(!!v)},[v,te]);const Ke=e=>{K(e.id),A(e.id),Re(e),F(!0),T(!0),k("Preset Filter Applied successfully"),J(),b()};return o("div",{className:"reusable-preset",children:[o(D,{id:"demo-customized-button","aria-controls":H?"demo-customized-menu":void 0,"aria-haspopup":"true","aria-expanded":H?"true":void 0,variant:"outlined",disableElevation:!0,onClick:Ve,endIcon:t(W,{iconName:"KeyboardArrowDown"}),startIcon:v&&t(W,{iconName:"Check"}),sx:{position:"relative",...v&&{borderColor:(ye=(De=G)==null?void 0:De.primary)==null?void 0:ye.main,color:(be=(Se=G)==null?void 0:Se.primary)==null?void 0:be.main,fontWeight:"bold"}},children:[m(v?"Preset Active":"Preset Filter"),v&&t(p,{sx:{position:"absolute",top:-5,right:-5,width:10,height:10,borderRadius:"50%",backgroundColor:(Pe=(ve=G)==null?void 0:ve.primary)==null?void 0:Pe.main}})]}),t(at,{MenuListProps:{style:{paddingTop:"0px",paddingBottom:"0px",position:"auto"}},id:"demo-customized-menu",anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},anchorEl:re,open:H,onClose:b,children:o(p,{sx:{width:"500px",overflowY:"hidden"},children:[t(p,{sx:{borderBottom:1,width:"inherit",borderColor:"divider"},children:t(y,{container:!0,children:t(y,{item:!0,md:12,children:o(rt,{value:V,onChange:He,children:[t(Fe,{label:m("Select Filter"),value:"SelectPreFilter",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}}),t(Fe,{label:m("Save Filter"),value:"SaveFilter",sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}})]})})})}),V==="SelectPreFilter"&&(ge?o(Ce,{children:[t(p,{sx:{minHeight:"10.1rem",maxHeight:"24.8vh",overflowY:"scroll"},children:t($,{PaperProps:{style:{minHeight:"auto"}},sx:{fontWeight:500,padding:"11px","&:hover":{backgroundColor:"#EAE9FF",color:"#3B30C8"}},children:o(y,{container:!0,children:[t(y,{item:!0,xs:12,children:t(I,{variant:"text"})}),t(y,{item:!0,xs:12,children:t(I,{variant:"text"})})]})})}),t(p,{sx:{borderTop:1,width:"inherit",borderColor:"divider"},children:t($,{sx:{display:"flex",justifyContent:"right"},children:t(D,{onClick:b,variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},children:m("Cancel")})})})]}):o(Ce,{children:[t(p,{sx:{minHeight:"10.1rem",maxHeight:"24.8vh",overflowY:"scroll"},children:M&&(M==null?void 0:M.map(e=>t($,{PaperProps:{style:{minHeight:"auto"}},sx:{fontWeight:500,padding:"11px",color:qe===e.id?"#3B30C8":"inherit","&:hover":{color:"#3B30C8"}},onClick:()=>Ke(e),children:o(y,{container:!0,spacing:1,children:[o(y,{item:!0,xs:12,display:"flex",justifyContent:"space-between",alignItems:"center",children:[t(Q,{variant:"subtitle1",sx:{fontWeight:500},children:e.filterName}),o(p,{children:[t(Me,{title:e.defaultPin?"Unpin Filter":"Pin Filter",arrow:!0,componentsProps:{tooltip:{sx:{fontSize:"13px",fontWeight:500}}},children:t(X,{onClick:r=>{r.stopPropagation(),je(e.defaultPin,e.id)},size:"small",sx:{padding:0,mr:1},children:t(W,{iconName:"PushPin",isSelected:e.defaultPin})})}),t(X,{onClick:r=>{r.stopPropagation(),Be(e.id)},size:"small",sx:{padding:0},children:t(W,{iconName:"Delete",iconColor:"red"})})]})]}),t(y,{item:!0,xs:12,children:(()=>{const r=JSON.parse(e.presetDescription),s=Object.entries(r).filter(([l,n])=>n&&n!==""&&!(typeof n=="object"&&Object.values(n).every(a=>!a)));return s.length>0&&t(N,{direction:"row",spacing:.5,flexWrap:"wrap",gap:.5,children:s.map(([l,n],a)=>t(p,{sx:{backgroundColor:"#EAE9FF",borderRadius:"4px",padding:"2px 8px",fontSize:"0.75rem",color:"#3B30C8",display:"inline-flex",alignItems:"center"},children:o(Q,{component:"span",sx:{fontSize:"0.75rem",fontWeight:500},children:[`${pt(l)}: `,t("span",{style:{fontWeight:400},children:(()=>{if(typeof n=="object"){if(l==="createdOn"&&Array.isArray(n)&&n.length===2){const f=S(n[0]).format("DD MMM YYYY"),i=S(n[1]).format("DD MMM YYYY");return`${f} - ${i}`}return n.code||n.desc||JSON.stringify(n)}if(typeof n=="string"&&n.includes("$^$"))return n.split("$^$").join(", ");if(l==="createdOn"&&typeof n=="string"){const f=/"([\d-]+T[\d:.]+Z)","([\d-]+T[\d:.]+Z)"/,i=n.match(f);if(i&&i.length===3){const d=S(i[1]).format("DD MMM YYYY"),h=S(i[2]).format("DD MMM YYYY");return`${d} - ${h}`}try{const d=n.replace(/\\"/g,'"').replace(/\\\\/g,"\\"),h=JSON.parse(d);if(Array.isArray(h)&&h.length===2){const U=S(h[0]).format("DD MMM YYYY"),x=S(h[1]).format("DD MMM YYYY");return`${U} - ${x}`}}catch{}}if(typeof n=="string"&&(n.includes("[")||n.includes('"')))try{const f=n.replace(/\\"/g,'"').replace(/\\\\/g,"\\"),i=JSON.parse(f);if(Array.isArray(i)&&i.length===2&&typeof i[0]=="string"&&i[0].includes("T")){const d=S(i[0]).format("DD MMM YYYY"),h=S(i[1]).format("DD MMM YYYY");return`${d} - ${h}`}return n}catch{return n}return n})()})]})},a))})})()})]})},e.id)))}),o(p,{sx:{borderTop:1,width:"inherit",borderColor:"divider"},children:[o($,{sx:{display:"flex",justifyContent:"flex-end",gap:2},children:[t(D,{onClick:Le,variant:"outlined",color:"error",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},disabled:!v,children:m("Clear Preset")}),t(D,{onClick:b,variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,minWidth:"6rem"},children:m("Close")})]}),w&&t(we,{openSnackBar:de,handleSnackbarClose:_,alertMsg:Y}),w&&t(Te,{dialogState:fe,openReusableDialog:q,closeReusableDialog:E,dialogTitle:me,dialogMessage:Y,handleDialogConfirm:E,dialogOkText:"OK",dialogSeverity:xe})]})]})),V==="SaveFilter"&&(ge?o(p,{sx:{minHeight:"13.5rem",maxHeight:"30vh",overflow:"hidden"},children:[o(N,{mt:6,ml:6,children:[t(I,{variant:"text",width:"400px"}),t(I,{variant:"text",width:"400px"})]}),o(p,{mt:8,mr:2,display:"flex",justifyContent:"flex-end",alignItems:"flex-end",children:[t(D,{variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,marginLeft:"10px",minWidth:"6rem"},children:"Cancel"}),t(D,{variant:"contained",disabled:L,style:{height:40,marginLeft:"1rem",minWidth:"6rem",textTransform:"none"},children:"Save"})]})]}):o(p,{sx:{minHeight:"13.5rem",maxHeight:"30vh",overflow:"hidden"},children:[w&&t(Te,{dialogState:fe,openReusableDialog:q,closeReusableDialog:E,dialogTitle:me,dialogMessage:Y,handleDialogConfirm:E,dialogOkText:"OK",dialogSeverity:xe}),w&&t(we,{openSnackBar:de,handleSnackbarClose:_,alertMsg:Y}),o(N,{mt:6,ml:6,children:[t(Q,{variant:"subtitle2",fontWeight:"bold",children:m("Add Filter Name")}),o(N,{direction:"row",children:[t(nt,{id:"outlined-basic",variant:"outlined",sx:{width:"380px"},value:C,onChange:e=>{ae(e.target.value)},onKeyDown:e=>{e.stopPropagation()},fullwidth:!0,padding:"none",focused:!1,InputProps:{disableUnderline:!0,sx:{height:"35px"}}}),t(X,{sx:{padding:0,marginLeft:"1rem"},children:t(ke,{sx:{marginTop:"10px"}})})]})]}),o(p,{mt:8,mr:2,display:"flex",justifyContent:"flex-end",alignItems:"flex-end",children:[t(D,{onClick:e=>{e.stopPropagation(),b()},variant:"outlined",sx:{textTransform:"none",fontWeight:"bold"},style:{height:40,marginLeft:"10px",minWidth:"6rem"},children:"Close"}),t(Me,{title:L?"Please fill mandatory fields":"",arrow:!0,children:t("span",{children:t(D,{variant:"contained",disabled:L,onClick:Ne,style:{height:40,marginLeft:"1rem",minWidth:"6rem",textTransform:"none"},children:"Save"})})})]})]}))]})})]})});export{ft as R};
