import{r as o,u as _a,g as $a,n as F,c as C,j as n,aZ as oa,O as N,a6 as R,a_ as ja,a$ as Qa,av as qa,d as na,af as Ka,b0 as ra,T as la,aF as ca,ah as Za,an as L,ad as U,ag as Ja,au as H,C as w}from"./index-226a1e75.js";import{R as ua}from"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";const rt=()=>{const[da,ga]=o.useState(!1),[Da,Xa]=o.useState("1"),[B,pa]=o.useState([]),[ha,V]=o.useState(!1),[Ma,z]=o.useState(!1),[fa,k]=o.useState(!1),[_,$]=o.useState(""),[Ya,j]=o.useState(!0),[xa,Q]=o.useState(!0),[ba,q]=o.useState(!1),[ma,K]=o.useState(!1),[Sa,Z]=o.useState(!1),[Ca,I]=o.useState(!1),[J,X]=o.useState(""),[Ba,Y]=o.useState(!1),[ya,x]=o.useState(!1),[Na,ee]=o.useState(!1),[Pa,va]=o.useState(!1),[ka,A]=o.useState(!1),[et,ae]=o.useState(!1),[P,te]=o.useState(!1),[Ia,se]=o.useState([]),[at,E]=o.useState(!1),[Aa,Ea]=o.useState(null),[tt,Ga]=o.useState([]),[G,Oa]=o.useState(!1),[Ta,Wa]=o.useState([]),[st,ie]=o.useState(!1),O=_a();let T=O==null?void 0:O.state;console.log(T,"massHandleType");const oe=()=>{x(!0)},ne=()=>{x(!1)},Fa=()=>{K(!0)},Ra=()=>{K(!1),v("/masterDataCockpit/materialMaster/material")},re=()=>{ee(!0)},le=()=>{ee(!1)},La=()=>{Y(!0)},Ua=()=>{Y(!1),v("/masterDataCockpit/materialMaster/material")},v=$a(),ce=F(e=>e.initialData.MultipleMaterial),ue=F(e=>e.initialData.EditMultipleMaterial);let W=F(e=>e.userManagement.userData);const y=ce.map((e,r)=>{var i,t,c,p,b,u,h,M,f,g,D,d;const l=e==null?void 0:e.Description;console.log(ue,"EditMultipleMaterial");const s=ue[l]??{};return console.log(s,"editData"),console.log(e,"materialll"),{id:r,material:e==null?void 0:e.Material,materialType:e==null?void 0:e["Material Type"],industrySector:e==null?void 0:e["Industry Sector"],description:l,language:e==null?void 0:e.Language,baseUnit:(s==null?void 0:s["Base Unit"])??((t=(i=e["Basic Data"])==null?void 0:i["General Data"][3])==null?void 0:t.value),materialGroup:(s==null?void 0:s["Material Group"])??((p=(c=e["Basic Data"])==null?void 0:c["General Data"][5])==null?void 0:p.value),oldMaterialNo:(s==null?void 0:s["Old Material Number"])??((u=(b=e==null?void 0:e["Basic Data"])==null?void 0:b["General Data"][7])==null?void 0:u.value),extMatlGroup:(s==null?void 0:s["Ext Matl Group"])??((M=(h=e["Basic Data"])==null?void 0:h["General Data"][0])==null?void 0:M.value),division:(s==null?void 0:s.Division)??((g=(f=e["Basic Data"])==null?void 0:f["General Data"][6])==null?void 0:g.value),productHierarchy:(s==null?void 0:s["Product Hierarchy"])??((d=(D=e["Basic Data"])==null?void 0:D["General Data"][5])==null?void 0:d.value)}}),Ha=[{field:"material",headerName:"Material",editable:!1,flex:1,renderCell:e=>{const r=e.row.material,l=Array.isArray(G)?G.includes(r):G===r,s=B.includes(e.id),i=Ta[r]||[],t=i.includes(r),c=s&&(l||i.length>0),p=l?"Direct Match":i.length>0?`Probable Matches: ${i.join(", ")}`:"";return C("div",{style:{display:"flex",alignItems:"center",color:t?"red":"unset"},children:[e==null?void 0:e.value,c&&n(la,{title:p,children:n(R,{sx:{fontSize:"12px"},children:n(ra,{})})})]})}},{field:"materialType",headerName:"Material Type",editable:!1,flex:1},{field:"description",headerName:"Material Description",editable:!1,flex:1,renderCell:e=>{const r=e.row.description,l=Array.isArray(P)?P.includes(r):P===r,s=B.includes(e.id),i=Ia[r]||[],t=i.includes(r),c=s&&(l||i.length>0),p=l?"Direct Match":i.length>0?`Probable Matches: ${i.join(", ")}`:"";return C("div",{style:{display:"flex",alignItems:"center",color:t?"red":"unset"},children:[e==null?void 0:e.value,c&&n(la,{title:p,children:n(R,{sx:{fontSize:"12px"},children:n(ra,{})})})]})}},{field:"industrySector",headerName:"Industry Sector",editable:!1,flex:1},{field:"language",headerName:"Language",editable:!1,flex:1},{field:"baseUnit",headerName:"Base Unit",editable:!1,flex:1},{field:"materialGroup",headerName:"Material Group",editable:!1,flex:1},{field:"oldMaterialNo",headerName:"Old Material No.",editable:!1,flex:1},{field:"extMatlGroup",headerName:"Ext Material Group",editable:!1,flex:1},{field:"division",headerName:"Division",editable:!1,flex:1},{field:"productHierarchy",headerName:"Product Hierarchy",editable:!1,flex:1}],a=(e,r)=>{const l=e==null?void 0:e.find(s=>(s==null?void 0:s.fieldName)===r);return l?l.value:""};var de=ce.map(e=>{var r,l,s,i,t,c,p,b,u,h,M,f,g,D,d,m,S,De,pe,he,Me,fe,be,me,Se,Ce,Be,ye,Ne,Pe,ve,ke,Ie,Ae,Ee,Ge,Oe,Te,We,Fe,Re,Le,Ue,He,we,Ve,ze,_e,$e,je,Qe,qe,Ke,Ze,Je,Xe,Ye,xe,ea,aa,ta,sa,ia;return{ProductID:"",BasicDataID:"",Product:e==null?void 0:e.Material,ProductType:e==null?void 0:e["Material Type"],ViewNames:"Basic Data",CreationID:"",Description:e==null?void 0:e.Description,EditID:"",ExtendID:"",MassCreationID:"",MassEditID:"",MassExtendID:"",CrossPlantStatus:"01",CrossPlantStatusValidityDate:"/Date(1694995200000)/",ReqCreatedOn:e!=null&&e.ReqCreatedOn?"/Date("+Date.parse(e==null?void 0:e.ReqCreatedOn)+")/":"",ReqCreatedBy:e!=null&&e.ReqCreatedBy?e==null?void 0:e.ReqCreatedBy:W==null?void 0:W.user_id,IsMarkedForDeletion:e!=null&&e.IsMarkedForDeletion?e==null?void 0:e.IsMarkedForDeletion:"",ProductOldID:a((r=e==null?void 0:e["Basic Data"])==null?void 0:r["General Data"],"Old Material Number"),GrossWeight:a((l=e==null?void 0:e["Basic Data"])==null?void 0:l["Dimensions/ EANS"],"Gross Weight")?a((s=e==null?void 0:e["Basic Data"])==null?void 0:s["Dimensions/ EANS"],"Gross Weight"):"",PurchaseOrderQuantityUnit:(i=e==null?void 0:e.payload)!=null&&i.OrderUnit?(t=e==null?void 0:e.payload)==null?void 0:t.OrderUnit:"",SourceOfSupply:"",WeightUnit:a((c=e==null?void 0:e["Basic Data"])==null?void 0:c["Dimensions/ EANS"],"Weight Unit")?a((p=e==null?void 0:e["Basic Data"])==null?void 0:p["Dimensions/ EANS"],"Weight Unit"):"",NetWeight:a((b=e==null?void 0:e["Basic Data"])==null?void 0:b["Dimensions/ EANS"],"Net Weight")?a((u=e==null?void 0:e["Basic Data"])==null?void 0:u["Dimensions/ EANS"],"Net Weight"):"",CountryOfOrigin:"",CompetitorID:"",ProductGroup:a((h=e==null?void 0:e["Basic Data"])==null?void 0:h["General Data"],"Material Group"),BaseUnit:a((M=e==null?void 0:e["Basic Data"])==null?void 0:M["General Data"],"Base Unit"),ItemCategoryGroup:a((f=e==null?void 0:e["Basic Data"])==null?void 0:f["General Data"],"Item Category Group"),ProductHierarchy:a((g=e==null?void 0:e["Basic Data"])==null?void 0:g["General Data"],"Product Hierarchy"),Division:a((D=e==null?void 0:e["Basic Data"])==null?void 0:D["General Data"],"Division"),VarblPurOrdUnitIsActive:(d=e==null?void 0:e.payload)!=null&&d.VarPurOrderUnitActive?(m=e==null?void 0:e.payload)==null?void 0:m.VarPurOrderUnitActive:"",VolumeUnit:a((S=e==null?void 0:e["Basic Data"])==null?void 0:S["Dimensions/ EANS"],"Volume Unit")?a((De=e==null?void 0:e["Basic Data"])==null?void 0:De["Dimensions/ EANS"],"Volume Unit"):"",MaterialVolume:a((pe=e==null?void 0:e["Basic Data"])==null?void 0:pe["Dimensions/ EANS"],"Volume")?a((he=e==null?void 0:e["Basic Data"])==null?void 0:he["Dimensions/ EANS"],"Volume"):"",ANPCode:a((Me=e==null?void 0:e["Basic Data"])==null?void 0:Me["Brazil Tax Data"],"ANP Code")?a((fe=e==null?void 0:e["Basic Data"])==null?void 0:fe["Brazil Tax Data"],"ANP Code"):"",Brand:"",ProcurementRule:"",ValidityStartDate:"",LowLevelCode:"",ProdNoInGenProdInPrepackProd:"",SizeOrDimensionText:a((be=e==null?void 0:e["Basic Data"])==null?void 0:be["Dimensions/ EANS"],"Size/Dimensions"),IndustryStandardName:a((me=e==null?void 0:e["Basic Data"])==null?void 0:me["Other Data"],"Ind Std Name"),ProductStandardID:a((Se=e==null?void 0:e["Basic Data"])==null?void 0:Se["Dimensions/ EANS"],"International Article Number (EAN/UPC)"),InternationalArticleNumberCat:a((Ce=e==null?void 0:e["Basic Data"])==null?void 0:Ce["Dimensions/ EANS"],"Category Of International Article Number (EAN)"),ProductIsConfigurable:a((Be=e==null?void 0:e["Basic Data"])==null?void 0:Be["Client Specific Configuration"],"Configurable Material")?a((ye=e==null?void 0:e["Basic Data"])==null?void 0:ye["General Data"],"Configurable Material"):"false",IsBatchManagementRequired:(Ne=e==null?void 0:e.payload)!=null&&Ne.BatchManagement?(Pe=e==null?void 0:e.payload)==null?void 0:Pe.BatchManagement:"false",ExternalProductGroup:a((ve=e==null?void 0:e["Basic Data"])==null?void 0:ve["General Data"],"Ext Matl Group"),CrossPlantConfigurableProduct:a((ke=e==null?void 0:e["Basic Data"])==null?void 0:ke["Client Specific Configuration"],"Cross Plant Conf Material"),SerialNoExplicitnessLevel:"",ProductManufacturerNumber:"",ManufacturerNumber:"",ManufacturerPartProfile:"",QltyMgmtInProcmtIsActive:"false",IndustrySector:e!=null&&e["Industry Sector"]?e==null?void 0:e["Industry Sector"]:"",ChangeNumber:a((Ie=e==null?void 0:e["Basic Data"])==null?void 0:Ie["Design Drawing"],"Doc Change Number")?a((Ae=e==null?void 0:e["Basic Data"])==null?void 0:Ae["Design Drawing"],"Doc Change Number"):"",MaterialRevisionLevel:"",HandlingIndicator:a((Ee=e==null?void 0:e["Basic Data"])==null?void 0:Ee["WM Execution Data"],"Handling Indicator")?a((Ge=e==null?void 0:e["Basic Data"])==null?void 0:Ge["WM Execution Data"],"Handling Indicator"):"",WarehouseProductGroup:a((Oe=e==null?void 0:e["Basic Data"])==null?void 0:Oe["WM Execution Data"],"Warehouse Material Group")?a((Te=e==null?void 0:e["Basic Data"])==null?void 0:Te["WM Execution Data"],"Warehouse Material Group"):"",WarehouseStorageCondition:a((We=e==null?void 0:e["Basic Data"])==null?void 0:We["WM Execution Data"],"Warehouse Storage Condition")?a((Fe=e==null?void 0:e["Basic Data"])==null?void 0:Fe["WM Execution Data"],"Warehouse Storage Condition"):"",StandardHandlingUnitType:a((Re=e==null?void 0:e["Basic Data"])==null?void 0:Re["WM Execution Data"],"Std HU Type")?a((Le=e==null?void 0:e["Basic Data"])==null?void 0:Le["WM Execution Data"],"Std HU Type"):"",SerialNumberProfile:a((Ue=e==null?void 0:e["Basic Data"])==null?void 0:Ue["WM Execution Data"],"Serial Number Profile")?a((He=e==null?void 0:e["Basic Data"])==null?void 0:He["WM Execution Data"],"Serial Number Profile"):"",AdjustmentProfile:"",PreferredUnitOfMeasure:"",IsPilferable:a((we=e==null?void 0:e["Basic Data"])==null?void 0:we["WM Execution Data"],"Pilferable")?a((Ve=e==null?void 0:e["Basic Data"])==null?void 0:Ve["WM Execution Data"],"Pilferable"):"false",IsRelevantForHzdsSubstances:a((ze=e==null?void 0:e["Basic Data"])==null?void 0:ze["WM Execution Data"],"Relevant For Hazardous Substances")?a((_e=e==null?void 0:e["Basic Data"])==null?void 0:_e["WM Execution Data"],"PilfeRelevant For Hazardous Substancesrable"):"false",QuarantinePeriod:a(($e=e==null?void 0:e["Basic Data"])==null?void 0:$e["Quality Management"],"Relevant For Hazardous Substances")?a((je=e==null?void 0:e["Basic Data"])==null?void 0:je["Quality Management"],"Relevant For Hazardous Substances"):"0",TimeUnitForQuarantinePeriod:"",QualityInspectionGroup:a((Qe=e==null?void 0:e["Basic Data"])==null?void 0:Qe["Quality Management"],"Quality Inspection Group")?a((qe=e==null?void 0:e["Basic Data"])==null?void 0:qe["Quality Management"],"Quality Inspection Group"):"",AuthorizationGroup:a((Ke=e==null?void 0:e["Basic Data"])==null?void 0:Ke["General Data"],"Authorization Group"),HandlingUnitType:a((Ze=e==null?void 0:e["Basic Data"])==null?void 0:Ze["General Packaging"],"Handling Unit Type")?a((Je=e==null?void 0:e["Basic Data"])==null?void 0:Je["General Packaging"],"Handling Unit Type"):"",HasVariableTareWeight:a((Xe=e==null?void 0:e["Basic Data"])==null?void 0:Xe["General Packaging"],"Variable Tare Weight")?a((Ye=e==null?void 0:e["Basic Data"])==null?void 0:Ye["General Packaging"],"Variable Tare Weight"):"false",MaximumPackagingLength:a((xe=e==null?void 0:e["Basic Data"])==null?void 0:xe["Maximum Length"],"Max Packaging Length")?a((ea=e==null?void 0:e["Basic Data"])==null?void 0:ea["Maximum Length"],"Max Packaging Length"):"0",MaximumPackagingWidth:a((aa=e==null?void 0:e["Basic Data"])==null?void 0:aa["Maximum Length"],"Max Packaging Width")?a((ta=e==null?void 0:e["Basic Data"])==null?void 0:ta["Maximum Length"],"Max Packaging Width"):"0",MaximumPackagingHeight:a((sa=e==null?void 0:e["Basic Data"])==null?void 0:sa["Maximum Length"],"Max Packaging Height")?a((ia=e==null?void 0:e["Basic Data"])==null?void 0:ia["Maximum Length"],"Max Packaging Height"):"0",UnitForMaxPackagingDimensions:"",to_Description:[{Product:"",DescriptionID:"",Language:e.Language,ProductDescription:e.Description}]}});const wa=e=>{console.log("Selected Rows: ",e),pa(e),va(e.length>0),e.length>0?ge():(te(!1),se([]),A(!1),E(!1))},ge=e=>{const r=y.filter((t,c)=>B.includes(c)),l=r.map(t=>t.description),s=r.map(t=>t.material),i=t=>{const c=u=>{if(Ea({...Aa,[t]:u.body}),Ga(u.body),u.body){const h=Object.keys(u.body);console.log(`Concatenated Values (${t}):`,h);const M=l.reduce((g,D)=>{const d=u.body[D];return d&&d.length>0&&(g[D]=d),g},{});console.log(`Probables for Selected Descriptions (${t}):`,M);const f=Object.values(M).some(g=>g.some(D=>h.includes(D)));if(console.log(`isDirectMatch (${t}):`,f),t==="description"&&E?A(!0):t==="number"&&ie?ae(!0):t==="description"?A(!1):t==="number"&&ae(!1),t==="description")E(f),te(f),se(M);else if(t==="number"){const g=s.reduce((d,m)=>{const S=u.body[m];return S&&S.length>0&&(d[m]=S),d},{}),D=Object.values(g).some(d=>d.some(m=>h.includes(m)));ie(D),Oa(D),Wa(g)}ga(!1)}else console.error("Data body is undefined or null")},p=u=>{console.log(u)},b=`/${H}/massAction/${t==="description"?"fetchMatDescsDupliChk":"fetchMaterialNosDupliChk"}?${t==="description"?"descriptionsToCheck":"materialNosToCheck"}=${t==="description"?l.join(","):s.join(",")}`;w(b,"get",c,p)};i("description"),i("number")},Va=e=>{const l=y.filter((t,c)=>B.includes(c)).map(t=>({...de[t==null?void 0:t.id]})),s=t=>{(t==null?void 0:t.statusCode)!=201?(k("Error"),V(!1),$(`${t.body.message[0]}`),z("danger"),j(!1),oe()):(k("Mass Create"),console.log("success"),k("Create"),$("Mass Material Submitted for Approval"),z("success"),j(!1),V(!0),Fa())},i=t=>{console.log(t)};w(`/${H}/massAction/createMaterialsApprovalSubmit`,"post",s,i,l)},za=()=>{const r=y.filter((i,t)=>B.includes(t)).map(i=>({...de[i==null?void 0:i.id]})),l=i=>{(i==null?void 0:i.statusCode)!=201?(I("Error"),q(!1),X(`${i.body.message[0]}`),Z("danger"),Q(!1),re()):(I("Mass Create"),console.log("success"),I("Create"),X("Mass Material Change Submitted for Approval"),Z("success"),Q(!1),q(!0),La())},s=i=>{console.log(i)};w(`/${H}/massAction/changeMaterialsApprovalSubmit`,"post",l,s,r)};return C("div",{style:{backgroundColor:"#FAFCFF"},children:[n(ua,{dialogState:ya,openReusableDialog:oe,closeReusableDialog:ne,dialogTitle:fa,dialogMessage:_,handleDialogConfirm:ne,dialogOkText:"OK",dialogSeverity:Ma}),ha&&n(ca,{openSnackBar:ma,alertMsg:_,handleSnackBarClose:Ra}),n(ua,{dialogState:Na,openReusableDialog:re,closeReusableDialog:le,dialogTitle:Ca,dialogMessage:J,handleDialogConfirm:le,dialogOkText:"OK",dialogSeverity:Sa}),ba&&n(ca,{openSnackBar:Ba,alertMsg:J,handleSnackBarClose:Ua}),n("div",{style:{backgroundColor:"#FAFCFF"},children:C(oa,{spacing:1,sx:{padding:"16px"},children:[n(N,{container:!0,children:C(N,{item:!0,md:5,sx:{display:"flex"},children:[n(N,{children:n(R,{color:"primary","aria-label":"upload picture",component:"label",sx:ja,children:n(Qa,{style:{height:"1em",width:"1em",color:"#000000"},onClick:()=>{v("/masterDataCockpit/materialMaster/material"),dispatch(qa()),dispatch(clearOrgData())}})})}),C(N,{children:[n(na,{variant:"h3",children:n("strong",{children:"Create Multiple Materials"})}),n(na,{variant:"body2",color:"#777",children:"This view displays list of uploaded materials"})]})]})}),n(N,{item:!0,sx:{position:"relative"},children:n(oa,{children:n(Ka,{isLoading:da,width:"100%",title:"Mass Material Master List ("+y.length+")",rows:y,columns:Ha,pageSize:10,getRowIdValue:"id",hideFooter:!1,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowSingleClick:!0,onRowsSelectionHandler:wa,callback_onRowSingleClick:e=>{const r=e.row.description;v(`/masterDataCockpit/materialMaster/editMultipleMaterial/${r}`,{state:e.row})},stopPropagation_Column:"action",status_onRowDoubleClick:!0})})})]})}),n(Ja,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:n(Za,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",gap:1,justifyContent:"flex-end"},value:Da,children:T==="Create"?[n(L,{variant:"contained",size:"small",sx:{...U},onClick:ge,disabled:!Pa,children:"Duplicate Check"},"duplicateDescButton"),n(L,{variant:"contained",size:"small",sx:{...U},onClick:Va,disabled:!ka||P,children:"Submit for Approval"},"submitForApprovalButton")]:T==="Change"?n(L,{variant:"contained",size:"small",sx:{...U},onClick:za,children:"Submit for Approval"}):""})})]})};export{rt as default};
