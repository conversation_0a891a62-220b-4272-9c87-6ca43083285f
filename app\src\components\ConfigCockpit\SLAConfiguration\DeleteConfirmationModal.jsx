// DeleteConfirmationModal.js
import React from 'react';
import {
  Modal,
  Typography,
  Space,
  Tag,
  Card,
  Alert,
  Row,
  Col
} from 'antd';
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const DeleteConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  selectedRowData
}) => {
  if (!selectedRowData) {
    return null;
  }

  return (
    <Modal
      title={
        <Title level={4} style={{ margin: 0, color: '#ff4d4f' }}>
          <DeleteOutlined style={{ marginRight: 8 }} />
          Confirm Deletion
        </Title>
      }
      style={{ marginTop: 60}}
      open={open}
      onOk={onConfirm}
      onCancel={onClose}
      okText="Yes, Delete"
      cancelText="Cancel"
      okButtonProps={{ 
        danger: true,
        icon: <DeleteOutlined />
      }}
      cancelButtonProps={{
        type: 'default'
      }}
      width={500}
      destroyOnClose
      maskClosable={false}
      centered
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {/* Warning Alert */}
        <Alert
          message="This action cannot be undone"
          description="Once deleted, this business hour configuration will be permanently removed from the system."
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          style={{ marginTop: 16 }}
        />

        {/* Configuration Details */}
        <Card 
          size="small" 
          title={
            <Text strong>
              <InfoCircleOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              Configuration to be deleted:
            </Text>
          }
          style={{ backgroundColor: '#fafafa' }}
        >
          <Row gutter={[16, 8]}>
            <Col span={12}>
              <Text type="secondary">Region:</Text>
              <br />
              <Tag color="blue">{selectedRowData.region}</Tag>
            </Col>
            <Col span={12}>
              <Text type="secondary">Day:</Text>
              <br />
              <Tag color="green">{selectedRowData.dayOfWeek}</Tag>
            </Col>
            <Col span={24}>
              <Text type="secondary">Business Hours:</Text>
              <br />
              <Text strong>
                {selectedRowData.workStartTime} - {selectedRowData.workEndTime}
              </Text>
            </Col>
            {selectedRowData.offHoursRanges && selectedRowData.offHoursRanges.length > 0 && (
              <Col span={24}>
                <Text type="secondary">Off Hours:</Text>
                <br />
                <Space wrap size="small">
                  {selectedRowData.offHoursRanges.map((range, index) => (
                    <Tag key={index} color="orange" size="small">
                      {range.startTime} - {range.endTime}
                    </Tag>
                  ))}
                </Space>
              </Col>
            )}
          </Row>
        </Card>

        {/* Confirmation Message */}
        <div style={{ 
          textAlign: 'center', 
          padding: '16px',
          backgroundColor: '#fff2f0',
          border: '1px solid #ffccc7',
          borderRadius: '6px'
        }}>
          <ExclamationCircleOutlined 
            style={{ 
              fontSize: '24px', 
              color: '#ff4d4f', 
              marginBottom: '8px' 
            }} 
          />
          <br />
          <Text strong style={{ fontSize: '16px' }}>
            Are you sure you want to delete this configuration?
          </Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px', marginTop: '4px' }}>
            This will permanently remove the business hour settings for {selectedRowData.region} on {selectedRowData.dayOfWeek}s.
          </Text>
        </div>
      </Space>
    </Modal>
  );
};

export default DeleteConfirmationModal;