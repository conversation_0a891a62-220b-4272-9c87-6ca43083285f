import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

const createProxy = (prefix, target, rewritePrefix = "/rest") => ({
  target,
  changeOrigin: true,
  rewrite: (path) => path.replace(new RegExp(`^${prefix}`), rewritePrefix),
});

// ✅ Your hardcoded tokens (keep short here for readability)
const IWA_TOKEN = `Bearer ********************************************************************************************************************************************************************************************************************************************************************************.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.f4FAl2lC4u-gBG3XrL7mx9MEfpoIWWp237txhRwcFAWarmYoB_DngssMGohSsD_hdBjzsK0sjUsRAVpsgusVMGvtPwuaq5VPpGTar9ADwxEvOuXxL9Wz2kBOuBFg69tTEmpJsxXnFlrTTbhVTkePUUcZA4JD2zW66JGYzDHhMqLQEsW4dIUX2LXfcvIKagUiW7es5uUV2jbyA4N6PcjfTpz_hP6Vr6_4Cf20kq9H7su7mQb60mG5eyTo6bcK64Rt2bPzqu6-OOM9eJE2-i9y-P_pmghOWqHaJEnWq8IaRu0fDjkQjND_4lYiq6jo_irwYoS0ZpVLimgKo-23f8i_0w`;
const IDM_TOKEN = `Bearer eyJhbGciOiJSUzI1NiIsImprdSI6Imh0dHBzOi8vaW5jdHVyZS1jaGVycnl3b3JrLWRldi5hdXRoZW50aWNhdGlvbi5ldTEwLmhhbmEub25kZW1hbmQuY29tL3Rva2VuX2tleXMiLCJraWQiOiJkZWZhdWx0LWp3dC1rZXktMTY4NjE5NzU3MyIsInR5cCI6IkpXVCIsImppZCI6ICJ6UWxyZVNTQytoaDUwVWdaSXA1b0lKUms4cEplWlFlT3BVMEFjamQ0SVRNPSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.p80wUJwseeBgg364r-TVfRd3E7nC8JypVM4YLNwoM4rLQQTFBsEcg2YorCDCQxMG9mV6ti-KMTElOnAo4qtZ9Q4Av051COQrnBkjk1K2wIat-b8yioYY8rHg2QynsDzGAXVNVqfpqQCFzLGMAhFavl-wxzsWEc6f8ziPPU0NY_vqusGujrtDwYS5cnGFBJr5Pcyj9M-eaRAxMEKP-dLsrJgEzXOANUSpuovUukUE6855JwH7r7jChsVgEzsMU7M1cgevA0ympY6SBwblAvsjVnG-k3UMFWl457IJ_qO2GrIWq4Ha28-ScZdE33khHgZFT9gakcxB3DUGC73hbQgfFg`;

export default defineConfig({
  optimizeDeps: {
    include: ["@emotion/react", "@emotion/styled", "@mui/material/Tooltip"],
  },
  plugins: [
    react({
      jsxImportSource: "@emotion/react",
      babel: {
        plugins: ["@emotion/babel-plugin"],
      },
    }),
  ],
  resolve: {
    alias: {
      "@components": path.resolve(__dirname, "src/components"),
      "@constant": path.resolve(__dirname, "src/constant"),
      "@data": path.resolve(__dirname, "src/data"),
      "@helper": path.resolve(__dirname, "src/helper"),
      "@hooks": path.resolve(__dirname, "src/hooks"),
      "@utilityImages": path.resolve(__dirname, "src/utilityImages"),
      "@app": path.resolve(__dirname, "src/app"),
      "@material": path.resolve(__dirname, "src/modules/material"),
      "@article": path.resolve(__dirname, "src/modules/article"),
      "@destinations": path.resolve(__dirname, "src/destinationVariables"),
      "@slice": path.resolve(__dirname, "src/app"),
      "@costCenter": path.resolve(__dirname, "src/modules/costCenter"),
      "@profitCenter": path.resolve(__dirname, "src/modules/profitCenter"),
      "@generalLedger": path.resolve(__dirname, "src/modules/generalLedger"),
      "@costCenterGroup": path.resolve(__dirname, "src/modules/costCenterGroup"),
      "@profitCenterGroup": path.resolve(__dirname, "src/modules/profitCenterGroup"),
      "@costElementGroup": path.resolve(__dirname, "src/modules/costElementGroup"),
      "@bankKey": path.resolve(__dirname, "src/modules/bankKey"),
      "@BillOfMaterial": path.resolve(__dirname, "src/modules/BillOfMaterial"),
      "@InternalOrder" : path.resolve(__dirname,"src/modules/InternalOrder"),
      "@modules": path.resolve(__dirname, "src/modules"),
      "@utils": path.resolve(__dirname, "src/utils"),
      "@baseUrl": path.resolve(__filename, "src/data/baseUrl.js"),
      "@api": path.resolve(__dirname, "src/api"),
      "@screens": path.resolve(__dirname,"src/screens")
    },
  },
  server: {
    host: "0.0.0.0",
    port: 5173,
    open: true,
    proxy: {
      "/IWAApi": {
        target: "https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/IWAApi/, ""),
        secure: false,
        headers: {
          Authorization: IWA_TOKEN,
        },
      },
      "/cw-caf-idm-services": {
        ...createProxy(
          "/cw-caf-idm-services",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/",
          "/rest"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkUtilsServices": {
        ...createProxy(
          "/WorkUtilsServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRulesServices": {
        ...createProxy(
          "/WorkRulesServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
      "/WorkRuleEngineServices": {
        ...createProxy(
          "/WorkRuleEngineServices",
          "https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com/"
        ),
        headers: {
          Authorization: IDM_TOKEN,
        },
      },
    },
  },
});
