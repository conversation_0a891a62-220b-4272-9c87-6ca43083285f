import { <PERSON><PERSON>, <PERSON>ack, Typography, I<PERSON><PERSON><PERSON>on, Tooltip } from "@mui/material";
import {
  setMultipleMaterialPayloadKey,
  updateMaterialData,
} from "../../../app/payloadslice";
import { DateTimePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { useDispatch, useSelector } from "react-redux";
import { useChangeLogUpdate } from "@hooks/useChangeLogUpdate";
import { useLocation } from "react-router-dom";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { colors } from "@constant/colors";
import { CHANGE_LOG_STATUSES, FIELD_VISIBILITY, VISIBILITY_TYPE } from "@constant/enum";
import SkeletonWithFallback from "@components/Common/ui/SkeletonWithFallback";
import ClearIcon from "@mui/icons-material/Clear";
import useLang from "@hooks/useLang";
import { handleMandatoryField } from "@material/utils/handleMandatoryField";

export default function DateType(props) {
  const dispatch = useDispatch();
  const { updateChangeLog } = useChangeLogUpdate();
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const requestId = queryParams.get("RequestId");
  const initialPayload = useSelector((state) => state.payload.payloadData);
  const SAPview = location.pathname.includes("DisplayMaterialSAPView");
  const storedRows = useSelector((state) => state.request.materialRows);

  const [value, setValue] = useState(null);
  const [showDateError, setShowDateError] = useState(false);
  const { t } = useLang();

  const handleClearDate = () => {
    setValue(null);
    if (keyName) {
      dispatch(
        setMultipleMaterialPayloadKey({
          materialID: materialID,
          keyName: keyName,
          data: null,
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );

      dispatch(
        updateMaterialData({
          materialID: materialID || "",
          keyName: keyName || "",
          data: null,
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );

      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props?.selectedMaterialNumber,
          viewName: props?.viewName,
          plantData: props?.plantData,
          fieldName: props?.details?.fieldName,
          jsonName: props?.details?.jsonName,
          currentValue: null,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }

      handleMandatoryField({
        details: props?.details,
        newValue:null,
        materialID: props?.materialID,
        storedRows,
        dispatch,
      });
    }
  };

  const valueFromPayload = useSelector((state) => state.payload || {});

  const initialFieldValue =
    valueFromPayload?.[props?.materialID]?.payloadData?.[props?.viewName]?.[
      props?.plantData
    ]?.[props?.keyName] || 
    valueFromPayload?.payloadData?.[props?.viewName]?.[props?.plantData]?.[
      props?.keyName
    ] ||
    props?.details?.fieldPriority === 'ApplyDef' ? props?.details?.value : "";

  const errorFields = useSelector((state) => state.payload?.errorFields || []);

  const today = dayjs();

  useEffect(() => {
    if (initialFieldValue) {
      setValue(dayjs(initialFieldValue)); // Convert to dayjs if it's a valid date
    } else {
      setValue(null); // Reset to null if no valid date
      // NOTE: Commented the code due to unneceesary dispatch of ValidFrom
      
      // dispatch(
      //   updateMaterialData({
      //     materialID: materialID || "",
      //     keyName: keyName || "",
      //     data: null,
      //     viewID: props?.viewName,
      //     itemID: props?.plantData,
      //   })
      // );
    }
  }, [initialFieldValue]);

  const handleDateChange = (newValue) => {
    if (keyName) {
      const formattedDate = newValue ? newValue.toISOString() : null;
      const sapFomatDate = `/Date(${Date.parse(formattedDate)})/`;

      dispatch(
        setMultipleMaterialPayloadKey({
          materialID: materialID,
          keyName: keyName,
          data: formattedDate,
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );

      dispatch(
        updateMaterialData({
          materialID: materialID || "",
          keyName: keyName || "",
          data: formattedDate,
          viewID: props?.viewName,
          itemID: props?.plantData,
        })
      );

      setValue(newValue);
      if (requestId && !CHANGE_LOG_STATUSES.includes(initialPayload?.RequestStatus)) {
        updateChangeLog({
          materialID: props?.selectedMaterialNumber,
          viewName: props?.viewName,
          plantData: props?.plantData,
          fieldName: props?.details?.fieldName,
          jsonName: props?.details?.jsonName,
          currentValue: sapFomatDate,
          requestId: initialPayload?.RequestId,
          childRequestId:requestId
        });
      }
    }
  };

  useEffect(() => {
    const launchDate = dayjs(valueFromPayload?.payloadData?.["LaunchDate"]);
    const firstProductionDate = dayjs(valueFromPayload?.payloadData?.["FirstProductionDate"]);

    if (
      !valueFromPayload?.payloadData?.["LaunchDate"] ||
      !valueFromPayload?.payloadData?.["FirstProductionDate"]
    ) {
      setShowDateError(false);
    } else if (launchDate.isBefore(firstProductionDate)) {
      setShowDateError(true); 
    } else {
      setShowDateError(false);
    }
  }, [
    valueFromPayload?.payloadData?.["LaunchDate"],
    valueFromPayload?.payloadData?.["FirstProductionDate"]
  ]);

  // Ensure props and required keys exist
  const fieldName = props?.details?.fieldName || "Field Name";
  const visibility = props?.details?.visibility || "";
  const keyName = props?.keyName || "";
  const materialID = props?.materialID || "";
  return (
    <Grid item md={props.width ? 6 : 2}>
      <Stack>
        {SAPview ? (
          <div
            style={{
              padding: "16px",
              backgroundColor: colors.primary.white,
              borderRadius: "8px",
              boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
              transition: "all 0.3s ease",
            }}
          >
            <Typography
              variant="body1"
              style={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "100%",
                fontWeight: 600,
                fontSize: "12px",
                color: colors.secondary.grey,
                marginBottom: "4px",
              }}
              title={t(props?.details?.fieldName)}
            >
              {t(props?.details?.fieldName) || "Field Name"}
              {(props?.details?.visibility === VISIBILITY_TYPE.REQUIRED ||
                props?.details?.visibility === FIELD_VISIBILITY.MANDATORY) && (
                <span style={{ color: colors.error.darkRed, marginLeft: "2px" }}>*</span>
              )}
            </Typography>

            <div
              style={{
                fontSize: "0.8rem",
                color: colors.black.dark,
                minHeight: "25px",
                marginTop: "4px",
              }}
            >
              <span
                style={{
                  fontWeight: 500,
                  color: colors.black.dark,
                  letterSpacing: "0.5px",
                  wordSpacing: "1px",
                }}
              >
                {value && value.$isDayjsObject
                  ? value.isValid()
                    ? value.format("YYYY-MM-DD")
                    : "--"
                  : <SkeletonWithFallback fallback="--" />}
              </span>
            </div>
          </div>
        ) : (
          <>
            <Typography
              variant="body2"
              sx={{
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "100%",
              }}
              title={fieldName}
            >
              {t(fieldName)}
              {(visibility === "Required" || visibility === "0") && (
                <span style={{ color: "red" }}>*</span>
              )}
            </Typography>

            <Tooltip title={props.details?.fieldTooltip || ""} arrow placement="top">
              <Stack direction="row" spacing={1} alignItems="center">
                <LocalizationProvider dateAdapter={AdapterDayjs} sx={{ flex: 1 }}>
                  <DateTimePicker
                    slotProps={{ 
                      textField: {
                        size: "small",
                        placeholder: props.disabled ? "" : t("Select date"),
                        fullWidth: true,
                        sx: {
                          "& .MuiInputBase-root.Mui-disabled": {
                            "& > input": {
                              WebkitTextFillColor: colors.black.dark,
                              color: colors.black.dark,
                            },
                            backgroundColor: colors.hover.light,
                          },
                          width: "100%",
                        },
                      },
                    }}
                    value={value}
                    disabled={props.disabled || props.details?.visibility === VISIBILITY_TYPE.DISPLAY}
                    onChange={handleDateChange}
                    onError={() => errorFields.includes(keyName)}
                    required={visibility === "0" || visibility === "Required"}
                    renderInput={(params) => (
                      <params.TextField
                        {...params}
                        error={errorFields.includes(keyName)}
                      />
                    )}
                    sx={{ width: "100%" }}
                  />
                </LocalizationProvider>
                
                {value && !props.disabled && (
                  <IconButton 
                    size="small" 
                    onClick={handleClearDate}
                    sx={{ 
                      color: colors.secondary.grey,
                      padding: '4px',
                      flexShrink: 0,
                    }}
                  >
                    <ClearIcon fontSize="small" />
                  </IconButton>
                )}
              </Stack>
            </Tooltip>
          </>
        )}
        {showDateError && keyName === "FirstProductionDate" && (
          <Typography variant="body2" color="error" sx={{ marginTop: 1 }}>
            {t("The First production date should precede the launch date.")}
          </Typography>
        )}
      </Stack>
    </Grid>
  );
}
