import React from 'react';
import {
  <PERSON>,
  Button,
  Typography,
  Row,
  Col
} from 'antd';
import { BottomNavigation, Box, Paper, Stack, useTheme } from '@mui/material';
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import ChangeLogWF from './ChangeLogWF';
import { colors } from '@constant/colors';

const WorkflowLayout = ({
  children,
  handleSubmitWorkflow,
  blurLoading,
  loaderMessage,
  openChangeLog,
  handleChangeLogClose,
  changeLogData,
  mode
}) => {
  const theme = useTheme()
  return (
    <>
      {/* Main Content */}
      {children}

      {/* Bottom Navigation */}
      <Stack>
        <Paper
          sx={{ position: "fixed", bottom: 0, left: 0, right: 0 }}
          elevation={2}
        >
          <BottomNavigation
            className="container_BottomNav"
            showLabels
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              gap: 1,
              width: "100%",
            }}
          >
            <Box sx={{ display: "flex", gap: 1 }}>
              <>
                {mode !== "view" && (
                  <Button
                    type="primary"
                    style={{
                      backgroundColor: theme.palette.primary.main,
                      borderRadius: '5px',
                    }}
                    onClick={handleSubmitWorkflow}
                  >
                    Submit
                  </Button>
                )}
              </>
            </Box>
          </BottomNavigation>

          {/* Backdrop Loading */}
          {blurLoading && <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />}

          {/* Change Log Modal */}
          {openChangeLog && (
            <ChangeLogWF
              open={openChangeLog}
              onClose={handleChangeLogClose}
              data={changeLogData}
            />
          )}
        </Paper>
      </Stack>
    </>
  );
};

export default WorkflowLayout;