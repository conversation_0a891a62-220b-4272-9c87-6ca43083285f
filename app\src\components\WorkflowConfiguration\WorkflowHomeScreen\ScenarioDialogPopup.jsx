import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Typography,
  IconButton,
  Box,
  Paper,
  Divider,
  Fade
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import CloseIcon from '@mui/icons-material/Close';

const ScenarioDialogPopup = ({
  open,
  onClose,
  setDialogState,
  handleOpenDialog
}) => (
  <Dialog
    open={open}
    onClose={onClose}
    TransitionComponent={Fade}
    maxWidth="md"
    fullWidth
    PaperProps={{
      sx: {
        borderRadius: 3,
        boxShadow: 10,
        overflow: 'hidden',
      },
    }}
  >
    <DialogTitle
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        px: 3,
        py: 2,
        backgroundColor: (theme) => theme.palette.grey[100],
        borderBottom: '1px solid',
        borderColor: 'divider',
      }}
    >
      <Typography variant="h6" fontWeight="bold">
        Which scenario do you want?
      </Typography>
      <IconButton
        onClick={onClose}
        sx={{
          color: 'text.secondary',
          '&:hover': {
            color: 'error.main',
          },
        }}
      >
        <CloseIcon />
      </IconButton>
    </DialogTitle>

    <DialogContent sx={{ px: 3, py: 4, mb: -7 }}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 4,
          mt: 4,
        }}
      >
        {/* Create Workflow Card */}
        <Paper
          elevation={5}
          onClick={() => {
            setDialogState((prev) => ({ ...prev, createWorkflow: false }));
            handleOpenDialog();
          }}
          sx={{
            flex: 1,
            p: 7,
            cursor: 'pointer',
            borderRadius: 3,
            border: '2px solid transparent',
            background: (theme) =>
              `linear-gradient(145deg, ${theme.palette.background.paper}, ${theme.palette.grey[100]})`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            '&:hover': {
              borderColor: 'primary.main',
              boxShadow: 8,
              transform: 'scale(1.02)',
            },
          }}
        >
          <AddCircleOutlineIcon sx={{ fontSize: 48, color: 'primary.main', mb: 3 }} />
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Create Workflow Directly in Application
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1, lineHeight: 1.6 }}>
            Use our visual builder to design approval flows, notifications, and logic steps tailored to your needs.
          </Typography>
        </Paper>
        {/* Import Workflow Card */}
        <Paper
          elevation={5}
          onClick={() =>
            setDialogState((prev) => ({
              ...prev,
              createWorkflow: false,
              excelOperations: true,
            }))
          }
          sx={{
            flex: 1,
            p: 7,
            cursor: 'pointer',
            borderRadius: 3,
            border: '2px solid transparent',
            background: (theme) =>
              `linear-gradient(145deg, ${theme.palette.background.paper}, ${theme.palette.grey[100]})`,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            transition: 'all 0.3s ease',
            '&:hover': {
              borderColor: 'primary.main',
              boxShadow: 8,
              transform: 'scale(1.02)',
            },
          }}
        >
          <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h6" fontWeight="bold" gutterBottom>
            Create Workflow with Upload
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1, lineHeight: 1.6 }}>
            Upload a configuration file to instantly build a workflow without manual setup.
          </Typography>
        </Paper>
      </Box>
      <Divider sx={{ my: 4, borderStyle: 'dashed' }} />
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }} />
    </DialogContent>
  </Dialog>
);

export default ScenarioDialogPopup;
