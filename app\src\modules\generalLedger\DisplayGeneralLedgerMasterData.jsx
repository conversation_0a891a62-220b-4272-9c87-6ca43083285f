import React, { useEffect, useState } from "react";
import { Typo<PERSON>, IconButton, Grid, Box, Stack, Tabs, Tab, Accordion, AccordionSummary, AccordionDetails, Paper } from "@mui/material";
import ArrowCircleLeftOutlinedIcon from "@mui/icons-material/ArrowCircleLeftOutlined";
import { useLocation, useNavigate } from "react-router-dom";
import { iconButton_SpacingSmall, outermostContainer_Information } from "@components/Common/commonStyles";
import { doAjax } from "@components/Common/fetchService";
import { destination_GeneralLedger_Mass, destination_MaterialMgmt } from "../../destinationVariables";
import { makeStyles } from "@mui/styles";
import InventoryIcon from '@mui/icons-material/Inventory';
import BusinessIcon from '@mui/icons-material/Business';
import CategoryIcon from '@mui/icons-material/Category';
import DescriptionIcon from '@mui/icons-material/Description';
import { useDispatch, useSelector } from "react-redux";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { pushMaterialDisplayData, setAdditionalData, setUOmData } from "../../app/payloadslice";
import GenericTabsForChange from "@components/MasterDataCockpit/GenericTabsForChange";
import { ERROR_MESSAGES, LOCAL_STORAGE_KEYS, MATERIAL_VIEWS, MODULE_MAP } from "@constant/enum";
import { colors } from "@constant/colors";
import ReusableBackDrop from "@components/Common/ReusableBackDrop";
import useMaterialFieldConfig from "@hooks/useMaterialFieldConfig";
import GenericViewGeneral from "@components/MasterDataCockpit/GenericViewGeneral";
import { END_POINTS } from "@constant/apiEndPoints";
import TaxDataSAP from "@components/MasterDataCockpit/TaxDataSAP";
import { convertSAPDateForCalendar, setLocalStorage } from "@helper/helper";
import useLogger from "@hooks/useLogger";
import useLang from "@hooks/useLang";
import AdditionalData from "../material/AdditionalData/AdditionalData";
import useGeneralLedgerFieldConfig from "@hooks/useGeneralLedgerFieldConfig";
import GenericTabsGlobal from "@components/MasterDataCockpit/GenericTabsGlobal";
import { v4 as uuidv4 } from "uuid";
import { setGLPayload } from "@app/generalLedgerTabSlice";
import { setDependentDropdown } from "./slice/generalLedgerDropDownSlice";



const RenderRow = ({ label, value, labelWidth = "25%", centerWidth = "5%", icon }) => (
  <Stack flexDirection="row" alignItems="center">
    {icon && <div style={{ marginRight: "10px" }}>{icon}</div>}
    <Typography variant="body2" color={colors.secondary.grey} style={{ width: labelWidth }}>
      {label}
    </Typography>
    <Typography variant="body2" fontWeight="bold" sx={{ width: centerWidth, textAlign: "center" }}>
      :
    </Typography>
    <Typography variant="body2" fontWeight="bold" justifyContent="flex-start">
      {value || ""}
    </Typography>
  </Stack>
);

const DisplayGeneralLedgerMasterData = () => {
  const { fetchMaterialFieldConfig } = useMaterialFieldConfig();
  const useStyles = makeStyles(() => ({
    customTabs: {
      "& .MuiTabs-scroller": {
        overflowX: "auto !important",
        overflowY: "hidden !important",
      },
    },
  }));
  const payloadData = useSelector((state) => state.payload);
  const generalLedgerTabs = useSelector((state) => {
      const tabs = state.generalLedger.generalLedgerTabs || [];
      return tabs.filter((tab) => tab.tab !== "Initial Screen");
    });

  
  const { loading, error, fetchGeneralLedgerFieldConfig } =useGeneralLedgerFieldConfig();
  const { t } = useLang();
  const navigate = useNavigate();
  const classes = useStyles();
  const location = useLocation();
  const dispatch = useDispatch();
  const structureData = location.state;
  const { customError } = useLogger()

  const dropDownData = [];

  const [tabNames, setTabNames] = useState([]);
  const [tabContentData, setTabContentData] = useState({}); 
  const [blurLoading, setBlurLoading] = useState(false);
  const [loaderMessage, setLoaderMessage] = useState("");
  const [activeTab, setActiveTab] = useState(0);
  const [expandedAccordion, setExpandedAccordion] = useState(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedRowId, setSelectedRowId] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  let allDropDownData =useSelector(
      (state) => state.generalLedgerDropDownData?.dropDown || {}
    );

  useEffect(() => {
    setLocalStorage(LOCAL_STORAGE_KEYS.MODULE,MODULE_MAP.GL)
    fetchgeneralLedgerData();
    fetchGeneralLedgerFieldConfig();
  }, []);

    const getAccountType = (glID) => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "Accounttype",
              data: data.body || [],
              keyName2: glID,
            })
          )
        };
    
        const hError = (error) => {
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getGLAccountType`,
          "get",
          hSuccess,
          hError
        );
    };
    const getAccountGroup = (coa, glID) => {
    
        const hSuccess = (data) => {
    
          let accGrparr = []
          data?.body?.map((item) => {
            let hash = {}
            hash["code"] = item?.AccountGroup
            hash["desc"] = item?.Description
            accGrparr?.push(hash)
          })
          dispatch(
            setDependentDropdown({
              keyName: "AccountGroup",
              data: accGrparr || [],
              keyName2: glID,
            })
          )
        };
    
        const hError = (error) => {
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getAccountGroup?chartAccount=${coa}`,
          "get",
          hSuccess,
          hError
        );
      };
    const getLanguage = (glID) => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "Language",
              data: data.body || [],
              keyName2: glID,
            })
          )
        };
    
        const hError = (error) => {
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getLanguageKey`,
          "get",
          hSuccess,
          hError
        );
      };
    const getSortKey = (id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "Sortkey",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getSortKey`,
          "get",
          hSuccess,
          hError
        );
      };

      const getAccountCurrency = (compCode, id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "AccountCurrency",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getAccountCurrency?companyCode=${compCode}`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getFiledStatusGroup = (compCode, id) => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "FieldStsGrp",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getFieldStatusGroup?fieldStatusVariant=${compCode}`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getTaxCategory = (compCode, id = '') => {
        const hSuccess = (data) => {
    
          dispatch(
            setDependentDropdown({
              keyName: "Taxcategory",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getTaxCategory?companyCode=${compCode}`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getHouseBank = (compCode, id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "HouseBank",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getHouseBank?companyCode=${compCode}`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getAccontId = (compCode,id) => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "AccountId",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getAccountId?companyCode=${compCode}`,
          "get",
          hSuccess,
          hError
        );
      };
    
    
      const getCostElementCategory = (accType, id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "CostEleCategory",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getCostElementCategory?accountType=${accType}`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getreconAccountType = ( id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "ReconAcc",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getReconAccountForAccountType`,
          "get",
          hSuccess,
          hError
        );
      };
    
      const getPlanningLevel = ( id = '') => {
        const hSuccess = (data) => {
          dispatch(
            setDependentDropdown({
              keyName: "Planninglevel",
              data: data.body || [],
              keyName2: id,
            })
          )
        };
    
        const hError = (error) => {
          customError(error);
        };
    
        doAjax(
          `/${destination_GeneralLedger_Mass}/data/getPlanningLevel`,
          "get",
          hSuccess,
          hError
        );
      };
    
  
    const fetchgeneralLedgerData = () => {
      const glAccCOACoCode = [];
  
        
            glAccCOACoCode.push({
              glAccount: structureData?.glAccount,
              chartOfAccount: structureData?.chartOfAccount,
              companyCode: structureData?.companyCode
            });
          
       const payload = {
        glAccCOACoCode: glAccCOACoCode
      };

      const successHandler = async (data) => {
  const id        = uuidv4();
  const rawData   = data?.body || [];

  // flatten the header/body data you already have
  const flat = rawData.reduce((acc, obj) => ({
    ...acc,
    ...obj.typeNDescriptionViewDto,
    ...obj.controlDataViewDto,
    ...obj.createBankInterestViewDto,
    ...obj.keywordNTranslationViewDto,
    ...obj.informationViewDto,
    COA:         obj.COA,
    CompanyCode: obj.CompanyCode,
  }), {});

  /*********************************************************
   * 1️⃣  Build an array of promises for every drop‑down call
   *********************************************************/
  const promises = [];

  if (flat.COA) {
    promises.push(
      getAccountType(id),                                  // -> {accountType: 'P'}
      getAccountGroup(flat.COA, id),                       // -> {accountGroup: 'S'}
      getLanguage(id),                                     // -> {language: 'EN'}
      getSortKey(id)                                       // -> {sortKey: '001'}
    );
  }

  if (flat.CompanyCode) {
    promises.push(
      getAccountCurrency(flat.CompanyCode, id),            // -> {currency: 'USD'}
      getTaxCategory(flat.CompanyCode, id),                // -> {taxCategory: '01'}
      getHouseBank(flat.CompanyCode, id),                  // -> {houseBank: 'HB01'}
      getAccontId(flat.CompanyCode, id),                   // -> {accountId: '...'}
      getreconAccountType(flat.CompanyCode, id),
      getPlanningLevel(flat.CompanyCode, id),
      getFiledStatusGroup(flat.CompanyCode, id)
    );
  }

  if (flat.Accounttype) {
    promises.push(
      getCostElementCategory(flat.Accounttype, id)         // -> {costElementCategory: '...'}
    );
  }

  /*********************************************************
   * 2️⃣  Wait until EVERY promise resolves
   *********************************************************/
  const dropDownObjects   = await Promise.all(promises);   // Array of objects
  const dropDownCombined  = Object.assign({}, ...dropDownObjects);

  /*********************************************************
   * 3️⃣  Assemble the final payload
   *********************************************************/
  const rowsBodyData = {
    [id]: {
      ...flat,               // base data from first API
      ...dropDownCombined,   // every look‑up result
    },
  };

  const payload = {
    requestHeaderData: {},   // you can still build these…
    rowsHeaderData:   {},
    rowsBodyData,
  };

  /*********************************************************
   * 4️⃣  NOW push everything to Redux
   *********************************************************/
  dispatch(setGLPayload(payload));
  setTimeout(() => {
  setIsLoading(true);
}, 1000);
  
  setSelectedRowId(id);
};
  
      const errorHandler = (err) => {
      };
  
      doAjax(
        `/${destination_GeneralLedger_Mass}/data/getGeneralLedgersData`,
        "post",
        successHandler,
        errorHandler,
        payload
      );
    };

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };
  return (
    <div style={{ backgroundColor: "#FAFCFF" }}>
      <Grid container sx={outermostContainer_Information}>
        <Grid item md={12} sx={{ padding: "16px", display: "flex" }}>
          <Grid md={9} sx={{ display: "flex" }}>
            <IconButton color="primary" sx={iconButton_SpacingSmall} onClick={() => navigate(-1)}>
              <ArrowCircleLeftOutlinedIcon sx={{ fontSize: "25px", color: "#000000" }} />
            </IconButton>
            <Grid item md={12}>
              <Typography variant="h3">
                <strong>{t("Display General Ledger")}</strong>
              </Typography>
              <Typography variant="body2" color="#777">
                {t("This view displays the details of the General Ledgers")}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Grid
        container
        display="flex"
        flexDirection="row"
        flexWrap="nowrap"
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
          paddingLeft: "29px",
          backgroundColor: colors.basic.lighterGrey,
          borderRadius: "10px",
          boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
        }}
      >
        {/* Left Section */}
        <Stack
          width="48%"
          spacing={1}
          sx={{
            padding: "10px 15px",
            borderRight: "1px solid #eaedf0"
          }}
        >
          <Grid item>
            <RenderRow
              label={t("GeneralLedger")}
              value={structureData?.glAccount || ""}
              labelWidth="35%"
              icon={<InventoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
          <Grid item>
            <RenderRow
              label={t("Chat Of Account")}
              value={structureData?.chartOfAccount || ""}
              labelWidth="35%"
              icon={<BusinessIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
        </Stack>

        <Stack
          width="48%"
          spacing={1}
          marginRight={"-10%"}
          sx={{
            padding: "10px 15px"
          }}
        >
          <Grid item>
            <RenderRow
              label={t("Company Code")}
              value={structureData?.companyCode || ""}
              labelWidth="35%"
              icon={<CategoryIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
          <Grid item>
            <RenderRow
              label={t("General Ledger Description")}
              value={structureData?.glAcctLongText || ""}
              labelWidth="35%"
              icon={<DescriptionIcon sx={{ color: colors.blue.indigo, fontSize: "20px" }} />}
            />
          </Grid>
        </Stack>
      </Grid>
      <Grid>
        {generalLedgerTabs ? (
          <Box sx={{ mt: 3 }}>
            <Tabs
              value={selectedTab}
              onChange={handleTabChange}
              indicatorColor="primary"
              textColor="primary"
              aria-label="Request tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
            >
              {generalLedgerTabs.map((tab, index) => (
                <Tab key={index} label={tab.tab} />
              ))}
            </Tabs>
            <Paper elevation={2} sx={{ p: 3, borderRadius: 4 }}>
              {generalLedgerTabs[selectedTab] && allDropDownData?.Accounttype ? (
                <GenericTabsGlobal
                  disabled={false}
                  basicDataTabDetails={generalLedgerTabs[selectedTab].data}
                  dropDownData={''}
                  activeViewTab={generalLedgerTabs[selectedTab].tab}
                  uniqueId={selectedRowId}
                  selectedRow={selectedRow || {}}
                  module={"GeneralLedger"}
                />
              ):''}
            </Paper>
          </Box>
        ) : (
          <Box
            sx={{
              marginTop: "30px",
              border: `1px solid ${colors.secondary.grey}`,
              padding: "16px",
              background: `${colors.primary.white}`,
              textAlign: "center",
            }}
          >
            <span>{ERROR_MESSAGES.NO_DATA_AVAILABLE}</span>
          </Box>
        )}
      </Grid>
      <ReusableBackDrop blurLoading={blurLoading} loaderMessage={loaderMessage} />
    </div>
  );
};

export default DisplayGeneralLedgerMasterData;
