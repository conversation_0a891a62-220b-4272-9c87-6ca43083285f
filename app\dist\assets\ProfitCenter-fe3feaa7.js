import{r as d,a as Fo,b as <PERSON>,at as ot,n as he,s as Xo,g as <PERSON>o,t as Dt,o as rs,bx as Zo,aC as jo,bL as ei,E as ti,bz as li,bA as ni,x as te,z as Je,C as T,c as S,j as l,ai as Xe,af as as,al as Qe,N as si,S as os,O as x,Q as ri,bC as ai,d as P,U as oi,V as ii,W as ci,i as di,X as $e,Y as F,$,b3 as J,B as Z,bD as ie,F as D,aa as H,bE as w,bF as ge,bG as k,a9 as U,T as ce,a4 as is,a8 as Rt,h as ui,a5 as hi,a6 as Dl,a7 as gi,a1 as fi,a2 as Ci,a3 as Ei,ab as cs,ac as Si,ad as xi,ag as il,ah as Ai,aq as Rl,ar as kl,as as Gl,aj as Ze,bM as ds,bN as it,am as je,an as re,bO as yi,bP as Bl,bQ as ct,bR as $l,bS as Ti,bT as pi,k as mi,A as Li,bU as m,bJ as q,bI as dt,w as Pi,aD as _i,bV as us,aE as hs,bW as Mi,a_ as Ni,R as Ii,aG as bi,bX as gs,bY as fs,bZ as Oi,b_ as Di,b$ as Ri,c0 as ki,c1 as Gi,aK as Cs,J as Es,c2 as Bi,bK as $i,aO as zi}from"./index-226a1e75.js";import{d as vi}from"./History-09ae589c.js";import{A as Hi}from"./AttachmentUploadDialog-5b2112e0.js";import{R as wi}from"./ReusablePresetFilter-da63464b.js";import"./CloudUpload-17ed0189.js";import"./Delete-3f2fc9ef.js";import"./utilityImages-067c3dc2.js";const hc=()=>{var Jn,Xn,Qn,Zn,jn,es,ts,ls,ns,ss;d.useState(!1);const[Ui,Ss]=d.useState(!1),[ae,v]=d.useState(!1),[zl,xs]=d.useState(""),[vl,As]=d.useState(""),[Hl,ys]=d.useState(""),[wl,Ts]=d.useState(""),[Ul,ps]=d.useState(""),[Kl,ms]=d.useState(""),[Yl,Ls]=d.useState(""),[fe,ut]=d.useState([]),[Ce,ht]=d.useState([]),[Ee,gt]=d.useState([]),[Se,ft]=d.useState([]),[Le,Ct]=d.useState([]),[xe,Et]=d.useState([]),[Ae,St]=d.useState([]),[ye,xt]=d.useState([]),[Te,At]=d.useState([]),[Pe,yt]=d.useState([]),[Ps,_s]=d.useState(""),[cl,kt]=d.useState([]),[dl,Gt]=d.useState([]),[ul,Bt]=d.useState([]),[hl,$t]=d.useState([]),[Vl,zt]=d.useState([]),[gl,vt]=d.useState([]),[fl,Ht]=d.useState([]),[Cl,wt]=d.useState([]),[El,Ut]=d.useState([]),[Wl,Kt]=d.useState([]),[ql,Yt]=d.useState({}),[Ms,Ns]=d.useState([]),[Vt,Fl]=d.useState([]),{t:X}=Fo(),[Sl,Is]=d.useState(),Wt=Jo(),bs=ot(mi,{target:"e1wjtcts6"})(({theme:e})=>({marginTop:"0px !important",border:`1px solid ${e.palette.primary.main}`,borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)","&:not(:last-child)":{borderBottom:0},"&:before":{display:"none"}}),""),Os=ot(Li,{target:"e1wjtcts5"})(({theme:e})=>({minHeight:"2rem !important",margin:"0px !important",backgroundColor:e.palette.primary.light,borderRadius:"8px 8px 0 0",transition:"all 0.2s ease-in-out","&:hover":{backgroundColor:`${e.palette.primary.light}20`}}),""),j=ot(P,{target:"e1wjtcts4"})({fontSize:"0.75rem",color:Wt.palette.primary.dark,marginBottom:"0.25rem",fontWeight:500},""),Jl=ot(re,{target:"e1wjtcts3"})({name:"1x9mjbh",styles:"border-radius:4px;padding:4px 12px;text-transform:none;font-size:0.875rem"}),Ds=ot(x,{target:"e1wjtcts2"})({name:"1ikq1ll",styles:"display:flex;justify-content:flex-end;padding-right:0.75rem;padding-bottom:0.75rem;padding-top:0rem;gap:0.5rem"}),Rs=d.useMemo(()=>fe.length>0?fe:cl.length>0?cl:[],[fe,cl]),ks=d.useMemo(()=>Ce.length>0?Ce:dl.length>0?dl:[],[Ce,dl]),Gs=d.useMemo(()=>Ee.length>0?Ee:ul.length>0?ul:[],[Ee,ul]),Bs=d.useMemo(()=>Ae.length>0?Ae:fl.length>0?fl:[],[Ae,fl]),$s=d.useMemo(()=>Se.length>0?Se:hl.length>0?hl:[],[Se,hl]),zs=d.useMemo(()=>ye.length>0?ye:Cl.length>0?Cl:[],[ye,Cl]),vs=d.useMemo(()=>Te.length>0?Te:El.length>0?El:[],[Te,El]),Hs=d.useMemo(()=>xe.length>0?xe:gl.length>0?gl:[],[xe,gl]),[ws,Xl]=d.useState(!1),[Us,Ks]=d.useState(""),[Ys,Vs]=d.useState(),[Ws,b]=d.useState("");he(e=>e.appSettings.Format);const[qs,Ql]=d.useState(!1),[et,qt]=d.useState("systemGenerated"),A=Xo(),pe=Qo(),Fs=48,Js=8,Zl={PaperProps:{style:{maxHeight:Fs*4.5+Js,width:250}}};d.useState(!1);const[xl,jl]=d.useState([]),[Xs,Ft]=d.useState(!1),[Ki,de]=d.useState(!1),[le,ze]=d.useState(null),[Qs,Jt]=d.useState(!1),[Zs,js]=d.useState(null),[oe,en]=d.useState([]);d.useState([...oe]),Dt.useState(""),d.useState(!1),d.useState(""),d.useState(""),d.useState(!0);const[Yi,De]=d.useState(!1),[Vi,Re]=d.useState(!0);d.useState([]),d.useState([]),d.useState([]),d.useState([]),d.useState(!0),d.useState([]),d.useState([]),d.useState(!1);const[ke,Tt]=d.useState({}),[_e,tn]=d.useState([]),[Wi,er]=d.useState([]),[qi,tr]=d.useState({});d.useState([]),d.useState([]),d.useState(!1),d.useState([]);const[ne,lr]=d.useState([]),[ln,nr]=d.useState([]),{getDtCall:sr,dtData:pt}=rs(),{getDtCall:rr,dtData:mt}=rs(),[Al,ar]=d.useState([]);d.useState([]);const[or,nn]=d.useState(!1),[ir,sn]=d.useState(!1);d.useState(!0),d.useState("sm");const[cr,Me]=d.useState(!1),[Fi,tt]=d.useState(!1),[ve,yl]=d.useState(""),[He,rn]=d.useState(""),[Tl,pl]=d.useState(0),[ml,dr]=d.useState(10),[Ji,ur]=d.useState(0);d.useState([]),d.useState(null),d.useState(null);const[Xi,an]=d.useState(0),[hr,on]=d.useState(0);d.useState(!1);const[gr,Lt]=d.useState(!1),[fr,Ne]=d.useState(!1);d.useState(!1),d.useState(!1),d.useState(!1),d.useState(!1),d.useState(""),d.useState("");const[Cr,cn]=d.useState(!1),[Er,M]=d.useState(""),[Sr,O]=d.useState(),[dn,xr]=d.useState(!1),[un,Ar]=d.useState(!1);d.useState(null);const Xt=Dt.useRef(null),[yr,hn]=d.useState(!1),[Tr,pr]=d.useState(0),[mr,gn]=d.useState(!1),Pt=Dt.useRef(null),Qt=Dt.useRef(null),[G,_t]=d.useState(""),[we,Lr]=d.useState(""),[Ie,Ue]=d.useState(""),[Pr,_r]=d.useState(0),[Mr,fn]=d.useState(!1),[Ke,Nr]=d.useState(""),[Ir,br]=d.useState(0),[se,Cn]=d.useState([]),[En,lt]=d.useState(!1),[Or,Zt]=d.useState(!1),[Sn,Mt]=d.useState(!1),[xn,jt]=d.useState(!1),[Ge,An]=d.useState([]);d.useState(!0);const[W,Qi]=d.useState([]),[be,Ye]=d.useState(!1),[nt,Ve]=d.useState(!1),[Dr,Zi]=d.useState([]),[el,Rr]=d.useState("");d.useState(!1),d.useState("");const Ll=he(e=>e.applicationConfig),[kr,_]=d.useState(!1),[ji,yn]=d.useState(!1),[We,Gr]=d.useState([]),[Be,Br]=d.useState([]),[st,ec]=d.useState([]),[B,$r]=d.useState([]),[z,me]=d.useState([]),[tl,Pl]=d.useState("ALL OTHER CHANGES"),[K,Nt]=d.useState([]),[Y,ll]=d.useState([]),[qe,zr]=d.useState([]),[rt,tc]=d.useState([]),[vr,Tn]=d.useState(!1),[Hr,pn]=d.useState(!1),[p,wr]=d.useState("ALL OTHER CHANGES");d.useState(""),d.useState("");const[Ur,N]=d.useState(""),[lc,mn]=d.useState(""),[nl,Kr]=d.useState("yes"),[Yr,Ln]=d.useState(!1),sl=ot(({className:e,...t})=>l(ce,{...t,classes:{popper:e}}),{target:"e1wjtcts0"})({[`& .${Zo.tooltip}`]:{maxWidth:"none"}},""),_l=()=>{Ql(!1),qt("systemGenerated")},Ml=()=>{Ln(!1),qt("systemGenerated")},Vr=e=>{var t;qt((t=e==null?void 0:e.target)==null?void 0:t.value)},Wr=e=>{var t;qt((t=e==null?void 0:e.target)==null?void 0:t.value)},qr=()=>{et==="systemGenerated"&&(Ho(),_l()),et==="mailGenerated"&&(vo(),_l())},Fr=()=>{et==="systemGenerated"&&(Mn(),Ml()),et==="mailGenerated"&&(ea(),Ml())},Jr=e=>{Kr(e.target.value)},ue=he(e=>e.profitCenter.singlePCPayload),Pn=[{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1,width:150},{field:"reqId",headerName:"Req Id",editable:!1,flex:1,width:200},{field:"requestedBy",headerName:"Requested By",editable:!1,flex:1,width:250}];d.useState([{name:"Name",id:1},{name:"Long Description",id:2},{name:"Person Respons.",id:3},{name:"Segment",id:4},{name:"Lock indicator",id:5},{name:"Name 1",id:6},{name:"Name 2",id:7},{name:"Name 3",id:8},{name:"Name 4",id:9},{name:"Street",id:10},{name:"City",id:11},{name:"Country/Reg.",id:12},{name:"Postal Code",id:13},{name:"Region",id:14},{name:"Blocking Status",id:15}]);const[nc,Fe]=d.useState([]),Xr=["Create Multiple","Upload Template ","Download Template "],Qr=["Change Multiple","Upload Template ","Download Template "],Zr=["Create Single","With Copy","Without Copy"];he(e=>{var t,a;return(a=(t=e==null?void 0:e.userManagement)==null?void 0:t.entitiesAndActivities)==null?void 0:a["Display Material"]}),he(e=>{var t;return(t=e==null?void 0:e.userManagement)==null?void 0:t.userData});const[_n,rl]=d.useState(!1),n=he(e=>e.commonFilter.ProfitCenter);he(e=>e.commonSearchBar.ProfitCenter);const jr=he(e=>e.profitCenter.handleMassMode);he(e=>{var t;return(t=e==null?void 0:e.profitCenter)==null?void 0:t.buttonsIDM});const Mn=()=>{var E;let e=[];if(p==="ALL OTHER CHANGES"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");z.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION==i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}else if(p==="BLOCK"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="BLOCK");K.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION===i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");Y.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION===i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}let t=(E=e==null?void 0:e.map(g=>g==null?void 0:g.name))==null?void 0:E.join(",");if(nt===!0&&Ge.length===0)if(t!==""){qn(),Ve(!1),Ne(!1),Fe([]),me([]);return}else{M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}let a=[];if(nt===!0&&Ge.length>0){if(t===""){M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}a=se.map(g=>({coArea:g.controllingArea,profitCenter:g.profitCenter,changedFieldsToCheck:t}))}else{if(t===""){M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}a=[{coArea:W==null?void 0:W.controllingArea,profitCenter:W==null?void 0:W.profitCenter,changedFieldsToCheck:t}]}const c=g=>{if(g.some(r=>r.statusCode!==200)){const r=g.filter(s=>s.statusCode===400);let o=[];r==null||r.map((s,f)=>{var L,ee,R,Oe,Q,It,bt,Ot;let h={};const y=(ee=(L=s==null?void 0:s.message)==null?void 0:L.split("Profit Center: ")[1])==null?void 0:ee.split(",")[0];h.id=f,h.profitCenter=y,h.reqId=((Oe=(R=s==null?void 0:s.body)==null?void 0:R.EditIds)==null?void 0:Oe[0])||((It=(Q=s==null?void 0:s.body)==null?void 0:Q.MassEditIds)==null?void 0:It[0]),h.requestedBy=(Ot=(bt=s==null?void 0:s.body)==null?void 0:bt.RequestCreatedBy)==null?void 0:Ot[0],o.push(h)}),jl(o),Ft(!0)}else if(nt===!0)Ge.length>0?($o(),Ve(!1),Lt(!1),Ne(!1),Fe([]),me([])):Ge.length===0&&(qn(),Ve(!1),Lt(!1),Ne(!1),Fe([]),me([]));else{if(A(gs(z)),A(fs(tl)),p==="ALL OTHER CHANGES"){let r=[];const o=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");z.forEach(f=>{o==null||o.map((h,y)=>{if(h.MDG_SELECT_OPTION==f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,r.push(L)}})});let s=Object==null?void 0:Object.values(r==null?void 0:r.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}else if(p==="BLOCK"){const r=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="BLOCK");let o=[];K.forEach(f=>{r==null||r.map((h,y)=>{if(h.MDG_SELECT_OPTION===f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,o.push(L)}})});let s=Object==null?void 0:Object.values(o==null?void 0:o.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const r=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");let o=[];Y.forEach(f=>{r==null||r.map((h,y)=>{if(h.MDG_SELECT_OPTION===f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,o.push(L)}})});let s=Object==null?void 0:Object.values(o==null?void 0:o.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}tl==="TEMPORARY BLOCK/UNBLOCK"?pe("/masterDataCockpit/profitCenterNew/changeETPCTemporaryBlock",{state:W}):pe("/masterDataCockpit/profitCenter/changeETPC",{state:W})}},C=g=>{console.log("Failure")};T(`/${m}/alter/checkDuplicatePCRequest`,"post",c,C,a)},ea=()=>{var E;let e=[];if(p==="ALL OTHER CHANGES"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");z.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION==i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}else if(p==="BLOCK"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="BLOCK");K.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION===i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const g=B.filter(i=>i.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");Y.forEach(i=>{g==null||g.map((r,o)=>{if(r.MDG_SELECT_OPTION===i.name){let s={};s.id=o,s.name=r==null?void 0:r.MDG_FIELD_NAME,e.push(s)}})})}let t=(E=e==null?void 0:e.map(g=>g==null?void 0:g.name))==null?void 0:E.join(",");if(nt===!0&&Ge.length===0)if(t!==""){Fn(),Ve(!1),Ne(!1),Fe([]),me([]);return}else{M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}let a=[];if(nt===!0&&Ge.length>0){if(t===""){M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}a=se.map(g=>({coArea:g.controllingArea,profitCenter:g.profitCenter,changedFieldsToCheck:t}))}else{if(t===""){M("Error"),O("Please Select Any Field To Proceed?"),b("danger"),I();return}a=[{coArea:W==null?void 0:W.controllingArea,profitCenter:W==null?void 0:W.profitCenter,changedFieldsToCheck:t}]}const c=g=>{if(g.some(r=>r.statusCode!==200)){const r=g.filter(s=>s.statusCode===400);let o=[];r==null||r.map((s,f)=>{var L,ee,R,Oe,Q,It,bt,Ot;let h={};const y=(ee=(L=s==null?void 0:s.message)==null?void 0:L.split("Profit Center: ")[1])==null?void 0:ee.split(",")[0];h.id=f,h.profitCenter=y,h.reqId=((Oe=(R=s==null?void 0:s.body)==null?void 0:R.EditIds)==null?void 0:Oe[0])||((It=(Q=s==null?void 0:s.body)==null?void 0:Q.MassEditIds)==null?void 0:It[0]),h.requestedBy=(Ot=(bt=s==null?void 0:s.body)==null?void 0:bt.RequestCreatedBy)==null?void 0:Ot[0],o.push(h)}),jl(o),Ft(!0)}else if(nt===!0)Ge.length>0?(zo(),Ve(!1),Lt(!1),Ne(!1),Fe([]),me([])):Ge.length===0&&(Fn(),Ve(!1),Lt(!1),Ne(!1),Fe([]),me([]));else{if(A(gs(z)),A(fs(tl)),p==="ALL OTHER CHANGES"){let r=[];const o=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");z.forEach(f=>{o==null||o.map((h,y)=>{if(h.MDG_SELECT_OPTION==f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,r.push(L)}})});let s=Object==null?void 0:Object.values(r==null?void 0:r.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}else if(p==="BLOCK"){const r=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="BLOCK");let o=[];K.forEach(f=>{r==null||r.map((h,y)=>{if(h.MDG_SELECT_OPTION===f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,o.push(L)}})});let s=Object==null?void 0:Object.values(o==null?void 0:o.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const r=B.filter(f=>f.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");let o=[];Y.forEach(f=>{r==null||r.map((h,y)=>{if(h.MDG_SELECT_OPTION===f.name){let L={};L.id=y,L.name=h==null?void 0:h.MDG_FIELD_NAME,o.push(L)}})});let s=Object==null?void 0:Object.values(o==null?void 0:o.reduce((f,h)=>(f[h==null?void 0:h.name]||(f[h==null?void 0:h.name]=h),f),{}));A(setFields(s))}tl==="TEMPORARY BLOCK/UNBLOCK"?pe("/masterDataCockpit/profitCenter/changeETPCTemporaryBlock",{state:W}):pe("/masterDataCockpit/profitCenter/changeETPC",{state:W})}},C=g=>{console.log("Failure")};T(`/${m}/alter/checkDuplicatePCRequest`,"post",c,C,a)},Nn=e=>{const t=c=>{var C;A(q({keyName:"CompCodeBasedOnControllingArea",data:(C=c.body)==null?void 0:C.map((E,g)=>({id:g,companyCodes:E.code,companyName:E.desc,assigned:"X"}))}))},a=c=>{console.log(c)};T(`/${m}/data/getCompCodeBasedOnControllingArea?controllingArea=${e.ControllingArea}&rolePrefix=ETP`,"get",t,a)},In=e=>{A(q({keyName:"CompCodeBasedOnControllingArea",data:[{id:0,companyCodes:e==null?void 0:e.code,companyName:e==null?void 0:e.desc,assigned:"X"}]}))},bn={};Dr.forEach(e=>{bn[e.templateName]||(bn[e.templateName]=e)});const u=he(e=>{var t;return(t=e==null?void 0:e.AllDropDown)==null?void 0:t.dropDown}),at=he(e=>e.AllDropDown.dropDown),V={profitCenter:{newProfitCenter:He},companyCode:{newCompanyCode:G},companyCodeCopy:{newCompanyCodeCopy:we},profitCenterName:{newProfitCenterName:Ie},controllingArea:ue==null?void 0:ue.ControllingArea,controllingAreaDataCopy:{newControllingAreaCopyFrom:Ke}},ta=()=>{nn(!0)},la=()=>{sn(!0)},On=(e,t)=>{wr(t),t==="ALL OTHER CHANGES"?Pl("ALL OTHER CHANGES"):t==="BLOCK"?Pl("BLOCK"):t==="TEMPORARY BLOCK/UNBLOCK"&&Pl("TEMPORARY BLOCK/UNBLOCK")},Dn=e=>{const t=z.findIndex(c=>c.id===e.id);let a=[];t===-1?a=[...z,e]:t===0?a=z.slice(1):t===z.length-1?a=z.slice(0,-1):t>0&&(a=[...z.slice(0,t),...z.slice(t+1)]),me(a)},Rn=e=>{const t=K.findIndex(c=>c.id===e.id);let a=[];t===-1?a=[...K,e]:t===0?a=K.slice(1):t===K.length-1?a=K.slice(0,-1):t>0&&(a=[...K.slice(0,t),...K.slice(t+1)]),Nt(a)},kn=e=>{const t=Y.findIndex(c=>c.id===e.id);let a=[];t===-1?a=[...Y,e]:t===0?a=Y.slice(1):t===Y.length-1?a=Y.slice(0,-1):t>0&&(a=[...Y.slice(0,t),...Y.slice(t+1)]),ll(a)},Gn=()=>{sn(!1),Zt(!1),jt(!1),yl(""),_t(""),Ue(""),rn(""),rl(!1)},na=e=>{const t=c=>{A(q({keyName:"CompanyCode",data:c.body}))},a=c=>{console.log(c)};T(`/${m}/data/getCompanyCodeBasedOnControllingArea?controllingArea=${e==null?void 0:e.code}&rolePrefix=ETP`,"get",t,a)},sa=e=>{const t=c=>{A(q({keyName:"CompanyCode",data:c.body}))},a=c=>{console.log(c)};T(`/${m}/data/getCompCodeBasedOnControllingArea?controllingArea=${e==null?void 0:e.ControllingArea}&rolePrefix=ETP`,"get",t,a)},Bn=(e="ETCA")=>{const t=c=>{A(q({keyName:"CompanyCodeForSearch",data:c.body}))},a=c=>{console.log(c)};T(`/${m}/data/getCompCodeBasedOnControllingArea?controllingArea=${e}&rolePrefix=ETP`,"get",t,a)},ra=e=>{const t=c=>{A(q({keyName:"ProfitCenter",data:c.body}))},a=c=>{console.log(c)};T(`/${m}/data/getProfitCenterAsPerControllingArea?controllingArea=${e.code}`,"get",t,a)},aa=()=>{var t,a,c;let e={controllingArea:((t=n==null?void 0:n.controllingArea)==null?void 0:t.code)??"",profitCenter:(n==null?void 0:n.profitCenter)??"",profitCenterName:(n==null?void 0:n.profitCenterName)??"",createdBy:(n==null?void 0:n.createdBy)??"",segment:((a=n==null?void 0:n.Segment)==null?void 0:a.code)??"",profitCenterGroup:((c=n==null?void 0:n.profitCenterGroup)==null?void 0:c.code)??""};A(te({module:"ProfitCenter",filterData:e}))},oa=[{name:"ProfitCenterName",value:"TZUS"}],ia=()=>{ca()},ca=()=>{var g,i,r;if((ve==null?void 0:ve.code)===void 0||(ve==null?void 0:ve.code)===""||(we==null?void 0:we.code)===void 0||(we==null?void 0:we.code)===""||Ie===void 0||Ie===""||(Ke==null?void 0:Ke.code)===void 0||(Ke==null?void 0:Ke.code)===""||(He==null?void 0:He.code)===void 0||(He==null?void 0:He.code)===""){jt(!1),Zt(!0);return}else{if(Ie.length!==5){jt(!0),Zt(!1);return}else jt(!1);Zt(!1)}let e=V==null?void 0:V.controllingArea,t=(i=(g=V==null?void 0:V.companyCodeCopy)==null?void 0:g.newCompanyCodeCopy)==null?void 0:i.code,a=(r=V==null?void 0:V.profitCenterName)==null?void 0:r.newProfitCenterName,c=e==null?void 0:e.concat("$$","P",t,a);N("We are validating the Profit Center Number to ensure it does not exist. Thank you for your patience"),_(!0);const C=o=>{var s;_(!1),N(""),o.body.length>0?rl(!0):pe(`/masterDataCockpit/profitCenter/displayCopyProfitCenter/${(s=V==null?void 0:V.profitCenterName)==null?void 0:s.newProfitCenterName}`,{state:V})},E=o=>{console.log(o)};T(`/${m}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${c}`,"get",C,E)},da=()=>{ua()},ua=()=>{var a,c,C;let e=(c=(a=V==null?void 0:V.companyCode)==null?void 0:a.newCompanyCode)==null?void 0:c.code,t=(C=V==null?void 0:V.profitCenterName)==null?void 0:C.newProfitCenterName;if(nl==="no"){if((G==null?void 0:G.code)===void 0||(G==null?void 0:G.code)===""){Mt(!1),lt(!0),yn(!0);return}else lt(!1);pe("/masterDataCockpit/profitCenter/newSingleProfitCenter",{state:V})}else{if((G==null?void 0:G.code)===void 0||(G==null?void 0:G.code)===""||Ie===void 0||Ie===""){Mt(!1),lt(!0),yn(!0);return}else{if(Ie.length!==5){Mt(!0),lt(!1);return}else Mt(!1);lt(!1)}let E=ue==null?void 0:ue.ControllingArea.concat("$$","P",e,t);N("We are validating the Profit Center Number to ensure it does not exist. Thank you for your patience"),_(!0);const g=r=>{_(!1),N(""),r.body.length>0?rl(!0):pe("/masterDataCockpit/profitCenter/newSingleProfitCenter",{state:V})},i=r=>{console.log(r)};T(`/${m}/alter/fetchCoAreaPCDupliChk?ctrlAreaPCToCheck=${E}`,"get",g,i)}},ha=e=>{Qt.current&&Qt.current.contains(e.target)||fn(t=>!t)},$n=()=>{xr(!1),Ar(!1),nn(!1),lt(!1),Mt(!1),yl(""),_t(""),Ue(""),rl(!1)},ga=(e,t)=>{var c;{var a=t;let C={...n,controllingArea:a};A(te({module:"ProfitCenter",filterData:C})),getHierarchyArea(C),Bn((c=n==null?void 0:n.controllingArea)==null?void 0:c.code)}},fa=e=>Ee.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),Ca=e=>Se.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),Ea=e=>xe.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),Sa=e=>ye.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),xa=e=>Ce.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),Aa=e=>Ae.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),ya=e=>Te.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),Ta=e=>Pe.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),pa=e=>fe.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),ma=e=>Le.some(t=>(t==null?void 0:t.code)===(e==null?void 0:e.code)),La=(e,t)=>{var a;return(a=ke[e])==null?void 0:a.some(c=>(c==null?void 0:c.code)===(t==null?void 0:t.code))},Pa=(e,t)=>{_s(t)},_a=e=>{var t,a;((t=ke[e])==null?void 0:t.length)===((a=ne[e])==null?void 0:a.length)?(Tt(c=>({...c,[e]:[]})),Yt(c=>({...c,[e]:[]}))):Tt(c=>({...c,[e]:ne[e]??[]}))},Ma=()=>{fe.length===u.CompanyCode.length?(ut([]),kt([])):ut(u==null?void 0:u.CompanyCode)},Na=()=>{Ee.length===(u==null?void 0:u.LongTextSearchET.length)?(gt([]),Bt([])):gt(u==null?void 0:u.LongTextSearchET)},Ia=()=>{var e;Se.length===((e=u==null?void 0:u.PersonResponsibleSearchET)==null?void 0:e.length)?(ft([]),$t([])):ft(u==null?void 0:u.PersonResponsibleSearchET)},ba=()=>{var e;Le.length===((e=u==null?void 0:u.country)==null?void 0:e.length)?(Ct([]),zt([])):Ct(u==null?void 0:u.country)},Oa=()=>{var e;xe.length===((e=u==null?void 0:u.CreatedBySearchET)==null?void 0:e.length)?(Et([]),vt([])):Et(u==null?void 0:u.CreatedBySearchET)},Da=()=>{var e;ye.length===((e=u==null?void 0:u.StreetSearchET)==null?void 0:e.length)?(xt([]),wt([])):xt(u==null?void 0:u.StreetSearchET)},Ra=()=>{var e;Ce.length===((e=u==null?void 0:u.PCSearchDataET)==null?void 0:e.length)?(ht([]),Gt([])):ht(u==null?void 0:u.PCSearchDataET)},ka=()=>{var e;Ae.length===((e=u==null?void 0:u.PCNameSearchET)==null?void 0:e.length)?(St([]),Ht([])):St(u==null?void 0:u.PCNameSearchET)},Ga=()=>{Te.length===(u==null?void 0:u.CitySearchET.length)?(At([]),Ut([])):At(u==null?void 0:u.CitySearchET)},Ba=()=>{Pe.length===(u==null?void 0:u.region.length)?(yt([]),Kt([])):yt(u==null?void 0:u.region)},$a=e=>{const t=e.target.value;if(As(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{ao(t)},500);ze(a)}},za=e=>{const t=e.target.value;if(ys(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{oo(t)},500);ze(a)}},va=e=>{const t=e.target.value;if(Ts(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{co(t)},500);ze(a)}},Ha=e=>{const t=e.target.value;if(ps(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{io(t)},500);ze(a)}},wa=e=>{const t=e.target.value;if(ms(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{uo(t)},500);ze(a)}},Ua=e=>{const t=e.target.value;if(xs(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{ho(t)},500);ze(a)}},Ka=e=>{const t=e.target.value;if(Ls(t),le&&clearTimeout(le),t.length>=4){const a=setTimeout(()=>{go(t)},500);ze(a)}},Ya=()=>{la()},Va=()=>{ta()},Wa=e=>{if(e.target.value!==null){var t=e.target.value;let a={...n,blockingStatus:t};A(te({module:"ProfitCenter",filterData:a}))}};let qa={Segment:`/${m}/data/getSearchParamsSegment`};const Fa=e=>{const t=e.target.value;tn(t),er([]),t.forEach(async a=>{const c=qa[a];To(c,a)})},Ja={"Short Description":"ProfitCenterName",Segment:"Segment",Street:"street","Country/Region":"country","Created On":"createdOn","Created By":"CreatedBy"},Nl=["Blocked","Unblocked",""],Xa=()=>{const e=a=>{A(q({keyName:"ControllingArea",data:a.body}))},t=a=>{console.log(a)};T(`/${m}/data/getControllingArea`,"get",e,t)},Qa=()=>{const e=a=>{A(q({keyName:"CompanyCode",data:a.body}))},t=a=>{console.log(a)};T(`/${m}/data/getCompCode?rolePrefix=ETP`,"get",e,t)},Za=e=>{const t=c=>{A(q({keyName:"ProfitCtrGroup",data:c.body}))},a=c=>{console.log(c)};T(`/${m}/data/getProfitCtrGroup?controllingArea=${e.code}`,"get",t,a)},ja=()=>{const e=a=>{A(q({keyName:"Segment",data:a.body}))},t=a=>{console.log(a)};T(`/${m}/data/getSearchParamsSegment`,"get",e,t)},eo=()=>{let e="Basic Data";const t=c=>{A(Oi(c.body))},a=c=>{console.log(c)};T(`/${m}/data/getViewFieldDetails?viewName=${e}`,"get",t,a)},to=()=>{let e="Comp Codes";const t=c=>{A(Di(c.body))},a=c=>{console.log(c)};T(`/${m}/data/getViewFieldDetails?viewName=${e}`,"get",t,a)},lo=()=>{let e="Address";const t=c=>{A(Ri(c.body))},a=c=>{console.log(c)};T(`/${m}/data/getViewFieldDetails?viewName=${e}`,"get",t,a)},no=()=>{let e="Communication";const t=c=>{A(ki(c.body))},a=c=>{console.log(c)};T(`/${m}/data/getViewFieldDetails?viewName=${e}`,"get",t,a)},so=()=>{let e="History";const t=c=>{A(Gi(c.body))},a=c=>{console.log(c)};T(`/${m}/data/getViewFieldDetails?viewName=${e}`,"get",t,a)},zn=e=>{var C,E,g;v(!0);let t={controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",top:200,skip:0};const a=i=>{A(q({keyName:"region",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsRegion`,"post",a,c,t)},ro=()=>{var c,C,E;v(!0);let e={rolePrefix:"ETP",top:200,skip:0,controllingArea:(c=n==null?void 0:n.controllingArea)!=null&&c.code?((C=n==null?void 0:n.controllingArea)==null?void 0:C.code)===""?"ETCA":(E=n==null?void 0:n.controllingArea)==null?void 0:E.code:"ETCA"};const t=g=>{A(q({keyName:"country",data:g.body})),v(!1)},a=g=>{console.log(g)};T(`/${m}/data/getSearchParamsCountryReg`,"post",t,a,e)},ao=e=>{var C,E,g;v(!0);let t={controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",description:e,top:200,skip:0};const a=i=>{A(q({keyName:"LongTextSearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsLongText`,"post",a,c,t)},oo=e=>{var C,E,g;v(!0);let t={controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",personRespons:e,top:200,skip:0};const a=i=>{A(q({keyName:"PersonResponsibleSearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsPersonRespons`,"post",a,c,t)},io=e=>{var C,E,g;v(!0);let t={pcName:e,controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",top:200,skip:0};const a=i=>{A(q({keyName:"PCNameSearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsPCName`,"post",a,c,t)},co=e=>{var C,E,g;v(!0);let t={city:e,rolePrefix:"ETP",controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",top:200,skip:0};const a=i=>{A(q({keyName:"CitySearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsCity`,"post",a,c,t)},uo=e=>{var C,E,g;v(!0);let t={street:e,controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",top:200,skip:0};const a=i=>{A(q({keyName:"StreetSearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsStreet`,"post",a,c,t)},ho=e=>{var C,E,g;v(!0);let t={profitCenter:e,controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",top:200,skip:0};const a=i=>{A(q({keyName:"PCSearchDataET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsProfitCenter`,"post",a,c,t)},go=e=>{var C,E,g;v(!0);let t={createdBy:e,rolePrefix:"ETP",top:200,skip:0,controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA"};const a=i=>{A(q({keyName:"CreatedBySearchET",data:i.body})),v(!1)},c=i=>{console.log(i)};T(`/${m}/data/getSearchParamsCreatedBy`,"post",a,c,t)},fo=()=>{v(!0);const e=a=>{A(q({keyName:"ControllingAreaForSearchET",data:a.body})),v(!1)},t=a=>{console.log(a)};T(`/${m}/data/getSearchParamsConArea`,"get",e,t)},vn=()=>{Ss(!0)},Co=e=>{_(!0);const t=new FormData;if([...e].forEach(c=>t.append("files",c)),t.append("dtName","MDG_PC_FIELD_CONFIG"),t.append("version","v1"),t.append("IsSunoco","false"),jr==="Change"){var a=`/${m}/massAction/getAllProfitCenterFromExcelForMassChange`;T(a,"postformdata",E=>{if(de(!1),E.statusCode===200){Me(!1),M("Create");const g=l(P,{component:"div",children:S("ul",{children:[l("li",{children:"Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number."}),l("li",{children:"Then you can visit the Request Bench Tab and search for that request ID and do further actions on it."}),l("li",{children:"Note - All request IDs generated in the background would initially have the status Draft."})]})});_(!1),N(""),I(),M("Header - Information"),O(g),b("success"),Re(!1),tt(!0),De(!0),vn(),de(!1)}else E.statusCode===429?(_(!1),N(""),Me(!1),M("Error"),tt(!1),O((E==null?void 0:E.message)||"Please try again."),b("danger"),Re(!1),De(!0),I(),de(!1)):(Me(!1),M("Error"),tt(!1),O("Upload failed. Incorrect template tab name, please recheck upload file"),b("danger"),Re(!1),De(!0),_(!1),N(""),I(),de(!1));Ol()},E=>{M("Error"),O(`${E==null?void 0:E.message}`),b("danger"),Re(!1),De(!0),mn("OK"),_(!1),N(""),I()},t)}else{var a=`/${m}/massAction/getAllProfitCenterFromExcel`;T(a,"postformdata",g=>{if(g.statusCode===200){Me(!1),M("Create");const i=l(P,{component:"div",children:S("ul",{children:[l("li",{children:"Mass Upload Process has Started in the background. As soon as the request ID is generated, you will receive a notification and mail for it containing the new request ID number."}),l("li",{children:"Then you can visit the Request Bench Tab and search for that request ID and do further actions on it."}),l("li",{children:"Note - All request IDs generated in the background would initially have the status Draft."})]})});_(!1),N(""),I(),M("Header - Information"),O(i),b("success"),Re(!1),tt(!0),De(!0),vn(),de(!1)}else g.statusCode===429?(_(!1),N(""),Me(!1),M("Error"),tt(!1),O((g==null?void 0:g.message)||"Please try again."),b("danger"),Re(!1),De(!0),I(),de(!1)):(_(!1),N(""),Me(!1),M("Error"),tt(!1),O("Upload failed. Incorrect template tab name, please recheck upload file"),b("danger"),Re(!1),De(!0),I(),de(!1));Ol()},g=>{M("Error"),O(`${g==null?void 0:g.message}`),b("danger"),Re(!1),De(!0),mn("OK"),_(!1),N(""),I()},t)}};console.log("rndatarows",ln);const Eo=e=>{An(e);let t=ol==null?void 0:ol.map(C=>C.field);const a=oe.filter(C=>e.includes(C.id));let c=[];a==null||a.map(C=>{let E={};t.forEach(g=>{C[g]!==null&&(E[g]=C[g]||"")}),E.controllingArea=C.controllingArea,c.push(E),Cn(c),Fl(c)})},So=()=>{let e={decisionTableId:null,decisionTableName:"MDG_ET_CHNG_FIELD_SELECTION_DT",version:"v4",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"ET PROFIT CENTER"}],systemFilters:null,systemOrders:null,filterString:null};de(!0);const t=c=>{var C,E;if(de(!1),c.statusCode===200){let g=(E=(C=c==null?void 0:c.data)==null?void 0:C.result[0])==null?void 0:E.MDG_ET_CHNG_FIELD_SELECTION_ACTION_TYPE;$r(g);let i=[],r=[],o=[];g==null||g.map((R,Oe)=>{if(R.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES"){let Q={};Q.id=Oe,Q.name=R.MDG_SELECT_OPTION,i.push(Q)}else if(R.MDG_FIELD_SELECTION_LVL==="BLOCK"){let Q={};Q.id=Oe,Q.name=R.MDG_SELECT_OPTION,r.push(Q)}else if(R.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK"){let Q={};Q.id=Oe,Q.name=R.MDG_SELECT_OPTION,o.push(Q)}});const s=new Set,f=i.filter(R=>s.has(R.name)?!1:(s.add(R.name),!0)),h=new Set,y=r.filter(R=>h.has(R.name)?!1:(h.add(R.name),!0)),L=new Set,ee=o.filter(R=>L.has(R.name)?!1:(L.add(R.name),!0));Nt(y),Gr(f),zr(ee),ll(ee),Br(y)}Ol()},a=c=>{console.log(c)};Ll.environment==="localhost"?T(`/${dt}/rest/v1/invoke-rules`,"post",t,a,e):T(`/${dt}/v1/invoke-rules`,"post",t,a,e)},xo=e=>{e==null||e.map(t=>{A($l({keyName:t==null?void 0:t.MDG_PC_UI_FIELD_NAME.replaceAll("(","").replaceAll(")","").replaceAll("/","").replaceAll("-","").replaceAll(".","").replaceAll("%","").split(" ").join(""),data:t==null?void 0:t.MDG_PC_DEFAULT_VALUE}))})};function Ao(e,t){return e.reduce((a,c)=>((a[c[t]]=a[c[t]]||[]).push(c),a),{})}const Hn=()=>{Xl(!0)},wn=()=>{Xl(!1)},yo=()=>{let e={decisionTableId:null,decisionTableName:"MDG_PC_FIELD_CONFIG",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_PC_SCENARIO":"Create"}],systemFilters:null,systemOrders:null,filterString:null};de(!0);const t=c=>{var C,E,g;if(de(!1),c.statusCode===200){let r=((E=(C=c==null?void 0:c.data)==null?void 0:C.result[0])==null?void 0:E.MDG_PC_FIELD_DETAILS_ACTION_TYPE).sort((s,f)=>s.MDG_PC_VIEW_SEQUENCE-f.MDG_PC_VIEW_SEQUENCE);const o=Ao(r,"MDG_PC_VIEW_NAME");(g=o==null?void 0:o["Initial Screen"])==null||g.map(s=>{(s==null?void 0:s.MDG_PC_UI_FIELD_NAME)==="Profit Center"&&Rr(s==null?void 0:s.MDG_PC_MAX_LENGTH)}),xo(o==null?void 0:o["Initial Screen"])}},a=c=>{console.log(c)};Ll.environment==="localhost"?T(`/${dt}/rest/v1/invoke-rules`,"post",t,a,e):T(`/${dt}/v1/invoke-rules`,"post",t,a,e)};d.useEffect(()=>{Bn(),Xa(),fo(),zn(),ro(),yo(),Qa(),ja(),eo(),to(),lo(),so(),no(),A(jo({})),So(),A(ei()),A(ti()),A(li({})),A(ni())},[]),d.useEffect(()=>{if(n!=null&&n["Profit Center"]){const t=(n==null?void 0:n["Profit Center"].split("$^$")).map(a=>({code:a}));Gt(t)}if(n!=null&&n.profitCenterName){const t=(n==null?void 0:n.profitCenterName.split("$^$")).map(a=>({code:a}));Ht(t)}if(n!=null&&n["Company Code"]){const t=(n==null?void 0:n["Company Code"].split("$^$")).map(a=>({code:a}));kt(t)}if(n!=null&&n.createdBy){const t=(n==null?void 0:n.createdBy.split("$^$")).map(a=>({code:a}));vt(t)}if(n!=null&&n.personResponsible){const t=(n==null?void 0:n.personResponsible.split("$^$")).map(a=>({code:a}));$t(t)}if(n!=null&&n.longText){const t=(n==null?void 0:n.longText.split("$^$")).map(a=>({code:a}));Bt(t)}if(n!=null&&n.street){const t=(n==null?void 0:n.street.split("$^$")).map(a=>({code:a}));wt(t)}if(n!=null&&n.city){const t=(n==null?void 0:n.city.split("$^$")).map(a=>({code:a}));Ut(t)}if(n!=null&&n.country){const t=(n==null?void 0:n.country.split("$^$")).map(a=>({code:a}));zt(t)}if(n!=null&&n.createdOn){const e=new Date(n==null?void 0:n.createdOn[0]),t=new Date(n==null?void 0:n.createdOn[1]);Ns([e,t])}if(n!=null&&n.Segment){const t=(n==null?void 0:n.Segment.split("$^$")).map(a=>({code:a}));Yt(a=>({...a,Segment:t??[]}))}if(n!=null&&n.Region){const t=(n==null?void 0:n.Region.split("$^$")).map(a=>({code:a}));Kt(t)}},[n]),d.useEffect(()=>{Object.keys(ke).forEach(e=>{var c;const t=(c=ke[e])==null?void 0:c.map(C=>C==null?void 0:C.code).join("$^$");let a={...n,[e]:t};A(te({module:"ProfitCenter",filterData:a}))})},[ke]),d.useEffect(()=>{let e=fe.map(a=>a==null?void 0:a.code).join("$^$"),t={...n,"Company Code":e};A(te({module:"ProfitCenter",filterData:t}))},[fe]),d.useEffect(()=>{let e=Se.map(a=>a==null?void 0:a.code).join("$^$"),t={...n,personResponsible:e};A(te({module:"ProfitCenter",filterData:t}))},[Se]),d.useEffect(()=>{let e=Ee.map(a=>a==null?void 0:a.code).join("$^$"),t={...n,longText:e};A(te({module:"ProfitCenter",filterData:t}))},[Ee]),d.useEffect(()=>{var e=Te.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,city:e};A(te({module:"ProfitCenter",filterData:t}))},[Te]),d.useEffect(()=>{var e=Pe.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,Region:e};A(te({module:"ProfitCenter",filterData:t}))},[Pe]),d.useEffect(()=>{var e=Ae.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,profitCenterName:e};A(te({module:"ProfitCenter",filterData:t}))},[Ae]),d.useEffect(()=>{var e=ye.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,street:e};A(te({module:"ProfitCenter",filterData:t}))},[ye]),d.useEffect(()=>{var e=Ce.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,"Profit Center":e};A(te({module:"ProfitCenter",filterData:t}))},[Ce]),d.useEffect(()=>{var e=xe.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,createdBy:e};A(te({module:"ProfitCenter",filterData:t}))},[xe]),d.useEffect(()=>{var e=Le.map(a=>a==null?void 0:a.code).join("$^$");let t={...n,country:e};A(te({module:"ProfitCenter",filterData:t})),zn()},[Le]),d.useEffect(()=>{(W==null?void 0:W.blockingStatus)==="Blocked"?Tn(!0):Tn(!1)},[W]),d.useEffect(()=>{const e=Vt==null?void 0:Vt.some(t=>t.blockingStatus==="Blocked");pn(!!e)},[Vt]),d.useEffect(()=>{Nn(ue),sa(ue)},[ue==null?void 0:ue.ControllingArea]),d.useEffect(()=>{_t(""),Ue("")},[nl]);const To=(e,t)=>{var E,g,i,r,o,s;v(!0);let a;t==="Segment"?a={segment:"",controllingArea:(E=n==null?void 0:n.controllingArea)!=null&&E.code?((g=n==null?void 0:n.controllingArea)==null?void 0:g.code)===""?"ETCA":(i=n==null?void 0:n.controllingArea)==null?void 0:i.code:"ETCA",rolePrefix:"ETP",top:200,skip:0}:a={controllingArea:(r=n==null?void 0:n.controllingArea)!=null&&r.code?((o=n==null?void 0:n.controllingArea)==null?void 0:o.code)===""?"ETCA":(s=n==null?void 0:n.controllingArea)==null?void 0:s.code:"ETCA",rolePrefix:"ETP",top:200,skip:0},T(e,"post",f=>{const h=f.body;lr(y=>({...y,[t]:h})),v(!1)},f=>{console.log(f)},a)},Il=new Date,al=new Date;al.setDate(al.getDate()-15),d.useState([al,Il]),d.useState([al,Il]);const po=e=>{e!==null&&A(te({module:"ProfitCenter",filterData:{...n,createdOn:e}}))};d.useEffect(()=>{bl()},[]);const bl=e=>{var C,E,g,i;Jt(!0),pl(0);let t={companyCode:(n==null?void 0:n["Company Code"])??"",longText:(n==null?void 0:n.longText)??"",personResponsible:(n==null?void 0:n.personResponsible)??"",city:(n==null?void 0:n.city)??"",region:(n==null?void 0:n.Region)??"",street:(n==null?void 0:n.street)??"",country:(n==null?void 0:n.country)??"",fromDate:Je(n==null?void 0:n.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:Je(n==null?void 0:n.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",profitCenter:(n==null?void 0:n["Profit Center"])??"",profitCenterName:(n==null?void 0:n.profitCenterName)??"",createdBy:(n==null?void 0:n.createdBy)??"",segment:(n==null?void 0:n.Segment)??"",profitCenterGroup:((i=n==null?void 0:n.profitCenterGroup)==null?void 0:i.code)??"",blockingStatus:(n==null?void 0:n.blockingStatus)==="Blocked"?"X":(n==null?void 0:n.blockingStatus)==="Unblocked"?"Y":"",top:100,skip:0};const a=r=>{var h,y,L;if(r.statusCode===200){var o=[];for(let ee=0;ee<((y=(h=r==null?void 0:r.body)==null?void 0:h.list)==null?void 0:y.length);ee++){var s=r==null?void 0:r.body.list[ee],f={id:Cs(),description:s==null?void 0:s.Description,personResponsible:s==null?void 0:s.PersonResponsible,Location:s==null?void 0:s.Location,Region:s==null?void 0:s.Region,street:s==null?void 0:s.Street,country:s==null?void 0:s.Country,createdOn:s.CreatedOn!==""?`${Je(s.CreatedOn).format("DD MMM YYYY")}`:"Not Available",controllingArea:s.ControllingArea,companyCode:s.CompanyCode,profitCenter:s.ProfitCenter,profitCenterGroup:s.ProfitCenterGroup,ProfitCenterName:s.ProfitCenterName,CreatedBy:s.CreatedBy!==""?`${s.CreatedBy}`:"Not Available",Segment:s.Segment,blockingStatus:s.LockIndicator==="X"?"Blocked":"Unblocked"};o.push(f)}en(o),Jt(!1),an(o.length),on((L=r==null?void 0:r.body)==null?void 0:L.count)}else r.statusCode===400&&(Ks("Warning"),Vs("Please Select Lesser Fields as the URL is getting too long !!"),Hn())},c=r=>{console.log(r)};T(`/${m}/data/getProfitCentersBasedOnAdditionalParams`,"post",a,c,t)},mo=e=>{var C,E,g,i,r;Jt(!0);let t={companyCode:(n==null?void 0:n["Company Code"])??"",longText:(n==null?void 0:n.longText)??"",personResponsible:(n==null?void 0:n.personResponsible)??"",city:(n==null?void 0:n.city)??"",region:(n==null?void 0:n.Region)??"",street:(n==null?void 0:n.street)??"",country:(n==null?void 0:n.country)??"",fromDate:Je(n==null?void 0:n.createdOn[0]).format("YYYY-MM-DDT00:00:00")??"",toDate:Je(n==null?void 0:n.createdOn[1]).format("YYYY-MM-DDT00:00:00")??"",controllingArea:(C=n==null?void 0:n.controllingArea)!=null&&C.code?((E=n==null?void 0:n.controllingArea)==null?void 0:E.code)===""?"ETCA":(g=n==null?void 0:n.controllingArea)==null?void 0:g.code:"ETCA",rolePrefix:"ETP",profitCenter:(n==null?void 0:n["Profit Center"])??"",profitCenterName:(n==null?void 0:n.profitCenterName)??"",createdBy:((i=n==null?void 0:n.createdBy)==null?void 0:i.code)??"",segment:(n==null?void 0:n.Segment)??"",profitCenterGroup:((r=n==null?void 0:n.profitCenterGroup)==null?void 0:r.code)??"",blockingStatus:(n==null?void 0:n.blockingStatus)==="Blocked"?"X":(n==null?void 0:n.blockingStatus)==="Unblocked"?"Y":"",top:100,skip:(oe==null?void 0:oe.length)??0};const a=o=>{var y,L,ee;var s=[];for(let R=0;R<((L=(y=o==null?void 0:o.body)==null?void 0:y.list)==null?void 0:L.length);R++){var f=o==null?void 0:o.body.list[R],h={id:Cs(),description:f==null?void 0:f.Description,personResponsible:f==null?void 0:f.PersonResponsible,Location:f==null?void 0:f.Location,Region:f==null?void 0:f.Region,street:f==null?void 0:f.Street,country:f==null?void 0:f.Country,createdOn:f.CreatedOn!==""?`${Je(f.CreatedOn).format("DD MMM YYYY")}`:"Not Available",controllingArea:f.ControllingArea,companyCode:f.CompanyCode,profitCenter:f.ProfitCenter,profitCenterGroup:f.ProfitCenterGroup,ProfitCenterName:f.ProfitCenterName,CreatedBy:f.CreatedBy!==""?`${f.CreatedBy}`:"Not Available",Segment:f.Segment,blockingStatus:f.LockIndicator==="X"?"Blocked":"Unblocked"};s.push(h)}en(R=>[...R,...s]),Jt(!1),an(s.length),on((ee=o==null?void 0:o.body)==null?void 0:ee.count)},c=o=>{console.log(o)};T(`/${m}/data/getProfitCentersBasedOnAdditionalParams`,"post",a,c,t)};d.useEffect(()=>{Tl*ml>=(oe==null?void 0:oe.length)&&mo()},[Tl,ml]);const Lo=()=>{let e={decisionTableId:null,decisionTableName:"MDG_DYN_BTN_DT",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_DYN_BTN_MODULE_NAME":"ET PC","MDG_CONDITIONS.MDG_DYN_BTN_REQUEST_TYPE":"Create"}],systemFilters:null,systemOrders:null,filterString:null};const t=c=>{var C,E;c.statusCode===200&&((E=(C=c==null?void 0:c.data)==null?void 0:C.result[0])==null||E.MDG_DYN_BTN_ACTION_TYPE)},a=c=>{console.log(c)};Ll.environment==="localhost"?T(`/${dt}/rest/v1/invoke-rules`,"post",t,a,e):T(`/${dt}/v1/invoke-rules`,"post",t,a,e)};d.useEffect(()=>{Lo()},[]),d.useState([]);const Un=()=>{!be&&p==="ALL OTHER CHANGES"?me(We):be&&p==="ALL OTHER CHANGES"?me([]):!be&&p==="BLOCK"?Nt(Be):!be&&p==="TEMPORARY BLOCK/UNBLOCK"?ll(qe):be&&p==="TEMPORARY BLOCK/UNBLOCK"?ll([]):Nt(!be&&p==="BLOCK"?Be:[]),Ye(!be)},Kn=()=>{An([]),Ye(!1),Fe([]),Cn([]),Fl([]),Ne(!1)},Yn=()=>{me([]),Ye(!1),Lt(!1)},I=()=>{cn(!0)},Vn=()=>{cn(!1)},Po=(e,t)=>{pl(t)},_o=e=>{const t=e.target.value;dr(t),pl(0),ur(0)};d.useState(null),d.useState(null);const Mo=()=>{A(Pi({module:"ProfitCenter"})),ut([]),gt([]),ft([]),Ct([]),Et([]),St([]),xt([]),ht([]),At([]),yt([]),Tt({}),kt([]),Bt([]),$t([]),zt([]),vt([]),Ht([]),wt([]),Gt([]),Ut([]),Kt([]),Yt({}),tr({})};d.useState([]),d.useState([]);const[sc,No]=d.useState(!1);d.useState(null),d.useState(null),d.useState([]);const Ol=()=>{No(!1)};d.useState(null);const Io=(e,t)=>({field:e,headerName:t,editable:!1,flex:1,renderCell:a=>{const c=a.value?a.value.split(",").map(g=>g.trim()):[],C=c.length-1;if(c.length===0)return"-";const E=g=>{const[i,...r]=g.split("-");return S(D,{children:[l("strong",{children:i}),r.length?` - ${r.join("-")}`:""]})};return S(Z,{sx:{display:"flex",alignItems:"center",width:"100%",minWidth:0},children:[l(ce,{title:c[0],placement:"top",arrow:!0,children:l(P,{variant:"body2",sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",flex:1,minWidth:0},children:E(c[0])})}),C>0&&l(Z,{sx:{display:"flex",alignItems:"center",ml:1,flexShrink:0},children:l(ce,{arrow:!0,placement:"right",title:S(Z,{sx:{p:1,maxHeight:200,overflowY:"auto"},children:[S(P,{variant:"subtitle2",sx:{fontWeight:600,mb:1},children:["Additional ",t,"s (",C,")"]}),c.slice(1).map((g,i)=>l(P,{variant:"body2",sx:{mb:.5},children:E(g)},i))]}),children:S(Z,{sx:{display:"flex",alignItems:"center",cursor:"pointer"},children:[l(InfoIcon,{sx:{fontSize:"1rem",color:"primary.main","&:hover":{color:"primary.dark"}}}),S(P,{variant:"caption",sx:{ml:.5,color:"primary.main",fontSize:"11px"},children:["+",C]})]})})})]})}}),bo=(e,t)=>({field:e,headerName:t,editable:!1,flex:1,renderCell:a=>{var E;const[c,...C]=((E=a.value)==null?void 0:E.split(" - "))||[];return S("span",{style:{flex:1,wordBreak:"break-word",whiteSpace:"normal"},children:[l("strong",{children:c})," ",C.length?`- ${C.join(" - ")}`:""]})}}),Oo=()=>({field:"dataValidation",headerName:X("Audit History"),editable:!1,flex:1,renderCell:e=>l(Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%"},children:l(ce,{title:"View Audit Log",placement:"top",children:l(Dl,{onClick:a=>{var c,C;a.stopPropagation(),pe((c=$i)==null?void 0:c.AUDIT_LOG,{state:{materialNumber:e.row.profitCenter,module:(C=zi)==null?void 0:C.PC}})},size:"small",sx:{color:"primary.main",marginLeft:"20px","&:hover":{color:"primary.dark",backgroundColor:"rgba(25, 118, 210, 0.04)",transform:"scale(1.05)",marginLeft:"20px"},transition:"all 0.2s ease-in-out"},children:l(vi,{sx:{fontSize:"1.5rem"}})})})})}),Do=()=>{let e={decisionTableId:null,decisionTableName:Es.MDG_MAT_SEARCHSCREEN_COLUMN,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Profit Center","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};sr(e)},Ro=()=>{let e={decisionTableId:null,decisionTableName:Es.MDG_MAT_SEARCHSCREEN_PARAMETER,version:"v2",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":"US","MDG_CONDITIONS.MDG_MODULE":"Profit Center","MDG_CONDITIONS.MDG_MAT_SEARCHTYPE":"Master Data","MDG_CONDITIONS.MDG_MAT_VIEW_TYPE":"NA"}]};rr(e)};d.useState("");const ol=[{field:"companyCode",headerName:"Company Code",editable:!1,flex:1},{field:"profitCenter",headerName:"Profit Center",editable:!1,flex:1},{field:"longText",headerName:"Long Description",editable:!1,flex:1},{field:"personResponsible",headerName:"Person Responsible",editable:!1,flex:1},{field:"city",headerName:"City",editable:!1,flex:1},{field:"region",headerName:"Region",editable:!1,flex:1},{field:"blockingStatus",headerName:"Blocking Status",editable:!1,flex:1}],ko=_e==null?void 0:_e.map(e=>{const t=Ja[e];return t?{field:t,headerName:e,editable:!1,flex:1}:null}).filter(e=>e!==null);[...ol,...ko];const Wn={convertJsonToExcel:()=>{let e=[];Pn.forEach(t=>{t.headerName.toLowerCase()!=="action"&&!t.hide&&e.push({header:t.headerName,key:t.field})}),Bi({fileName:`Duplicate Requests -${Je(Il).format("DD-MMM-YYYY")}`,columns:e,rows:xl})},button:()=>l(re,{sx:{textTransform:"capitalize",position:"absolute",right:0,top:0},onClick:()=>Wn.convertJsonToExcel(),children:"Download"})},Go=e=>{const t=[];let a=(e==null?void 0:e.sort((c,C)=>c.MDG_MAT_SEQUENCE_NO-C.MDG_MAT_SEQUENCE_NO))||[];return a&&(a==null||a.forEach(c=>{if((c==null?void 0:c.MDG_MAT_VISIBILITY)===_i.DISPLAY&&c!=null&&c.MDG_MAT_UI_FIELD_NAME){const C=c.MDG_MAT_JSON_FIELD_NAME,E=c.MDG_MAT_UI_FIELD_NAME;C==="DataValidation"?t.push(Oo()):c.MDG_MAT_FIELD_TYPE==="Multiple"?t.push(Io(C,E)):c.MDG_MAT_FIELD_TYPE==="Single"&&t.push(bo(C,E))}})),t};d.useEffect(()=>{var e,t,a,c;if(pt){const C=Go((t=(e=pt==null?void 0:pt.result)==null?void 0:e[0])==null?void 0:t.MDG_MAT_SEARCHSCREEN_COLUMN_ACTION_TYPE);nr(C)}if(mt){const C=(c=(a=mt==null?void 0:mt.result)==null?void 0:a[0])==null?void 0:c.MDG_MAT_SEARCHSCREEN_PARAMETER_ACTION_TYPE,E=C==null?void 0:C.filter(g=>g.MDG_MAT_FILTER_TYPE==="Additional").map(g=>({title:g.MDG_MAT_UI_FIELD_NAME}));ar(C),Is(E)}},[pt,mt]),d.useEffect(()=>{Do(),Ro()},[]);let Bo=d.useRef(null);const $o=()=>{N("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),_(!0);const e=se==null?void 0:se.map(o=>({profitCenter:o.profitCenter,controllingArea:o.controllingArea})),t=["Profit Center","Controlling Area","Business Segment"];let a=[],c=new Set;const C=(o,s)=>{const f=new Set;s.forEach(h=>{o==null||o.forEach((y,L)=>{y.MDG_SELECT_OPTION===h.name&&!f.has(y.MDG_FIELD_NAME)&&(f.add(y.MDG_FIELD_NAME),a.push({id:L,name:y.MDG_FIELD_NAME}),c.add(y.MDG_SELECT_OPTION))})})};if(p==="ALL OTHER CHANGES"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");C(o,z)}else if(p==="BLOCK"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="BLOCK");C(o,K)}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");C(o,Y)}a.forEach(o=>{t.push(o.name)});const E={coAreaPCs:e,headers:t,dtName:"MDG_PC_FIELD_CONFIG",version:"v1",templateName:Array.from(c).join(", "),templateHeaders:"",IsSunoco:"false"},g={coAreaPCs:e,isSunoco:"false"},i=o=>{if((o==null?void 0:o.size)!==0||(o==null?void 0:o.type)!==""){Ye(!1);const s=URL.createObjectURL(o),f=document.createElement("a");f.href=s,f.setAttribute("download","Profit Center Mass Change.xlsx"),document.body.appendChild(f),f.click(),document.body.removeChild(f),URL.revokeObjectURL(s),I(),M("Success"),O("Profit Center Mass Change.xlsx has been downloaded successfully"),b("success"),_(!1),N("")}else I(),M("Error"),O("Please try again."),b("danger"),_(!1),N("")},r=o=>{_(!1),N(""),o.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"),_(!1))};p==="TEMPORARY BLOCK/UNBLOCK"?T(`/${m}/excel/downloadExcelWithDataForTempBlock`,"postandgetblob",i,r,g):T(`/${m}/excel/downloadExcelWithData`,"postandgetblob",i,r,E)},zo=()=>{_(!0);const e=se==null?void 0:se.map(o=>({profitCenter:o.profitCenter,controllingArea:o.controllingArea})),t=["Profit Center","Controlling Area","Business Segment"];let a=[],c=new Set;const C=(o,s)=>{const f=new Set;s.forEach(h=>{o==null||o.forEach((y,L)=>{y.MDG_SELECT_OPTION===h.name&&!f.has(y.MDG_FIELD_NAME)&&(f.add(y.MDG_FIELD_NAME),a.push({id:L,name:y.MDG_FIELD_NAME}),c.add(y.MDG_SELECT_OPTION))})})};if(p==="ALL OTHER CHANGES"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");C(o,z)}else if(p==="BLOCK"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="BLOCK");C(o,K)}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const o=B.filter(s=>s.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");C(o,Y)}a.forEach(o=>{t.push(o.name)});const E={coAreaPCs:e,headers:t,dtName:"MDG_PC_FIELD_CONFIG",version:"v1",templateName:Array.from(c).join(", "),templateHeaders:"",IsSunoco:"false"},g={coAreaPCs:e,isSunoco:"false"},i=o=>{Ye(!1),_(!1),N(""),I(),M("Success"),O("Download has been started. You will get the Excel file via email"),b("success")},r=o=>{_(!1),N(""),o.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"),_(!1))};p==="TEMPORARY BLOCK/UNBLOCK"?T(`/${m}/excel/downloadExcelWithDataForTempBlockInMail`,"post",i,r,g):T(`/${m}/excel/downloadExcelWithDataInMail`,"post",i,r,E)},qn=()=>{N("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),_(!0);const e=["Profit Center","Controlling Area","Business Segment"];let t=[],a=new Set;const c=(r,o)=>{const s=new Set;o.forEach(f=>{r==null||r.forEach((h,y)=>{h.MDG_SELECT_OPTION===f.name&&!s.has(h.MDG_FIELD_NAME)&&(s.add(h.MDG_FIELD_NAME),t.push({id:y,name:h.MDG_FIELD_NAME}),a.add(h.MDG_SELECT_OPTION))})})};if(p==="ALL OTHER CHANGES"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");c(r,z)}else if(p==="BLOCK"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="BLOCK");c(r,K)}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");c(r,Y)}t.forEach(r=>{e.push(r.name)});const C={coAreaPCs:[],headers:e,dtName:"MDG_PC_FIELD_CONFIG",version:"v1",templateName:Array.from(a).join(", "),templateHeaders:"",IsSunoco:"false"},E={coAreaPCs:[],isSunoco:"false"},g=r=>{if(N("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),Ye(!1),(r==null?void 0:r.size)!==0||(r==null?void 0:r.type)!==""){const o=URL.createObjectURL(r),s=document.createElement("a");s.href=o,s.setAttribute("download","Profit Center Mass Change.xlsx"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o),I(),M("Success"),O("Profit Center Mass Change.xlsx has been downloaded successfully"),b("success"),_(!1),N("")}else I(),M("Error"),O("Please try again."),b("danger"),_(!1),N("")},i=r=>{_(!1),N(""),r.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"))};p==="TEMPORARY BLOCK/UNBLOCK"?T(`/${m}/excel/downloadExcelWithDataForTempBlock`,"postandgetblob",g,i,E):T(`/${m}/excel/downloadExcelWithData`,"postandgetblob",g,i,C)},Fn=()=>{_(!0);const e=["Profit Center","Controlling Area","Business Segment"];let t=[],a=new Set;const c=(r,o)=>{const s=new Set;o.forEach(f=>{r==null||r.forEach((h,y)=>{h.MDG_SELECT_OPTION===f.name&&!s.has(h.MDG_FIELD_NAME)&&(s.add(h.MDG_FIELD_NAME),t.push({id:y,name:h.MDG_FIELD_NAME}),a.add(h.MDG_SELECT_OPTION))})})};if(p==="ALL OTHER CHANGES"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="ALL OTHER CHANGES");c(r,z)}else if(p==="BLOCK"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="BLOCK");c(r,K)}else if(p==="TEMPORARY BLOCK/UNBLOCK"){const r=B.filter(o=>o.MDG_FIELD_SELECTION_LVL==="TEMPORARY BLOCK/UNBLOCK");c(r,Y)}t.forEach(r=>{e.push(r.name)});const C={coAreaPCs:[],headers:e,dtName:"MDG_PC_FIELD_CONFIG",version:"v1",templateName:Array.from(a).join(", "),templateHeaders:"",IsSunoco:"false"},E={coAreaPCs:[],isSunoco:"false"},g=r=>{_(!1),N(""),Ye(!1),I(),M("Success"),O("Download has been started. You will get the Excel file via email"),b("success")},i=r=>{_(!1),N(""),r.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"))};p==="TEMPORARY BLOCK/UNBLOCK"?T(`/${m}/excel/downloadExcelWithDataForTempBlockInMail`,"post",g,i,E):T(`/${m}/excel/downloadExcelWithDataInMail`,"post",g,i,C)},vo=async()=>{_(!0),se==null||se.map(c=>({profitCenter:c.profitCenter,controllingArea:c.controllingArea}));const e=new URLSearchParams({dtName:"MDG_PC_FIELD_CONFIG",version:"v1"});let t=c=>{_(!1),N(""),I(),M("Success"),O("Download has been started. You will get the Excel file via email"),b("success")},a=c=>{_(!1),N(""),c.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"),_(!1))};T(`/${m}/excel/downloadExcelInMail?${e.toString()}&rolePrefix=ETP`,"get",t,a)},Ho=async()=>{N("Please wait 1-2 minutes while real-time data and validations are downloaded into the template. Thank you for your patience."),_(!0),se==null||se.map(c=>({profitCenter:c.profitCenter,controllingArea:c.controllingArea}));const e=new URLSearchParams({dtName:"MDG_PC_FIELD_CONFIG",version:"v1"});let t=c=>{if((c==null?void 0:c.size)!==0||(c==null?void 0:c.type)!==""){const C=URL.createObjectURL(c),E=document.createElement("a");E.href=C,E.setAttribute("download","Profit Center Mass Create.xlsx"),document.body.appendChild(E),E.click(),document.body.removeChild(E),URL.revokeObjectURL(C),I(),M("Success"),O("Profit Center Mass Create.xlsx has been downloaded successfully"),b("success"),_(!1),N("")}else I(),M("Error"),O("Please try again."),b("danger"),_(!1),N("")},a=c=>{if(console.log("error data",c),c.status===429){I(),M("Error"),O("Please try again."),b("warning");return}_(!1),N(""),c.message&&(I(),M("Error"),O("Oops! Something went wrong. Please try again later. If the issue persists, feel free to reach <NAME_EMAIL>."),b("danger"),_(!1))};T(`/${m}/excel/downloadExcel?${e.toString()}&rolePrefix=ETP`,"getblobfile",t,a)},wo=(e,t)=>{t!==0&&(_r(t),fn(!1),t===1?Ya():t===2&&Va())},Uo=()=>{Me(!0),A(us("Create"))},Ko=e=>{Xt.current&&Xt.current.contains(e.target)||hn(t=>!t)},Yo=e=>{Pt.current&&Pt.current.contains(e.target)||gn(t=>!t)},Vo=(e,t)=>{t!==0&&(pr(t),hn(!1),t===1?Uo():t===2&&Ql(!0))},Wo=(e,t)=>{t!==0&&(br(t),gn(!1),t===1?qo():t===2&&(Ve(!0),Ne(!0)))},qo=()=>{Me(!0),A(us("Change"))};return S(D,{children:[S("div",{ref:Bo,children:[l(hs,{dialogState:Cr,openReusableDialog:I,closeReusableDialog:Vn,dialogTitle:Er,dialogMessage:Sr,handleDialogConfirm:Vn,dialogOkText:"OK",dialogSeverity:Ws}),l(hs,{dialogState:ws,openReusableDialog:Hn,closeReusableDialog:wn,dialogTitle:Us,dialogMessage:Ys,handleDialogConfirm:wn,dialogSeverity:"danger",showCancelButton:!1,dialogOkText:"OK"}),S(Xe,{open:Xs,onClose:()=>Ft(!1),maxWidth:"md",fullWidth:!0,children:[l(Ze,{sx:{bgcolor:"#FFDAB9",color:"warning.contrastText"},children:S(P,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[S("span",{children:[l(Mi,{sx:{mr:1}})," Duplicate Requests Alert"]}),l(ce,{title:"Export Table",children:l(Dl,{sx:Ni,onClick:Wn.convertJsonToExcel,children:l(Ii,{iconName:"IosShare"})})})]})}),l(Qe,{children:l("div",{style:{marginTop:"20px"},children:l(as,{height:400,rows:xl,columns:Pn,pageSize:xl.length,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!1,disableSelectionOnClick:!0,status_onRowSingleClick:!0,stopPropagation_Column:"action",status_onRowDoubleClick:!0})})}),l(je,{children:l(re,{variant:"contained",onClick:()=>{Ft(!1),Ne(!1)},children:"OK"})})]}),S(Xe,{open:qs,onClose:_l,children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:l(P,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),l(Qe,{children:l($,{children:S(Bl,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:et,onChange:Vr,children:[l(sl,{arrow:!0,placement:"bottom",title:l("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:l(k,{value:"systemGenerated",control:l(ct,{}),label:"System-Generated"})}),l(sl,{arrow:!0,placement:"bottom",title:l("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:l(k,{value:"mailGenerated",control:l(ct,{}),label:"Mail-Generated"})})]})})}),l(je,{children:l(re,{variant:"contained",onClick:qr,children:"OK"})})]}),S(Xe,{open:Yr,onClose:Ml,children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:l(P,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:"Select Download Option"})}),l(Qe,{children:l($,{children:S(Bl,{row:!0,"aria-labelledby":"demo-row-radio-buttons-group-label",name:"row-radio-buttons-group",value:et,onChange:Wr,children:[l(sl,{arrow:!0,placement:"bottom",title:l("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be downloaded"}),children:l(k,{value:"systemGenerated",control:l(ct,{}),label:"System-Generated"})}),l(sl,{arrow:!0,placement:"bottom",title:l("span",{style:{whiteSpace:"nowrap",fontSize:"12px",overflow:"hidden",textOverflow:"ellipsis"},children:"Here Excel will be sent to your email"}),children:l(k,{value:"mailGenerated",control:l(ct,{}),label:"Mail-Generated"})})]})})}),l(je,{children:l(re,{variant:"contained",onClick:Fr,children:"OK"})})]}),l("div",{style:{...si,backgroundColor:"#FAFCFF"},children:S(os,{spacing:1,children:[l(x,{container:!0,sx:ri,children:S(x,{item:!0,md:5,sx:ai,children:[l(P,{variant:"h3",children:l("strong",{children:X("Profit Center")})}),l(P,{variant:"body2",color:"#777",children:X("This view displays the list of Profit Centers")})]})}),l(x,{container:!0,sx:oi,className:"filterPC",children:l(x,{item:!0,md:12,children:S(bs,{defaultExpanded:!0,children:[S(Os,{expandIcon:l(ii,{sx:{fontSize:"1.25rem",color:Wt.palette.primary.dark}}),"aria-controls":"panel1a-content",id:"panel1a-header",children:[l(ci,{sx:{fontSize:"1.25rem",marginRight:1,color:Wt.palette.primary.dark}}),l(P,{sx:{fontSize:"0.875rem",fontWeight:600,color:Wt.palette.primary.dark},children:X("Filter Profit Center")})]}),S(di,{sx:{padding:"1rem 1rem 0.5rem"},children:[S(x,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",children:[S(x,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:[Al==null?void 0:Al.filter(e=>e.MDG_MAT_VISIBILITY!=="Hidden").sort((e,t)=>e.MDG_MAT_SEQUENCE_NO-t.MDG_MAT_SEQUENCE_NO).map((e,t)=>{var a,c,C,E,g;return S(Dt.Fragment,{children:[(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.CONTROLINGAREA&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",value:n==null?void 0:n.controllingArea,onChange:ga,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",options:(u==null?void 0:u.ControllingAreaForSearchET)??[],getOptionLabel:i=>i!=null&&i.code?`${i==null?void 0:i.code} - ${i==null?void 0:i.desc}`??"":"",renderOption:(i,r)=>l("li",{...i,children:l(P,{style:{fontSize:12},children:r!=null&&r.desc?S(D,{children:[l("strong",{children:r.code})," -"," ",r.desc]}):l("strong",{children:r.code})})}),renderInput:i=>l(H,{sx:{fontSize:"12px !important"},...i,variant:"outlined",placeholder:"Select Controlling Area"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.COMPANYCODE&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{fullWidth:!0,size:"small",sx:{paddingBottom:"0.7rem"},children:l(J,{size:"small",multiple:!0,limitTags:1,id:"checkboxes-tags-demo",options:[{code:"Select All",desc:"Select All"},...(u==null?void 0:u.CompanyCodeForSearch)??[]],disableCloseOnSelect:!0,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",getOptionLabel:i=>i!=null&&i.code?`${i==null?void 0:i.code}`??"":"",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){ut([]),kt([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Ma():ut(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,inputValue:Ps,onInputChange:Pa,value:Rs,renderInput:i=>l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Select Company code"}),renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:pa(r)||(r==null?void 0:r.code)==="Select All"&&(fe==null?void 0:fe.length)===((s=u==null?void 0:u.CompanyCodeForSearch)==null?void 0:s.length)}),label:S(D,{children:[l("strong",{children:r.code})," -"," ",r.desc]})})})})}})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.PROFITCENTER&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{fullWidth:!0,size:"small",children:l(J,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:ks,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){ht([]),Gt([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Ra():ht(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(a=u==null?void 0:u.PCSearchDataET)!=null&&a.length?[{code:"Select All"},...u==null?void 0:u.PCSearchDataET]:(u==null?void 0:u.PCSearchDataET)??[],getOptionLabel:i=>i!=null&&i.code?(i==null?void 0:i.code)??"":"",renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:xa(r)||(r==null?void 0:r.code)==="Select All"&&(Ce==null?void 0:Ce.length)===((s=u==null?void 0:u.PCSearchDataET)==null?void 0:s.length)}),label:l(D,{children:l("strong",{children:r.code})})})})})},renderInput:i=>l(ce,{title:zl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:zl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Select Profit Center",onChange:r=>{Ua(r)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.LONGTEXT&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){gt([]),Bt([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Na():gt(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,value:Gs,options:(c=u==null?void 0:u.LongTextSearchET)!=null&&c.length?[{code:"Select All"},...u==null?void 0:u.LongTextSearchET]:(u==null?void 0:u.LongTextSearchET)??[],getOptionLabel:i=>i!=null&&i.code?(i==null?void 0:i.code)??"":"",renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:fa(r)||(r==null?void 0:r.code)==="Select All"&&(Ee==null?void 0:Ee.length)===((s=u==null?void 0:u.LongTextSearchET)==null?void 0:s.length)}),label:l(D,{children:l("strong",{children:r.code})})})})})},renderInput:i=>l(ce,{title:vl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:vl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Search Long Description",onChange:r=>{$a(r)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.PERSONRES&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:$s,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){ft([]),$t([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Ia():ft(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(C=u==null?void 0:u.PersonResponsibleSearchET)!=null&&C.length?[{code:"Select All"},...u==null?void 0:u.PersonResponsibleSearchET]:(u==null?void 0:u.PersonResponsibleSearchET)??[],getOptionLabel:i=>i!=null&&i.code?(i==null?void 0:i.code)??"":"",renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:Ca(r)||(r==null?void 0:r.code)==="Select All"&&(Se==null?void 0:Se.length)===((s=u==null?void 0:u.PersonResponsibleSearchET)==null?void 0:s.length)}),label:l(D,{children:l("strong",{children:r.code})})})})})},renderInput:i=>l(ce,{title:Hl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:Hl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Search Person Responsible",onChange:r=>{za(r)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.CITY&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:vs,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){At([]),Ut([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Ga():At(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(E=u==null?void 0:u.CitySearchET)!=null&&E.length?[{code:"Select All"},...u==null?void 0:u.CitySearchET]:(u==null?void 0:u.CitySearchET)??[],getOptionLabel:i=>i!=null&&i.code?(i==null?void 0:i.code)??"":"",renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:ya(r)||(r==null?void 0:r.code)==="Select All"&&(Te==null?void 0:Te.length)===((s=u==null?void 0:u.CitySearchET)==null?void 0:s.length)}),label:l(D,{children:l("strong",{children:r.code})})})})})},renderInput:i=>l(ce,{title:wl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:wl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Search City",onChange:r=>{va(r)}})})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.REGIONPC&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Pe.length>0?Pe:Wl.length>0?Wl:[],noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(i,r,o)=>{var s;if(o==="clear"||(r==null?void 0:r.length)===0){yt([]),Kt([]);return}r.length>0&&((s=r[r.length-1])==null?void 0:s.code)==="Select All"?Ba():yt(r)},renderTags:(i,r)=>i.length>0?S(D,{children:[l(w,{label:i[0].code,...r({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),i.length>1&&l(w,{label:`+${i.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(g=u==null?void 0:u.region)!=null&&g.length?[{code:"Select All"},...u==null?void 0:u.region]:(u==null?void 0:u.region)??[],getOptionLabel:i=>i!=null&&i.code?`${i==null?void 0:i.code}`??"":"",renderOption:(i,r,{selected:o})=>{var s;return l("li",{...i,children:l(ge,{children:l(k,{control:l(U,{checked:Ta(r)||(r==null?void 0:r.code)==="Select All"&&(Pe==null?void 0:Pe.length)===((s=u==null?void 0:u.region)==null?void 0:s.length)}),label:l(D,{children:l("strong",{children:r.code})})})})})},renderInput:i=>l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...i,variant:"outlined",placeholder:"Select Region"})})})]}),(e==null?void 0:e.MDG_MAT_JSON_FIELD_NAME)===$e.BLOCKINGSAT&&S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X(e==null?void 0:e.MDG_MAT_UI_FIELD_NAME)}),l($,{fullWidth:!0,size:"small",children:l(is,{placeholder:"Select Blocking Status",sx:{height:"36px"},size:"small",value:n==null?void 0:n.blockingStatus,name:"blockingStatus",onChange:i=>Wa(i),displayEmpty:!0,MenuProps:Zl,children:Nl==null?void 0:Nl.map(i=>l(Rt,{sx:F,value:i,style:{fontSize:"12px !important",height:"35px"},children:l(ui,{sx:F,primary:i,style:{fontSize:"12px !important"}})},i))})})]})]},t)}),S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X("Add New Filters")}),l($,{fullWidth:!0,children:l(is,{sx:{font_Small:F,height:"36px",fontSize:"12px"},size:"small",multiple:!0,limitTags:2,value:_e,onChange:Fa,renderValue:e=>e.join(", "),MenuProps:{MenuProps:Zl},endAdornment:_e.length>0&&l(hi,{position:"end",sx:{marginRight:"15px"},children:l(Dl,{sx:{height:"10px",width:"10px"},size:"small",onClick:()=>tn([]),"aria-label":"Clear selections",children:l(gi,{})})}),children:Sl==null?void 0:Sl.map(e=>S(Rt,{value:e.title,children:[l(U,{checked:_e.indexOf(e.title)>-1}),e.title]},e.title))})}),l(x,{style:{display:"flex",justifyContent:"space-around"}})]})]}),l(x,{container:!0,rowSpacing:1,spacing:2,justifyContent:"space-between",alignItems:"center",sx:{padding:"0.5rem 1rem 0.5rem"},children:l(x,{container:!0,spacing:1,sx:{padding:"0rem 1rem 0.5rem"},children:_e==null?void 0:_e.map((e,t)=>{var a,c,C,E,g,i,r;return e==="Short Description"?l(D,{children:S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X("Short Description")}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Bs,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(o,s,f)=>{var h;if(f==="clear"||(s==null?void 0:s.length)===0){St([]),Ht([]);return}s.length>0&&((h=s[s.length-1])==null?void 0:h.code)==="Select All"?ka():St(s)},renderTags:(o,s)=>o.length>0?S(D,{children:[l(w,{label:o[0].code,...s({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),o.length>1&&l(w,{label:`+${o.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(a=u==null?void 0:u.PCNameSearchET)!=null&&a.length?[{code:"Select All"},...u==null?void 0:u.PCNameSearchET]:(u==null?void 0:u.PCNameSearchET)??[],getOptionLabel:o=>o!=null&&o.code?(o==null?void 0:o.code)??"":"",renderOption:(o,s,{selected:f})=>{var h;return l("li",{...o,children:l(ge,{children:l(k,{control:l(U,{checked:Aa(s)||(s==null?void 0:s.code)==="Select All"&&(Ae==null?void 0:Ae.length)===((h=u==null?void 0:u.PCNameSearchET)==null?void 0:h.length)}),label:l(D,{children:l("strong",{children:s.code})})})})})},renderInput:o=>l(ce,{title:Ul.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:Ul.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...o,variant:"outlined",placeholder:"Search Short Description",onChange:s=>{Ha(s)}})})})})]})}):e==="Street"?l(D,{children:S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X("Street")}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:zs,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",limitTags:1,onChange:(o,s,f)=>{var h;if(f==="clear"||(s==null?void 0:s.length)===0){xt([]),wt([]);return}s.length>0&&((h=s[s.length-1])==null?void 0:h.code)==="Select All"?Da():xt(s)},renderTags:(o,s)=>o.length>0?S(D,{children:[l(w,{label:o[0].code,...s({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),o.length>1&&l(w,{label:`+${o.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,options:(c=u==null?void 0:u.StreetSearchET)!=null&&c.length?[{code:"Select All"},...u==null?void 0:u.StreetSearchET]:(u==null?void 0:u.StreetSearchET)??[],getOptionLabel:o=>o!=null&&o.code?(o==null?void 0:o.code)??"":"",renderOption:(o,s,{selected:f})=>{var h;return l("li",{...o,children:l(ge,{children:l(k,{control:l(U,{checked:Sa(s)||(s==null?void 0:s.code)==="Select All"&&(ye==null?void 0:ye.length)===((h=u==null?void 0:u.StreetSearchET)==null?void 0:h.length)}),label:l(D,{children:l("strong",{children:s.code})})})})})},renderInput:o=>l(ce,{title:Kl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:Kl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...o,variant:"outlined",placeholder:"Search Street",onChange:s=>{wa(s)}})})})})]})}):e==="Created By"?l(D,{children:S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X("Created By")}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:Hs,noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(o,s,f)=>{var h;if(f==="clear"||(s==null?void 0:s.length)===0){Et([]),vt([]);return}s.length>0&&((h=s[s.length-1])==null?void 0:h.code)==="Select All"?Oa():Et(s)},renderTags:(o,s)=>o.length>0?S(D,{children:[l(w,{label:o[0].code,...s({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),o.length>1&&l(w,{label:`+${o.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(C=u==null?void 0:u.CreatedBySearchET)!=null&&C.length?[{code:"Select All"},...u==null?void 0:u.CreatedBySearchET]:(u==null?void 0:u.CreatedBySearchET)??[],getOptionLabel:o=>o!=null&&o.code?(o==null?void 0:o.code)??"":"",renderOption:(o,s,{selected:f})=>{var h;return l("li",{...o,children:l(ge,{children:l(k,{control:l(U,{checked:Ea(s)||(s==null?void 0:s.code)==="Select All"&&(xe==null?void 0:xe.length)===((h=u==null?void 0:u.CreatedBySearchET)==null?void 0:h.length)}),label:l(D,{children:l("strong",{children:s.code})})})})})},renderInput:o=>l(ce,{title:Yl.length<4?"Enter at least 4 characters":"",arrow:!0,placement:"top",disableHoverListener:Yl.length>=4,children:l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...o,variant:"outlined",placeholder:"Search Created By",onChange:s=>{Ka(s)}})})})})]})}):e==="Created On"?S(x,{item:!0,md:2,children:[l(j,{sx:F,children:e}),l($,{size:"small",fullWidth:!0,children:l(fi,{dateAdapter:Ci,children:l(Ei,{handleDate:po,date:Ms})})})]}):e==="Country/Region"?l(D,{children:S(x,{item:!0,md:2,children:[l(j,{sx:F,children:X("Country/Region")}),l($,{size:"small",fullWidth:!0,children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",multiple:!0,disableCloseOnSelect:!0,value:Le.length>0?Le:Vl.length>0?Vl:[],noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(o,s,f)=>{var h;if(f==="clear"||(s==null?void 0:s.length)===0){Ct([]),zt([]);return}s.length>0&&((h=s[s.length-1])==null?void 0:h.code)==="Select All"?ba():Ct(s)},renderTags:(o,s)=>o.length>0?S(D,{children:[l(w,{label:o[0].code,...s({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),o.length>1&&l(w,{label:`+${o.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(E=u==null?void 0:u.country)!=null&&E.length?[{code:"Select All",desc:"Select All"},...u==null?void 0:u.country]:(u==null?void 0:u.country)??[],getOptionLabel:o=>o!=null&&o.code?`${o==null?void 0:o.code}`??"":"",renderOption:(o,s,{selected:f})=>{var h;return l("li",{...o,children:l(ge,{children:l(k,{control:l(U,{checked:ma(s)||(s==null?void 0:s.code)==="Select All"&&(Le==null?void 0:Le.length)===((h=u==null?void 0:u.country)==null?void 0:h.length)}),label:S(D,{children:[l("strong",{children:s.code})," ","- ",s.desc]})})})})},renderInput:o=>l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...o,variant:"outlined",placeholder:"Search Country"})})})]})}):S(x,{item:!0,md:2,children:[l(j,{sx:F,children:e}),l($,{fullWidth:!0,size:"small",children:l(J,{sx:{height:"31px"},fullWidth:!0,multiple:!0,disableCloseOnSelect:!0,size:"small",value:((g=ke[e])==null?void 0:g.length)>0?ke[e]:((i=ql[e])==null?void 0:i.length)>0?ql[e]:[],noOptionsText:ae?l(Z,{sx:{display:"flex",justifyContent:"center",mt:1,zIndex:9999,top:"10px"},children:l(ie,{size:20})}):"No Data Available",onChange:(o,s,f)=>{var h;if(f==="clear"||(s==null?void 0:s.length)===0){Tt(y=>({...y,[e]:[]})),Yt(y=>({...y,[e]:[]}));return}s.length>0&&((h=s[s.length-1])==null?void 0:h.code)==="Select All"?_a(e):Tt(y=>({...y,[e]:s}))},renderTags:(o,s)=>o.length>0?S(D,{children:[l(w,{label:o[0].code,...s({index:0}),sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}}),o.length>1&&l(w,{label:`+${o.length-1}`,sx:{height:20,fontSize:"0.75rem",".MuiChip-label":{padding:"0 6px"}}})]}):null,limitTags:1,options:(r=ne==null?void 0:ne[e])!=null&&r.length?[{code:"Select All"},...ne==null?void 0:ne[e]]:(ne==null?void 0:ne[e])??[],getOptionLabel:o=>o!=null&&o.code?`${o.code}`:"",renderOption:(o,s,{selected:f})=>{var h,y;return l("li",{...o,children:l(ge,{children:l(k,{control:l(U,{checked:La(e,s)||(s==null?void 0:s.code)==="Select All"&&((h=ke[e])==null?void 0:h.length)===((y=ne==null?void 0:ne[e])==null?void 0:y.length)}),label:l(D,{children:l("strong",{children:s==null?void 0:s.code})})})})})},renderInput:o=>l(H,{sx:{fontSize:"12px !important","& .MuiOutlinedInput-root":{height:35},"& .MuiInputBase-input":{padding:"10px 14px"}},...o,variant:"outlined",placeholder:`Select ${e}`})})})]})})})})]}),S(Ds,{children:[l(Jl,{variant:"outlined",onClick:Mo,children:"Clear"}),l(x,{sx:{...cs},children:l(wi,{moduleName:"ProfitCenter",PresetObj:oa,handleSearch:bl,PresetMethod:aa})}),l(Jl,{variant:"contained",startIcon:l(Si,{sx:{fontSize:"1rem"}}),sx:{...xi,...cs},onClick:()=>bl(),children:"Search"})]})]})]})})}),l(x,{item:!0,sx:{position:"relative"},children:l(os,{children:l(as,{isLoading:Qs,module:"ProfitCenter",width:"100%",title:"List of Profit Centers",rows:oe,columns:ln??[],showSearch:!0,page:Tl,pageSize:ml,showExport:!0,showRefresh:!0,rowCount:hr??(oe==null?void 0:oe.length)??0,onPageChange:Po,onPageSizeChange:_o,getRowIdValue:"id",hideFooter:!0,checkboxSelection:!0,disableSelectionOnClick:!0,status_onRowDoubleClick:!0,onRowsSelectionHandler:Eo,callback_onRowDoubleClick:e=>{const t=e.row.profitCenter;pe(`/masterDataCockpit/profitCenter/displayProfitCenter/${t}`,{state:e.row})},stopPropagation_Column:"action",showCustomNavigation:!0})})}),l(il,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:2,children:S(Ai,{className:"container_BottomNav",showLabels:!0,sx:{display:"flex",justifyContent:"flex-end",gap:1},value:Zs,onChange:e=>{js(e)},children:[l(Rl,{sx:{zIndex:1},open:Mr,anchorEl:Qt.current,placement:"top-end",children:l(il,{style:{width:(Jn=Qt.current)==null?void 0:Jn.clientWidth},children:l(kl,{onClickAway:ha,children:l(Gl,{id:"split-button-menu",autoFocusItem:!0,children:(Xn=Zr.slice(1))==null?void 0:Xn.map((e,t)=>l(Rt,{selected:t===Pr-1,onClick:()=>wo(e,t+1),children:e},e))})})})}),S(Xe,{open:gr,onClose:Yn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:S(x,{children:[l(x,{sx:{display:"flex",justifyContent:"space-between"},children:S(ds,{color:"primary",value:p,exclusive:!0,onChange:On,children:[l(it,{value:"ALL OTHER CHANGES",children:"ALL OTHER CHANGES"}),!vr&&l(it,{value:"BLOCK",children:"BLOCK"}),l(it,{value:"TEMPORARY BLOCK/UNBLOCK",children:"TEMPORARY BLOCK/UNBLOCK"})]})}),l(x,{children:l(P,{variant:"h6",children:"Select the field(s) to be changed"})})]})}),l(Qe,{sx:{padding:".5rem 1rem",maxHeight:400,maxWidth:400,overflowY:"auto"},children:S(x,{container:!0,children:[p==="ALL OTHER CHANGES"?l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:Un,checked:be}),label:"SELECT ALL"})}):"",p==="ALL OTHER CHANGES"?We==null?void 0:We.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>Dn(e),checked:z==null?void 0:z.some(t=>t.id===e.id)}),label:e.name})},e==null?void 0:e.id)):p==="BLOCK"?Be==null?void 0:Be.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>Rn(e),checked:K==null?void 0:K.some(t=>t.id===e.id),disabled:!0}),label:e.name})},e==null?void 0:e.id)):p==="TEMPORARY BLOCK/UNBLOCK"?qe==null?void 0:qe.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>kn(e),checked:Y==null?void 0:Y.some(t=>t.id===e.id),disabled:!0}),label:e.name})},e==null?void 0:e.id)):st==null?void 0:st.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{onChange:()=>handleSelectionProfitCenterChange(e),checked:rt==null?void 0:rt.some(t=>t.id===e.id)}),label:e.name})},e.id))]})}),S(je,{sx:{display:"flex",justifyContent:"end"},children:[l(re,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Yn,children:"Cancel"}),l(re,{className:"button_primary--normal",type:"save",onClick:Mn,variant:"contained",children:"Apply"})]})]}),S(Xe,{open:fr,onClose:Kn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:S(x,{children:[l(x,{sx:{display:"flex",justifyContent:"space-between"},children:S(ds,{color:"primary",value:p,exclusive:!0,onChange:On,children:[l(it,{value:"ALL OTHER CHANGES",children:"ALL OTHER CHANGES"}),!Hr&&l(it,{value:"BLOCK",children:"BLOCK"}),l(it,{value:"TEMPORARY BLOCK/UNBLOCK",children:"TEMPORARY BLOCK/UNBLOCK"})]})}),l(x,{children:l(P,{variant:"h6",children:"Select the field(s) to be changed"})})]})}),l(Qe,{sx:{padding:".5rem 1rem",maxHeight:400,maxWidth:400,overflowY:"auto"},children:S(x,{container:!0,children:[p==="ALL OTHER CHANGES"?l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:Un,checked:be}),label:"SELECT ALL"})}):"",p==="ALL OTHER CHANGES"?We==null?void 0:We.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>Dn(e),checked:z==null?void 0:z.some(t=>t.id===e.id)}),label:e.name})},e==null?void 0:e.id)):p==="BLOCK"?Be==null?void 0:Be.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>Rn(e),checked:K==null?void 0:K.some(t=>t.id===e.id),disabled:!0}),label:e.name})},e==null?void 0:e.id)):p==="TEMPORARY BLOCK/UNBLOCK"?qe==null?void 0:qe.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{sx:{height:"2vh"},onChange:()=>kn(e),checked:Y==null?void 0:Y.some(t=>t.id===e.id),disabled:!0}),label:e.name})},e==null?void 0:e.id)):st==null?void 0:st.map(e=>l(x,{item:!0,xs:12,children:l(k,{control:l(U,{onChange:()=>handleSelectionProfitCenterChange(e),checked:rt==null?void 0:rt.some(t=>t.id===e.id)}),label:e.name})},e.id))]})}),S(je,{sx:{display:"flex",justifyContent:"end"},children:[l(re,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Kn,children:"Cancel"}),l(re,{className:"button_primary--normal",type:"save",onClick:()=>Ln(!0),variant:"contained",children:"Apply"})]})]}),S(Xe,{fullWidth:!0,open:or,onClose:$n,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF",display:"flex"},children:l(P,{variant:"h6",children:"New Profit Center"})}),S(Qe,{sx:{padding:".5rem 1rem"},children:[S($,{component:"fieldset",children:[l(yi,{component:"legend",children:"Do You Know Profit Center Number?"}),S(Bl,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:nl,onChange:Jr,children:[l(k,{value:"yes",control:l(ct,{}),label:"Yes"}),l(k,{value:"no",control:l(ct,{}),label:"No"})]})]}),nl==="yes"?S(x,{container:!0,spacing:1,children:[S(x,{item:!0,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Profit Center",l("span",{style:{color:"red"},children:"*"})]}),S($,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[l(x,{md:1,children:l(H,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),l(x,{md:6,children:l(J,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{_t(t),A($l({keyName:"CompanyCode",data:t})),In(t)},options:(u==null?void 0:u.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),S(x,{md:5,children:[l(H,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:Ie,onChange:e=>{let t=e.target.value;if(/^\d*$/.test(t))if(t.length>0&&t[0]===" ")Ue(t.trimStart());else{let a=t.toUpperCase();Ue(a)}},inputProps:{length:el-((Qn=G==null?void 0:G.code)==null?void 0:Qn.length)-1,maxLength:el-((Zn=G==null?void 0:G.code)==null?void 0:Zn.length)-1,style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:Sn}),Sn&&l(P,{variant:"caption",color:"error",children:"Profit Center must be 10 digits"})]})]}),dn&&l(P,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),En&&l(x,{children:l(P,{style:{color:"red"},children:"Please Enter Mandatory Fields"})}),_n&&l(x,{children:l(P,{style:{color:"red"},children:"*Profit Center Number is already used - please select a different number before submitting request."})})]}):S(x,{container:!0,spacing:1,sx:{display:"flex",flexDirection:"column"},children:[S(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Select Company Code",l("span",{style:{color:"red"},children:"*"})]}),l($,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:l(J,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{_t(t),A($l({keyName:"CompanyCode",data:t})),In(t)},options:(u==null?void 0:u.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})]}),l(x,{item:!0,children:En&&l(x,{children:l(P,{style:{color:"red"},children:"Please Enter Mandatory Fields"})})})]})]}),S(je,{sx:{display:"flex",justifyContent:"end"},children:[l(re,{sx:{width:"max-content",textTransform:"capitalize"},onClick:$n,children:"Cancel"}),l(re,{className:"button_primary--normal",type:"save",onClick:da,variant:"contained",children:"Proceed"})]})]}),S(Xe,{open:ir,onClose:Gn,sx:{"&::webkit-scrollbar":{width:"1px"}},children:[l(Ze,{sx:{justifyContent:"space-between",alignItems:"center",height:"max-content",padding:".5rem",paddingLeft:"1rem",backgroundColor:"#EAE9FF40",display:"flex"},children:l(P,{variant:"h6",children:"New Profit Center"})}),S(Qe,{sx:{padding:".5rem 1rem"},children:[S(x,{container:!0,spacing:1,children:[S(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Controlling Area",l("span",{style:{color:"red"},children:"*"})]}),l($,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px"},children:l(J,{sx:{height:"31px"},fullWidth:!0,size:"small",onChange:(e,t)=>{yl(t),Nn(t),na(t),Za(t)},options:(u==null?void 0:u.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA",error:un})})}),un&&l(P,{variant:"caption",color:"error",children:"Please Select a Controlling Area."})]}),S(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Profit Center",l("span",{style:{color:"red"},children:"*"})]}),S($,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:[l(x,{md:2,children:l(H,{sx:{fontSize:"12px !important",height:"31px"},value:"P",fullWidth:!0,size:"small",editable:!1})}),l(x,{md:5,children:l(J,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Lr(t)},options:(u==null?void 0:u.CompanyCode)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t.desc}`})}),renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})}),S(x,{md:5,children:[l(H,{sx:{fontSize:"12px !important",height:"31px"},fullWidth:!0,size:"small",value:Ie,onChange:e=>{let t=e.target.value;if(t.length>0&&t[0]===" ")Ue(t.trimStart());else{let a=t.toUpperCase();Ue(a)}},inputProps:{length:el-((jn=G==null?void 0:G.code)==null?void 0:jn.length),maxLength:el-((es=G==null?void 0:G.code)==null?void 0:es.length),style:{textTransform:"uppercase"}},placeholder:"Enter Profit Center",required:!0,error:xn}),xn&&l(P,{variant:"caption",color:"error",children:"Profit Center must be 10 digits"})]})]}),dn&&l(P,{variant:"caption",color:"error",children:"Please enter a Profit Center."})]}),l(Ti,{sx:{width:"100%",marginLeft:"2%"},children:l("b",{children:"Copy From"})}),S(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Controlling Area",l("span",{style:{color:"red"},children:"*"})]}),l($,{fullWidth:!0,sx:{margin:".5em 0px"},children:l(J,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{Nr(t),ra(t)},options:(at==null?void 0:at.ControllingArea)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),error:ve==="",renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT CONTROLLING AREA"})})})]}),S(x,{item:!0,md:6,sx:{width:"100%",marginTop:".5rem"},children:[S(P,{children:["Profit Center",l("span",{style:{color:"red"},children:"*"})]}),l($,{fullWidth:!0,sx:{margin:".5em 0px",minWidth:"250px",flexDirection:"row"},children:l(x,{md:12,children:l(J,{sx:{height:"42px"},required:"true",size:"small",onChange:(e,t)=>{rn(t)},options:(at==null?void 0:at.ProfitCenter)??[],getOptionLabel:e=>`${e==null?void 0:e.code}-${e==null?void 0:e.desc}`,renderOption:(e,t)=>l("li",{...e,children:l(P,{style:{fontSize:12},children:`${t==null?void 0:t.code}-${t==null?void 0:t.desc}`})}),renderInput:e=>l(H,{sx:{fontSize:"12px !important"},...e,variant:"outlined",placeholder:"SELECT COMPANY CODE"})})})})]}),_n&&l(x,{children:l(P,{style:{color:"red"},children:"*The Profit Center with Controlling Area already exists. Please enter different Profit Center or Controlling Area"})})]}),Or&&l(x,{children:l(P,{style:{color:"red"},children:"Please Enter Mandatory Fields"})})]}),S(je,{sx:{display:"flex",justifyContent:"end"},children:[l(re,{sx:{width:"max-content",textTransform:"capitalize"},onClick:Gn,children:"Cancel"}),l(re,{className:"button_primary--normal",type:"save",onClick:ia,variant:"contained",children:"Proceed"})]})]}),l(Rl,{sx:{zIndex:1},open:yr,anchorEl:Xt.current,placement:"top-end",children:l(il,{style:{width:(ts=Xt.current)==null?void 0:ts.clientWidth},children:l(kl,{onClickAway:Ko,children:l(Gl,{id:"split-button-menu",autoFocusItem:!0,children:(ls=Xr.slice(1))==null?void 0:ls.map((e,t)=>l(Rt,{selected:t===Tr-1,onClick:()=>Vo(e,t+1),children:e},e))})})})}),l(pi,{variant:"contained",ref:Pt,"aria-label":"split button",children:l(re,{className:"createRequestButtonPC",size:"small",onClick:()=>{pe("/requestBench/ProfitCenterRequestTab",{state:{steaperData:["Request Header","Profit Center List","Attachments & Comments"],moduleName:"ProfitCenter"}}),A(setDisplayPayload({})),A(setRequestHeader({}))},sx:{cursor:"pointer"},children:"Create Request"})}),l(Rl,{sx:{zIndex:1},open:mr,anchorEl:Pt.current,placement:"top-end",children:l(il,{style:{width:(ns=Pt.current)==null?void 0:ns.clientWidth},children:l(kl,{onClickAway:Yo,children:l(Gl,{id:"split-button-menu",autoFocusItem:!0,children:(ss=Qr.slice(1))==null?void 0:ss.map((e,t)=>l(Rt,{selected:t===Ir-1,onClick:()=>Wo(e,t+1),children:e},e))})})})}),cr&&l(Hi,{artifactId:"",artifactName:"",setOpen:Me,handleUpload:Co})]})})]})})]}),l(bi,{blurLoading:kr,loaderMessage:Ur})]})};export{hc as default};
