import{r as c,j as u,aR as ke,aS as e,n as ye,c as U,ai as He,B as Ie,d as We,al as we,am as Pe,an as Me,aG as xe,F as $e,C as a,au as h,aT as G,aJ as q,aU as oe,aV as Ye,aF as Fe,aW as Xe,aX as je,ae as ve,aY as me}from"./index-226a1e75.js";import{d as qe}from"./FeedOutlined-2c089703.js";import{F as Ce}from"./FilterChangeDropdown-2d228e28.js";import{u as Ve}from"./useMaterialFieldConfig-a3bf7965.js";const ze=c.forwardRef(function(I,B){return u(ke,{direction:"down",ref:B,...I})}),Je=({open:ne,onClose:I,parameters:B,templateName:de,allDropDownData:g,onSearch:_e,buttonName:Le="Search"})=>{var te,De,pe,Ue,ae;const[o,N]=c.useState({}),[M,Ne]=c.useState({}),[b,d]=c.useState({[e.MATERIAL_NUMBER]:[],[e.SALES_ORG]:[],[e.DISTRIBUTION_CHANNEL]:[],[e.PLANT]:[],[e.WAREHOUSE]:[],[e.STORAGE_LOCATION]:[]});c.useState([]);const[se,Oe]=c.useState(!1),[v,V]=c.useState({code:"",desc:""}),[Ee,fe]=c.useState(null),[i,T]=c.useState({[e.MATERIAL_NUMBER]:!1,[e.SALES_ORG]:!1,[e.DISTRIBUTION_CHANNEL]:!1,[e.PLANT]:!1,[e.WAREHOUSE]:!1,[e.STORAGE_LOCATION]:!1}),y=c.useRef(null),z=ye(t=>t.request.salesOrgDTData),{fetchOrgData:ge}=Ve(),[C,le]=c.useState(null),[x,Ae]=c.useState(""),[$,D]=c.useState(!1),k=c.useRef(null),H=(t,s)=>{N(E=>({...E,[t]:s})),s.length>0&&Ne(E=>({...E,[t]:""}))},W=(t,s)=>{le(t.currentTarget),Ae(s),D(!0)},w=()=>{D(!1)},P=()=>{D(!0)},Y=()=>{D(!1)},F=(t,s)=>{N(E=>({...E,[t]:s}))},ce=t=>{var l;const s=(l=t.target.value)==null?void 0:l.toUpperCase();V({code:s,desc:""}),Ee&&clearTimeout(Ee);const E=setTimeout(()=>{var n;(n=o==null?void 0:o[e.MATERIAL_TYPE])!=null&&n.length&&j(s,!0,o==null?void 0:o[e.MATERIAL_TYPE][0])},500);fe(E)},X=t=>t.code&&t.desc?`${t.code} - ${t.desc}`:t.code||"",j=(t="",s=!1,E)=>{var r,S,L,O;T(R=>({...R,[e.MATERIAL_NUMBER]:!0}));const l={matlType:(E==null?void 0:E.code)??"",materialNo:t??"",top:500,skip:s?0:skip,salesOrg:((S=(r=z==null?void 0:z.uniqueSalesOrgList)==null?void 0:r.map(R=>R.code))==null?void 0:S.join("$^$"))||""},n=R=>{(R==null?void 0:R.statusCode)===q.STATUS_200&&(d(s?_=>({..._,[e.MATERIAL_NUMBER]:R.body}):_=>({..._,[e.MATERIAL_NUMBER]:[..._[e.MATERIAL_NUMBER]||[],...R.body]})),T(_=>({..._,[e.MATERIAL_NUMBER]:!1})))},A=R=>{customError(R),T(_=>({..._,[e.MATERIAL_NUMBER]:!1}))};a(`/${h}${(O=(L=G)==null?void 0:L.DATA)==null?void 0:O.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",n,A,l)},re=(t,s)=>{T(n=>({...n,[e.SALES_ORG]:!0}));const E=n=>{if((n==null?void 0:n.statusCode)===q.STATUS_200){let A=n.body.length>0?oe(n.body):[];d(r=>({...r,[e.SALES_ORG]:A}))}T(A=>({...A,[e.SALES_ORG]:!1}))},l=()=>{T(n=>({...n,[e.SALES_ORG]:!1}))};a(`/${h}${G.DATA.GET_SALES_ORG_EXTENDED}?materialNo=${t}&region=${s}`,"get",E,l)},ie=(t,s,E)=>{T(A=>({...A,[e.PLANT]:!0}));const l=A=>{if((A==null?void 0:A.statusCode)===q.STATUS_200){let r=A.body.length>0?oe(A.body||[]):[];d(S=>({...S,[e.PLANT]:r}))}T(r=>({...r,[e.PLANT]:!1}))},n=()=>{T(A=>({...A,[e.PLANT]:!1}))};a(`/${h}${G.DATA.GET_PLANT_EXTENDED}?materialNo=${t}&region=${E}&salesOrg=${s}`,"get",l,n)},Te=(t,s)=>{T(n=>({...n,[e.DISTRIBUTION_CHANNEL]:!0}));const E=n=>{if((n==null?void 0:n.statusCode)===q.STATUS_200){let A=n.body.length>0?oe(n.body||[]):[];d(r=>({...r,[e.DISTRIBUTION_CHANNEL]:A}))}T(A=>({...A,[e.DISTRIBUTION_CHANNEL]:!1}))},l=()=>{T(n=>({...n,[e.DISTRIBUTION_CHANNEL]:!1}))};a(`/${h}${G.DATA.GET_DISTR_CHAN_EXTENDED}?materialNo=${t}&salesOrg=${s}`,"get",E,l)},Re=(t,s,E)=>{T(A=>({...A,[e.WAREHOUSE]:!0}));const l=A=>{if((A==null?void 0:A.statusCode)===q.STATUS_200){let r=A.body.length>0?oe(A.body||[]):[];d(S=>({...S,[e.WAREHOUSE]:r}))}T(r=>({...r,[e.WAREHOUSE]:!1}))},n=()=>{T(A=>({...A,[e.WAREHOUSE]:!1}))};a(`/${h}${G.DATA.GET_WAREHOUSE_EXTENDED}?materialNo=${t}&region=${E}&plant=${s}`,"get",l,n)},Se=(t,s,E,l)=>{T(r=>({...r,[e.STORAGE_LOCATION]:!0}));const n=r=>{if((r==null?void 0:r.statusCode)===q.STATUS_200){let S=r.body.length>0?oe(r.body||[]):[];d(L=>({...L,[e.STORAGE_LOCATION]:S}))}T(S=>({...S,[e.STORAGE_LOCATION]:!1}))},A=()=>{T(r=>({...r,[e.STORAGE_LOCATION]:!1}))};a(`/${h}${G.DATA.GET_STOR_LOC_EXTENDED}?materialNo=${t}&region=${E}&plant=${s}&salesOrg=${l}`,"get",n,A)};c.useEffect(()=>{var t,s,E;(s=o[(t=e)==null?void 0:t.REGION])!=null&&s.length&&ge(o[(E=e)==null?void 0:E.REGION][0])},[o[(te=e)==null?void 0:te.REGION]]),c.useEffect(()=>{var t,s,E;(s=o[(t=e)==null?void 0:t.MATERIAL_TYPE])!=null&&s.length&&j("",!0,o[(E=e)==null?void 0:E.MATERIAL_TYPE][0])},[o[(De=e)==null?void 0:De.MATERIAL_TYPE]]),c.useEffect(()=>{var t,s,E,l,n,A,r,S,L,O,R;if((s=o[(t=e)==null?void 0:t.MATERIAL_NUMBER])!=null&&s.length){re((l=o[(E=e)==null?void 0:E.MATERIAL_NUMBER][0])==null?void 0:l.code,(A=o[(n=e)==null?void 0:n.REGION][0])==null?void 0:A.code);return}(S=o[(r=e)==null?void 0:r.MATERIAL_TYPE])!=null&&S.length&&!((O=o[(L=e)==null?void 0:L.MATERIAL_NUMBER])!=null&&O.length)&&j("",!0,o[(R=e)==null?void 0:R.MATERIAL_TYPE][0])},[o[(pe=e)==null?void 0:pe.MATERIAL_NUMBER]]),c.useEffect(()=>{var t,s,E,l,n,A,r,S,L,O,R,_;(s=o[(t=e)==null?void 0:t.SALES_ORG])!=null&&s.length&&(ie((l=o[(E=e)==null?void 0:E.MATERIAL_NUMBER][0])==null?void 0:l.code,(A=o[(n=e)==null?void 0:n.SALES_ORG][0])==null?void 0:A.code,(S=o[(r=e)==null?void 0:r.REGION][0])==null?void 0:S.code),Te((O=o[(L=e)==null?void 0:L.MATERIAL_NUMBER][0])==null?void 0:O.code,(_=o[(R=e)==null?void 0:R.SALES_ORG][0])==null?void 0:_.code))},[o[(Ue=e)==null?void 0:Ue.SALES_ORG]]),c.useEffect(()=>{var t,s,E,l,n,A,r,S,L,O,R,_,he,Ge,Be,be;(s=o[(t=e)==null?void 0:t.PLANT])!=null&&s.length&&(Re((l=o[(E=e)==null?void 0:E.MATERIAL_NUMBER][0])==null?void 0:l.code,(A=o[(n=e)==null?void 0:n.PLANT][0])==null?void 0:A.code,(S=o[(r=e)==null?void 0:r.REGION][0])==null?void 0:S.code),Se((O=o[(L=e)==null?void 0:L.MATERIAL_NUMBER][0])==null?void 0:O.code,(_=o[(R=e)==null?void 0:R.PLANT][0])==null?void 0:_.code,(Ge=o[(he=e)==null?void 0:he.REGION][0])==null?void 0:Ge.code,(be=o[(Be=e)==null?void 0:Be.SALES_ORG][0])==null?void 0:be.code))},[o[(ae=e)==null?void 0:ae.PLANT]]),c.useEffect(()=>{Object.keys(o).length===0&&d(t=>({...t,[e.MATERIAL_NUMBER]:[],[e.SALES_ORG]:[],[e.DISTRIBUTION_CHANNEL]:[],[e.PLANT]:[],[e.WAREHOUSE]:[],[e.STORAGE_LOCATION]:[]}))},[o]);const J=c.useRef(),K=c.useRef(),Q=c.useRef(),Z=c.useRef(),ee=c.useRef();c.useEffect(()=>{var s,E,l;const t=(E=(s=o[e.REGION])==null?void 0:s[0])==null?void 0:E.code;(!((l=o[e.REGION])!=null&&l.length)||t!==J.current)&&(d(n=>({...n,[e.MATERIAL_TYPE]:[]})),N(n=>({...n,[e.MATERIAL_TYPE]:[]}))),J.current=t},[o[e.REGION]]),c.useEffect(()=>{var s,E,l;const t=(E=(s=o[e.MATERIAL_TYPE])==null?void 0:s[0])==null?void 0:E.code;(!((l=o[e.MATERIAL_TYPE])!=null&&l.length)||t!==K.current)&&(d(n=>({...n,[e.MATERIAL_NUMBER]:[]})),N(n=>({...n,[e.MATERIAL_NUMBER]:[]}))),K.current=t},[o[e.MATERIAL_TYPE]]),c.useEffect(()=>{var s,E,l;const t=(E=(s=o[e.MATERIAL_NUMBER])==null?void 0:s[0])==null?void 0:E.code;(!((l=o[e.MATERIAL_NUMBER])!=null&&l.length)||t!==Q.current)&&(d(n=>({...n,[e.SALES_ORG]:[]})),N(n=>({...n,[e.SALES_ORG]:[]}))),Q.current=t},[o[e.MATERIAL_NUMBER]]),c.useEffect(()=>{var s,E,l;const t=(E=(s=o[e.SALES_ORG])==null?void 0:s[0])==null?void 0:E.code;(!((l=o[e.SALES_ORG])!=null&&l.length)||t!==Z.current)&&(d(n=>({...n,[e.DISTRIBUTION_CHANNEL]:[],[e.PLANT]:[]})),N(n=>({...n,[e.DISTRIBUTION_CHANNEL]:[],[e.PLANT]:[]}))),Z.current=t},[o[e.SALES_ORG]]),c.useEffect(()=>{var s,E,l;const t=(E=(s=o[e.PLANT])==null?void 0:s[0])==null?void 0:E.code;(!((l=o[e.PLANT])!=null&&l.length)||t!==ee.current)&&(d(n=>({...n,[e.WAREHOUSE]:[],[e.STORAGE_LOCATION]:[]})),N(n=>({...n,[e.WAREHOUSE]:[],[e.STORAGE_LOCATION]:[]}))),ee.current=t},[o[e.PLANT]]);const f=t=>{var s,E,l;return(t==null?void 0:t.key)===((s=e)==null?void 0:s.MATERIAL_NUMBER)?u(Ce,{param:t,dropDownData:b,allDropDownData:g,selectedValues:o,inputState:v,handleSelectAll:F,handleSelectionChange:H,handleMatInputChange:ce,dropdownRef:y,errors:M,formatOptionLabel:X,handlePopoverOpen:W,handlePopoverClose:w,handleMouseEnterPopover:P,handleMouseLeavePopover:Y,isPopoverVisible:$,popoverId:m?"custom-popover":void 0,popoverAnchorEl:C,popoverRef:k,popoverContent:x,isLoading:i[t.key],singleSelect:t.singleSelect}):(t==null?void 0:t.key)===((E=e)==null?void 0:E.REGION)||(t==null?void 0:t.key)===((l=e)==null?void 0:l.MATERIAL_TYPE)?u(Ce,{param:t,dropDownData:{[t.key]:t.options},allDropDownData:g,selectedValues:o,inputState:v,handleSelectAll:F,handleSelectionChange:H,dropdownRef:y,errors:M,formatOptionLabel:X,handlePopoverOpen:W,handlePopoverClose:w,handleMouseEnterPopover:P,handleMouseLeavePopover:Y,isPopoverVisible:$,popoverId:m?"custom-popover":void 0,popoverAnchorEl:C,popoverRef:k,popoverContent:x,isLoading:i[t.key],singleSelect:t.singleSelect}):u(Ce,{param:t,dropDownData:b,allDropDownData:g,selectedValues:o,inputState:v,handleSelectAll:F,handleSelectionChange:H,dropdownRef:y,errors:M,formatOptionLabel:X,handlePopoverOpen:W,handlePopoverClose:w,handleMouseEnterPopover:P,handleMouseLeavePopover:Y,isPopoverVisible:$,popoverId:m?"custom-popover":void 0,popoverAnchorEl:C,popoverRef:k,popoverContent:x,isLoading:i[t.key],singleSelect:t.singleSelect})},ue=()=>{Oe(!0),_e(o),N({})},p=()=>Object.values(o).some(t=>Array.isArray(t)&&t.length>0),m=!!C;return U($e,{children:[U(He,{open:ne,TransitionComponent:ze,keepMounted:!0,onClose:I,maxWidth:"lg",fullWidth:!0,children:[U(Ie,{sx:{backgroundColor:"#e3f2fd",padding:"1rem 1.5rem",display:"flex",alignItems:"center"},children:[u(qe,{color:"primary",sx:{marginRight:"0.5rem"}}),U(We,{variant:"h6",component:"div",color:"primary",children:[de," Search Filter(s)"]})]}),u(we,{sx:{padding:"1.5rem 1.5rem 1rem"},children:u(Ie,{sx:{display:"grid",gridTemplateColumns:"repeat(3, 1fr)",gap:2},children:B==null?void 0:B.map(t=>u(Ie,{sx:{marginBottom:"1rem"},children:f(t)},t.key))})}),U(Pe,{sx:{padding:"0.5rem 1.5rem",display:"flex",justifyContent:"space-between"},children:[u("div",{}),U("div",{style:{display:"flex",gap:"8px"},children:[u(Me,{onClick:()=>{N({})},color:"warning",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Clear"}),u(Me,{onClick:()=>{N({}),I()},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),u(Me,{onClick:ue,variant:"contained",disabled:!p(),sx:{height:36,minWidth:"3.5rem",backgroundColor:"#3B30C8",textTransform:"none",fontWeight:500,"&:hover":{backgroundColor:"#2c278f"}},children:Le})]})]})]}),u(xe,{blurLoading:!1})]})},tt=({openSearch:ne,setOpenSearch:I,onSearchComplete:B})=>{const[de,g]=c.useState(!1),_e=ye(i=>i.AllDropDown.dropDown),[Le,o]=c.useState(""),[N,M]=c.useState(!1),[Ne,b]=c.useState(""),[d,se]=c.useState(""),[Oe,v]=c.useState(!1),V=()=>{v(!0)},Ee=()=>{v(!1)},fe=i=>{var C,le,x,Ae,$,D,k,H,W,w,P,Y,F,ce,X,j,re,ie,Te,Re,Se,J,K,Q,Z,ee;o((C=Xe)==null?void 0:C.REPORT_LOADING),g(!0);let T={orgDetails:[{material:((Ae=(x=i==null?void 0:i[(le=e)==null?void 0:le.MATERIAL_NUMBER])==null?void 0:x[0])==null?void 0:Ae.code)||"",whseNo:((k=(D=i==null?void 0:i[($=e)==null?void 0:$.WAREHOUSE])==null?void 0:D[0])==null?void 0:k.code)||"",storLoc:((w=(W=i==null?void 0:i[(H=e)==null?void 0:H.STORAGE_LOCATION])==null?void 0:W[0])==null?void 0:w.code)||"",salesOrg:((F=(Y=i==null?void 0:i[(P=e)==null?void 0:P.SALES_ORG])==null?void 0:Y[0])==null?void 0:F.code)||"",distrChan:((j=(X=i==null?void 0:i[(ce=e)==null?void 0:ce.DISTRIBUTION_CHANNEL])==null?void 0:X[0])==null?void 0:j.code)||"",valArea:((Te=(ie=i==null?void 0:i[(re=e)==null?void 0:re.PLANT])==null?void 0:ie[0])==null?void 0:Te.code)||"",plant:((J=(Se=i==null?void 0:i[(Re=e)==null?void 0:Re.PLANT])==null?void 0:Se[0])==null?void 0:J.code)||""}],region:((Z=(Q=i==null?void 0:i[(K=e)==null?void 0:K.REGION])==null?void 0:Q[0])==null?void 0:Z.code)||"",scenario:(ee=je)==null?void 0:ee.EXTEND_WITH_UPLOAD,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1"};const y=f=>{var m,te;if((f==null?void 0:f.size)==0){g(!1),o(""),M(!0),b((m=ve)==null?void 0:m.DATA_NOT_FOUND_FOR_SEARCH),se("danger"),V();return}I(!1);const ue=URL.createObjectURL(f),p=document.createElement("a");p.href=ue,p.setAttribute("download","SAP Excel Report.xlsx"),document.body.appendChild(p),p.click(),document.body.removeChild(p),URL.revokeObjectURL(ue),g(!1),o(""),M(!0),b((te=me)==null?void 0:te.SAP_DOWNLOAD_SUCCESS),se("success"),V()},z=()=>{var f;g(!1),M(!0),b((f=ve)==null?void 0:f.ERR_DOWNLOADING_EXCEL),se("danger"),V()},ge=`/${h}${G.EXCEL.DOWNLOAD_EXCEL_SAP_REPORT}`;a(ge,"postandgetblob",y,z,T)};return U($e,{children:[u(Je,{open:ne,onClose:()=>I(!1),parameters:Ye,onSearch:(i,T,y)=>fe(i),templateName:"Export",allDropDownData:_e,buttonName:"Export"}),u(xe,{blurLoading:de,loaderMessage:Le}),N&&u(Fe,{openSnackBar:Oe,alertMsg:Ne,alertType:d,handleSnackBarClose:Ee})]})};export{tt as E};
