import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Tag,
  Typography,
  Toolt<PERSON>,
  Popconfirm,
  Divider
} from 'antd';
import {
  DeleteOutlined,
  ArrowRightOutlined,
  DragOutlined,
  EditOutlined
} from '@ant-design/icons';
import { colors } from '@constant/colors';
import { WORKFLOW_DATA_CONSTANTS } from '@constant/enum';
import { useTheme } from '@mui/material';

const { Text } = Typography;

const WorkflowDisplay = ({
  workflowData,
  selectedCard,
  handleCardClick,
  mode,
  handleEditTask,
  setCardToMove,
  setIsMoveModalVisible,
  handleDeleteTask,
  getSortedLevels
}) => {
  const getCardGradient = (level) => {
    const gradients = [
      'linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%)',
      'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
      'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)',
      'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)',
      'linear-gradient(135deg, #e0f2f1 0%, #b2dfdb 100%)',
    ];

    if (level === -1 || level === "-1") {
      return 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)';
    }

    const numericLevel = parseInt(level, 10);
    const colorIndex = numericLevel % gradients.length;

    return gradients[colorIndex] || 'linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%)';
  };

  const sortedLevels = getSortedLevels();
  const theme = useTheme()

  return (
    <div style={{
      width: '100%',
      minHeight: '400px',
      maxHeight: '80vh',
      height: 'auto',
      background: 'white',
      borderRadius: '10px',
      boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
      border: `1px solid ${theme.palette.primary.main}60`,
      padding: '15px',
      position: 'relative',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header Section */}
      <div style={{
        flexShrink: 0
      }}>
        <div style={{
          fontSize: '17px',
          fontWeight: '600',
          color: theme.palette.primary.main,
          textAlign: 'left'
        }}>
          Workflow Design
        </div>
        <Divider style={{ opacity: 1.5, marginTop: "10px", marginBottom: "10px" }} />
      </div>

      {/* Scrollable Content Section */}
      <div style={{
        flex: 1,
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        gap: '16px',
        overflowX: 'auto',
        overflowY: 'auto',
        padding: '10px 0',
        scrollbarWidth: 'thin',
        scrollbarColor: '#d1d5db #f3f4f6',
      }}>
        {sortedLevels?.map((level, levelIndex) => (
          <React.Fragment key={level}>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              minWidth: '200px',
              flexShrink: 0,
              height: 'fit-content'
            }}>
              <div style={{ marginBottom: 10, textAlign: 'center' }}>
                <Tag
                  color={level === -1 ? 'blue' : level === 0 ? 'red' : 'green'}
                  style={{
                    fontSize: '10px',
                    padding: '2px 8px',
                    fontWeight: 'bold'
                  }}
                >
                  {level === -1 ? 'REQUESTOR' : level === 0 ? 'FINAL' : `LEVEL ${level}`}
                </Tag>
                <div style={{
                    fontSize: '13px',
                    color: '#666',
                    marginTop: 2,
                    fontWeight: '500',
                    marginLeft: "-7.5px"
                }}>
                  {workflowData[level]?.levelName}
                </div>
                <div style={{
                  fontSize: '11px',
                  color: '#666',
                  marginTop: 2,
                  fontWeight: '500',
                  marginLeft: "-7.5px"
                }}>
                  No. of Tasks: {workflowData[level]?.tasks?.length || 0}
                </div>
              </div>

              <div style={{
                display: 'flex',
                flexDirection: 'column',
                gap: '10px',
                width: '100%',
                flex: 1,
                overflowY: 'visible',
                overflowX: 'hidden',
                paddingLeft: '4px',
                paddingTop: '10px',
                paddingBottom: '10px',
                marginTop: "5px",
                minHeight: 'fit-content',
              }}>
                {workflowData[level]?.tasks.map((task) => {
                  const isSelected = selectedCard?.id === task.id;

                  return (
                    <Card
                      key={task.id}
                      size="small"
                      hoverable
                      onClick={() => handleCardClick(task)}
                      style={{
                        width: '100%',
                        minHeight: '100px',
                        cursor: 'pointer',
                        border: isSelected ? `2px solid #1890ff` : `none`,
                        background: getCardGradient(level),
                        borderRadius: '12px',
                        transition: 'all 0.2s ease',
                        position: 'relative',
                        boxShadow: isSelected
                          ? '0 4px 10px rgba(24, 144, 255, 0.3), 0 2px 2px rgba(0,0,0,0.1)'
                          : '0 2px 5px rgba(0,0,0,0.1), 0 2px 2px rgba(0,0,0,0.05)',
                        transform: isSelected ? 'translateY(-1px)' : 'translateY(0)',
                        marginBottom: "20px"
                      }}
                      bodyStyle={{
                        padding: '10px',
                        background: 'transparent'
                      }}
                      onMouseEnter={(e) => {
                        if (!task.isFixed) {
                          const hoverButtons = e.currentTarget.querySelector('.hover-buttons');
                          if (hoverButtons) {
                            hoverButtons.style.display = 'flex';
                          }
                        }
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        const hoverButtons = e.currentTarget.querySelector('.hover-buttons');
                        if (hoverButtons) {
                          hoverButtons.style.display = 'none';
                        }
                        if (!isSelected) {
                          e.currentTarget.style.transform = 'translateY(0)';
                        }
                      }}
                    >
                      <div style={{
                        position: 'absolute',
                        top: 0,
                        right: 0,
                        width: '24px',
                        height: '24px',
                        background: 'linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%)',
                        borderTopRightRadius: '12px',
                        borderBottomLeftRadius: '12px',
                      }} />

                      {mode !== "view" && (
                        <div
                          className="hover-buttons"
                          style={{
                            position: 'absolute',
                            top: '6px',
                            right: '6px',
                            display: 'none',
                            gap: '3px',
                            zIndex: 10
                          }}
                        >
                          {/* Always show Edit icon if not fixed */}
                          {!task.isFixed && (
                            <Tooltip title="Edit Task">
                              <Button
                                size="small"
                                shape="circle"
                                icon={<EditOutlined style={{ fontSize: '13px' }} />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleEditTask(task);
                                }}
                                style={{
                                  color: 'blue',
                                  backgroundColor: 'rgba(255,255,255,0.9)',
                                  border: 'none',
                                  boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
                                  width: '23px',
                                  height: '23px',
                                  minWidth: '20px'
                                }}
                              />
                            </Tooltip>
                          )}

                          {/* Show Move and Delete only if not final-task */}
                          {(!task.isFixed && level !== 0) && (
                            <>
                              <Tooltip title="Move Task">
                                <Button
                                  size="small"
                                  shape="circle"
                                  icon={<DragOutlined style={{ fontSize: '13px' }} />}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setCardToMove(task);
                                    setIsMoveModalVisible(true);
                                  }}
                                  style={{
                                    color: '#722ed1',
                                    backgroundColor: 'rgba(255,255,255,0.9)',
                                    border: 'none',
                                    boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
                                    width: '23px',
                                    height: '23px',
                                    minWidth: '20px'
                                  }}
                                />
                              </Tooltip>
                              <Tooltip title="Delete Task">
                                <Popconfirm
                                  title="Delete Task"
                                  description= {WORKFLOW_DATA_CONSTANTS.POPCONFIRM_DESC}
                                  onConfirm={(e) => {
                                    e?.stopPropagation();
                                    handleDeleteTask(level, task.id);
                                  }}
                                  okText="Delete"
                                  cancelText="Cancel"
                                  okType="danger"
                                >
                                  <Button
                                    size="small"
                                    shape="circle"
                                    icon={<DeleteOutlined style={{ fontSize: '13px' }} />}
                                    onClick={(e) => e.stopPropagation()}
                                    danger
                                    style={{
                                      backgroundColor: 'rgba(255,255,255,0.9)',
                                      border: 'none',
                                      boxShadow: '0 1px 4px rgba(0,0,0,0.15)',
                                      width: '23px',
                                      height: '23px',
                                      minWidth: '20px'
                                    }}
                                  />
                                </Popconfirm>
                              </Tooltip>
                            </>
                          )}
                        </div>
                      )}

                      {/* Task Content */}
                      <div style={{ position: 'relative', zIndex: 1 }}>
                        {/* Task Name - Enhanced Typography */}
                        <div style={{
                          display: 'flex',
                          alignItems: 'flex-start',
                          justifyContent: 'space-between',
                          marginBottom: 8
                        }}>
                          <Text style={{
                            fontSize: '12px',
                            fontWeight: '600',
                            color: '#1a1a1a',
                            lineHeight: '1.2',
                            letterSpacing: '-0.01em',
                            textShadow: '0 1px 1px rgba(0,0,0,0.1)',
                            flex: 1,
                            marginRight: '6px'
                          }}>
                            {task.workflowTaskName}
                          </Text>
                        </div>

                        {/* Approver Group - Prominently displayed */}
                        <div style={{
                          fontSize: '10px',
                          fontWeight: '500',
                          color: '#2c3e50',
                          marginBottom: 6,
                          padding: '4px 8px',
                          backgroundColor: 'rgba(255,255,255,0.7)',
                          borderRadius: '6px',
                          border: '1px solid rgba(255,255,255,0.8)',
                          textAlign: 'center',
                          boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
                          marginTop: "12px"
                        }}>
                          {task.workflowGroup}
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>

            {/* Arrow between levels */}
            {levelIndex < sortedLevels.length - 1 && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                height: 'auto',
                paddingTop: "115px",
                flexShrink: 0,
                justifyContent: 'center'
              }}>
                <ArrowRightOutlined
                  style={{
                    fontSize: '18px',
                    color: '#1890ff',
                    padding: '8px',
                    backgroundColor: 'rgba(255,255,255,0.9)',
                    borderRadius: '50%',
                    boxShadow: '0 1px 4px rgba(24, 144, 255, 0.3)'
                  }}
                />
              </div>
            )}
          </React.Fragment>
        ))}
      </div>

      {/* Optional: Custom scrollbar styles for WebKit browsers */}
      <style jsx>{`
        div::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        div::-webkit-scrollbar-track {
          background: #f3f4f6;
          border-radius: 4px;
        }
        
        div::-webkit-scrollbar-thumb {
          background: #d1d5db;
          border-radius: 4px;
        }
        
        div::-webkit-scrollbar-thumb:hover {
          background: #9ca3af;
        }
      `}</style>
    </div>
  );
};

export default WorkflowDisplay;