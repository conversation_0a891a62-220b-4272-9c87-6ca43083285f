// EditBusinessHourModal.js
import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  TimePicker,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Divider,
  Switch
} from 'antd';
import { ClockCircleOutlined, PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { useTheme } from '@mui/material';

const { Title, Text } = Typography;
const { Option } = Select;

const EditBusinessHourModal = ({
  open,
  onClose,
  onSubmit,
  selectedRowData,
  regionOptions,
  timezoneOptions,
  fetchTimezonesByRegion
}) => {
  const theme = useTheme();
  const [form] = Form.useForm();
  const [editOffHoursRanges, setEditOffHoursRanges] = useState([]);
  const [tempOffHoursRange, setTempOffHoursRange] = useState({
    startTime: null,
    endTime: null,
  });
  const [isLoadingTimezones, setIsLoadingTimezones] = useState(false);

  const daysOfWeek = [
    "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"
  ];

  const handleSubmit = () => {
    form.validateFields().then(values => {
      onSubmit(values, editOffHoursRanges);
      handleReset();
    });
  };

  const handleReset = () => {
    form.resetFields();
    setEditOffHoursRanges([]);
    setTempOffHoursRange({ startTime: null, endTime: null });
  };

  const handleClose = () => {
    handleReset();
    onClose();
  };

  const handleRegionChange = async (region) => {
    form.setFieldsValue({ timeZone: undefined });
    setIsLoadingTimezones(true);
    await fetchTimezonesByRegion(region);
    setIsLoadingTimezones(false);
  };

  const addOffHoursRange = () => {
    if (tempOffHoursRange.startTime && tempOffHoursRange.endTime) {
      const newRange = { ...tempOffHoursRange };
      setEditOffHoursRanges(prev => [...prev, newRange]);
      setTempOffHoursRange({ startTime: null, endTime: null });
    }
  };

  const removeOffHoursRange = (index) => {
    setEditOffHoursRanges(prev => prev.filter((_, i) => i !== index));
  };

  useEffect(() => {
    if (open && selectedRowData) {
      // Pre-populate form with selected row data
      form.setFieldsValue({
        region: selectedRowData.region,
        timeZone: selectedRowData.timeZone,
        dayOfWeek: selectedRowData.dayOfWeek,
        workStartTime: selectedRowData.workStartTime ? dayjs(selectedRowData.workStartTime, "HH:mm") : null,
        workEndTime: selectedRowData.workEndTime ? dayjs(selectedRowData.workEndTime, "HH:mm") : null,
        isActive: selectedRowData.isActive !== undefined ? selectedRowData.isActive : true,
      });

      // Set off hours ranges
      setEditOffHoursRanges(
        selectedRowData.offHoursRanges?.map(range => ({
          startTime: dayjs(range.startTime, "HH:mm"),
          endTime: dayjs(range.endTime, "HH:mm"),
        })) || []
      );
    } else if (!open) {
      handleReset();
    }
  }, [open, selectedRowData, form]);

  return (
    <Modal
      title={
        <Title level={4} style={{ margin: 0 }}>
          Edit Business Hour Configuration
        </Title>
      }
      open={open}
      onOk={handleSubmit}
      onCancel={handleClose}
      width={550}
      style={{ marginTop: -30}}
      okText="Update Schedule"
      cancelText="Cancel"
      maskClosable={false}
      okButtonProps={{
        style: {
          backgroundColor: theme?.palette?.primary?.main,
        }
      }}
    >
      <Divider style={{ marginTop: 16, marginBottom: 12 }} />
      
      <Form 
        form={form} 
        layout="vertical" 
        requiredMark="optional"
        scrollToFirstError
      >
        {/* Location Information */}
        <div style={{ marginBottom: 5 }}>
          <Title level={5} style={{ marginBottom: 10, color: '#1890ff' }}>
            Location Information
          </Title>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={<Text strong>Region</Text>}
                name="region"
                rules={[{ required: true, message: "Please select a region!" }]}
              >
                <Select 
                  placeholder="Select region"
                  showSearch
                  onChange={handleRegionChange}
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {regionOptions.map(option => (
                    <Option key={option.value} value={option.value} label={`${option.value}-${option.label}`}>
                      {option.value} - {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<Text strong>Timezone</Text>}
                name="timeZone"
                rules={[{ required: true, message: "Please select a timezone!" }]}
              >
                <Select 
                  placeholder="Select timezone"
                  loading={isLoadingTimezones}
                  disabled={!form.getFieldValue('region')}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {timezoneOptions.map(option => (
                    <Option key={option.value} value={option.value} label={`${option.value}-${option.label}`}>
                      {option.value}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </div>

        {/* Schedule Information */}
        <div style={{ marginBottom: 5 }}>
          <Title level={5} style={{ marginBottom: 10, color: '#52c41a' }}>
            Schedule Information
          </Title>
          
          <Row gutter={16} align="bottom">
            <Col span={12}>
              <Form.Item
                label={<Text strong>Day of Week</Text>}
                name="dayOfWeek"
                rules={[{ required: true, message: "Please select a day!" }]}
                style={{ marginBottom: 16 }}
              >
                <Select 
                  placeholder="Select day of the week"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {daysOfWeek.map(day => (
                    <Option key={day} value={day}>
                      {day}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<Text strong>Active</Text>}
                name="isActive"
                valuePropName="checked"
                style={{ marginBottom: 16 }}
              >
                <Switch
                  checkedChildren="On" 
                  unCheckedChildren="Off"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={<Text strong>Business Start Time</Text>}
                name="workStartTime"
                rules={[{ required: true, message: "Please select start time!" }]}
              >
                <TimePicker 
                  format="HH:mm" 
                  placeholder="Start time" 
                  style={{ width: "100%" }}
                  prefix={<ClockCircleOutlined />}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label={<Text strong>Business End Time</Text>}
                name="workEndTime"
                rules={[{ required: true, message: "Please select end time!" }]}
              >
                <TimePicker 
                  format="HH:mm" 
                  placeholder="End time" 
                  style={{ width: "100%" }}
                  prefix={<ClockCircleOutlined />}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        {/* Off Hours Configuration */}
        <div>
          <Title level={5} style={{ marginBottom: 10, color: '#fa8c16' }}>
            Off Hours Configuration (Optional)
          </Title>
          
          <Space direction="vertical" style={{ width: "100%" }} size="middle">
            <Row gutter={8} align="middle">
              <Col span={8}>
                <TimePicker
                  format="HH:mm"
                  placeholder="Off start time"
                  value={tempOffHoursRange.startTime}
                  onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, startTime: time }))}
                  style={{ width: '100%' }}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={8}>
                <TimePicker
                  format="HH:mm"
                  placeholder="Off end time"
                  value={tempOffHoursRange.endTime}
                  onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, endTime: time }))}
                  style={{ width: '100%' }}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={addOffHoursRange}
                  disabled={!tempOffHoursRange.startTime || !tempOffHoursRange.endTime}
                  style={{ width: '100%' }}
                >
                  Add Range
                </Button>
              </Col>
            </Row>

            {editOffHoursRanges.length > 0 && (
              <div style={{ 
                background: '#fafafa', 
                padding: '12px', 
                borderRadius: '6px',
                border: '1px solid #d9d9d9'
              }}>
                <Text strong style={{ display: 'block', marginBottom: 8 }}>
                  Configured Off Hours Ranges:
                </Text>
                <Space wrap>
                  {editOffHoursRanges.map((range, index) => (
                    <Tag
                      key={index}
                      closable
                      onClose={() => removeOffHoursRange(index)}
                      color="orange"
                    >
                      {range.startTime?.format("HH:mm")} - {range.endTime?.format("HH:mm")}
                    </Tag>
                  ))}
                </Space>
              </div>
            )}

            <Text type="secondary" style={{ fontSize: '12px' }}>
              Off hours are periods within business hours when services are temporarily unavailable.
              You can add multiple off-hour ranges as needed.
            </Text>
          </Space>
        </div>
      </Form>
    </Modal>
  );
};

export default EditBusinessHourModal;