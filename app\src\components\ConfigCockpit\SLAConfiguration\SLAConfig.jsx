// Updated SLA Management Component with Holiday Management
import React, { useEffect, useState } from "react";
import { Typography, Grid, Stack, Paper, useTheme } from "@mui/material";
import { Row, Col, Button, Space } from "antd";
import { CalendarOutlined } from "@ant-design/icons";
import {
  outermostContainer,
  outermostContainer_Information,
  outerContainer_Information,
} from "../../common/commonStyles";
import { doAjax } from "../../common/fetchService";
import { destination_Admin } from "../../../destinationVariables";
import LeftPane from "./LeftPane";
import MiddlePane from "./MiddlePane";
import RightPane from "./RightPane";
import AddBusinessHourModal from "./AddBusinessHourModal";
import EditBusinessHourModal from "./EditBusinessHourModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import HolidayManagement from "./HolidayManagement";
import { useSnackbar } from "@hooks/useSnackbar";

const SLAConfig = () => {
  // Data states
  const [businessHoursData, setBusinessHoursData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [regionOptions, setRegionOptions] = useState([]);
  const [timezoneOptions, setTimezoneOptions] = useState([]);
  const [selectedRowData, setSelectedRowData] = useState(null);
  
  // Filter states
  const [selectedRegion, setSelectedRegion] = useState(null);
  const [selectedTimezone, setSelectedTimezone] = useState(null);
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isLeftPaneCollapsed, setIsLeftPaneCollapsed] = useState(false);
  const [isRightPaneCollapsed, setIsRightPaneCollapsed] = useState(false);
  
  // Modal states
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [holidayModalOpen, setHolidayModalOpen] = useState(false);

  const { showSnackbar } = useSnackbar()
  const theme = useTheme()

  const fetchBusinessHours = (region = null, timezone = null) => {
    setIsLoading(true);
    let url = `/${destination_Admin}/api/fetch-region-business-hours`;
    const payload = {
        region: region || null,
        timeZone: timezone || null,
    };
    
    const hSuccess = (res) => {
      let index = 1;
      const transformedData = res?.data?.flatMap(item =>
        item?.dayBusinessHours?.map(dayHour => {
          let offHours = [];
          if (dayHour?.offHoursRanges) {
            offHours = dayHour?.offHoursRanges.split(",").map(range => {
              const [startTime, endTime] = range.split("-");
              return { startTime, endTime };
            });
          }

          return {
            ...dayHour,
            RegionId: item?.RegionId,
            region: item.region,
            timeZone: item.timeZone,
            offHoursRanges: offHours,
            id: index++
          };
        })
      );

      setBusinessHoursData(transformedData || []);
      setFilteredData(transformedData || []);
      setIsLoading(false);
    };

    const hError = () => {
      setIsLoading(false);
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const fetchBusinessRegions = () => {
    const hSuccess = (res) => {
      setRegionOptions(res?.data?.map(region => ({ value: region?.code, label: region?.desc })) || []);
    };
    const hError = () => {};
    doAjax(`/${destination_Admin}/api/region-business-hours/countries`, "get", hSuccess, hError);
  };

  const fetchTimezonesByRegion = (region) => {
    const hSuccess = (res) => {
      setTimezoneOptions(res?.data?.map(tz => ({ value: tz?.code, label: tz?.code })) || []);
    };
    const hError = () => {
      setTimezoneOptions([]);
    };
    doAjax(`/${destination_Admin}/api/region-business-hours/${region}/timezones`, "get", hSuccess, hError);
  };

  // Filter handlers
  const handleRegionChange = (region) => {
    setSelectedRegion(region);
    setSelectedTimezone(null);
    if (region) {
      fetchTimezonesByRegion(region);
    } else {
      setTimezoneOptions([]);
    }
    fetchBusinessHours(region, null);
  };

  const handleTimezoneChange = (timezone) => {
    setSelectedTimezone(timezone);
    fetchBusinessHours(selectedRegion, timezone);
  };

  const clearFilters = () => {
    setSelectedRegion(null);
    setSelectedTimezone(null);
    setTimezoneOptions([]);
    fetchBusinessHours();
  };

  // CRUD operations
  const createBusinessHour = (values, offHoursRanges) => {
    const payload = {
        RegionId: null,
        region: values.region,
        timeZone: values.timeZone,
        dayBusinessHours: [
        {
            DayId: null,
            dayOfWeek: values.dayOfWeek,
            workStartTime: values.workStartTime ? values.workStartTime.format("HH:mm:ss") : null,
            workEndTime: values.workEndTime ? values.workEndTime.format("HH:mm:ss") : null,
            offHoursRanges: offHoursRanges
            ?.map(range => `${range?.startTime?.format("HH:mm")}-${range?.endTime?.format("HH:mm")}`)
            ?.join(","),
            isActive: values?.isActive,
        },
        ],
    };
    const hSuccess = () => {
      showSnackbar("Business Hour created successfully.", "success")
      fetchBusinessHours(selectedRegion, selectedTimezone);
      setAddModalOpen(false);
    };
    
    const hError = () => {
      showSnackbar("Failed to create Business Hour.", "success")
    };
    
    doAjax(`/${destination_Admin}/api/region-business-hours`, "post", hSuccess, hError, payload);
  };

  const updateBusinessHour = (values, offHoursRanges) => {
    const payload = {
        RegionId: selectedRowData.RegionId || null,
        region: values.region,
        timeZone: values.timeZone,
        dayBusinessHours: [
        {
            DayId: selectedRowData.DayId || null,
            dayOfWeek: values.dayOfWeek,
            workStartTime: values.workStartTime ? values.workStartTime.format("HH:mm:ss") : null,
            workEndTime: values.workEndTime ? values.workEndTime.format("HH:mm:ss") : null,
            offHoursRanges: offHoursRanges
            ?.map(range => `${range?.startTime?.format("HH:mm")}-${range?.endTime?.format("HH:mm")}`)
            ?.join(","),
            isActive: values?.isActive,
        },
        ],
    };

    const hSuccess = () => {
      showSnackbar("Business Hour updated successfully.", "success")
      fetchBusinessHours(selectedRegion, selectedTimezone);
      setEditModalOpen(false);
    };
    
    const hError = () => {
      showSnackbar("Failed to update Business Hour.", "error")
    };
    
    doAjax(`/${destination_Admin}/api/region-business-hours`, "post", hSuccess, hError, payload);
  };

  const deleteBusinessHour = () => {
    const hSuccess = () => {
      showSnackbar("Business Hour deleted successfully.", "success")
      fetchBusinessHours(selectedRegion, selectedTimezone);
      setDeleteModalOpen(false);
      setSelectedRowData(null);
    };
    
    const hError = () => {
      showSnackbar("Failed to delete Business Hour.", "error")
    };
    
    doAjax(`/${destination_Admin}/api/region-business-hours/day/${selectedRowData.DayId}`, "delete", hSuccess, hError);
  };

  // Modal handlers
  const handleRowSelect = (rowData) => {
    setSelectedRowData(rowData);
    if (isRightPaneCollapsed) {
      setIsRightPaneCollapsed(false);
    }
  };

  const handleEditRow = (rowData) => {
    setSelectedRowData(rowData);
    fetchTimezonesByRegion(rowData.region);
    setEditModalOpen(true);
  };

  const handleDeleteRow = (rowData) => {
    setSelectedRowData(rowData);
    setDeleteModalOpen(true);
  };

  useEffect(() => {
    fetchBusinessHours();
    fetchBusinessRegions();
  }, []);

  const getLeftPaneWidth = () => isLeftPaneCollapsed ? 1 : 6;
  const getMiddlePaneWidth = () => {
    if (isLeftPaneCollapsed && isRightPaneCollapsed) return 22;
    if (isLeftPaneCollapsed || isRightPaneCollapsed) return 17;
    return 12;
  };
  const getRightPaneWidth = () => isRightPaneCollapsed ? 1 : 6;

  return (
    <div id="printScreen" style={outermostContainer}>
      {/* Header */}
      <Stack spacing={1}>
        <Grid container sx={outermostContainer_Information}>
          <Grid item md={8} sx={outerContainer_Information}>
            <Typography variant="h3">
              <strong>SLA Management</strong>
            </Typography>
            <Typography variant="body2">
              This view displays the existing business hours configurations and allows you to manage them.
            </Typography>
          </Grid>
          <Grid item md={4} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
            <Space>
              <Button
                type="primary"
                icon={<CalendarOutlined />}
                size="large"
                onClick={() => setHolidayModalOpen(true)}
                style={{ backgroundColor: theme?.palette?.primary?.main}}
              >
                Manage Holidays
              </Button>
            </Space>
          </Grid>
        </Grid>

        {/* Three Pane Layout */}
        <Paper elevation={1} style={{ padding: '16px', minHeight: 'calc(100vh - 200px)' }}>
          <Row gutter={[16, 16]} style={{ height: '100%' }}>
            {/* Left Pane - Add Business Hours */}
            <Col span={getLeftPaneWidth()}>
              <LeftPane
                isCollapsed={isLeftPaneCollapsed}
                onToggleCollapse={() => setIsLeftPaneCollapsed(!isLeftPaneCollapsed)}
                regionOptions={regionOptions}
                timezoneOptions={timezoneOptions}
                selectedRegion={selectedRegion}
                selectedTimezone={selectedTimezone}
                onRegionChange={handleRegionChange}
                onTimezoneChange={handleTimezoneChange}
                onClearFilters={clearFilters}
                onOpenAddModal={() => setAddModalOpen(true)}
                fetchTimezonesByRegion={fetchTimezonesByRegion}
              />
            </Col>

            {/* Middle Pane - Data Table */}
            <Col span={getMiddlePaneWidth()}>
              <MiddlePane
                data={filteredData}
                isLoading={isLoading}
                onRowSelect={handleRowSelect}
                selectedRowId={selectedRowData?.id}
              />
            </Col>

            {/* Right Pane - Selected Row Details */}
            <Col span={getRightPaneWidth()}>
              <RightPane
                isCollapsed={isRightPaneCollapsed}
                onToggleCollapse={() => setIsRightPaneCollapsed(!isRightPaneCollapsed)}
                selectedRowData={selectedRowData}
                onEditRow={handleEditRow}
                onDeleteRow={handleDeleteRow}
              />
            </Col>
          </Row>
        </Paper>
      </Stack>

      {/* Business Hours Modals */}
      <AddBusinessHourModal
        open={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSubmit={createBusinessHour}
        regionOptions={regionOptions}
        timezoneOptions={timezoneOptions}
        fetchTimezonesByRegion={fetchTimezonesByRegion}
      />

      <EditBusinessHourModal
        open={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        onSubmit={updateBusinessHour}
        selectedRowData={selectedRowData}
        regionOptions={regionOptions}
        timezoneOptions={timezoneOptions}
        fetchTimezonesByRegion={fetchTimezonesByRegion}
      />

      <DeleteConfirmationModal
        open={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={deleteBusinessHour}
        selectedRowData={selectedRowData}
      />

      {/* Holiday Management Modal */}
      <HolidayManagement
        open={holidayModalOpen}
        onClose={() => setHolidayModalOpen(false)}
        regionOptions={regionOptions}
      />
    </div>
  );
};

export default SLAConfig;