import { useCallback } from 'react';
import { doAjax } from "@components/Common/fetchService";
import { useSnackbar } from "@hooks/useSnackbar";
import useLogger from "@hooks/useLogger";
import { ERROR_MESSAGES } from "@constant/enum";
import { END_POINTS } from "@constant/apiEndPoints";
import { destination_MaterialMgmt } from "../../../destinationVariables";

const useDuplicateMatNoCheck = (singlePayloadData, requestDetails) => {
  const { showSnackbar } = useSnackbar();
  const { customError } = useLogger();
  const checkDuplicateMatNo = useCallback((matNo, requestId = "", matDescription = "") => {
    return new Promise((resolve, reject) => {
      if (!matNo) {
        resolve(false);
        return;
      }

      const payload = [
        {
          materialNo: matNo,
          requestNo: requestId || requestDetails?.requestId,
          materialDesc: matDescription,
        },
      ];

      const successHandler = (data) => {
        if (data?.body?.totalDuplicatesFound > 0) {
          showSnackbar(data?.message, "error");
          resolve(true);
        } else {
          resolve(false);
        }
      };

      const errorHandler = (error) => {
        customError(error);
        resolve(false);
      };
      let materialCount = 0;
      let descriptionCount = 0;

      if (singlePayloadData) {
        Object.keys(singlePayloadData).forEach((key) => {
          if (key.includes("-") || /\d/.test(key)) {
            const itemData = singlePayloadData[key]?.headerData;
            
            if (itemData?.materialNumber === matNo) {
              materialCount++;
            }
            
            if (matDescription && itemData?.globalMaterialDescription === matDescription) {
              descriptionCount++;
            }
          }
        });
      }
      if (materialCount > 1) {
        showSnackbar(`${ERROR_MESSAGES.DUPLICATE_MATERIAL}${matNo}`, "error");
        resolve(true);
        return;
      }
      
      if (matDescription && descriptionCount > 1) {
        showSnackbar(`${ERROR_MESSAGES.DUPLICATE_MATERIAL_DESCRIPTION}${matDescription}`, "error");
        resolve(true);
        return;
      }
      doAjax(
        `/${destination_MaterialMgmt}${END_POINTS.MASS_ACTION?.MAT_NO_DUPLICATE_CHECK}`,
        "post",
        successHandler,
        errorHandler,
        payload
      );
    });
  }, [singlePayloadData, requestDetails]);

  return { checkDuplicateMatNo };
};

export default useDuplicateMatNoCheck;