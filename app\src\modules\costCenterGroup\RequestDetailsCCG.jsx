import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import ReusableHierarchyTree from "@components/MasterDataCockpit/Hierarchy/ReusableHIerarchyTree";
import ReusableBackDrop from "../../components/Common/ReusableBackDrop";
import BottomNavGlobal from "../../components/RequestBench/RequestPages/BottomNavGlobal";
import { Box, Grid, Typography } from "@mui/material";
import { colors } from "@constant/colors";

import { API_CODE, ENABLE_STATUSES, MODULE, MODULE_MAP, REQUEST_TYPE } from "@constant/enum";
import useButtonDTConfig from "@hooks/useButtonDTConfig";
import ReusableSnackBar from "@components/Common/ReusableSnackBar";


import ChangeWithUploadCCG from "./ChangeWithUploadCCG";
import { useRequestDetailsCCG } from "./hooks/useRequestDetailsCCG";
const RequestDetailsCCG = (props) => {
  const taskData = useSelector((state) => state.userManagement.taskData);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const RequestType = queryParams.get("RequestType");
 const reqBench = queryParams.get("reqBench");
  const requestStatus = useSelector((state) => state.hierarchyData.requestHeaderData?.RequestStatus);
  const disableCheck = (reqBench && !ENABLE_STATUSES.includes(requestStatus) || RequestType=== REQUEST_TYPE?.CHANGE_WITH_UPLOAD ) 
  const { getButtonsDisplayGlobal } = useButtonDTConfig();
  const {
    // State
    showTree,
    blurLoading,
    openSnackBar,
    alertMsg,
    alertType,

    // Redux data
    filteredButtons,
    requestorPayload,
    loadForFetching,
    initialNodeData,

    // Handlers
    handleButtonClick,
    handleSnackBarClose,
  } = useRequestDetailsCCG();
  useEffect(() => {
    if (taskData?.ATTRIBUTE_1 || RequestType) {
      getButtonsDisplayGlobal(
        "Hierarchy Node (Cost Center)",
        "MDG_DYN_BTN_DT",
        "v3"
      );
    }
  }, [taskData]);

  const renderValue = (value) => {
    if (
      Array.isArray(value) &&
      value.length === 1 &&
      typeof value[0] === "object" &&
      value[0].code &&
      value[0].desc
    ) {
      return (
        <>
          <Typography component="span" variant="body1">
            {value[0].code}
          </Typography>
        </>
      );
    }
    return typeof value === "string" ? value : JSON.stringify(value);
  };

    const RequestorPayloadDisplay = ({ requestorPayload, renderValue }) => {
      if (!requestorPayload || !Object.keys(requestorPayload).length) {
        return null;
      }
  
      return (
        <Box
          sx={{
            backgroundColor: colors.primary.whiteSmoke,
            px: 2,
            py: 1,
            borderBottom: `1px solid ${colors.primary.whiteSmoke}`,
            borderRadius: "5px",
            mb: 2,
          }}
        >
          <Box sx={{ display: "flex", flexWrap: "wrap", gap: 3 }}>
            {Object.keys(requestorPayload).map((key) => (
              <Box
                key={key}
                sx={{ display: "flex", alignItems: "center", gap: 1 }}
              >
                <Typography
                  variant="body1"
                  sx={{ fontWeight: 700, color: colors.primary.grey }}
                >
                  {key} :
                </Typography>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  {renderValue(requestorPayload[key])}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      );
    };

  return (
    <div>
     
      <Grid container sx={{ height: "80vh", overflow: "hidden" }}>
       
        <Grid
          item
          md={RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD ? 6 : 12}
          sx={{
            overflowY: "auto",
            height: "100%",
            p: 2,
            pr: 1,
            borderRight: "1px solid #e0e0e0",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
          }}
        >

           <RequestorPayloadDisplay 
          requestorPayload={requestorPayload}
          renderValue={renderValue}
        />
          {showTree && (
            <ReusableHierarchyTree
              initialRawTreeData={initialNodeData}
               editmode={ !disableCheck }
              object={"CCG"}
            moduleObject={MODULE_MAP?.CC}
            />
          )}
        </Grid>

        {RequestType === REQUEST_TYPE?.CHANGE_WITH_UPLOAD && (
          <Grid
            item
            md={6}
            sx={{
              overflowY: "auto",
              height: "100%",
              p: 2,
              pl: 1,
              scrollbarWidth: "none",
              "&::-webkit-scrollbar": {
                display: "none",
              },
            }}
          >
            <ChangeWithUploadCCG />
          </Grid>
        )}
      </Grid>
      <ReusableBackDrop blurLoading={loadForFetching || blurLoading} />
      <ReusableSnackBar
        openSnackBar={openSnackBar}
        alertMsg={alertMsg}
        handleSnackBarClose={handleSnackBarClose}
        alertType={alertType}
        // isLoading={isLoading}
      />
      <ToastContainer />
    </div>
  );
};

export default RequestDetailsCCG;
