import colorLib from "@kurkle/color";
import { destination_BankKey, destination_BOM, destination_CostCenter_Mass, destination_GeneralLedger_Mass, destination_InternalOrder, destination_MaterialMgmt, destination_ProfitCenter_Mass } from "../../../destinationVariables";
import { END_POINTS } from "../../../constant/apiEndPoints";
import { MODULE_MAP } from "@constant/enum";

// Define the module configuration map
export const MODULE_CONFIG = {
  [MODULE_MAP?.MAT]: {
    destination: destination_MaterialMgmt,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_MAT_MATERIAL_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v4",
    changeVersion: "v6",
  },
  [MODULE_MAP?.PC]: {
    destination: destination_ProfitCenter_Mass,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF_FMD || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_PC_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v2",
    changeVersion: "v6",
  },
  [MODULE_MAP?.CC]: {
    destination: destination_CostCenter_Mass,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF_FMD || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_CC_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v3",
    changeVersion: "v6",
  },
  [MODULE_MAP?.BK]: {
    destination: destination_BankKey,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_BK_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v2",
    changeVersion: "v6",
  },
  [MODULE_MAP?.GL]: {
    destination: destination_GeneralLedger_Mass,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF_FMD || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_GL_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v2",
    changeVersion: "v6",
  },
  [MODULE_MAP?.IO]: {
    destination: destination_InternalOrder,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF_FMD || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_INTORD_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v2",
    changeVersion: "v6",
  },
  [MODULE_MAP?.BOM]: {
    destination: destination_BOM,
    pdfEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS
        ?.GENERATE_SINGLE_CONSOLIDATED_MATERIAL_PDF || "",
    timelineEndpoint:
      END_POINTS?.PDF_GENERATOR_APIS?.GENERATE_TIMELINE_PDF_FMD || "",
    filePrefix: "objectNumber",
    defaultDtName: "MDG_INTORD_FIELD_CONFIG",
    changeDtName: "MDG_CHANGE_TEMPLATE_DT",
    defaultVersion: "v2",
    changeVersion: "v6",
  },
};

export const transparentize = (value, opacity) => {
  var alpha = opacity === undefined ? 0.5 : 1 - opacity;
  return colorLib(value).alpha(alpha).rgbString();
};

export const KPI_CARD_COLOR = {
  Create: transparentize("#4dc9f6", 0.7),
  Change: transparentize("#f6d55c", 0.7),
  Extend: transparentize("#537bc4", 0.7),
  "Create With Upload": transparentize("#00a950", 0.7),
  "Change With Upload": transparentize("#8549ba", 0.7),
  "Extend With Upload": transparentize("#ff6384", 0.7),
};

// Utility functions
export const normalizeRequestType = (type) => {
  if (type?.includes("Extend")) return "EXTEND";
  if (type?.includes("Change")) return "CHANGE";
  if (type?.includes("Create")) return "CREATE";
  return type?.toUpperCase() || "";
};

export const getStatusColor = (status) => {
  const colors = {
    Approved: "success",
    Pending: "warning",
    Rejected: "error",
  };
  return colors[status] || "default";
};

export const calculateProcessingTime = (createdOn, completedOn) => {
  if (!createdOn || !completedOn) return "N/A";
  const created = new Date(createdOn);
  const completed = new Date(completedOn);
  const diffTime = Math.abs(completed - created);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return `${diffDays} day${diffDays !== 1 ? "s" : ""}`;
};
