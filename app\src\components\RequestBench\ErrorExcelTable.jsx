import React, { forwardRef, useEffect, useImper<PERSON><PERSON><PERSON><PERSON>, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
  Chip,
  TablePagination,
  Box,
  Tooltip,
  Button
} from "@mui/material";
import ErrorOutlineIcon from "@mui/icons-material/ErrorOutline";
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import { useLocation } from "react-router-dom";
import { doAjax } from "@components/Common/fetchService";
import useLogger from "@hooks/useLogger";
import { saveExcel } from "../../functions"; 
import { END_POINTS } from "@constant/apiEndPoints";
import { API_CODE, ERROR_MESSAGES} from "@constant/enum";
import { filterNavigation } from "@helper/helper";
import { commonSearchBarClear } from "@app/commonSearchBarSlice";
import { useDispatch } from "react-redux";
import useLang from "@hooks/useLang";

const getStatusColor = (type) =>
  type.includes("Duplicate") ? "error" : "error";

const ErrorExcelTable = forwardRef(({ module, errorType},ref) => {
  const dispatch = useDispatch();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [errorData, setErrorData] = useState([]);
  const [filteredErrorData, setFilteredErrorData] = useState([]);
  const [loading, setLoading] = useState(false);
  const location = useLocation();
  const urlSearchParams = new URLSearchParams(location.search.split("?")[1]);
  const RequestID = urlSearchParams.get("RequestId");
  const [backendExcelErrorMessage, setBackendExcelErrorMessage] = useState("");
  const { customError } = useLogger();
  const { t } = useLang();

  const handleChangePage = (event, newPage) => setPage(newPage);
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const { destination } = filterNavigation(module);
  const paginatedData = filteredErrorData?.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  useEffect(() => {
    setFilteredErrorData(errorData);
  }, [errorData]);

  const handleExcelErrorHistory = () => {
    setLoading(true);
    const hSuccess = (data) => {
      if (data?.statusCode === API_CODE.STATUS_200) {
        setErrorData(data?.body);
        setFilteredErrorData(data?.body);
        onHandled()
      } else {
        setBackendExcelErrorMessage(data?.message);
      }
      setLoading(false);
    };
    const hError = (error) => {
      customError(error);
      setBackendExcelErrorMessage(error?.message);
      setLoading(false);
    };
    doAjax(
      `/${destination}/${END_POINTS.ERROR_HISTORY.EXCEL_ERROR_HISTORY}?requestId=${RequestID}`,
      "get",
      hSuccess,
      hError
    );
  };

  const handleSearchAction = (searchTerm) => {
    if (!searchTerm || searchTerm.trim() === "") {
      setFilteredErrorData(errorData);
      return;
    }

    const filtered = errorData.filter((item) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        item.sheetName?.toLowerCase().includes(searchLower) ||
        item.errorType?.toLowerCase().includes(searchLower) ||
        item.rowNumber?.toString().includes(searchLower) ||
        item.columnNumber?.toString().includes(searchLower) ||
        item.errorDetails?.toLowerCase().includes(searchLower)
      );
    });

    setFilteredErrorData(filtered);
    setPage(0); // Reset to first page when searching
  };

  const clearSearch = () => {
    setFilteredErrorData(errorData);
    setPage(0);
    dispatch(commonSearchBarClear({ module: "ErrorHistory" }));
  };

  // Excel export configuration
  const fieldConfig = [
    { label: "Sheet Name", key: "sheetName" },
    { label: "Error Type", key: "errorType" },
    { label: "Row Number", key: "rowNumber" },
    { label: "Column Number", key: "columnNumber" },
    { label: "Error Details", key: "errorDetails" }
  ];

  const functions_ExportAsExcel = {
    convertJsonToExcel: () => {
      let excelColumns = [];
      const columnsLocal = [
        {
          field: "requestId",
          headerName: `Request ID`,
        },
        ...fieldConfig
          .filter(({ label, key }) => label.trim() !== "-" && key.trim() !== "-")
          .map(({ label, key }) => ({
            field: key,
            headerName: label,
          })),
      ];

      columnsLocal.forEach((item) => {
        if (item.headerName.toLowerCase() !== "action" && !item.hide) {
          excelColumns.push({ header: item.headerName, key: item.field });
        }
      });

      saveExcel({
        fileName: `${module} Excel Error Log`,
        columns: excelColumns,
        rows: filteredErrorData.length > 0 ? filteredErrorData : errorData,
      });
    },
  };

  useImperativeHandle(ref, () => ({
    triggerExcelErrorAction: handleExcelErrorHistory,
    triggerExcelExportAction: functions_ExportAsExcel.convertJsonToExcel,
  }));

  return (
    <Box
      sx={{ mt: 2, display: "flex", flexDirection: "column", height: "100%" }}
    >
      {/* Table */}
      <TableContainer
        component={Paper}
        elevation={3}
        sx={{ borderRadius: 2, maxHeight: "56vh", overflow: "auto" }}
      >
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              {[
                "Sheet Name",
                "Error Type",
                "Row",
                "Column",
                "Error Details",
              ].map((header) => (
                <TableCell
                  key={header}
                  sx={{
                    minWidth: "150px",
                    fontWeight: "bold",
                    backgroundColor: "#f5f5f5",
                  }}
                >
                  {header}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography>{t("Loading...")}</Typography>
                </TableCell>
              </TableRow>
            ) : paginatedData?.length > 0 ? (
              paginatedData?.map((error, index) => (
                <TableRow
                  key={error.id || index}
                  hover
                  sx={{ '&:hover': { backgroundColor: '#f9f9f9' } }}
                >
                  <TableCell>{error.sheetName}</TableCell>
                  <TableCell>
                    <Chip
                      icon={
                        getStatusColor(error.errorType) === "error" ? (
                          <ErrorOutlineIcon
                            fontSize="small"
                            sx={{ color: "#dee3e2 !important" }}
                          />
                        ) : (
                          <CheckCircleOutlineIcon
                            fontSize="small"
                            sx={{ color: "#dee3e2 !important" }}
                          />
                        )
                      }
                      label={error.errorType}
                      color={getStatusColor(error.errorType)}
                      size="small"
                      sx={{
                        fontSize: "0.65rem",
                        fontWeight: 500,
                        height: 27,
                        borderRadius: "99px",
                        color: "#dee3e2 !important",
                        p: 0.3,
                      }}
                    />
                  </TableCell>
                  <TableCell>{error.rowNumber}</TableCell>
                  <TableCell>{error.columnNumber}</TableCell>
                  <TableCell>
                    <Tooltip title={error.errorDetails} arrow>
                      <Typography
                        variant="body2"
                        sx={{
                          flex: 1,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          display: "-webkit-box",
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: "vertical",
                          maxWidth: "300px",
                        }}
                      >
                        {error.errorDetails}
                      </Typography>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography variant="body1" sx={{ py: 3 }}>
                    {filteredErrorData.length === 0 && errorData.length > 0
                      ? t("No results found for your search")
                      : backendExcelErrorMessage || ERROR_MESSAGES.NO_ERROR_FOUND}
                  </Typography>
                  {filteredErrorData.length === 0 && errorData.length > 0 && (
                    <Button
                      variant="text"
                      onClick={clearSearch}
                      sx={{ mt: 1 }}
                    >
                      {t("Clear search to show all records")}
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        count={filteredErrorData?.length ?? 0}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        sx={{ mt: 1 }}
        showFirstButton
        showLastButton
      />
    </Box>
  );
});

export default ErrorExcelTable;
