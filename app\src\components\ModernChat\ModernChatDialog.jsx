import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, Box, Menu, MenuItem, Popover } from '@mui/material';
import { PersonAdd, Star, Settings } from '@mui/icons-material';
import { Client } from '@stomp/stompjs';
import  SockJS from 'sockjs-client';
import { useSelector } from 'react-redux';
import { CHAT_MESSAGES } from '@constant/enum';
import { baseUrl_Websocket } from '@data/baseUrl';
import { useSnackbar } from '@hooks/useSnackbar';
import useLogger from '@hooks/useLogger';
import { doAjax } from '@components/Common/fetchService';
import { destination_Websocket } from '../../destinationVariables';
import EmojiPicker from 'emoji-picker-react';
import ChatHeader from './ChatHeader';
import ChatSideNav from './ChatSideNav';
import ChatBody from './ChatBody';

const ModernChatDialog = ({ open, onClose }) => {
  const [currentUserId, setCurrentUserId] = useState(null);
  const [currentChatId, setCurrentChatId] = useState(null);
  const [messages, setMessages] = useState([]);
  const [users, setUsers] = useState([]);
  const [messageInput, setMessageInput] = useState('');
  const [chatUserEmail, setChatUserEmail] = useState('');
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [stompClient, setStompClient] = useState(null);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
   const [isLoadingChats, setIsLoadingChats] = useState(false);

  const [searchQuery, setSearchQuery] = useState('');
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [emojiAnchor, setEmojiAnchor] = useState(null);
  const [editingMessage, setEditingMessage] = useState(null);
  const [editText, setEditText] = useState('');
  const [messageMenuAnchor, setMessageMenuAnchor] = useState(null);
  const [selectedMessage, setSelectedMessage] = useState(null);

  // Search state
  const [messageSearchQuery, setMessageSearchQuery] = useState('');
  const [searchResultIndices, setSearchResultIndices] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [showMessageSearch, setShowMessageSearch] = useState(false);

  const { showSnackbar } = useSnackbar();
  const { customError } = useLogger();
  const loginEmail = useSelector((state) => state.userManagement.userData.emailId || '');
  const messageListRef = useRef(null);

  // Helper function to check if a message is only emoji
  const isOnlyEmoji = (text) => {
    if (!text || typeof text !== 'string') return false;
    // Remove all emoji characters and see if anything is left
    const emojiRegex = /[\u{1f300}-\u{1f5ff}\u{1f900}-\u{1f9ff}\u{1f600}-\u{1f64f}\u{1f680}-\u{1f6ff}\u{2600}-\u{26ff}\u{2700}-\u{27bf}\u{1f1e6}-\u{1f1ff}\u{1f191}-\u{1f251}\u{1f004}\u{1f0cf}\u{1f170}-\u{1f171}\u{1f17e}-\u{1f17f}\u{1f18e}\u{3030}\u{2b50}\u{2b55}\u{2934}-\u{2935}\u{2b05}-\u{2b07}\u{2b1b}-\u{2b1c}\u{3297}\u{3299}\u{303d}\u{00a9}\u{00ae}\u{2122}\u{23f3}\u{24c2}\u{23e9}-\u{23ef}\u{25b6}\u{23f8}-\u{23fa}]/gu;
    const textWithoutEmoji = text.replace(emojiRegex, '').trim();
    return textWithoutEmoji.length === 0 && text.trim().length > 0; 
  };

  useEffect(() => {
    if (loginEmail) loginUser();
  }, [loginEmail]);

  useEffect(() => {
    if (currentUser?.id) {
      loadUsers(loginEmail);
    }
  }, [currentUser]);

  const connectWebSocket = (userId) => {
    setConnectionStatus('connecting');

    if (stompClient?.connected) {
      stompClient.deactivate();
    }

    const client = new Client ({
      webSocketFactory: () => new SockJS(`${baseUrl_Websocket}/ws`),
      reconnectDelay: 5000,
      onConnect: () => {
        setConnectionStatus('connected');
        loadUsers(userId);
      },
      onDisconnect: () => setConnectionStatus('disconnected'),
      onStompError: () => setConnectionStatus('disconnected'),
      onWebSocketError: () => setConnectionStatus('disconnected'),
    });

    client.activate();
    setStompClient(client);
  };

  const subscribeToChat = (chatId) => {
    if (currentSubscription) {
      currentSubscription.unsubscribe();
    }

    const topic = `/topic/chat/${chatId}/messages`;
    const subscription = stompClient.subscribe(topic, (message) => {
      try {
        const msg = JSON.parse(message.body);
        if (msg.senderId !== currentUserId?.id) {
          displayMessage(msg, false);
        }
      } catch (error) {
        customError('Error parsing message:', error);
      }
    });
    setCurrentSubscription(subscription);
  };

  const loginUser = async () => {
    if (!loginEmail.trim()) return showSnackbar(CHAT_MESSAGES.EMAIL, "warning");
    try {
      const response = await fetch(`${baseUrl_Websocket}/api/users/${loginEmail}`);
      if (!response.ok) throw new Error(`${CHAT_MESSAGES.NO_USER}`);
      const user = await response.json();
      setCurrentUser(user?.data || {});
      connectWebSocket(loginEmail);
    } catch (error) {
      customError(`${CHAT_MESSAGES.ERROR_LOGGING}`, error);
      showSnackbar(`${CHAT_MESSAGES.ERROR_LOGGING}` + error.message, "warning");
    }
  };

  const loadUsers = async (userId = "") => {
     setIsLoadingChats(true);
    if (!userId) return;

    const hSuccess = (data) => {
       setIsLoadingChats(false);
      setUsers(data?.data || []);
    };
    const hError = (error) => {
      setIsLoadingChats(false);
    };
    doAjax(`/${destination_Websocket}/api/chats/user/${currentUser.id}/group`, "get", hSuccess, hError);
  };

  const startChat = async (userData) => {
    const endPoint = (userData.type === "GROUP")
      ? `/${destination_Websocket}/api/chats/group?chatId=${userData.id}&userId=${currentUser.id}`
      : `/${destination_Websocket}/api/chats/individual?user1Id=${currentUser?.id}&user2Id=${userData.id}`;

    const hSuccess = (data) => {
      setCurrentChatId(data?.data?.id);
      setChatUserEmail(userData.name);
      setMessages([]);
      if (stompClient?.connected) {
        subscribeToChat(data?.data?.id);
      }
      setMessages(data?.data?.messages || []);
      loadMessages(data?.data?.id);
      setMessageSearchQuery('');
      setSearchResultIndices([]);
      setCurrentSearchIndex(-1);
      setShowMessageSearch(false);
    };
    const hError = (error) => {
      customError(CHAT_MESSAGES?.ERROR_CHAT, error);
      showSnackbar(CHAT_MESSAGES?.ERROR_CHAT + error.message, "error");
    };
    doAjax(endPoint, "get", hSuccess, hError);
  };

  const loadMessages = async (chatId) => {
    try {
      const response = await fetch(`${baseUrl_Websocket}/api/messages/${chatId}`);
      const msgs = await response.json();
      setMessages(msgs);
    } catch (error) {
      customError(CHAT_MESSAGES.ERROR, error);
    }
  };

  const displayMessage = (message, isLocal) => {
    const newMessage = {
      ...message,
      timestamp: isLocal ? new Date().toISOString() : message.timestamp
    };
    setMessages(prev => [...prev, newMessage]);
  };

  // Modified sendMessage function to accept optional content parameter
  const sendMessage = (content = null) => {
    const messageContent = content || messageInput.trim();
    if (!messageContent || !currentChatId) return;

    const messagingDto = {
      senderId: currentUser?.id,
      content: messageContent,
      chatId: currentChatId,
      senderEmail: currentUser?.email,
      receiverEmail: null,
      receiverId: null,
      isEmojiOnly: isOnlyEmoji(messageContent) // Mark emoji-only messages
    };

    // Only clear input if we're sending the typed message (not emoji)
    if (!content) {
      setMessageInput("");
    }

    if (stompClient?.connected) {
      stompClient.publish({
        destination: `/app/chat/${currentChatId}/send`,
        body: JSON.stringify(messagingDto),
      });
    } else {
      showSnackbar(CHAT_MESSAGES.WEBSOCKET_DISCONNECTED, "error");
    }
  };

  // New function to handle emoji sending
  const sendEmoji = (emojiData) => {
    if (!currentChatId) return;
    
    // Send emoji directly as a message
    sendMessage(emojiData.emoji);
    
    // Close emoji picker
    setEmojiAnchor(null);
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#6BB700';
      case 'connecting': return '#FF8C00';
      default: return '#FF4444';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected';
      case 'connecting': return 'Connecting...';
      default: return 'Disconnected';
    }
  };


 

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="xl"
      fullWidth
      PaperProps={{
        sx: {
          height: '95vh',
          maxHeight: '95vh',
          borderRadius: '8px',
          overflow: 'hidden'
        }
      }}
    >
      <ChatHeader
        connectionStatus={connectionStatus}
        getStatusColor={getStatusColor}
        getStatusText={getStatusText}
        menuAnchor={menuAnchor}
        setMenuAnchor={setMenuAnchor}
        onClose={onClose}
      />

      <DialogContent sx={{ p: 0, height: 'calc(100% - 56px)', display: 'flex' }}>
        <ChatSideNav
          users={users}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          currentUser={currentUser}
          currentChatId={currentChatId}
          startChat={startChat}
          getStatusColor={getStatusColor}
          isLoading={isLoadingChats} 
        />

        <ChatBody
          currentChatId={currentChatId}
          chatUserEmail={chatUserEmail}
          messages={messages}
          setMessages={setMessages}
          messageInput={messageInput}
          setMessageInput={setMessageInput}
          sendMessage={sendMessage}
          currentUserId={currentUser}
          messageListRef={messageListRef}
          editingMessage={editingMessage}
          setEditingMessage={setEditingMessage}
          editText={editText}
          setEditText={setEditText}
          messageMenuAnchor={messageMenuAnchor}
          setMessageMenuAnchor={setMessageMenuAnchor}
          selectedMessage={selectedMessage}
          setSelectedMessage={setSelectedMessage}
          messageSearchQuery={messageSearchQuery}
          setMessageSearchQuery={setMessageSearchQuery}
          searchResultIndices={searchResultIndices}
          setSearchResultIndices={setSearchResultIndices}
          currentSearchIndex={currentSearchIndex}
          setCurrentSearchIndex={setCurrentSearchIndex}
          showMessageSearch={showMessageSearch}
          setShowMessageSearch={setShowMessageSearch}
          emojiAnchor={emojiAnchor}
          setEmojiAnchor={setEmojiAnchor}
          showSnackbar={showSnackbar}
          isOnlyEmoji={isOnlyEmoji} 
        />
      </DialogContent>
      <Popover
        open={Boolean(emojiAnchor)}
        anchorEl={emojiAnchor}
        onClose={() => setEmojiAnchor(null)}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'bottom', horizontal: 'left' }}
        PaperProps={{
          sx: {
            maxWidth: 350,
            maxHeight: 400,
            overflow: 'auto'
          }
        }}
      >
        <EmojiPicker
          width={320}
          height={300}
          searchDisabled={false}
          skinTonesDisabled={false}
          onEmojiClick={sendEmoji} // Changed from adding to input to sending directly
        />
      </Popover>

      {/* Settings Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
        PaperProps={{
          sx: {
            minWidth: 200,
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            border: '1px solid #e1dfdd'
          }
        }}
      >
        <MenuItem onClick={() => setMenuAnchor(null)} sx={{ py: 1 }}>
          <PersonAdd sx={{ mr: 2, fontSize: 20 }} />
          Add People
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)} sx={{ py: 1 }}>
          <Star sx={{ mr: 2, fontSize: 20 }} />
          Favorites
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)} sx={{ py: 1 }}>
          <Settings sx={{ mr: 2, fontSize: 20 }} />
          Settings
        </MenuItem> 
      </Menu>
    </Dialog>
  );
};

export default ModernChatDialog;