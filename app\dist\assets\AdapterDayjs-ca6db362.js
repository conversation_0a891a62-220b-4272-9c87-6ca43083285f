import{r as c,j as l,dj as y,d as T,bu as r,ef as M}from"./index-226a1e75.js";import{w as g,a as p}from"./advancedFormat-23da442e.js";import{c as D}from"./customParseFormat-f5b19256.js";import{l as O,i as j}from"./isBetween-fc08a3a5.js";const C=({fallback:u="--",variant:h="text",delay:d=3e3,sx:t={fontSize:"1rem"}})=>{const[e,s]=c.useState(!0);return c.useEffect(()=>{const n=setTimeout(()=>{s(!1)},d);return()=>clearTimeout(n)},[d]),e?l(y,{variant:h,sx:t}):l(T,{component:"span",sx:t,children:u})};r.extend(O);r.extend(g);r.extend(j);r.extend(p);const k={YY:"year",YYYY:{sectionType:"year",contentType:"digit",maxLength:4},M:{sectionType:"month",contentType:"digit",maxLength:2},MM:"month",MMM:{sectionType:"month",contentType:"letter"},MMMM:{sectionType:"month",contentType:"letter"},D:{sectionType:"day",contentType:"digit",maxLength:2},DD:"day",Do:{sectionType:"day",contentType:"digit-with-letter"},d:{sectionType:"weekDay",contentType:"digit",maxLength:2},dd:{sectionType:"weekDay",contentType:"letter"},ddd:{sectionType:"weekDay",contentType:"letter"},dddd:{sectionType:"weekDay",contentType:"letter"},A:"meridiem",a:"meridiem",H:{sectionType:"hours",contentType:"digit",maxLength:2},HH:"hours",h:{sectionType:"hours",contentType:"digit",maxLength:2},hh:"hours",m:{sectionType:"minutes",contentType:"digit",maxLength:2},mm:"minutes",s:{sectionType:"seconds",contentType:"digit",maxLength:2},ss:"seconds"},Y={year:"YYYY",month:"MMMM",monthShort:"MMM",dayOfMonth:"D",dayOfMonthFull:"Do",weekday:"dddd",weekdayShort:"dd",hours24h:"HH",hours12h:"hh",meridiem:"A",minutes:"mm",seconds:"ss",fullDate:"ll",keyboardDate:"L",shortDate:"MMM D",normalDate:"D MMMM",normalDateWithWeekday:"ddd, MMM D",fullTime:"LT",fullTime12h:"hh:mm A",fullTime24h:"HH:mm",keyboardDateTime:"L LT",keyboardDateTime12h:"L hh:mm A",keyboardDateTime24h:"L HH:mm"},f=["Missing UTC plugin","To be able to use UTC or timezones, you have to enable the `utc` plugin","Find more information on https://mui.com/x/react-date-pickers/timezone/#day-js-and-utc"].join(`
`),m=["Missing timezone plugin","To be able to use timezones, you have to enable both the `utc` and the `timezone` plugin","Find more information on https://mui.com/x/react-date-pickers/timezone/#day-js-and-timezone"].join(`
`),S=(u,h)=>h?(...d)=>u(...d).locale(h):u;class U{constructor({locale:h,formats:d}={}){this.isMUIAdapter=!0,this.isTimezoneCompatible=!0,this.lib="dayjs",this.dayjs=void 0,this.locale=void 0,this.formats=void 0,this.escapedCharacters={start:"[",end:"]"},this.formatTokenMap=k,this.setLocaleToValue=t=>{const e=this.getCurrentLocaleCode();return e===t.locale()?t:t.locale(e)},this.hasUTCPlugin=()=>typeof r.utc<"u",this.hasTimezonePlugin=()=>typeof r.tz<"u",this.isSame=(t,e,s)=>{const n=this.setTimezone(e,this.getTimezone(t));return t.format(s)===n.format(s)},this.cleanTimezone=t=>{switch(t){case"default":return;case"system":return r.tz.guess();default:return t}},this.createSystemDate=t=>{if(this.hasUTCPlugin()&&this.hasTimezonePlugin()){const e=r.tz.guess();return e!=="UTC"?r.tz(t,e):r(t)}return r(t)},this.createUTCDate=t=>{if(!this.hasUTCPlugin())throw new Error(f);return r.utc(t)},this.createTZDate=(t,e)=>{if(!this.hasUTCPlugin())throw new Error(f);if(!this.hasTimezonePlugin())throw new Error(m);const s=t!==void 0&&!t.endsWith("Z");return r(t).tz(this.cleanTimezone(e),s)},this.getLocaleFormats=()=>{const t=r.Ls,e=this.locale||"en";let s=t[e];return s===void 0&&(s=t.en),s.formats},this.adjustOffset=t=>{if(!this.hasTimezonePlugin())return t;const e=this.getTimezone(t);if(e!=="UTC"){const s=t.tz(this.cleanTimezone(e),!0);if(s.$offset===(t.$offset??0))return t;t.$offset=s.$offset}return t},this.date=(t,e="default")=>{if(t===null)return null;let s;return e==="UTC"?s=this.createUTCDate(t):e==="system"||e==="default"&&!this.hasTimezonePlugin()?s=this.createSystemDate(t):s=this.createTZDate(t,e),this.locale===void 0?s:s.locale(this.locale)},this.getInvalidDate=()=>r(new Date("Invalid date")),this.getTimezone=t=>{var e;if(this.hasTimezonePlugin()){const s=(e=t.$x)==null?void 0:e.$timezone;if(s)return s}return this.hasUTCPlugin()&&t.isUTC()?"UTC":"system"},this.setTimezone=(t,e)=>{if(this.getTimezone(t)===e)return t;if(e==="UTC"){if(!this.hasUTCPlugin())throw new Error(f);return t.utc()}if(e==="system")return t.local();if(!this.hasTimezonePlugin()){if(e==="default")return t;throw new Error(m)}return r.tz(t,this.cleanTimezone(e))},this.toJsDate=t=>t.toDate(),this.parse=(t,e)=>t===""?null:this.dayjs(t,e,this.locale,!0),this.getCurrentLocaleCode=()=>this.locale||"en",this.is12HourCycleInCurrentLocale=()=>/A|a/.test(this.getLocaleFormats().LT||""),this.expandFormat=t=>{const e=this.getLocaleFormats(),s=n=>n.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(a,i,o)=>i||o.slice(1));return t.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(n,a,i)=>{const o=i&&i.toUpperCase();return a||e[i]||s(e[o])})},this.isValid=t=>t==null?!1:t.isValid(),this.format=(t,e)=>this.formatByString(t,this.formats[e]),this.formatByString=(t,e)=>this.dayjs(t).format(e),this.formatNumber=t=>t,this.isEqual=(t,e)=>t===null&&e===null?!0:t===null||e===null?!1:t.toDate().getTime()===e.toDate().getTime(),this.isSameYear=(t,e)=>this.isSame(t,e,"YYYY"),this.isSameMonth=(t,e)=>this.isSame(t,e,"YYYY-MM"),this.isSameDay=(t,e)=>this.isSame(t,e,"YYYY-MM-DD"),this.isSameHour=(t,e)=>t.isSame(e,"hour"),this.isAfter=(t,e)=>t>e,this.isAfterYear=(t,e)=>this.hasUTCPlugin()?!this.isSameYear(t,e)&&t.utc()>e.utc():t.isAfter(e,"year"),this.isAfterDay=(t,e)=>this.hasUTCPlugin()?!this.isSameDay(t,e)&&t.utc()>e.utc():t.isAfter(e,"day"),this.isBefore=(t,e)=>t<e,this.isBeforeYear=(t,e)=>this.hasUTCPlugin()?!this.isSameYear(t,e)&&t.utc()<e.utc():t.isBefore(e,"year"),this.isBeforeDay=(t,e)=>this.hasUTCPlugin()?!this.isSameDay(t,e)&&t.utc()<e.utc():t.isBefore(e,"day"),this.isWithinRange=(t,[e,s])=>t>=e&&t<=s,this.startOfYear=t=>this.adjustOffset(t.startOf("year")),this.startOfMonth=t=>this.adjustOffset(t.startOf("month")),this.startOfWeek=t=>this.adjustOffset(this.setLocaleToValue(t).startOf("week")),this.startOfDay=t=>this.adjustOffset(t.startOf("day")),this.endOfYear=t=>this.adjustOffset(t.endOf("year")),this.endOfMonth=t=>this.adjustOffset(t.endOf("month")),this.endOfWeek=t=>this.adjustOffset(this.setLocaleToValue(t).endOf("week")),this.endOfDay=t=>this.adjustOffset(t.endOf("day")),this.addYears=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"year"):t.add(e,"year")),this.addMonths=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"month"):t.add(e,"month")),this.addWeeks=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"week"):t.add(e,"week")),this.addDays=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"day"):t.add(e,"day")),this.addHours=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"hour"):t.add(e,"hour")),this.addMinutes=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"minute"):t.add(e,"minute")),this.addSeconds=(t,e)=>this.adjustOffset(e<0?t.subtract(Math.abs(e),"second"):t.add(e,"second")),this.getYear=t=>t.year(),this.getMonth=t=>t.month(),this.getDate=t=>t.date(),this.getHours=t=>t.hour(),this.getMinutes=t=>t.minute(),this.getSeconds=t=>t.second(),this.getMilliseconds=t=>t.millisecond(),this.setYear=(t,e)=>this.adjustOffset(t.set("year",e)),this.setMonth=(t,e)=>this.adjustOffset(t.set("month",e)),this.setDate=(t,e)=>this.adjustOffset(t.set("date",e)),this.setHours=(t,e)=>this.adjustOffset(t.set("hour",e)),this.setMinutes=(t,e)=>this.adjustOffset(t.set("minute",e)),this.setSeconds=(t,e)=>this.adjustOffset(t.set("second",e)),this.setMilliseconds=(t,e)=>this.adjustOffset(t.set("millisecond",e)),this.getDaysInMonth=t=>t.daysInMonth(),this.getWeekArray=t=>{const e=this.startOfWeek(this.startOfMonth(t)),s=this.endOfWeek(this.endOfMonth(t));let n=0,a=e;const i=[];for(;a<s;){const o=Math.floor(n/7);i[o]=i[o]||[],i[o].push(a),a=this.addDays(a,1),n+=1}return i},this.getWeekNumber=t=>t.week(),this.getYearRange=([t,e])=>{const s=this.startOfYear(t),n=this.endOfYear(e),a=[];let i=s;for(;this.isBefore(i,n);)a.push(i),i=this.addYears(i,1);return a},this.dayjs=S(r,h),this.locale=h,this.formats=M({},Y,d),r.extend(D)}getDayOfWeek(h){return h.day()+1}}export{U as A,C as S};
