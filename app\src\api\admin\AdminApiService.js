import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {destination_Admin} from '../../destinationVariables'
import {getUrlByName} from "@utils/dynamicUrl"
export const adminApi = createApi({
  reducerPath: 'adminApi',
  baseQuery: fetchBaseQuery({
    baseUrl: getUrlByName(destination_Admin),
    prepareHeaders: (headers, { getState }) => {
      const { token, environment } = getState().applicationConfig || {};
      if (token && (environment === 'localhost' || environment === '127.0.0.1')) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set("Access-Control-Allow-Origin", "*");
      return headers;
    },
  }),
  endpoints: () => ({}),
});