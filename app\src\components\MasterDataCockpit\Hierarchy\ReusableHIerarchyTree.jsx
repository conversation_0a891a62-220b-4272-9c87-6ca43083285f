import  { useState, useMemo, useEffect } from "react";
import { Tree, Input, Button, Modal, Form, Space, Alert } from "antd";
import {
  DownOutlined,
  CloseOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
  SwapOutlined,
  DeleteOutlined,
  SearchOutlined,
  ExpandOutlined,
  CompressOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { setTreeData, updateToChangeLog } from "@app/hierarchyDataSlice";
import { MODULE_KEY_MAP } from "../../../constant/enum";
import { v4 as uuidv4 } from "uuid";
import useAddOperation from "../../../modules/modulesHooks/hierarchyHooks/useAddOperation";
import useMoveOperation from "../../../modules/modulesHooks/hierarchyHooks/useMoveOperation";
import useRemoveOperation from "../../../modules/modulesHooks/hierarchyHooks/useRemoveOperation";
import useDeleteOperation from "../../../modules/modulesHooks/hierarchyHooks/useDeleteOperation";
import { treeStyles, globalTreeCSS } from "../../../constant/treeStyle";
import { convertTreeData, getAllNodeKeys } from "../../../modules/modulesHooks/hierarchyHooks/treeUtils";

const { Search } = Input;
const { Item } = Form;

const ReusableHierarchyTree = ({ initialRawTreeData = [], editmode = false, object = "Tag",moduleObject="" }) => {
  const [searchValue, setSearchValue] = useState("");
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [isTagModalVisible, setIsTagModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [currentParent, setCurrentParent] = useState(null);
  const [currentTagParent, setCurrentTagParent] = useState(null);
  const [currentEditNode, setCurrentEditNode] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [originalParent, setOriginalParent] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [activeAction, setActiveAction] = useState("add");
  const [isAddingNode, setIsAddingNode] = useState(false);
  const [isAddingTag, setIsAddingTag] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);

  const [form] = Form.useForm();
  const [tagForm] = Form.useForm();
  const [editForm] = Form.useForm();

  const dispatch = useDispatch();
  const rawTreeData = useSelector((state) => state?.hierarchyData?.treeData);
  const requestorPayload = useSelector((state) => state.payload.requestorPayload);
  const reduxPayload = useSelector((state) => state.hierarchyData);
  const treeChanges = useSelector((state) => state.hierarchyData.TreeChanges);
  const userData = useSelector((state) => state.userManagement.userData);

  useEffect(() => {
    dispatch(setTreeData(initialRawTreeData));
  }, [initialRawTreeData, dispatch]);

  useEffect(() => {
    setExpandedKeys(getAllNodeKeys(rawTreeData));
  }, [rawTreeData]);

  const generateFullNodeList = (nodes) => {
    const list = [];
    const processNode = (node) => {
      list.push({ key: node?.id, label: node?.label, description: node?.description, isTag: false });
      if (node?.tags) {
        node?.tags.forEach((tag, index) => {
          list.push({
            key: `${node.id}-tag-${index}`,
            label: tag,
            description: `${node.label} Tag`,
            isTag: true,
            parentKey: node.id,
          });
        });
      }
      if (node?.child) node.child.forEach(processNode);
    };
    nodes.forEach(processNode);
    return list;
  };

  const fullNodeList = useMemo(() => generateFullNodeList(rawTreeData), [rawTreeData]);

  const addToChangeLog = (type, description) => {
    const id = uuidv4();
    const updatedBy = userData?.emailId || "";
    const updatedOn = `/Date(${new Date().getTime()})/` || "";
    dispatch(updateToChangeLog({ id, type, description, updatedBy, updatedOn }));
  };

  const { handleAddNode, handleEditDescription, handleAddTag } = useAddOperation({
    form, editForm, tagForm, rawTreeData, currentParent, currentEditNode, object, moduleObject, requestorPayload,
    reduxPayload, MODULE_KEY_MAP, currentTagParent, setIsTagModalVisible, setIsModalVisible,
    setIsEditModalVisible, setExpandedKeys, addToChangeLog, treeChanges,isAddingNode, setIsAddingNode, isAddingTag, setIsAddingTag, 
  isEditingDescription, setIsEditingDescription
  });

  const { handleSelectNode, handlePlaceNode, handleSelectTag, handlePlaceTag, handleCancelMoveNode, handleCancelMoveTag } = useMoveOperation({
    rawTreeData, treeChanges, selectedNode, setSelectedNode, setOriginalParent, addToChangeLog,
    setSelectedTag, selectedTag, object, originalParent,
  });

  const { handleRemoveTag } = useRemoveOperation({
    rawTreeData, treeChanges, addToChangeLog, object, setIsEditModalVisible, editForm,
  });

  const { handleDeleteNode } = useDeleteOperation({ rawTreeData, treeChanges, addToChangeLog });

  const handlers = {
    handleRemoveTag, handleSelectTag, handleSelectNode, handlePlaceNode, handlePlaceTag,
    handleDeleteNode, setCurrentParent, setIsModalVisible, setCurrentTagParent,
    setIsTagModalVisible, setCurrentEditNode, setIsEditModalVisible, editForm,
  };

  const onSearch = (value) => {
    setSearchValue(value);
    if (!value) {
      setExpandedKeys([]);
      return;
    }
    const matchedKeys = fullNodeList.filter((node) => {
      const matchesLabel = node.label.toLowerCase().includes(value.toLowerCase());
      const matchesDesc = node.description.toLowerCase().includes(value.toLowerCase());
      return matchesLabel || matchesDesc;
    }).map((node) => node.key);

    const parentKeys = new Set();
    const findParentKeys = (nodes, targetKey) => {
      for (const node of nodes) {
        if (node.id === targetKey || (targetKey.startsWith(node.id) && targetKey.includes("-tag-"))) {
          return true;
        }
        if (node.child && findParentKeys(node.child, targetKey)) {
          parentKeys.add(node.id);
          return true;
        }
      }
      return false;
    };

    matchedKeys.forEach((key) => {
      const nodeKey = key.includes("-tag-") ? key.split("-tag-")[0] : key;
      findParentKeys(rawTreeData, nodeKey);
    });

    setExpandedKeys([...matchedKeys, ...parentKeys]);
    setAutoExpandParent(true);
  };

  const treeData = useMemo(() =>
    convertTreeData(rawTreeData, null, {
      searchValue, expandedKeys, editmode, activeAction, selectedNode, selectedTag, object,moduleObject, handlers
    }),
    [rawTreeData, searchValue, selectedNode, activeAction, expandedKeys, editmode, selectedTag, object, handlers]
  );

  return (
    <div style={treeStyles.container}>
      <style>{globalTreeCSS}</style>

      <div style={treeStyles.actionButtonsContainer}>
        <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
          {editmode && (
            <Space>
              <Button
                type={activeAction === "add" ? "primary" : "default"}
                onClick={() => setActiveAction("add")}
                icon={<PlusCircleOutlined />}
                style={treeStyles.actionButton}
              >
                Add
              </Button>
              <Button
                type={activeAction === "move" ? "primary" : "default"}
                onClick={() => setActiveAction("move")}
                icon={<SwapOutlined />}
                style={treeStyles.actionButton}
              >
                Move
              </Button>
              <Button
                type={activeAction === "remove" ? "primary" : "default"}
                onClick={() => setActiveAction("remove")}
                icon={<MinusCircleOutlined />}
                style={treeStyles.actionButton}
              >
                Remove
              </Button>
              <Button
                type={activeAction === "delete" ? "primary" : "default"}
                onClick={() => setActiveAction("delete")}
                icon={<DeleteOutlined />}
                danger={activeAction === "delete"}
                style={treeStyles.actionButton}
              >
                Delete
              </Button>
            </Space>
          )}
        </div>
        <Space>
          <Button icon={<ExpandOutlined />} onClick={() => setExpandedKeys(getAllNodeKeys(rawTreeData))} style={treeStyles.actionButton}>
            Expand All
          </Button>
          <Button icon={<CompressOutlined />} onClick={() => setExpandedKeys([])} style={treeStyles.actionButton}>
            Collapse All
          </Button>
        </Space>
      </div>

      {(selectedNode || selectedTag) && (
        <Alert
          message={
            <div>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              {selectedNode
                ? `Moving node "${selectedNode.label}" - Click on a parent node to place it`
                : `Moving tag "${selectedTag.tag}" - Click on a parent node to place it`}
            </div>
          }
          type="warning"
          showIcon={false}
          style={treeStyles.moveAlert}
          action={
            <Button
              size="small"
              type="text"
              onClick={() => selectedNode ? handleCancelMoveNode() : handleCancelMoveTag()}
              icon={<CloseOutlined />}
            >
              Cancel
            </Button>
          }
        />
      )}

      <div style={treeStyles.searchContainer}>
        <Search
          placeholder={`Search Node, Descriptions or ${moduleObject}...`}
          allowClear
          onSearch={onSearch}
          onChange={(e) => onSearch(e.target.value)}
          style={{ flex: 1 }}
          prefix={<SearchOutlined />}
        />
      </div>

      <div className="hierarchy-tree-container" style={treeStyles.treeContainer}>
        <Tree
          showLine={{ showLeafIcon: false }}
          showIcon={false}
          switcherIcon={<DownOutlined />}
          treeData={treeData}
          onExpand={(keys) => { setExpandedKeys(keys); setAutoExpandParent(false); }}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          height={600}
          virtual={true}
        />
      </div>

      <Modal
        title={`Add New Node under "${currentParent?.label}"`}
        open={isModalVisible}
        onOk={() => form.submit()}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setCurrentParent(null);
        }}
        okText="Add Node"
        cancelText="Cancel"
        confirmLoading={isAddingNode} 
        okButtonProps={{ disabled: isAddingNode }} 
      >
        <Form form={form} layout="vertical" onFinish={handleAddNode} initialValues={{ isParent: false }}>
          <Item
            name="label"
            label="Node Label"
            rules={[
              { required: true, message: "Please enter node label" },
              { max: 10, message: "Label cannot exceed 10 characters" },
            ]}
          >
            <Input
              placeholder="Enter Node Label"
              maxLength={10}
              showCount
              style={{ textTransform: "uppercase" }}
              onChange={(e) => {
                // Convert to uppercase
                const upperValue = e.target.value.replace(
                  /[^a-zA-Z0-9_\/-]/g,
                  ""
                );
                // Update the form value
                form.setFieldsValue({ label: upperValue?.toUpperCase() });
              }}
            />
          </Item>

          <Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: "Please Enter Description" },
              { max: 40, message: "Description cannot exceed 40 characters" },
            ]}
          >
            <Input
              placeholder="Enter Node Description"
              style={{ textTransform: "uppercase" }}
              maxLength={40}
              showCount
              onChange={(e) => {
                const formattedValue = e.target.value
                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                  .replace(/\s{2,}/g, " ")
                  .replace(/\s*([-&()#,])\s*/g, "$1")
                  .trimStart()
                  .toUpperCase();

                form.setFieldsValue({ description: formattedValue });
              }}
            />
          </Item>
        </Form>
      </Modal>

      {/* Add Tag Modal */}
      <Modal
        title={`Add New ${moduleObject} to "${currentTagParent?.label || "node"}"`}
        open={isTagModalVisible}
        onOk={() => tagForm.submit()}
        onCancel={() => {
          setIsTagModalVisible(false);
          tagForm.resetFields();
          setCurrentTagParent(null);
        }}
        okText={`Add`}
        cancelText="Cancel"
        confirmLoading={isAddingTag} 
        okButtonProps={{ disabled: isAddingTag }}
      >
        <Form form={tagForm} layout="vertical" onFinish={handleAddTag}>
          <Item
            name="tag"
            label={`${moduleObject}`}
            rules={[
              {
                required: true,
                message: `Please enter ${moduleObject}`,
              },
              {
                max: 10,
                message: `${moduleObject} name cannot exceed 10 characters`,
              },
            ]}
          >
            <Input
              placeholder={`Enter ${moduleObject}`}
              style={{ textTransform: "uppercase" }}
              maxLength={10}
              showCount
              onChange={(e) => {
                const formattedValue = e.target.value
                  .replace(/[^a-zA-Z0-9-&()#, ]/g, "")
                  .replace(/\s{2,}/g, " ")
                  .replace(/\s*([-&()#,])\s*/g, "$1")
                  .trimStart()
                  .toUpperCase();

                tagForm.setFieldsValue({ tag: formattedValue });
              }}
            />
          </Item>
        </Form>
      </Modal>

      {/* Edit Description Modal */}
      <Modal
        title={`Edit Description for "${currentEditNode?.label}"`}
        open={isEditModalVisible}
        onOk={() => editForm.submit()}
        onCancel={() => {
          setIsEditModalVisible(false);
          editForm.resetFields();
          setCurrentEditNode(null);
        }}
        okText="Update Description"
        cancelText="Cancel"
         confirmLoading={isEditingDescription} 
      okButtonProps={{ disabled: isEditingDescription }} 
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleEditDescription}
        >
          <Item
            name="description"
            label="Description"
            rules={[
              { required: true, message: "Please Enter Description" },

              { max: 40, message: "Description cannot exceed 40 characters" },
            ]}
          >
            <Input
              showCount
              maxLength={40}
              placeholder="Enter Node Description"
              onChange={(e) => {
                // Convert to uppercase
                const upperValue = e.target.value.toUpperCase();
                // Update the form value
                editForm.setFieldsValue({ description: upperValue });
              }}
            />
          </Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ReusableHierarchyTree;
