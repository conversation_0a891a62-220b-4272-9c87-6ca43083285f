import{n as le,s as pn,u as nl,aX as $,C as ce,bI as on,aT as ht,dG as Qi,xi as er,r as i,g as Yo,a as Gl,o as Zo,aP as Ul,M as Vs,cz as Wo,ep as qo,xj as tr,au as ge,fw as ws,f1 as Wn,j as n,c as N,O as Mt,b1 as sr,d as vt,B as je,an as Pe,bD as nr,aH as co,ai as $n,aj as kn,al as zs,b3 as ml,aa as ln,am as Xs,aG as qn,aF as $l,aZ as _l,aD as Qo,bK as nn,xk as lr,xl as or,xm as ir,g3 as Gn,aY as rr,ae as Cl,aJ as ze,ap as ei,xn as Hn,aC as cr,wV as dr,H as ti,wW as ur,wX as ar,dT as Ho,J as si,xo as fr,be as m,xp as gr,xq as hr,xr as pl,Z as ke,xs as uo,d7 as ao,xt as Sr,b9 as ni,b as Er,cZ as li,bf as vn,g5 as oi,cw as ii,xu as ri,xv as ci,d0 as di,ge as js,bJ as Tr,xw as Ar,f_ as io,ek as Ol,xx as ui,xy as ai,xz as fi,xA as Ms,xB as pr,em as Fo,bi as Bo,eb as Vt,xC as yn,xD as gi,xE as Ps,er as bt,cH as Un,a9 as Rl,fy as wo,xF as Il,T as fs,F as _s,xG as hi,a6 as qt,xH as Si,ch as Ml,b5 as Ei,b6 as xl,aO as Tn,aE as fo,xI as Ti,bE as Ai,ak as pi,bl as Ci,ag as Oi,bm as bi,bn as Ni,bo as Dl,bp as qe,bq as mi,xJ as Dn,$ as Cr,bP as Or,bQ as Vo,bG as jo,fY as _i,br as Ri,qT as Po,ad as Ii,fR as Mi,xK as Ll,aK as no,xL as bl,xM as Nl,xN as br,qF as xi,aU as es,xO as Di,xP as Nr,xQ as Li,xR as vi,xS as us,xT as Jo,dJ as go,bh as Js,cI as xs,eq as zo,xU as mr,xV as _r,xW as Rr,ao as Ir,ba as Mr,xX as lo,xY as xr,xZ as Dr,I as Lr,x_ as vr,x$ as yr,g4 as Gr,wZ as Ur,w_ as $r,y0 as kr,y1 as Wr,wY as qr,aA as Hr,aB as Fr,bd as oo,K as Br,aW as wr,bb as Vr,bc as jr,cA as Pr,y2 as Jr,y3 as zr,d8 as Xr,a$ as Kr,d6 as Yr,d3 as Zr,d4 as Qr,d5 as ec,d9 as tc}from"./index-226a1e75.js";import{F as sc}from"./FilterField-868050e3.js";import{u as nc}from"./useProfitcenterRequestHeaderConfig-b7f65f7d.js";import{u as lc}from"./useChangeMaterialRows-cd4e01b9.js";import{R as yi,u as Gi,a as Ui,b as $i,c as ki,d as ro,S as oc,G as Wi,o as Ln,e as qi,E as ic,f as Xo,C as rc,g as cc,h as dc}from"./RequestDetailsForFC-07159606.js";import{D as uc,u as ac,a as Hi,b as An,c as fc,d as gc,e as hc,A as Sc,P as Ec}from"./PreviewPage-262cf4cb.js";import{u as ho}from"./useMaterialFieldConfig-a3bf7965.js";import{d as tl}from"./DeleteOutlineOutlined-d41ebb56.js";import{d as sl}from"./Description-d98685cc.js";import{d as Fi}from"./TaskAlt-9d2cabf1.js";import Bi from"./AdditionalData-955d2223.js";import{S as gt}from"./SingleSelectDropdown-ee61a6b7.js";import{d as vl,a as yl}from"./CloseFullscreen-e3b32947.js";import{u as wi}from"./useDynamicWorkflowDT-7ae52689.js";import{u as Vi}from"./useCustomDtCall-f90ca5c1.js";import{d as Tc}from"./LibraryAdd-cbd81e59.js";import{d as Ac}from"./PermIdentityOutlined-842d404f.js";import{d as pc}from"./FeedOutlined-2c089703.js";import{d as Ko}from"./TrackChangesTwoTone-f7d7fb26.js";import{d as Cc}from"./FileUploadOutlined-3ff8ee58.js";import{E as Oc}from"./ExcelOperationsCard-3cc40005.js";import{u as bc}from"./useDisplayDataDto-192489ea.js";import"./useChangeLogUpdate-23c3e0f8.js";import"./AdapterDayjs-ca6db362.js";import"./advancedFormat-23da442e.js";import"./customParseFormat-f5b19256.js";import"./isBetween-fc08a3a5.js";import"./AutoCompleteType-63e88d3d.js";import"./useMobilePicker-d8e74594.js";import"./CSSTransition-691ca8e6.js";import"./useChangeMaterialRowsRequestor-9caa254c.js";import"./FilterChangeDropdown-2d228e28.js";import"./DatePicker-e5574363.js";import"./GenericViewGeneral-3e4c8862.js";import"./Edit-3af3a8b3.js";import"./createChangeLogTemplate-774d7b1c.js";import"./useFinanceCostingRows-699f667f.js";import"./AttachFile-fd8e4fbe.js";import"./UtilDoc-5c47dd2e.js";import"./FileDownloadOutlined-0c7acbe7.js";import"./VisibilityOutlined-b2b52c11.js";import"./DeleteOutlined-9dca1b70.js";import"./CloudUpload-17ed0189.js";import"./utilityImages-067c3dc2.js";import"./Delete-3f2fc9ef.js";import"./ReusablePromptBox-c937bab8.js";import"./featureConfig-652a9f8d.js";import"./DataObject-2e0c0294.js";import"./Download-f2e7dedd.js";import"./CheckCircleOutline-70edf41f.js";import"./DeleteOutline-259b9549.js";import"./CloudDownload-23cede9e.js";import"./AttachmentUploadDialog-5b2112e0.js";const ji=()=>{const U=le(J=>J.payload.payloadData),ut=le(J=>J.applicationConfig),ie=le(J=>{var He;return(He=J.userManagement)==null?void 0:He.taskData}),R=pn(),ot=nl(),st=new URLSearchParams(ot.search).get("RequestType");return{getRequestHeaderTemplate:()=>{var w,Q,Re;let J={decisionTableId:null,decisionTableName:"MDG_MAT_REQUEST_HEADER_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_SCENARIO":(ie==null?void 0:ie.ATTRIBUTE_2)===((w=$)==null?void 0:w.FINANCE_COSTING)?(Q=$)==null?void 0:Q.FINANCE_COSTING:st||(U==null?void 0:U.RequestType)||((Re=$)==null?void 0:Re.CREATE),"MDG_CONDITIONS.MDG_MAT_REGION":(U==null?void 0:U.Region)||"US","MDG_CONDITIONS.MDG_MODULE":"Material"}],systemFilters:null,systemOrders:null,filterString:null};const He=z=>{var C,Qe;if(z.statusCode===200){const pe={"Header Data":((Qe=(C=z==null?void 0:z.data)==null?void 0:C.result[0])==null?void 0:Qe.MDG_MAT_REQUEST_HEADER_CONFIG).sort((ue,Ot)=>ue.MDG_MAT_SEQUENCE_NO-Ot.MDG_MAT_SEQUENCE_NO).map(ue=>({fieldName:ue.MDG_MAT_UI_FIELD_NAME,sequenceNo:ue.MDG_MAT_SEQUENCE_NO,fieldType:ue.MDG_MAT_FIELD_TYPE,maxLength:ue.MDG_MAT_MAX_LENGTH,value:ue.MDG_MAT_DEFAULT_VALUE,visibility:ue.MDG_MAT_VISIBILITY,jsonName:ue.MDG_MAT_JSON_FIELD_NAME}))};R(Qi({tab:"Request Header",data:pe})),R(er(pe))}},S=z=>{console.log(z)};ut.environment==="localhost"?ce(`/${on}${ht.INVOKE_RULES.LOCAL}`,"post",He,S,J):ce(`/${on}${ht.INVOKE_RULES.PROD}`,"post",He,S,J)}}},Nc=({setIsSecondTabEnabled:U,setIsAttachmentTabEnabled:ut,requestStatus:ie,downloadClicked:R,setDownloadClicked:ot})=>{var Le,Ut,$s,is,$t,ne,Xe,ve;const[tt,st]=i.useState({}),[F,J]=i.useState(!1),[He,S]=i.useState(!1),[w,Q]=i.useState("success"),[Re,z]=i.useState(!1),[C,Qe]=i.useState([]),[_,be]=i.useState(),[pe,ue]=i.useState({}),[Ot,Ie]=i.useState(!1),[Fe,it]=i.useState("systemGenerated"),[at,Ne]=i.useState(""),[nt,de]=i.useState(""),[he,X]=i.useState([]),[B,se]=i.useState(!1),P=pn(),St=Yo(),c=le(I=>I.payload.payloadData),De=le(I=>I.tabsData.requestHeaderData),Oe=le(I=>I.tabsData.changeFieldsDT);let Jt=le(I=>I.userManagement.roles);const V=le(I=>I.payload.payloadData),Y=le(I=>I.userManagement.userData),ee=le(I=>{var H,re;return(re=(H=I.userManagement)==null?void 0:H.entitiesAndActivities)==null?void 0:re.Material}),f=le(I=>I.request.requestHeader),rt=le(I=>I.request.salesOrgDTData),xt=nl(),v=new URLSearchParams(xt.search),zt=v.get("reqBench"),ts=v.get("RequestId"),{t:Dt}=Gl(),{getRequestHeaderTemplate:ct}=ji(),{getChangeTemplate:Fn}=lc();nc();const{fetchOrgData:Ds}=ho(),{getDtCall:ss}=Zo(),{customError:dt}=Ul(),On=[{code:"Create",desc:"Create New Material in Application"},{code:"Change",desc:"Modify Existing Material in Application"},{code:"Extend",desc:"Extend Existing Material in Application"},{code:"Create with Upload",desc:"Create New Material with Excel Upload"},{code:"Change with Upload",desc:"Modify Existing Material with Excel Upload"},{code:"Extend with Upload",desc:"Extend Existing Material with Excel Upload"}].filter(I=>ee==null?void 0:ee.includes(I.code)),Ls=[{code:"Oncology",desc:""},{code:"Anesthesia/Pain Management",desc:""},{code:"Cardiovascular",desc:""}],bn=[{code:(Le=ws)==null?void 0:Le.LOGISTIC,desc:""},{code:(Ut=ws)==null?void 0:Ut.MRP,desc:""},{code:($s=ws)==null?void 0:$s.WARE_VIEW_2,desc:""},{code:(is=ws)==null?void 0:is.ITEM_CAT,desc:""},{code:($t=ws)==null?void 0:$t.SET_DNU,desc:""},{code:(ne=ws)==null?void 0:ne.UPD_DESC,desc:""},{code:(Xe=ws)==null?void 0:Xe.CHG_STAT,desc:""}],Xt=[{code:"High",desc:""},{code:"Medium",desc:""},{code:"Low",desc:""}];P(Vs({keyName:(ve=Wo)==null?void 0:ve.REQUEST_TYPE,data:On})),P(Vs({keyName:"LeadingCat",data:Ls})),P(Vs({keyName:"RequestPriority",data:Xt})),P(Vs({keyName:"TemplateName",data:bn})),!ts&&!zt&&(P(qo({keyName:"ReqCreatedBy",data:Y==null?void 0:Y.user_id})),P(qo({keyName:"RequestStatus",data:"DRAFT"})));const rn="Basic Data",[Kt,q]=i.useState([rn]),[gs,Ue]=i.useState(""),[hs,we]=i.useState(""),[ye,yt]=i.useState(!0),[Ss,Es]=i.useState(!1);i.useEffect(()=>{P(tr(Kt))},[P,Kt]);const Yt=()=>{var H,re;let I=!0;return V&&((H=De[Object.keys(De)])!=null&&H.length)?(re=De[Object.keys(De)[0]])==null||re.forEach(M=>{var Ge;!V[M.jsonName]&&M.visibility===((Ge=Qo)==null?void 0:Ge.MANDATORY)&&(I=!1)}):I=!1,I};i.useEffect(()=>{V!=null&&V.MatlType&&Zt(V),Yt()},[V]);const Zt=I=>{var M;const H=Ge=>{Ue(Ge.body[0].MaintStatus.split("")),we(Ge.body[0].MaterialType)},re=Ge=>{console.log(Ge)};ce(`/${ge}/data/getViewForMaterialType?materialType=${(M=I==null?void 0:I.MatlType)==null?void 0:M.code}`,"get",H,re)},Me=()=>{z(!0)},ns=()=>{z(!1)},vs=()=>{var I;ot(!1),Ie(!1),it("systemGenerated"),ts||St((I=nn)==null?void 0:I.REQUEST_BENCH)},At=I=>{var H;it((H=I==null?void 0:I.target)==null?void 0:H.value)},Et=()=>{Fe==="systemGenerated"&&(Ks(),vs()),Fe==="mailGenerated"&&(Ts(),vs())},Ks=()=>{Ne("Please wait 1-2 minutes while real-time data are downloaded into the template. Thank you for your patience."),de(!0);let I={region:c==null?void 0:c.Region,scenario:c==null?void 0:c.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:f!=null&&f.requestId?f==null?void 0:f.requestId:c!=null&&c.RequestId?c==null?void 0:c.RequestId:""};const H=Ge=>{if((Ge==null?void 0:Ge.size)==0){de(!1),Ne(""),S(!0),be("No data found for the selected criteria."),Q("danger"),Me();return}const ae=URL.createObjectURL(Ge),We=document.createElement("a");We.href=ae,We.setAttribute("download",`${(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD?"Mass_Extend.xlsx":"Mass_Create.xlsx"}`),document.body.appendChild(We),We.click(),document.body.removeChild(We),URL.revokeObjectURL(ae),de(!1),Ne(""),S(!0),be(`${c!=null&&c.TemplateName?`${c.TemplateName}_Mass Change`:(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD?"Mass_Extend":"Mass_Create"}.xlsx has been downloaded successfully.`),Q("success"),Me(),setTimeout(()=>{St("/requestBench")},2600)},re=()=>{de(!1)},M=`/${ge}${(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD?ht.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND:ht.EXCEL.DOWNLOAD_EXCEL}`;ce(M,"postandgetblob",H,re,I)},Ts=()=>{de(!0);let I={region:c==null?void 0:c.Region,scenario:c==null?void 0:c.RequestType,matlType:"ALL",dtName:"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:"v1",rolePrefix:"",requestId:f!=null&&f.requestId?f==null?void 0:f.requestId:c!=null&&c.RequestId?c==null?void 0:c.RequestId:""};const H=()=>{var Ge;de(!1),Ne(""),S(!0),be((Ge=rr)==null?void 0:Ge.DOWNLOAD_MAIL_INITIATED),Q("success"),Me(),setTimeout(()=>{var ae;St((ae=nn)==null?void 0:ae.REQUEST_BENCH)},2600)},re=()=>{var Ge;de(!1),S(!0),be((Ge=Cl)==null?void 0:Ge.ERR_DOWNLOADING_EXCEL),Q("danger"),Me(),setTimeout(()=>{var ae;St((ae=nn)==null?void 0:ae.REQUEST_BENCH)},2600)},M=`/${ge}${(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD?ht.EXCEL.DOWNLOAD_EXCEL_FOR_EXTEND_MAIL:ht.EXCEL.DOWNLOAD_EXCEL_MAIL}`;ce(M,"post",H,re,I)},Lt=()=>J(!1),ys=I=>{if(C.includes("Distribution Channel")){const H=M=>X(M==null?void 0:M.body),re=M=>console.error(M);ce(`/${ge}/data/getDistrChan?salesOrg=${I.code}`,"get",H,re)}},Gt={orgData:["Plant","Sales Organization","Distribution Channel"].map(I=>({info:pe[I]||{code:"",desc:""},desc:I})),selectedViews:{selectedSections:Kt}},Gs=(I,H)=>{ue(re=>({...re,[I]:H})),I==="Sales Organization"&&ys(H)},Us=`/Date(${Date.now()})/`,Ys=()=>{var ae;Es(!0);let I=lr(V==null?void 0:V.Region,Jt);P(or({...Y,role:I})),se(!1);const H=new Date(V==null?void 0:V.ReqCreatedOn).getTime(),re={RequestId:f!=null&&f.requestId?f==null?void 0:f.requestId:"",Region:(V==null?void 0:V.Region)||"",MatlType:(V==null?void 0:V.MatlType)||"",ReqCreatedBy:(Y==null?void 0:Y.user_id)||"",ReqCreatedOn:H?`/Date(${H})/`:Us,ReqUpdatedOn:H?`/Date(${H})/`:Us,RequestType:(V==null?void 0:V.RequestType)||"",RequestDesc:(V==null?void 0:V.RequestDesc)||"",Division:(V==null?void 0:V.Division)||"",RequestStatus:"DRAFT",RequestPriority:(V==null?void 0:V.RequestPriority)||"",LeadingCat:(V==null?void 0:V.LeadingCat)||"",FieldName:((ae=V==null?void 0:V.FieldName)==null?void 0:ae.join("$^$"))||"",TemplateName:(V==null?void 0:V.TemplateName)||""},M=We=>{var K,Ke,ft;if((We==null?void 0:We.statusCode)===ze.STATUS_200){if(Es(!1),S(!0),be(We==null?void 0:We.message),Q("success"),Me(),P(ei(We.body)),P(Wn({keyName:Wo.REQUEST_ID,data:(K=We==null?void 0:We.body)==null?void 0:K.requestId})),ut(!0),yt(!1),P(Hn({})),P(cr({})),(c==null?void 0:c.RequestType)===$.CREATE_WITH_UPLOAD||(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD){Ie(!0);return}if((c==null?void 0:c.RequestType)===((Ke=$)==null?void 0:Ke.CHANGE_WITH_UPLOAD)){se(!0);return}if((c==null?void 0:c.RequestType)===((ft=$)==null?void 0:ft.CHANGE)){const As=dr(Oe==null?void 0:Oe["Config Data"],c==null?void 0:c.FieldName,["Material","Plant","Sales Org","Distribution Channel","Warehouse","MRP Controller"]);P(ti({...Oe,"Config Data":As}));const cn=ur(Oe==null?void 0:Oe[c==null?void 0:c.TemplateName],c==null?void 0:c.FieldName);P(ar([...cn]))}setTimeout(()=>{P(Gn(1)),U(!0)},2500)}else S(!0),be(We==null?void 0:We.message),Q("error"),Me(),Es(!1)},Ge=()=>{Es(!1),S(!0),Q("error"),be("Error occured while saving Request Header"),Me()};ce(`/${ge}/alter/createRequestHeader`,"post",M,Ge,re)};i.useEffect(()=>{var I;if(R){if((c==null?void 0:c.RequestType)===$.CREATE_WITH_UPLOAD||(c==null?void 0:c.RequestType)===$.EXTEND_WITH_UPLOAD){Ie(!0);return}if((c==null?void 0:c.RequestType)===((I=$)==null?void 0:I.CHANGE_WITH_UPLOAD)){se(!0);return}}},[R]);function jt(I){return I.every(H=>H.info.code&&H.info.desc)}const ls=()=>{if(!jt(Gt.orgData))S(!0),Q("error"),be("Please choose all mandatory fields"),Me();else{const H={label:"Attachments & Comments",value:"attachments&comments"},M=[{label:"General Information",value:"generalInformation"},...Kt,H];Gt.selectedViews=M,P(ir(Gt)),P(Gn(1)),U(!0)}};i.useEffect(()=>{ct()},[c==null?void 0:c.RequestType]);const Rs=(I="")=>{var Ge,ae,We,K;const H={materialNo:I??"",top:500,skip:0,salesOrg:((ae=(Ge=rt==null?void 0:rt.uniqueSalesOrgList)==null?void 0:Ge.map(Ke=>Ke.code))==null?void 0:ae.join("$^$"))||""},re=Ke=>{(Ke==null?void 0:Ke.statusCode)===ze.STATUS_200&&(P(Vs({keyName:Ho.RETURN_MAT_NUMBER,data:Ke==null?void 0:Ke.body})),P(Vs({keyName:Ho.PARENT_MAT_NUMBER,data:Ke==null?void 0:Ke.body})))},M=Ke=>{dt(Ke)};ce(`/${ge}${(K=(We=ht)==null?void 0:We.DATA)==null?void 0:K.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",re,M,H)};i.useEffect(()=>{rt!=null&&rt.uniqueSalesOrgList&&Rs()},[]);const os=I=>{let H={decisionTableId:null,decisionTableName:si.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":I||""}]};ss(H)};return i.useEffect(()=>{c!=null&&c.Region&&(Ds(),os(c==null?void 0:c.Region))},[c==null?void 0:c.Region]),i.useEffect(()=>{c!=null&&c.TemplateName&&(((c==null?void 0:c.TemplateName)===ws.MRP||(c==null?void 0:c.TemplateName)===ws.WARE_VIEW_2)&&P(Wn({keyName:"FieldName",data:void 0})),Fn())},[c==null?void 0:c.TemplateName]),n("div",{children:N(_l,{spacing:2,children:[Object.entries(De).map(([I,H])=>N(Mt,{item:!0,md:12,sx:{backgroundColor:"white",maxHeight:"max-content",height:"max-content",borderRadius:"8px",border:"1px solid #E0E0E0",mt:.25,boxShadow:"0px 2px 14px 0px rgba(48, 38, 185, 0.10)",...sr},children:[n(vt,{sx:{fontSize:"12px",fontWeight:"700",paddingBottom:"10px"},children:Dt(I)}),n(je,{children:n(Mt,{container:!0,sx:{gap:"1rem 1.5rem"},children:H.filter(re=>re.visibility!=="Hidden").sort((re,M)=>re.sequenceNo-M.sequenceNo).map(re=>n(sc,{isHeader:!0,field:re,dropDownData:tt,disabled:ts||(f==null?void 0:f.requestId),requestHeader:!0},re.id))})}),!ts&&!(f!=null&&f.requestId)&&n(je,{sx:{display:"flex",justifyContent:"flex-end",marginTop:"20px"},children:n(Pe,{variant:"contained",color:"primary",disabled:Ss||!Yt(),onClick:Ys,startIcon:Ss?n(nr,{size:20,color:"inherit"}):null,children:Dt("Save Request Header")})}),n(co,{})]},I)),N($n,{open:F,onClose:Lt,children:[n(kn,{sx:{backgroundColor:"#EAE9FF"},children:"Select Org Data"}),n(zs,{children:n(Mt,{container:!0,columnSpacing:1,children:C.map((I,H)=>N(i.Fragment,{children:[n(Mt,{item:!0,md:4,children:N(vt,{children:[I,n("span",{style:{color:"red"},children:"*"})]})}),n(Mt,{item:!0,md:8,children:n(ml,{options:I==="Distribution Channel"?he:tt[I]||[],size:"small",getOptionLabel:re=>`${re.code} - ${re.desc}`,renderOption:(re,M)=>n("li",{...re,children:n(vt,{children:`${M.code} - ${M.desc}`})}),onChange:(re,M)=>Gs(I,M),renderInput:re=>n(ln,{...re,placeholder:`Select ${I}`})})})]},H))})}),N(Xs,{children:[n(Pe,{onClick:Lt,variant:"outlined",children:Dt("Cancel")}),n(Pe,{variant:"contained",onClick:()=>{ls()},children:Dt("Proceed")})]})]}),B&&n(yi,{downloadClicked:R,setDownloadClicked:ot}),n(uc,{onDownloadTypeChange:Et,open:Ot,downloadType:Fe,handleDownloadTypeChange:At,onClose:vs}),n(qn,{blurLoading:nt,loaderMessage:at}),He&&n($l,{openSnackBar:Re,alertMsg:_,alertType:w,handleSnackBarClose:ns})]})})},Pi=(U,ut,ie,R,ot,tt="")=>({checkValidation:(F,J,He,S,w)=>{var ue,Ot,Ie,Fe,it,at,Ne,nt,de,he,X,B,se,P,St;const Q=(ue=U==null?void 0:U[F])==null?void 0:ue.payloadData,Re=(Ot=U==null?void 0:U[F])==null?void 0:Ot.headerData;(Ie=U==null?void 0:U[F])==null||Ie.ManufacturerID;const z=(Fe=U==null?void 0:U.payloadData)==null?void 0:Fe.Region;if(!(Re!=null&&Re.materialNumber))return{missingFields:["Material number"],isValid:!1};if(!(Re!=null&&Re.globalMaterialDescription)||!Q)return{missingFields:["Material Description"],isValid:!1};const C=fr(Q[m.BASIC_DATA]);C.Material=Re==null?void 0:Re.materialNumber,C.MatlDesc=Re==null?void 0:Re.globalMaterialDescription;const Qe=((it=Re==null?void 0:Re.materialType)==null?void 0:it.code)??(Re==null?void 0:Re.materialType),_=ut==null?void 0:ut.find(c=>(c==null?void 0:c[z])&&(c==null?void 0:c[z][Qe])),be=_&&_[z]&&((at=_[z][Qe])==null?void 0:at.mandatoryFields),pe=be==null?void 0:be[m.BASIC_DATA];if((pe==null?void 0:pe.length)>0){for(const c of pe)if(!C[c==null?void 0:c.jsonName])return{missingFields:gr(pe,C),viewType:m.BASIC_DATA,isValid:!1,plant:[m.BASIC_DATA]}}for(const c of ie){const De=hr(J),{displayCombinations:Oe}=(De==null?void 0:De[c])||{};if(Oe&&Oe[0]&&(Oe==null?void 0:Oe.length)>0){const Jt=be==null?void 0:be[c];if(Jt){let V={};for(const Y of Oe){const ee=(Ne=Q[c])==null?void 0:Ne[Y];if(ee){const f=pl(Jt,ee);((nt=Object.keys(f))==null?void 0:nt.length)>0&&(V[Y]=Object.keys(f))}else V[Y]=Jt.map(f=>f.fieldName);if(((de=Object.keys(V))==null?void 0:de.length)>0)return{missingFields:V,viewType:c,isValid:!1,plant:Object.keys(V)}}}}}if(ie.includes(m.SALES)){const c=be==null?void 0:be[m.SALES_GENERAL];let De={};if(c&&Q[m.SALES_GENERAL]){const Oe=pl(c,(he=Q[m.SALES_GENERAL])==null?void 0:he[m.SALES_GENERAL]);Object.keys(Oe).length>0&&(De[m.SALES_GENERAL]=Object.keys(Oe))}else c&&(De[m.SALES_GENERAL]=c.map(Oe=>Oe.fieldName));if(((X=Object.keys(De))==null?void 0:X.length)>0)return{missingFields:De,viewType:m.SALES,isValid:!1,plant:[m.SALES_GENERAL]}}if(ie.includes(m.PURCHASING)){const c=be==null?void 0:be[m.PURCHASING_GENERAL];let De={};if(c&&Q[m.PURCHASING_GENERAL]){const Oe=pl(c,(B=Q[m.PURCHASING_GENERAL])==null?void 0:B[m.PURCHASING_GENERAL]);Object.keys(Oe).length>0&&(De[m.PURCHASING_GENERAL]=Object.keys(Oe))}else c&&(De[m.PURCHASING_GENERAL]=c.map(Oe=>Oe.fieldName));if(((se=Object.keys(De))==null?void 0:se.length)>0)return{missingFields:De,viewType:m.PURCHASING,isValid:!1,plant:[m.PURCHASING_GENERAL]}}if(ie.includes(m.STORAGE)){const c=be==null?void 0:be[m.STORAGE_GENERAL];let De={};if(c&&Q[m.STORAGE_GENERAL]){const Oe=pl(c,(P=Q[m.STORAGE_GENERAL])==null?void 0:P[m.STORAGE_GENERAL]);Object.keys(Oe).length>0&&(De[m.STORAGE_GENERAL]=Object.keys(Oe))}else c&&(De[m.STORAGE_GENERAL]=c.map(Oe=>Oe.fieldName));if(((St=Object.keys(De))==null?void 0:St.length)>0)return{missingFields:De,viewType:m.STORAGE,isValid:!1,plant:[m.STORAGE_GENERAL]}}return{missingFields:null,isValid:!0}}}),Ji=({open:U,onClose:ut,title:ie,lengthOfOrgRow:R,selectedMaterialPayload:ot,materialID:tt,orgRows:st})=>{var Q,Re;const[F,J]=i.useState({}),He=pn(),S=()=>{const z=[];return st&&st.length>0&&(st==null||st.forEach((C,Qe)=>{var _,be,pe,ue,Ot,Ie,Fe,it,at,Ne,nt,de,he,X;if(Qe!==(R==null?void 0:R.copyFor)){const B=(be=(_=C.plant)==null?void 0:_.value)==null?void 0:be.code,se=((ue=(pe=C.plant)==null?void 0:pe.value)==null?void 0:ue.desc)||B,P=(Ot=C.salesOrg)==null?void 0:Ot.code,St=((Ie=C.salesOrg)==null?void 0:Ie.desc)||P,c=(it=(Fe=C.dc)==null?void 0:Fe.value)==null?void 0:it.code,De=((Ne=(at=C.dc)==null?void 0:at.value)==null?void 0:Ne.desc)||c,Oe=(de=(nt=C.warehouse)==null?void 0:nt.value)==null?void 0:de.code,Jt=((X=(he=C.warehouse)==null?void 0:he.value)==null?void 0:X.desc)||Oe;if(B){let V=`Plant: ${se||"N/A"}`;P&&(V+=` | SalesOrg: ${St||"N/A"}`),c&&(V+=` | DC: ${De||"N/A"}`),Oe&&(V+=` | Warehouse: ${Jt||"N/A"}`);let Y=B;P&&(Y+=`-${P}`),c&&(Y+=`-${c}`),Oe&&(Y+=`-${Oe}`),z==null||z.push({code:Y,desc:V,index:Qe,plant:B,salesOrg:P,dc:c,warehouse:Oe})}}})),z},w=()=>{var ue,Ot,Ie,Fe,it,at,Ne,nt;if(!F.code)return;const z=st[R.copyFor],C=(Ot=(ue=z==null?void 0:z.plant)==null?void 0:ue.value)==null?void 0:Ot.code,Qe=(Ie=z==null?void 0:z.salesOrg)==null?void 0:Ie.code,_=(it=(Fe=z==null?void 0:z.dc)==null?void 0:Fe.value)==null?void 0:it.code,be=(Ne=(at=z==null?void 0:z.warehouse)==null?void 0:at.value)==null?void 0:Ne.code;if(!C)return;const pe=JSON.parse(JSON.stringify(ot));(nt=Object.keys(pe))==null||nt.forEach(de=>{const he=pe[de];if(!(de===m.BASIC_DATA||de===m.SALES_GENERAL||de===m.PURCHASING_GENERAL||de===m.TAX_DATA)&&typeof he=="object"){const X=Object.keys(he);if(de===m.WAREHOUSE){const B=X==null?void 0:X.find(P=>P.includes(F.warehouse)),se=X==null?void 0:X.find(P=>P.includes(be));if(B&&se&&se!==B){const P=JSON.parse(JSON.stringify(he[B]));delete P.WarehouseId,pe[de][se]={...JSON.parse(JSON.stringify(pe[de][se]||{})),...P}}}else if(de===m.SALES){const B=`${F.salesOrg}-${F.dc}`,se=`${Qe}-${_}`,P=X==null?void 0:X.find(c=>c===B),St=X==null?void 0:X.find(c=>c===se);if(P&&St&&St!==P){const c=JSON.parse(JSON.stringify(he[P]));delete c.SalesId,pe[de][St]={...JSON.parse(JSON.stringify(pe[de][St]||{})),...c}}}else{const B=X==null?void 0:X.find(P=>P.includes(F.plant)),se=X==null?void 0:X.find(P=>P.includes(C));if(B&&se&&se!==B){const P=JSON.parse(JSON.stringify(he[B]));P&&(delete P.SalesId,delete P.PlantId,delete P.StorageLocationId,delete P.AccountingId,se&&(pe[de][se]={...JSON.parse(JSON.stringify(pe[de][se]||{})),...P}))}}}}),He(Sr({materialID:tt,data:pe})),ut()};return N(ao,{isOpen:U,titleIcon:n(sl,{size:"small",sx:{color:(Re=(Q=ke)==null?void 0:Q.primary)==null?void 0:Re.dark,fontSize:"20px"}}),Title:ie,handleClose:()=>ut(),children:[N(zs,{sx:{mt:2},children:[n(vt,{sx:{mb:2},children:uo.COPY_ORG_DATA_VALUES}),n(gt,{options:S(),placeholder:"SELECT SOURCE ORGANIZATION",onChange:z=>J(z),value:F})]}),n(Xs,{children:n(Pe,{variant:"contained",size:"small",onClick:()=>w(),children:"Ok"})})]})},mc=ni(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),_c=U=>{var po,Co,Oo,bo,No,mo,_o,Ro,Io,Mo,xo,Do,Lo,vo,yo;const ut=mc(),{customError:ie}=Ul(),R=pn(),ot=Er(),{getDynamicWorkflowDT:tt}=wi(),{fetchMaterialFieldConfig:st,fieldConfigLoading:F}=ho(),{getNextDisplayDataForCreate:J}=Gi(),{fetchValuationClassData:He}=Ui(),S=le(e=>e.payload.payloadData),w=S==null?void 0:S.RequestType,Q=le(e=>e.request.salesOrgDTData),Re=le(e=>e.applicationConfig),z=le(e=>e.paginationData),C=le(e=>e.payload),Qe=le(e=>e.request.requestHeader),_=le(e=>e.request.materialRows),be=le(e=>e.payload.payloadData),pe=le(e=>{var t;return((t=e.materialDropDownData)==null?void 0:t.dropDown)||{}}),ue=le(e=>e.tabsData.allTabsData);le(e=>e.userManagement.userData);let Ot=le(e=>e.userManagement.roles),Ie=le(e=>e.userManagement.taskData);const Fe=li(vn.CURRENT_TASK),it=typeof Fe=="string"?JSON.parse(Fe):Fe,at=le(e=>e.tabsData.allMaterialFieldConfigDT),Ne=nl(),nt=new URLSearchParams(Ne.search),de=nt.get("reqBench"),he=nt.get("RequestId"),[X,B]=i.useState(!1),[se,P]=i.useState(!1),[St,c]=i.useState(0),[De,Oe]=i.useState(null),[Jt,V]=i.useState(null),Y="Basic Data",[ee,f]=i.useState([Y]),[rt,xt]=i.useState({data:{},isVisible:!1}),[v,zt]=i.useState(_||[]),ts=le(e=>e.selectedSections.selectedSections),[Dt,ct]=i.useState(!!(v!=null&&v.length)),[Fn,Ds]=i.useState(!1),[ss,dt]=i.useState(!1),[Cn,On]=i.useState(""),{fetchTabSpecificData:Ls}=$i(),[bn,Xt]=i.useState([]),[rn,Kt]=i.useState(0),[q,gs]=i.useState(null),[Ue,hs]=i.useState(!1),[we,ye]=i.useState(!0),[yt,Ss]=i.useState(v.length+1),[Es,Yt]=i.useState(0),[Zt,Me]=i.useState(_.length>0),[ns,vs]=i.useState({}),[At,Et]=i.useState({}),[Ks,Ts]=i.useState(0),[Lt,ys]=i.useState([]),[Gt,Gs]=i.useState({}),[Us,Ys]=i.useState([]),[jt,ls]=i.useState(!1),[Rs,os]=i.useState(""),[Le,Ut]=i.useState("Basic Data"),[$s,is]=i.useState(!1);let $t={id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null};const[ne,Xe]=i.useState([$t]),[ve,I]=i.useState(!1),[H,re]=i.useState(null),[M,Ge]=i.useState("yes"),[ae,We]=i.useState([]),[K,Ke]=i.useState(null),ft=(po=C==null?void 0:C[K])==null?void 0:po.headerData,[As,cn]=i.useState("success"),[Bn,dn]=i.useState(!1),[wn,Pt]=i.useState([]),[un,Vn]=i.useState(""),[ps,Nn]=i.useState(""),[ks,an]=i.useState(""),Cs=le(e=>e.tabsData.matViews),Is=le(e=>{var t;return(t=e.tabsData)==null?void 0:t.matOdataViews}),{checkValidation:fn}=Pi(C,at,ee),{t:x}=Gl(),{getDtCall:gn,dtData:Ce}=Vi(),[hn,Zs]=i.useState(null),fe=i.useRef(null),oe=[{region:"US",temp:"MIDDLE EAST HUB"},{region:"US",temp:"SOUTHERN HUB"},{region:"EUR",temp:"NORTH HUB"},{region:"EUR",temp:"CENTRAL HUB"},{region:"EUR",temp:"WEST HUB"}],[Ye,Rt]=i.useState(null),[rs,me]=i.useState(""),[Ze,Os]=i.useState(""),pt=i.useRef(ne),[Ws,Qs]=i.useState(!1),qs=(Co=C==null?void 0:C[K])==null?void 0:Co.payloadData,{fetchDataAndDispatch:mn}=ki(),Ve=["Sales Org","Plant","Distribution Channel","Storage Location","Warehouse"],[Se,kt]=i.useState({}),[ll,ol]=i.useState(!1),[il,jn]=i.useState(0),[Hs,Be]=i.useState({"Material No":!1}),{getContryBasedOnPlant:Pn}=qi({doAjax:ce,customError:ie,fetchDataAndDispatch:mn,destination_MaterialMgmt:ge}),[Nt,kl]=i.useState([]),{filteredButtons:rl,showWfLevels:cl}=ac(Ie,Re,on,Mi),dl=oi(rl,[An.HANDLE_SUBMIT_FOR_APPROVAL,An.HANDLE_SAP_SYNDICATION,An.HANDLE_SUBMIT_FOR_REVIEW]),{showSnackbar:mt}=ii(),Ht=40,Sn=18,[Wl,_n]=i.useState([]);i.useEffect(()=>{var e,t,o,r,d,a,g,T,W,p,L,A,y,k,te,xe,$e;if(zt(_),Me((_==null?void 0:_.length)>0),(_==null?void 0:_.length)>0&&he&&(!K||ks)){Ke((e=_==null?void 0:_[0])==null?void 0:e.id),an((t=_==null?void 0:_[0])==null?void 0:t.materialNumber),tn((r=(o=_==null?void 0:_[0])==null?void 0:o.materialType)==null?void 0:r.code),Yt(0),Ut(((g=(a=(d=_==null?void 0:_[0])==null?void 0:d.views)==null?void 0:a.filter(_t=>En(_t)))==null?void 0:g[0])||m.BASIC_DATA),f((W=(T=_==null?void 0:_[0])==null?void 0:T.views)!=null&&W.length?(p=_==null?void 0:_[0])==null?void 0:p.views:[Y]);const Ae=ri(C),lt=ci(Ae);let It=JSON.parse(JSON.stringify(lt));R(di(It)),R(Wn({keyName:"selectedMaterialID",data:(L=_==null?void 0:_[0])==null?void 0:L.id})),(k=(y=C==null?void 0:C[(A=_==null?void 0:_[0])==null?void 0:A.id])==null?void 0:y.Tochildrequestheaderdata)!=null&&k.ChildRequestId&&R(Wn({keyName:"childRequestId",data:($e=(xe=C==null?void 0:C[(te=_==null?void 0:_[0])==null?void 0:te.id])==null?void 0:xe.Tochildrequestheaderdata)==null?void 0:$e.ChildRequestId}))}},[_]),i.useEffect(()=>{var e,t,o;(e=_==null?void 0:_[0])!=null&&e.materialType&&(hl((t=_==null?void 0:_[0])==null?void 0:t.materialType),zn({row:_[0]}),js(_)&&(ye(!1),ct(!1))),_!=null&&_.length&&Ts((o=_==null?void 0:_.at(-1))==null?void 0:o.lineNumber),R(Tr({keyName:"VarOrdUn",data:Ar})),!(_!=null&&_.length)&&!he&&Jn(),Rn()},[]),i.useEffect(()=>{F?(me(!0),Os(io.LOADING)):(me(!1),Os(""))},[F]),i.useEffect(()=>{var e,t,o,r;if(Ce&&((e=Ce==null?void 0:Ce.customParam)==null?void 0:e.dt)===Ol.MDG_ORG_ELEMENT_DEFAULT_VALUE){const d=ui((r=(o=(t=Ce==null?void 0:Ce.data)==null?void 0:t.result)==null?void 0:o[0])==null?void 0:r.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);R(ai({data:d}))}},[Ce]);const Jn=()=>{let e={decisionTableId:null,decisionTableName:Ol.MDG_ORG_ELEMENT_DEFAULT_VALUE,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_REGION":S==null?void 0:S.Region,"MDG_CONDITIONS.MDG_SCENARIO":S==null?void 0:S.RequestType}]};gn(e,{dt:Ol.MDG_ORG_ELEMENT_DEFAULT_VALUE})};i.useEffect(()=>{const e=async()=>{var t,o;try{const r=await tt(w,S==null?void 0:S.Region,"",(o=(t=C[K])==null?void 0:t.Tochildrequestheaderdata)==null?void 0:o.MaterialGroupType,Ie==null?void 0:Ie.ATTRIBUTE_3,"v4","MDG_MAT_DYNAMIC_WF_DT",Tn.MAT);kl(r)}catch(r){ie(r)}};w&&(S!=null&&S.Region)&&K&&(Ie!=null&&Ie.ATTRIBUTE_3||it!=null&&it.ATTRIBUTE_3)&&e()},[w,S==null?void 0:S.Region,K,Ie==null?void 0:Ie.ATTRIBUTE_3]),i.useEffect(()=>{M==="no"&&(kt({}),re(null),gs(null))},[M]),i.useEffect(()=>{var e,t,o,r,d,a,g,T,W,p,L,A,y,k,te,xe,$e,Ae,lt,It;K&&(ue!=null&&ue[m.BASIC_DATA])&&(((e=C[K])!=null&&e.headerData.refMaterialData||C!=null&&C.OrgElementDefaultValues)&&!((r=(o=(t=C[K])==null?void 0:t.payloadData)==null?void 0:o["Basic Data"])!=null&&r.basic)&&!he&&ul((a=(d=C[K])==null?void 0:d.headerData)==null?void 0:a.refMaterialData),(p=(W=(T=(g=C[K])==null?void 0:g.payloadData)==null?void 0:T[m.CLASSIFICATION])==null?void 0:W.basic)!=null&&p.Classtype&&fi((k=(y=(A=(L=C[K])==null?void 0:L.payloadData)==null?void 0:A[m.CLASSIFICATION])==null?void 0:y.basic)==null?void 0:k.Classtype,R)),(!Le||(($e=(xe=(te=C[K])==null?void 0:te.headerData.views)==null?void 0:xe.filter(_t=>En(_t)))==null?void 0:$e[0])!==m.BASIC_DATA)&&Ut(((It=(lt=(Ae=C[K])==null?void 0:Ae.headerData.views)==null?void 0:lt.filter(_t=>En(_t)))==null?void 0:It[0])||m.BASIC_DATA)},[K,ue]),i.useEffect(()=>{(v==null?void 0:v.length)===0&&ct(!1)},[v,ve]),i.useEffect(()=>{H!=null&&H.code?ql(H==null?void 0:H.code,"extended"):Et(e=>({...e,"Sales Org":[]})),kt(e=>({...e,[Ms.SALES_ORG]:null}))},[H]),i.useEffect(()=>{var e;(e=Se==null?void 0:Se["Material Type"])!=null&&e.code&&(Rn(),re(null),gs(null))},[(Oo=Se==null?void 0:Se["Material Type"])==null?void 0:Oo.code]),i.useEffect(()=>{["Distribution Channel","Plant"].forEach(t=>{kt(o=>({...o,[t]:""})),At[t]&&Et(o=>({...o,[t]:[]}))})},[(bo=Se==null?void 0:Se["Sales Org"])==null?void 0:bo.code]),i.useEffect(()=>{["Storage Location","Warehouse"].forEach(t=>{kt(o=>({...o,[t]:""})),At[t]&&Et(o=>({...o,[t]:[]}))})},[(No=Se==null?void 0:Se.Plant)==null?void 0:No.code]),i.useEffect(()=>{var e,t,o,r,d,a,g,T,W;if(K&&((t=(e=C[K])==null?void 0:e.headerData)!=null&&t.materialType)){let p=(o=C[K])==null?void 0:o.headerData;if(Cs&&Cs[(r=p==null?void 0:p.materialType)==null?void 0:r.code]&&((d=p==null?void 0:p.views)==null?void 0:d.length)<2&&Is&&Is[(a=p==null?void 0:p.materialType)==null?void 0:a.code]){let L=(S==null?void 0:S.Region)==="EUR"?((T=Cs[(g=p==null?void 0:p.materialType)==null?void 0:g.code])==null?void 0:T.filter(A=>A!==m.WAREHOUSE))||[]:Cs[(W=p==null?void 0:p.materialType)==null?void 0:W.code]||[];L=L.filter(A=>{var y,k;return(k=Is[(y=p==null?void 0:p.materialType)==null?void 0:y.code])==null?void 0:k.includes(A)}),Ys(L),f(L),Ft({id:K,field:"views",value:L})}}},[Cs,Is,K,(_o=(mo=C[K])==null?void 0:mo.headerData)==null?void 0:_o.materialType]),i.useEffect(()=>{Se[Ms.SALES_ORG]&&(fl(),kt(e=>({...e,[Ms.DIST_CHNL]:null,[Ms.PLANT]:null})))},[Se[Ms.SALES_ORG]]);const ul=e=>{var a,g,T,W,p,L;const t=((T=(g=(a=e==null?void 0:e.copyPayload)==null?void 0:a.payloadData)==null?void 0:g["Basic Data"])==null?void 0:T.basic)||{},o=((p=(W=C==null?void 0:C.OrgElementDefaultValues)==null?void 0:W[m.BASIC_DATA])==null?void 0:p[m.BASIC_DATA])||{};new Set([...Object.keys(o),...Object.keys(t)]).forEach(A=>{const y=(t==null?void 0:t[A])||"",k=(o==null?void 0:o[A])||"",te=A==="Division"?S==null?void 0:S.Division:pr(A,y,ue["Basic Data"],k);R(Fo({materialID:K,viewID:"Basic Data",itemID:"basic",keyName:A,data:te}))});let d=(L=e==null?void 0:e.copyPayload)==null?void 0:L.unitsOfMeasureData;if(d!=null&&d.length){let A=[];d==null||d.forEach(y=>{A.push({...y,id:(y==null?void 0:y.id)||A.length+1})}),R(Bo({materialID:K,data:A}))}},ql=(e,t)=>{const o=d=>{Be(a=>({...a,"Sales Org":!1})),(d==null?void 0:d.statusCode)===ze.STATUS_200&&Et(t==="notExtended"?a=>({...a,"Sales Org":d.body}):a=>({...a,"Sales Org":(d==null?void 0:d.body.length)>0?d.body:[]}))},r=()=>{Be(d=>({...d,"Sales Org":!1}))};Be(d=>({...d,"Sales Org":!0})),ce(`/${ge}/data/${t==="notExtended"?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${e}&region=${S==null?void 0:S.Region}`,"get",o,r)},Hl=(e,t,o)=>{Be(g=>({...g,Plant:!0}));const r=g=>{Be(T=>({...T,Plant:!1})),(g==null?void 0:g.statusCode)===ze.STATUS_200&&Et(t==="notExtended"?T=>({...T,Plant:g.body}):T=>({...T,Plant:(g==null?void 0:g.body.length)>0?g.body:[]}))},d=()=>{Be(g=>({...g,Plant:!1}))},a=o?`&salesOrg=${o.code}`:"";ce(`/${ge}/data/${t==="notExtended"?"getPlantNotExtended":"getPlantExtended"}?materialNo=${e}&region=${S==null?void 0:S.Region}${a}`,"get",r,d)},al=(e,t,o)=>{Be(g=>({...g,Warehouse:!0}));const r=g=>{Be(T=>({...T,Warehouse:!1})),(g==null?void 0:g.statusCode)===ze.STATUS_200&&Et(t==="notExtended"?T=>({...T,Warehouse:g.body}):T=>({...T,Warehouse:(g==null?void 0:g.body.length)>0?g.body:[]}))},d=()=>{Be(g=>({...g,Warehouse:!1}))},a=o?`&plant=${o.code}`:"";ce(`/${ge}/data/${t==="notExtended"?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${e}&region=${S==null?void 0:S.Region}${a}`,"get",r,d)},Fl=(e,t,o)=>{Be(a=>({...a,"Storage Location":!0}));const r=a=>{Be(g=>({...g,"Storage Location":!1})),(a==null?void 0:a.statusCode)===ze.STATUS_200&&Et(g=>{var T;return{...g,[(T=Ms)==null?void 0:T.STORAGE_LOC]:a.body||[]}})},d=a=>{ie(a),Be(g=>({...g,"Storage Location":!1}))};ce(`/${ge}/data/getStorageLocationExtended?plant=${t==null?void 0:t.code}&materialNo=${e}&region=${S==null?void 0:S.Region}&salesOrg=${o==null?void 0:o.code}`,"get",r,d)},fl=()=>{var o;Be(r=>({...r,"Distribution Channel":!0}));const e=r=>{Be(d=>({...d,"Distribution Channel":!1})),(r==null?void 0:r.statusCode)===ze.STATUS_200&&Et(d=>{var a;return{...d,[(a=Ms)==null?void 0:a.DIST_CHNL]:r.body&&(r==null?void 0:r.body)}})},t=r=>{ie(r),Be(d=>({...d,"Distribution Channel":!1}))};ce(`/${ge}/data/getDistributionChannelExtended?materialNo=${H==null?void 0:H.code}&salesOrg=${(o=Se[Ms.SALES_ORG])==null?void 0:o.code}`,"get",e,t)};i.useEffect(()=>{["Mrp Profile"].forEach(D),(_==null?void 0:_.length)===0&&(w===$.CREATE||w===$.CREATE_WITH_UPLOAD)&&I(!0),Bl(),wl()},[]),i.useEffect(()=>{var t,o,r,d,a,g,T,W,p,L,A,y,k,te,xe,$e,Ae,lt,It,_t,bs,Bt,Ns,Je,Wt,ms;pt.current=ne,ne.some(Fs=>{var Yn,Zn,Qn;return((Yn=Fs==null?void 0:Fs.salesOrg)==null?void 0:Yn.code)&&!((Qn=(Zn=Fs==null?void 0:Fs.dc)==null?void 0:Zn.value)!=null&&Qn.code)})?Qs(!1):((o=(t=ne[0])==null?void 0:t.salesOrg)!=null&&o.code&&((a=(d=(r=ne[0])==null?void 0:r.dc)==null?void 0:d.value)!=null&&a.code)||!((p=(T=(g=C[K])==null?void 0:g.headerData)==null?void 0:T.views)!=null&&p.includes((W=m)==null?void 0:W.SALES)))&&((y=(A=(L=ne[0])==null?void 0:L.plant)==null?void 0:A.value)!=null&&y.code)&&((xe=(te=(k=ne[0])==null?void 0:k.sloc)==null?void 0:te.value)!=null&&xe.code||!((It=(Ae=($e=C[K])==null?void 0:$e.headerData)==null?void 0:Ae.views)!=null&&It.includes((lt=m)==null?void 0:lt.STORAGE)))&&((Bt=(bs=(_t=ne[0])==null?void 0:_t.warehouse)==null?void 0:bs.value)!=null&&Bt.code||(S==null?void 0:S.Region)==="EUR"||!((ms=(Je=(Ns=C[K])==null?void 0:Ns.headerData)==null?void 0:Je.views)!=null&&ms.includes((Wt=m)==null?void 0:Wt.WAREHOUSE)))?Qs(!0):Qs(!1)},[ne]),i.useEffect(()=>{ye(!0)},[(Ro=C[K])==null?void 0:Ro.headerData,(Io=C[K])==null?void 0:Io.payloadData]);const Bl=()=>{if(S!=null&&S.Region){const e=o=>{(o==null?void 0:o.statusCode)===ze.STATUS_200&&Et(r=>({...r,"Sales Organization":o.body?o==null?void 0:o.body:[]}))},t=o=>{ie(o)};ce(`/${ge}${ht.DATA.GET_SALES_ORG}?region=${S==null?void 0:S.Region}`,"get",e,t)}},wl=()=>{if(S!=null&&S.Region){const e=o=>{if((o==null?void 0:o.statusCode)===ze.STATUS_200){let r=pt.current?JSON.parse(JSON.stringify(pt.current)):JSON.parse(JSON.stringify(ne));Et(d=>({...d,PlantNotExtended:o.body?o==null?void 0:o.body:[]})),Xe(r),pt.current=r}},t=o=>{ie(o)};ce(`/${ge}${ht.DATA.GET_PLANT}?region=${S==null?void 0:S.Region}`,"get",e,t)}},Vl=(e,t)=>{if(e){Be(d=>({...d,[us.STORAGE_LOCATION]:{...d[us.STORAGE_LOCATION],[t]:!0}}));const o=d=>{if(Be(a=>({...a,[us.STORAGE_LOCATION]:{...a[us.STORAGE_LOCATION],[t]:!1}})),(d==null?void 0:d.statusCode)===ze.STATUS_200){let a=pt.current?JSON.parse(JSON.stringify(pt.current)):JSON.parse(JSON.stringify(ne));t!==-1&&(a[t].sloc.options=es(d.body)),Xe(a),pt.current=a}},r=d=>{ie(d),Be(a=>({...a,[us.STORAGE_LOCATION]:{...a[us.STORAGE_LOCATION],[t]:!1}}))};ce(`/${ge}${ht.DATA.GET_STORAGE_LOCATION}?region=${S==null?void 0:S.Region}&plant=${e==null?void 0:e.code}`,"get",o,r)}},jl=(e,t,o)=>{if(e){Be(a=>({...a,[us.WAREHOUSE]:{...a[us.WAREHOUSE],[o]:!0}}));const r=a=>{if(Be(g=>({...g,[us.WAREHOUSE]:{...g[us.WAREHOUSE],[o]:!1}})),(a==null?void 0:a.statusCode)===ze.STATUS_200){let g=pt.current?JSON.parse(JSON.stringify(pt.current)):JSON.parse(JSON.stringify(ne));o!==-1&&(g[o].warehouse.options=es(a.body)),Xe(g),pt.current=g}},d=a=>{ie(a),Be(g=>({...g,[us.WAREHOUSE]:{...g[us.WAREHOUSE],[o]:!1}}))};ce(`/${ge}${ht.DATA.GET_WAREHOUSE_NO}?region=${S==null?void 0:S.Region}&plant=${e==null?void 0:e.code}&storageLocation=${t==null?void 0:t.code}`,"get",r,d)}},gl=(e,t="",o)=>new Promise((r,d)=>{var L;const a=[{materialNo:e,requestNo:t||(Qe==null?void 0:Qe.requestId),materialDesc:o||""}],g=A=>{var y;((y=A==null?void 0:A.body)==null?void 0:y.totalDuplicatesFound)>0?(mt(A==null?void 0:A.message,"error"),r(!0)):r(!1)},T=A=>{ie(A),r(!1)};let W=0;Object.keys(C).forEach((A,y)=>{var k,te;(A.includes("-")||/\d/.test(A))&&((te=(k=C[A])==null?void 0:k.headerData)==null?void 0:te.materialNumber)===e&&W++});let p=0;Object.keys(C).forEach(A=>{var y,k;(A.includes("-")||/\d/.test(A))&&((k=(y=C[A])==null?void 0:y.headerData)==null?void 0:k.globalMaterialDescription)===o&&p++}),W>1?(mt(`${Cl.DUPLICATE_MATERIAL}${e}`,"error"),r(!0)):p>1?(mt(`${Cl.DUPLICATE_MATERIAL_DESCRIPTION}${o}`,"error"),r(!0)):ce(`/${ge}${(L=ht.MASS_ACTION)==null?void 0:L.MAT_NO_DUPLICATE_CHECK}`,"post",g,T,a)}),Pl=async()=>{let e=[...v],t=!0;return me(!0),Os(io.VALIDATING_MATS),new Promise(async(o,r)=>{for(let a=0;a<(v==null?void 0:v.length);a++){const g=v[a],{missingFields:T,viewType:W,isValid:p,plant:L=[]}=fn(g.id,(g==null?void 0:g.orgData)||[],!1,!1,!1);if(Pt(L),Zs(W),Yt(W?ee==null?void 0:ee.indexOf(W):0),Ut(W||m.BASIC_DATA),_n(T),p){let A=!1;p&&(!he||g!=null&&g.isMatNoChanged||g!=null&&g.isMatDescChanged)&&(A=await gl(g.materialNumber,he,g==null?void 0:g.globalMaterialDescription)),A&&(t=!1),e=e==null?void 0:e.map(y=>y.id===g.id?{...y,validated:!A}:y),R(bt(e))}else{if(t=!1,e=e.map(A=>A.id===g.id?{...A,validated:!1}:A),R(bt(e)),T)if(Ke(g.id),an(g.materialNumber),typeof T=="object"&&!Array.isArray(T)){const A=Object.entries(T).map(([y,k])=>`Combination ${y}: ${k.join(", ")}`);mt(`Line No ${g.lineNumber} : Please fill all the Mandatory fields in ${W||""}: ${A.join(" | ")}`,"error",1e4)}else mt(`Line No ${g.lineNumber} : Please fill all the Mandatory fields in ${W||""}: ${T.join(", ")}`,"error",1e4);break}}t?o(!0):r(),me(!1);const d=js(e);ct(!d),ye(!d),t&&mt("Validation successful for all materials.","success")})},Jl=e=>{var t,o;if(e){let r=JSON.parse(JSON.stringify(((o=(t=C==null?void 0:C[K])==null?void 0:t.headerData)==null?void 0:o.calledMrpCodes)||[]))||[];e.forEach((a,g)=>{var T,W,p,L,A,y,k,te,xe;(T=a==null?void 0:a.mrpProfile)!=null&&T.code&&!((A=(p=(W=C==null?void 0:C[K])==null?void 0:W.headerData)==null?void 0:p.calledMrpCodes)!=null&&A.includes((L=a==null?void 0:a.mrpProfile)==null?void 0:L.code))&&(zl((k=(y=a==null?void 0:a.plant)==null?void 0:y.value)==null?void 0:k.code,(te=a==null?void 0:a.mrpProfile)==null?void 0:te.code),r.push((xe=a==null?void 0:a.mrpProfile)==null?void 0:xe.code))}),R(Ps({materialID:K,keyName:"calledMrpCodes",data:r}));const d=v==null?void 0:v.map(a=>a.id===K?{...a,calledMrpCodes:r}:a);R(bt(d))}},zl=(e,t,o)=>{var g;const r={mrpProfile:t},d=T=>{T.body[0]&&Object.keys(T==null?void 0:T.body[0]).filter(p=>T==null?void 0:T.body[0][p]).forEach(p=>{Xl(e,p,T==null?void 0:T.body[0][p],m.MRP)})},a=T=>{ie(T)};ce(`/${ge}${(g=ht.MASS_ACTION)==null?void 0:g.MRP_DEFAULT_VALUES}`,"post",d,a,r)},Xl=(e,t,o,r)=>{R(Fo({materialID:K||"",keyName:t||"",data:o??null,viewID:r,itemID:e}))};i.useEffect(()=>{js(_)&&(_!=null&&_.length)||de||he?(U.setCompleted([!0,!0]),U==null||U.setIsAttachmentTabEnabled(!0)):(U.setCompleted([!0,!1]),U==null||U.setIsAttachmentTabEnabled(!1))},[_]);const en=Ks+10,Kl=()=>{var o,r;const e=no(),t={id:e,included:!0,lineNumber:en,industrySector:(o=wo)==null?void 0:o.DEFAULT_IND_SECTOR,materialType:((r=be==null?void 0:be.MatlType)==null?void 0:r.code)??"",materialNumber:(H==null?void 0:H.code)||"",globalMaterialDescription:"",views:[],orgData:[],validated:bl.default,withReference:M};R(Nl({materialID:e,data:t})),R(bt([...v,t])),Ss(yt+1),Ts(en),Me(!0),ct(!0),ye(!0),f([Y]),Ke(e),tn("")},Yl=()=>{I(!1),(S==null?void 0:S.RequestType)==="Create"?Kl():(S==null?void 0:S.RequestType)==="Change"&&Ds(!0)},Zl=()=>{In()},Rn=(e="",t=!0)=>{var a,g;const o={materialNo:e??"",salesOrg:((a=Q==null?void 0:Q.uniqueSalesOrgList)==null?void 0:a.map(T=>T.code).join("$^$"))||"",top:500,skip:t?0:rn,matlType:((g=Se==null?void 0:Se["Material Type"])==null?void 0:g.code)??""};Be(T=>({...T,"Material No":!0}));const r=T=>{(T==null?void 0:T.statusCode)===ze.STATUS_200&&(T!=null&&T.body)&&Xt(t?T==null?void 0:T.body:W=>[...W,...T==null?void 0:T.body]),hs(!1),Be(W=>({...W,"Material No":!1}))},d=()=>{hs(!1),Be(T=>({...T,"Material No":!1}))};hs(!0),ce(`/${ge}/data/getSearchParamsMaterialNo`,"post",r,d,o)},hl=e=>{const t=r=>{(r==null?void 0:r.statusCode)===ze.STATUS_200&&ys(r==null?void 0:r.body)},o=r=>{console.error(r,"while fetching the validation data of material number")};ce(`/${ge}/data/getNumberRangeForMaterialType?materialType=${e==null?void 0:e.code}`,"get",t,o)},Ql=((Mo=Lt==null?void 0:Lt[0])==null?void 0:Mo.External)==="X",eo=((xo=Lt==null?void 0:Lt[1])==null?void 0:xo.External)==="X",to=Lt==null?void 0:Lt.some(e=>e.ExtNAwock==="X");(e=>{const t=new Set;let o=null;e==null||e.forEach(d=>{d.External==="X"&&d.ExtNAwock==="X"?(t.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),t.add("Ext W/O Check: Allowed")):d.External!=="X"&&d.ExtNAwock==="X"?(t.add("Internal Number Range: Allowed"),t.add("Ext W/O Check: Allowed")):d.External==="X"&&d.ExtNAwock!=="X"?(t.add(`External Number Range: Allowed (${d.FromNumber}-${d.ToNumber})`),o="Ext W/O Check: Not Allowed"):d.External!=="X"&&d.ExtNAwock!=="X"&&(t.add("Internal Number Range: Allowed"),o="Ext W/O Check: Not Allowed")});const r=Array.from(t);return o&&r.push(o),r.map((d,a)=>n("div",{children:n(vt,{children:d})},a))})(Lt);function tn(e){var d;const t=(S==null?void 0:S.Region)||Vt.US,o=at.some(a=>a[t]&&a[t][e]),r=e!==fe.current;if(!o&&e)st(e,t),(he&&de||!he&&!de)&&Sl(e);else if(!e)R(Hn({}));else{const a=at==null?void 0:at.find(g=>(g==null?void 0:g[t])&&(g==null?void 0:g[t][e]));a&&R(Hn((d=a[t][e])==null?void 0:d.allfields))}e&&r&&He(e),fe.current=e}const Sl=e=>{const t=r=>{var d;(r==null?void 0:r.statusCode)===((d=ze)==null?void 0:d.STATUS_200)?R(Ll({matType:e,views:r==null?void 0:r.body})):R(Ll({matType:e,views:[]}))},o=r=>{ie(r)};ce(`/${ge}${ht.DATA.GET_VIEWS_FOR_MAT}=${e}`,"get",t,o)},Ft=e=>{const{id:t,field:o,value:r}=e;let d=v.map(a=>a.id===t?{...a,[o]:r}:a);Gs({...Gt,[o]:r}),o===yn.MATERIALTYPE&&(hl(r),f([Y]),gi([$t]),R(Ps({materialID:t,keyName:"views",data:[Y]})),R(Ps({materialID:t,keyName:"orgData",data:[]})),d=d.map(a=>a.id===t?{...a,orgData:[]}:a),tn(r==null?void 0:r.code)),o===yn.INCLUDED&&(js(d)?(ct(!1),ye(!1)):(ct(!0),ye(!0))),o===yn.VIEWS&&(ct(!0),ye(!0)),zt(d),R(Ps({materialID:t,keyName:o,data:r})),R(bt(d))},zn=e=>{var t,o,r,d,a,g,T,W,p,L,A,y,k,te;Ke(e.row.id),an(e.row.materialNumber),tn((o=(t=e==null?void 0:e.row)==null?void 0:t.materialType)==null?void 0:o.code),Ys((a=Cs[(d=(r=e==null?void 0:e.row)==null?void 0:r.materialType)==null?void 0:d.code])==null?void 0:a.filter(xe=>{var $e,Ae,lt;return(lt=Is[(Ae=($e=e==null?void 0:e.row)==null?void 0:$e.materialType)==null?void 0:Ae.code])==null?void 0:lt.includes(xe)})),f((T=(g=e==null?void 0:e.row)==null?void 0:g.views)!=null&&T.length?(W=e.row)==null?void 0:W.views:[Y]),Xe((L=(p=e==null?void 0:e.row)==null?void 0:p.orgData)!=null&&L.length?(A=e.row)==null?void 0:A.orgData:[$t]),Yt(0),Ut(((te=(k=(y=e.row)==null?void 0:y.views)==null?void 0:k.filter(xe=>En(xe)))==null?void 0:te[0])||"Basic Data")},Xn=()=>{dt(!0)},In=()=>{dt(!1)},Mn=(e,t)=>{t==="backdropClick"||t==="escapeKeyDown"||is(!1)},El=()=>f(Us||[Y]),Tl=()=>{if(I(!1),M==="yes")if(ae!=null&&ae.length){let e=[...v];ae==null||ae.forEach(t=>{var g,T;const o=no();let r=JSON.parse(JSON.stringify(t));r!=null&&r.refMaterialData&&delete r.refMaterialData;let d=JSON.parse(JSON.stringify((g=C==null?void 0:C[t.id])==null?void 0:g.payloadData));r.id=o,r.lineNumber=en,r.globalMaterialDescription="",r.materialNumber="",r.validated=bl.default,R(Nl({materialID:o,data:r,payloadData:d})),e.push(r),zt(e),R(bt(e)),Ss(yt+1),Ts(en),Me(!0),ct(!0),ye(!0);let a=(T=C==null?void 0:C[t.id])==null?void 0:T.unitsOfMeasureData;if(a!=null&&a.length){let W=[];a==null||a.forEach(p=>{var L,A,y;W.push({...p,eanUpc:"",eanCategory:"",length:"",width:"",height:"",volume:"",grossWeight:"",netWeight:"",eanCategory:(S==null?void 0:S.Region)===((L=Vt)==null?void 0:L.US)?p==null?void 0:p.EanCat:"",eanUpc:(p==null?void 0:p.EanCat)==="MB"&&(S==null?void 0:S.Region)===((A=Vt)==null?void 0:A.US)||(S==null?void 0:S.Region)===((y=Vt)==null?void 0:y.EUR)?"":p==null?void 0:p.EanUpc,id:(p==null?void 0:p.id)||W.length+1})}),R(Bo({materialID:o,data:W}))}}),We([])}else H&&Al();else Yl()},Al=()=>{var r,d,a,g,T,W,p;me(!0);let e={material:H==null?void 0:H.code,wareHouseNumber:(r=Se==null?void 0:Se.Warehouse)==null?void 0:r.code,storageLocation:(d=Se==null?void 0:Se["Storage Location"])==null?void 0:d.code,salesOrg:(a=Se==null?void 0:Se["Sales Org"])==null?void 0:a.code,distributionChannel:(g=Se==null?void 0:Se["Distribution Channel"])==null?void 0:g.code,valArea:(T=Se==null?void 0:Se.Plant)==null?void 0:T.code,plant:(W=Se==null?void 0:Se.Plant)==null?void 0:W.code};const t=L=>{var A,y,k,te,xe,$e,Ae,lt,It,_t,bs;if(me(!1),kt({}),L!=null&&L.body[0]){Jo(L==null?void 0:L.body,S);let Bt=[...v];const Ns=no();let Je={};Je.id=Ns,Je.included=!0,Je.lineNumber=en,Je.globalMaterialDescription="",Je.materialType={code:((A=L.body[0])==null?void 0:A.MatlType)||"",desc:((k=(y=pe==null?void 0:pe.MatlType)==null?void 0:y.find(Wt=>{var ms;return Wt.code===((ms=L.body[0])==null?void 0:ms.MatlType)}))==null?void 0:k.desc)||""},Je.industrySector={code:((te=L.body[0])==null?void 0:te.IndSector)||"",desc:(($e=(xe=pe==null?void 0:pe.IndSector)==null?void 0:xe.find(Wt=>{var ms;return Wt.code===((ms=L.body[0])==null?void 0:ms.IndSector)}))==null?void 0:$e.desc)||""},Je.materialNumber="",Je.views=(lt=(((Ae=L.body[0])==null?void 0:Ae.Views)||"").split(",").map(Wt=>Wt.trim()==="Storage"?m.STORAGE:Wt.trim()))==null?void 0:lt.filter(Wt=>!go.includes(Wt)),(S==null?void 0:S.Region)===((It=Vt)==null?void 0:It.EUR)&&(Je.views=((_t=Je==null?void 0:Je.views)==null?void 0:_t.filter(Wt=>Wt!==m.WAREHOUSE))||[]),Je.validated=bl.default,Je.withReference=M,Je.refMaterialData=Jo(L.body,S),R(Nl({materialID:Ns,data:Je,payloadData:{}})),Bt.push(Je),zt(Bt),R(bt(Bt)),f(Je==null?void 0:Je.views),Ss(yt+1),Ts(en),Me(!0),ct(!0),ye(!0),tn((bs=L.body[0])==null?void 0:bs.MatlType),Ke(Ns)}else me(!1),mt(Cl.NO_MATERIAL_FOUND,"warning"),I(!0)},o=L=>{ie(L),me(!1),I(!0)};kt({}),re(null),gs(null),ce(`/${ge}${(p=ht.DATA)==null?void 0:p.GET_COPY_MATERIAL}`,"post",t,o,e)},s=!Un.includes(U==null?void 0:U.requestStatus)||he&&!de,l=e=>({hasFertRole:e.includes("CA-MDG-MRKTNG-FERT-EUR"),hasSalesRole:e.includes("CA-MDG-MRKTNG-SALES-EUR")}),u=(e,t,o)=>{var a;const{hasFertRole:r,hasSalesRole:d}=l(Ot);if(r&&!d&&(S==null?void 0:S.Region)===Vt.EUR)return(e==null?void 0:e.code)!=="FERT";if(!r&&d&&(S==null?void 0:S.Region)===Vt.EUR)return(e==null?void 0:e.code)==="FERT";if(r&&d&&(S==null?void 0:S.Region)===Vt.EUR){const g=t[0];if(o===(g==null?void 0:g.id))return!1;const T=(a=g==null?void 0:g.materialType)==null?void 0:a.code;if(T==="FERT")return(e==null?void 0:e.code)!=="FERT";if(T)return(e==null?void 0:e.code)==="FERT"}return!1},h=(e,t)=>{var o;oc.fire({title:x("Are you sure?"),text:x("Changing the material type will reset all the field values entered!"),icon:"warning",showCancelButton:!0,confirmButtonColor:(o=ke.primary)==null?void 0:o.main,cancelButtonColor:ke.error.red,confirmButtonText:x("Yes, do it!"),cancelButtonText:x("Cancel"),reverseButtons:!0}).then(r=>{r.isConfirmed&&(f([]),Ft({id:Rs,field:yn.MATERIALTYPE,value:e}),R(br({materialId:t})),Ft({id:t,field:"materialType",value:e}))})},E=[{field:"included",headerName:x("Included"),flex:.5,align:"center",headerAlign:"center",renderCell:e=>n(Rl,{checked:e.row.included,disabled:s,onChange:t=>Ft({id:e.row.id,field:"included",value:t.target.checked})})},{field:"lineNumber",headerName:x("Line Number"),flex:.5,editable:!0,align:"center",headerAlign:"center"},{field:"industrySector",headerName:x("Industry Sector"),flex:.7,align:"center",headerAlign:"center",...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{renderCell:e=>{var t;return n(gt,{options:(pe==null?void 0:pe.IndSector)||[],value:e.row.industrySector||((t=wo)==null?void 0:t.DEFAULT_IND_SECTOR),onChange:o=>Ft({id:e.row.id,field:"industrySector",value:o}),placeholder:x("Select Industry Sector"),disabled:s,minWidth:"90%",listWidth:235})}}:{editable:!1,renderCell:e=>{var t,o;return((o=(t=C==null?void 0:C[e.row.id])==null?void 0:t.headerData)==null?void 0:o.industrySector)||""}}},{field:"materialType",headerName:x("Material Type"),flex:.7,align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:[x("Material Type"),n("span",{style:{color:"red"},children:"*"})]}),...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{renderCell:e=>n(gt,{options:Il||[],value:e.row.materialType,onChange:t=>{e.row.materialType?h(t,e.row.id):Ft({id:e.row.id,field:"materialType",value:t})},placeholder:x("Select Material Type"),disabled:s,minWidth:"90%",listWidth:235,isOptionDisabled:t=>u(t,v,e.row.id)})}:{editable:!1,renderCell:e=>{var t,o;return((o=(t=C==null?void 0:C[e.row.id])==null?void 0:t.headerData)==null?void 0:o.materialType)||""}}},{field:"materialNumber",headerName:x("Material Number"),flex:.7,editable:!1,align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:[x("Material Number"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:e=>{var T,W;const[t,o]=i.useState({[(T=e==null?void 0:e.row)==null?void 0:T.id]:e.row.materialNumber}),r=e.row.id,d=i.useRef(null),a=p=>{const L=p.target.value.toUpperCase();(L==null?void 0:L.length)>=Sn&&(d.current||(d.current=setTimeout(()=>{mt(`Material Number cannot exceed ${Sn} characters`,"error"),d.current=null},1e3)));const A=L.replace(/[^A-Z0-9-]/g,"").slice(0,Sn);o(k=>({...k,[r]:A})),Ft({id:e.row.id,field:"materialNumber",value:A});const y=v.map(k=>k.id===e.row.id?{...k,isMatNoChanged:!0,materialNumber:A}:k);R(bt(y))},g=(((W=t[r])==null?void 0:W.length)||0)===Sn;return n(fs,{title:t[r]||"",arrow:!0,children:(S==null?void 0:S.RequestType)===$.CREATE||(S==null?void 0:S.RequestType)===$.CREATE_WITH_UPLOAD?n(ln,{fullWidth:!0,placeholder:x("ENTER MATERIAL NUMBER"),variant:"outlined",size:"small",name:"material number",value:t[r]||"",onChange:p=>{a(p)},error:g,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},disabled:s}):e.row.materialNumber})}},{field:"globalMaterialDescription",flex:.7,headerName:x("Material Description"),renderHeader:()=>N("span",{children:[x("Material Description"),n("span",{style:{color:"red"},children:"*"})]}),renderCell:e=>{var T,W;const[t,o]=i.useState({[(T=e==null?void 0:e.row)==null?void 0:T.id]:e.row.globalMaterialDescription}),r=e.row.id,d=i.useRef(null),a=p=>{const L=p.target.value.toUpperCase();(L==null?void 0:L.length)>Ht&&(d.current||(d.current=setTimeout(()=>{mt(`Material Description cannot exceed ${Ht} characters`,"error"),d.current=null},1e3)));const A=L.replace(/[^A-Z0-9\s-]/g,"").slice(0,Ht);o(k=>({...k,[r]:A})),Ft({id:e.row.id,field:"globalMaterialDescription",value:A});const y=v.map(k=>k.id===e.row.id?{...k,isMatDescChanged:!0,globalMaterialDescription:A}:k);R(bt(y))},g=(((W=t[r])==null?void 0:W.length)||0)===Ht;return n(je,{sx:{display:"flex",alignItems:"center",width:"100%"},children:n(fs,{title:t[r]||"",arrow:!0,placement:"top",children:n(ln,{fullWidth:!0,variant:"outlined",size:"small",placeholder:x("ENTER MATERIAL DESCRIPTION"),value:t[r]||"",onChange:a,onKeyDown:p=>{p.key===" "&&p.stopPropagation()},error:g,sx:{flexGrow:1,"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:g?ke.error.dark:void 0},"&:hover fieldset":{borderColor:g?ke.error.dark:void 0},"&.Mui-focused fieldset":{borderColor:g?ke.error.dark:void 0}},"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},disabled:s})})})},align:"center",headerAlign:"center",editable:!1},{...w===$.CREATE||w===$.CREATE_WITH_UPLOAD?{field:"views",headerName:"",flex:.8,align:"center",headerAlign:"center",renderCell:e=>{var t,o,r;return N(_s,{children:[n(Pe,{variant:"contained",size:"small",disabled:!((t=e==null?void 0:e.row)!=null&&t.materialType),onClick:()=>{var d,a,g;ls(!0),os(e.row.id),f((a=(d=e==null?void 0:e.row)==null?void 0:d.views)!=null&&a.length?(g=e.row)==null?void 0:g.views:[Y])},children:x("Views")}),n(Pe,{variant:"contained",disabled:!(((r=(o=e==null?void 0:e.row)==null?void 0:o.views)==null?void 0:r.length)>1),size:"small",sx:{marginLeft:"4px"},onClick:()=>{var d,a,g;is(!0),os(e.row.id),Xe((a=(d=e==null?void 0:e.row)==null?void 0:d.orgData)!=null&&a.length?(g=e.row)==null?void 0:g.orgData:[$t])},children:x("ORG Data")})]})}}:{}},{field:"action",headerName:x("Action"),flex:.5,align:"center",headerAlign:"center",renderCell:e=>{let t=hi(e==null?void 0:e.row);const o=async r=>{var A,y,k,te,xe,$e,Ae,lt,It,_t,bs;if(r.stopPropagation(),((y=(A=e==null?void 0:e.row)==null?void 0:A.views)==null?void 0:y.length)>1&&(!((k=e==null?void 0:e.row)!=null&&k.orgData)||((xe=(te=e==null?void 0:e.row)==null?void 0:te.orgData)==null?void 0:xe.length)===0)){mt(x((Ae=($e=xi)==null?void 0:$e.FILES)==null?void 0:Ae.MISSING_ORG_DATA),"error",1e4);return}const{missingFields:d,viewType:a,isValid:g,plant:T=[]}=fn(e.row.id,((lt=e==null?void 0:e.row)==null?void 0:lt.orgData)||[],Ql,eo,to);if(Pt(T),Zs(a),_n(d),Yt(a?ee==null?void 0:ee.indexOf(a):0),Ut(a||m.BASIC_DATA),d)if(typeof d=="object"&&!Array.isArray(d)){const Bt=Object.entries(d).map(([Ns,Je])=>`Combination ${Ns}: ${Je.join(", ")}`);mt(`${x("Line No")} ${e.row.lineNumber} : ${x("Please fill all the Mandatory fields in")} ${a||""}: ${Bt.join(" | ")}`,"error",1e4)}else mt(`${x("Line No")} ${e.row.lineNumber} : ${x("Please fill all the Mandatory fields in")} ${a||""}: ${d.join(", ")}`,"error",1e4);let W=!1;g&&(!he||(It=e.row)!=null&&It.isMatNoChanged||(_t=e.row)!=null&&_t.isMatDescChanged)&&(W=await gl(e.row.materialNumber,he,(bs=e==null?void 0:e.row)==null?void 0:bs.globalMaterialDescription)),t=g&&!W?"success":"error",t==="success"&&mt("Validation successful","success");const p=v.map(Bt=>Bt.id===e.row.id?{...Bt,validated:g&&!W}:Bt);R(bt(p));const L=js(p);ct(!L),ye(!L)};return N(_l,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",marginRight:"0.5rem",cursor:s&&de?"not-allowed":"pointer"},spacing:.5,children:[n(fs,{title:t==="success"?"Validated Successfully":x(t==="error"?"Validation Failed":"Click to Validate"),children:n(qt,{onClick:o,color:t==="success"?"success":t==="error"?"error":"default",disabled:s&&de,children:t==="error"?n(Si,{}):n(Fi,{})})}),!s&&n(fs,{title:x("Delete Row"),children:n(qt,{onClick:()=>{xt({...rt,data:e,isVisible:!0})},color:"error",children:n(tl,{})})})]})}}],O=(e,t)=>{var o;Yt(ee.filter(r=>En(r)).indexOf(e.target.innerText)!==-1?ee.filter(r=>En(r)).indexOf(e.target.innerText):t),Ut(((o=e==null?void 0:e.target)==null?void 0:o.id)==="AdditionalKey"?"Additional Data":e.target.innerText)},D=e=>{const t={"Sales Org":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},o=d=>{const a=es(d.body);Et(g=>({...g,[e]:a}))},r=d=>console.error(d);ce(`/${ge}/data${t[e]}`,"get",o,r)},G=e=>{Di(e,ee,qs,K,ne,R,Js)},b=e=>{Nr(e,ee,qs,K,ne,R,Js,m)},Z=(e,t,o)=>(r,d)=>{var A,y,k;let a={},g="",T="";o==="Purchasing"||o==="Costing"?(a={materialNo:t==null?void 0:t.Material,plant:t==null?void 0:t.Plant},T=t==null?void 0:t.Plant,g=`/${ge}/data/displayLimitedPlantData`):o==="Accounting"?(a={materialNo:t==null?void 0:t.Material,valArea:t==null?void 0:t.ValArea},T=t==null?void 0:t.ValArea,g=`/${ge}/data/displayLimitedAccountingData`):o==="Sales"&&(a={materialNo:t==null?void 0:t.Material,salesOrg:t==null?void 0:t.SalesOrg,distChnl:t==null?void 0:t.DistrChan},T=`${t==null?void 0:t.SalesOrg}-${t==null?void 0:t.DistrChan}`,g=`/${ge}/data/displayLimitedSalesData`);const W=te=>{var xe,$e,Ae;o==="Purchasing"||o==="Costing"?R(Js({materialID:K,viewID:o,itemID:t==null?void 0:t.Plant,data:(xe=te==null?void 0:te.body)==null?void 0:xe.SpecificPlantDataViewDto[0]})):o==="Accounting"?R(Js({materialID:K,viewID:o,itemID:t==null?void 0:t.ValArea,data:($e=te==null?void 0:te.body)==null?void 0:$e.SpecificAccountingDataViewDto[0]})):o==="Sales"&&R(Js({materialID:K,viewID:o,itemID:`${t==null?void 0:t.SalesOrg}-${t==null?void 0:t.DistrChan}`,data:(Ae=te==null?void 0:te.body)==null?void 0:Ae.SpecificSalesDataViewDto[0]}))},p=()=>{};!((k=(y=(A=C==null?void 0:C[K])==null?void 0:A.payloadData)==null?void 0:y[o])!=null&&k[T])&&ce(g,"post",W,p,a),V(d?e:null)},Ee=()=>ue&&Le&&(ue[Le]||Le==="Additional Data")?Le==="Additional Data"?[n(Bi,{disableCheck:de&&!Un.includes(U==null?void 0:U.requestStatus),materialID:K,selectedMaterialNumber:ks})]:[n(Wi,{disabled:de&&!Un.includes(U==null?void 0:U.requestStatus),selectedMaterialNumber:ks,materialID:K,basicData:ns,setBasicData:vs,dropDownData:At,basicDataTabDetails:ue[Le],allTabsData:ue,activeViewTab:Le,selectedViews:ee,handleAccordionClick:Z,missingValidationPlant:wn,isDisplay:he||de,mandatoryFailedView:hn,moduleName:"Material",missingFields:Wl})]:n(_s,{}),j=e=>{var r,d;const t=((d=(r=e==null?void 0:e.target)==null?void 0:r.value)==null?void 0:d.toUpperCase())||"";gs(null),Kt(0),De&&clearTimeout(De);const o=setTimeout(()=>{Rn(t,!0)},500);Oe(o)},Te=(e,t)=>{const o=H==null?void 0:H.code,r=M==="yes"?"extended":"notExtended";kt(d=>({...d,[e]:t})),e==="Sales Org"&&t?Hl(o,r,t):e==="Plant"&&t&&(al(o,r,t),Fl(o,t,Se["Sales Org"]))},et=(e,t,o)=>{e==="Sales Organization"&&(t?(Xe(r=>r.map((d,a)=>a===o?{...d,salesOrg:t}:d)),_e(t,o).then(r=>{})):Xe(r=>r.map((d,a)=>a===o?{...d,salesOrg:null}:d)))},_e=(e,t,o="",r="")=>new Promise((d,a)=>{Be(p=>({...p,"Distribution Channel":{...p["Distribution Channel"],[t]:!0}}));let g={salesOrg:e==null?void 0:e.code};const T=p=>{Be(A=>({...A,"Distribution Channel":{...A["Distribution Channel"],[t]:!1}}));let L=JSON.parse(JSON.stringify(o||pt.current));if(L[t].dc.options=es(p.body),Xe(L),pt.current=L,r){R(Ps({materialID:r==null?void 0:r.id,keyName:"orgData",data:L}));let A=(v==null?void 0:v.length)||[JSON.parse(JSON.stringify(r))],y=A.findIndex(k=>k.id===(r==null?void 0:r.id));A[y].orgData=L,R(bt(A)),d({org:L,material:A[y]})}else d(""),Be(A=>({...A,"Distribution Channel":{...A["Distribution Channel"],[t]:!1}}))},W=p=>{console.error(p),Be(L=>({...L,"Distribution Channel":{...L["Distribution Channel"],[t]:!1}}))};ce(`/${ge}/data/getDistrChan`,"post",T,W,g)}),Ct=(e,t)=>{let o=JSON.parse(JSON.stringify(ne));o[t].dc.value=e,Xe(o)},Tt=e=>{let t=JSON.parse(JSON.stringify(ne));t.splice(e,1),Xe(t)},Qt=(e,t)=>{let o=JSON.parse(JSON.stringify(ne));o[t].plant.value=e,o[t].sloc.value={},o[t].sloc.options=[],o[t].warehouse.value={},o[t].warehouse.options=[],Xe(o),Vl(e,t),pt.current=o},cs=(e,t)=>{var r,d;let o=JSON.parse(JSON.stringify(ne));o[t].sloc.value=e,o[t].warehouse.value={},o[t].warehouse.options=[],Xe(o),pt.current=o,jl((d=(r=o[t])==null?void 0:r.plant)==null?void 0:d.value,e,t)},ds=(e,t)=>{let o=JSON.parse(JSON.stringify(ne));o[t].mrpProfile=e,Xe(o)},Kn=(e,t)=>{let o=JSON.parse(JSON.stringify(ne));o[t].warehouse.value=e,Xe(o)},so=()=>{let e=JSON.parse(JSON.stringify(ne));e.push($t),Xe(e)},zi=e=>{if(!(e!=null&&e.temp)||(e==null?void 0:e.temp)===(Ye==null?void 0:Ye.temp))return;me(!0);let t={decisionTableId:null,decisionTableName:"MDG_MAT_ORGDATA_TEMPLATE_CONFIG",version:"v2",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":(S==null?void 0:S.Region)||Vt.US,"MDG_CONDITIONS.MDG_MAT_TEMPLATE":e.temp||""}],systemFilters:null,systemOrders:null,filterString:null};const o=d=>{var a,g;if(d.statusCode===ze.STATUS_200){me(!1);let T=(g=(a=d==null?void 0:d.data)==null?void 0:a.result[0])==null?void 0:g.MDG_MAT_ORGDATA_TEMPLATE_CONFIG,W=[];T==null||T.forEach((p,L)=>{var $e;let A=JSON.parse(JSON.stringify($t));A.salesOrg={},A.salesOrg.code=p.MDG_MAT_SALES_ORG,A.salesOrg.desc=p.MDG_MAT_SALES_ORG_DESC,A.plant.value={},A.plant.value.code=p.MDG_MAT_PLANT,A.plant.value.desc=p.MDG_MAT_PLANT_DESC;let y=($e=ps==null?void 0:ps.filter(Ae=>Ae.MDG_MAT_SALES_ORG===p.MDG_MAT_SALES_ORG))==null?void 0:$e.map(Ae=>({code:Ae.MDG_MAT_PLANT,desc:Ae.MDG_MAT_PLANT_DESC}));y=y==null?void 0:y.filter((Ae,lt,It)=>lt===It.findIndex(_t=>_t.code===Ae.code)),A.plant.options=y==null?void 0:y.sort((Ae,lt)=>Ae.code-lt.code);let k=ps==null?void 0:ps.filter(Ae=>Ae.MDG_MAT_SALES_ORG===p.MDG_MAT_SALES_ORG&&Ae.MDG_MAT_PLANT===p.MDG_MAT_PLANT),te=k==null?void 0:k.map(Ae=>({code:Ae.MDG_MAT_STORAGE_LOCATION,desc:Ae.MDG_MAT_STORE_LOC_DESC})),xe=k==null?void 0:k.map(Ae=>Ae.MDG_MAT_WAREHOUSE?{code:Ae.MDG_MAT_WAREHOUSE,desc:Ae.MDG_MAT_WAREHOUSE_DESC}:null).filter(Boolean);p.MDG_MAT_STORAGE_LOCATION&&(A.sloc.value={},A.sloc.value.code=p.MDG_MAT_STORAGE_LOCATION,A.sloc.value.desc=p.MDG_MAT_STORE_LOC_DESC),A.sloc.options=te,p.MDG_MAT_WAREHOUSE&&(A.warehouse.value={},A.warehouse.value.code=p.MDG_MAT_WAREHOUSE||"",A.warehouse.value.desc=p.MDG_MAT_WAREHOUSE_DESC||""),A.warehouse.options=xe,W.push(A)}),pt.current=W,Xe(W),Eo(W,0)}else ie("Something went wrong"),me(!1),mt("No Org data found","error")},r=d=>{ie("Something went wrong"),me(!1),mt("No Org data found","error")};Re.environment==="localhost"?ce(`/${on}${ht.INVOKE_RULES.LOCAL}`,"post",o,r,t):ce(`/${on}${ht.INVOKE_RULES.PROD}`,"post",o,r,t)},Eo=async(e,t)=>{t<(e==null?void 0:e.length)&&(await _e(e[t].salesOrg,t),t++,Eo(e,t))},Xi=()=>{const e=rt==null?void 0:rt.data;zt(v==null?void 0:v.filter(t=>{var o;return t.id!==((o=e==null?void 0:e.row)==null?void 0:o.id)})),R(Li(e==null?void 0:e.row.id)),tn(""),R(bt(v==null?void 0:v.filter(t=>{var o;return t.id!==((o=e==null?void 0:e.row)==null?void 0:o.id)}))),v!=null&&v.length?v.filter(t=>{var o,r;return((o=t.params)==null?void 0:o.id)!==((r=e==null?void 0:e.row)==null?void 0:r.id)}).every(t=>t.validated)&&ct(!1):ct(!1),xt({...rt,isVisible:!1})};i.useEffect(()=>{var a,g,T,W,p,L,A;const e=ee==null?void 0:ee.includes((a=m)==null?void 0:a.SALES),t=ee==null?void 0:ee.includes((g=m)==null?void 0:g.SALES_PLANT),o=ee==null?void 0:ee.includes((T=m)==null?void 0:T.STORAGE),r=ee==null?void 0:ee.includes((W=m)==null?void 0:W.STORAGE_PLANT),d=(A=(L=(p=C[K])==null?void 0:p.headerData)==null?void 0:L.orgData)==null?void 0:A.some(y=>{var k,te;return(te=(k=y==null?void 0:y.plant)==null?void 0:k.value)==null?void 0:te.code});e&&!t&&d&&f(y=>{var xe,$e;const k=[...y],te=k.indexOf((xe=m)==null?void 0:xe.SALES);return k.splice(te+1,0,($e=m)==null?void 0:$e.SALES_PLANT),k}),o&&!r&&f(y=>{var xe,$e;const k=[...y],te=k.indexOf((xe=m)==null?void 0:xe.STORAGE);return k.splice(te+1,0,($e=m)==null?void 0:$e.STORAGE_PLANT),k})},[ee,(Lo=(Do=C[K])==null?void 0:Do.headerData)==null?void 0:Lo.orgData]);const To=e=>{!e||!Array.isArray(e)||e.forEach(t=>{var o,r,d,a,g,T,W,p,L,A,y,k,te,xe,$e,Ae;if((r=(o=t.plant)==null?void 0:o.value)!=null&&r.code){if(Ls((a=(d=t.plant)==null?void 0:d.value)==null?void 0:a.code,m.PLANT),(g=t.salesOrg)!=null&&g.code||(W=(T=t.dc)==null?void 0:T.value)!=null&&W.code){const lt=`${((p=t.salesOrg)==null?void 0:p.code)||""}-${((A=(L=t.dc)==null?void 0:L.value)==null?void 0:A.code)||""}`;Ls(lt,m.SALES)}(k=(y=t.warehouse)==null?void 0:y.value)!=null&&k.code&&Ls((xe=(te=t.warehouse)==null?void 0:te.value)==null?void 0:xe.code,m.WAREHOUSE),Pn((Ae=($e=t.plant)==null?void 0:$e.value)==null?void 0:Ae.code)}})};i.useEffect(()=>{if(he){const e=ft==null?void 0:ft.orgData;(e==null?void 0:e.length)>0&&e.some(t=>{var o,r,d,a,g;return((r=(o=t.plant)==null?void 0:o.value)==null?void 0:r.code)&&(((d=t.salesOrg)==null?void 0:d.code)||((g=(a=t.dc)==null?void 0:a.value)==null?void 0:g.code))})&&To(e)}},[ft==null?void 0:ft.orgData]);const Ao=e=>{R(vi(e)),c(e)};i.useEffect(()=>{var e,t;(z==null?void 0:z.page)!==0&&(w===((e=$)==null?void 0:e.CREATE_WITH_UPLOAD)||w===((t=$)==null?void 0:t.CREATE))&&J(),c((z==null?void 0:z.page)||0)},[z==null?void 0:z.page]);const Ki=()=>{B(!X),se&&P(!1)},Yi=()=>{P(!se),X&&B(!1)},En=e=>{if(!ue||!e)return!1;const t=Object.keys(ue).find(d=>d.toLowerCase()===e.toLowerCase());return t?!Object.values(ue[t]).flat().every(d=>d.visibility==="Hidden"):!1};return N("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:N(je,{sx:{position:X?"fixed":"relative",top:X?0:"auto",left:X?0:"auto",right:X?0:"auto",bottom:X?0:"auto",width:X?"100vw":"100%",height:X?"100vh":"auto",zIndex:X?1004:void 0,backgroundColor:X?"white":"transparent",padding:X?"20px":"0",display:"flex",flexDirection:"column",boxShadow:X?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(vt,{variant:"h6",children:x("Material Data")}),N(je,{sx:{display:"flex",alignItems:"center",gap:1},children:[N(Pe,{variant:"contained",color:"primary",size:"small",onClick:()=>{w===$.CREATE&&(I(!0),We([]),re(null),kt({}),Rn("",!0))},disabled:Dt||s,children:["+ ",x("Add")]}),n(fs,{title:x(X?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(qt,{onClick:Ki,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:X?n(vl,{}):n(yl,{})})})]})]}),he&&v&&(v==null?void 0:v.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ml,{rows:v,columns:E,pageSize:50,autoHeight:!1,page:St,rowCount:(z==null?void 0:z.totalElements)||0,rowsPerPageOptions:[50],onRowClick:zn,onCellEditCommit:Ft,onPageChange:e=>Ao(e),pagination:!0,disableSelectionOnClick:!0,getRowClassName:e=>e.id===K?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:X?"calc(100vh - 150px)":`${Math.min(v.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n(_s,{children:n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ml,{autoHeight:!1,rows:v,columns:E,pageSize:50,page:St,rowsPerPageOptions:[50],onRowClick:zn,onCellEditCommit:Ft,onPageChange:e=>Ao(e),disableSelectionOnClick:!0,getRowClassName:e=>e.id===K?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:X?"calc(100vh - 150px)":`${Math.min(v.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})})]})}),w===$.CREATE||w===$.CREATE_WITH_UPLOAD||Ie!=null&&Ie.ATTRIBUTE_1?K&&Zt&&(v==null?void 0:v.length)>0&&(ts==null?void 0:ts.length)>0&&ue&&((vo=Object.getOwnPropertyNames(ue))==null?void 0:vo.length)>0&&N(je,{sx:{position:se?"fixed":"relative",top:se?0:"auto",left:se?0:"auto",right:se?0:"auto",bottom:se?0:"auto",width:se?"100vw":"100%",height:se?"100vh":"auto",zIndex:se?1004:void 0,backgroundColor:se?"white":"transparent",padding:se?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:se?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(vt,{variant:"h6",children:x("View Details")}),n(fs,{title:x(se?"Exit Zoom":"Zoom In"),sx:{zIndex:"1009"},children:n(qt,{onClick:Yi,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:se?n(vl,{}):n(yl,{})})})]}),N(je,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[N(Ei,{value:Es,onChange:O,className:ut.customTabs,"aria-label":"material tabs",sx:{top:0,position:"sticky",zIndex:998,backgroundColor:ke.background.container,borderBottom:`1px solid ${ke.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:ke.black.graphite,"&.Mui-selected":{color:ke.primary.main,fontWeight:700},"&:hover":{color:ke.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:ke.primary.main,height:"3px"}},children:[ee&&ne.length>0&&(ee==null?void 0:ee.length)>0?ee==null?void 0:ee.map((e,t)=>En(e)&&n(xl,{label:x(e)},t)):n(_s,{}),n(xl,{label:x("Additional Data"),id:"AdditionalKey"},"Additional data")]}),n(je,{sx:{padding:2,marginTop:2,flexGrow:1,overflow:"auto",height:se?"calc(100vh - 180px)":"auto"},children:(v==null?void 0:v.length)>0&&Ee()}),(!s||he&&!de||de&&Un.includes(U==null?void 0:U.requestStatus))&&n(je,{sx:{borderTop:"1px solid #e0e0e0",padding:"16px"},children:n(Hi,{activeTab:Es,submitForApprovalDisabled:!js(_),filteredButtons:dl,validateMaterials:Pl,workFlowLevels:Nt,showWfLevels:cl,childRequestHeaderData:(yo=C==null?void 0:C[K])==null?void 0:yo.Tochildrequestheaderdata,module:Tn.MAT})})]})]}):n(_s,{}),n(fo,{dialogState:ss,openReusableDialog:Xn,closeReusableDialog:In,dialogTitle:"Warning",dialogMessage:Cn,showCancelButton:!1,handleOk:Zl,handleDialogConfirm:In,dialogOkText:"OK",dialogSeverity:"danger"}),jt&&n($n,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Mn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:N(je,{sx:{width:"600px !important"},children:[N(kn,{sx:{backgroundColor:ot.palette.primary.light,marginBottom:".5rem"},children:[n(sl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:x("Select Views")})]}),n(zs,{sx:{paddingBottom:".5rem"},children:N(je,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(ml,{size:"small",multiple:!0,fullWidth:!0,options:Us||[],disabled:s,disableCloseOnSelect:!0,value:ee==null?void 0:ee.filter(e=>!Ti.includes(e)),onChange:(e,t)=>{Ie!=null&&Ie.requestId||(f([Y,...t.filter(o=>o!==Y)]),Ft({id:Rs,field:yn.VIEWS,value:t}))},getOptionDisabled:e=>e===Y,renderOption:(e,t,{selected:o})=>N("li",{...e,children:[n(Rl,{checked:o,sx:{marginRight:1}}),t]}),renderTags:(e,t)=>e.map((o,r)=>{const{key:d,...a}=t({index:r});return n(Ai,{label:o,...a,disabled:o===Y||s},d)}),renderInput:e=>n(ln,{...e,label:x("Select Views")})}),n(Pe,{variant:"contained",size:"small",onClick:()=>El(),disabled:s,children:x("Select all")})]})}),n(Xs,{children:n(Pe,{onClick:()=>{ls(!1),Ft({id:Rs,field:"views",value:ee})},variant:"contained",children:x("Ok")})})]})}),$s&&N($n,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:Mn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[N(kn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:ot.palette.primary.light},children:[n(sl,{fontSize:"small"}),n("span",{children:x("Select Org Data")}),n(je,{sx:{position:"absolute",right:"7%",width:"15%"},children:n(ml,{options:oe.filter(e=>e.region===(S==null?void 0:S.Region)),value:Ye,size:"small",disabled:s,isOptionEqualToValue:(e,t)=>e.region===t.region,onChange:(e,t)=>{Rt(t),zi(t)},getOptionLabel:e=>e==null?void 0:e.temp,renderInput:e=>n(ln,{...e,label:x("Select Template"),sx:{minWidth:165}}),sx:{"& .MuiAutocomplete-popper":{minWidth:250}}})}),n(qt,{onClick:Mn,sx:{position:"absolute",right:15},children:n(pi,{})})]}),n(zs,{sx:{padding:0},children:n(Ci,{component:Oi,children:N(bi,{children:[n(Ni,{children:N(Dl,{children:[n(qe,{align:"center",children:x("S NO.")}),N(qe,{align:"center",children:[x("Sales Org"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[x("Distribution Channel"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[x("Plant"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),N(qe,{align:"center",children:[x("Storage Location"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),(S==null?void 0:S.Region)!=="EUR"&&N(qe,{align:"center",children:[x("Warehouse"),n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"})]}),n(qe,{align:"center",children:x("MRP Profile")}),ne.length>1&&n(qe,{align:"center",children:x("Action")})]})}),n(mi,{children:ne.map((e,t)=>{var o,r,d,a,g,T,W,p,L,A,y,k,te,xe,$e,Ae,lt,It,_t,bs,Bt,Ns,Je,Wt,ms,Fs,Yn,Zn,Qn,Go;return N(Dl,{sx:{padding:"12px",opacity:s?.5:1,pointerEvents:s?"none":"auto"},children:[n(qe,{children:n(vt,{variant:"body2",children:t+1})}),n(qe,{children:n(gt,{options:At["Sales Organization"],value:e.salesOrg,onChange:wt=>et("Sales Organization",wt,t),placeholder:x("Select Sales Org"),minWidth:165,listWidth:215,title:((o=e==null?void 0:e.salesOrg)==null?void 0:o.code)+` - ${(r=e==null?void 0:e.salesOrg)==null?void 0:r.desc}`,disabled:!Dn(Ln.salesOrg,ee)})}),n(qe,{children:n(gt,{options:((d=e.dc)==null?void 0:d.options)||[],isLoading:((a=Hs["Distribution Channel"])==null?void 0:a[t])||!1,value:(g=e.dc)==null?void 0:g.value,onChange:wt=>Ct(wt,t),placeholder:x("Select DC"),disabled:!Dn(Ln.distributionChannel,ee),minWidth:165,listWidth:215,title:((W=(T=e==null?void 0:e.dc)==null?void 0:T.value)==null?void 0:W.code)+` - ${(L=(p=e==null?void 0:e.dc)==null?void 0:p.value)==null?void 0:L.desc}`})}),n(qe,{children:n(gt,{options:At.PlantNotExtended||[],value:(A=e.plant)==null?void 0:A.value,onChange:wt=>Qt(wt,t),placeholder:x("Select Plant"),disabled:!Dn(Ln.plant,ee),minWidth:165,listWidth:215,title:((k=(y=e==null?void 0:e.plant)==null?void 0:y.value)==null?void 0:k.code)+` - ${(xe=(te=e==null?void 0:e.plant)==null?void 0:te.value)==null?void 0:xe.desc}`})}),n(qe,{children:n(gt,{options:($e=e==null?void 0:e.sloc)==null?void 0:$e.options,value:(Ae=e==null?void 0:e.sloc)==null?void 0:Ae.value,isLoading:((lt=Hs["Storage Location"])==null?void 0:lt[t])||!1,onChange:wt=>cs(wt,t),placeholder:x("Select Sloc"),disabled:!Dn(Ln.storage,ee),minWidth:165,listWidth:215,title:((_t=(It=e==null?void 0:e.sloc)==null?void 0:It.value)==null?void 0:_t.code)+` - ${(Bt=(bs=e==null?void 0:e.sloc)==null?void 0:bs.value)==null?void 0:Bt.desc}`})}),(S==null?void 0:S.Region)!=="EUR"&&n(qe,{children:n(gt,{options:((Ns=e==null?void 0:e.warehouse)==null?void 0:Ns.options)||[],value:(Je=e==null?void 0:e.warehouse)==null?void 0:Je.value,isLoading:((Wt=Hs.Warehouse)==null?void 0:Wt[t])||!1,onChange:wt=>Kn(wt,t),disabled:!Dn(Ln.warehouse,ee),placeholder:x("Select Warehouse"),minWidth:165,listWidth:215,title:((Fs=(ms=e==null?void 0:e.warehouse)==null?void 0:ms.value)==null?void 0:Fs.code)+` - ${(Zn=(Yn=e==null?void 0:e.warehouse)==null?void 0:Yn.value)==null?void 0:Zn.desc}`})}),n(qe,{children:n(gt,{options:At["Mrp Profile"]||[],value:e.mrpProfile,onChange:wt=>ds(wt,t),placeholder:x("Select MRP Profile"),disabled:!Dn(Ln.mrpProfile,ee),isOptionDisabled:wt=>{var sn,Uo;if(t===0)return!1;const xn=(Uo=(sn=ne[t].plant)==null?void 0:sn.value)==null?void 0:Uo.code;if(!xn)return!1;const Bs=ne.slice(0,t).find(Zi=>{var $o,ko;return((ko=($o=Zi.plant)==null?void 0:$o.value)==null?void 0:ko.code)===xn});return Bs&&Bs.mrpProfile?wt.code!==Bs.mrpProfile.code:!1},minWidth:165,listWidth:215,title:((Qn=e==null?void 0:e.mrpProfile)==null?void 0:Qn.code)+` - ${(Go=e==null?void 0:e.mrpProfile)==null?void 0:Go.desc}`})}),ne.length>1&&N(qe,{align:"right",children:[n(qt,{size:"small",color:"primary",onClick:()=>{ol(!0),jn({orgRowLength:ne.length,copyFor:t});const wt=ne.filter(xn=>{var Bs,sn;return(sn=(Bs=xn.plant)==null?void 0:Bs.value)==null?void 0:sn.code}).map(xn=>{var Bs,sn;return(sn=(Bs=xn.plant)==null?void 0:Bs.value)==null?void 0:sn.code});M==="yes"&&b(wt)},style:{display:t===0?"none":"inline-flex"},children:n(ro,{})}),n(qt,{size:"small",color:"error",onClick:()=>Tt(t),children:n(tl,{})})]})]},t)})})]})})}),N(Xs,{sx:{justifyContent:"flex-end",gap:.5},children:[N(Pe,{onClick:so,variant:"contained",disabled:!Ws||s,children:["+ ",x("Add")]}),n(fs,{title:Ws?"":x("Please fill all the fields of first row at least"),arrow:!0,children:n("span",{children:n(Pe,{onClick:()=>{var e,t;if(is(!1),(t=(e=ne[0].plant)==null?void 0:e.value)!=null&&t.code){To(ne),Ft({id:Rs,field:"orgData",value:ne}),Jl(ne);const o=v==null?void 0:v.map(r=>r.id===K?{...r,orgData:ne}:r);if(R(bt(o)),M==="no"){const r=ne.filter(d=>{var a,g;return(g=(a=d.plant)==null?void 0:a.value)==null?void 0:g.code}).map(d=>{var a,g;return(g=(a=d.plant)==null?void 0:a.value)==null?void 0:g.code});r.length>0&&G(r)}Rt(null)}},variant:"contained",disabled:!Ws||s,tooltip:Ws?"":x("Please fill all the fields of first row at least"),children:x("Apply")})})})]})]}),ve&&N($n,{fullWidth:!0,open:!0,maxWidth:"lg",sx:{"&::webkit-scrollbar":{width:"1px"}},children:[n(kn,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:"0.75rem 1rem",background:e=>e.palette.primary.light,borderBottom:"1px solid #d6d6f0"},children:N(je,{sx:{display:"flex",alignItems:"center"},children:[n(Tc,{sx:{mr:1,color:ot.palette.primary.dark}}),n(vt,{variant:"h6",sx:{fontWeight:600,color:ot.palette.primary.dark},children:x("Add New Material")})]})}),N(zs,{sx:{padding:".5rem 1rem",alignItems:"center",justifyContent:"center",margin:"0px 25px"},children:[N(Cr,{component:"fieldset",sx:{paddingBottom:"2%"},children:[n(vt,{sx:{padding:"15px 0px",fontWeight:"600",fontSize:"15px",color:ot.palette.primary.dark},children:x("How would you like to proceed?")}),N(Or,{row:!0,"aria-label":"profit-center-number",name:"profit-center-number",value:M,onChange:e=>Ge(e.target.value),children:[n(jo,{value:"yes",control:n(Vo,{}),label:x("With Reference")}),n(jo,{value:"no",control:n(Vo,{}),label:x("Without Reference")})]})]}),N(Mt,{container:!0,spacing:2,children:[n(Mt,{item:!0,xs:12,children:N(Mt,{container:!0,spacing:2,children:[n(Mt,{item:!0,xs:3,children:n(gt,{options:Il||[],value:Se[Ms.MATERIAL_TYPE]||"",onChange:e=>{kt(t=>({...t,[Ms.MATERIAL_TYPE]:e}))},placeholder:x("Select Material Type"),minWidth:180,listWidth:266,disabled:(ae==null?void 0:ae.length)||M==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]})})}),n(Mt,{item:!0,xs:3,children:n(gt,{options:bn,value:q||H,onChange:e=>{re(e),gs(e),e||j(e)},minWidth:180,listWidth:266,placeholder:x("Select Material"),disabled:(ae==null?void 0:ae.length)||M==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]}),handleInputChange:j,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},isLoading:Hs["Material No"]})}),Ve==null?void 0:Ve.slice(0,2).map(e=>n(Mt,{item:!0,xs:3,children:n(gt,{options:(At==null?void 0:At[e])||[],value:Se[e]||"",onChange:t=>{Te(e,t)},placeholder:x(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ae==null?void 0:ae.length)||M==="no",isLoading:Hs[e]})},e))]})}),n(Mt,{item:!0,xs:12,children:N(Mt,{container:!0,spacing:2,alignItems:"center",children:[n(Mt,{item:!0,xs:3,children:n(gt,{options:(At==null?void 0:At[Ve[2]])||[],value:Se[Ve[2]]||"",onChange:e=>{kt(t=>({...t,[Ve[2]]:e}))},placeholder:x(`Select ${Ve[2]}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ae==null?void 0:ae.length)||M==="no",isLoading:Hs["Distribution Channel"]===!0})}),Ve==null?void 0:Ve.slice(3).map(e=>n(Mt,{item:!0,xs:3,children:n(gt,{options:(At==null?void 0:At[e])||[],value:Se[e]||"",onChange:t=>{kt(o=>({...o,[e]:t}))},placeholder:x(`Select ${e}`),minWidth:180,listWidth:306,sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}},disabled:(ae==null?void 0:ae.length)||M==="no",isLoading:Hs[e]===!0})},e)),(_==null?void 0:_.length)>0&&N(_s,{children:[n(Mt,{item:!0,xs:1,sx:{textAlign:"center"},children:n(vt,{variant:"body1",sx:{fontWeight:"bold",color:"gray"},children:"OR"})}),n(Mt,{item:!0,xs:3,children:n(gt,{options:_.map(e=>({...e,code:e.lineNumber,desc:""})),value:ae[0],onChange:e=>{We(e?[e]:[]),kt({}),re(null),gs(null)},minWidth:180,listWidth:266,placeholder:x("Select Material Line Number"),disabled:(H==null?void 0:H.code)||M==="no",getOptionLabel:e=>e!=null&&e.desc?`${e.code} - ${e.desc}`:(e==null?void 0:e.code)||"",renderOption:(e,t)=>N("li",{...e,children:[n("strong",{children:t==null?void 0:t.code}),t!=null&&t.desc?` - ${t==null?void 0:t.desc}`:""]}),sx:{minWidth:270,"& .MuiAutocomplete-popper":{minWidth:306}}})})]})]})})]})]}),N(Xs,{sx:{display:"flex",justifyContent:"end"},children:[n(Pe,{sx:{width:"max-content",textTransform:"capitalize"},onClick:()=>{I(!1),kt({})},variant:"outlined",children:x("Cancel")}),n(Pe,{type:"save",disabled:!(ae!=null&&ae.length||H!=null&&H.code)&&M==="yes",onClick:Tl,variant:"contained",children:x("Proceed")})]})]}),(rt==null?void 0:rt.isVisible)&&N(ao,{isOpen:rt==null?void 0:rt.isVisible,titleIcon:n(tl,{size:"small",color:"error",sx:{fontSize:"20px"}}),Title:x("Delete Row")+"!",handleClose:()=>xt({...rt,isVisible:!1}),children:[n(zs,{sx:{mt:2},children:x(_i.DELETE_MESSAGE)}),N(Xs,{children:[n(Pe,{variant:"outlined",size:"small",sx:{...Ri},onClick:()=>xt({...rt,isVisible:!1}),children:x(Po.CANCEL)}),n(Pe,{variant:"contained",size:"small",sx:{...Ii},onClick:Xi,children:x(Po.DELETE)})]})]}),ll&&n(Ji,{open:ll,onClose:()=>ol(!1),title:uo.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:qs,lengthOfOrgRow:il,materialID:K,orgRows:ne}),un&&n($l,{openSnackBar:Bn,alertMsg:un,alertType:As,handleSnackBarClose:()=>dn(!1)}),n(qn,{blurLoading:rs,loaderMessage:Ze}),n(co,{})]})},Rc=({openSearchMat:U,setOpenSearchMat:ut,AddCopiedMaterial:ie})=>{const[R,ot]=i.useState(!1),tt=le(J=>J.AllDropDown.dropDown),st={Extend:[{key:"Material Type",options:Il},{key:"Material Number",options:[]},{key:"Plant",options:[]},{key:"Sales Org",options:[]},{key:"Distribution Channel",options:[]},{key:"Storage Location",options:[]},{key:"Division",options:[]}]},F=(J,He="0",S)=>{var z,C,Qe,_,be,pe,ue,Ot,Ie,Fe,it,at;const w={materialNo:((C=(z=J==null?void 0:J["Material Number"])==null?void 0:z.map(Ne=>Ne.code))==null?void 0:C.join(","))??"",division:((_=(Qe=J==null?void 0:J.Division)==null?void 0:Qe.map(Ne=>Ne.code))==null?void 0:_.join(","))??"",plant:((pe=(be=J==null?void 0:J.Plant)==null?void 0:be.map(Ne=>Ne.code))==null?void 0:pe.join(","))??"",salesOrg:((Ot=(ue=J==null?void 0:J["Sales Org"])==null?void 0:ue.map(Ne=>Ne.code))==null?void 0:Ot.join(","))??"",distrChan:((Fe=(Ie=J==null?void 0:J["Distribution Channel"])==null?void 0:Ie.map(Ne=>Ne.code))==null?void 0:Fe.join(","))??"",storageLocation:((at=(it=J==null?void 0:J["Storage Location"])==null?void 0:it.map(Ne=>Ne.code))==null?void 0:at.join(","))??"",top:200,skip:He},Q=Ne=>{var nt;if((Ne==null?void 0:Ne.statusCode)===ze.STATUS_200){const de=(nt=Ne==null?void 0:Ne.body)==null?void 0:nt.map(he=>{if(he.Views){const X=he.Views.split(",").map(B=>B.trim()).filter(B=>!go.includes(B)).join(",");return{...he,Views:X}}return he});ie(de||[]),S==null||S(de||[]),ot(!1)}},Re=()=>{ot(!1),S==null||S([])};ot(!0),ce(`/${ge}${ht.DATA.GET_EXTEND_SEARCH_SET}`,"post",Q,Re,w)};return N(_s,{children:[n(ic,{open:U,onClose:()=>ut(!1),parameters:st.Extend,onSearch:(J,He,S)=>F(J,He,S),templateName:"Extend",name:"Extend",allDropDownData:tt,buttonName:"Search"}),n(qn,{blurLoading:R})]})};function Ic(){const U=pn(),{customError:ut}=Ul(),ie=le(tt=>tt.tabsData.allTabsData),R=i.useCallback(tt=>{const st=J=>{var He;(J==null?void 0:J.statusCode)===((He=ze)==null?void 0:He.STATUS_200)?U(Ll({matType:tt,views:(J==null?void 0:J.body)||[]})):U(Ll({matType:tt,views:[]}))},F=J=>{ut(J)};ce(`/${ge}${ht.DATA.GET_VIEWS_FOR_MAT}=${tt}`,"get",st,F)},[U]),ot=i.useCallback(tt=>{if(!ie||!tt)return!1;const st=Object.keys(ie).find(He=>He.toLowerCase()===tt.toLowerCase());return st?!Object.values(ie[st]).flat().every(He=>He.visibility===Qo.HIDDEN):!1},[ie]);return{getViews:R,checkForHiddenView:ot}}function Mc(U){if(!U)return!1;const ut=["salesOrg","dc","plant","sloc","warehouse"];for(const ie of ut){if(!U[ie])return!1;if(ie==="salesOrg"){if(!U[ie].code)return!1}else if(!U[ie].value||!U[ie].value.code)return!1}return!0}const el=()=>n("span",{style:{color:ke.error.deepRed,marginLeft:2},children:"*"}),xc=ni(()=>({customTabs:{"& .MuiTabs-scroller":{overflowX:"auto !important",overflowY:"hidden !important"}}})),as={NOT_EXTENDED:"notExtended",EXTENDED:"Extended"},Dc=U=>{var In,Mn,El,Tl,Al;const ut=xc(),{customError:ie}=Ul(),R=pn(),{fetchMaterialFieldConfig:ot}=ho(),{getNextDisplayDataForCreate:tt}=Gi(),{fetchValuationClassData:st}=Ui(),F=le(s=>s.payload.payloadData),J=F==null?void 0:F.RequestType,He=le(s=>s.applicationConfig),S=le(s=>s.paginationData),w=le(s=>s.payload),Q=le(s=>s.request.materialRows),Re=le(s=>{var l;return((l=s.materialDropDownData)==null?void 0:l.dropDown)||{}}),z=le(s=>s.tabsData.allTabsData);let C=le(s=>s.userManagement.taskData);const Qe=le(s=>s.tabsData.allMaterialFieldConfigDT),{checkForHiddenView:_}=Ic(),be=nl(),pe=new URLSearchParams(be.search),ue=pe.get("RequestId"),Ot=pe.get("RequestType"),Ie=pe.get("reqBench"),{showSnackbar:Fe}=ii(),[it,at]=i.useState(0),[Ne,nt]=i.useState(null),[de,he]=i.useState(null),X="Basic Data",[B,se]=i.useState([X]),[P,St]=i.useState([]),[c,De]=i.useState(Q||[]),Oe=le(s=>s.selectedSections.selectedSections),[Jt,V]=i.useState(!1),[Y,ee]=i.useState(!1),[f,rt]=i.useState(""),[xt,v]=i.useState([]),[zt,ts]=i.useState(0),[Dt,ct]=i.useState({code:"",desc:""}),[Fn,Ds]=i.useState(!1),[ss,dt]=i.useState(""),[Cn,On]=i.useState(""),{fetchDataAndDispatch:Ls}=ki(),[bn,Xt]=i.useState(!0),[rn,Kt]=i.useState(c.length+1),[q,gs]=i.useState(!1),[Ue,hs]=i.useState(!1),[we,ye]=i.useState(0),[yt,Ss]=i.useState(Q.length>0),[Es,Yt]=i.useState({}),[Zt,Me]=i.useState({}),[ns,vs]=i.useState([]),[At,Et]=i.useState({}),[Ks,Ts]=i.useState([]),[Lt,ys]=i.useState(!1),[Gt,Gs]=i.useState(""),[Us,Ys]=i.useState(0),[jt,ls]=i.useState(m.BASIC_DATA),[Rs,os]=i.useState(!1),[Le,Ut]=i.useState(null),[$s,is]=i.useState([]),$t=le(s=>s.request.salesOrgDTData),ne=(In=w==null?void 0:w[Le])==null?void 0:In.headerData,Xe=(F==null?void 0:F.Region)===Vt.EUR?{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null}:{id:0,salesOrg:null,dc:{value:null,options:[]},plant:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null},[ve,I]=i.useState([Xe]),[H,re]=i.useState([]),[M,Ge]=i.useState({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:{value:null,options:[]}}),[ae,We]=i.useState(null),[K,Ke]=i.useState(!1),[ft,As]=i.useState({}),[cn,Bn]=i.useState("success"),dn=(Mn=w==null?void 0:w[Le])==null?void 0:Mn.payloadData,{getDtCall:wn,dtData:Pt}=Vi(),[un,Vn]=i.useState(!1),[ps,Nn]=i.useState([]),[ks,an]=i.useState(""),[Cs,Is]=i.useState([]),{getDynamicWorkflowDT:fn}=wi(),[x,gn]=i.useState(!1),[Ce,hn]=i.useState(!1),[Zs,fe]=i.useState(!1),[oe,Ye]=i.useState(""),[Rt,rs]=i.useState(!1),[me,Ze]=i.useState({"Sales Organization":!1,"Distribution Channel":{},Plant:{},"Storage Location":{},warehouse:{},"Mrp Profile":!1}),[Os,pt]=i.useState(!1),[Ws,Qs]=i.useState(0),{fetchTabSpecificData:qs}=$i(),{getContryBasedOnPlant:mn}=qi({doAjax:ce,customError:ie,fetchDataAndDispatch:Ls,destination_MaterialMgmt:ge}),{t:Ve}=Gl(),{checkValidation:Se}=Pi(w,Qe,B),{extendFilteredButtons:kt,showWfLevels:ll}=fc(C,He,on,Mi),ol=oi(kt,[An.HANDLE_SUBMIT_FOR_APPROVAL,An.HANDLE_SAP_SYNDICATION,An.HANDLE_SUBMIT_FOR_REVIEW,An.HANDLE_SUBMIT]),il=s=>{!s||!Array.isArray(s)||s.forEach(l=>{var u,h,E,O,D,G,b,Z,Ee,j,Te,et,_e,Ct,Tt,Qt;if((h=(u=l.plant)==null?void 0:u.value)!=null&&h.code){if(qs((O=(E=l.plant)==null?void 0:E.value)==null?void 0:O.code,m.PLANT),(D=l.salesOrg)!=null&&D.code||(b=(G=l.dc)==null?void 0:G.value)!=null&&b.code){const cs=`${((Z=l.salesOrg)==null?void 0:Z.code)||""}-${((j=(Ee=l.dc)==null?void 0:Ee.value)==null?void 0:j.code)||""}`;qs(cs,m.SALES)}(et=(Te=l.warehouse)==null?void 0:Te.value)!=null&&et.code&&qs((Ct=(_e=l.warehouse)==null?void 0:_e.value)==null?void 0:Ct.code,m.WAREHOUSE),mn((Qt=(Tt=l.plant)==null?void 0:Tt.value)==null?void 0:Qt.code)}})};i.useEffect(()=>{const l=(ve==null?void 0:ve.map(u=>Mc(u))).every(u=>u===!0);rs(l)},[ve]);const jn=s=>{if(!s||!Array.isArray(s))return[];let l=(F==null?void 0:F.Region)===Vt.EUR?s==null?void 0:s.filter(u=>u!==m.WAREHOUSE&&u!==m.WORKSCHEDULING&&u!==m.WORK_SCHEDULING):[...s];return l.sort((u,h)=>u===m.BASIC_DATA?-1:h===m.BASIC_DATA?1:0),l};i.useEffect(()=>{var s,l,u,h;if(Pt&&((s=Pt==null?void 0:Pt.customParam)==null?void 0:s.dt)===Ol.MDG_ORG_ELEMENT_DEFAULT_VALUE){const E=ui((h=(u=(l=Pt==null?void 0:Pt.data)==null?void 0:l.result)==null?void 0:u[0])==null?void 0:h.MDG_ORG_ELEMENT_DEFAULT_VALUE_ACTION_TYPE);R(ai({data:E}))}},[Pt]);const Hs=async()=>{var s,l;try{const u=await fn(J,F==null?void 0:F.Region,"",(l=(s=w[Le])==null?void 0:s.Tochildrequestheaderdata)==null?void 0:l.MaterialGroupType,C==null?void 0:C.ATTRIBUTE_3,"v4","MDG_MAT_DYNAMIC_WF_DT",Tn.MAT);Is(u)}catch(u){ie(u)}};i.useEffect(()=>{J&&(F!=null&&F.Region)&&Le&&(C!=null&&C.ATTRIBUTE_3)&&Hs()},[J,F==null?void 0:F.Region,Le,C==null?void 0:C.ATTRIBUTE_3]),i.useEffect(()=>{var s,l,u,h,E,O,D,G;(h=(u=(l=(s=w[Le])==null?void 0:s.payloadData)==null?void 0:l[m.CLASSIFICATION])==null?void 0:u.basic)!=null&&h.Classtype&&fi((G=(D=(O=(E=w[Le])==null?void 0:E.payloadData)==null?void 0:O[m.CLASSIFICATION])==null?void 0:D.basic)==null?void 0:G.Classtype,R)},[Le]),i.useEffect(()=>{var s,l,u,h,E,O,D,G;if(De(Q),Ss((Q==null?void 0:Q.length)>0),(Q==null?void 0:Q.length)>0){let b=null;Le&&(b=Q.find(Te=>Te.id===Le.toString()||Te.id===Le)),!b&&oe&&(b=Q.find(Te=>Te.materialNumber===oe)),b||(b=Q[0]),Ut(b==null?void 0:b.id),mt(((s=b==null?void 0:b.materialType)==null?void 0:s.code)||(b==null?void 0:b.materialType)),ye(0),Ye(b==null?void 0:b.materialNumber),ls(((u=(l=b==null?void 0:b.views)==null?void 0:l.filter(Te=>_(Te)))==null?void 0:u[0])||m.BASIC_DATA),se((h=b==null?void 0:b.views)!=null&&h.length?jn(b==null?void 0:b.views):jn([X]));const Z=ri(w),Ee=ci(Z);let j=JSON.parse(JSON.stringify(Ee));R(di(j)),R(Wn({keyName:"selectedMaterialID",data:b==null?void 0:b.id})),(O=(E=w==null?void 0:w[b==null?void 0:b.id])==null?void 0:E.Tochildrequestheaderdata)!=null&&O.ChildRequestId&&R(Wn({keyName:"childRequestId",data:(G=(D=w==null?void 0:w[b==null?void 0:b.id])==null?void 0:D.Tochildrequestheaderdata)==null?void 0:G.ChildRequestId}))}},[Q,z]),i.useEffect(()=>{(c==null?void 0:c.length)===0&&V(!1)},[c]),i.useEffect(()=>{js(Q)&&(Q!=null&&Q.length)||Ie||ue?(U.setCompleted([!0,!0]),U==null||U.setIsAttachmentTabEnabled(!0)):(U.setCompleted([!0,!1]),U==null||U.setIsAttachmentTabEnabled(!1))},[Q]),i.useEffect(()=>{w!=null&&w.isAnyMandatoryFieldEmpty?(U==null||U.setIsAttachmentTabEnabled(!1),V(!0)):V(!1)},[w==null?void 0:w.isAnyMandatoryFieldEmpty]),i.useEffect(()=>{["Sales Organization","Mrp Profile"].forEach(jl)},[]),i.useEffect(()=>{if(ue){const s=ne==null?void 0:ne.orgData;(s==null?void 0:s.length)>0&&s.some(l=>{var u,h,E,O,D;return((h=(u=l.plant)==null?void 0:u.value)==null?void 0:h.code)&&(((E=l.salesOrg)==null?void 0:E.code)||((D=(O=l.dc)==null?void 0:O.value)==null?void 0:D.code))})&&il(s)}},[ne==null?void 0:ne.orgData]),i.useEffect(()=>{var E,O,D,G;const s=B==null?void 0:B.includes((E=m)==null?void 0:E.SALES),l=B==null?void 0:B.includes((O=m)==null?void 0:O.SALES_PLANT),u=B==null?void 0:B.includes((D=m)==null?void 0:D.STORAGE),h=B==null?void 0:B.includes((G=m)==null?void 0:G.STORAGE_PLANT);s&&!l&&se(b=>{var j,Te;const Z=[...b],Ee=Z.indexOf((j=m)==null?void 0:j.SALES);return Z.splice(Ee+1,0,(Te=m)==null?void 0:Te.SALES_PLANT),Z}),u&&!h&&se(b=>{var j,Te;const Z=[...b],Ee=Z.indexOf((j=m)==null?void 0:j.STORAGE);return Z.splice(Ee+1,0,(Te=m)==null?void 0:Te.STORAGE_PLANT),Z})},[B]);const Be=()=>{_n()},Pn=(s="",l=!1)=>{var O,D,G,b;const u={materialNo:s??"",top:500,skip:l?0:zt,salesOrg:((D=(O=$t==null?void 0:$t.uniqueSalesOrgList)==null?void 0:O.map(Z=>Z.code))==null?void 0:D.join("$^$"))||""},h=Z=>{(Z==null?void 0:Z.statusCode)===ze.STATUS_200&&(v(l?Z==null?void 0:Z.body:Ee=>[...Ee,...Z==null?void 0:Z.body]),Ds(!1))},E=()=>{Ds(!1)};Ds(!0),ce(`/${ge}${(b=(G=ht)==null?void 0:G.DATA)==null?void 0:b.GET_SEARCH_PARAMS_MATERIAL_NO}`,"post",h,E,u)},Nt=!Un.includes(U==null?void 0:U.requestStatus),kl=s=>{const l=h=>{(h==null?void 0:h.statusCode)===ze.STATUS_200&&vs(h==null?void 0:h.body)},u=h=>{console.error(h,"while fetching the validation data of material number")};ce(`/${ge}/data/getNumberRangeForMaterialType?materialType=${s==null?void 0:s.code}`,"get",l,u)};function rl(s){const l=h=>{var E;if((h==null?void 0:h.statusCode)===ze.STATUS_200){let O=(E=h==null?void 0:h.body)==null?void 0:E.filter(D=>!go.includes(D));O=O==null?void 0:O.map(D=>D==="Storage"?m.STORAGE:D),(F==null?void 0:F.Region)===Vt.EUR&&(O=O==null?void 0:O.filter(D=>D!==m.WAREHOUSE&&D!==m.WORK_SCHEDULING&&D!==m.WORKSCHEDULING)),Ts(O)}},u=h=>{ie(h)};ce(`/${ge}/data/getViewForMaterialType?materialType=${s}`,"get",l,u)}i.useEffect(()=>{Pn()},[]);const cl=((El=ns==null?void 0:ns[1])==null?void 0:El.External)==="X",dl=ns==null?void 0:ns.some(s=>s.ExtNAwock==="X");function mt(s){var h;const l=(F==null?void 0:F.Region)||Vt.US;if(!Qe.some(E=>E[l]&&E[l][s])&&s)ot(s,l);else if(!s)R(Hn({}));else{const E=Qe==null?void 0:Qe.find(O=>(O==null?void 0:O[l])&&(O==null?void 0:O[l][s]));E&&R(Hn((h=E[l][s])==null?void 0:h.allfields))}s&&st(s)}const Ht=s=>{const{id:l,field:u,value:h}=s,E=c.map(O=>O.id===l?{...O,[u]:h}:O);Et({...At,[u]:h}),u===yn.MATERIALTYPE&&(kl(h),rl(h),se([X]),gi([Xe]),R(Ps({materialID:l,keyName:"views",data:[X]})),R(Ps({materialID:l,keyName:"orgData",data:""})),mt(h==null?void 0:h.code)),De(E),R(Ps({materialID:l,keyName:u,data:h}))},Sn=s=>{var l,u,h,E,O,D,G,b,Z,Ee,j,Te;Ut(s.row.id),As(s.row),Ye(s.row.materialNumber),Ts((l=s==null?void 0:s.row)==null?void 0:l.views),mt(((h=(u=s==null?void 0:s.row)==null?void 0:u.materialType)==null?void 0:h.code)||((E=s.row)==null?void 0:E.materialType)),se((O=s==null?void 0:s.row)!=null&&O.views?(D=s.row)==null?void 0:D.views:[X]),I((b=(G=s==null?void 0:s.row)==null?void 0:G.orgData)!=null&&b.length?(Z=s.row)==null?void 0:Z.orgData:[Xe]),ye(0),ls(((Te=(j=(Ee=s.row)==null?void 0:Ee.views)==null?void 0:j.filter(et=>_(et)))==null?void 0:Te[0])||m.BASIC_DATA)},Wl=()=>{ee(!0)},_n=()=>{ee(!1)},Jn=(s,l)=>{l==="backdropClick"||l==="escapeKeyDown"||os(!1)},ul=Us+10,ql=()=>se(jn(Ks)),Hl=s=>{if(Ke(!1),s!=null&&s.length){let l=[...c];s==null||s.forEach(u=>{var D,G,b;const h=u==null?void 0:u.Material;let E={...u},O=(D=w==null?void 0:w[u.id])!=null&&D.payloadData?JSON.parse(JSON.stringify((G=w==null?void 0:w[u.id])==null?void 0:G.payloadData)):"";E.id=h,E.globalMaterialDescription="",E.materialNumber="",E.included=!0,E.lineNumber=ul,E.industrySector=u==null?void 0:u.IndSector,E.materialType=u==null?void 0:u.MatlType,E.materialNumber=u==null?void 0:u.Material,E.globalMaterialDescription=u==null?void 0:u.MaterialDescrption,E.views=u!=null&&u.Views?(b=u==null?void 0:u.Views.split(","))==null?void 0:b.map(Z=>Z.trim()==="Storage"?m.STORAGE:Z.trim()):[X],E.validated=bl.default,l.push(E),R(Nl({materialID:h,data:E,payloadData:O}))}),St(u=>[...u,...l.map(h=>({material:h==null?void 0:h.Material,views:h==null?void 0:h.views}))]),De(l),R(bt(l)),Kt(rn+1),Ys(ul),Ss(!0),Xt(!0)}},al=[{field:"included",headerName:"Included",flex:.5,align:"center",headerAlign:"center",renderCell:s=>{var l;return s!=null&&s.row?n(Rl,{checked:(l=s==null?void 0:s.row)==null?void 0:l.included,disabled:Nt,onChange:u=>{var h;(h=s==null?void 0:s.row)!=null&&h.id&&Ht({id:s.row.id,field:"included",value:u.target.checked})}}):null}},{field:"lineNumber",headerName:"Line Number",flex:.6,editable:J==="Create",align:"center",headerAlign:"center",renderCell:s=>{const u=((Q==null?void 0:Q.findIndex(h=>{var E;return(h==null?void 0:h.id)===((E=s==null?void 0:s.row)==null?void 0:E.id)}))+1)*10;return n("div",{children:u})}},{field:"industrySector",headerName:"Industry Sector",flex:1,align:"center",headerAlign:"center",renderCell:s=>{var l,u,h,E,O;return n(gt,{options:(Re==null?void 0:Re.IndSector)||[],value:(l=s==null?void 0:s.row)==null?void 0:l.industrySector,onChange:D=>Ht({id:s.row.id,field:"industrySector",value:D}),placeholder:"Select Industry Sector",minWidth:"90%",disabled:!0,listWidth:232,title:`${((h=(u=s.row)==null?void 0:u.industrySector)==null?void 0:h.code)||""} - ${((O=(E=s.row)==null?void 0:E.industrySector)==null?void 0:O.desc)||""}`})}},{field:"materialType",headerName:"Material Type",flex:1,align:"center",headerAlign:"center",renderCell:s=>{var l,u,h,E,O;return n(gt,{options:Il||[],value:(l=s==null?void 0:s.row)==null?void 0:l.materialType,onChange:D=>Ht({id:s.row.id,field:"materialType",value:D}),placeholder:"Select Material Type",disabled:!0,minWidth:"90%",listWidth:232,title:`${((h=(u=s.row)==null?void 0:u.materialType)==null?void 0:h.code)||""} - ${((O=(E=s.row)==null?void 0:E.materialType)==null?void 0:O.desc)||""}`})}},{field:"materialNumber",headerName:"Material Number",flex:1,editable:!(!cl&&!dl),align:"center",headerAlign:"center",renderHeader:()=>N("span",{children:["Material Number",n("span",{style:{color:"red"},children:"*"})]}),renderCell:s=>{var l;return n(ln,{fullWidth:!0,placeholder:"Enter Material Number",variant:"outlined",size:"small",name:"material number",value:(l=s==null?void 0:s.row)==null?void 0:l.materialNumber,sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},onChange:(u,h)=>Ht({id:s.row.id,field:"materialNumber",value:h}),disabled:!cl&&!dl})}},{field:"globalMaterialDescription",flex:1,headerName:"Material Description",renderHeader:()=>N("span",{children:["Material Description",n("span",{style:{color:"red"},children:"*"})]}),renderCell:s=>{var l;return n(ln,{fullWidth:!0,placeholder:"Enter Material Description",variant:"outlined",disabled:!0,size:"small",name:"material description",sx:{"& .MuiInputBase-root.Mui-disabled":{"& > input":{WebkitTextFillColor:ke.black.dark,color:ke.black.dark}}},onChange:(u,h)=>Ht({id:s.row.id,field:"globalMaterialDescription",value:h}),value:(l=s==null?void 0:s.row)==null?void 0:l.globalMaterialDescription})},align:"center",headerAlign:"center",editable:!0},{field:"views",headerName:"",flex:1.8,align:"center",headerAlign:"center",renderCell:s=>N(_l,{direction:"row",spacing:0,alignItems:"center",children:[n(Pe,{variant:"contained",size:"small",sx:{minWidth:80},onClick:()=>{var l,u;rl(s.row.materialType),ys(!0),Gs(s.row.id),As(s.row),se((l=s==null?void 0:s.row)!=null&&l.Views?(u=s==null?void 0:s.row)==null?void 0:u.Views:[X])},children:"Views"}),n(Xo,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(Pe,{variant:"contained",size:"small",sx:{minWidth:100},onClick:()=>{var l,u,h,E;os(!0),Gs(s.row.id),I((u=(l=s==null?void 0:s.row)==null?void 0:l.orgData)!=null&&u.length?(h=s.row)==null?void 0:h.orgData:[Xe]),As(s.row),fl((E=s==null?void 0:s.row)==null?void 0:E.materialNumber,as.NOT_EXTENDED),hn(!1)},children:"ORG Data"}),n(Xo,{color:"disabled",fontSize:"small",sx:{mx:.5}}),n(fs,{title:"Click after changing Views or ORG Data",children:n(qt,{onClick:()=>{var l,u;os(!0),Gs(s.row.id),hn(!0),As(s.row),fl((l=s==null?void 0:s.row)==null?void 0:l.materialNumber,as.EXTENDED),Ge(H.find(h=>{var E;return h.id===((E=s.row)==null?void 0:E.id)})||{id:(u=s.row)==null?void 0:u.id,plant:null,salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},mrpProfile:null,warehouse:null})},disabled:Nt,color:"primary",size:"small",children:n(ro,{})})})]})},{field:"action",headerName:"Action",flex:.9,align:"center",headerAlign:"center",renderCell:s=>{let l=hi(s==null?void 0:s.row);return N(_l,{direction:"row",alignItems:"center",sx:{marginLeft:"0.5rem",magrinRight:"0.5rem"},spacing:.5,children:[n(fs,{title:l==="success"?"Validated Successfully":l==="error"?"Validation Failed":"Click to Validate",children:n(qt,{onClick:async h=>{var j,Te,et,_e,Ct,Tt,Qt,cs;if(h.stopPropagation(),((Te=(j=s==null?void 0:s.row)==null?void 0:j.views)==null?void 0:Te.length)>1&&(!((et=s==null?void 0:s.row)!=null&&et.orgData)||((Ct=(_e=s==null?void 0:s.row)==null?void 0:_e.orgData)==null?void 0:Ct.length)===0)){Fe(Ve((Qt=(Tt=xi)==null?void 0:Tt.FILES)==null?void 0:Qt.MISSING_ORG_DATA),"error",1e4);return}const{missingFields:E,viewType:O,isValid:D,plant:G=[]}=Se(s.row.id,((cs=s==null?void 0:s.row)==null?void 0:cs.orgData)||[]);if(Nn(G),We(O),is(E),ye(O?B==null?void 0:B.indexOf(O):0),ls(O||m.BASIC_DATA),E)if(typeof E=="object"&&!Array.isArray(E)){const ds=Object.entries(E).map(([Kn,so])=>`Combination ${Kn}: ${so.join(", ")}`);Fe(`${Ve("Line No")} ${s.row.lineNumber} : ${Ve("Please fill all the Mandatory fields in")} ${O||""}: ${ds.join(" | ")}`,"error",1e4)}else Fe(`${Ve("Line No")} ${s.row.lineNumber} : ${Ve("Please fill all the Mandatory fields in")} ${O||""}: ${E.join(", ")}`,"error",1e4);else l==="success"&&Fe("Validation successful","success");const b=c.map(ds=>ds.id===s.row.id?{...ds,validated:D}:ds);b.every(ds=>ds.validated===!0)&&R(zo(!1)),R(bt(b));const Ee=js(b);V(!Ee)},color:l==="success"?"success":l==="error"?"error":"default",disabled:Nt&&Ie,children:l==="error"?n(Si,{}):n(Fi,{})})}),!ue&&n(fs,{title:"Delete Row",children:n(qt,{onClick:()=>{De(c.filter(h=>h.id!==s.row.id)),R(Li(s.row.id)),R(bt(c.filter(h=>h.id!==s.row.id))),c!=null&&c.length||V(!1)},color:"error",children:n(tl,{})})})]})}}],Fl=async()=>{let s=[...c],l=!0;return dt(!0),On(io.VALIDATING_MATS),new Promise(async(u,h)=>{for(let O=0;O<(c==null?void 0:c.length);O++){const D=c[O],{missingFields:G,viewType:b,isValid:Z,plant:Ee=[]}=Se(D.id,(D==null?void 0:D.orgData)||[],!1,!1,!1);if(Nn(Ee),We(b),ye(b?B==null?void 0:B.indexOf(b):0),ls(b||m.BASIC_DATA),is(G),!Z){if(l=!1,s=s.map(j=>j.id===D.id?{...j,validated:!1}:j),R(bt(s)),G)if(Ut(D.id),Ye(D.materialNumber),typeof G=="object"&&!Array.isArray(G)){const j=Object.entries(G).map(([Te,et])=>`Combination ${Te}: ${et.join(", ")}`);Fe(`Line No ${D.lineNumber} : Please fill all the Mandatory fields in ${b||""}: ${j.join(" | ")}`,"error",1e4)}else Fe(`Line No ${D.lineNumber} : Please fill all the Mandatory fields in ${b||""}: ${G.join(", ")}`,"error",1e4);break}}l?u(!0):h(),dt(!1);const E=js(s);V(!E),Xt(!E),l&&(Fe("Validation successful for all materials.","success"),R(zo(!1)))})},fl=(s,l)=>{Ze(E=>({...E,"Sales Organization":!0}));const u=E=>{if((E==null?void 0:E.statusCode)===ze.STATUS_200){let O;l===as.NOT_EXTENDED?O=es(E.body):O=E.body.length>0?es(E.body):[],Me(D=>({...D,"Sales Organization":O}))}Ze(O=>({...O,"Sales Organization":!1}))},h=()=>{Ze(E=>({...E,"Sales Organization":!1}))};ce(`/${ge}/data/${l===as.NOT_EXTENDED?"getSalesOrgNotExtended":"getSalesOrgExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}`,"get",u,h)},Bl=(s,l,u,h)=>{Ze(G=>({...G,Plant:{...G.Plant,[h]:!0}}));const E=G=>{if((G==null?void 0:G.statusCode)===ze.STATUS_200){let b;l===as.NOT_EXTENDED?b=es(G.body):b=G.body.length>0?es(G.body||[]):[],Me(Z=>({...Z,Plant:b}))}Ze(b=>({...b,Plant:{...b.Plant,[h]:!1}}))},O=()=>{Ze(G=>({...G,Plant:{...G.Plant,[h]:!1}}))},D=u?`&salesOrg=${u.code}`:"";ce(`/${ge}/data/${l===as.NOT_EXTENDED?"getPlantNotExtended":"getPlantExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}${D}`,"get",E,O)},wl=(s,l,u,h)=>{Ze(G=>({...G,warehouse:{...G.warehouse,[h]:!0}}));const E=G=>{if((G==null?void 0:G.statusCode)===ze.STATUS_200){let b;l===as.NOT_EXTENDED?b=es(G.body):b=G.body.length>0?es(G.body||[]):[],Me(Z=>({...Z,warehouse:b}))}Ze(b=>({...b,warehouse:{...b.warehouse,[h]:!1}}))},O=()=>{Ze(G=>({...G,warehouse:{...G.warehouse,[h]:!1}}))},D=u?`&plant=${u.code}`:"";ce(`/${ge}/data/${l===as.NOT_EXTENDED?"getWarehouseNotExtended":"getWarehouseExtended"}?materialNo=${s}&region=${F==null?void 0:F.Region}${D}`,"get",E,O)},Vl=(s,l)=>{var u;ye(B.filter(h=>_(h)).indexOf(s.target.innerText)!==-1?B.filter(h=>_(h)).indexOf(s.target.innerText):l),ls(((u=s==null?void 0:s.target)==null?void 0:u.id)==="AdditionalKey"?"Additional Data":s.target.innerText)},jl=s=>{Ze(E=>({...E,[s]:!0}));const l={"Sales Organization":"/getSalesOrg","Mrp Profile":"/getMRPProfile"},u=E=>{if((E==null?void 0:E.statusCode)===ze.STATUS_200){const O=es(E.body);Me(D=>({...D,[s]:O}))}Ze(O=>({...O,[s]:!1}))},h=E=>{console.error(E),Ze(O=>({...O,[s]:!1}))};ce(`/${ge}/data${l[s]}`,"get",u,h)},gl=s=>{Di(s,B,dn,Le,ve,R,Js)},Pl=(s,l,u)=>(h,E)=>{var j,Te,et;let O={},D="",G="";u==="Purchasing"||u==="Costing"?(O={materialNo:l==null?void 0:l.Material,plant:l==null?void 0:l.Plant},G=l==null?void 0:l.Plant,D=`/${ge}/data/displayLimitedPlantData`):u==="Accounting"?(O={materialNo:l==null?void 0:l.Material,valArea:l==null?void 0:l.ValArea},G=l==null?void 0:l.ValArea,D=`/${ge}/data/displayLimitedAccountingData`):u==="Sales"&&(O={materialNo:l==null?void 0:l.Material,salesOrg:l==null?void 0:l.SalesOrg,distChnl:l==null?void 0:l.DistrChan},G=`${l==null?void 0:l.SalesOrg}-${l==null?void 0:l.DistrChan}`,D=`/${ge}/data/displayLimitedSalesData`);const b=_e=>{var Ct,Tt,Qt;(_e==null?void 0:_e.statusCode)===ze.STATUS_200&&(u==="Purchasing"||u==="Costing"?R(Js({materialID:Le,viewID:u,itemID:l==null?void 0:l.Plant,data:(Ct=_e==null?void 0:_e.body)==null?void 0:Ct.SpecificPlantDataViewDto[0]})):u==="Accounting"?R(Js({materialID:Le,viewID:u,itemID:l==null?void 0:l.ValArea,data:(Tt=_e==null?void 0:_e.body)==null?void 0:Tt.SpecificAccountingDataViewDto[0]})):u==="Sales"&&R(Js({materialID:Le,viewID:u,itemID:`${l==null?void 0:l.SalesOrg}-${l==null?void 0:l.DistrChan}`,data:(Qt=_e==null?void 0:_e.body)==null?void 0:Qt.SpecificSalesDataViewDto[0]})))},Z=()=>{};!((et=(Te=(j=w==null?void 0:w[Le])==null?void 0:j.payloadData)==null?void 0:Te[u])!=null&&et[G])&&ce(D,"post",b,Z,O),he(E?s:null)},Jl=()=>z&&jt&&(z[jt]||jt==="Additional Data")?jt==="Additional Data"?[n(Bi,{disabled:Nt,materialID:Le,selectedMaterialNumber:oe})]:[n(Wi,{disabled:Nt,materialID:Le,basicData:Es,setBasicData:Yt,dropDownData:Zt,allTabsData:z,basicDataTabDetails:z[jt],activeViewTab:jt,selectedViews:B,handleAccordionClick:Pl,missingValidationPlant:ps,selectedMaterialNumber:oe,callGetCountryBasedonSalesOrg:Zs,mandatoryFailedView:ae,missingFields:$s})]:n(_s,{}),zl=s=>{const l=s.target.value;ct({code:l,desc:""}),ts(0),Ne&&clearTimeout(Ne);const u=setTimeout(()=>{Pn(l,!0)},500);nt(u)};i.useEffect(()=>{zt>0&&Pn(Dt==null?void 0:Dt.code)},[zt]);const Xl=(s,l,u)=>{var h;if(s==="Sales Organization"){en(l,u);const E=(h=c==null?void 0:c.find(O=>O.id===Gt))==null?void 0:h.materialNumber;Bl(E,Ce?as.EXTENDED:as.NOT_EXTENDED,l,u)}},en=(s,l,u="",h="")=>(Ze(E=>({...E,"Distribution Channel":{...E["Distribution Channel"],[l]:!0}})),new Promise((E,O)=>{var Z;const D=Ee=>{if(Ee.statusCode===ze.STATUS_200){const j=es(Ee.body);let Te=JSON.parse(JSON.stringify(u||ve));Ce?Ge(et=>({...et,salesOrg:s,dc:{value:null,options:(j==null?void 0:j.length)>0?j:[]}})):(Te[l].salesOrg=s,Te[l].dc.options=j,I(Te))}Ze(j=>({...j,"Distribution Channel":{...j["Distribution Channel"],[l]:!1}})),E(Ee)},G=Ee=>{Ze(j=>({...j,"Distribution Channel":{...j["Distribution Channel"],[l]:!1}})),O(Ee)};let b=(Z=c==null?void 0:c.find(Ee=>Ee.id===Gt))==null?void 0:Z.materialNumber;b&&ce(`/${ge}/data/${Ce?"getDistributionChannelExtended":"getDistributionChannelNotExtended"}?materialNo=${b}&salesOrg=${s==null?void 0:s.code}`,"get",D,G)})),Kl=(s,l)=>{var h;Yl(s,l);const u=(h=c==null?void 0:c.find(E=>E.id===Gt))==null?void 0:h.materialNumber;wl(u,Ce?as.EXTENDED:as.NOT_EXTENDED,s,l)},Yl=(s,l,u="",h)=>{var Z;Ze(Ee=>({...Ee,"Storage Location":{...Ee["Storage Location"],[l]:!0}}));const E=Ee=>{if(Ze(j=>({...j,"Storage Location":{...j["Storage Location"],[l]:!1}})),Ee.statusCode===ze.STATUS_200){const j=es(Ee.body);let Te=JSON.parse(JSON.stringify(u||ve));Ce?Ge(et=>({...et,plant:{value:s,options:[]},sloc:{value:null,options:(j==null?void 0:j.length)>0?j:[]}})):(Te[l].plant.value=s,Te[l].sloc.options=j,I(Te))}if(h){R(Ps({materialID:h==null?void 0:h.id,keyName:"orgData",data:rowOption}));let j=(c==null?void 0:c.length)||[JSON.parse(JSON.stringify(h))],Te=j.findIndex(et=>et.id===(h==null?void 0:h.id));j[Te].orgData=rowOption,R(bt(j))}},O=Ee=>{console.error(Ee),Ze(j=>({...j,"Storage Location":{...j["Storage Location"],[l]:!1}}))};let D=(Z=c.find(Ee=>Ee.id===Gt))==null?void 0:Z.materialNumber;const G=ve[l],b=G!=null&&G.salesOrg?`&salesOrg=${G.salesOrg.code}`:"";D&&ce(`/${ge}/data/${Ce?"getStorageLocationExtended":"getStorageLocationNotExtended"}?materialNo=${D}&region=${F==null?void 0:F.Region}&plant=${s==null?void 0:s.code}${b}`,"get",E,O)},Zl=(s,l)=>{let u=JSON.parse(JSON.stringify(ve));u[l].dc.value=s,I(u)},Rn=s=>{let l=JSON.parse(JSON.stringify(ve));l.splice(s,1),I(l)},hl=(s,l)=>{let u=JSON.parse(JSON.stringify(ve));u[l].sloc.value=s,I(u)},Ql=(s,l)=>{let u=JSON.parse(JSON.stringify(ve));u[l].warehouse.value=s,I(u)},eo=(s,l)=>{let u=JSON.parse(JSON.stringify(ve));u[l].mrpProfile=s,I(u)},to=()=>{let s=JSON.parse(JSON.stringify(ve));s.push({id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}),I(s)},So=(s,l,u,h)=>{var G,b,Z,Ee,j,Te,et,_e,Ct,Tt,Qt;const E={material:ft==null?void 0:ft.materialNumber,wareHouseNumber:((b=(G=s==null?void 0:s.warehouse)==null?void 0:G.value)==null?void 0:b.code)??"",plant:((Ee=(Z=s==null?void 0:s.plant)==null?void 0:Z.value)==null?void 0:Ee.code)??"",salesOrg:((j=s==null?void 0:s.salesOrg)==null?void 0:j.code)??"",storageLocation:((et=(Te=s==null?void 0:s.sloc)==null?void 0:Te.value)==null?void 0:et.code)??"",distributionChannel:((Ct=(_e=s==null?void 0:s.dc)==null?void 0:_e.value)==null?void 0:Ct.code)??"",valArea:((Qt=(Tt=s==null?void 0:s.plant)==null?void 0:Tt.value)==null?void 0:Qt.code)??""},O=cs=>{const ds=mr(cs==null?void 0:cs.body,l,u,h,ft),Kn=J===$.EXTEND_WITH_UPLOAD||Ot===$.EXTEND_WITH_UPLOAD?_r(w,ds):Rr(w,ds);R(Ir({data:Kn})),fe(!Zs)},D=cs=>{ie(cs)};ce(`/${ge}${ht.DATA.COPY_FROM_MATERIAL_ORG_ELMS_ETEXTEND}`,"post",O,D,E)},tn=[{id:1,plant:{value:null,options:[]},salesOrg:null,dc:{value:null,options:[]},sloc:{value:null,options:[]},warehouse:{value:null,options:[]},mrpProfile:null}],Sl=s=>{R(vi(s)),at(s)};i.useEffect(()=>{var s,l;(S==null?void 0:S.page)!==0&&(J===((s=$)==null?void 0:s.EXTEND_WITH_UPLOAD)||J===((l=$)==null?void 0:l.EXTEND))&&tt(),at((S==null?void 0:S.page)||0)},[S==null?void 0:S.page]);const Ft=()=>{gs(!q),Ue&&hs(!1)},zn=()=>{hs(!Ue),q&&gs(!1)},Xn=Ce?tn:ve;return N("div",{children:[n("div",{style:{padding:"0",width:"100%",margin:"0",marginTop:"20px"},children:N(je,{sx:{position:q?"fixed":"relative",top:q?0:"auto",left:q?0:"auto",right:q?0:"auto",bottom:q?0:"auto",width:q?"100vw":"100%",height:q?"100vh":"auto",zIndex:q?1004:void 0,backgroundColor:q?"white":"transparent",padding:q?"20px":"0",display:"flex",flexDirection:"column",boxShadow:q?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",backgroundColor:"#f5f5f5",borderRadius:"8px 8px 0 0"},children:[n(vt,{variant:"h6",children:"Details"}),N(je,{sx:{display:"flex",alignItems:"center",gap:1},children:[n(Pe,{variant:"contained",color:"primary",onClick:()=>{gn(!0)},disabled:Jt||Nt||ue&&(U==null?void 0:U.requestStatus)!==xs.DRAFT,children:"+ Add"}),n(fs,{title:q?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(qt,{onClick:Ft,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:q?n(vl,{}):n(yl,{})})})]})]}),c&&(c==null?void 0:c.length)>0?n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ml,{rows:c,columns:al,pageSize:50,page:it,rowsPerPageOptions:[50],rowCount:(S==null?void 0:S.totalElements)||0,onRowClick:Sn,onCellEditCommit:Ht,onPageChange:s=>Sl(s),disableSelectionOnClick:!0,getRowClassName:s=>s.id===Le?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:q?"calc(100vh - 150px)":`${Math.min(c.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})}):n("div",{style:{width:"100%",height:"100%",overflowX:"auto"},children:n("div",{style:{height:"100%"},children:n(Ml,{rows:c,columns:al,pageSize:5,rowsPerPageOptions:[5],page:it,onRowClick:Sn,onCellEditCommit:Ht,onPageChange:s=>Sl(s),disableSelectionOnClick:!0,getRowClassName:s=>s.id===Le?"selected-row":"",style:{border:"1px solid #ccc",borderRadius:"8px",width:"100%",height:q?"calc(100vh - 150px)":`${Math.min(c.length*50+130,300)}px`,overflow:"auto"},sx:{"& .selected-row":{backgroundColor:"rgb(234 233 255)"}}})})})]})}),Le&&yt&&(c==null?void 0:c.length)>0&&Oe.length>0&&z&&((Tl=Object.getOwnPropertyNames(z))==null?void 0:Tl.length)>0&&N(je,{sx:{position:Ue?"fixed":"relative",top:Ue?0:"auto",left:Ue?0:"auto",right:Ue?0:"auto",bottom:Ue?0:"auto",width:Ue?"100vw":"100%",height:Ue?"100vh":"auto",zIndex:Ue?1004:void 0,backgroundColor:Ue?"white":"transparent",padding:Ue?"20px":"0",marginTop:"20px",display:"flex",flexDirection:"column",boxShadow:Ue?"0px 0px 15px rgba(0, 0, 0, 0.2)":"none",transition:"all 0.3s ease",borderRadius:"8px",border:"1px solid #e0e0e0"},children:[N(je,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",padding:"8px 16px",borderRadius:"8px 8px 0 0"},children:[n(vt,{variant:"h6",children:"View Details"}),n(fs,{title:Ue?"Exit Zoom":"Zoom In",sx:{zIndex:"1009"},children:n(qt,{onClick:zn,color:"primary",sx:{backgroundColor:"rgba(0, 0, 0, 0.05)","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.1)"}},children:Ue?n(vl,{}):n(yl,{})})})]}),N(je,{sx:{flexGrow:1,display:"flex",flexDirection:"column"},children:[N(Ei,{sx:{top:0,position:"sticky",zIndex:998,backgroundColor:ke.background.container,borderBottom:`1px solid ${ke.border.light}`,"& .MuiTab-root":{minHeight:"48px",textTransform:"none",fontSize:"14px",fontWeight:600,color:ke.black.graphite,"&.Mui-selected":{color:ke.primary.main,fontWeight:700},"&:hover":{color:ke.primary.main,opacity:.8}},"& .MuiTabs-indicator":{backgroundColor:ke.primary.main,height:"3px"}},value:we,onChange:Vl,className:ut.customTabs,"aria-label":"material tabs",children:[B&&ve.length>0&&(B==null?void 0:B.length)>0?B==null?void 0:B.map((s,l)=>_(s)&&n(xl,{label:s},l)):n(_s,{}),n(xl,{label:"Additional Data",id:"AdditionalKey"},"Additional data")]}),(c==null?void 0:c.length)>0&&n(je,{sx:{padding:2,marginTop:2},children:Jl()}),n(Hi,{activeTab:we,submitForApprovalDisabled:bn,filteredButtons:ol,validateMaterials:Fl,workFlowLevels:Cs,showWfLevels:ll,childRequestHeaderData:(Al=w==null?void 0:w[Le])==null?void 0:Al.Tochildrequestheaderdata})]})]}),n("div",{}),n(fo,{dialogState:Y,openReusableDialog:Wl,closeReusableDialog:_n,dialogTitle:"Warning",dialogMessage:f,showCancelButton:!1,handleOk:Be,handleDialogConfirm:_n,dialogOkText:"OK",dialogSeverity:"danger"}),Lt&&n($n,{fullWidth:!0,maxWidth:!1,open:!0,onClose:Jn,sx:{display:"flex",justifyContent:"center"},disableEscapeKeyDown:!0,children:N(je,{sx:{width:"600px !important"},children:[N(kn,{sx:{backgroundColor:"#EAE9FF",marginBottom:".5rem"},children:[n(sl,{style:{height:"20px",width:"20px",marginBottom:"-5px"}}),n("span",{children:"Select Views"})]}),n(zs,{sx:{paddingBottom:".5rem"},children:N(je,{display:"flex",alignItems:"center",sx:{flex:1,padding:"22px 0px",gap:"5px"},children:[n(ml,{size:"small",multiple:!0,fullWidth:!0,options:Ks,disabled:Nt,disableCloseOnSelect:!0,value:B==null?void 0:B.filter(s=>!Ti.includes(s)),onChange:(s,l)=>{se([X,...l.filter(u=>u!==X)]),Ht({id:Gt,field:"views",value:l})},getOptionDisabled:s=>s===X,renderOption:(s,l,{selected:u})=>{var O;const h=P.find(D=>(D==null?void 0:D.material)===(ft==null?void 0:ft.materialNumber)),E=((O=h==null?void 0:h.views)==null?void 0:O.includes(l))||!1;return N("li",{...s,children:[n(Rl,{checked:u||l=="Basic Data",sx:{marginRight:1}}),l," ",E?"(extended)":""]})},renderTags:(s,l)=>s.map((u,h)=>{var b;const{key:E,...O}=l({index:h}),D=P.find(Z=>(Z==null?void 0:Z.material)===(ft==null?void 0:ft.materialNumber)),G=((b=D==null?void 0:D.views)==null?void 0:b.includes(u))||!1;return n(Ai,{label:`${u} ${G?"(extended)":""}`,...O,disabled:u===X},E)}),renderInput:s=>n(ln,{...s,label:"Select Views"})}),n(Pe,{variant:"contained",disabled:Nt,size:"small",onClick:()=>ql(),children:"Select all"})]})}),N(Xs,{children:[n(Pe,{onClick:()=>{ys(!1)},color:"error",variant:"outlined",sx:{height:36,minWidth:"3.5rem",textTransform:"none",borderColor:"#cc3300",fontWeight:500},children:"Cancel"}),n(Pe,{onClick:()=>{ys(!1),Ht({id:Gt,field:"views",value:B})},variant:"contained",children:"OK"})]})]})}),Rs&&N($n,{fullWidth:!0,maxWidth:"xl",open:!0,onClose:Jn,disableEscapeKeyDown:!0,sx:{"& .MuiDialog-paper":{padding:2,borderRadius:2}},children:[N(kn,{sx:{display:"flex",alignItems:"center",gap:1,backgroundColor:"#EAE9FF"},children:[n(sl,{fontSize:"small"}),Ce?n("span",{children:"Select org data for copy"}):n("span",{children:"Select org data to be extended"}),n(qt,{onClick:Jn,sx:{position:"absolute",right:15},children:n(pi,{})})]}),n(zs,{sx:{padding:0},children:n(Ci,{component:Oi,children:N(bi,{children:[n(Ni,{children:N(Dl,{children:[!Ce&&n(qe,{align:"center",children:Ve("S NO.")}),N(qe,{align:"center",children:[Ve("Sales Org"),!Ce&&n(el,{})]}),N(qe,{align:"center",children:[Ve("Distribution Channel"),!Ce&&n(el,{})]}),N(qe,{align:"center",children:[Ve("Plant"),!Ce&&n(el,{})]}),N(qe,{align:"center",children:[Ve("Storage Location"),!Ce&&n(el,{})]}),(F==null?void 0:F.Region)!==Vt.EUR&&N(qe,{align:"center",children:[Ve("Warehouse"),!Ce&&n(el,{})]}),n(qe,{align:"center",children:Ve("MRP Profile")}),(ve==null?void 0:ve.length)>1&&!Ce&&n(qe,{align:"center",children:"Action"})]})}),n(mi,{children:Xn==null?void 0:Xn.map((s,l)=>{var u,h,E,O,D,G,b,Z,Ee,j,Te,et;return N(Dl,{sx:{padding:"12px"},children:[!Ce&&n(qe,{children:n(vt,{variant:"body2",children:l+1})}),n(qe,{children:n(gt,{options:Zt["Sales Organization"],value:Ce?M==null?void 0:M.salesOrg:s==null?void 0:s.salesOrg,onChange:_e=>Xl("Sales Organization",_e,l),placeholder:"Select Sales Org",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Sales Organization"]})}),n(qe,{children:n(gt,{options:Ce?(h=M==null?void 0:M.dc)==null?void 0:h.options:(u=s.dc)==null?void 0:u.options,value:Ce?(O=M==null?void 0:M.dc)==null?void 0:O.value:(E=s.dc)==null?void 0:E.value,onChange:_e=>Ce?Ge(Ct=>{var Tt;return{...Ct,dc:{value:_e,options:(Tt=M==null?void 0:M.dc)==null?void 0:Tt.options}}}):Zl(_e,l),placeholder:"Select DC",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Distribution Channel"][l]})}),n(qe,{children:n(gt,{options:Zt.Plant||[],value:Ce?(G=M==null?void 0:M.plant)==null?void 0:G.value:(D=s.plant)==null?void 0:D.value,onChange:_e=>Kl(_e,l),placeholder:"Select Plant",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me.Plant[l]})}),n(qe,{children:n(gt,{options:Ce?(Z=M==null?void 0:M.sloc)==null?void 0:Z.options:(b=s==null?void 0:s.sloc)==null?void 0:b.options,value:Ce?(j=M==null?void 0:M.sloc)==null?void 0:j.value:(Ee=s==null?void 0:s.sloc)==null?void 0:Ee.value,onChange:_e=>Ce?Ge(Ct=>{var Tt;return{...Ct,sloc:{value:_e,options:(Tt=M==null?void 0:M.sloc)==null?void 0:Tt.options}}}):hl(_e,l),placeholder:"Select Sloc",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Storage Location"][l]})}),(F==null?void 0:F.Region)!==Vt.EUR&&n(qe,{children:n(gt,{options:Zt.warehouse||[],value:Ce?(et=M==null?void 0:M.warehouse)==null?void 0:et.value:(Te=s==null?void 0:s.warehouse)==null?void 0:Te.value,onChange:_e=>Ce?Ge(Ct=>{var Tt;return{...Ct,warehouse:{value:_e,options:(Tt=M==null?void 0:M.warehouse)==null?void 0:Tt.options}}}):Ql(_e,l),placeholder:"Select Warehouse",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me.warehouse[l]})}),n(qe,{children:n(gt,{options:Zt["Mrp Profile"]||[],value:Ce?M==null?void 0:M.mrpProfile:s.mrpProfile,onChange:_e=>Ce?Ge(Ct=>({...Ct,mrpProfile:_e})):eo(_e,l),placeholder:"Select MRP Profile",disabled:Nt,isFieldError:!1,minWidth:165,isLoading:me["Mrp Profile"]})}),ve.length>1&&N(qe,{align:"right",children:[n(qt,{size:"small",color:"primary",disabled:Nt,onClick:()=>{pt(!0),Qs({orgRowLength:ve.length,copyFor:l})},style:{display:l===0?"none":"inline-flex"},children:n(ro,{})}),n(qt,{style:{display:l===0?"none":"inline-flex"},size:"small",color:"error",onClick:()=>Rn(l),children:n(tl,{})})]})]},l)})})]})})}),N(Xs,{sx:{justifyContent:"flex-end",gap:.5},children:[!Ce&&n(Pe,{onClick:to,disabled:Nt||!Rt,variant:"contained",children:"+ Add"}),n(Pe,{onClick:()=>{if(os(!1),ve[0].plant&&(Ht({id:Gt,field:"orgData",value:ve}),!Ce)){il(ve);const l=c==null?void 0:c.map(u=>(u==null?void 0:u.id)===Gt?{...u,orgData:ve}:u);R(bt(l))}const s=ve.filter(l=>{var u,h;return(h=(u=l.plant)==null?void 0:u.value)==null?void 0:h.code}).map(l=>{var u,h;return(h=(u=l.plant)==null?void 0:u.value)==null?void 0:h.code});s.length>0&&gl(s),Ce&&(re(l=>{const u=l.findIndex(h=>h.id===M.id);return u!==-1?l.map((h,E)=>E===u?{...h,...M}:h):[...l,M]}),So(M,ve,F,B))},variant:"contained",disabled:Nt||!Rt,children:"Apply"})]})]}),Os&&n(Ji,{open:Os,onClose:()=>pt(!1),title:uo.COPY_ORG_DATA_VALES_HEADING,selectedMaterialPayload:dn,lengthOfOrgRow:Ws,materialID:Le,orgRows:ve}),ks&&n($l,{openSnackBar:un,alertMsg:ks,alertType:cn,handleSnackBarClose:()=>Vn(!1)}),n(Rc,{openSearchMat:x,materialOptions:xt,handleMatInputChange:zl,inputState:Dt,setOpenSearchMat:gn,dropDownData:Zt,AddCopiedMaterial:Hl}),n(qn,{blurLoading:ss,loaderMessage:Cn}),n(co,{})]})},Dd=()=>{var Le,Ut,$s,is,$t,ne,Xe,ve,I,H,re,M,Ge,ae,We,K,Ke,ft,As,cn,Bn,dn,wn,Pt,un,Vn,ps,Nn,ks,an,Cs,Is,fn,x,gn,Ce,hn,Zs;const[U,ut]=i.useState(!1),[ie,R]=i.useState([]),[ot,tt]=i.useState(!1),[st,F]=i.useState(!1),[J,He]=i.useState(!1),[S,w]=i.useState(""),[Q,Re]=i.useState(!1),[z,C]=i.useState([]),[Qe,_]=i.useState(!1),[be,pe]=i.useState(!1),[ue,Ot]=i.useState(""),[Ie,Fe]=i.useState(),[it,at]=i.useState(""),[Ne,nt]=i.useState(!1),[de,he]=i.useState(""),[X,B]=i.useState("success"),[se,P]=i.useState(!1),[St,c]=i.useState(!1),[De,Oe]=i.useState(!1),[Jt,V]=i.useState(!1),Y=pn(),ee=le(fe=>fe.applicationConfig),f=le(fe=>{var oe;return((oe=fe.payload.payloadData)==null?void 0:oe.data)||fe.payload.payloadData}),rt=le(fe=>fe.payload),xt=le(fe=>{var oe;return(oe=fe.request.requestHeader)==null?void 0:oe.requestId}),v=le(fe=>{var oe;return(oe=fe.userManagement)==null?void 0:oe.taskData}),zt=le(fe=>{var oe;return(oe=fe.materialDropDownData)==null?void 0:oe.isOdataApiCalled}),{getDtCall:ts,dtData:Dt}=Zo(),ct=Yo(),[Fn,Ds]=i.useState(!0),ss=le(fe=>fe.request.tabValue),{t:dt}=Gl(),{fetchAllDropdownMasterData:Cn}=Mr(),{getRequestHeaderTemplate:On}=ji(),Ls=[dt("Request Header"),dt("Material List"),dt("Attachments & Remarks"),dt("Preview")],[bn,Xt]=i.useState([!1]),rn=fe=>{Y(Gn(fe))},Kt=nl(),q=Kt.state,Ue=new URLSearchParams(Kt.search.split("?")[1]).get("RequestId"),hs=new URLSearchParams(Kt.search),we=hs.get("RequestId"),ye=hs.get("RequestType"),yt=hs.get("reqBench"),Ss=!(v!=null&&v.taskId)&&!yt,{createPayloadFromReduxState:Es}=gc({initialReqScreen:Ss,isReqBench:yt}),{changePayloadForTemplate:Yt}=hc(f==null?void 0:f.TemplateName),Zt=((Le=Kt.state)==null?void 0:Le.isChildRequest)??(we&&!yt)??!1,Me=(f==null?void 0:f.RequestType)===((Ut=$)==null?void 0:Ut.CHANGE)||(f==null?void 0:f.RequestType)===(($s=$)==null?void 0:$s.CHANGE_WITH_UPLOAD)?Yt(!!we):Es(rt),ns={materialDetails:Me,dtName:lo(($t=(is=Me==null?void 0:Me[0])==null?void 0:is.Torequestheaderdata)==null?void 0:$t.RequestType).dtName,version:lo((Xe=(ne=Me==null?void 0:Me[0])==null?void 0:ne.Torequestheaderdata)==null?void 0:Xe.RequestType).version,requestId:((I=(ve=Me==null?void 0:Me[0])==null?void 0:ve.Torequestheaderdata)==null?void 0:I.RequestId)||"",scenario:(M=lo((re=(H=Me==null?void 0:Me[0])==null?void 0:H.Torequestheaderdata)==null?void 0:re.RequestType))==null?void 0:M.scenario,templateName:(f==null?void 0:f.RequestType)===((Ge=$)==null?void 0:Ge.CHANGE)||(f==null?void 0:f.RequestType)===((ae=$)==null?void 0:ae.CHANGE_WITH_UPLOAD)?(K=(We=Me==null?void 0:Me[0])==null?void 0:We.Torequestheaderdata)==null?void 0:K.TemplateName:"",matlType:"ALL",region:((ft=(Ke=Me==null?void 0:Me[0])==null?void 0:Ke.Torequestheaderdata)==null?void 0:ft.Region)||""},{getDisplayData:vs}=bc(),At=()=>{_(!0)},Et=()=>{_(!1)},Ks=()=>{pe(!0)},Ts=fe=>{pe(fe)},Lt=()=>{it==="success"?ct("/requestBench"):Et()},ys=()=>{tt(!0)},Gt=fe=>{let oe="";ye===$.CREATE_WITH_UPLOAD?oe="getAllMaterialsFromExcel":ye===$.EXTEND_WITH_UPLOAD?oe="getAllMaterialsFromExcelForMassExtend":oe="getAllMaterialsFromExcelForMassChange",he("Initiating Excel Upload"),nt(!0);const Ye=new FormData;[...fe].forEach(me=>Ye.append("files",me)),Ye.append("dtName",ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD?"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG":"MDG_MAT_CHANGE_TEMPLATE"),Ye.append("version",ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD?"v1":"v5"),Ye.append("requestId",Ue||""),Ye.append("region",f!=null&&f.Region?f==null?void 0:f.Region:"US"),Ye.append("matlType","ALL");const Rt=me=>{var Ze;(me==null?void 0:me.statusCode)===ze.STATUS_200?(Re(!1),nt(!1),he(""),ct((Ze=nn)==null?void 0:Ze.REQUEST_BENCH)):(Re(!1),nt(!1),Fe(me==null?void 0:me.message),he(""),B("error"),jt())},rs=me=>{nt(!1),Fe(me==null?void 0:me.message),he(""),B("error"),jt()};ce(`/${ge}/massAction/${oe}`,"postformdata",Rt,rs,Ye)};i.useEffect(()=>((async()=>{if(we){const oe=li(vn.CURRENT_TASK,!0,{}),Ye=ye||(v==null?void 0:v.ATTRIBUTE_2)||(oe==null?void 0:oe.ATTRIBUTE_2);await vs(we,Ye,yt,v,q,"Material"),(ye===$.CHANGE_WITH_UPLOAD||ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD)&&(q==null?void 0:q.objectNumbers)==="Not Available"&&((q==null?void 0:q.reqStatus)===xs.DRAFT||(q==null?void 0:q.reqStatus)===xs.UPLOAD_FAILED)?(Y(Gn(0)),F(!1),He(!1)):(Y(Gn(1)),F(!0),He(!0)),c(!0)}else Y(Gn(0))})(),()=>{Y(ti([])),Y(xr()),Y(Dr()),Y(Lr()),Y(vr()),Y(yr()),Y(Hn({})),Y(Gr({data:{}})),Y(Ur([])),Y($r([])),Y(kr({})),Y(Wr()),Y(qr([])),Y(Hr([])),Y(Fr({})),oo(vn.CURRENT_TASK),oo(vn.ROLE)}),[Ue,Y]);function Gs(fe){let oe={decisionTableId:null,decisionTableName:si.MDG_MAT_REGION_DIVISION_MAPPING,version:"v1",conditions:[{"MDG_CONDITIONS.MDG_MAT_REGION":fe}]};ts(oe)}i.useEffect(()=>{f!=null&&f.Region&&Gs(f==null?void 0:f.Region)},[f==null?void 0:f.Region]),i.useEffect(()=>{var fe,oe;if(Dt){const Rt=[...Br((oe=(fe=Dt==null?void 0:Dt.result)==null?void 0:fe[0])==null?void 0:oe.MDG_MAT_REGION_DIVISION_MAPPING)].sort((rs,me)=>rs.code.localeCompare(me.code));Y(Vs({keyName:"Division",data:Rt})),Ds(!1),he(wr.DT_LOADING)}},[Dt]),i.useEffect(()=>(zt||(Cn(),Y(Vr(!0))),jr(vn.MODULE,Tn.MAT),On(),Us(),Y(bt([])),Y(Vs({keyName:"Region",data:Pr})),Y(Vs({keyName:"DiversionControlFlag",data:Jr})),w(zr("MAT")),()=>{Y(ei({})),oo(vn.MODULE)}),[]),i.useEffect(()=>{st&&Xt([!0])},[st]);const Us=()=>{let fe={decisionTableId:null,decisionTableName:"MDG_ATTACHMENTS_LIST_DT",version:"v1",rulePolicy:null,validityDate:null,conditions:[{"MDG_CONDITIONS.MDG_ATTACHMENTS_OBJECT_TYPE":"Material","MDG_CONDITIONS.MDG_ATTACHMENTS_SCENARIO":"Create","MDG_CONDITIONS.MDG_ATTACHMENTS_REG_COMP_CODE":1}],systemFilters:null,systemOrders:null,filterString:null};ut(!0);const oe=Rt=>{var rs,me;if(ut(!1),Rt.statusCode===200){const Os=((me=(rs=Rt==null?void 0:Rt.data)==null?void 0:rs.result[0])==null?void 0:me.MDG_ATTACHMENTS_ACTION_TYPE)||[];C(Os)}},Ye=Rt=>{console.log(Rt)};ee.environment==="localhost"?ce(`/${on}/rest/v1/invoke-rules`,"post",oe,Ye,fe):ce(`/${on}/v1/invoke-rules`,"post",oe,Ye,fe)},Ys=()=>{var me,Ze,Os,pt,Ws,Qs;const fe=we!=null&&we.includes("FCA")?ht.EXCEL.DOWNLOAD_EXCEL_FINANCE:ht.EXCEL.DOWNLOAD_EXCEL_MAT;he("Please wait 1-2 minutes while real-time data are exported into the template. Thank you for your patience."),nt(!0);let oe={massSchedulingId:f==null?void 0:f.RequestId},Ye={dtName:(f==null?void 0:f.RequestType)===((me=$)==null?void 0:me.CHANGE)||(f==null?void 0:f.RequestType)===((Ze=$)==null?void 0:Ze.CHANGE_WITH_UPLOAD)?"MDG_MAT_CHANGE_TEMPLATE":"MDG_MAT_MASS_CREATE_EXTEND_FIELD_CONFIG",version:(f==null?void 0:f.RequestType)===((Os=$)==null?void 0:Os.CHANGE)||(f==null?void 0:f.RequestType)===((pt=$)==null?void 0:pt.CHANGE_WITH_UPLOAD)?"v4":"v1",requestId:(f==null?void 0:f.RequestId)||xt||"",scenario:(f==null?void 0:f.RequestType)===((Ws=$)==null?void 0:Ws.CHANGE)||(f==null?void 0:f.RequestType)===((Qs=$)==null?void 0:Qs.CHANGE_WITH_UPLOAD)?"Change with Upload":"Create with Upload",templateName:(f==null?void 0:f.TemplateName)||"",region:(f==null?void 0:f.Region)||"",isChildRequest:Zt,matlType:"ALL"};const Rt=qs=>{const mn=URL.createObjectURL(qs),Ve=document.createElement("a");Ve.href=mn,Ve.setAttribute("download",`${f!=null&&f.TemplateName?f==null?void 0:f.TemplateName:we!=null&&we.includes("FCA")?$.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx`),document.body.appendChild(Ve),Ve.click(),document.body.removeChild(Ve),URL.revokeObjectURL(mn),nt(!1),he(""),Fe(`${f!=null&&f.TemplateName?f==null?void 0:f.TemplateName:we!=null&&we.includes("FCA")?$.FINANCE_COSTING:"Mass_Create"}_Data Export.xlsx has been exported successfully.`),B("success"),jt()},rs=()=>{};ce(`/${ge}${fe}`,"postandgetblob",Rt,rs,we!=null&&we.includes("FCA")?oe:Ye)},jt=()=>{P(!0)},ls=()=>{P(!1)},Rs=()=>{var fe,oe,Ye;Ue&&!yt?ct((fe=nn)==null?void 0:fe.MY_TASK):yt?ct((oe=nn)==null?void 0:oe.REQUEST_BENCH):!Ue&&!yt&&ct((Ye=nn)==null?void 0:Ye.MASTER_DATA)},os=()=>{V(!1)};return N(_s,{children:[Fn&&n(qn,{blurLoading:Ne,loaderMessage:de}),N(je,{sx:{padding:2},children:[N(Mt,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[xt||Ue?N(vt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(Ac,{sx:{fontSize:"1.5rem"}}),dt("Request Header ID"),": ",n("span",{children:xt||Ue})]}):n("div",{style:{flex:1}}),ss===1&&N(je,{sx:{display:"flex",justifyContent:"flex-end",gap:"1rem"},children:[n(Pe,{variant:"outlined",size:"small",title:dt("Error History"),disabled:!we,onClick:()=>{ct(`/requestBench/errorHistory?RequestId=${we||""}`,{state:{display:!0,childRequest:Zt,module:Tn.MAT}})},color:"primary",children:n(Xr,{sx:{padding:"2px"}})}),(f==null?void 0:f.RequestType)===$.CREATE||(f==null?void 0:f.RequestType)===$.EXTEND||(f==null?void 0:f.RequestType)===$.EXTEND_WITH_UPLOAD||(f==null?void 0:f.RequestType)===$.CREATE_WITH_UPLOAD||Ue!=null&&Ue.includes("FCA")?n(Pe,{variant:"outlined",disabled:!we,size:"small",onClick:()=>Oe(!0),title:Ue!=null&&Ue.includes("FCA")?dt("Finance Costing Change Log"):dt("Create Change Log"),children:n(Ko,{sx:{padding:"2px"}})}):n(Pe,{variant:"outlined",disabled:!we,size:"small",onClick:Ks,title:dt("Change Log"),children:n(Ko,{sx:{padding:"2px"}})}),n(Pe,{variant:"outlined",disabled:!we,size:"small",onClick:Ys,title:dt("Export Excel"),children:n(Cc,{sx:{padding:"2px"}})})]}),be&&n(rc,{open:!0,closeModal:Ts,requestId:xt||Ue,requestType:f==null?void 0:f.RequestType}),De&&n(cc,{open:!0,closeModal:()=>Oe(!1),requestId:xt||Ue,requestType:f==null?void 0:f.RequestType})]}),(f==null?void 0:f.TemplateName)&&N(vt,{variant:"h6",sx:{mb:1,textAlign:"left",display:"flex",alignItems:"center",gap:1},children:[n(pc,{sx:{fontSize:"1.5rem"}}),dt("Template Name"),": ",n("span",{children:f==null?void 0:f.TemplateName})]}),n(qt,{onClick:()=>{var fe,oe;if(yt&&!((fe=Un)!=null&&fe.includes(f==null?void 0:f.RequestStatus))){ct((oe=nn)==null?void 0:oe.REQUEST_BENCH);return}V(!0)},color:"primary","aria-label":"upload picture",component:"label",sx:{left:"-10px"},title:dt("Back"),children:n(Kr,{sx:{fontSize:"25px",color:"#000000"}})}),n(ec,{nonLinear:!0,activeStep:ss,sx:{display:"flex",alignItems:"center",justifyContent:"center",margin:"25px 14%",marginTop:"-35px"},children:Ls.map((fe,oe)=>n(Zr,{children:n(Qr,{color:"error",disabled:oe===1&&!st||oe===2&&!J||oe===3&&!J,onClick:()=>rn(oe),sx:{fontSize:"50px",fontWeight:"bold"},children:n("span",{style:{fontSize:"15px",fontWeight:"bold"},children:fe})})},fe))}),n(fo,{dialogState:Qe,openReusableDialog:At,closeReusableDialog:Et,dialogTitle:ue,dialogMessage:Ie,handleDialogConfirm:Et,dialogOkText:"OK",handleOk:Lt,dialogSeverity:it}),n(qn,{blurLoading:Ne,loaderMessage:de}),ss===0&&N(_s,{children:[n(Nc,{setIsSecondTabEnabled:F,setIsAttachmentTabEnabled:He,requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:xs.ENABLE_FOR_FIRST_TIME,downloadClicked:ot,setDownloadClicked:tt}),(ye===$.CHANGE_WITH_UPLOAD||ye===$.CREATE_WITH_UPLOAD||ye===$.EXTEND_WITH_UPLOAD)&&((q==null?void 0:q.reqStatus)==xs.DRAFT&&(q==null?void 0:q.objectNumbers)==="Not Available"||(q==null?void 0:q.reqStatus)==xs.UPLOAD_FAILED)&&n(Oc,{handleDownload:ys,setEnableDocumentUpload:Re,enableDocumentUpload:Q,handleUploadMaterial:Gt}),((f==null?void 0:f.RequestType)===((As=$)==null?void 0:As.CHANGE)||(f==null?void 0:f.RequestType)===((cn=$)==null?void 0:cn.CHANGE_WITH_UPLOAD))&&!we&&(f==null?void 0:f.DirectAllowed)!=="X"&&(f==null?void 0:f.DirectAllowed)!==void 0&&N(vt,{sx:{fontSize:"13px",fontWeight:"500",color:(dn=(Bn=ke)==null?void 0:Bn.error)==null?void 0:dn.dark,marginTop:"1rem",marginLeft:"0.5rem"},children:[n(je,{component:"span",sx:{fontWeight:"bold"},children:"Note:"})," ","You are not authorized to Tcode"," ",N(je,{component:"span",sx:{fontWeight:"bold"},children:[" ","MM02."]})]})]}),ss===1&&((f==null?void 0:f.RequestType)===((wn=$)==null?void 0:wn.CREATE)||(v==null?void 0:v.ATTRIBUTE_2)===((Pt=$)==null?void 0:Pt.CREATE)||ye===((un=$)==null?void 0:un.CREATE)||ye===((Vn=$)==null?void 0:Vn.CREATE_WITH_UPLOAD)?n(_c,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:xs.ENABLE_FOR_FIRST_TIME,mandFields:ie,addHardCodeData:St,setIsAttachmentTabEnabled:He,setCompleted:Xt}):(f==null?void 0:f.RequestType)===((ps=$)==null?void 0:ps.EXTEND)||(v==null?void 0:v.ATTRIBUTE_2)===((Nn=$)==null?void 0:Nn.EXTEND)||(v==null?void 0:v.ATTRIBUTE_2)===((ks=$)==null?void 0:ks.EXTEND_WITH_UPLOAD)||ye===((an=$)==null?void 0:an.EXTEND)||ye===((Cs=$)==null?void 0:Cs.EXTEND_WITH_UPLOAD)?n(Dc,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:xs.ENABLE_FOR_FIRST_TIME,mandFields:ie,addHardCodeData:St,setIsAttachmentTabEnabled:He,setCompleted:Xt}):(f==null?void 0:f.RequestType)===((Is=$)==null?void 0:Is.FINANCE_COSTING)||(v==null?void 0:v.ATTRIBUTE_2)===((fn=$)==null?void 0:fn.FINANCE_COSTING)||ye===((x=$)==null?void 0:x.FINANCE_COSTING)?n(dc,{setCompleted:Xt}):n(yi,{setIsAttachmentTabEnabled:!0,setCompleted:Xt,downloadClicked:ot,setDownloadClicked:tt})),ss===2&&n(Sc,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:xs.ENABLE_FOR_FIRST_TIME,attachmentsData:z,requestIdHeader:xt||Ue,pcNumber:S,module:(gn=Tn)==null?void 0:gn.MAT,artifactName:Yr.MATERIALMASTER}),ss===3&&n(je,{sx:{width:"100%",overflow:"auto"},children:n(Ec,{requestStatus:q!=null&&q.reqStatus?q==null?void 0:q.reqStatus:xs.ENABLE_FOR_FIRST_TIME,module:(Ce=Tn)==null?void 0:Ce.MAT,payloadData:rt,payloadForDownloadExcel:ns})})]}),n($l,{openSnackBar:se,alertMsg:Ie,alertType:X,handleSnackBarClose:ls}),Jt&&N(ao,{isOpen:Jt,titleIcon:n(tc,{size:"small",sx:{color:(Zs=(hn=ke)==null?void 0:hn.secondary)==null?void 0:Zs.amber,fontSize:"20px"}}),Title:dt("Warning"),handleClose:os,children:[n(zs,{sx:{mt:2},children:dt(_i.LEAVE_PAGE_MESSAGE)}),N(Xs,{children:[n(Pe,{variant:"outlined",size:"small",sx:{...Ri},onClick:os,children:dt("No")}),n(Pe,{variant:"contained",size:"small",sx:{...Ii},onClick:Rs,children:dt("Yes")})]})]})]})};export{Dd as default};
