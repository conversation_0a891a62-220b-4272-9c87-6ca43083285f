import {
  Box,
  Grid,
  Typography,
  FormControl,
  MenuItem,
  Select,
  Button,
  Stack
} from "@mui/material";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { doAjax } from "../Common/fetchService";
import { appSettingsUpdate } from "@app/appSettingsSlice";
import { destination_Admin } from "../../destinationVariables";
import { font_Small } from "../Common/commonStyles";
import useLang from "@hooks/useLang";
import { showToast } from "../../functions";
import { ToastContainer } from "react-toastify";
import { END_POINTS } from "@constant/apiEndPoints";
import { ERROR_MESSAGES, LANG_LIST, SUCCESS_MESSAGES } from "@constant/enum";

export default function LanguageSettings() {
  const appSettings = useSelector((state) => state.appSettings);
  let userData = useSelector((state) => state.userManagement.userData);
  const dispatch = useDispatch();
  const { t } = useLang();

  const [SettingsObj, setSettingsObj] = useState({
    language: appSettings.language,
  });

  const [formValidationErrorItems, setFormValidationErrorItems] = useState([]);

  const handleSelect = (e) => {
    setSettingsObj({ ...SettingsObj, [e.target.name]: e.target.value });
  };

  const handleClear = () => {
    setSettingsObj({
      language: ""
    });
    setFormValidationErrorItems([]);
  };

  const handleSave = () => {
    let payload = {
      email: userData?.emailId,
      language: SettingsObj.language,
    };
    let hSuccess = (data) => {
      dispatch(appSettingsUpdate({ ...appSettings, language: SettingsObj.language }));
      if (data.status === "Success") {
        showToast(SUCCESS_MESSAGES?.LANG_SET_SUCCESS, "success");
      } else {
        showToast(ERROR_MESSAGES?.LANG_SET_ERROR, "error");
      }
    };
    let hError = () => {
      showToast(ERROR_MESSAGES?.LANG_SET_ERROR, "error");
    };
    doAjax(
      `/${destination_Admin}${END_POINTS?.USER_ACCESS?.SAVE_APP_SETTINGS}`,
      "post",
      hSuccess,
      hError,
      payload
    );
  };

  return (
    <>
      <Box sx={{ p: 3, position: 'relative', minHeight: '400px' }}>
        <Typography variant="h5" sx={{ mb: 4 }}>
          {t("Language Settings")}
        </Typography>
        <Stack spacing={2}>
          <Grid item md={12}>
            <Box>
              <Typography sx={font_Small}>Preferred Language</Typography>
              <FormControl fullWidth size="small" sx={{ margin: ".5em 0px" }}>
                <Select
                  placeholder="Select Language"
                  size="small"
                  value={SettingsObj.language}
                  name={"language"}
                  onChange={handleSelect}
                  displayEmpty
                  sx={font_Small}
                  error={formValidationErrorItems.includes("language")}
                >
                  <MenuItem sx={font_Small} value={""}>
                    <div style={{ color: "#C1C1C1" }}>Select Language</div>
                  </MenuItem>
                  {LANG_LIST?.map((lang) => (
                    <MenuItem key={lang} value={lang}>{lang}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Stack>
      </Box>
      <Box
        sx={{
          position: 'absolute',
          bottom: 16,
          right: 24,
          display: 'flex',
          gap: 1
        }}>
        <Button
          variant="outlined"
          onClick={handleClear}
          sx={{ textTransform: "capitalize" }}
        >
          {t("Clear")}
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          sx={{ textTransform: "capitalize" }}
        >
          {t("Save")}
        </Button>
      </Box>
      <ToastContainer/>
    </>
  );
}
