{"costCenter": [{"keyName": "PersonInChargeUser", "endPoint": "getUserResponsible"}, {"keyName": "CostcenterType", "endPoint": "getCostCenterCategory"}, {"keyName": "AddrCountry", "endPoint": "getCountry"}, {"keyName": "FuncAreaLong", "endPoint": "getFunctionalArea"}], "profitCenter": [{"keyName": "UserResponsible", "endPoint": "getUserResponsible"}, {"keyName": "Segment", "endPoint": "getSegment"}, {"keyName": "FormPlanningTemp", "endPoint": "getFormPlanningTemp"}, {"keyName": "CountryReg", "endPoint": "getCountryOrReg"}, {"keyName": "TaxJur", "endPoint": "getJurisdiction"}, {"keyName": "Language", "endPoint": "getLanguageKey"}], "bankKey": [{"keyName": "BankCtry", "endPoint": "getBankCountryRegion"}, {"keyName": "Country", "endPoint": "getBankCountryRegion"}, {"keyName": "<PERSON><PERSON>", "endPoint": "getLanguage"}, {"keyName": "PoBoxReg", "endPoint": "PoBoxReg"}, {"keyName": "PoboxCtry", "endPoint": "getUndeliverable"}, {"keyName": "<PERSON><PERSON><PERSON><PERSON>", "endPoint": "getDeliverySrvType"}, {"keyName": "CountyCode", "endPoint": "getCountyCode"}, {"keyName": "Sort1", "endPoint": ""}, {"keyName": "SwiftCode", "endPoint": "getSwiftCode"}, {"keyName": "IbanRule", "endPoint": ""}], "generalLedger": [{"keyName": "Language", "endPoint": "getLanguageKey"}, {"keyName": "TradingPartner", "endPoint": "getTradingP<PERSON>ner"}, {"keyName": "<PERSON><PERSON><PERSON><PERSON>", "endPoint": "getSortKey"}, {"keyName": "PlanningLevel", "endPoint": "getPlanningLevel"}, {"keyName": "PlanningLevel", "endPoint": "getPlanningLevel"}, {"keyName": "InternalUOM", "endPoint": "getInternalUOM"}, {"keyName": "InterestIndicator", "endPoint": "getInterestIndicator"}, {"keyName": "InterestCalculationFrequency", "endPoint": "getInterestCalculationFreq"}, {"keyName": "AccountType", "endPoint": "getGLAccountType"}, {"keyName": "FunctionalArea", "endPoint": "getFunctionalArea"}, {"keyName": "ChartOfAccounts", "endPoint": "getChartOfAccounts"}, {"keyName": "Account<PERSON><PERSON><PERSON>cy", "endPoint": "getAccountCurrency"}, {"keyName": "ExchangeRateDiffKey", "endPoint": "getExchangeRateDiffKey"}, {"keyName": "ReconAccountForAccountType", "endPoint": "getReconAccountForAccountType"}]}