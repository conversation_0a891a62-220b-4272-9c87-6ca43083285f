import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { updateMaterialData } from "../../app/payloadSlice";
import { Box, Grid, TextField, Button, Typography, IconButton, Tooltip } from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { useSelector } from "react-redux";
import { MATERIAL_VIEWS } from "@constant/enum";

const CustomSupplierTable = (props) => {
  const materialID = props.materialID || "SupplierForm";
  const supplierRowsFromRedux = useSelector(
    (state) =>
      state.payload?.[materialID]?.payloadData?.[MATERIAL_VIEWS.SUPPLIER_FORM]?.basic?.Togenericarraydata || []
  );
  const [rows, setRows] = useState([{ Json401: "", Json402: "", Json403: "", GenericArrayId:null }]);
  const dispatch = useDispatch();

  useEffect(() => {
    if (supplierRowsFromRedux && supplierRowsFromRedux.length > 0) {
      setRows(supplierRowsFromRedux);
    }
  }, [supplierRowsFromRedux]);

  const handleChange = (index, field, value) => {
    let updatedRows = JSON.parse(JSON.stringify(rows));
    updatedRows[index][field] = value;
    setRows(updatedRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.SUPPLIER_FORM,
        itemID: "basic",
        keyName: "Togenericarraydata",
        data: updatedRows,
      })
    );
  };

  const addRow = () => {
    const newRows = [...rows, { Json401: "", Json402: "", Json403: "" }];
    setRows(newRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.SUPPLIER_FORM,
        itemID: "basic",
        keyName: "Togenericarraydata",
        data: newRows,
      })
    );
  };

  const removeRow = (index) => {
    const updatedRows = rows.filter((_, i) => i !== index);
    setRows(updatedRows);
    dispatch(
      updateMaterialData({
        materialID,
        viewID: MATERIAL_VIEWS.SUPPLIER_FORM,
        itemID: "basic",
        keyName: "Togenericarraydata",
        data: updatedRows,
      })
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography fontWeight={700} mb={2}>Prepack Article Details</Typography>
      <Grid container spacing={2} sx={{ fontWeight: 600, mb: 1 }}>
        <Grid item xs={4}><Typography>Shirt Size</Typography></Grid>
        <Grid item xs={4}><Typography>Shirt Colour</Typography></Grid>
        <Grid item xs={3}><Typography>Shirt Quantity</Typography></Grid>
        <Grid item xs={1}></Grid> {/* For the delete button header space */}
      </Grid>

      {rows.map((row, index) => (
        <Grid container spacing={2} key={index} alignItems="center" mb={1}>
          <Grid item xs={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Size"
              value={row.Json401}
              onChange={(e) => handleChange(index, "Json401", e.target.value)}
              disabled={props.disabled}
            />
          </Grid>
          <Grid item xs={4}>
            <TextField
              fullWidth
              size="small"
              placeholder="Colour"
              value={row.Json402}
              onChange={(e) => handleChange(index, "Json402", e.target.value)}
              disabled={props.disabled}
            />
          </Grid>
          <Grid item xs={3}>
            <TextField
              fullWidth
              size="small"
              placeholder="Quantity"
              value={row.Json403}
              onChange={(e) => handleChange(index, "Json403", e.target.value)}
              disabled={props.disabled}
            />
          </Grid>
          <Grid item xs={1}>
            {index !== 0 && (
              <Tooltip title="Remove Row">
                <IconButton onClick={() => removeRow(index)} color="error">
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
            )}
          </Grid>
        </Grid>
      ))}

      <Button variant="outlined" onClick={addRow}>
        + Add Row
      </Button>
    </Box>
  );
};

export default CustomSupplierTable;
