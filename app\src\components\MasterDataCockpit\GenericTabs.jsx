import { Accordion, AccordionDetails, AccordionSummary, Grid, Box, Typography, CircularProgress } from "@mui/material";
import { useEffect, useMemo, useRef, useState } from "react";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useSelector } from "react-redux";
import FilterField from "../Common/ReusableFilterBox/FilterField";
import { container_Padding } from "../Common/commonStyles";
import { doAjax } from "../Common/fetchService";
import { destination_MaterialMgmt } from "../../destinationVariables";
import { pushMaterialDisplayData, updateMaterialData } from "../../app/payloadSlice";
import { useDispatch } from "react-redux";
import { generateUniqueCombinations, getKeysValue } from "@helper/helper";
import { colors } from "@constant/colors";
import { DT_TABLES, ERROR_MESSAGES, MATERIAL_VIEWS, VISIBILITY_TYPE } from "@constant/enum";
import useCustomDtCall from "@hooks/useCustomDtCall";
import TaxData from "./TaxData";
import GenericViewGeneral from "./GenericViewGeneral";
import useLogger from "@hooks/useLogger";
import { END_POINTS } from "../../constant/apiEndPoints";
import { REQUEST_TYPE } from "../../constant/enum";
import useLang from "@hooks/useLang";
import ClassificationView from "../../modules/material/create/ClassificationView";

const GenericTabs = (props) => {
  const payloadState = useSelector((state) => state.payload);
  const materialRows = useSelector((state) => state?.request?.materialRows);
  const orgData = payloadState?.[props.materialID]?.headerData?.orgData;
  let taskData = useSelector((state) => state.userManagement.taskData);
  const [plantCombination, setPlantCombination] = useState({});
  const requestType = useSelector((state) => state.payload.payloadData?.RequestType);
  const [mrpLoaderArr, setMrpLoaderArr] = useState([]);
  const [expanded, setExpanded] = useState([]);
   const [taxDataLoading, setTaxDataLoading] = useState(false);
  const dispatch = useDispatch();
  const { getDtCall, dtData } = useCustomDtCall();
  const { customError } = useLogger();
  const { t } = useLang();
  const accordionRefs = useRef({});

  // Format a raw combination for display
  function formatCombinationDisplay(combination, requiredKeys) {
    const fieldLabels = {
      plant: "Plant",
      salesOrg: "SalesOrg",
      dc: "Distribution Channel",
      sloc: "Storage Location",
      mrpProfile: "MRP Profile",
      warehouse: "Warehouse",
    };

    const combinationParts = combination.split("-");
    return requiredKeys
      .map((key, index) => {
        const label = fieldLabels[key];
        const value = combinationParts[index] || "N/A"; // Handle missing parts
        return `${label} - ${value}`;
      })
      .join(", ");
  }
  const formatUniqueSalesOrg = (salesOrgArray) => {
    return [...new Set(salesOrgArray)].join("$^$");
  };
  const getUniqueCountries = (data) => {
    const uniqueCountries = new Map();

    data.forEach(({ CountryName, Country }) => {
      uniqueCountries.set(Country, CountryName);
    });

    return Array.from(uniqueCountries, ([Country, CountryName]) => ({ Country, CountryName }));
  };
  const formatUniqueCountries = (uniqueCountries) => {
    return uniqueCountries.map(({ Country }) => Country).join("$^$");
  };
  const getCountriesBasedOnSalesOrg = (salesOrg) => {
    const url = `/${destination_MaterialMgmt}${END_POINTS.TAX_DATA.GET_COUNTRY_SALESORG}`;
    const payload = { salesOrg: salesOrg };
    setTaxDataLoading(true);
    const hSuccess = (response) => {
      const data = response?.body;
      const uniqueCountries = getUniqueCountries(data);
      const formattedCountries = formatUniqueCountries(uniqueCountries);
      getTaxDataSetBasedOnCountry(formattedCountries);
    };

    const hError = (error) => {
      setTaxDataLoading(false);
      customError(ERROR_MESSAGES.NO_DATA_AVAILABLE);
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const getTaxDataSetBasedOnCountry = (country) => {
    const url = `/${destination_MaterialMgmt}${END_POINTS.TAX_DATA.GET_TAX_COUNTRY}`;
    const payload = { country };

    const hSuccess = (data) => {
      setTaxDataLoading(false);
      const taxDataSet = data?.body;
      const existingTaxData = payloadState[props?.materialID]?.payloadData?.TaxData?.TaxData?.TaxDataSet || [];
      const taxClassDescMap = {};
      const validTaxDataSet = taxDataSet.filter(entry => entry.TaxType);
      
      validTaxDataSet.forEach(({ TaxClass, TaxClassDesc }) => {
        taxClassDescMap[TaxClass] = TaxClassDesc;
      });

      const groupedTaxData = existingTaxData.map(existingEntry => {
        const relevantOptions = validTaxDataSet
          .filter(item => item.TaxType === existingEntry.TaxType && item.Country === existingEntry.Country)
          .map(item => ({
            code: item.TaxClass,
            desc: item.TaxClassDesc,
          }));
        let updatedSelectedTaxClass = existingEntry.SelectedTaxClass;
        if (updatedSelectedTaxClass && taxClassDescMap[updatedSelectedTaxClass.TaxClass]) {
          updatedSelectedTaxClass = {
            ...updatedSelectedTaxClass,
            TaxClassDesc: taxClassDescMap[updatedSelectedTaxClass.TaxClass]
          };
        }

        return {
          ...existingEntry,
          options: relevantOptions,
          SelectedTaxClass: updatedSelectedTaxClass
        };
      });
      validTaxDataSet.forEach(({ TaxType, SequenceNo, Country, TaxClass, TaxClassDesc }) => {
        const exists = groupedTaxData.some(item => 
          item.TaxType === TaxType && item.Country === Country
        );

        if (!exists) {
          const options = validTaxDataSet
            .filter(item => item.TaxType === TaxType && item.Country === Country)
            .map(item => ({
              code: item.TaxClass,
              desc: item.TaxClassDesc,
            }));

          groupedTaxData.push({
            TaxType,
            SequenceNo,
            Country,
            options,
            SelectedTaxClass: null
          });
        }
      });

      dispatch(
        updateMaterialData({
          materialID: props?.materialID || "",
          keyName: "TaxDataSet",
          data: groupedTaxData,
          viewID: "TaxData",
          itemID: "TaxData",
        })
      );
    };

    const hError = (error) => {
      setTaxDataLoading(false);
      customError(ERROR_MESSAGES.NO_DATA_AVAILABLE);
    };

    doAjax(url, "post", hSuccess, hError, payload);
  };

  const CurrentMaterialRows = materialRows?.find((material) => {
    return material?.id === props.materialID;
  });
  useEffect(() => {
    if (orgData) {
      const isRefMaterial = payloadState[props.materialID]?.headerData?.refMaterialData ? true : false;
      const combinations = generateUniqueCombinations(orgData, payloadState?.[props.materialID]?.payloadData, props?.materialID, dispatch);
      setPlantCombination(combinations);
      if (!isRefMaterial && !props.isDisplay && combinations.hasOwnProperty(MATERIAL_VIEWS.SALES) && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
        combinations[MATERIAL_VIEWS.SALES].reduxCombinations.forEach((comb, index) => {
          if (requestType !== REQUEST_TYPE.EXTEND) {
            getSalesGrpPriceMapDT({ comb, dt: DT_TABLES.SALES_DIV_PRICE_MAPPING }, orgData[index]);
          }
        });
      }
      if (!isRefMaterial && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES) || props?.selectedViews?.includes(MATERIAL_VIEWS.ACCOUNTING) || props?.selectedViews?.includes(MATERIAL_VIEWS.COSTING)) {
        orgData.forEach((org, index) => {
          if (requestType !== REQUEST_TYPE.EXTEND && !props.isDisplay) {
            getItemCatGrpMappingDT({ combinations, index, dt: DT_TABLES.REG_PLNT_INSPSTK_MAPPING }, org);
          }
        });
      }
      if (requestType === REQUEST_TYPE.EXTEND) {
        let payloadForCopyMat = {
          "copyPayload":{
            "payloadData":payloadState[props.materialID]?.payloadData,
            "unitsOfMeasureData": payloadState[props.materialID]?.unitsOfMeasureData,
            "additionalData":payloadState[props.materialID]?.additionalData
          }
        } 
        if((payloadState?.OrgElementDefaultValues) && !props.isDisplay) constructPayloadFromCopyMat(combinations,payloadForCopyMat);
      }else if(requestType === REQUEST_TYPE.CREATE){
        if((isRefMaterial || payloadState?.OrgElementDefaultValues) && !props.isDisplay) constructPayloadFromCopyMat(combinations,payloadState[props.materialID]?.headerData?.refMaterialData);
      }
    } else {
      setPlantCombination({});
    }
  }, [orgData]);
  useEffect(() => {
    if (orgData) {
      const uniqueSalesOrgs = [...new Set(orgData?.map((item) => item.salesOrg?.code))];
      const FormattedSalesOrg = formatUniqueSalesOrg(uniqueSalesOrgs);
      getCountriesBasedOnSalesOrg(FormattedSalesOrg);
    }
  }, [orgData, props?.callGetCountryBasedonSalesOrg]);
  useEffect(() => {
    if (dtData) {
      if (dtData.customParam?.dt === DT_TABLES.SALES_DIV_PRICE_MAPPING && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
        const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_SALESDIV_PRCICEGRP_MAPPING[0]?.MDG_MAT_MATERIAL_PRICING_GROUP : "";
        if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
          if(fieldValue) setFieldValues(dtData.customParam?.comb, "MatPrGrp", fieldValue, "Sales");
        }
      } else if (dtData.customParam?.dt === DT_TABLES.REG_PLNT_INSPSTK_MAPPING) {
        let combs = dtData.customParam?.combinations;
        //let ind = dtData.customParam?.index;
        let org = dtData.customParam?.org;
        if (combs?.hasOwnProperty(MATERIAL_VIEWS.SALES) && props?.selectedViews?.includes(MATERIAL_VIEWS.SALES)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_ITEM_CAT_GROUP : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.salesOrg?.code + "-" + org?.dc?.value?.code, "ItemCat", fieldValue, "Sales");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.PURCHASING) && props?.selectedViews?.includes(MATERIAL_VIEWS.PURCHASING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_POST_TO_INSP_STOCK : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "IndPostToInspStock", fieldValue, "Purchasing");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.ACCOUNTING) && props?.selectedViews?.includes(MATERIAL_VIEWS.ACCOUNTING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_PRICE_UNIT : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "PriceUnit", fieldValue, "Accounting");
          }
        }
        if (combs.hasOwnProperty(MATERIAL_VIEWS.COSTING) && props?.selectedViews?.includes(MATERIAL_VIEWS.COSTING)) {
          const fieldValue = Object.keys(dtData?.data?.result[0])?.length ? dtData?.data?.result?.[0]?.MDG_MAT_REG_PLNT_INSPSTK_ITMCATGRP_MAPPING[0]?.MDG_MAT_COSTING_LOT_SIZE : "";
          if (requestType !== REQUEST_TYPE.EXTEND && requestType !== REQUEST_TYPE.CREATE_WITH_UPLOAD) {
            if(fieldValue) setFieldValues(org?.plant?.value?.code, "Lotsizekey", fieldValue, "Costing");
          }
        }
      }
    }
  }, [dtData]);
  const constructPayloadFromCopyMat = (combination, referenceData) => {
    let payloadData = payloadState[props.materialID]?.payloadData;
    let orgElementDTValues = payloadState?.OrgElementDefaultValues;
    
    Object.keys(combination).forEach(comb => {
      let reduxCombination = combination[comb]?.reduxCombinations;
      reduxCombination?.forEach(reduxComb => {
        const currentPayload = payloadData?.[comb]?.[reduxComb];
        const isEmptyOrUndefined = !currentPayload || Object.keys(currentPayload).length === 0;
        if (comb !== MATERIAL_VIEWS.BASIC_DATA && (referenceData?.copyPayload?.payloadData[comb] || orgElementDTValues) && isEmptyOrUndefined && props?.allTabsData[comb]) {
          // Loop through allTabsData keys instead of referenceData keys
          Object.keys(props?.allTabsData[comb]).forEach(section => {
            const fields = props?.allTabsData[comb][section];
            if (Array.isArray(fields)) {
              fields.forEach(field => {
                const key = field?.jsonName;
                if (key) {
                  // Get the reference value for this key
                  const referenceValue = referenceData?.copyPayload?.payloadData[comb]?.[key];
                  const orgDefaultValue = orgElementDTValues?.[comb]?.[reduxComb]?.[key] || "";
                  let keyValue = getKeysValue(key, referenceValue, props?.allTabsData[comb], orgDefaultValue);
                  if (keyValue) {
                    dispatch(updateMaterialData({
                      materialID: props?.materialID,
                      viewID: comb,
                      itemID: reduxComb,
                      keyName: key,
                      data: keyValue
                    }));
                  }
                }
              });
            }
          });

          // Handle SALES view tax data special cases
          if (comb === MATERIAL_VIEWS.SALES) {
            dispatch(updateMaterialData({
              materialID: props?.materialID,
              viewID: MATERIAL_VIEWS.TAX_DATA,
              itemID: MATERIAL_VIEWS.TAX_DATA,
              data: referenceData?.copyPayload?.payloadData?.TaxData?.TaxData
            }));

            // Process SALES_GENERAL data
            Object.keys(props?.allTabsData[MATERIAL_VIEWS.SALES_GENERAL] || {}).forEach(section => {
              const fields = props?.allTabsData[MATERIAL_VIEWS.SALES_GENERAL][section];
              if (Array.isArray(fields)) {
                fields.forEach(field => {
                  const key = field?.jsonName;
                  if (key) {
                    const referenceValue = referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL]?.[key];
                    let keyValue = getKeysValue(key, referenceValue, props?.allTabsData[MATERIAL_VIEWS.SALES_GENERAL], orgElementDTValues?.[MATERIAL_VIEWS.SALES_GENERAL]?.[MATERIAL_VIEWS.SALES_GENERAL]?.[key]);
                    if (keyValue) {
                      dispatch(updateMaterialData({
                        materialID: props?.materialID,
                        viewID: MATERIAL_VIEWS.SALES_GENERAL,
                        itemID: MATERIAL_VIEWS.SALES_GENERAL,
                        keyName: key,
                        data: keyValue
                      }));
                    }
                  }
                });
              }
            });
          }
          // Handle PURCHASING General special cases
          if (comb === MATERIAL_VIEWS.PURCHASING && referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.PURCHASING_GENERAL]) {
            Object.keys(props?.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL] || {}).forEach(section => {
              const fields = props?.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL][section];
              if (Array.isArray(fields)) {
                fields.forEach(field => {
                  const key = field?.jsonName;
                  if (key) {
                    const referenceValue = referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[key];
                    let keyValue = getKeysValue(key, referenceValue, props?.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL], orgElementDTValues?.[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[MATERIAL_VIEWS.PURCHASING_GENERAL]?.[key]);
                    if (keyValue) {
                      dispatch(updateMaterialData({
                        materialID: props?.materialID,
                        viewID: MATERIAL_VIEWS.PURCHASING_GENERAL,
                        itemID: MATERIAL_VIEWS.PURCHASING_GENERAL,
                        keyName: key,
                        data: keyValue
                      }));
                    }
                  }
                });
              }
            });
          }

          // Handle STORAGE General special cases
          if (comb === MATERIAL_VIEWS.STORAGE && referenceData?.copyPayload?.payloadData?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]) {
            Object.keys(props?.allTabsData[MATERIAL_VIEWS.STORAGE_GENERAL] || {}).forEach(section => {
              const fields = props?.allTabsData[MATERIAL_VIEWS.STORAGE_GENERAL][section];
              if (Array.isArray(fields)) {
                fields.forEach(field => {
                  const key = field?.jsonName;
                  if (key) {
                    const referenceValue = referenceData?.copyPayload?.payloadData[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[key];
                    let keyValue = getKeysValue(key, referenceValue, props?.allTabsData[MATERIAL_VIEWS.STORAGE_GENERAL], orgElementDTValues?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[MATERIAL_VIEWS.STORAGE_GENERAL]?.[key]);
                    if (keyValue) {
                      dispatch(updateMaterialData({
                        materialID: props?.materialID,
                        viewID: MATERIAL_VIEWS.STORAGE_GENERAL,
                        itemID: MATERIAL_VIEWS.STORAGE_GENERAL,
                        keyName: key,
                        data: keyValue
                      }));
                    }
                  }
                });
              }
            });
          }
        }
      });
    });
  };
  const getSalesGrpPriceMapDT = (comb, org) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.SALES_DIV_PRICE_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_SALES_ORG": org?.salesOrg?.code,
          "MDG_CONDITIONS.MDG_MAT_DIVISION": payloadState?.payloadData?.Division,
        },
      ],
    };
    comb.org = org;
    getDtCall(payload, comb);
  };
  const getItemCatGrpMappingDT = (comb, org) => {
    let payload = {
      decisionTableId: null,
      decisionTableName: DT_TABLES.REG_PLNT_INSPSTK_MAPPING,
      version: "v1",
      conditions: [
        {
          "MDG_CONDITIONS.MDG_MAT_REGION": payloadState?.payloadData?.Region,
          "MDG_CONDITIONS.MDG_MAT_PLANT": org?.plant?.value?.code,
        },
      ],
    };
    comb.org = org;
    getDtCall(payload, comb);
  };
  useEffect(() => {
    if (!orgData || !plantCombination[props.activeViewTab]) return;
    const { reduxCombinations = [] } = plantCombination[props.activeViewTab];
    const expandArr = reduxCombinations?.map((comb) =>
      props?.missingValidationPlant?.includes(comb) && props.mandatoryFailedView === props.activeViewTab
    );

    setExpanded(expandArr);
    // Scroll to the mandatory fields first missing plant accordion
    if (props.missingValidationPlant || props.missingValidationPlant?.length){
      const missingKey = props.missingValidationPlant[0];
      const ref = accordionRefs?.current[missingKey];
      if (ref && ref?.scrollIntoView) {
        setTimeout(() => ref.scrollIntoView({ behavior: "smooth", block: "center" }), 700);
      }
    }
  }, [props.activeViewTab, orgData, props?.missingValidationPlant, plantCombination]);

  const handleAccordionChange = (comb, requiredKeys, index) => (event, isExpanded) => {
    setExpanded((prevExpanded) => ({
      ...prevExpanded,
      [index]: isExpanded,
    }));
    
    // if (isExpanded) {
    //   let loaderObj = [...(mrpLoaderArr || [])];
    //   loaderObj[index] = {};
    //   loaderObj[index].value = 1;
    //   if (isWithReference === "yes") {
    //     fetchViewData(comb, index), setMrpLoaderArr(loaderObj);
    //   }
    // }
  };
  const fetchViewData = (comb, index) => {
    let orgKeys = orgData[index];
    if (props.activeViewTab) {
      let payload = {};
      let url = "";
      let plantData = "";
      if (props.activeViewTab === "Purchasing" || props.activeViewTab === "Costing" || props.activeViewTab === "MRP") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          plant: orgKeys?.plant?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedPlantData`;
      } else if (props.activeViewTab === "Accounting") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          valArea: orgKeys?.plant?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedAccountingData`;
      } else if (props.activeViewTab === "Sales") {
        payload = {
          materialNo: payloadState[props.materialID]?.headerData?.materialNumber,
          salesOrg: orgKeys?.salesOrg?.code || "",
          distChnl: orgKeys?.dc?.value?.code || "",
        };
        plantData = comb;
        url = `/${destination_MaterialMgmt}/data/displayLimitedSalesData`;
      }

      const hSuccess = (data) => {
        let loaderObj = [...(mrpLoaderArr || [])];
        loaderObj[index] = {};
        loaderObj[index].value = 0;
        setMrpLoaderArr(loaderObj);
        if (props.activeViewTab === "Purchasing" || props.activeViewTab === "Costing" || props.activeViewTab === "MRP") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificPlantDataViewDto[0],
            })
          );
        } else if (props.activeViewTab === "Accounting") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificAccountingDataViewDto[0],
            })
          );
        } else if (props.activeViewTab === "Sales") {
          dispatch(
            pushMaterialDisplayData({
              materialID: props.materialID,
              viewID: props.activeViewTab,
              itemID: comb,
              data: data?.body?.SpecificSalesDataViewDto[0],
            })
          );
        }
      };

      const hError = () => {};

      const shouldCallApi = !payloadState?.[props.materialID]?.payloadData?.[props.activeViewTab]?.[plantData];

      if (shouldCallApi) {
        doAjax(url, "post", hSuccess, hError, payload);
      } else {
        let loaderObj = [...(mrpLoaderArr || [])];
        loaderObj[index] = {};
        loaderObj[index].value = 0;
        setMrpLoaderArr(loaderObj);
      }
    }
  };
  const setFieldValues = (comb, fieldName, fieldValue, tab) => {
    dispatch(
      updateMaterialData({
        materialID: props?.materialID || "",
        keyName: fieldName || "",
        data: fieldValue ?? null,
        viewID: tab,
        itemID: comb,
      })
    );
  };
  const checkForMandatoryFieldCard = (cardFields, missingFields) => {
    return cardFields.some((field) => missingFields.includes(field.fieldName));
  };
  // Memoized content for dynamic rendering
  const memoizedComponent = useMemo(() => {
    const activeViewData = plantCombination[props.activeViewTab] || {};
    const { displayCombinations = [], reduxCombinations = [], requiredKeys = [] } = activeViewData;
    const filterFields = Object.entries(props?.basicDataTabDetails || {});
    const salesGeneralFields = props.allTabsData?.hasOwnProperty(MATERIAL_VIEWS.SALES_GENERAL) ? Object.entries(props.allTabsData[MATERIAL_VIEWS.SALES_GENERAL]) : [];
    const purchaseGeneralFields = props.allTabsData?.hasOwnProperty(MATERIAL_VIEWS.PURCHASING_GENERAL) ? Object.entries(props.allTabsData[MATERIAL_VIEWS.PURCHASING_GENERAL]) : [];
    const StorageGeneralFields = props.allTabsData?.hasOwnProperty(MATERIAL_VIEWS.STORAGE_GENERAL) ? Object.entries(props.allTabsData[MATERIAL_VIEWS.STORAGE_GENERAL]) : [];
    if (props.activeViewTab === "Basic Data") {
      return filterFields.map((item) => (
        <Grid
          item
          md={12}
          sx={{
            backgroundColor: "white",
            maxHeight: "max-content",
            height: "max-content",
            borderRadius: "8px",
            border: `1px solid ${(props?.missingValidationPlant?.includes(MATERIAL_VIEWS.BASIC_DATA) && checkForMandatoryFieldCard(item[1],props.missingFields)) && !CurrentMaterialRows?.validated ? colors.error.dark : colors.hover.hoverbg} `,
            mt: 0.25,
            boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
            ...container_Padding,
          }}
          key={item[0]}
        >
          <Grid container>
            <Typography
              sx={{
                fontSize: "12px",
                fontWeight: "700",
                paddingBottom: "10px",
              }}
            >
              {item[0]}
            </Typography>
          </Grid>
          <Box>
            <Grid container spacing={1}>
              {[...item[1]]
                .filter((x) => x.visibility !== "Hidden")
                .sort((a, b) => a.sequenceNo - b.sequenceNo)
                .map((innerItem) => (
                  <FilterField key={innerItem.fieldName} disabled={props?.disabled} field={innerItem} dropDownData={props.dropDownData} materialID={props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} viewName={props?.activeViewTab} plantData={"basic"} missingFields={Array.isArray(props.missingFields) ? props.missingFields : []} />
                ))}
            </Grid>
          </Box>
        </Grid>
      ));
    }
    else if (props.activeViewTab === MATERIAL_VIEWS.CLASSIFICATION) {
      return (<>{
          <Grid
            md={12}
            sx={{
              backgroundColor: "white",
              maxHeight: "max-content",
              height: "max-content",
              borderRadius: "8px",
              border: `1px solid ${colors.hover.hoverbg} `,
              mt: 0.25,
              boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
              ...container_Padding,
            }}
            key={filterFields[0]?.[0]}
          >
            <Grid container>
              <Typography
                sx={{
                  fontSize: "12px",
                  fontWeight: "700",
                  paddingBottom: "10px",
                }}
              >
                {filterFields[0]?.[0]}
              </Typography>
            </Grid>
            <Box>
              <Grid container spacing={1}>
                {[...filterFields[0]?.[1]]
                  .filter((x) => x.visibility !== VISIBILITY_TYPE.HIDDEN1)
                  .sort((a, b) => a.sequenceNo - b.sequenceNo)
                  .map((innerItem) => (
                    <>
                    {innerItem?.visibility==VISIBILITY_TYPE.HIDDEN ?
                    <FilterField classNum={props?.classNum} key={innerItem.fieldName} disabled={props?.disabled} field={innerItem} dropDownData={props.dropDownData} materialID={props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} viewName={props?.activeViewTab} plantData={"basic"} matType={props?.matType} missingFields={Array.isArray(props.missingFields) ? props.missingFields : []}/>
                    :  
                    <FilterField classNum={props?.classNum} key={innerItem.fieldName} disabled={props?.disabled} field={innerItem} dropDownData={props.dropDownData} materialID={props?.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} viewName={props?.activeViewTab} plantData={"basic"} matType={props?.matType} missingFields={Array.isArray(props.missingFields) ? props.missingFields : []}/>
                    }
                    </>
                  ))}
              </Grid>
            </Box>
          </Grid>
        }
        <ClassificationView characteristicDetails = {filterFields[1]} materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} classNum={props?.classNum} disabled={props.disabled} dropDownData={props.dropDownData} activeViewTab={props.activeViewTab}/>
      </>)
    }
    else if (!displayCombinations.length) {
      return (
        <Typography variant="body2" sx={{ margin: "20px", color: "gray" }}>
          No Org Data selected.
        </Typography>
      );
    }

    return (
      <>
        {props.activeViewTab === MATERIAL_VIEWS.SALES && (
          <>
            <TaxData materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} loading={taxDataLoading}/>
            {salesGeneralFields?.length > 0 && <GenericViewGeneral materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} GeneralFields={salesGeneralFields} disabled={props.disabled} dropDownData={props.dropDownData} viewName={MATERIAL_VIEWS?.SALES_GENERAL} isMandatoryFailed={props?.missingValidationPlant?.includes(MATERIAL_VIEWS.SALES_GENERAL) && !CurrentMaterialRows?.validated} missingFields={props.missingFields?.[MATERIAL_VIEWS.SALES_GENERAL]} />}
          </>
        )}
        {props.activeViewTab === MATERIAL_VIEWS.PURCHASING && <> {purchaseGeneralFields?.length > 0 && <GenericViewGeneral materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} GeneralFields={purchaseGeneralFields} disabled={props.disabled} dropDownData={props.dropDownData} viewName={MATERIAL_VIEWS?.PURCHASING_GENERAL} isMandatoryFailed={props?.missingValidationPlant?.includes(MATERIAL_VIEWS.PURCHASING_GENERAL) && !CurrentMaterialRows?.validated} missingFields={props.missingFields?.[MATERIAL_VIEWS.PURCHASING_GENERAL]} />}</>}
        {props.activeViewTab === MATERIAL_VIEWS.STORAGE && <> {StorageGeneralFields?.length > 0 && <GenericViewGeneral materialID={props.materialID} selectedMaterialNumber={props?.selectedMaterialNumber} GeneralFields={StorageGeneralFields} disabled={props.disabled} dropDownData={props.dropDownData} viewName={MATERIAL_VIEWS?.STORAGE_GENERAL} isMandatoryFailed={props?.missingValidationPlant?.includes(MATERIAL_VIEWS.STORAGE_GENERAL) && !CurrentMaterialRows?.validated} missingFields={props.missingFields?.[MATERIAL_VIEWS.STORAGE_GENERAL]} />}</>}
        {displayCombinations.map((combination, index) => {
          return (
            <Accordion ref={el => { accordionRefs.current[combination] = el; }} sx={{ marginBottom: "20px", boxShadow: 3, borderRadius: "10px", borderColor: (props?.missingValidationPlant?.includes(combination) && props.mandatoryFailedView === props.activeViewTab && !CurrentMaterialRows?.validated) ? colors?.error?.dark : colors?.primary.white }} key={index} onChange={handleAccordionChange(combination, requiredKeys, index)} expanded={expanded[index] === true}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{
                  backgroundColor: colors.primary.whiteSmoke,
                  borderRadius: "10px",
                  padding: "8px 16px",
                  "&:hover": { backgroundColor: colors.hover.hoverbg },
                }}
              >
                <Typography variant="h6" sx={{ fontWeight: "bold" }}>
                  {formatCombinationDisplay(combination, requiredKeys)}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                {mrpLoaderArr[index]?.value === 1 ? (
                  <Box sx={{ display: "flex", alignItems: "center", justifyContent: "center", minHeight: "200px" }}>
                    {" "}
                    <CircularProgress />
                  </Box>
                ) : (
                  filterFields.map((item) => (
                    <Grid
                      item
                      md={12}
                      sx={{
                        backgroundColor: "white",
                        maxHeight: "max-content",
                        height: "max-content",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                        mt: 0.25,
                        boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                        ...container_Padding,
                      }}
                      key={item[0]}
                    >
                      <Grid container>
                        <Typography
                          sx={{
                            fontSize: "12px",
                            fontWeight: "700",
                            paddingBottom: "10px",
                          }}
                        >
                          {t(item[0])}
                        </Typography>
                      </Grid>
                      <Box>
                        <Grid container spacing={1}>
                          {[...item[1]]
                            .filter((x) => x.visibility !== "Hidden")
                            .sort((a, b) => a.sequenceNo - b.sequenceNo)
                            .map((innerItem) => (
                              <FilterField
                                key={innerItem.fieldName}
                                disabled={props?.disabled}
                                field={innerItem}
                                dropDownData={props.dropDownData}
                                materialID={props?.materialID}
                                selectedMaterialNumber={props?.selectedMaterialNumber}
                                viewName={props?.activeViewTab}
                                plantData={reduxCombinations[index]} // Pass Redux-friendly combination
                                missingFields={props.missingFields?.[reduxCombinations[index]]}
                              />
                            ))}
                        </Grid>
                      </Box>
                    </Grid>
                  ))
                )}
              </AccordionDetails>
            </Accordion>
          );
        })}
      </>
    );
  }, [plantCombination, props.activeViewTab, props.basicDataTabDetails, mrpLoaderArr, props.materialID, props.missingValidationPlant, expanded]);

  return <>{memoizedComponent}</>;
};

export default GenericTabs;
