import React from 'react';
import {
  Modal,
  Form,
  Row,
  Col,
  Select,
  Input,
  InputNumber,
  Button,
  Space,
  Typography,
  notification
} from 'antd';
import { SaveOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { colors } from '@constant/colors';
import { WORKFLOW_DATA_CONSTANTS } from '@constant/enum';
import { useTheme } from '@mui/material';

const { Text } = Typography;
const { Option } = Select;

const WorkflowModals = ({
  workflowData,
  // Add Task Modal
  isAddModalVisible,
  setIsAddModalVisible,
  addForm,
  handleAddTask,
  getAvailableLevels,
  handleLevelNumberChange,
  selectedLevel,
  workflowGroups,
  getSendBackAllowedLevels,
  
  // Edit Task Modal
  isEditModalVisible,
  setIsEditModalVisible,
  cardToEdit,
  setCardToEdit,
  editDialogForm,
  handleSaveEditedTask,
  findTaskLevel,
  
  // Move Task Modal
  isMoveModalVisible,
  setIsMoveModalVisible,
  cardToMove,
  setCardToMove,
  moveForm,
  handleMoveTask,
  
  // Level Name Change Modal
  isLevelNameChangeModalVisible,
  setIsLevelNameChangeModalVisible,
  levelNameChangeData,
  setLevelNameChangeData,
  handleLevelNameChangeConfirm,

  // Level Name Change Modal
  isBenchStatusChangeModalVisible,
  setIsBenchStatusChangeModalVisible,
  benchStatusChangeData,
  setBenchStatusChangeData,
  handleBenchStatusChangeConfirm
}) => {
  const theme = useTheme()
  return (
    <>
      {/* Add Task Modal */}
      <Modal
        title="Add New Task"
        open={isAddModalVisible}
        onCancel={() => {
          setIsAddModalVisible(false);
          addForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={addForm}
          layout="vertical"
          onFinish={handleAddTask}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NUM?.name}
                label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NUM?.label}
                rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NUM }]}
              >
                <Select
                  showSearch
                  placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NUM?.placeholder}
                  optionFilterProp="children"
                  onChange={handleLevelNumberChange}
                  filterOption={(input, option) =>
                    option?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {getAvailableLevels().map(level => (
                    <Option key={level} value={level}>
                      {level === -1 ? 'Requestor Level (-1)' : `Level ${level}`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NAME?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NAME?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NAME?.message }]}>
                <Input placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.LVL_NAME?.placeholder} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.name}
                label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.label}
                rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.message }]}
              >
                <Select
                  showSearch
                  placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.placeholder}
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option?.children?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {workflowGroups.map((group) => (
                    <Select.Option key={group} value={group}>
                      {group}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.message }]}>
                <Input placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.placeholder} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.label}>
                <Select
                  mode="multiple"
                  placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.placeholder}
                  allowClear
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {(() => {
                    const selectedLevelForm = addForm.getFieldValue('levelNumber') || selectedLevel;
                    if (selectedLevelForm !== undefined && selectedLevelForm !== null) {
                      return getSendBackAllowedLevels(selectedLevelForm).filter(level => level !== 0).map(level => (
                        <Option key={level} value={level}>
                          {level === -1 ? 'Requestor Level (-1)' : `Level ${level}`}
                        </Option>
                      ));
                    }
                    return [];
                  })()}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.message }]}>
                <Input placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.message} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.message }]}>
                <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.placeholder} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.message }]}>
                <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.placeholder} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.message }]}>
                <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.placeholder} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsAddModalVisible(false);
                addForm.resetFields();
              }}>
                Cancel
              </Button>
              <Button type="primary" htmlType="submit" style={{ backgroundColor: theme.palette.primary.main }}>
                Add Task
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Edit Task Modal */}
      <Modal
        title="Edit Task"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false);
          setCardToEdit(null);
          editDialogForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {cardToEdit && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
              <Text strong>Editing: </Text>
              <Text>{cardToEdit.workflowTaskName}</Text>
              <Text type="secondary" style={{ marginLeft: 8 }}>
                (Level {findTaskLevel(cardToEdit.id)})
              </Text>
            </div>

            <Form
              form={editDialogForm}
              layout="vertical"
              onFinish={handleSaveEditedTask}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.message }]}>
                    <Select
                        showSearch
                        placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.WORKFLOW_GROUP?.placeholder}
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                            option?.children?.toLowerCase().includes(input.toLowerCase())
                        }
                        >
                        {workflowGroups.map((group) => (
                            <Select.Option key={group} value={group}>
                            {group}
                            </Select.Option>
                        ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.message }]}>
                    <Input placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_NAME?.placeholder} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.label}>
                    <Select
                      mode="multiple"
                      placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SEND_BACK_ALLOWED_TO?.placeholder}
                      allowClear
                    >
                      {getSendBackAllowedLevels(findTaskLevel(cardToEdit.id))?.filter(level => level !== 0)?.map(level => (
                        <Option key={level} value={level}>
                          {level === -1 ? 'Requestor Level (-1)' : `Level ${level}`}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.message }]}>
                    <Input placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.TASK_DESC?.message} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.message }]}>
                    <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_HIGH?.placeholder} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.message }]}>
                    <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_MEDIUM?.placeholder} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.name} label={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.label} rules={[{ required: true, message: WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.message }]}>
                    <InputNumber min={1} max={168} style={{ width: '100%' }} placeholder={WORKFLOW_DATA_CONSTANTS?.MODAL_FILTERS?.SLA_LOW?.placeholder} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => {
                    setIsEditModalVisible(false);
                    setCardToEdit(null);
                    editDialogForm.resetFields();
                  }}>
                    Cancel
                  </Button>
                  <Button type="primary" htmlType="submit" icon={<SaveOutlined />} style={{ backgroundColor: theme.palette.primary.main }}>
                    Save Changes
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* Move Task Modal */}
      <Modal
        title="Move Task"
        open={isMoveModalVisible}
        onCancel={() => {
          setIsMoveModalVisible(false);
          setCardToMove(null);
          moveForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        {cardToMove && (
          <div>
            <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 6 }}>
              <Text strong>Moving: </Text>
              <Text>{cardToMove.workflowTaskName}</Text>
            </div>

            <Form
              form={moveForm}
              layout="vertical"
              onFinish={handleMoveTask}
            >
              <Form.Item name="newLevel" label="Select New Level" rules={[{ required: true }]}>
                <Select placeholder="Select destination level">
                  {getAvailableLevels()
                    .filter(level => level.toString() !== findTaskLevel(cardToMove.id))
                    .map(level => (
                      <Option key={level} value={level}>
                        {level === -1 ? 'Requestor Level (-1)' : `Level ${level}`}
                      </Option>
                    ))}
                </Select>
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => {
                    setIsMoveModalVisible(false);
                    setCardToMove(null);
                    moveForm.resetFields();
                  }}>
                    Cancel
                  </Button>
                  <Button type="primary" htmlType="submit" style={{ backgroundColor: theme.palette.primary.main }}>
                    Move Task
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* Level Name Change Confirmation Modal */}
      <Modal
        title="Confirm Level Name Change"
        open={isLevelNameChangeModalVisible}
        onOk={handleLevelNameChangeConfirm}
        onCancel={() => {
          setIsLevelNameChangeModalVisible(false);
          setLevelNameChangeData(null);
        }}
        okText="Confirm Change"
        cancelText="Cancel"
        okType="primary"
      >
        {levelNameChangeData && (
          <div>
            <p>
              Level {levelNameChangeData.levelNumber} already has tasks with the name "{levelNameChangeData.currentLevelName}".
            </p>
            <p>
              Are you sure you want to change it to "{levelNameChangeData.newLevelName}"?
            </p>
            <p style={{ color: '#fa8c16', fontSize: '12px' }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              This will affect all tasks in this level.
            </p>
          </div>
        )}
      </Modal>

      {/* Bench Status Change Confirmation Modal */}
      <Modal
        title="Confirm Request Bench Status Change"
        open={isBenchStatusChangeModalVisible}
        onOk={handleBenchStatusChangeConfirm}
        onCancel={() => {
          setIsBenchStatusChangeModalVisible(false);
          setBenchStatusChangeData(null);
        }}
        okText="Confirm Change"
        cancelText="Cancel"
        okType="primary"
      >
        {benchStatusChangeData && (
          <div>
            <p>
              Level {benchStatusChangeData.levelNumber} currently has {workflowData[benchStatusChangeData.levelNumber]?.tasks?.length || 0} task(s) with Request Bench Status "{benchStatusChangeData.currentBenchStatus}".
            </p>
            <p>
              Are you sure you want to change it to "{benchStatusChangeData.newBenchStatus}"?
            </p>
            <p style={{ color: '#fa8c16', fontSize: '12px' }}>
              <ExclamationCircleOutlined style={{ marginRight: 4 }} />
              This will update the Request Bench Status for ALL tasks in Level {benchStatusChangeData.levelNumber}.
            </p>
          </div>
        )}
      </Modal>
    </>
  );
};

export default WorkflowModals;