import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  FormControl,
  Grid,
  Typography,
  styled,
  useTheme,
} from "@mui/material";
import {
  button_Outlined,
  button_Primary,
  font_Small,
  icon_MarginLeft,
} from "../common/commonStyles";
import DateRange from "../common/DateRangePicker";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { useState } from "react";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { commonFilterClear, commonFilterUpdate } from "../../app/commonFilterSlice";
import FilterField from "../common/ReusableFilterBox/FilterField";
import FilterListIcon from '@mui/icons-material/FilterList';
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import AutoCompleteSimpleDropDown from "@components/Common/ui/dropdown/AutoCompleteSimpleDropDown";
import useLang  from "@hooks/useLang";
import { MODULE_OPTIONS } from "@constant/enum";

const StyledAccordion = styled(Accordion)(({ theme }) => ({
  marginTop: "0px !important",
  border: `1px solid ${theme.palette.primary.main}`,
  borderRadius: '8px',
  boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
  '&:not(:last-child)': {
    borderBottom: 0,
  },
  '&:before': {
    display: 'none',
  },
}));
const StyledAccordionSummary = styled(AccordionSummary)(({ theme }) => ({
  minHeight: "2rem !important",
  margin: "0px !important",
  backgroundColor:  theme.palette.primary.light,
  borderRadius: '8px 8px 0 0',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light,
  },
}));

const FilterDashboard = ({handleSearch, company, supplier,clearFunction}) => {
   const theme = useTheme();
  const rbSearchForm = useSelector(
    (state) => state.commonFilter["RequestBench"]
  );
  const dashboardSearchForm = useSelector(
    (state) => state.commonFilter["Dashboard"]
    );
  const { t } = useLang();
  const [companyCodeSet, setCompanyCodeSet] = useState({});
  const [vendorDetailsSet, setVendorDetailsSet] = useState({});
  const [requestType,setRequestType]=useState([])
  const [requestStatus,setRequestStatus]=useState([])
  const [userIdName,setUserIdName]=useState([])
  const [moduleName,setModuleName]=useState([])
  const [regionName,setRegionName]=useState([])
  const [dateTime,setDateTime]=useState([...dashboardSearchForm.dashboardDate])

  useEffect(() => {
    var RegName = regionName
    .map((item) => item)
    .join("$^$");

      let tempFilterData = {
        ...rbSearchForm,
        selectedRegion: RegName,
      };
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: tempFilterData,
        })
      );
  },[regionName])

  useEffect(()=>{
    let tempFilterData = ''
    tempFilterData = {
        ...dashboardSearchForm,
        dashBoardModuleName: moduleName,
    };
    dispatch(
        commonFilterUpdate({
            module: "Dashboard",
            filterData: tempFilterData,
        })
    );
  },[moduleName])

  useEffect(()=>{
    let userIdArr = []
      userIdName?.map((itemData) => {
        userIdArr.push(itemData?.code)
      })

      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedusersId: userIdArr,
          },
        })
      );

  },[userIdName])

  useEffect(()=>{
    let reqStatusArr = []
      requestStatus?.map((itemData) => {
        reqStatusArr.push(itemData?.lable)
      })

      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestStatus: reqStatusArr,
          },
        })
      );

  },[requestStatus])

  useEffect(()=>{
    let reqTypeArr = []
      requestType?.map((itemData) => {
        reqTypeArr.push(itemData?.lable)
      })
      dispatch(
        commonFilterUpdate({
          module: "Dashboard",
          filterData: {
            ...dashboardSearchForm,
            selectedRequestType: reqTypeArr,
          },
        })
      );
  },[requestType])

const presentDate = new Date();
const backDate = new Date();
backDate.setDate(backDate.getDate() - 7);
  
const dispatch = useDispatch();
  const appSettings=useSelector((state)=> state.appSettings)
  const handleDate = (e) => {
    const tempdate=e;

    dispatch(
      commonFilterUpdate({
        module: "Dashboard",
        filterData: {
          ...dashboardSearchForm,
          dashboardDate: tempdate,
        },
      })
    );
  };

  useEffect(() => {
    if (dashboardSearchForm?.dashboardDate) {
      const presentDate = new Date(dashboardSearchForm?.dashboardDate[0]);
      const backDate = new Date(dashboardSearchForm?.dashboardDate[1]);
      setDateTime([presentDate,backDate]);
    }
  },[dashboardSearchForm?.dashboardDate])

  const moduleFilterData = [
    {
      type: "singleSelect",
      filterName: "companyCode",
      filterData: companyCodeSet,
      filterTitle: "Company Code",
    },
    {
      type: "singleSelect",
      filterName: "vendorNo",
      filterData: vendorDetailsSet,
      filterTitle: "Business Partner",
    },
    {
      type: "dateRange",
      filterName: "dashboardDate",
      filterTitle: "Date Range",
    },
  ];
  

  const regionOptions=[
     'US',
      'EUR'
  ]
  const handleSelectAllRegion = () => {
    if (regionName.length === regionOptions?.length) {
      setRegionName([]);
     
    } else {
      setRegionName(regionOptions);
    }
  };

  return (
    <>
      <Grid item md={12}>
        <StyledAccordion defaultExpanded={false}>
              <StyledAccordionSummary
                  expandIcon={<ExpandMoreIcon sx={{ fontSize: '1.25rem', color: theme.palette.primary.dark }} />}
                  aria-controls="panel1a-content"
                  id="panel1a-header"
                  className="filterDashBoard"
                >
                  <FilterListIcon sx={{ fontSize: '1.25rem', marginRight: 1, color: theme.palette.primary.dark }} />
                  <Typography
                    sx={{
                     fontSize: '0.875rem',
                      fontWeight: 600,
                      color: theme.palette.primary.dark,
                    }}
                  >
                    {t("Filter Dashboard")}
                  </Typography>
                </StyledAccordionSummary>
          <AccordionDetails>
            <Grid
              container
              rowSpacing={1}
              spacing={2}
            >

              {/* WILL BE UNCOMMENT LATER */}
              <Grid item md={2}>
                <Typography sx={font_Small}>
                  {t("Module")}
                </Typography>
                <AutoCompleteSimpleDropDown
                    options={[
                      ...MODULE_OPTIONS
                        .filter((name) => name !== "Select All")
                        .sort((a, b) => a.localeCompare(b))
                    ]}
                    value={dashboardSearchForm?.dashBoardModuleName?.length > 0 && dashboardSearchForm?.dashBoardModuleName || moduleName?.length > 0 && moduleName || [MODULE_OPTIONS[0]]}
                    onChange={(value) => {
                      if (
                        value.length > 0 &&
                        value[value.length - 1]?.label ===
                          "Select All"
                      ) {
                        handleSelectAllModule();
                      } else {
                        setModuleName(value);
                      }
                    }}
                    placeholder="Select Module Name"
                  />
              </Grid>

              <Grid item md={2}>
                <Typography sx={font_Small}>
                  {t("Region")}
                </Typography>
                <AutoCompleteSimpleDropDown
                    options={[
                      ...regionOptions
                        .filter((name) => name !== "Select All")
                        .sort((a, b) => a.localeCompare(b))
                    ]}
                    value={regionName}
                    onChange={(value) => {
                      if (
                        value.length > 0 &&
                        value[value.length - 1]?.label ===
                          "Select All"
                      ) {
                        handleSelectAllRegion();
                      } else {
                        setRegionName(value);
                      }
                    }}
                    placeholder="Select Region"
                  />
              </Grid>

              
              
              <Grid item md={2}>
                <Typography sx={font_Small}>{t("Date Range")}</Typography>
                <FormControl
                  fullWidth
                  sx={{ padding: 0, height: "37px" }}
                >
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DateRange
                      handleDate={handleDate}
                      cleanDate = {false}
                      date={dateTime}
                    />
                  </LocalizationProvider>
                </FormControl>
              </Grid>
              {moduleFilterData?.map((filter) => filter?.hideFilter ? (<></>) : (
                  <Grid item md={3} key={filter.filterTitle}>
                    <FilterField
                      type={filter.type}
                      filterName={filter.filterName}
                      filterData={filter.filterData}
                      moduleName={"Dashboard"}
                      onChangeFilter={filter.onChangeFilter}
                      filterTitle={filter.filterTitle}
                    />
                  </Grid>
                ))}
            </Grid>
            <Grid
              container
              style={{
                display: "flex",
                justifyContent: "flex-end",
              }}
            >
              <Grid
                item
                style={{
                  display: "flex",
                  justifyContent: "space-around",
                }}
              >
                <Button
                  variant="outlined"
                  sx={{ ...button_Outlined}}
                  onClick={() =>{
                    setDateTime([backDate,presentDate]);
                    setRequestStatus([])
                    setRequestType([]);
                    setRegionName([])
                    setUserIdName([]);
                    setModuleName([]);
                    clearFunction();
                    dispatch(commonFilterClear({ module: "Dashboard",days:appSettings.range }))
                    dispatch(
                      commonFilterUpdate({
                        module: "Dashboard",
                        filterData: {
                          ...dashboardSearchForm,
                          selectedRequestType: [],
                          selectedRequestStatus:[],
                          selectedusersId:[],
                          selectedRegion:"",
                          dashboardDate: [ backDate,presentDate],
                        },
                      })
                    );
                  
                  }
                  }
                >
                  {t("Clear")}
                </Button>
                {/* NOTE: WILL UNCOMMENT AFTER DISCUSSION */}
                {/* <Button
                  variant="contained"
                  sx={{ ...button_Primary, ...icon_MarginLeft }}
                  onClick={handleSearch}
                >
                  {t("Apply")}
                </Button> */}
              </Grid>
            </Grid>
          </AccordionDetails>
        </StyledAccordion>
      </Grid>
    </>
  );
};

export default FilterDashboard;
