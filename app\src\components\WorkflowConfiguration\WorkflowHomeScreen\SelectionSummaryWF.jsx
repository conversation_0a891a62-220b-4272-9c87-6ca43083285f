import React, { useState } from "react";
import { 
  Paper, 
  Typo<PERSON>, 
  Button, 
  Divider, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  IconButton, 
  TableContainer, 
  Table, 
  TableHead, 
  TableBody, 
  TableRow, 
  TableCell, 
  Chip 
} from "@mui/material";
import WarningIcon from "@mui/icons-material/Warning";
import CloseIcon from "@mui/icons-material/Close";
import ViewDetailsIcon from "@mui/icons-material/Visibility";
import DeleteForeverOutlined from "@mui/icons-material/DeleteForeverOutlined";
import DownloadIcon from "@mui/icons-material/Download";
import { colors } from "@constant/colors";
import { DELETE_MODAL_BUTTONS_NAME } from "@constant/enum";
import CustomDialog from "@components/Common/ui/CustomDialog";
import useLang from "@hooks/useLang";
import { button_Outlined, button_Primary } from "@components/Common/commonStyles";
import { useTheme } from "@mui/styles";

const SelectionSummaryWF = ({ 
  selectedRows, 
  count, 
  tableData, 
  handleMassCancel, 
  handleMassDownload,
  maxLimit = 10 
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const { t } = useLang();
  const theme = useTheme()

  if (selectedRows.length === 0) return null;

  const selectedRequests = tableData.filter((row) => selectedRows.includes(row.id));
  const isExceededLimit = selectedRows.length > maxLimit;

  const handleCancelClick = () => {
    setShowCancelConfirmation(true);
  };

  const handleConfirmCancel = () => {
    setShowCancelConfirmation(false);
    handleMassCancel(selectedRows);
  };

  const handleDownloadClick = () => {
    handleMassDownload(selectedRows);
  };

  return (
    <>
      <Paper
        elevation={3}
        sx={{
          position: "fixed",
          bottom: 20,
          left: "50%",
          transform: "translateX(-50%)",
          zIndex: 2000,
          padding: "8px 16px",
          backgroundColor: isExceededLimit ? colors.error.deepRed : theme.palette.primary.main,
          color: "white",
          borderRadius: "24px",
          display: "flex",
          alignItems: "center",
          gap: 1,
        }}
      >
        <Typography sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {isExceededLimit && <WarningIcon fontSize="small" />}
          {selectedRows.length} out of {count} selected
          {isExceededLimit && (
            <Typography component="span" sx={{ ml: 1, fontSize: "0.875rem" }}>
              (Maximum {maxLimit} workflows allowed)
            </Typography>
          )}
        </Typography>

        <Divider orientation="vertical" flexItem sx={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }} />
        
        <Button
          variant="text"
          onClick={() => setShowDetails(true)}
          sx={{
            color: "white",
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            padding: "4px 8px",
            minWidth: "auto",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
          startIcon={<ViewDetailsIcon />}
        >
          {t("View Details")}
        </Button>

        <Divider orientation="vertical" flexItem sx={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }} />
        
        <Button
          variant="text"
          onClick={handleDownloadClick}
          disabled={isExceededLimit}
          sx={{
            color: colors.primary.white,
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            padding: "4px 8px",
            minWidth: "auto",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
          startIcon={<DownloadIcon />}
        >
          {t("Download Workflow(s)")}
        </Button>

        <Divider orientation="vertical" flexItem sx={{ backgroundColor: "rgba(255, 255, 255, 0.3)" }} />
        
        <Button
          variant="text"
          onClick={handleCancelClick}
          disabled={isExceededLimit}
          sx={{
            color: colors.primary.white,
            textTransform: "none",
            fontSize: "0.875rem",
            fontWeight: 500,
            padding: "4px 8px",
            minWidth: "auto",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
          startIcon={<DeleteForeverOutlined />}
        >
          {t("Delete Workflow(s)")}
        </Button>
      </Paper>

      {/* View Details Dialog */}
      <Dialog open={showDetails} onClose={() => setShowDetails(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {t("Selected Workflows")}
          <IconButton
            aria-label="close"
            onClick={() => setShowDetails(false)}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <TableContainer 
            component={Paper} 
            sx={{ 
                maxHeight: 400, 
                boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
                borderRadius: '8px',
                border: '1px solid #e5e7eb'
            }}
            >
            <Table 
                size="small" 
                stickyHeader
                sx={{
                '& .MuiTableCell-root': {
                    borderBottom: '1px solid #f3f4f6',
                }
                }}
            >
                <TableHead>
                <TableRow>
                    <TableCell 
                    sx={{ 
                        backgroundColor: '#f8fafc',
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: '#374151',
                        borderBottom: '2px solid #e5e7eb',
                        textTransform: 'uppercase',
                        letterSpacing: '0.025em',
                        padding: '12px 16px'
                    }}
                    >
                    {t("Workflow ID")}
                    </TableCell>
                    <TableCell 
                    sx={{ 
                        backgroundColor: '#f8fafc',
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: '#374151',
                        borderBottom: '2px solid #e5e7eb',
                        textTransform: 'uppercase',
                        letterSpacing: '0.025em',
                        padding: '12px 16px'
                    }}
                    >
                    {t("Workflow Name")}
                    </TableCell>
                    <TableCell 
                    sx={{ 
                        backgroundColor: '#f8fafc',
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: '#374151',
                        borderBottom: '2px solid #e5e7eb',
                        textTransform: 'uppercase',
                        letterSpacing: '0.025em',
                        padding: '12px 16px'
                    }}
                    >
                    {t("Scenario")}
                    </TableCell>
                    <TableCell 
                    sx={{ 
                        backgroundColor: '#f8fafc',
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: '#374151',
                        borderBottom: '2px solid #e5e7eb',
                        textTransform: 'uppercase',
                        letterSpacing: '0.025em',
                        padding: '12px 16px'
                    }}
                    >
                    {t("Created By")}
                    </TableCell>
                    <TableCell 
                    sx={{ 
                        backgroundColor: '#f8fafc',
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: '#374151',
                        borderBottom: '2px solid #e5e7eb',
                        textTransform: 'uppercase',
                        letterSpacing: '0.025em',
                        padding: '12px 16px'
                    }}
                    >
                    {t("Created On")}
                    </TableCell>
                </TableRow>
                </TableHead>
                <TableBody>
                {selectedRequests.map((workflow, index) => (
                    <TableRow 
                    key={workflow.id}
                    sx={{
                        '&:nth-of-type(odd)': {
                        backgroundColor: '#f9fafb',
                        },
                        '&:nth-of-type(even)': {
                        backgroundColor: '#ffffff',
                        },
                    }}
                    >
                    <TableCell 
                        sx={{ 
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        color: '#1f2937',
                        fontWeight: 500
                        }}
                    >
                        {workflow.workflowId}
                    </TableCell>
                    <TableCell 
                        sx={{ 
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        color: '#1f2937',
                        fontWeight: 400
                        }}
                    >
                        {workflow.workflowName}
                    </TableCell>
                    <TableCell 
                        sx={{ 
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        color: '#6b7280'
                        }}
                    >
                        {workflow.scenario}
                    </TableCell>
                    <TableCell 
                        sx={{ 
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        color: '#6b7280'
                        }}
                    >
                        {workflow.createdBy}
                    </TableCell>
                    <TableCell 
                        sx={{ 
                        padding: '12px 16px',
                        fontSize: '0.875rem',
                        color: '#6b7280'
                        }}
                    >
                        {workflow.createdAt}
                    </TableCell>
                    </TableRow>
                ))}
                {selectedRequests.length === 0 && (
                    <TableRow>
                    <TableCell 
                        colSpan={5} 
                        align="center"
                        sx={{
                        padding: '24px',
                        color: '#9ca3af',
                        fontStyle: 'italic',
                        fontSize: '0.875rem'
                        }}
                    >
                        {t("No workflows found")}
                    </TableCell>
                    </TableRow>
                )}
                </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
      </Dialog>

      {/* Mass Cancel Confirmation Dialog */}
      {showCancelConfirmation && (
        <CustomDialog 
          isOpen={showCancelConfirmation} 
          titleIcon={
            <DeleteForeverOutlined 
              size="small" 
              color="error" 
              sx={{ fontSize: "20px" }} 
            />
          } 
          Title={t("Mass Delete Workflows")}
          handleClose={() => setShowCancelConfirmation(false)}
        >
          <DialogContent sx={{ mt: 2 }}>
            <Typography>
              {t("Are you sure you want to delete")} {selectedRows.length} {t("selected workflows? This action cannot be undone.")}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button 
              variant="outlined" 
              size="small" 
              sx={{ ...button_Outlined }} 
              onClick={() => setShowCancelConfirmation(false)}
            >
              {t(DELETE_MODAL_BUTTONS_NAME.CANCEL)}
            </Button>
            <Button 
              variant="contained" 
              size="small" 
              sx={{ ...button_Primary }} 
              onClick={handleConfirmCancel}
            >
              {t("Confirm")}
            </Button>
          </DialogActions>
        </CustomDialog>
      )}
    </>
  );
};

export default SelectionSummaryWF;