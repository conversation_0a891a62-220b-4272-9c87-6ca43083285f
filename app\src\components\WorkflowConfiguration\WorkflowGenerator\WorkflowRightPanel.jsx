import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Space,
  Typography,
  Tag,
  Row,
  Col,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  CheckCircleOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined
} from '@ant-design/icons';
import { colors } from '@constant/colors';
import { useTheme } from '@mui/material';

const { Title, Text } = Typography;

const WorkflowRightPanel = ({
  selectedCard,
  findTaskLevel,
  mode,
  setIsAddModalVisible,
  isCollapsed,
  onToggleCollapse
}) => {
  const theme = useTheme()
  if (isCollapsed) {
    return (
      <div style={{ 
        padding: '24px 8px', 
        display: 'flex', 
        flexDirection: 'column', 
        alignItems: 'center',
        height: '100%'
      }}>
        <Tooltip title="Expand Task Details" placement="left">
          <Button
            type="text"
            icon={<MenuFoldOutlined />}
            onClick={onToggleCollapse}
            style={{
              color: theme.palette.primary.main,
              fontSize: '18px',
              width: '40px',
              height: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '20px'
            }}
          />
        </Tooltip>
        
        {mode !== "view" && (
          <Tooltip title="Add Task" placement="left">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddModalVisible(true)}
              style={{
                backgroundColor: theme.palette.primary.main,
                borderRadius: '6px',
                boxShadow: `0 2px 4px ${theme.palette.primary.main}60`,
                width: '40px',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            />
          </Tooltip>
        )}
        
        {/* Vertical Task Details indicator */}
        {selectedCard && (
          <div style={{
            marginTop: '30px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: '8px'
          }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '8px',
              backgroundColor: '#fafafa',
              border: '1px solid #e8e8e8',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <CheckCircleOutlined style={{ color: theme.palette.primary.main }} />
            </div>
            <div style={{
              fontSize: '20px',
              fontWeight: 'bold',
              marginTop: '100px',
              paddingLeft: '4px',
              color: '#666',
              textAlign: 'center',
              writingMode: 'vertical-rl',
              textOrientation: 'mixed',
              maxHeight: '500px',
              overflow: 'hidden'
            }}>
              {selectedCard.workflowTaskName}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20
      }}>
        <Title level={4} style={{ margin: 0, color: theme.palette.primary.main }}>
          Task Details
        </Title>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
          {mode !== "view" && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsAddModalVisible(true)}
              style={{
                backgroundColor: theme.palette.primary.main,
                borderRadius: '6px',
                boxShadow: `0 2px 4px ${theme.palette.primary.main}60`
              }}
            >
              Add Task
            </Button>
          )}
          <Tooltip title="Collapse Panel">
            <Button
              type="text"
              icon={<MenuUnfoldOutlined />}
              onClick={onToggleCollapse}
              style={{
                color: theme.palette.primary.main,
                fontSize: '16px'
              }}
            />
          </Tooltip>
        </div>
      </div>

      {selectedCard ? (
        <div>
          {/* Task Info Card */}
          <Card
            title={
              <Space>
                <span style={{ fontSize: '14px' }}>{selectedCard.workflowTaskName}</span>
                {selectedCard.isFixed && <Tag size="small" color="gold">FIXED</Tag>}
              </Space>
            }
            size="small"
            style={{
              marginBottom: 16,
              borderRadius: '8px',
              backgroundColor: '#fafafa'
            }}
          >
            <div style={{ fontSize: '12px', lineHeight: '20px' }}>
              <div>
                <Row gutter={[8, 4]} style={{ marginBottom: '8px' }}>
                  <Col span={8}><Text strong>Level:</Text></Col>
                  <Col span={16}>{findTaskLevel(selectedCard.id)}</Col>
                </Row>

                <Row gutter={[8, 4]} style={{ marginBottom: '8px' }}>
                  <Col span={8}><Text strong>Group:</Text></Col>
                  <Col span={16}>{selectedCard.workflowGroup}</Col>
                </Row>

                <Row gutter={[8, 4]} style={{ marginBottom: '8px' }}>
                  <Col span={8}><Text strong>High Priority SLA:</Text></Col>
                  <Col span={16}>{selectedCard.slaHigh} hours</Col>
                </Row>

                <Row gutter={[8, 4]} style={{ marginBottom: '8px' }}>
                  <Col span={8}><Text strong>Medium Priority SLA:</Text></Col>
                  <Col span={16}>{selectedCard.slaMedium} hours</Col>
                </Row>

                <Row gutter={[8, 4]} style={{ marginBottom: '8px' }}>
                  <Col span={8}><Text strong>Low Priority SLA:</Text></Col>
                  <Col span={16}>{selectedCard.slaLow} hours</Col>
                </Row>
              </div>

              {selectedCard.sendBackAllowedTo && selectedCard.sendBackAllowedTo.length > 0 && (
                <div style={{ marginTop: 12 }}>
                  <Text strong>Send Back Allowed To:</Text>
                  <div style={{ marginTop: 4 }}>
                    {selectedCard.sendBackAllowedTo.map((level, index) => (
                      <Tag key={index} size="small" style={{ marginBottom: 4 }}>
                        Level {level}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

              {selectedCard.requestBenchStatus && (
                <div style={{ marginTop: 12 }}>
                  <Text strong>Request Bench Status:</Text>
                  <div style={{
                    marginTop: 4,
                    fontStyle: 'italic',
                    padding: '8px',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                    border: '1px solid #f0f0f0'
                  }}>
                    {selectedCard.requestBenchStatus}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* View Only Message */}
          <Card size="small" style={{ borderRadius: '8px' }}>
            <div style={{
              textAlign: 'center',
              color: '#666',
              padding: '20px 0'
            }}>
              <CheckCircleOutlined style={{ fontSize: '24px', marginBottom: 8 }} />
              <div>Task details are displayed above</div>
              <div style={{ fontSize: '12px', marginTop: 4 }}>
                Use the edit icon on task cards to modify
              </div>
            </div>
          </Card>
        </div>
      ) : (
        <Card size="small" style={{ borderRadius: '8px' }}>
          <div style={{
            textAlign: 'center',
            color: '#999',
            padding: '40px 0'
          }}>
            <CheckCircleOutlined style={{ fontSize: '48px', marginBottom: 16 }} />
            <div>Select a task card to view its details</div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default WorkflowRightPanel;