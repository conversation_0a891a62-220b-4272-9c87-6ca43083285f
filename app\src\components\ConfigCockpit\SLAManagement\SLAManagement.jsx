import React, { useEffect, useState } from "react";
import {
  BottomNavigation,
  Box,
  Button,
  Grid,
  IconButton,
  Paper,
  Typography,
  Chip,
  Tooltip,
} from "@mui/material";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";
import { Stack } from "@mui/system";
import { Modal, Form, Select, TimePicker, Tag, Space, Descriptions } from "antd";
import { PlusOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import {
  iconButton_SpacingSmall,
  outermostContainer,
  outermostContainer_Information,
  outerContainer_Information,
  container_table,
} from "../../common/commonStyles";
import ReusableTable from "../../common/ReusableTable";
import {
  destination_Admin,
  destination_SLA_Mgmt,
} from "../../../destinationVariables";
import { doAjax } from "../../common/fetchService";
import ReusableSnackBar from "../../common/ReusableSnackBar";
import ModeEditOutlineOutlinedIcon from '@mui/icons-material/ModeEditOutlineOutlined';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import moment from "moment";
import { colors } from "@constant/colors";

const SLAManagement = () => {
  const [businessHoursData, setBusinessHoursData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [pageSize, setPageSize] = useState(100);
  const [page, setPage] = useState(0);
  const [selectedBusinessHourId, setSelectedBusinessHourId] = useState("");

  // Dialog states
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedRowData, setSelectedRowData] = useState(null);

  // Form instances
  const [addForm] = Form.useForm();
  const [editForm] = Form.useForm();

  // Form states
  const [regionOptions, setRegionOptions] = useState([]);
  const [timezoneOptions, setTimezoneOptions] = useState([]);
  const [isLoadingTimezones, setIsLoadingTimezones] = useState(false);
  const [offHoursRanges, setOffHoursRanges] = useState([]);
  const [editOffHoursRanges, setEditOffHoursRanges] = useState([]);
  const [tempOffHoursRange, setTempOffHoursRange] = useState({
    startTime: null,
    endTime: null,
  });

  const daysOfWeek = [
    "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"
  ].map(day => ({ value: day, label: day }));

  // Pagination handlers
  const handlePageSizeChange = (event) => {
    const newPageSize = event.target.value;
    setPageSize(newPageSize);
    setPage(0);
  };

  const handlePageChange = (event, newPage) => {
    setPage(isNaN(newPage) ? 0 : newPage);
  };

  const fetchTimezonesByRegion = (region) => {
    setIsLoadingTimezones(true);
    let hSuccess = (res) => {
      setTimezoneOptions(res?.data?.map(tz => ({ value: tz, label: tz })) || []);
      setIsLoadingTimezones(false);
    };
    let hError = () => {
      setTimezoneOptions([]);
      setIsLoadingTimezones(false);
    };
    doAjax(`/${destination_Admin}/api/region-business-hours/${region}/timezones`, "get", hSuccess, hError);
  };

  const fetchBusinessHours = () => {
    setIsLoading(true);
    let hSuccess = (res) => {
      let index = 1;
      const transformedData = res?.data?.flatMap(item =>
        item?.dayBusinessHours?.map(dayHour => {
          let offHours = [];
          if (dayHour?.offHoursRanges) {
            offHours = dayHour?.offHoursRanges.split(",").map(range => {
              const [startTime, endTime] = range.split("-");
              return { startTime, endTime };
            });
          }

          return {
            ...dayHour,
            region: item.region,
            timeZone: item.timeZone,
            offHoursRanges: offHours,
            id: index++
          };
        })
      );

      setBusinessHoursData(transformedData || []);
      setIsLoading(false);
    };

    let hError = () => {
      setIsLoading(false);
    };

    doAjax(`/${destination_Admin}/api/region-business-hours`, "get", hSuccess, hError);
  };

  const fetchBusinessRegions = () => {
    setIsLoading(true);
    let hSuccess = (res) => {
      setRegionOptions(res?.data?.map(tz => ({ value: tz, label: tz })));
      setIsLoading(false);
    };
    let hError = () => {
      setIsLoading(false);
    };
    doAjax(`/${destination_Admin}/api/region-business-hours/regions`, "get", hSuccess, hError);
  };

  const createBusinessHour = () => {
    addForm.validateFields().then(values => {
      const payload = {
        region: values.region,
        timeZone: values.timeZone,
        dayOfWeek: values.dayOfWeek,
        workStartTime: values.workStartTime ? values.workStartTime.format("HH:mm") : null,
        workEndTime: values.workEndTime ? values.workEndTime.format("HH:mm") : null,
        offHoursRanges: offHoursRanges
          ?.map(range => `${range?.startTime?.format("HH:mm")}-${range?.endTime?.format("HH:mm")}`)
          ?.join(","),
      };

      let hSuccess = (res) => {
        setSuccess_Notification({
          currentNotification: `Business Hour created successfully.`,
          success: true,
          open: true,
          title: "Success",
          severity: "success",
        });
        fetchBusinessHours();
        handleCloseAddModal();
      };
      let hError = () => {
        setwarning_Notification({
          currentNotification: `Failed to create Business Hour`,
          success: false,
          open: true,
          title: "Error",
          severity: "error",
        });
      };
      doAjax(`/${destination_Admin}/business-hours/create`, "post", hSuccess, hError, payload);
    });
  };

  const updateBusinessHour = () => {
    editForm.validateFields().then(values => {
      const payload = {
        id: selectedRowData.id,
        region: values.region,
        timeZone: values.timeZone,
        dayOfWeek: values.dayOfWeek,
        workStartTime: values.workStartTime ? values.workStartTime.format("HH:mm") : null,
        workEndTime: values.workEndTime ? values.workEndTime.format("HH:mm") : null,
        offHoursRanges: editOffHoursRanges
          ?.map(range => `${range?.startTime?.format("HH:mm")}-${range?.endTime?.format("HH:mm")}`)
          ?.join(","),
      };

      let hSuccess = (res) => {
        setSuccess_Notification({
          currentNotification: `Business Hour updated successfully.`,
          success: true,
          open: true,
          title: "Success",
          severity: "success",
        });
        fetchBusinessHours();
        handleCloseEditModal();
      };
      let hError = () => {
        setwarning_Notification({
          currentNotification: `Failed to update Business Hour`,
          success: false,
          open: true,
          title: "Error",
          severity: "error",
        });
      };
      doAjax(`/${destination_SLA_Mgmt}/business-hours/update/${selectedRowData.id}`, "put", hSuccess, hError, payload);
    });
  };

  const handleOpenAddModal = () => {
    addForm.resetFields();
    setOffHoursRanges([]);
    setTempOffHoursRange({ startTime: null, endTime: null });
    setAddModalOpen(true);
  };

  const handleCloseAddModal = () => {
    setAddModalOpen(false);
    setOffHoursRanges([]);
    setTempOffHoursRange({ startTime: null, endTime: null });
  };

  const handleOpenEditModal = (rowData) => {
    setSelectedRowData(rowData);
    editForm.setFieldsValue({
      region: rowData.region,
      timeZone: rowData.timeZone,
      dayOfWeek: rowData.dayOfWeek,
      workStartTime: rowData.workStartTime ? dayjs(rowData.workStartTime, "HH:mm") : null,
      workEndTime: rowData.workEndTime ? dayjs(rowData.workEndTime, "HH:mm") : null,
    });
    // Fetch timezones for the selected region
    fetchTimezonesByRegion(rowData.region);
    setEditOffHoursRanges(
      rowData.offHoursRanges?.map(range => ({
        startTime: dayjs(range.startTime, "HH:mm"),
        endTime: dayjs(range.endTime, "HH:mm"),
      })) || []
    );
    setTempOffHoursRange({ startTime: null, endTime: null });
    setEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setEditModalOpen(false);
    setEditOffHoursRanges([]);
    setTempOffHoursRange({ startTime: null, endTime: null });
    setSelectedRowData(null);
  };

  const handleOpenViewModal = (rowData) => {
    setSelectedRowData(rowData);
    setViewModalOpen(true);
  };

  const handleCloseViewModal = () => {
    setViewModalOpen(false);
    setSelectedRowData(null);
  };

  // Off time range handlers
  const addOffHoursRange = (isEdit = false) => {
    if (tempOffHoursRange.startTime && tempOffHoursRange.endTime) {
      const newRange = { ...tempOffHoursRange };
      if (isEdit) {
        setEditOffHoursRanges(prev => [...prev, newRange]);
      } else {
        setOffHoursRanges(prev => [...prev, newRange]);
      }
      setTempOffHoursRange({ startTime: null, endTime: null });
    }
  };

  const removeOffHoursRange = (index, isEdit = false) => {
    if (isEdit) {
      setEditOffHoursRanges(prev => prev.filter((_, i) => i !== index));
    } else {
      setOffHoursRanges(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Table columns
  const columns = [
    {
      field: "id",
      headerName: "ID",
      editable: false,
      hide: true,
    },
    {
      field: "region",
      headerName: "Region",
      editable: false,
      flex: 1,
    },
    {
      field: "timeZone",
      headerName: "Timezone",
      editable: false,
      flex: 1,
    },
    {
      field: "dayOfWeek",
      headerName: "Day of Week",
      editable: false,
      flex: 1,
    },
    {
      field: "workStartTime",
      headerName: "Business Start Time",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography variant="body2">
          {moment(params.row.workStartTime, "HH:mm:ss").format("HH:mm")}
        </Typography>
      ),
    },
    {
      field: "workEndTime",
      headerName: "Business End Time",
      editable: false,
      flex: 1,
      renderCell: (params) => (
        <Typography variant="body2">
          {moment(params.row.workEndTime, "HH:mm:ss").format("HH:mm")}
        </Typography>
      ),
    },
    {
      field: "offHoursRanges",
      headerName: "Off Time Ranges",
      editable: false,
      flex: 1,
      renderCell: (params) => {
        const ranges = params?.row?.offHoursRanges || [];

        return (
          <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
            {ranges.length > 0 && (
              <Chip
                label={`${ranges[0].startTime} - ${ranges[0].endTime}`}
                size="small"
                variant="outlined"
              />
            )}
            {ranges.length > 1 && (
              <Tooltip
                title={
                  <Box>
                    <Typography sx={{ fontWeight: "bold", mb: 0.5 }}>
                      All Off Time Ranges
                    </Typography>
                    <Box
                      sx={{
                        maxHeight: ranges.length > 10 ? 200 : "auto",
                        overflowY: ranges.length > 10 ? "auto" : "visible",
                        pr: 1,
                      }}
                    >
                      {ranges.map((range, index) => (
                        <div key={index} style={{fontSize: "15px"}}>
                          {range.startTime} - {range.endTime}
                        </div>
                      ))}
                    </Box>
                  </Box>
                }
                arrow
              >
                <IconButton size="small">
                  <InfoOutlinedIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        );
      },
    },
    {
      field: "actionItem",
      headerName: "Actions",
      headerAlign: "center",
      align: "center",
      editable: false,
      flex: 1,
      clickable: false,
      renderCell: (params) => (
        <Box sx={{ display: "flex", gap: 1 }}>
          <Tooltip title="View Details">
            <IconButton
              sx={iconButton_SpacingSmall}
              onClick={() => handleOpenViewModal(params.row)}
            >
              <VisibilityOutlinedIcon color="primary" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Edit">
            <IconButton
              sx={iconButton_SpacingSmall}
              onClick={() => handleOpenEditModal(params.row)}
            >
              <ModeEditOutlineOutlinedIcon sx={{ color: `${colors.warning.amber}`, padding: "1px"}} />
            </IconButton>
          </Tooltip>
        </Box>
      ),
    },
  ];

  // Notification states
  const [warning_Notification, setwarning_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });

  const [Success_Notification, setSuccess_Notification] = useState({
    currentNotification: "",
    success: true,
    open: false,
    title: "",
    severity: "",
  });

  const functions_ReusableDialogBox = {
    MessageDialogClose: () => {
      setwarning_Notification({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      });
      setSuccess_Notification({
        open: false,
        currentNotification: "",
        success: "",
        title: "",
        severity: "",
      });
    },
  };

  const renderAddModal = () => (
    <Modal
      title="Add Business Hour"
      open={addModalOpen}
      onOk={createBusinessHour}
      onCancel={handleCloseAddModal}
      width={600}
      okText="Create"
      cancelText="Cancel"
    >
      <Form form={addForm} layout="vertical">
        <Form.Item
          label="Region"
          name="region"
          rules={[{ required: true, message: "Please select a region!" }]}
        >
          <Select 
            options={regionOptions} 
            placeholder="Select region"
            showSearch
            onChange={(value) => {
              addForm.setFieldsValue({ timeZone: undefined }); // Clear timezone field
              fetchTimezonesByRegion(value);
            }}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          label="Timezone"
          name="timeZone"
          rules={[{ required: true, message: "Please select a timezone!" }]}
        >
          <Select 
            options={timezoneOptions} 
            placeholder="Select timezone"
            loading={isLoadingTimezones}
            disabled={!addForm.getFieldValue('region')}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          label="Day of Week"
          name="dayOfWeek"
          rules={[{ required: true, message: "Please select a day!" }]}
        >
          <Select 
            options={daysOfWeek} 
            placeholder="Select day"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <div style={{ display: "flex", gap: "16px" }}>
          <Form.Item
            label="Start Time"
            name="workStartTime" // Changed from startTime to workStartTime
            style={{ flex: 1 }}
            rules={[{ required: true, message: "Please select start time!" }]}
          >
            <TimePicker format="HH:mm" placeholder="Start time" style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item
            label="End Time"
            name="workEndTime" // Changed from endTime to workEndTime
            style={{ flex: 1 }}
            rules={[{ required: true, message: "Please select end time!" }]}
          >
            <TimePicker format="HH:mm" placeholder="End time" style={{ width: "100%" }} />
          </Form.Item>
        </div>

        <Form.Item label="Off Hours Ranges">
          <Space direction="vertical" style={{ width: "100%" }}>
            <div style={{ display: "flex", gap: "8px" }}>
              <TimePicker
                format="HH:mm"
                placeholder="Off start time"
                value={tempOffHoursRange.startTime}
                onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, startTime: time }))}
                style={{ flex: 1 }}
              />
              <TimePicker
                format="HH:mm"
                placeholder="Off end time"
                value={tempOffHoursRange.endTime}
                onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, endTime: time }))}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => addOffHoursRange(false)}
                disabled={!tempOffHoursRange.startTime || !tempOffHoursRange.endTime}
              >
                Add
              </Button>
            </div>

            <div>
              {offHoursRanges.map((range, index) => (
                <Tag
                  key={index}
                  closable
                  onClose={() => removeOffHoursRange(index, false)}
                  style={{ marginBottom: 4 }}
                >
                  {range.startTime?.format("HH:mm")} - {range.endTime?.format("HH:mm")}
                </Tag>
              ))}
            </div>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );

  const renderEditModal = () => (
    <Modal
      title="Edit Business Hour"
      open={editModalOpen}
      onOk={updateBusinessHour}
      onCancel={handleCloseEditModal}
      width={600}
      okText="Update"
      cancelText="Cancel"
    >
      <Form form={editForm} layout="vertical">
        <Form.Item
          label="Region"
          name="region"
          rules={[{ required: true, message: "Please select a region!" }]}
        >
          <Select 
            options={regionOptions} 
            placeholder="Select region"
            showSearch
            onChange={(value) => {
              editForm.setFieldsValue({ timeZone: undefined }); // Clear timezone field
              fetchTimezonesByRegion(value);
            }}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          label="Timezone"
          name="timeZone"
          rules={[{ required: true, message: "Please select a timezone!" }]}
        >
          <Select 
            options={timezoneOptions} 
            placeholder="Select timezone"
            loading={isLoadingTimezones}
            disabled={!editForm.getFieldValue('region')}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          label="Day of Week"
          name="dayOfWeek"
          rules={[{ required: true, message: "Please select a day!" }]}
        >
          <Select 
            options={daysOfWeek} 
            placeholder="Select day"
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <div style={{ display: "flex", gap: "16px" }}>
          <Form.Item
            label="Start Time"
            name="workStartTime" // Changed from startTime to workStartTime
            style={{ flex: 1 }}
            rules={[{ required: true, message: "Please select start time!" }]}
          >
            <TimePicker format="HH:mm" placeholder="Start time" style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item
            label="End Time"
            name="workEndTime" // Changed from endTime to workEndTime
            style={{ flex: 1 }}
            rules={[{ required: true, message: "Please select end time!" }]}
          >
            <TimePicker format="HH:mm" placeholder="End time" style={{ width: "100%" }} />
          </Form.Item>
        </div>

        <Form.Item label="Off Hours Ranges">
          <Space direction="vertical" style={{ width: "100%" }}>
            <div style={{ display: "flex", gap: "8px" }}>
              <TimePicker
                format="HH:mm"
                placeholder="Off start time"
                value={tempOffHoursRange.startTime}
                onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, startTime: time }))}
                style={{ flex: 1 }}
              />
              <TimePicker
                format="HH:mm"
                placeholder="Off end time"
                value={tempOffHoursRange.endTime}
                onChange={(time) => setTempOffHoursRange(prev => ({ ...prev, endTime: time }))}
                style={{ flex: 1 }}
              />
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => addOffHoursRange(true)}
                disabled={!tempOffHoursRange.startTime || !tempOffHoursRange.endTime}
              >
                Add
              </Button>
            </div>

            <div>
              {editOffHoursRanges.map((range, index) => (
                <Tag
                  key={index}
                  closable
                  onClose={() => removeOffHoursRange(index, true)}
                  style={{ marginBottom: 4 }}
                >
                  {range.startTime?.format("HH:mm")} - {range.endTime?.format("HH:mm")}
                </Tag>
              ))}
            </div>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );

  const renderViewModal = () => (
    <Modal
      title="Business Hour Details"
      open={viewModalOpen}
      onCancel={handleCloseViewModal}
      footer={[
        <Button key="close" onClick={handleCloseViewModal}>
          Close
        </Button>
      ]}
      width={600}
    >
      {selectedRowData && (
        <Descriptions column={1} bordered>
          <Descriptions.Item label="Region">{selectedRowData.region}</Descriptions.Item>
          <Descriptions.Item label="Timezone">{selectedRowData.timeZone}</Descriptions.Item>
          <Descriptions.Item label="Day of Week">{selectedRowData.dayOfWeek}</Descriptions.Item>
          <Descriptions.Item label="Business Hours">
            {selectedRowData.workStartTime} - {selectedRowData.workEndTime} {/* Changed from startTime/endTime */}
          </Descriptions.Item>
          <Descriptions.Item label="Off Hours Ranges">
            <Space wrap>
              {selectedRowData.offHoursRanges?.map((range, index) => (
                <Tag key={index} color="orange">
                  {range.startTime} - {range.endTime}
                </Tag>
              )) || "No off hours ranges"}
            </Space>
          </Descriptions.Item>
        </Descriptions>
      )}
    </Modal>
  );

  useEffect(() => {
    fetchBusinessHours();
    fetchBusinessRegions();
  }, []);

  return (
    <div id="printScreen" style={outermostContainer}>
      {/* Reusable Snackbar */}
      <ReusableSnackBar
        openSnackBar={Success_Notification.open}
        alertMsg={Success_Notification.currentNotification}
        handleSnackBarClose={functions_ReusableDialogBox.MessageDialogClose}
      />

      <ReusableSnackBar
        openSnackBar={warning_Notification.open}
        alertMsg={warning_Notification.currentNotification}
        handleSnackBarClose={functions_ReusableDialogBox.MessageDialogClose}
      />

      {/* Bottom Navigation */}
      <Paper
        sx={{ position: "fixed", bottom: 0, left: 0, right: 0, zIndex: 1 }}
        elevation={2}
      >
        <BottomNavigation
          showLabels
          className="container_BottomNav"
          sx={{
            display: "flex",
            justifyContent: "flex-end",
          }}
        >
          <Button
            variant="contained"
            sx={{ marginLeft: "auto" }}
            onClick={handleOpenAddModal}
          >
            Add Business Hour
          </Button>
        </BottomNavigation>
      </Paper>

      {/* Modals */}
      {renderAddModal()}
      {renderEditModal()}
      {renderViewModal()}

      {/* Header */}
      <Stack spacing={1}>
        <Grid container sx={outermostContainer_Information}>
          <Grid item md={5} sx={outerContainer_Information}>
            <Typography variant="h3">
              <strong>SLA Management</strong>
            </Typography>
            <Typography variant="body2">
              This view displays the existing business hours configurations and allows you to manage them.
            </Typography>
          </Grid>
        </Grid>

        {/* Table */}
        <Stack>
          <Grid>
            <Grid container columns={12} sx={container_table}>
              <Grid item md={12} sx={{ position: "relative" }}>
                <ReusableTable
                  tempheight="calc(100vh - 260px)"
                  width="100%"
                  title={"Business Schedule"}
                  stopPropagation_Column={["actionItem"]}
                  pageSize={pageSize}
                  page={page}
                  onPageSizeChange={handlePageSizeChange}
                  rowCount={businessHoursData.length ?? 0}
                  rows={businessHoursData}
                  columns={columns}
                  getRowIdValue="id"
                  hideFooter={true}
                  checkboxSelection={false}
                  onPageChange={handlePageChange}
                  disableSelectionOnClick={true}
                  isLoading={isLoading}
                  showCustomNavigation={true}
                />
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </Stack>
    </div>
  );
};

export default SLAManagement;