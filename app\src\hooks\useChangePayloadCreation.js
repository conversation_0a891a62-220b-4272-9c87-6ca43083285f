import { useSelector } from "react-redux";
import { convertToDateFormat } from "@helper/helper";
import { TEMPLATE_KEYS } from "@constant/changeTemplates";
import { DEFAULT_VALUES } from "@constant/enum";

const useChangePayloadCreation = (templateName) => {
  const changeFieldRows = useSelector((state) => state.payload.changeFieldRows);
  const changeLogData = useSelector((state) => state.payload.changeLogData);
  const requestState = useSelector((state) => state.request);
  const payloadData = useSelector((state) => state.payload);
  const dynamicData = useSelector((state) => state.payload.dynamicKeyValues);
  const selectedRows = useSelector((state) => state.payload.selectedRows);
  const taskData = useSelector((state) => state.userManagement.taskData);
  const template = templateName ? templateName : dynamicData?.templateName;

  const createChangeLogData = (material,changeLogId) => {
    if (!changeLogData[material]) {
      return {
        RequestId:
          payloadData?.payloadData?.RequestId ||
          payloadData?.changeLogData?.RequestId,
        ChildRequestId:
          dynamicData?.childRequestHeaderData?.ChildRequestId ?? null,
        ChangeLogId: changeLogId ?? null,
      };
    }
    return {
      RequestId:
        payloadData?.payloadData?.RequestId ||
        payloadData?.changeLogData?.RequestId,
      ChildRequestId:
        dynamicData?.childRequestHeaderData?.ChildRequestId ?? null,
      ChangeLogId: changeLogId ?? null,
      ...changeLogData[material],
    };
  };

  const changePayloadForTemplate = (display) => {
    if (
      templateName === TEMPLATE_KEYS?.LOGISTIC ||
      dynamicData?.templateName === TEMPLATE_KEYS?.LOGISTIC
    ) {
      const result = getLogisticDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.ITEM_CAT ||
      dynamicData?.templateName === TEMPLATE_KEYS?.ITEM_CAT
    ) {
      const result = getItemCatDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.MRP ||
      dynamicData?.templateName === TEMPLATE_KEYS?.MRP
    ) {
      const result = getMRPDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.UPD_DESC ||
      dynamicData?.templateName === TEMPLATE_KEYS?.UPD_DESC
    ) {
      const result = getUpdDescDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.WARE_VIEW_2 ||
      dynamicData?.templateName === TEMPLATE_KEYS?.WARE_VIEW_2
    ) {
      const result = getWarehouseDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.CHG_STAT ||
      dynamicData?.templateName === TEMPLATE_KEYS?.CHG_STAT
    ) {
      const result = getChangeStatusDataPayload(display);
      return result;
    } else if (
      templateName === TEMPLATE_KEYS?.SET_DNU ||
      dynamicData?.templateName === TEMPLATE_KEYS?.SET_DNU
    ) {
      const result = getSetToDNUDataPayload(display);
      return result;
    }
  };

  const getLogisticDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
      if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
        return acc; // Skip processing for this object
      }
      const material = current?.Material;

      if (!acc[material]) {
        acc[material] = [];
      }

      // Push the current object into the respective Material group
      acc[material].push(current);

      return acc;
    }, {});

    if (display) {
      const keysToRemove = [
        "id",
        "MaterialId",
        "ClientId",
        "slNo",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];
      const result = Object.keys(groupedData).map((material) => {
        const materialGroup = groupedData[material];
        const { MaterialId, ClientId,ChangeLogId, Version } =
          materialGroup[materialGroup?.length - 1];
        return {
          MaterialId: MaterialId,
          Version:Version,
          ChangeLogId:ChangeLogId,
          Material: material,
          MatlType:
            groupedData[material]?.[groupedData[material]?.length - 1]
              ?.MatlType || "",
          Function: "UPD",
          TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
          TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
          creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
          dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
          IsFirstCreate: false,
          MassEditId: dynamicData?.otherPayloadData["MassEditId"],
          MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
          TotalIntermediateTasks:
            dynamicData?.otherPayloadData["TotalIntermediateTasks"],
          IntermediateTaskCount:
            dynamicData?.otherPayloadData["IntermediateTaskCount"],
          Toclientdata: {
            ClientId: ClientId,
            Material: material,
            Function: "UPD",
          },
          Touomdata: materialGroup.map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
          }),
          Tochildrequestheaderdata: {
            ChildRequestId:
              dynamicData?.childRequestHeaderData?.ChildRequestId || null,
            MaterialGroupType:
              dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
            TaskId: taskData?.taskId || null,
            Comments: dynamicData?.Comments || "",
            TotalIntermediateTasks:
              dynamicData?.childRequestHeaderData?.TotalIntermediateTasks ||
              null,
            IntermediateTaskCount:
              dynamicData?.childRequestHeaderData?.IntermediateTaskCount ||
              null,
            ReqCreatedBy:
              dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
            ReqCreatedOn:
              dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
            ReqUpdatedOn:
              dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
            RequestType:
              dynamicData?.childRequestHeaderData?.RequestType || null,
            RequestPrefix:
              dynamicData?.childRequestHeaderData?.RequestPrefix || null,
            RequestDesc:
              dynamicData?.childRequestHeaderData?.RequestDesc || null,
            RequestPriority:
              dynamicData?.childRequestHeaderData?.RequestPriority || null,
            RequestStatus:
              dynamicData?.childRequestHeaderData?.RequestStatus || null,
            CurrentLevel: taskData?.ATTRIBUTE_3 || "",
            CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
            ParticularLevel: dynamicData?.Level || "-1",
            TaskName: taskData?.taskDesc || "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
            DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
            Version: dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION,
          },
          Torequestheaderdata: dynamicData?.requestHeaderData || {},
          Tomaterialerrordata: dynamicData?.errorData[material] || {},
          TemplateName: template,
          changeLogData: createChangeLogData(material,ChangeLogId),
          ...(dynamicData?.Comments && {
            Comments: dynamicData?.Comments || "",
          }),
        };
      });

      return result;
    } else {
      const keysToRemove = ["id", "slNo", "MatlType"];

      const result = Object.keys(groupedData).map((material) => ({
        Touomdata: groupedData[material].map((item) => {
          const newItem = { ...item, Function: "UPD" };
          keysToRemove.forEach((key) => delete newItem[key]);
          return newItem;
        }),
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Toclientdata: {
          ClientId: null,
          Function: "UPD",
        },
        Material: material,
        MatlType:
          groupedData[material]?.[groupedData[material]?.length - 1]
            ?.MatlType || "",
        TemplateName: template,
        IsFirstCreate: true,
        Function: "UPD",
        changeLogData: createChangeLogData(material),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));
      return result;
    }
  };
  const getItemCatDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
      if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
        return acc; // Skip processing for this object
      }
      const material = current?.Material;

      if (!acc[material]) {
        acc[material] = [];
      }

      // Push the current object into the respective Material group
      acc[material].push(current);

      return acc;
    }, {});

    if (display) {
      const keysToRemove = [
        "id",
        "MaterialId",
        "ClientId",
        "slNo",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];

      const result = Object.keys(groupedData).map((material) => {
        const materialGroup = groupedData[material];
        const { MaterialId, ClientId ,Version} = materialGroup[0];
        return {
          MaterialId: MaterialId,
          Version:Version,
          Material: material,
          MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
          MatlType:
            groupedData[material]?.[groupedData[material]?.length - 1]
              ?.MatlType || "",
          Function: "UPD",
          TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
          TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
          creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
          dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
          IsFirstCreate: false,
          MassEditId: dynamicData?.otherPayloadData["MassEditId"],
          TotalIntermediateTasks:
            dynamicData?.otherPayloadData["TotalIntermediateTasks"],
          IntermediateTaskCount:
            dynamicData?.otherPayloadData["IntermediateTaskCount"],
          Tosalesdata: materialGroup.map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
          }),
          Tochildrequestheaderdata: {
            ChildRequestId:
              dynamicData?.childRequestHeaderData?.ChildRequestId || null,
            MaterialGroupType:
              dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
            TaskId: taskData?.taskId || null,
            Comments: dynamicData?.Comments || "",
            TotalIntermediateTasks:
              dynamicData?.childRequestHeaderData?.TotalIntermediateTasks ||
              null,
            IntermediateTaskCount:
              dynamicData?.childRequestHeaderData?.IntermediateTaskCount ||
              null,
            ReqCreatedBy:
              dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
            ReqCreatedOn:
              dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
            ReqUpdatedOn:
              dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
            RequestType:
              dynamicData?.childRequestHeaderData?.RequestType || null,
            RequestPrefix:
              dynamicData?.childRequestHeaderData?.RequestPrefix || null,
            RequestDesc:
              dynamicData?.childRequestHeaderData?.RequestDesc || null,
            RequestPriority:
              dynamicData?.childRequestHeaderData?.RequestPriority || null,
            RequestStatus:
              dynamicData?.childRequestHeaderData?.RequestStatus || null,
            CurrentLevel: taskData?.ATTRIBUTE_3 || "",
            CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
            ParticularLevel: dynamicData?.Level || "-1",
            TaskName: taskData?.taskDesc || "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
            DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
            Version: dynamicData?.childRequestHeaderData?.RequestStatus || DEFAULT_VALUES.DEFAULT_VERSION,
          },
          Torequestheaderdata: dynamicData?.requestHeaderData || {},
          Tomaterialerrordata: dynamicData?.errorData[material] || {},
          TemplateName: template,
          changeLogData: createChangeLogData(material,groupedData[material]?.[0]?.ChangeLogId),
          ...(dynamicData?.Comments && {
            Comments: dynamicData?.Comments || "",
          }),
        };
      });

      return result;
    } else {
      const keysToRemove = ["id", "slNo", "MatlType"];

      const result = Object.keys(groupedData).map((material) => ({
        Tosalesdata: groupedData[material].map((item) => {
          const newItem = { ...item, Function: "UPD" };
          keysToRemove.forEach((key) => delete newItem[key]);
          return newItem;
        }),
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Material: material,
        MatlType:
          groupedData[material]?.[groupedData[material]?.length - 1]
            ?.MatlType || "",
        TemplateName: template,
        IsFirstCreate: true,
        Function: "UPD",
        changeLogData: createChangeLogData(material),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));

      return result;
    }
  };
  const getMRPDataPayload = (display) => {
    if (display) {
      const groupedData = {};
      const clientKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "Plant",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];
      const plantKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];
      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material, MaterialId, ChangeLogId,Version } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              MaterialId: MaterialId,
              Version:Version,
              ChangeLogId:ChangeLogId,
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            clientKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = newItem;
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push(newItem);
          }
        });
      });

      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Material: material,
        Function: "UPD",
        MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
        TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
        TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
        creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
        dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
        IsFirstCreate: false,
        MassEditId: dynamicData?.otherPayloadData["MassEditId"],
        TotalIntermediateTasks:
          dynamicData?.otherPayloadData["TotalIntermediateTasks"],
        IntermediateTaskCount:
          dynamicData?.otherPayloadData["IntermediateTaskCount"],
        Torequestheaderdata: dynamicData?.requestHeaderData || {},
        Tomaterialerrordata: dynamicData?.errorData[material] || {},
        TemplateName: template,
        Tochildrequestheaderdata: {
          ChildRequestId:
            dynamicData?.childRequestHeaderData?.ChildRequestId || null,
          MaterialGroupType:
            dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
          TaskId: taskData?.taskId || null,
          Comments: dynamicData?.Comments || "",
          TotalIntermediateTasks:
            dynamicData?.childRequestHeaderData?.TotalIntermediateTasks || null,
          IntermediateTaskCount:
            dynamicData?.childRequestHeaderData?.IntermediateTaskCount || null,
          ReqCreatedBy:
            dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
          ReqCreatedOn:
            dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
          ReqUpdatedOn:
            dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
          RequestType: dynamicData?.childRequestHeaderData?.RequestType || null,
          RequestPrefix:
            dynamicData?.childRequestHeaderData?.RequestPrefix || null,
          RequestDesc: dynamicData?.childRequestHeaderData?.RequestDesc || null,
          RequestPriority:
            dynamicData?.childRequestHeaderData?.RequestPriority || null,
          RequestStatus:
            dynamicData?.childRequestHeaderData?.RequestStatus || null,
          CurrentLevel: taskData?.ATTRIBUTE_3 || "",
          CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
          ParticularLevel: dynamicData?.Level || "-1",
          TaskName: taskData?.taskDesc || "",
          ApproverGroup: taskData?.ATTRIBUTE_5 || "",
          TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
          DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
          Version: dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION
        },
        changeLogData: createChangeLogData(material,groupedData[material]?.ChangeLogId),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));
      return result;
    } else {
      const groupedData = {};
      const clientKeysToRemove = ["id", "slNo", "type", "Plant", "MatlType"];
      const plantKeysToRemove = ["id", "slNo", "type"];

      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            clientKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = {
              ...newItem,
              Function: "UPD",
            };
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push({
              ...newItem,
              Function: "UPD",
            });
          }
        });
      });

      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Material: material,
        TemplateName: requestState?.requestHeader?.templateName,
        IsFirstCreate: true,
        Function: "UPD",
        MassEditId: requestState?.requestHeader?.requestId,
        changeLogData: createChangeLogData(material),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));

      return result;
    }
  };
  const getChangeStatusDataPayload = (display) => {
    if (display) {
      const groupedData = {};
      const basicKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "Plant",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];
      const plantKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];
      const salesKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];

      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material, MaterialId,ChangeLogId,Version } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              Tosalesdata: [],
              MaterialId: MaterialId,
              Version:Version,
              ChangeLogId:ChangeLogId,
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            basicKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = newItem;
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push(newItem);
          } else if (type === "Sales Data") {
            salesKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tosalesdata.push(newItem);
          }
        });
      });
      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Material: material,
        MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
        Function: "UPD",
        TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
        TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
        creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
        dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
        IsFirstCreate: false,
        MassEditId: dynamicData?.otherPayloadData["MassEditId"],
        TotalIntermediateTasks:
          dynamicData?.otherPayloadData["TotalIntermediateTasks"],
        IntermediateTaskCount:
          dynamicData?.otherPayloadData["IntermediateTaskCount"],
        Torequestheaderdata: dynamicData?.requestHeaderData || {},
        Tomaterialerrordata: dynamicData?.errorData[material] || {},
        TemplateName: template,
        Tochildrequestheaderdata: {
          ChildRequestId:
            dynamicData?.childRequestHeaderData?.ChildRequestId || null,
          MaterialGroupType:
            dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
          TaskId: taskData?.taskId || null,
          Comments: dynamicData?.Comments || "",
          TotalIntermediateTasks:
            dynamicData?.childRequestHeaderData?.TotalIntermediateTasks || null,
          IntermediateTaskCount:
            dynamicData?.childRequestHeaderData?.IntermediateTaskCount || null,
          ReqCreatedBy:
            dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
          ReqCreatedOn:
            dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
          ReqUpdatedOn:
            dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
          RequestType: dynamicData?.childRequestHeaderData?.RequestType || null,
          RequestPrefix:
            dynamicData?.childRequestHeaderData?.RequestPrefix || null,
          RequestDesc: dynamicData?.childRequestHeaderData?.RequestDesc || null,
          RequestPriority:
            dynamicData?.childRequestHeaderData?.RequestPriority || null,
          RequestStatus:
            dynamicData?.childRequestHeaderData?.RequestStatus || null,
          CurrentLevel: taskData?.ATTRIBUTE_3 || "",
          CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
          ParticularLevel: dynamicData?.Level || "-1",
          TaskName: taskData?.taskDesc || "",
          ApproverGroup: taskData?.ATTRIBUTE_5 || "",
          TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
          DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
          Version : dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION
        },
        changeLogData: createChangeLogData(material,groupedData[material]?.ChangeLogId),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));
      return result;
    } else {
      const groupedData = {};
      const basicKeysToRemove = ["id", "slNo", "type", "Plant", "MatlType"];
      const plantKeysToRemove = ["id", "slNo", "type"];
      const salesKeysToRemove = ["id", "slNo", "type"];

      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              Tosalesdata: [],
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            basicKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = {
              ...newItem,
              Function: "UPD",
            };
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push({
              ...newItem,
              Function: "UPD",
            });
          } else if (type === "Sales Data") {
            salesKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tosalesdata.push({
              ...newItem,
              Function: "UPD",
            });
          }
        });
      });

      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Material: material,
        TemplateName: requestState?.requestHeader?.templateName,
        IsFirstCreate: true,
        Function: "UPD",
        MassEditId: requestState?.requestHeader?.requestId,
        changeLogData: createChangeLogData(material),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));

      return result;
    }
  };
  const getSetToDNUDataPayload = (display) => {
    if (display) {
      const groupedData = {};
      const basicKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "Plant",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];
      const plantKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];
      const salesKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];
      const descKeysToRemove = [
        "id",
        "slNo",
        "type",
        "MaterialId",
        "ChangeLogId",
        "Version",
      ];

      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material, MaterialId,ChangeLogId,Version } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              Tosalesdata: [],
              Tomaterialdescription: [],
              MaterialId: MaterialId,
              Version:Version,
              ChangeLogId:ChangeLogId,
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            basicKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = newItem;
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push(newItem);
          } else if (type === "Sales Data") {
            salesKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tosalesdata.push(newItem);
          } else if (type === "Description") {
            descKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tomaterialdescription.push(newItem);
          }
        });
      });

      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Material: material,
        MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
        Function: "UPD",
        TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
        TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
        creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
        dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
        IsFirstCreate: false,
        MassEditId: dynamicData?.otherPayloadData["MassEditId"],
        TotalIntermediateTasks:
          dynamicData?.otherPayloadData["TotalIntermediateTasks"],
        IntermediateTaskCount:
          dynamicData?.otherPayloadData["IntermediateTaskCount"],
        Torequestheaderdata: dynamicData?.requestHeaderData || {},
        Tomaterialerrordata: dynamicData?.errorData[material] || {},
        TemplateName: template,
        Tochildrequestheaderdata: {
          ChildRequestId:
            dynamicData?.childRequestHeaderData?.ChildRequestId || null,
          MaterialGroupType:
            dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
          TaskId: taskData?.taskId || null,
          Comments: dynamicData?.Comments || "",
          TotalIntermediateTasks:
            dynamicData?.childRequestHeaderData?.TotalIntermediateTasks || null,
          IntermediateTaskCount:
            dynamicData?.childRequestHeaderData?.IntermediateTaskCount || null,
          ReqCreatedBy:
            dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
          ReqCreatedOn:
            dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
          ReqUpdatedOn:
            dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
          RequestType: dynamicData?.childRequestHeaderData?.RequestType || null,
          RequestPrefix:
            dynamicData?.childRequestHeaderData?.RequestPrefix || null,
          RequestDesc: dynamicData?.childRequestHeaderData?.RequestDesc || null,
          RequestPriority:
            dynamicData?.childRequestHeaderData?.RequestPriority || null,
          RequestStatus:
            dynamicData?.childRequestHeaderData?.RequestStatus || null,
          CurrentLevel: taskData?.ATTRIBUTE_3 || "",
          CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
          ParticularLevel: dynamicData?.Level || "-1",
          TaskName: taskData?.taskDesc || "",
          ApproverGroup: taskData?.ATTRIBUTE_5 || "",
          TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
          DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
          Version: dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION,
        },
        changeLogData: createChangeLogData(material,groupedData[material]?.ChangeLogId),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));

      return result;
    } else {
      const groupedData = {};
      const basicKeysToRemove = ["id", "slNo", "type", "Plant", "MatlType"];
      const plantKeysToRemove = ["id", "slNo", "type"];
      const salesKeysToRemove = ["id", "slNo", "type"];
      const descKeysToRemove = ["id", "slNo", "type"];

      Object.keys(changeFieldRows).forEach((type) => {
        changeFieldRows[type].forEach((item) => {
          const { Material } = item;
          if (!groupedData[Material]) {
            groupedData[Material] = {
              Toclientdata: null,
              Toplantdata: [],
              Tosalesdata: [],
              Tomaterialdescription: [],
              MatlType: "",
            };
          }

          const newItem = { ...item };

          if (type === "Basic Data" && !groupedData[Material].Toclientdata) {
            groupedData[Material].MatlType = newItem?.MatlType || "";
            basicKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toclientdata = {
              ...newItem,
              Function: "UPD",
            };
          } else if (type === "Plant Data") {
            plantKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Toplantdata.push({
              ...newItem,
              Function: "UPD",
            });
          } else if (type === "Sales Data") {
            salesKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tosalesdata.push({
              ...newItem,
              Function: "UPD",
            });
          } else if (type === "Description") {
            descKeysToRemove.forEach((key) => delete newItem[key]);
            groupedData[Material].Tomaterialdescription.push({
              ...newItem,
              Function: "UPD",
            });
          }
        });
      });

      // Construct final payload
      const result = Object.keys(groupedData).map((material) => ({
        ...groupedData[material],
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Material: material,
        TemplateName: requestState?.requestHeader?.templateName,
        IsFirstCreate: true,
        Function: "UPD",
        MassEditId: requestState?.requestHeader?.requestId,
        changeLogData: createChangeLogData(material),
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
      }));

      return result;
    }
  };
  const getUpdDescDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
      if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
        return acc;
      }
      const material = current?.Material;

      if (!acc[material]) {
        acc[material] = [];
      }

      acc[material].push(current);
      return acc;
    }, {});

    if (display) {
      const keysToRemove = [
        "id",
        "MaterialId",
        "ClientId",
        "slNo",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];

      const result = Object.keys(groupedData).map((material) => {
        const materialGroup = groupedData[material];
        const { MaterialId, ClientId,Version } =
          materialGroup[materialGroup?.length - 1];

        return {
          MaterialId: MaterialId,
          Material: material,
          MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
          MatlType:
            groupedData[material]?.[groupedData[material]?.length - 1]
              ?.MatlType || "",
          Function: "UPD",
          Version:Version,
          TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
          TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
          creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
          dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
          IsFirstCreate: false,
          MassEditId: dynamicData?.otherPayloadData["MassEditId"],
          TotalIntermediateTasks:
            dynamicData?.otherPayloadData["TotalIntermediateTasks"],
          IntermediateTaskCount:
            dynamicData?.otherPayloadData["IntermediateTaskCount"],
          Toclientdata: {
            ClientId: ClientId,
            Material: material,
            Function: "UPD",
          },
          Tochildrequestheaderdata: {
            ChildRequestId:
              dynamicData?.childRequestHeaderData?.ChildRequestId || null,
            MaterialGroupType:
              dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
            TaskId: taskData?.taskId || null,
            Comments: dynamicData?.Comments || "",
            TotalIntermediateTasks:
              dynamicData?.childRequestHeaderData?.TotalIntermediateTasks ||
              null,
            IntermediateTaskCount:
              dynamicData?.childRequestHeaderData?.IntermediateTaskCount ||
              null,
            ReqCreatedBy:
              dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
            ReqCreatedOn:
              dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
            ReqUpdatedOn:
              dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
            RequestType:
              dynamicData?.childRequestHeaderData?.RequestType || null,
            RequestPrefix:
              dynamicData?.childRequestHeaderData?.RequestPrefix || null,
            RequestDesc:
              dynamicData?.childRequestHeaderData?.RequestDesc || null,
            RequestPriority:
              dynamicData?.childRequestHeaderData?.RequestPriority || null,
            RequestStatus:
              dynamicData?.childRequestHeaderData?.RequestStatus || null,
            CurrentLevel: taskData?.ATTRIBUTE_3 || "",
            CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
            ParticularLevel: dynamicData?.Level || "-1",
            TaskName: taskData?.taskDesc || "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
            DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
            Version: dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION,
          },
          Tomaterialdescription: materialGroup.map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
          }),
          Torequestheaderdata: dynamicData?.requestHeaderData || {},
          Tomaterialerrordata: dynamicData?.errorData[material] || {},
          TemplateName: template,
          ...(dynamicData?.Comments && {
            Comments: dynamicData?.Comments || "",
          }),
          changeLogData: createChangeLogData(material,groupedData[material]?.[0]?.ChangeLogId),
        };
      });
      return result;
    } else {
      const keysToRemove = ["id", "slNo", "MatlType"];

      const result = Object.keys(groupedData).map((material) => ({
        Tomaterialdescription: groupedData[material].map((item) => {
          const newItem = { ...item, Function: "UPD" };
          keysToRemove.forEach((key) => delete newItem[key]);
          return newItem;
        }),
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Toclientdata: {
          ClientId: null,
          Function: "UPD",
        },
        Material: material,
        MatlType:
          groupedData[material]?.[groupedData[material]?.length - 1]
            ?.MatlType || "",
        TemplateName: template,
        IsFirstCreate: true,
        Function: "UPD",
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
        changeLogData: createChangeLogData(material),
      }));
      return result;
    }
  };
  const getWarehouseDataPayload = (display) => {
    const groupedData = changeFieldRows.reduce((acc, current) => {
      if (selectedRows?.length !== 0 && !selectedRows?.includes(current?.id)) {
        return acc;
      }
      const material = current?.Material;

      if (!acc[material]) {
        acc[material] = [];
      }

      acc[material].push(current);

      return acc;
    }, {});

    if (display) {
      const keysToRemove = [
        "id",
        "MaterialId",
        "ClientId",
        "slNo",
        "ChangeLogId",
        "MatlType",
        "Version",
      ];

      const result = Object.keys(groupedData).map((material) => {
        const materialGroup = groupedData[material];
        const { MaterialId,Version } = materialGroup[0];
        return {
          MaterialId: MaterialId,
          Material: material,
          MassChildEditId: dynamicData?.otherPayloadData["MassChildEditId"],
          MatlType:
            groupedData[material]?.[groupedData[material]?.length - 1]
              ?.MatlType || "",
          Function: "UPD",
          Version:Version,
          TaskId: dynamicData?.otherPayloadData["TaskId"] || "",
          TaskName: dynamicData?.otherPayloadData["TaskName"] || "",
          creationTime: dynamicData?.otherPayloadData["CreationTime"] || "",
          dueDate: dynamicData?.otherPayloadData["DueDate"] || "",
          IsFirstCreate: false,
          MassEditId: dynamicData?.otherPayloadData["MassEditId"],
          TotalIntermediateTasks:
            dynamicData?.otherPayloadData["TotalIntermediateTasks"],
          IntermediateTaskCount:
            dynamicData?.otherPayloadData["IntermediateTaskCount"],
          Towarehousedata: materialGroup.map((item) => {
            const newItem = { ...item, Function: "UPD" };
            keysToRemove.forEach((key) => delete newItem[key]);
            return newItem;
          }),
          Tochildrequestheaderdata: {
            ...dynamicData?.childRequestHeaderData,
            ChildRequestId:
              dynamicData?.childRequestHeaderData?.ChildRequestId || null,
            MaterialGroupType:
              dynamicData?.childRequestHeaderData?.MaterialGroupType || null,
            TaskId: taskData?.taskId || null,
            Comments: dynamicData?.Comments || "",
            TotalIntermediateTasks:
              dynamicData?.childRequestHeaderData?.TotalIntermediateTasks ||
              null,
            IntermediateTaskCount:
              dynamicData?.childRequestHeaderData?.IntermediateTaskCount ||
              null,
            ReqCreatedBy:
              dynamicData?.childRequestHeaderData?.ReqCreatedBy || null,
            ReqCreatedOn:
              dynamicData?.childRequestHeaderData?.ReqCreatedOn || null,
            ReqUpdatedOn:
              dynamicData?.childRequestHeaderData?.ReqUpdatedOn || null,
            RequestType:
              dynamicData?.childRequestHeaderData?.RequestType || null,
            RequestPrefix:
              dynamicData?.childRequestHeaderData?.RequestPrefix || null,
            RequestDesc:
              dynamicData?.childRequestHeaderData?.RequestDesc || null,
            RequestPriority:
              dynamicData?.childRequestHeaderData?.RequestPriority || null,
            RequestStatus:
              dynamicData?.childRequestHeaderData?.RequestStatus || null,
            CurrentLevel: taskData?.ATTRIBUTE_3 || "",
            CurrentLevelName: taskData?.ATTRIBUTE_4 || "",
            ParticularLevel: dynamicData?.Level || "-1",
            TaskName: taskData?.taskDesc || "",
            ApproverGroup: taskData?.ATTRIBUTE_5 || "",
            TaskCreatedOn: taskData?.createdOn ? convertToDateFormat(taskData?.createdOn) : null, 
            DueDate: taskData?.compDeadline ? convertToDateFormat(taskData?.compDeadline) : null,
            Version : dynamicData?.childRequestHeaderData?.Version || DEFAULT_VALUES.DEFAULT_VERSION
          },
          Torequestheaderdata: dynamicData?.requestHeaderData || {},
          Tomaterialerrordata: dynamicData?.errorData[material] || {},
          TemplateName: template,
          changeLogData: createChangeLogData(material,groupedData[material]?.[0]?.ChangeLogId),
          ...(dynamicData?.Comments && {
            Comments: dynamicData?.Comments || "",
          }),
        };
      });

      return result;
    } else {
      const keysToRemove = ["id", "slNo", "MatlType"];

      const result = Object.keys(groupedData).map((material) => ({
        Towarehousedata: groupedData[material].map((item) => {
          const newItem = { ...item, Function: "UPD" };
          keysToRemove.forEach((key) => delete newItem[key]);
          return newItem;
        }),
        Torequestheaderdata: {
          RequestId: requestState?.requestHeader?.requestId,
          ReqCreatedBy: requestState?.requestHeader?.reqCreatedBy,
          ReqCreatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          ReqUpdatedOn: convertToDateFormat(
            requestState?.requestHeader?.reqCreatedOn
          ),
          RequestType: requestState?.requestHeader?.requestType,
          RequestPriority: requestState?.requestHeader?.requestPriority,
          RequestDesc: requestState?.requestHeader?.requestDesc,
          RequestStatus: requestState?.requestHeader?.requestStatus,
          FirstProd: payloadData?.payloadData?.FirstProductionDate || null,
          LaunchDate: payloadData?.payloadData?.LaunchDate || null,
          LeadingCat: requestState?.requestHeader?.leadingCat,
          Division: requestState?.requestHeader?.division,
          Region: requestState?.requestHeader?.region,
          TemplateName: requestState?.requestHeader?.templateName,
          FieldName: requestState?.requestHeader?.fieldName,
        },
        Tochildrequestheaderdata: {},
        Material: material,
        MatlType:
          groupedData[material]?.[groupedData[material]?.length - 1]
            ?.MatlType || "",
        TemplateName: template,
        IsFirstCreate: true,
        Function: "UPD",
        ...(dynamicData?.Comments && { Comments: dynamicData?.Comments || "" }),
        changeLogData: createChangeLogData(material),
      }));
      return result;
    }
  };

  return { changePayloadForTemplate };
};

export default useChangePayloadCreation;
