import React, { useEffect } from "react";
import {
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Box,
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { colors } from "@constant/colors";
import FilterField from "../Common/ReusableFilterBox/FilterField";
import { container_Padding } from "../Common/commonStyles";

const GenericViewGeneral = (props) => {
  const [expanded, setExpanded] = React.useState({
    0: props?.isMandatoryFailed || false,
  });
  useEffect(() => {
    setExpanded((prevExpanded) => ({
      ...prevExpanded,
      0: props?.isMandatoryFailed ? true : false, // Expand if mandatory fields are failed
    }));
  },[props?.isMandatoryFailed])
  const handleAccordionChange = (index) => (event, isExpanded) => {
    setExpanded((prevExpanded) => ({
      ...prevExpanded,
      [index]: isExpanded,
    }));
  };

  return (
    <Accordion sx={{ marginBottom: "20px", boxShadow: 3, borderRadius: "10px",borderColor: props.isMandatoryFailed ? colors.error.dark : colors.hover.hoverbg}} onChange={handleAccordionChange(0)} expanded={expanded[0]} key={1}>
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        sx={{
          backgroundColor: colors.primary.whiteSmoke,
          borderRadius: "10px",
          padding: "8px 16px",
          "&:hover": { backgroundColor: colors.hover.hoverbg },
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          {props?.viewName}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        {
          props?.GeneralFields?.map((item) => (
            <Grid
              item
              md={12}
              sx={{
                backgroundColor: colors.white,
                maxHeight: "max-content",
                height: "max-content",
                borderRadius: "8px",
                border: `1px solid ${colors.grey}`,
                mt: 0.25,
                boxShadow: "0px 2px 14px 0px rgba(48, 38, 185, 0.10)",
                ...container_Padding,
              }}
              key={item[0]}
            >
              <Grid container>
                <Typography
                  sx={{
                    fontSize: "12px",
                    fontWeight: "700",
                    paddingBottom: "10px",
                  }}
                >
                  {item[0]}
                </Typography>
              </Grid>
              <Box>
                <Grid container spacing={1}>
                  {[...item[1]]
                    .filter((x) => x.visibility !== "Hidden")
                    .sort((a, b) => a.sequenceNo - b.sequenceNo)
                    .map((innerItem) => (
                      <FilterField
                        key={innerItem.fieldName}
                        disabled={props?.disabled}
                        field={innerItem}
                        dropDownData={props.dropDownData}
                        materialID={props?.materialID}
                        selectedMaterialNumber={props?.selectedMaterialNumber}
                        viewName={props?.viewName}
                        plantData={props.viewName} // Pass Redux-friendly combination
                        missingFields={props.missingFields}
                      />
                    ))}
                </Grid>
              </Box>
            </Grid>
          ))
        }
      </AccordionDetails>
    </Accordion>
  );
};

export default GenericViewGeneral;
