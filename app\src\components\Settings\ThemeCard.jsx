import React from "react";
import { Box, Typography, Card, CardContent, Stack, Chip, Radio, Tooltip } from "@mui/material";

const ColorSwatch = ({ color, label }) => (
  <Tooltip title={color}>
    <Stack alignItems="center" spacing={0} width="5rem">
      <Box
        sx={{
          width: 60,
          height: 40,
          backgroundColor: color,
          borderRadius: 2,
          border: "0.5px solid #e0e0e0",
        }}
      />
      <Typography variant="caption" color="text.secondary" noWrap sx={{ maxWidth: "100%", textAlign: "center" }}>
        {label}
      </Typography>
    </Stack>
  </Tooltip>
);

const ThemeCard = ({ theme, selected, onSelect, paletteKeys }) => {
  const { themeLabel, themeKey, ...palette } = theme;

  return (
    <Card
      variant="outlined"
      onClick={onSelect}
      onKeyDown={(e) => (e.key === "Enter" || e.key === " ") && onSelect()}
      tabIndex={0}
      role="button"
      aria-pressed={selected}
      sx={{
        outline: "none",
        borderColor: selected ? "primary.main" : "#e0e0e0",
        borderWidth: 1,
        borderRadius: 2,
        cursor: "pointer",
        transition: "border-color 0.3s ease",
        "&:hover": { borderColor: "primary.main" },
        "&:focus": {
          borderColor: "primary.main",
          boxShadow: (theme) => `0 0 0 2px ${theme.palette.primary.light}`,
        },
      }}
    >
      <CardContent>
        <Stack direction="row" justifyContent="space-between" alignItems="center" mb={2}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Radio checked={selected} value={themeKey} size="small" />
            <Typography variant="subtitle1" fontWeight={500}>
              {themeLabel}
            </Typography>
          </Stack>
          {selected && (
            <Chip label="Selected" size="small" sx={{ fontSize: 10 }} color="primary" />
          )}
        </Stack>

        <Stack direction="row" flexWrap="wrap" gap={2}>
          {paletteKeys.map(({ key, label }) => {
            const [section, color] = key.split(".");
            const colorValue = palette?.[section]?.[color] || "#ccc";
            return <ColorSwatch key={key} color={colorValue} label={label} />;
          })}
        </Stack>
      </CardContent>
    </Card>
  );
};


export default ThemeCard;
