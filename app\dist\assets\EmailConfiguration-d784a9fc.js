import{c9 as Ut,ca as Vt,cb as Gt,c as n,j as t,aj as gt,cg as Ii,al as xt,am as wt,an as de,ai as St,O,R as qt,a6 as Ge,S as ai,d as T,$ as bi,aa as Ae,F as Se,dW as ni,hK as Ei,hL as wi,dz as vi,hM as Ai,hN as nt,aT as ze,n as lt,r as m,t as I,s as zt,bD as Nt,fV as Ot,hO as Ri,Z as P,B as k,b5 as ri,b6 as dt,aZ as b,ag as Re,T as Et,aJ as Lt,dy as Ct,hP as Ft,bs as li,bS as Ni,bE as tt,dj as We,hQ as ci,bw as Oi,hR as _i,hS as ki,ak as oi,g as jt,a$ as di,fv as Wt,b3 as at,hT as Di,hU as si,ah as $t,hV as Mi,hW as Bi,hX as <PERSON>,C as vt,au as At,b9 as Li,gd as Fi,hY as Wi,bn as Ui,bo as pt,fs as Vi,cs as Gi,hZ as zi,ch as ji,aK as $i,a8 as Xe,a4 as et,h_ as Hi,a as <PERSON>,N as Ji,Q as Xi,bC as Zi,ac as Qi,h$ as Pt,i0 as Ki}from"./index-226a1e75.js";import{d as qi}from"./EditOutlined-b0a055aa.js";import{d as ui}from"./DeleteOutlined-9dca1b70.js";import{D as He,r as Ht,h as mi,d as pi,a as ea,T as ta,b as ia,c as ei,e as aa}from"./Check-30add394.js";import{f as ti}from"./featureConfig-652a9f8d.js";import{d as hi,a as fi}from"./ChevronRight-8fea09f9.js";import{d as Yt}from"./Description-d98685cc.js";import{d as na}from"./Edit-3af3a8b3.js";var Jt={},ra=Vt;Object.defineProperty(Jt,"__esModule",{value:!0});var gi=Jt.default=void 0,la=ra(Ut()),ca=Gt;gi=Jt.default=(0,la.default)((0,ca.jsx)("path",{d:"M22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2zm-2 0-8 5-8-5zm0 12H4V8l8 5 8-5z"}),"EmailOutlined");const _t=e=>n(St,{open:e==null?void 0:e.open,children:[t(gt,{className:"styleMessageBox",sx:{height:"3rem",display:"flex",alignItems:"center",fontWeight:"500",borderRadius:"7px",background:"#F1F5FE",fontSize:"1.5rem",color:"black "},children:"Confirmation"}),t(xt,{sx:{minWidth:"20rem",display:"flex",alignItems:"center"},children:t(Ii,{sx:{display:"flex",alignItems:"center"},children:e==null?void 0:e.message})}),n(wt,{sx:{height:"3rem",borderTop:"2px solid #d9d9d9"},children:[(e==null?void 0:e.creationType)!=="Timeout"&&t(de,{variant:"outlined",size:"small",sx:{color:"red",borderColor:"red !important"},onClick:()=>{e==null||e.onClose("Cancel")},children:"Cancel"}),t(de,{variant:"contained",size:"small",onClick:()=>e==null?void 0:e.onClose(e==null?void 0:e.creationType),children:"Ok"})]})]});function oa({promptState:e,setPromptState:d,handlePromptClose:S,onCloseAction:f,promptFullWidth:r,promptMessage:R,promptMaxWidth:i,showInputText:s,inputText:W,setInputText:j,dialogInputPlaceholder:V,DialogMessageContent:M,dialogSeverity:c,dialogTitleText:v,handleCancelButtonAction:_,cancelButtonText:te,showCancelButton:H,handleOkButtonAction:Z,okButtonText:F,showOkButton:u,handleExtraButtonAction:X,extraButtonText:le,showExtraButton:Q}){var ie,Ce,oe,ae;const ce=()=>{d(!1)};return t(Se,{children:n(St,{hideBackdrop:!1,elevation:2,PaperProps:{sx:{boxShadow:"none",minWidth:450}},open:e,onClose:()=>{f?f():S?S():ce()},fullWidth:r,children:[n(O,{container:!0,sx:{display:"flex",justifyContent:"space-between"},children:[v&&t(O,{item:!0,children:n(gt,{id:"alert-dialog-title",sx:{fontWeight:600,display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:"16px"},children:[c&&n("span",{style:{display:"flex",alignItems:"center"},children:[t(qt,{iconName:((Ce=(ie=ti.severityIcons)==null?void 0:ie[c.toUpperCase()])==null?void 0:Ce.iconName)??"",iconColor:((ae=(oe=ti.severityIcons)==null?void 0:oe[c.toUpperCase()])==null?void 0:ae.iconColor)??""}),"  "]}),v]})}),t(O,{item:!0,sx:{padding:"12px"},children:t(Ge,{onClick:Te=>{Te.stopPropagation(),S?S():ce()},children:t(qt,{iconName:"Close"})})})]}),t(xt,{sx:{paddingTop:0},children:n(ai,{children:[t(O,{container:!0,children:n(O,{item:!0,md:12,sx:{padding:"0px 20px 20px 0px",textAlign:"left"},children:[M&&t(M,{}),R&&t(T,{children:R})]})}),s&&t(bi,{sx:{height:"auto"},fullWidth:!0,children:t(Ae,{sx:{backgroundColor:"#F5F5F5"},value:W,onChange:Te=>j(Te.target.value),multiline:!0,placeholder:V})})]})}),(H||u||Q)&&n(wt,{sx:{paddingRight:"1.5rem"},children:[H&&t(de,{variant:"outlined",sx:{height:40,minWidth:"4rem",textTransform:"none",borderColor:"#3B30C8",color:"#3B30C8"},onClick:()=>{_?_():S?S():ce()},children:te??"Cancel"}),u&&t(de,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{Z?Z():S?S():ce()},children:F??"Ok"}),Q&&t(de,{variant:"contained",style:{height:40,minWidth:"4rem",backgroundColor:"#3B30C8",textTransform:"none"},onClick:()=>{X&&X(),S?S():ce()},children:le??"Ok"})]})]})})}function da({promptState:e,setPromptState:d,handleSnackBarPromptClose:S,promptMessage:f}){const r=()=>{d(!1)};return t(ai,{spacing:2,sx:{width:"100%"},children:t(ni,{autoHideDuration:5e3,anchorOrigin:{vertical:"top",horizontal:"center"},open:e,onClose:()=>{S?S():r()},message:f,sx:{height:"150px"}})})}function kt(e){switch(e==null?void 0:e.type){case"dialog":return t(oa,{...e});case"snackbar":return t(da,{...e})}}const{VITE_APP_TOKEN:sa}={VITE_ENV:"dev",VITE_CLIENT_ID:"************-45noron6i0dj5j5l5ljin32fa635ts1m.apps.googleusercontent.com",VITE_DESTINATION_ADMIN:"cw-mdg-admin-dest",VITE_DESTINATION_SERVICE_REQUEST:"cw-scp-serv-req-oauth2-dev",VITE_DESTINATION_PO:"cw-scp-purch-order-oauth2-dev",VITE_DESTINATION_INVOICE:"cw-scp-invoice-oauth2-dev",VITE_DESTINATION_DOCUMENT_MANAGEMENT:"cw-mdg-documentmanagement-dest",VITE_DESTINATION_RETURNS:"cw-scp-returns-oauth2-dev",VITE_DESTINATION_MANAGE_ACCOUNT:"cw-scp-manage-acct-oauth2-dev",VITE_DESTINATION_NOTIFICATION:"cw-scp-notification-oauth2-dev",VITE_DESTINATION_BOM:"cw-mdg-billofmaterial-dest",VITE_DESTINATION_PR:"cw-scp-pr-oauth2-dev",VITE_DESTINATION_IWA:"cw-mdg-iwm-dev",VITE_DESTINATION_IWA_NPI:"cw-mdg-iwa-oauth2-dest",VITE_DESTINATION_SERVICE_ENTRY_SHEET:"cw-scp-ses-oauth2-dev",VITE_DESTINATION_PLANNING_MANAGEMENT:"cw-scp-pfm-oauth2-dev",VITE_DESTINATION_MATERIAL_MGMT:"cw-mdg-materialmanagement-dest",VITE_DESTINATION_ARTICLE_MGMT:"cw-mdg-articlemanagement-dest",VITE_DESTINATION_AI:"cw-mdg-artificialintelligence-dest",VITE_DESTINATION_WEBSOCKET:"cw-mdg-notification-dest",VITE_DESTINATION_COST_CENTER:"cw-mdg-costcenter-dest",VITE_DESTINATION_PROFIT_CENTER:"cw-mdg-profitcenter-dest",VITE_DESTINATION_BANK_KEY:"cw-mdg-bankkey-dest",VITE_DESTINATION_GENERAL_LEDGER:"cw-mdg-generalledger-dest",VITE_DESTINATION_DASHBOARD:"cw-mdg-dashboard-dest",VITE_DESTINATION_SLA_MGMT:"cw-mdg-slamanagement-dest",VITE_DESTINATION_IDM:"cw-caf-idm-services",VITE_DESTINATION_ITM_JAVA_SERVICES:"ITMJavaServices",VITE_DESTINATION_IWA_NEW:"IWAApi",VITE_CW_MDG_COSTCENTER_MASS_DEST:"cw-mdg-costcenter-dest",VITE_CW_MDG_GENERALLEDGER_MASS_DEST:"cw-mdg-generalledger-dest",VITE_CW_MDG_PROFITCENTER_MASS_DEST:"cw-mdg-profitcenter-dest",VITE_DESTINATION_INTERNAL_ORDER:"cw-mdg-internalorder-dest",VITE_URL_ITM_JAVA_SERVICES:"https://cw-mdg-iwm-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_ITM_JAVA_SERVICES:"https://cherryworkproducts-itm-java-dev.cfapps.eu10-004.hana.ondemand.com",VITE_BASE_URL_MESSAGING_SERVICES:"https://messaging.cherryworkproducts.com",VITE_BASE_URL_CRUD_SERVICES:"https://crudservicesdev.cherryworkproducts.com",VITE_BASE_URL_IWASCP_SERVICES:"https://cw-scp-authentication.cfapps.eu10-004.hana.ondemand.com",VITE_URL_MATERIAL_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ARTICLE_MGMT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-articlemanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AI:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-artificialintelligence.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DOCUMENT_MANAGEMENT:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-documentmanagement.cfapps.eu10-004.hana.ondemand.com",VITE_URL_WEBSOCKET:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-notification.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_PROFIT_CENTER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BANK_KEY:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-bankkey.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_ADMIN:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-admin.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NPI:"https://cw-mdg-authentication-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_DASHBOARD:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-dashboard.cfapps.eu10-004.hana.ondemand.com",VITE_URL_SLA_MGMT:"https://cw-mdg-slamanagement-dev.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IDM:"https://cw-caf-idm-services.cfapps.eu10-004.hana.ondemand.com",VITE_URL_AUTH_TOKEN:"https://cw-mdg-materialmanagement-dev.cfapps.eu10-004.hana.ondemand.com/authenticate/token",VITE_URL_AUTH_TOKEN_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-materialmanagement.cfapps.eu10-004.hana.ondemand.com/authenticate/tokenCaf",VITE_URL_PROFIT_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-profitcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_COST_CENTER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-costcenter.cfapps.eu10-004.hana.ondemand.com",VITE_URL_GENERAL_LEDGER_MASS_CAF:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-generalledger.cfapps.eu10-004.hana.ondemand.com",VITE_URL_INTERNAL_ORDER:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-internalorder.cfapps.eu10-004.hana.ondemand.com",VITE_URL_BOM:"https://incture-cherrywork-dev-cw-mdg-dev-cw-mdg-billofmaterial.cfapps.eu10-004.hana.ondemand.com",VITE_URL_IWA_NEW:"https://incture-cherrywork-dev-cw-caf-dev-cw-caf-iwa-services.cfapps.eu10-004.hana.ondemand.com",VITE_APP_TOKEN:"********************************************************************************************************************************************************************************************************************************************************************************.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.rvLKfD9TuxV9sQ1EtaiV167ddozR_YJX-jwcHmqBD6hbKXjlXrydLQ5a0S5YR3I_q_t-d6sDEBnHUKO1XSQYb2Sq3Lr_R_Q9UKQmlaXP5BJqRqe1ZxE7E9spZ8CT4fMgLGkUQ_FPF-9MJJ6zZArGOg8_m8S4O0hhebZfRjkWhpqu-ScVKb4CM57NVcdqys7WRAFGUmYz6dygk0sL3zW2XhwaPSnW8p9gOXqbKkAyEVXP_n3TrJfSzlU1MVcaW6M9vLYifcAlMP2hu7Zox21hsNesKEf6cSMVNCIW4uEYFTkcroyQz8Jrhg7m9tAagIGgNO30a1YV9FmPzvjMPzmCSw",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1},Rt=Ei({reducerPath:"notificationApi",baseQuery:wi({baseUrl:vi(Ai),prepareHeaders:(e,{getState:d})=>{const{token:S,environment:f}=d().applicationConfig||{};return S&&(f==="localhost"||f==="127.0.0.1")&&e.set("authorization",`Bearer ${sa}`),e.set("Access-Control-Allow-Origin","*"),e}}),endpoints:()=>({})}),ua=Rt.injectEndpoints({endpoints:e=>({getIdentifiers:e.query({query:()=>`${nt}${ze.EMAIL_CONFIG.POPULATE_APP_IDENTIFIERS_HANA}`}),getMailList:e.query({query:()=>`${nt}${ze.API.MAIL_DEFINATION}`}),submitMailTemplate:e.mutation({query:d=>{const{templateData:S,...f}=d;let r,R;return S.emailDefinitionId===""?(r="POST",R=`${nt}${ze.API.MAIL_DEFINATION}`):(r="PATCH",R=`${nt}${ze.API.MAIL_DEFINATION}/${S.emailDefinitionId}`),{url:R,method:r,body:f}}}),deleteMailTemplate:e.mutation({query:d=>({url:`${nt}${ze.API.MAIL_DEFINATION}/${d}`,method:"DELETE"})})})}),{useGetIdentifiersQuery:xi,useGetMailListQuery:Xt,useSubmitMailTemplateMutation:ma,useDeleteMailTemplateMutation:ha}=ua,fa=({...e})=>{var se;const d=lt(A=>A.userReducer);m.useState(!1);const[S,f]=I.useState(!1);zt();const[r,R]=I.useState(()=>He.EditorState.createEmpty()),[i,s]=I.useState(()=>He.EditorState.createEmpty()),[W,j]=I.useState("1"),[V,M]=m.useState(new Map),[c,v]=m.useState(new Map),[_,te]=I.useState(!1),[H,Z]=I.useState(""),[F,u]=I.useState("Cancel"),{refetch:X}=Xt(),[le,{isLoading:Q}]=ha(),ce=(A,w)=>{j(w)},ie=A=>{A==="delete"&&Ce(e==null?void 0:e.data),te(!1)},Ce=async A=>{Ne(w=>({...w,open:!1})),f(Q);try{const w=await le(A.emailDefinitionId).unwrap();(w==null?void 0:w.statusCode)===Lt.STATUS_200&&(X(),U.handleOpenPromptBox("SUCCESS",{message:w==null?void 0:w.message,redirectOnClose:!1}))}catch{U.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to Delete template",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"})}f(Q)},oe=A=>{const w=mi(A),{contentBlocks:G,entityMap:ye}=w,$=He.ContentState.createFromBlockArray(G,ye),ge=He.EditorState.createWithContent($);s(ge),R(ge)},ae=(A,w)=>{var G=new FileReader;G.readAsDataURL(A),G.onload=()=>w(G.result),G.onerror=ye=>{}},Te=A=>new Promise((w,G)=>ae(A,ye=>w({data:{link:ye}})));I.useEffect(()=>{var A;d.applicationName!==""&&oe((A=e==null?void 0:e.data)==null?void 0:A.content)},[e==null?void 0:e.data,d]),m.useEffect(()=>{d.applicationName!==""&&(e==null||e.userList.map(A=>(V.set(A.emailId,A.userName),{code:A.userName,description:A.emailId})),M(new Map(V)),e==null||e.allGroups.map(A=>(c.set(A.id,A.name),{code:A.name,description:A.id})),v(new Map(c)))},[d,e==null?void 0:e.allGroups]);const[ne,Ne]=m.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[E,K]=m.useState(""),U={handleClosePromptBox:()=>{Ne(A=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),K("")},handleOpenPromptBox:(A,w={})=>{let G={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};A==="SUCCESS"&&(G.type="snackbar"),K(A),Ne({...G,...w})},handleCloseAndRedirect:()=>{U.handleClosePromptBox(),navigate("")},getCancelFunction:()=>{switch(E){default:return()=>{U.handleClosePromptBox()}}},getCloseFunction:()=>{switch(E){case"COMMENTERROR":default:return A=>{U.handleClosePromptBox()}}},getOkFunction:()=>{switch(E){case"DELETE_TEMPLATE":return()=>ie("delete");default:return()=>U.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>ne.redirectOnClose?U.handleCloseAndRedirect:U.handleClosePromptBox};return n("div",{children:[t(Ot,{className:"backdrop",sx:{zIndex:"9"},open:S,children:t(Nt,{color:"primary"})}),t(kt,{type:ne.type,promptState:ne.open,setPromptState:U.handleClosePromptBox,onCloseAction:U.getCloseFunction(),promptMessage:ne.message,dialogSeverity:ne.severity,dialogTitleText:ne.title,handleCancelButtonAction:U.getCancelFunction(),cancelButtonText:ne.cancelText,showCancelButton:ne.cancelButton,handleSnackBarPromptClose:U.getCloseAndRedirectFunction(),handleOkButtonAction:U.getOkFunction(),okButtonText:ne.okButtonText,showOkButton:ne.okButton}),n(Ri,{anchor:"right",onClose:e==null?void 0:e.onClose,open:e==null?void 0:e.open,PaperProps:{elevation:4,sx:{width:"45vw",minWidth:"600px",bgcolor:P.background.default,borderRadius:"12px 0 0 12px",padding:"24px"}},children:[n(k,{sx:{borderBottom:`1px solid ${P.border.main}`,pb:2,mb:3,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[n(ri,{value:W,onChange:ce,sx:{"& .MuiTab-root":{textTransform:"none",fontSize:"14px",fontWeight:500,minWidth:"unset",px:3,color:P.text.secondary,"&.Mui-selected":{color:P.primary.main,fontWeight:600}},"& .MuiTabs-indicator":{backgroundColor:P.primary.main,height:"3px",borderRadius:"2px"}},children:[t(dt,{label:"Template Information",value:"1"}),t(dt,{label:"Preview Information",value:"2"})]}),n(b,{direction:"row",spacing:1.5,children:[t(Ge,{onClick:()=>{e==null||e.setOpenCreateTemplate(!0),e==null||e.setSelectedRow(e==null?void 0:e.data),e==null||e.setCreationType("edit"),e==null||e.setScenario("EDIT"),e==null||e.setIsEditing(e==null?void 0:e.index),e==null||e.setOpenTemplateDialog(!1)},sx:{color:P.primary.main,"&:hover":{backgroundColor:P.primary.lighter}},children:t(qi,{fontSize:"small"})}),d.feature.EMAIL_CONFIG_DELETE==="True"&&t(Ge,{onClick:()=>{e==null||e.setSelectedRow(e==null?void 0:e.data),U.handleOpenPromptBox("DELETE_TEMPLATE",{title:"Confirm Delete",message:"Do you want to delete this record?",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Ok"})},sx:{color:P.error.main,"&:hover":{backgroundColor:P.error.lighter}},children:t(ui,{fontSize:"small"})})]})]}),W==="1"&&t(b,{spacing:3,sx:{px:2,height:"100%",overflow:"auto"},children:[{label:(e==null?void 0:e.headers[3])||"Template Name",value:(se=e==null?void 0:e.data)==null?void 0:se.name},{label:"Identifier",value:e==null?void 0:e.data.identifierDesc},{label:(e==null?void 0:e.headers[1])||"Module",value:e==null?void 0:e.data.entityDesc},{label:(e==null?void 0:e.headers[2])||"Event",value:e==null?void 0:e.data.processDesc},{label:"Recipients",value:(e==null?void 0:e.cardData.toParticipantType)==="GROUP"?c.get(parseInt(e==null?void 0:e.cardData.toParticipant)):(e==null?void 0:e.cardData.toParticipantType)==="USER"?V.get(e==null?void 0:e.cardData.toParticipant):e==null?void 0:e.cardData.toParticipant},{label:"CC Recipients",value:(e==null?void 0:e.cardData.ccParticipantType)==="GROUP"?c.get(parseInt(e==null?void 0:e.cardData.ccParticipant)):((e==null?void 0:e.cardData.toParticipantType)==="USER",e==null?void 0:e.cardData.ccParticipant)}].map((A,w)=>n(Re,{elevation:0,sx:{p:2,borderRadius:"8px",backgroundColor:P.background.paper,border:`1px solid ${P.border.light}`},children:[t(T,{variant:"body1",sx:{color:P.text.primary,fontWeight:600,display:"block",mb:1,fontSize:"0.95rem"},children:A.label}),t(Et,{title:A.value||"Not Available",children:t(T,{variant:"body2",sx:{fontWeight:500,color:P.text.secondary,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:A.value||"Not Available"})})]},w))}),W==="2"&&n(Re,{elevation:0,sx:{width:"100%",flexGrow:1,bgcolor:P.background.paper,overflow:"auto",border:`1px solid ${P.border.light}`,borderRadius:"8px"},children:[t(k,{sx:{borderBottom:`1px solid ${P.border.light}`,p:2,bgcolor:P.background.neutral},children:t(T,{variant:"subtitle2",sx:{color:P.text.primary,fontWeight:600},children:"Email Preview"})}),t(b,{sx:{height:"100%",minHeight:"65vh",p:3},children:t(Ht.Editor,{editorState:r,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:i,onEditorStateChange:R,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:Te,previewImage:!0,alignmentEnabled:!0}},toolbarHidden:!0,readOnly:!0})})]})]}),t(_t,{message:H,creationType:F,open:_,onClose:A=>ie(A)})]})},Zt=e=>{var R,i,s,W,j,V,M,c,v;const[d,S]=m.useState({}),f=_=>{S({[_]:!0})},r=()=>{S({})};return n(Se,{children:[t(O,{item:!0,children:t(Ct,{elevation:0,sx:{backgroundColor:P.primary.veryLight,borderRadius:"8px",border:`1px solid ${P.border.light}`,height:"160px",cursor:"pointer","&:hover":{backgroundColor:P.primary.light,borderColor:P.border.medium}},onClick:()=>{var _;return f((_=e==null?void 0:e.cardData)==null?void 0:_.emailDefinitionId)},children:t(Ft,{sx:{height:"100%"},children:n(li,{sx:{p:2,height:"100%",display:"flex",flexDirection:"column",backgroundColor:P.primary.veryLight,justifyContent:"space-between",gap:1},children:[n(k,{sx:{flex:"1 1 auto"},children:[t(Et,{title:(R=e==null?void 0:e.cardData)==null?void 0:R.name,placement:"top",children:t(T,{sx:{fontSize:"14px",color:P.text.primary,mb:.5,fontWeight:700,display:"-webkit-box",WebkitLineClamp:"1",WebkitBoxOrient:"vertical",overflow:"hidden",lineHeight:"1.2"},children:(i=e==null?void 0:e.cardData)==null?void 0:i.name})}),n(k,{sx:{display:"flex",alignItems:"center",mb:.5},children:[t(hi,{sx:{fontSize:12,color:((s=e==null?void 0:e.cardData)==null?void 0:s.status)==="Active"?P.success.vibrant:P.warning.amber,mr:.5}}),t(T,{sx:{fontSize:"12px",color:((W=e==null?void 0:e.cardData)==null?void 0:W.status)==="Active"?P.success.vibrant:P.warning.amber},children:(j=e==null?void 0:e.cardData)==null?void 0:j.status})]}),t(Ni,{sx:{my:.5}}),t(T,{sx:{fontSize:"12px",color:P.text.secondary,mb:.5,wordBreak:"break-word"},children:((V=e==null?void 0:e.cardData)==null?void 0:V.subject)||"Approval for Request"})]}),n(k,{sx:{display:"flex",gap:.5,flexWrap:"wrap",flexShrink:0},children:[((M=e==null?void 0:e.cardData)==null?void 0:M.entityDesc)&&t(Et,{title:e.cardData.entityDesc,placement:"top",children:t(tt,{label:e.cardData.entityDesc,size:"small",sx:{backgroundColor:P.primary.light,color:P.primary.main,fontSize:"10px",height:"20px",maxWidth:"120px",border:`1px solid ${P.primary.border}`,"& .MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",px:1},"&:hover":{backgroundColor:P.primary.ultraLight}}})}),((c=e==null?void 0:e.cardData)==null?void 0:c.identifierDesc)&&t(Et,{title:e.cardData.identifierDesc,placement:"top",children:t(tt,{label:e.cardData.identifierDesc,size:"small",sx:{backgroundColor:P.secondary.light,color:P.secondary.green,fontSize:"10px",height:"20px",maxWidth:"120px",border:`1px solid ${P.secondary.lightGreen}`,"& .MuiChip-label":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",px:1},"&:hover":{backgroundColor:P.primary.chipBackground}}})})]})]})})})},e==null?void 0:e.index),t(fa,{open:d[(v=e==null?void 0:e.cardData)==null?void 0:v.emailDefinitionId],onClose:r,setCreationType:e==null?void 0:e.setCreationType,index:e==null?void 0:e.index,data:e==null?void 0:e.cardData,setSelectedRow:e==null?void 0:e.setSelectedRow,setOpenCreateTemplate:e==null?void 0:e.setOpenCreateTemplate,setOpenTemplateDialog:S,mailmappingData:e==null?void 0:e.mailmappingData,groupList:e==null?void 0:e.groupList,userList:e==null?void 0:e.userList,allGroups:e==null?void 0:e.allGroups,headers:e==null?void 0:e.headers,setScenario:e.setScenario,...e})]})};function ga(e){const[d,S]=m.useState({});return t(Se,{children:e!=null&&e.isLoading?t(Se,{children:t(O,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((f,r)=>t(O,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:t(Re,{width:"100%",sx:{padding:"1rem"},children:n(b,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[t(We,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),t(We,{variant:"rounded",width:"80%",height:60,p:2}),n(b,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},r))})}):t(O,{container:!0,rowSpacing:2,columnSpacing:{xs:2,sm:2,md:3},children:(e!=null&&e.filteredData.length||(e==null?void 0:e.searchParam)!==""?e==null?void 0:e.filteredData:e==null?void 0:e.emailTemplateData).map((f,r)=>t(Se,{children:t(Zt,{cardData:f,index:r,setCreationType:e==null?void 0:e.setCreationType,setSelectedRow:e==null?void 0:e.setSelectedRow,setOpenCreateTemplate:e==null?void 0:e.setOpenCreateTemplate,setOpenTemplateDialog:S,mailmappingData:e==null?void 0:e.mailmappingData,groupList:e==null?void 0:e.groupList,userList:e==null?void 0:e.userList,setIsEditing:e==null?void 0:e.setIsEditing,allGroups:e==null?void 0:e.allGroups,headers:e==null?void 0:e.headers,setScenario:e==null?void 0:e.setScenario})}))})})}var Qt={},xa=Vt;Object.defineProperty(Qt,"__esModule",{value:!0});var Tt=Qt.default=void 0,Sa=xa(Ut()),Ca=Gt;Tt=Qt.default=(0,Sa.default)((0,Ca.jsx)("path",{d:"M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8z"}),"Folder");function Si({message:e="No templates available"}){return t(k,{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",textAlign:"center",children:n(k,{children:[t(Yt,{sx:{fontSize:64,color:"#e0e0e0",mb:2}}),t(T,{variant:"h6",color:"text.primary",children:e}),t(T,{color:"text.secondary",children:"Please create a new template"})]})})}function Ta(e){var W,j,V,M,c;const[d,S]=m.useState(null),f=((W=e.active)==null?void 0:W.length)&&e.active,r=m.useMemo(()=>{const v={};if(f)return f==null||f.forEach(_=>{const te=_.entityDesc||"Uncategorized";v[te]||(v[te]=[]),v[te].push(_)}),v},[f]);if(m.useEffect(()=>{var v;!d&&r&&((v=Object.keys(r))==null?void 0:v.length)>0&&S(Object.keys(r)[0])},[r,d]),e.isLoading)return t(O,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((v,_)=>t(O,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:t(Re,{width:"100%",sx:{padding:"1rem"},children:n(b,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[t(We,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),t(We,{variant:"rounded",width:"80%",height:60,p:2}),n(b,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},_))});if(e.active.length<1)return t(Si,{});const R=()=>Tt,i=()=>"blue",s={blue:{bg:"#e3f2fd",border:"#90caf9",icon:"#1e88e5",text:"#0d47a1",badge:"#bbdefb"},green:{bg:"#e8f5e9",border:"#a5d6a7",icon:"#43a047",text:"#1b5e20",badge:"#c8e6c9"},purple:{bg:"#f3e5f5",border:"#ce93d8",icon:"#8e24aa",text:"#4a148c",badge:"#e1bee7"},gray:{bg:"#f5f5f5",border:"#e0e0e0",icon:"#757575",text:"#212121",badge:"#eeeeee"}};return n(k,{display:"flex",bgcolor:"#f9fafb",children:[n(k,{width:"20%",bgcolor:"#fff",borderRight:"1px solid #e0e0e0",boxShadow:1,children:[n(k,{p:3,borderBottom:"1px solid #f0f0f0",children:[t(T,{variant:"h6",fontWeight:"bold",color:"text.primary",children:"Template Categories"}),t(T,{variant:"body2",color:"text.secondary",mt:.5,children:"Select a category to view templates"})]}),t(k,{p:2,display:"flex",flexDirection:"column",gap:2,children:(j=Object.entries(r))==null?void 0:j.map(([v,_])=>{const te=R()||Tt,H=i(),Z=d===v,F=_.filter(X=>X.status==="Active").length,u=s[H]||s.gray;return n(Re,{elevation:Z?4:1,onClick:()=>S(v),sx:{border:`2px solid ${Z?u.border:"#e0e0e0"}`,backgroundColor:Z?u.bg:"#fff",p:2,borderRadius:2,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:2,backgroundColor:Z?u.bg:"#f5f5f5"}},children:[n(b,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[n(b,{direction:"row",spacing:2,alignItems:"center",children:[t(k,{px:.5,pt:.3,borderRadius:2,bgcolor:"#ffffff",children:t(te,{sx:{color:u.icon,fontSize:20}})}),n(k,{children:[t(T,{variant:"subtitle2",fontWeight:600,color:u.text,children:v}),n(T,{variant:"caption",color:"text.secondary",children:[_.length," template",_.length!==1?"s":""]})]})]}),Z&&t(fi,{sx:{color:"#9e9e9e",fontSize:16}})]}),n(b,{direction:"row",justifyContent:"space-between",mt:2,children:[t(tt,{label:`${F} Active`,size:"small",sx:{backgroundColor:u.badge,color:u.text}}),_.length-F>0&&t(tt,{label:`${_.length-F} Inactive`,size:"small",sx:{backgroundColor:"#ffecb3",color:"#ff6f00"}})]})]},v)})})]}),t(k,{flex:1,overflow:"auto",children:d?n(k,{p:3,children:[n(k,{mb:4,children:[n(b,{direction:"row",spacing:2,alignItems:"center",mb:1,children:[I.createElement(R(),{style:{color:"#1e88e5",fontSize:28}}),t(T,{variant:"h5",fontWeight:"bold",children:d})]}),n(T,{variant:"body1",color:"text.secondary",children:[(V=r==null?void 0:r[d])==null?void 0:V.length," template",((M=r[d])==null?void 0:M.length)!==1?"s":""," available"]})]}),t(O,{container:!0,spacing:3,children:(c=r==null?void 0:r[d])==null?void 0:c.map((v,_)=>t(O,{item:!0,xs:12,sm:6,md:4,children:t(Zt,{headers:e.headers,cardData:v,index:_,setCreationType:e.setCreationType,setSelectedRow:e.setSelectedRow,setOpenCreateTemplate:e.setOpenCreateTemplate,mailmappingData:e.mailmappingData,groupList:e.groupList,userList:e.userList,setIsEditing:e.setIsEditing,allGroups:e.allGroups,setScenario:e.setScenario})},v.emailDefinitionId))})]}):t(k,{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",children:n(k,{textAlign:"center",children:[t(Yt,{sx:{fontSize:64,color:"#e0e0e0",mb:2}}),t(T,{variant:"h6",color:"text.primary",children:"Select a Category"}),t(T,{color:"text.secondary",children:ci.TEMPLATE_MESSAGE})]})})})]})}function ya(e){var W,j,V,M;const[d,S]=m.useState(null),f=((W=e.draft)==null?void 0:W.length)&&e.draft,r=m.useMemo(()=>{if(f){const c={};return f==null||f.forEach(v=>{const _=v.entityDesc||"Uncategorized";c[_]||(c[_]=[]),c[_].push(v)}),c}},[f]);if(m.useEffect(()=>{var c,v;!d&&r&&((c=Object.keys(r))==null?void 0:c.length)>0&&S((v=Object.keys(r))==null?void 0:v[0])},[r,d]),((j=e==null?void 0:e.draft)==null?void 0:j.length)<1)return t(Si,{});const R=()=>Tt,i=()=>"blue",s={blue:{bg:"#e3f2fd",border:"#90caf9",icon:"#1e88e5",text:"#0d47a1",badge:"#bbdefb"},green:{bg:"#e8f5e9",border:"#a5d6a7",icon:"#43a047",text:"#1b5e20",badge:"#c8e6c9"},purple:{bg:"#f3e5f5",border:"#ce93d8",icon:"#8e24aa",text:"#4a148c",badge:"#e1bee7"},gray:{bg:"#f5f5f5",border:"#e0e0e0",icon:"#757575",text:"#212121",badge:"#eeeeee"}};return t(Se,{children:e!=null&&e.isLoading?t(O,{container:!0,rowSpacing:{xs:2,sm:2,md:3,lg:3},columnSpacing:{xs:2,sm:2,md:3},children:Array.from(Array(10)).map((c,v)=>t(O,{item:!0,xs:2,sm:3,md:3,xl:3,height:"10rem",children:t(Re,{width:"100%",sx:{padding:"1rem"},children:n(b,{direction:"column",spacing:1,height:"100%",width:"100%",justifyContent:"center",alignItems:"flex-start",children:[t(We,{variant:"text",sx:{fontSize:"1rem"},width:200,p:2}),t(We,{variant:"rounded",width:"80%",height:60,p:2}),n(b,{direction:"row",spacing:2,justifyContent:"flex-start",alignItems:"center",width:"100%",children:[t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}}),t(We,{variant:"rounded",width:"45%",height:24,sx:{borderRadius:"9px"}})]})]})})},v))}):n(k,{display:"flex",bgcolor:(M=(V=P)==null?void 0:V.background)==null?void 0:M.templateBg,children:[n(k,{width:"20%",bgcolor:"#fff",borderRight:"1px solid #e0e0e0",boxShadow:1,children:[n(k,{p:3,borderBottom:"1px solid #f0f0f0",children:[t(T,{variant:"h6",fontWeight:"bold",color:"text.primary",children:"Template Categories"}),t(T,{variant:"body2",color:"text.secondary",mt:.5,children:"Select a category to view templates"})]}),t(k,{p:2,display:"flex",flexDirection:"column",gap:2,children:Object.entries(r).map(([c,v])=>{const _=R()||Tt,te=i(),H=d===c,Z=v.filter(u=>u.status==="Active").length,F=s[te]||s.gray;return n(Re,{elevation:H?4:1,onClick:()=>S(c),sx:{border:`2px solid ${H?F.border:"#e0e0e0"}`,backgroundColor:H?F.bg:"#fff",p:2,borderRadius:2,cursor:"pointer",transition:"all 0.2s","&:hover":{boxShadow:2,backgroundColor:H?F.bg:"#f5f5f5"}},children:[n(b,{direction:"row",justifyContent:"space-between",alignItems:"center",children:[n(b,{direction:"row",spacing:2,alignItems:"center",children:[t(k,{px:.5,pt:.3,borderRadius:2,bgcolor:"#ffffff",children:t(_,{sx:{color:F.icon,fontSize:20}})}),n(k,{children:[t(T,{variant:"subtitle2",fontWeight:600,color:F.text,children:c}),n(T,{variant:"caption",color:"text.secondary",children:[v.length," template",v.length!==1?"s":""]})]})]}),H&&t(fi,{sx:{color:"#9e9e9e",fontSize:16}})]}),n(b,{direction:"row",justifyContent:"space-between",mt:2,children:[t(tt,{label:`${Z} Active`,size:"small",sx:{backgroundColor:F.badge,color:F.text}}),v.length-Z>0&&t(tt,{label:`${v.length-Z} Inactive`,size:"small",sx:{backgroundColor:"#ffecb3",color:"#ff6f00"}})]})]},c)})})]}),t(k,{flex:1,overflow:"auto",children:d?n(k,{p:3,children:[n(k,{mb:4,children:[n(b,{direction:"row",spacing:2,alignItems:"center",mb:1,children:[I.createElement(R(),{style:{color:"#1e88e5",fontSize:28}}),t(T,{variant:"h5",fontWeight:"bold",children:d})]}),n(T,{variant:"body1",color:"text.secondary",children:[r[d].length," template",r[d].length!==1?"s":""," available"]})]}),t(O,{container:!0,spacing:3,children:r[d].map((c,v)=>t(O,{item:!0,xs:12,sm:6,md:4,children:t(Zt,{headers:e.headers,cardData:c,index:v,setCreationType:e.setCreationType,setSelectedRow:e.setSelectedRow,setOpenCreateTemplate:e.setOpenCreateTemplate,mailmappingData:e.mailmappingData,groupList:e.groupList,userList:e.userList,setIsEditing:e.setIsEditing,allGroups:e.allGroups,setScenario:e.setScenario})},c.emailDefinitionId))})]}):t(k,{height:"100%",display:"flex",alignItems:"center",justifyContent:"center",children:n(k,{textAlign:"center",children:[t(Yt,{sx:{fontSize:64,color:"#e0e0e0",mb:2}}),t(T,{variant:"h6",color:"text.primary",children:"Select a Category"}),t(T,{color:"text.secondary",children:ci.TEMPLATE_MESSAGE})]})})})]})})}const Ci=Oi({name:"userReducer",initialState:{userToken:{},userData:{user_id:""},useWorkAccess:!1,useConfigServerDestination:!1,oDestinationUrlMap:{},aDestinationUrl:[],environment:"",applicationName:"",needHeading:!0,refreshTemplates:{},showManageGroups:!0,useCrud:!0,feature:{}},reducers:{setToken:(e,d)=>{e.userToken=d.payload},setUserData:(e,d)=>{e.userData=d.payload},setfeature:(e,d)=>{e.feature=d.payload},setAPIBaseUrl:(e,d)=>{var f;e.environment=d.payload.environment;let S={};for(let r=0;r<((f=d.payload)==null?void 0:f.destinations.length);r++)S[d.payload.destinations[r].Name]=d.payload.destinations[r].URL;e.oDestinationUrlMap=S,e.aDestinationUrl=d.payload.destinations},setConfigs:(e,d)=>{e.useWorkAccess=d.payload.useWorkAccess,e.useConfigServerDestination=d.payload.useConfigServerDestination,e.userData={user_id:d.payload.userId},e.applicationName=d.payload.applicationName,e.applicationId=d.payload.applicationId,e.needHeading=d.payload.needHeading,e.showManageGroups=d.payload.needHeading,e.useCrud=d.payload.useCrud},updateBusyLoader:(e,d)=>{e.showBusyLoader=d.payload},setRefreshTemplate:(e,d)=>{e.refreshTemplates={}}}}),{setToken:Ia,setUserData:an,setAPIBaseUrl:ba,setConfigs:Ea,setRefreshTemplate:nn,setfeature:wa}=Ci.actions,va=Ci.reducer,Aa=_i({userReducer:va,[Rt.reducerPath]:Rt.reducer}),Dt=ki({reducer:Aa,middleware:e=>e({immutableCheck:!1,serializableCheck:!1}).concat(Rt.middleware)}),rt=async(e,d,S,f,r,R)=>{const i=Ra(R);switch(e=Na(e,i),d.toLowerCase()){case"post":await fetch(e,{method:"POST",body:JSON.stringify(S),headers:i.headers,mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"postblobfile":await fetch(e,{method:"POST",body:S,headers:{authorization:i.headers.Authorization}}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"get":await fetch(e,{method:"GET",headers:i.headers,mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"getblobfile":await fetch(e,{method:"GET",headers:i.headers,mode:"cors"}).then(s=>s.blob()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"delete":await fetch(e,{method:"DELETE",headers:i.headers,mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"deletewithbody":await fetch(e,{method:"DELETE",headers:i.headers,body:JSON.stringify(S),mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"put":await fetch(e,{method:"PUT",headers:i.headers,mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break;case"patch":await fetch(e,{method:"PATCH",body:JSON.stringify(S),headers:i.headers,mode:"cors"}).then(s=>s.json()).then(s=>{f(s)}).catch(s=>{r(s)});break}},Ti=async(e,d,S,f)=>{const r=Dt.getState().userReducer;let R="";r.useCrud?R="/CrudApiServices/crud/api/fetchQuery?converterName=map":R="/CrudApiServices/api/fetchQuery?converterName=map",rt(R,"post",{query:e,args:d},function(i){S(i)},function(i){f(i)})},Ra=e=>{const d=Dt.getState().userReducer;let S={headers:{"Content-Type":"application/json","Access-Control-Allow-Origin":"*"}};return d.useWorkAccess&&(S.headers={...S.headers,Authorization:`Bearer ${d.userToken}`,tenantId:"azure"}),e&&(e==="no-auth"?delete S.headers.Authorization:S.headers={...S.headers,...e}),S},Na=(e,d)=>{var f;const S=Dt.getState().userReducer;if(e.startsWith("/CrudApiServices")&&(d.headers={...d.headers,env:S!=null&&S.environment?S.environment:"itm"}),S.useConfigServerDestination){let r=e.split("/");e=e.replace("/"+r[1],(f=S.oDestinationUrlMap)==null?void 0:f[r[1]])}return e},Oa="/assets/Logo-f785ba8f.png",_a=e=>{lt(s=>s.userReducer),I.useState(!1);const[d,S]=I.useState(()=>He.EditorState.createEmpty()),[f,r]=I.useState(()=>He.EditorState.createEmpty()),R=(s,W)=>{var j=new FileReader;j.readAsDataURL(s),j.onload=()=>W(j.result),j.onerror=V=>{}},i=s=>new Promise((W,j)=>R(s,V=>W({data:{link:V}})));return n(St,{open:e==null?void 0:e.openPreview,onClose:e==null?void 0:e.closePreview,children:[t(gt,{children:n(b,{direction:"row",spacing:2,alignItems:"center",justifyContent:"space-between",width:"100%",children:[t(T,{sx:{fontWeight:500,color:"#1D1D11",fontSize:"16px",fontFamily:'"Roboto", sans-serif !important'},children:"Preview"}),t(de,{onClick:e==null?void 0:e.closePreview,startIcon:t(oi,{})})]})}),t(xt,{children:t(k,{children:n(Re,{elevation:3,width:"80%",height:"90%",children:[t(b,{backgroundColor:"#F0EFFF",children:t("img",{src:Oa,style:{width:"100%",height:"2rem"}})}),n(b,{children:[n(b,{justifyContent:"space-around",children:[n(b,{direction:"row",justifyContent:"space-around",alignItems:"center",children:[n("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[t("span",{style:{width:"46.5%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Template Name"}),t("span",{style:{fontWeight:400,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"name"})]}),n("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 2px 0px 2px ",width:"50%"},children:[t("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Identifier"}),t("span",{style:{fontWeight:400,width:"70%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Identifier"})]})]}),n(b,{direction:"row",justifyContent:"space-around",children:[n("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[t("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Module"}),t("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Module"})]}),n("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 2px 0px 2px ",width:"50%"},children:[t("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important',whiteSpace:"nowrap"},children:"Event"}),t("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Event"})]})]}),t(b,{direction:"row",children:n("span",{style:{display:"flex",flexDirection:"column",padding:" 0px 0px 0px 2px ",width:"50%"},children:[t("span",{style:{width:"50%",fontWeight:500,color:" #6A6A6A; !important",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Subject"}),t("span",{style:{fontWeight:500,width:"50%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",color:"#1D1D11",fontSize:"14px",fontFamily:'"Roboto", sans-serif !important'},children:"Subject"})]})})]}),t(Ht.Editor,{editorState:d,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:f,onEditorStateChange:S,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:i,previewImage:!0,alignmentEnabled:!0}},toolbarHidden:!0,readOnly:!0})]}),n(b,{backgroundColor:"#F5F5F5",width:"100%",children:[n(T,{sx:{fontWeight:400,color:"#757575",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:["Send us your ",t("u",{children:"feedback!"})]}),t(T,{sx:{fontWeight:400,color:"#1D1D11",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:"Reply Directly to this email to comment , and CC teamsmates to add them as collaborators."}),n(T,{sx:{fontWeight:400,color:"#757575",fontSize:"12px",fontFamily:'"Roboto", sans-serif !important'},children:["If you want to stop recieving notifications about this task , you can",t("u",{children:"remove yourself from it."})]})]})]})})})]})},ka=e=>t("div",{children:t(()=>{switch(e==null?void 0:e.channel){case"Phone":return t("div",{});case"SMS":return t(Da,{});case"WhatsApp":return t("div",{});case"Email":return t("div",{});case"InApp":return t("div",{});default:return t("div",{})}},{})}),Da=()=>n(b,{diection:"column",justifyContent:"start",alignItems:"center",children:[t(T,{variant:"h2",children:"SMS Configuration"}),n(b,{spacing:1,direction:"row",justifyItems:"flex-start",alignItems:"center",children:[t(T,{variant:"body2",noWrap:!0,color:"text.primary",children:"Graph Title"}),t(T,{variant:"body2",noWrap:!0,color:"error.main",children:"*"})]})]}),Ma={Phone:{key:"Phone",label:"Phone",icon:t(Mi,{})},SMS:{key:"SMS",label:"SMS",icon:t(Bi,{})},WhatsApp:{key:"WhatsApp",label:"WhatsApp",icon:t(Pi,{})},Email:{key:"Email",label:"Email",icon:t(Wt,{})},InApp:{key:"InApp",label:"In-App",icon:t(si,{})}},ii=({scenario:e,open:d,onClose:S,userList:f=[],groupList:r=[],setScenario:R,...i})=>{var p,ue;const s=lt(a=>a.userReducer),W=jt(),[j,V]=I.useState(!1),[M,c]=I.useState(!1),[v,_]=m.useState(!1),[te,H]=I.useState([]),[Z,F]=I.useState([]),[u,X]=I.useState({name:"",entity:null,process:null,identifier:null,subject:"",body:"",application:s.applicationName,emailDefinitionId:"",createdBy:s.userData.user_id,createdOn:new Date().toISOString(),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()}),[le,Q]=I.useState(()=>He.EditorState.createEmpty()),[ce,ie]=I.useState(()=>He.EditorState.createEmpty()),[Ce,oe]=I.useState(!1),[ae,Te]=I.useState(""),[ne,Ne]=I.useState("Cancel"),[E,K]=m.useState([]),[U,se]=m.useState([]),[A,w]=m.useState([]),[G,ye]=m.useState(!1),[$,ge]=m.useState("success"),[Ee,Ye]=m.useState(""),[Ze,st]=m.useState(""),[Be,me]=I.useState(!1),[he,xe]=m.useState(new Map),[we,Qe]=m.useState(new Map),[Oe,je]=m.useState([]),[fe,Ue]=m.useState({}),[Pe,y]=m.useState([]),[h,_e]=m.useState({receipentType:"",receipents:"",ccType:"",cc:""}),Ie=["INITIATOR","REVIEWER"],[Je,Ve]=m.useState("Email"),{data:$e}=xi(),{refetch:yt}=Xt(),[ut]=ma();m.useEffect(()=>{let a=(i==null?void 0:i.contentHTML)??"";mt(a)},[]);const l=(a,g)=>{if(_e(z=>({...z,[`${a}`]:g})),h.receipentType=="VARIABLE"&&g=="VARIABLE"&&je(Ie),a==="receipentType"&&g=="GROUP"){var o=[],J={};for(let[z,B]of we.entries())J[B]=z,o.push(B);Ue({...J,"":""}),je(o)}if(h.receipentType=="USER"){var o=[];for(let[,B]of he.entries())o.push(B);je(o)}if(a=="ccType"&&g=="VARIABLE"&&y(Ie),h.receipentType=="VARIABLE"&&g=="VARIABLE"&&je(Ie),a==="ccType"&&g=="GROUP"){var o=[],J={};for(let[L,re]of we.entries())J[re]=L,o.push(re);Ue({...J,"":""}),y(o)}if(a=="ccType"&&g=="VARIABLE"&&y(Ie),a=="ccType"&&g=="USER"){var o=[];for(let[,B]of he.entries())o.push(B);y(o)}},C=()=>{X({name:"",entity:null,process:null,identifier:null,subject:"",body:"",application:"",emailDefinitionId:"",createdBy:s.userData.user_id,createdOn:new Date().toISOString(),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()});let a=(i==null?void 0:i.contentHTML)??"";mt(a),_e({receipentType:"",receipents:"",ccType:"",cc:""}),se([])},x=a=>{ht(g=>({...g,open:!1})),a!=="Cancel"&&a!=="Discard"&&a!=="Timeout"?u.entity&&u.process&&u.name&&u.identifier?(D(a),Y(),(i==null?void 0:i.creationType)!=="edit"&&(i==null||i.setCreationType("new")),i==null||i.setSelectedRow(i==null?void 0:i.data),i==null||i.setIsEditing(null)):V(!1):a==="Discard"?(C(),i==null||i.setSelectedRow(null),i==null||i.setCreationType("new"),i==null||i.setIsEditing(null)):a==="Timeout"&&window.location.reload(),oe(!1),Te("")},D=async a=>{var o,J,z,B;c(!0);let g={application:s.applicationName,applicationDesc:s.applicationName,content:u.body,createdBy:u.createdBy,createdOn:u.createdOn,emailDefinitionId:u.emailDefinitionId,entity:u.entity.entity,entityDesc:u.entity.entityDesc,name:u.name,process:u.process.process,processDesc:u.process.processDesc,identifier:(o=u==null?void 0:u.identifier)==null?void 0:o.identifier,identifierDesc:(J=u==null?void 0:u.identifier)==null?void 0:J.identifierDesc,status:a,subject:u.subject.replaceAll("[","").replaceAll("]",""),updatedBy:s.userData.user_id,updatedOn:new Date().toISOString()};try{const L=await ut({templateData:u,...g});((z=L==null?void 0:L.data)==null?void 0:z.statusCode)===Lt.STATUS_200&&(yt(),ot()),q.handleOpenPromptBox("SUCCESS",{message:(B=L==null?void 0:L.data)==null?void 0:B.message,redirectOnClose:!1}),c(!1)}catch(L){c(!1),q.handleOpenPromptBox("ERROR",{message:L==null?void 0:L.message,redirectOnClose:!1})}},N=(a,g)=>{V(!0);const o=B=>{if((B==null?void 0:B.statusCode)===401||(B==null?void 0:B.statusCode)==="401")q.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(B){const L=Array.isArray(B)?B:[];H(L)}V(!1)},J=B=>{var L,re,Me;(L=i==null?void 0:i.setAlert)==null||L.call(i,!0),(re=i==null?void 0:i.setAlertSeverity)==null||re.call(i,"error"),(Me=i==null?void 0:i.setAlertMessage)==null||Me.call(i,B),V(!1)},z=`/${At}${ze.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(g)}`;vt(z,"get",o,J)},Y=()=>{me(!0);let a="/WorkUtilsServices/v1/application/variables",g=[];g=U||[{}],rt(a,"patch",g,function(o){me(!1),Ye("Updated successfully"),ge("success"),ke(),me(!1)},function(){me(!1),Ye("error"),ge("error"),ke()})},ee=(a,g,o)=>{V(!0);const J=L=>{if((L==null?void 0:L.statusCode)===401||(L==null?void 0:L.statusCode)==="401")q.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(L){const re=Array.isArray(L)?L:[];F(re)}V(!1)},z=L=>{var re,Me,pe;(re=i==null?void 0:i.setAlert)==null||re.call(i,!0),(Me=i==null?void 0:i.setAlertSeverity)==null||Me.call(i,"error"),(pe=i==null?void 0:i.setAlertMessage)==null||pe.call(i,L),V(!1)},B=`/${At}${ze.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(o)}&notificationId=${encodeURIComponent(g)}`;vt(B,"get",J,z)},be=()=>{if(me(!0),s.aDestinationUrl&&s.aDestinationUrl){const a=o=>{var J,z,B;if((o==null?void 0:o.statusCode)===Lt.STATUS_200){K(o==null?void 0:o.data);let L=(J=o==null?void 0:o.data)==null?void 0:J.filter(re=>re.processDesc===u.process.processDesc);se((z=L==null?void 0:L[0])==null?void 0:z.variables),w((B=o==null?void 0:o.data)==null?void 0:B.map(ct))}},g=()=>{me(!1),Ye("error"),ge("error"),ke()};rt(`${nt}${ze.EMAIL_CONFIG.PROCESS_VARIABLE}?application=${s.applicationName}&entity=${u.entity.entity}&identifier=${u.identifier.identifier}`,"get",{},a,g)}},Le=()=>{let a=f==null?void 0:f.map(g=>(he==null||he.set(g.emailId,g.userName),g.emailId));xe(new Map(he)),je(a),y(a)},ke=()=>{ye(!0)},ct=a=>({process:a==null?void 0:a.process,processDesc:a==null?void 0:a.processDesc,active:a==null?void 0:a.variables.filter(o=>(o==null?void 0:o.active)===!0),deactive:a==null?void 0:a.variables.filter(o=>(o==null?void 0:o.active)===!1)}),Ke=(a,g)=>{var o=new FileReader;o.readAsDataURL(a),o.onload=()=>g(o.result),o.onerror=()=>{}},Mt=a=>new Promise(g=>Ke(a,o=>g({data:{link:o}}))),Bt=a=>{let g=pi(a);try{let o=document.createElement("div");o.style.display="flex",o.style.flexDirection="column",o.style.justifyContent="start",o.innerHTML=g,[...o.children].forEach(z=>{if(z.localName==="p")if(z.innerHTML)z.style.display="block !important",z.style.margin="0em",z.style.lineHeight="1.2em",z.style.width="100%";else{let B=document.createElement("br");z.replaceWith(B)}}),g=o.outerHTML}catch(o){console.log(o)}X({...u,body:g})},mt=a=>{const g=mi(a),{contentBlocks:o,entityMap:J}=g,z=He.ContentState.createFromBlockArray(o,J),B=He.EditorState.createWithContent(z);ie(B),Q(B)},It=a=>/^[A-Za-z][A-Za-z0-9_]*$/.test(a),bt=["VARIABLE","GROUP","USER"];I.useEffect(()=>{var a,g,o,J,z,B,L;if(s.applicationName!=="")if((i==null?void 0:i.creationType)!=="new"&&(i!=null&&i.data)){let re=i==null?void 0:i.data.subject;if(re!==""){re=re.replaceAll("$","$[");let Me=re.split(" ");Me=Me.map(pe=>(pe[0]==="$"&&(pe+="]"),pe)),re="",Me.forEach(pe=>re+=pe+" ")}X({name:(i==null?void 0:i.creationType)==="copy"?(i==null?void 0:i.data.name)+"_Copy":i==null?void 0:i.data.name,entity:{entity:(a=i==null?void 0:i.data)==null?void 0:a.entity,entityDesc:(g=i==null?void 0:i.data)==null?void 0:g.entityDesc},process:{process:i==null?void 0:i.data.process,processDesc:i==null?void 0:i.data.processDesc},identifier:{identifier:i==null?void 0:i.data.identifier,identifierDesc:i==null?void 0:i.data.identifierDesc},subject:re,body:i==null?void 0:i.data.content,application:i==null?void 0:i.data.application,applicationDesc:i==null?void 0:i.data.applicationDesc,emailDefinitionId:(i==null?void 0:i.creationType)==="copy"?"":i==null?void 0:i.data.emailDefinitionId,createdBy:i==null?void 0:i.data.createdBy,createdOn:new Date(i==null?void 0:i.data.createdOn).toISOString(),updatedBy:(J=(o=i==null?void 0:i.data)==null?void 0:o.createdBy)==null?void 0:J.updatedBy,updatedOn:new Date(i==null?void 0:i.data.updatedOn).toISOString()}),_e({receipentType:i==null?void 0:i.data.toParticipantType,receipents:(i==null?void 0:i.data.toParticipantType)==="GROUP"?(z=Array.from(we).find(([Me])=>Me==(i==null?void 0:i.data.toParticipant)))==null?void 0:z[1]:i==null?void 0:i.data.toParticipant,ccType:i==null?void 0:i.data.ccParticipantType,cc:(i==null?void 0:i.data.ccParticipantType)==="GROUP"?(B=Array.from(we).find(([Me])=>Me==(i==null?void 0:i.data.ccParticipant)))==null?void 0:B[1]:i==null?void 0:i.data.ccParticipant}),l("receipentType",i==null?void 0:i.data.toParticipantType),l("ccType",i==null?void 0:i.data.ccParticipantType),N(i==null?void 0:i.data.application,i==null?void 0:i.data.identifier),ee(i==null?void 0:i.data.application,i==null?void 0:i.data.entity,i==null?void 0:i.data.identifier),mt((L=i==null?void 0:i.data)==null?void 0:L.content)}else C()},[i==null?void 0:i.data,i==null?void 0:i.creationType,s]);const qe=(a,g)=>{let o=structuredClone(U);o[g].active=!a.active,se(o)};m.useEffect(()=>{u!=null&&u.identifier&&(u!=null&&u.process)&&(u!=null&&u.entity)&&be(u==null?void 0:u.entity)},[u==null?void 0:u.identifier,u==null?void 0:u.process,u==null?void 0:u.entity,s.applicationId]),m.useEffect(()=>{s.applicationId!==void 0&&Le()},[s.applicationId]);const[Fe,ht]=m.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[ft,De]=m.useState(""),q={handleClosePromptBox:()=>{ht(a=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),De("")},handleOpenPromptBox:(a,g={})=>{let o={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};a==="SUCCESS"&&(o.type="snackbar"),De(a),ht({...o,...g})},handleCloseAndRedirect:()=>{q.handleClosePromptBox(),W("/purchaseManagement/purchaseOrder")},getCancelFunction:()=>{switch(ft){default:return()=>{q.handleClosePromptBox()}}},getCloseFunction:()=>{switch(ft){case"COMMENTERROR":default:return a=>{q.handleClosePromptBox()}}},getOkFunction:()=>{switch(ft){case"CONFIRM_DISCARD":return()=>{ot()};case"CONFIRM_SUBMIT":return()=>{x("Active")};case"CONFIRM_SUBMIT_AS_DRAFT":return()=>{x("Draft")};case"TIMEOUT":return()=>{x("Timeout")};default:return()=>q.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>Fe.redirectOnClose?q.handleCloseAndRedirect:q.handleClosePromptBox};let it=a=>{switch(a){case"CONFIRM_DISCARD":q.handleOpenPromptBox("CONFIRM_DISCARD",{title:"Confirm Discard",message:"Are you sure you want to proceed with discard? The entered data will be lost",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Discard"});break;case"CONFIRM_SUBMIT":if(!It(u.name)){q.handleOpenPromptBox("WARNING",{title:"Error",message:"Please Enter a valid template name",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}if(!u.entity|!u.process|!u.name|!u.identifier){q.handleOpenPromptBox("WARNING",{title:"Error",message:"Please fill in all the mandatory fields",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"});return}q.handleOpenPromptBox("CONFIRM_SUBMIT",{title:"Confirm Submit",message:"Are you sure you want to proceed with submission?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Submit"});break;case"CONFIRM_SUBMIT_AS_DRAFT":q.handleOpenPromptBox("CONFIRM_SUBMIT_AS_DRAFT",{title:"Confirm Submit",message:"Are you sure you want to proceed with saving as draft?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Save as draft"});break}};const ot=()=>{S(),(i==null?void 0:i.creationType)!=="edit"&&C(),R("INITIAL"),K([]),st("new")},ve=()=>{_(!1)};return m.useEffect(()=>{var a;((a=u.subject)==null?void 0:a.length)>100&&q.handleOpenPromptBox("ERROR",{title:"Error",message:"Subject exceeded max length of 100",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"})},[u.subject]),n("div",{children:[t(Ot,{className:"backdrop",sx:{zIndex:"9999999"},open:M,children:t(Nt,{color:"primary"})}),t(kt,{type:Fe.type,promptState:Fe.open,setPromptState:q.handleClosePromptBox,onCloseAction:q.getCloseFunction(),promptMessage:Fe.message,dialogSeverity:Fe.severity,dialogTitleText:Fe.title,handleCancelButtonAction:q.getCancelFunction(),cancelButtonText:Fe.cancelText,showCancelButton:Fe.cancelButton,handleSnackBarPromptClose:q.getCloseAndRedirectFunction(),handleOkButtonAction:q.getOkFunction(),okButtonText:Fe.okButtonText,showOkButton:Fe.okButton}),t(O,{container:!0,sx:{marginBottom:"1.5rem"},children:n(O,{item:!0,md:9,style:{display:"flex"},children:[t(O,{item:!0,sx:{maxWidth:"max-content"},children:t(Ge,{onClick:ot,color:"primary",component:"label",className:"iconButton-spacing-small",sx:{padding:"0.25rem",height:"max-content"},children:t(di,{sx:{fontSize:"25px",color:"#000000"}})})}),n(O,{item:!0,xs:!0,children:[t(T,{variant:"h3",children:n("strong",{children:[" ",(i==null?void 0:i.creationType)==="new"||(i==null?void 0:i.creationType)==="copy"?"Create":"Edit"," Template"]})}),n(T,{variant:"body2",color:"#777",children:["This view allows users to ",e==="CREATE"?"create":"edit"," Email Templates"]})]})]})}),n(O,{container:!0,alignItems:"center",justifyContent:"center",children:[n(k,{sx:{marginBottom:"64px",width:"100%"},className:"cwWorkUtilsScroll",children:[n(Ct,{sx:{padding:"1.5rem",marginBottom:"1.5rem",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.08)",background:"linear-gradient(to bottom, #ffffff, #fafbff)"},children:[n(T,{variant:"h5",sx:{marginBottom:"1.5rem",fontWeight:600,color:"#1a3e72",display:"flex",alignItems:"center",gap:"0.5rem"},children:[t(Wt,{fontSize:"small"})," Email Template"]}),n(O,{container:!0,spacing:3,columns:24,children:[n(O,{item:!0,xs:6,children:[t(T,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:i!=null&&i.headers[3]?i==null?void 0:i.headers[3]:"Template Name"}),t(Ae,{fullWidth:!0,size:"small",placeholder:"Enter Template Name",className:"CustomTextField",id:"outlined-basic",variant:"outlined",value:u.name,onChange:a=>X({...u,name:a.target.value}),sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}})]}),n(O,{item:!0,xs:6,children:[t(T,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:i!=null&&i.headers[0]?i==null?void 0:i.headers[0]:"Identifier"}),t(at,{disabled:(i==null?void 0:i.creationType)==="edit",variant:"outlined",disablePortal:!0,className:"CustomAutoComplete",size:"small",id:"combo-box-demo",placeholder:"Enter Identifier Name",options:$e,value:u.identifier,renderInput:a=>t(Ae,{...a,fullWidth:!0,placeholder:"Enter Identifier Name",sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.identifierDesc?a.identifierDesc:"",onChange:(a,g)=>{X({...u,identifier:g,entity:null,process:null}),g&&N(s.applicationName,g.identifier)},isOptionEqualToValue:(a,g)=>a.identifier===g.identifier})]}),n(O,{item:!0,xs:6,children:[t(T,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:i!=null&&i.headers[1]?i==null?void 0:i.headers[1]:"Module"}),t(at,{disabled:(i==null?void 0:i.creationType)==="edit",disablePortal:!0,id:"combo-box-demo",size:"small",options:te,value:u.entity,renderInput:a=>t(Ae,{...a,fullWidth:!0,placeholder:"Enter Module Name",sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.entityDesc?a.entityDesc:"",onChange:(a,g)=>{var o;X({...u,entity:g,process:null}),g&&ee(s.applicationName,g.entity,(o=u==null?void 0:u.identifier)==null?void 0:o.identifier)},isOptionEqualToValue:(a,g)=>a.entity===g.entity})]}),n(O,{item:!0,xs:6,children:[t(T,{variant:"body1",sx:{marginBottom:"0.5rem",fontWeight:500},children:i!=null&&i.headers[2]?i==null?void 0:i.headers[2]:"Event"}),t(at,{disabled:(i==null?void 0:i.creationType)==="edit",variant:"outlined",disablePortal:!0,size:"small",id:"combo-box-demo",options:Z,value:u.process,renderInput:a=>t(Ae,{placeholder:"Enter Event Name",fullWidth:!0,...a,sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px","&:hover fieldset":{borderColor:"#1976d2"}}}}),getOptionLabel:a=>a.processDesc?a.processDesc:"",onChange:(a,g)=>{X({...u,process:g})},isOptionEqualToValue:(a,g)=>a.processDesc===g.processDesc})]})]}),n(k,{sx:{marginTop:"2rem",padding:"1.5rem",backgroundColor:"#f8f9fa",borderRadius:"6px"},children:[t(T,{variant:"h5",sx:{marginBottom:"1rem",fontWeight:600,color:"#1a3e72"},children:"Event Variables"}),U!=null&&U.length?t(b,{direction:"row",rowGap:2,columnGap:1.5,alignItems:"start",justifyContent:"start",sx:{display:"flex",width:"100%",flexWrap:"wrap",padding:"0.5rem"},children:U==null?void 0:U.map((a,g)=>t(de,{variant:a!=null&&a.active?"contained":"outlined",color:a!=null&&a.active?"primary":"secondary",sx:{marginBottom:"0.75rem",borderRadius:"20px",padding:"6px 16px",textTransform:"none",fontWeight:500,boxShadow:a!=null&&a.active?"0 2px 5px rgba(0,0,0,0.1)":"none"},onClick:()=>qe(a,g),children:a==null?void 0:a.variable},g))}):t(k,{sx:{minHeight:"20vh",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"#fff",borderRadius:"6px",border:"1px dashed #d32f2f",padding:"1rem"},children:t(T,{sx:{color:"#d32f2f",fontWeight:500,fontSize:"0.95rem"},children:"No variables available. Please select an event first."})})]})]}),(i==null?void 0:i.useChannels)&&t(k,{sx:{padding:"1rem 1rem ",marginBottom:"1rem"},children:n(b,{container:!0,direction:"column",children:[t(T,{sx:{marginBottom:"1rem"},variant:"h5",children:"Channels"}),n(O,{spacing:2,direction:"row",children:[t(b,{justifyContent:"start",alignItems:"center",direction:"row",spacing:2,children:Object.values(Ma).map(a=>t(de,{onClick:()=>Ve(a.key),variant:"contained",color:"action",startIcon:a.key===Je?t(Di,{}):a.icon,children:a.label}))}),t(ka,{channel:Je})]})]})}),n(Ct,{sx:{padding:"1.5rem",marginBottom:"1.5rem",borderRadius:"8px",boxShadow:"0 2px 8px rgba(0,0,0,0.08)",background:"linear-gradient(to bottom, #ffffff, #fafbff)"},children:[n(T,{variant:"h5",sx:{marginBottom:"1.5rem",fontWeight:600,color:"#1a3e72",display:"flex",alignItems:"center",gap:"0.5rem"},children:[t(Wt,{fontSize:"small"})," Email Template"]}),n(O,{container:!0,direction:"column",spacing:3,children:[n(O,{item:!0,children:[n(k,{sx:{display:"flex",alignItems:"flex-start",gap:"1rem",width:"100%"},children:[t(T,{sx:{fontWeight:500,width:"80px",paddingTop:"8px",color:"#555"},children:"Subject"}),t(Ae,{fullWidth:!0,size:"small",placeholder:"Enter email subject line",inputProps:{maxLength:100},value:u.subject,onChange:a=>X({...u,subject:a.target.value}),sx:{"& .MuiOutlinedInput-root":{borderRadius:"6px",backgroundColor:"#fff",transition:"all 0.2s","&:hover fieldset":{borderColor:"#1976d2"},"&.Mui-focused fieldset":{borderColor:"#3b30c8",borderWidth:"1px"}}}})]}),n(k,{sx:{mt:.5,textAlign:"right",color:((p=u.subject)==null?void 0:p.length)>80?"#d32f2f":"#666",fontSize:"0.75rem"},children:[((ue=u.subject)==null?void 0:ue.length)||0,"/100"]})]}),n(O,{item:!0,children:[t(T,{sx:{fontWeight:500,mb:1.5,color:"#555"},children:"Email Body"}),t(k,{sx:{border:"1px solid #e0e0e0",borderRadius:"8px",overflow:"hidden","& .rdw-editor-toolbar":{border:"none",borderBottom:"1px solid #e0e0e0",padding:"8px 16px",background:"#f5f7fa"},"& .rdw-option-wrapper":{border:"none",background:"transparent",height:"28px",width:"28px",display:"flex",alignItems:"center",justifyContent:"center",margin:"0 2px",borderRadius:"4px","&:hover":{background:"#e3e8f0"},"&.rdw-option-active":{background:"#d8e0f0"}},"& .rdw-dropdown-wrapper":{border:"1px solid #e0e0e0",borderRadius:"4px",margin:"0 4px"},"& .Editor":{padding:"16px",minHeight:"250px",fontSize:"14px",lineHeight:"1.6",color:"#333"},"& .public-DraftStyleDefault-block":{margin:"0.5em 0"},"& .rdw-link-modal, & .rdw-image-modal":{boxShadow:"0 3px 10px rgba(0,0,0,0.15)",borderRadius:"8px"},"& .rdw-suggestion-dropdown":{borderRadius:"8px",boxShadow:"0 3px 10px rgba(0,0,0,0.15)",padding:"8px 0"},"& .rdw-suggestion-option":{padding:"8px 16px","&:hover":{background:"#f0f4fa"}}},children:t(Ht.Editor,{editorState:le,wrapperClassName:"Editor",editorClassName:"Editor",defaultEditorState:ce,onEditorStateChange:Q,onContentStateChange:Bt,toolbar:{inline:{inDropdown:!1},list:{inDropdown:!1},textAlign:{inDropdown:!1},link:{inDropdown:!1},history:{inDropdown:!1},image:{uploadCallback:Mt,previewImage:!0,alignmentEnabled:!0}},mention:{separator:" ",trigger:"$",suggestions:U==null?void 0:U.map(a=>({text:a.variable??"",value:a.variable??"",url:a.variable??""}))}})}),t(k,{sx:{mt:2,display:"flex",alignItems:"center",gap:1},children:n(k,{sx:{display:"inline-flex",alignItems:"center",gap:.5,backgroundColor:"#f0f4fa",padding:"4px 10px",borderRadius:"4px",fontSize:"0.75rem",color:"#3b30c8",fontWeight:500},children:[t(si,{fontSize:"small",sx:{fontSize:"14px"}}),"Use $variable to insert dynamic content"]})})]})]})]}),(i==null?void 0:i.isRecepientData)&&t(O,{item:!0,xs:12,p:4,spacing:2,children:n(O,{container:!0,children:[t(T,{sx:{fontWeight:500,color:"black ",fontSize:"18px"},children:"Select The Participants"}),n(O,{container:!0,spacing:2,p:2,children:[n(O,{item:!0,xs:12,children:[t(T,{children:"Recipent Type"}),t(at,{value:h==null?void 0:h.receipentType,onSelect:a=>{l("receipentType",a.target.value)},disablePortal:!0,id:"combo-box-demo",options:bt,size:"small",renderInput:a=>t(Ae,{fullWidth:!0,...a,label:"Select Recipient Type"})})]}),n(O,{item:!0,xs:12,children:[t(T,{children:"Receipents"}),t(at,{value:h==null?void 0:h.receipents,onSelect:a=>l("receipents",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:Oe,size:"small",renderInput:a=>t(Ae,{fullWidth:!0,...a,label:"Select Recipients"})})]}),n(O,{item:!0,xs:12,children:[t(T,{children:"CC Type"}),t(at,{value:h==null?void 0:h.ccType,onSelect:a=>l("ccType",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:bt,size:"small",renderInput:a=>t(Ae,{fullWidth:!0,...a,label:"Add CC recipient type"})})]}),n(O,{item:!0,xs:12,children:[t(T,{children:"CC"}),t(at,{value:h==null?void 0:h.cc,onSelect:a=>l("cc",a.target.value),fullWidth:!0,disablePortal:!0,id:"combo-box-demo",options:Pe,size:"small",renderInput:a=>t(Ae,{fullWidth:!0,...a,label:"Add CC recipient "})})]})]})]})})]}),t(Re,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:"4"},elevation:2,children:t($t,{className:"container_BottomNav",children:n(b,{direction:"row",sx:{marginLeft:"auto"},spacing:2,alignItems:"center",children:[s.feature.EMAIL_CONFIG_DISCARD==="True"&&t(de,{variant:"text",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{it("CONFIRM_DISCARD")},children:"Discard"}),s.feature.EMAIL_CONFIG_SAVE==="True"&&t(de,{variant:"outlined",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{it("CONFIRM_SUBMIT_AS_DRAFT")},children:"Save As Draft"}),s.feature.EMAIL_CONFIG_SUBMIT==="True"&&t(de,{variant:"contained",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:()=>{it("CONFIRM_SUBMIT")},children:"Submit"})]})})})]}),t(_t,{message:ae,creationType:ne,open:Ce,onClose:a=>x(a)}),t(_a,{openPreview:v,closePreview:ve})]})},Ba=Li(e=>({avatar:{width:"26px",height:"26px",background:"#F1F5FE"}})),Pa=e=>{var d=[""];return e&&(d=e==null?void 0:e.trim().split(",")[0].split(" ")),d.length>1?`${d[0][0]}${d[(d==null?void 0:d.length)-1][0]}`:d[0].length>0?`${d[0][0]}`:""};function La(e){const d=Ba();return t(Fi,{...e,src:(e==null?void 0:e.src)&&(e==null?void 0:e.src),className:`${d.avatar} ${e==null?void 0:e.className}`,sx:(e==null?void 0:e.sx)&&(e==null?void 0:e.sx),children:Pa(e==null?void 0:e.name)})}const Fa=({open:e,onClose:d,setScenario:S,...f})=>{const r=useSelector(y=>y.userReducer),[R,i]=I.useState("1"),[s,W]=I.useState(!1),[j,V]=I.useState(!1),[M,c]=I.useState(!1),[v,_]=I.useState(""),[te,H]=I.useState("Cancel"),[Z,F]=m.useState("success"),[u,X]=m.useState(""),[le,Q]=m.useState(!1);m.useState([]),m.useState([]);const[ce,ie]=I.useState(!1),[Ce,oe]=m.useState(null),[ae,Te]=m.useState(new Map),[ne,Ne]=m.useState(new Map),[E,K]=m.useState([]),[U,se]=m.useState([]),[A,w]=m.useState([]),[G,ye]=m.useState([]),[$,ge]=m.useState({id:null,groupName:"",email:"",createdBy:r.userData.user_id,createdOn:new Date().toISOString(),updatedBy:r.userData.user_id,updatedOn:new Date().toISOString()}),Ee=(y,h)=>{h!="clickaway"&&Q(!1)},Ye=()=>{W(!0)},Ze=()=>{W(!1)},st=()=>{V(!0)},Be=()=>{V(!1)},me=()=>{d(),S("INITIAL")},he=(y,h)=>{i(h)};m.useEffect(()=>{if(r.applicationName!==""){f==null||f.userList.map(h=>(ae.set(h.emailId,h.userName),{code:h.userName,description:h.emailId})),Te(new Map(ae));let y=f==null?void 0:f.groupList.map(h=>(ne.set(h.id,h.name),{code:h.name,description:h.id}));Ne(new Map(ne)),se(y)}},[r]);const xe=()=>{Q(!0)},we=y=>{y==="Timeout"&&window.location.reload(),c(!1),_("")},Qe=y=>{let h;y.includes(",")?h=y.split(","):h=y.split(";");let _e=h.map(Ie=>({emailId:Ie,username:ae.get(Ie)===void 0?Ie:ae.get(Ie)}));K(_e)},Oe=y=>{Ti("fetchAssociatedTemplatesHana",[y,y],function(h){(h.statusCode===401||h.statusCode==="401")&&(c(!0),H("Timeout"),_("Session Timed Out.Kindly Refresh")),h&&ye(h),console.log(h),ie(!1)},function(h){f==null||f.setAlert(!0),f==null||f.setAlertSeverity("error"),f==null||f.setAlertMessage(h),ie(!1)})},je=()=>{ie(!0);let y=$.email.split(";"),h=$.groupName;if(fe(y)&&Ue(h)){let _e="/WorkUtilsServices/v1/mail-group",Ie="POST",Je={id:$.id,createdBy:$.createdBy,createdOn:$.createdOn,email:$.email,groupName:$.groupName,updatedBy:r.userData.user_id,updatedOn:new Date().toISOString()};rt(_e,Ie,Je,function(Ve){(Ve.statusCode===401||Ve.statusCode==="401")&&(c(!0),H("Timeout"),_("Session Timed Out.Kindly Refresh")),X("Saved successfully"),F("success"),xe(),ie(!1),W(!1)},function(Ve){X("Error"),F("error"),xe(),ie(!1)})}else X("Enter valid Data!"),F("error"),xe(),ie(!1)},fe=y=>{const h=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;return y.every(_e=>_e.match(h))},Ue=y=>/^[A-Za-z][A-Za-z0-9_]*$/.test(y),Pe=(y,h)=>{ge(h==="groupName"?{...$,groupName:y.target.value}:{...$,email:y.target.value})};return n("div",{p:2,className:"cwntSetHeight100 cwntSetWidth100",children:[n(b,{direction:"row",spacing:2,alignItems:"center",justifyContent:"space-between",children:[n(b,{direction:"row",alignItems:"center",children:[t(Ge,{"aria-label":"delete",onClick:me,children:t(ea,{})}),t(T,{sx:{fontWeight:600,color:"black !important",fontSize:"20px"},children:"Groups"})]}),t(b,{direction:"row",spacing:2,alignItems:"center",children:t(de,{variant:"outlined",onClick:Ye,children:"Create Group"})})]}),n(b,{height:"100%",width:"100%",direction:"row",justifyContent:"space-around",sx:{overflow:"hidden",background:"#fff"},children:[n(Re,{elevation:1,sx:{width:"35%",background:"#fff",borderRadius:"12px",margin:"1rem 0.5rem 1rem 1rem",height:"calc(100%-2rem)"},children:[t(b,{direction:"row",pl:1,pr:1,justifyContent:"space-between",alignItems:"center",width:"100%",height:"2.5rem",sx:{background:"#F1F5FE",borderRadius:"12px"},children:t("span",{style:{fontWeight:500,color:"black ",fontSize:"14px",whiteSpace:"nowrap",fontFamily:'"Roboto", sans-serif !important'},children:"Display Name"})}),t(b,{pt:1,sx:{height:"93%",width:"100%",borderRadius:"5px",overflowY:"scroll",backgroundColor:"white",position:"relative"},alignItems:"center",children:A==null?void 0:A.map((y,h)=>t(k,{sx:{paddingTop:1,paddingBottom:2,width:"90%"},children:t(Ct,{sx:{minWidth:275,background:Ce===h?" linear-gradient(180.76deg, #C1DCFF -113.11%, rgba(255, 255, 255, 0) 198.03%)":""},onClick:()=>{Qe(y.userIdList),Oe(y.id),oe(h)},children:t(Ft,{children:t(Wi,{avatar:t(La,{src:"",name:y.name,sx:{fontSize:"0.8rem",color:"black"}}),title:y.name})})},h)}))})]}),n(Re,{elevation:1,sx:{width:"64%",background:"#fff",borderRadius:"12px",margin:"1rem 1rem 1rem 0.5rem",height:"calc(100%-2rem)"},children:[n(b,{direction:"row",justifyContent:"space-between",children:[t(T,{children:"Group Details"}),r.feature.EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER==="True"&&n(de,{variant:"outlined",onClick:st,children:[" ","Add User"]})]}),t(k,{sx:{width:"100%",typography:"body1"},children:n(ta,{value:R,children:[t(k,{sx:{borderBottom:1,borderColor:"divider"},children:n(ia,{onChange:he,"aria-label":"lab API tabs example",children:[r.feature.EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO==="True"&&t(dt,{label:"Member Information",value:"1"}),r.feature.EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO==="True"&&t(dt,{label:"Associated Template",value:"2"})]})}),n(ei,{value:"1",children:[" ",t(TableContainer,{component:Re,children:n(Table,{sx:{minWidth:650},"aria-label":"simple table",children:[t(Ui,{children:n(pt,{children:[t(TableCell,{children:"User Name"}),t(TableCell,{children:"Email ID"})]})}),t(TableBody,{children:E==null?void 0:E.map(y=>n(pt,{sx:{"&:last-child td, &:last-child th":{border:0}},children:[t(TableCell,{component:"th",scope:"row",children:y.username}),t(TableCell,{children:y.emailId})]}))})]})})]}),t(ei,{value:"2",overflowY:"scroll",height:"100%",children:t(O,{container:!0,direction:"row",spacing:2,children:G==null?void 0:G.map((y,h)=>t(O,{item:!0,xs:6,sm:6,md:6,xl:3,children:t(Ct,{sx:{backgroundColor:"#FFFFFF",minHeight:"7rem",maxWidth:"20rem"},children:t(Ft,{sx:{height:"100%"},children:t(li,{children:n(b,{direction:"column",spacing:1,children:[n(b,{direction:"row",width:"100%",justifyContent:"space-between",children:[t(T,{sx:{fontFamily:"Roboto, sans-serif !important",color:"#000000 !important",fontWeight:"500 !important",fontSize:"14px !important",height:"24px !important"},children:y.templateName}),n(b,{direction:"row",children:[t(Ge,{size:"small",children:t(hi,{variant:"outlined",sx:{color:y.status==="Active"?"#4CAF50":y.status==="Draft"?"#FFA500":"#647C90",fontSize:"0.8rem"}})}),t(T,{sx:{width:"34px !important",color:"#000000 !important ",fontWeight:"400 !important",fontSize:"12px !important",color:y.status==="Active"?"#4CAF50":y.status==="Draft"?"#FFA500":"#647C90",display:"flex !important",height:"24px !important",alignItems:"center!important"},children:y.status})]})]}),n(b,{direction:"row",spacing:2,children:[t(tt,{label:y.entitydesc,sx:{width:"120px !important",color:"#072E3A !important ",fontWeight:"400 !important",fontSize:"14px !important",lineHeight:"16.41px !important",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",padding:"8px !important",background:"#E4F1FF !important",height:"24px !important",borderRadius:"9px !important"}}),t(tt,{label:y.processName,variant:"filled",sx:{width:"120px !important",color:"#600E59 !important",fontWeight:"400 !important",fontSize:"14px !important",lineHeight:"16px !important",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",padding:"8px !important",background:"#FBEEFA !important",height:"24px !important",borderRadius:"9px !important"}})]})]})})})})}))})})]})})]})]}),n(St,{open:s,onClose:Ze,children:[t(gt,{children:"Create Group"}),t(xt,{children:n(b,{children:[n(b,{justifyContent:"space-between",alignItems:"left",children:[t("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Group Name:"}),t(Ae,{autoFocus:!0,margin:"dense",value:$.groupName,id:"groupName",sx:{width:"25rem","& .MuiInputBase-root":{height:40}},fullWidth:!0,variant:"outlined",onChange:y=>Pe(y,"groupName")})]}),n(b,{justifyContent:"space-between",alignItems:"left",children:[t("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Email ID:"}),t(Ae,{autoFocus:!0,margin:"dense",id:"emailId",value:$==null?void 0:$.email,fullWidth:!0,variant:"outlined",sx:{width:"25rem","& .MuiInputBase-root":{height:40}},onChange:y=>Pe(y,"email")})]})]})}),n(wt,{children:[t(de,{onClick:Ze,children:"Cancel"}),n(de,{variant:"contained",onClick:()=>je(),children:["Save & Continue"," "]})]})]}),n(St,{open:j,onClose:Be,children:[t(gt,{children:"Add User"}),t(xt,{children:n(b,{children:[t(T,{sx:{height:"29px"},children:"PO Group"}),n(b,{justifyContent:"space-between",alignItems:"left",children:[t("span",{style:{fontWeight:500,color:"black ",fontSize:"1rem"},children:"Select User"}),t(Ae,{autoFocus:!0,margin:"dense",id:"emailId",fullWidth:!0,variant:"outlined",sx:{width:"25rem","& .MuiInputBase-root":{height:40}}})]})]})}),n(wt,{children:[t(de,{onClick:Be,variant:"outlined",children:"Cancel"}),t(de,{variant:"contained",children:"Save "})]})]}),t(_t,{message:v,creationType:te,open:M,onClose:y=>we(y)}),t(ni,{open:le,autoHideDuration:6e3,onClose:Ee,anchorOrigin:{vertical:"bottom",horizontal:"center"},children:t(Vi,{onClose:Ee,severity:Z,sx:{width:"100%"},children:u})})]})},Wa=Gi("div")(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%"}));function Ua(){return t(Wa,{children:t(k,{sx:{mt:1},children:"No Data Available"})})}function Va({onCellEditCommit:e,field_name:d,url_onRowClick:S,stopPropagation_Column:f,redirecOnDoubleClick:r=null,status_onRowDoubleClick:R=!1,status_onRowSingleClick:i=!1,title:s,getRowIdValue:W,getRowHeight:j,rows:V,columns:M,hideFooter:c,checkboxSelection:v,disableSelectionOnClick:_,onRowsSelectionHandler:te=()=>{},showConfig:H,setShowWork:Z,fieldName_onCellClick:F,onCellKeyDown:u=()=>{},onEditCellPropsChange:X=()=>{},experimentalFeatures:le,isRowSelectable:Q,module:ce,isLoading:ie,rowsPerPageOptions:Ce,noOfColumns:oe,callback_onRowDoubleClick:ae=null,callback_onRowSingleClick:Te=null,sortModel:ne,onSortModelChange:Ne,selectedRows:E,columnVisibility:K=!0}){zt();const[U,se]=m.useState(oe??10);let A=jt();return n("div",{className:"reusable-table",children:[t(T,{variant:"h6",sx:{marginBottom:".5rem"},children:s}),t(ji,{onCellKeyDown:u,disableColumnSelector:K,autoHeight:!0,loading:ie,getRowId:w=>W?w[W]:"id",rows:V,columns:M,pageSize:U,onPageSizeChange:w=>se(w),rowsPerPageOptions:Ce??[10,25,50],disableExtendRowFullWidth:!1,hideFooter:c,sortModel:ne,onSortModelChange:Ne,checkboxSelection:v,disableSelectionOnClick:_,experimentalFeatures:le,getRowClassName:w=>`custom-row--${d}-${w.row[d]}`,getRowHeight:j,onRowDoubleClick:!ae&&R?(w,G)=>{G.stopPropagation(),r&&r(),w.row.id?A(S+`${w.row.id}`):A(S+`${w.id}`),Z&&Z(!0)}:ae?w=>ae(w):null,onRowClick:i?(w,G)=>{Te(w)}:null,onCellClick:(w,G)=>{typeof f!="object"?f===w.field&&G.stopPropagation():f.includes(w.field)&&G.stopPropagation()},onCellEditCommit:e,onEditCellPropsChange:(w,G)=>X(w,G),onSelectionModelChange:w=>te(w),sx:{"& .MuiDataGrid-row:hover":{backgroundColor:"#EAE9FF40",cursor:i?"pointer":"default"},backgroundColor:"#fff"},components:{LoadingOverlay:zi,NoRowsOverlay:Ua},isRowSelectable:Q,selectionModel:E})]})}const Ga=({onClose:e,userList:d,groupList:S,setScenario:f,...r})=>{const R=lt(l=>l.userReducer),[i,s]=m.useState(new Map),[W,j]=m.useState(new Map),[V,M]=I.useState(!1);m.useState({application:null,process:null,templateName:null,participant:null,ccParticipant:null,groupName:null,emailId:null}),m.useState(!1);const[c,v]=m.useState([]),[_,te]=m.useState([]),[H,Z]=m.useState([]),[F,u]=m.useState([]),[X,le]=m.useState([]),[Q,ce]=m.useState([]),[ie,Ce]=m.useState([]),oe=["GROUP","USER"],[ae,Te]=m.useState(!1),[ne,Ne]=m.useState("success"),[E,K]=m.useState([]),[U,se]=I.useState(!1),[A,w]=I.useState(""),[G,ye]=I.useState("Cancel"),[$,ge]=m.useState(""),[Ee,Ye]=m.useState([]),Ze={ccParticipant:"",ccParticipantType:"",createdBy:R.userData.user_id,createdOn:new Date().toISOString(),updatedBy:R.userData.user_id,updatedOn:new Date().toISOString(),fromDestination:"",id:$i(),application:"",name:"",process:"",regionId:"",status:"",templateId:null,toParticipant:"",toParticipantType:"",isRowEditable:!0,creationType:"new",applicationName:"",processName:"",entity:"",identifier:""};m.useState(Ze);const[st,Be]=m.useState("");m.useEffect(()=>{R.applicationName!==""&&(me(le),me(ce))},[R]);const me=l=>{let C=d.map((x,D)=>(i.set(x.emailId,x.userName),{id:x.emailId,name:x.userName}));s(new Map(i)),l(C)},he=(l,C,x,D)=>{M(!0),Ti("fetchTemplatesOnIdentifiersHana",[x,D,C],function(N){(N.statusCode===401||N.statusCode==="401")&&(se(!0),ye("Timeout"),w("Session Timed Out.Kindly Refresh")),N&&u(N),M(!1)},function(N){Te(!0),Be("error"),Ne("error"),M(!1)})},xe=(l,C,x,D)=>{let N="";for(let Y=0;Y<E.length;Y++)if(E[Y].id===C){N=Y;break}E[N].isEdited=!0,D==="application"&&(E[N].applicationName=x.applicationName,E[N].application=x.application,K([...E]),Ve(x.application)),D==="identifier"?(E[N].identifierDesc=x.identifierDesc,E[N].identifier=x.identifier,K([...E]),Je(R.applicationName,x.identifier)):D==="entity"?(E[N].entitydesc=x.entitydesc,E[N].entity=x.entity,K([...E]),Ve(R.applicationName,x.entity,E[N].identifier)):D==="process"?(E[N].processName=x.processName,E[N].process=x.process,K([...E]),he(R.applicationName,x.process,E[N].identifier,E[N].entity)):D==="name"?(E[N].templateName=x.name,E[N].templateId=x.emailDefinitionId,K([...E])):D==="toParticipantType"?(E[N].toParticipantType=x,K([...E]),x==="VARIABLE"?le([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}]):me(le)):D==="toParticipant"?(E[N].toParticipant=x.id,K([...E])):D==="ccParticipantType"?(E[N].ccParticipantType=x,x===""?(E[N].ccParticipant="",ce([])):x==="VARIABLE"?ce([]):me(Ce),K([...E])):D==="ccParticipant"&&(E[N].ccParticipant=x.id,K([...E]))},we=()=>{let l=structuredClone(E);l.unshift(Ze),K(l)},Qe=l=>{let C="",x=[...E];for(let D=0;D<x.length;D++)if(x[D].id===l){C=D;break}if(E[C].hasOwnProperty("creationType")){const D=[...E];D.splice(C,1),K(D)}},Oe=(l,C)=>{let x="";l.isRowEditable=!0;let D=[...E];for(let N=0;N<(D==null?void 0:D.length);N++)if(E[N].id===C){x=N;break}switch(D[x]=l,K(D),Je(R.applicationName,l.identifier),Ve(R.applicationName,l.entity,l.identifier),he(R.applicationName,l.process,l.identifier,l.entity),l.ccParticipantType){case"USER":me(Ce);break}l.toParticipantType==="GROUP"||(l.toParticipantType==="USER"?me(le):l.toParticipantType==="VARIABLE"&&le([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}])),l.ccParticipantType==="GROUP"||(l.ccParticipantType==="USER"?me(ce):l.ccParticipantType==="VARIABLE"&&ce([{id:"INITIATOR",name:"INITIATOR"},{id:"REVIEWER",name:"REVIEWER"}]))},je=l=>{Ue(C=>({...C,open:!1})),M(!0),rt("/WorkUtilsServices/v1/mail-mapping?Id="+l,"delete",null,function(C){(C.statusCode===401||C.statusCode==="401")&&h.handleOpenPromptBox("ERROR",{title:"Error",message:"Session Timeout",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),h.handleOpenPromptBox("SUCCESS",{title:"Success",message:"successfully updated the record",severity:"success"}),M(!1)},function(C){h.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to update the record",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),M(!1)})},[fe,Ue]=m.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[Pe,y]=m.useState(""),h={handleClosePromptBox:()=>{Ue(l=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),y("")},handleOpenPromptBox:(l,C={})=>{let x={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};l==="SUCCESS"&&(x.type="snackbar"),y(l),Ue({...x,...C})},handleCloseAndRedirect:()=>{h.handleClosePromptBox(),navigate("")},getCancelFunction:()=>{switch(Pe){default:return()=>{h.handleClosePromptBox()}}},getCloseFunction:()=>{switch(Pe){case"COMMENTERROR":default:return l=>{h.handleClosePromptBox()}}},getOkFunction:()=>{switch(Pe){case"DISCARD_CHANGES":return()=>{Ue(l=>({...l,open:!1})),Qe($)};case"DELETE_MAPPING":return()=>{je($)};case"SUBMIT_CHANGES":return()=>{_e(Ee,$)};default:return()=>h.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>fe.redirectOnClose?h.handleCloseAndRedirect:h.handleClosePromptBox},_e=(l,C)=>{M(!0),Ue(N=>({...N,open:!1}));let x="",D=[...E];for(let N=0;N<D.length;N++)if(E[N].id===C){x=N;break}if(E[x].isRowEditable=!1,l.isEdited){E[x].isEdited=!1;let N="/WorkUtilsServices/v1/mail-mapping",Y="POST";l.id&&(Y="PATCH");let ee={ccParticipant:l.ccParticipant,ccParticipantType:l.ccParticipantType,createdBy:l.createdBy,createdOn:new Date(l.createdOn).toISOString(),fromDestination:null,id:l.id,application:R.applicationName,name:l.templateName,process:l.process,regionId:l.regionId,status:l.status,templateId:l.templateId,toParticipant:l.toParticipant,toParticipantType:l.toParticipantType,updatedBy:R.userData.user_id,updatedOn:new Date().toISOString()};rt(N,Y,ee,function(be){(be.statusCode===401||be.statusCode==="401")&&(se(!0),h.handleOpenPromptBox("ERROR",{title:"Error",message:"Session Timeout",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"})),K([...E]),h.handleOpenPromptBox("SUCCESS",{title:"Success",message:"successfully updated the record",severity:"success"}),M(!1)},function(be){h.handleOpenPromptBox("ERROR",{title:"Error",message:"Failed to update the record",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Ok"}),M(!1)})}};m.useEffect(()=>{R.applicationName!==""&&Ie()},[R]);const Ie=()=>{M(!0);const l=D=>{D&&(v(D),M(!1))},C=D=>{Te(!0),Be("error"),Ne("error"),M(!1)};M(!1);const x=`${nt}${ze.EMAIL_CONFIG.POPULATE_APP_IDENTIFIERS_HANA}`;rt(x,"get",l,C)},Je=(l,C)=>{M(!0);const x=Y=>{if((Y==null?void 0:Y.statusCode)===401||(Y==null?void 0:Y.statusCode)==="401")h.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(Y){const ee=Array.isArray(Y)?Y:[];te(ee)}M(!1)},D=Y=>{var ee,be,Le;(ee=r==null?void 0:r.setAlert)==null||ee.call(r,!0),(be=r==null?void 0:r.setAlertSeverity)==null||be.call(r,"error"),(Le=r==null?void 0:r.setAlertMessage)==null||Le.call(r,Y),M(!1)},N=`/${At}${ze.EMAIL_CONFIG.FETCH_NOTIFICATION_MODULES}?identifierId=${encodeURIComponent(C)}`;vt(N,"get",x,D)},Ve=(l,C,x)=>{M(!0);const D=ee=>{if((ee==null?void 0:ee.statusCode)===401||(ee==null?void 0:ee.statusCode)==="401")h.handleOpenPromptBox("TIMEOUT",{title:"Error",message:"Session Timed Out. Kindly Refresh",severity:"danger",cancelButton:!0,okButton:!0,okButtonText:"Refresh"});else if(ee){const be=Array.isArray(ee)?ee:[];Z(be)}M(!1)},N=ee=>{var be,Le,ke;(be=r==null?void 0:r.setAlert)==null||be.call(r,!0),(Le=r==null?void 0:r.setAlertSeverity)==null||Le.call(r,"error"),(ke=r==null?void 0:r.setAlertMessage)==null||ke.call(r,ee),M(!1)},Y=`/${At}${ze.EMAIL_CONFIG.FETCH_NOTIFICATION_EVENTS}?identifierId=${encodeURIComponent(x)}&notificationId=${encodeURIComponent(C)}`;vt(Y,"get",D,N)},$e=l=>{l==="Timeout"&&window.location.reload(),se(!1),w("")},yt=()=>{e(),f("INITIAL")},ut=[{field:"id",headerName:"ID",hide:!0},{field:"masterDataCat",headerName:r!=null&&r.headers[0]?r==null?void 0:r.headers[0]:"Identifier",flex:1,headerAlign:"left",align:"left",renderCell:l=>l.row.isRowEditable?t(et,{value:l.row.identifier,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:c==null?void 0:c.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C.identifier,onClick:x=>xe(x,l.row.id,C,"identifier"),children:C.identifierDesc}))}):l.row.identifierDesc},{field:"entity",headerName:r!=null&&r.headers[1]?r==null?void 0:r.headers[1]:"Module",flex:1,headerAlign:"left",align:"left",renderCell:l=>t(Se,{children:l.row.isRowEditable?t(et,{value:l.row.entity,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:_==null?void 0:_.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C.entity,onClick:x=>xe(x,l.row.id,C,"entity"),children:C.entityDesc}))}):l.row.entitydesc})},{field:"process",headerName:(r==null?void 0:r.headers[2])??"Event",type:"boolean",headerAlign:"left",align:"left",flex:1,renderCell:l=>t(Se,{children:l.row.isRowEditable?t(et,{value:l.row.process,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:H==null?void 0:H.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C.process,onClick:x=>xe(x,l.row.id,C,"process"),children:C.processDesc}))}):l.row.processName})},{field:"templateName",headerName:r!=null&&r.headers[3]?r==null?void 0:r.headers[3]:"Template Name",flex:1,headerAlign:"left",align:"left",renderCell:l=>{var C;return t(Se,{children:l.row.isRowEditable?t(et,{value:(C=l.row)==null?void 0:C.templateName,sx:{width:"100%",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:F==null?void 0:F.map(x=>t(Xe,{sx:{width:"inherit",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:x==null?void 0:x.name,onClick:D=>xe(D,l.row.id,x,"name"),children:x==null?void 0:x.name}))}):l.row.templateName})}},{field:"toParticipantType",headerName:"Recipent Type",type:"boolean",hide:!0,flex:1,headerAlign:"left",align:"left",renderCell:l=>t(Se,{children:l.row.isRowEditable?t(et,{name:"toParticipantType",value:l.row.toParticipantType,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:oe==null?void 0:oe.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C,onClick:x=>xe(x,l.row.id,C,"toParticipantType"),children:C},C))}):l.row.toParticipantType})},{field:"toParticipant",headerName:"Recipent",sortable:!1,filterable:!1,width:"100",headerAlign:"left",align:"left",hide:!0,renderCell:l=>t(Se,{children:l.row.isRowEditable?t(et,{value:l.row.toParticipant,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:X==null?void 0:X.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C.id,onClick:x=>xe(x,l.row.id,C,"toParticipant"),children:C.name}))}):l.row.toParticipantType==="VARIABLE"?l.row.toParticipant:l.row.toParticipantType==="GROUP"?W.get(parseInt(l.row.toParticipant)):i.get(l.row.toParticipant)})},{field:"ccParticipantType",headerName:"CC Type",sortable:!1,filterable:!1,flex:1,hide:!1,headerAlign:"left",align:"left",renderCell:l=>t(Se,{children:l.row.isRowEditable?n(et,{value:l.row.ccParticipantType,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:[t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"grey !important"},value:"",onClick:C=>xe(C,l.row.id,"","ccParticipantType"),children:"Select CC Type"},""),oe==null?void 0:oe.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C,onClick:x=>xe(x,l.row.id,C,"ccParticipantType"),children:C},C))]}):l.row.ccParticipantType})},{field:"ccParticipant",headerName:"CC",sortable:!1,filterable:!1,flex:1,headerAlign:"left",align:"left",hide:!1,renderCell:l=>t(Se,{children:l.row.isRowEditable?t(et,{value:l.row.ccParticipant,sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},children:ie==null?void 0:ie.map(C=>t(Xe,{sx:{width:"15rem",height:"2rem",fontWeight:400,fontSize:"12px",fontFamily:'"Roboto", sans-serif !important',color:"black !important"},value:C.id,onClick:x=>xe(x,l.row.id,C,"ccParticipant"),children:C.name}))}):l.row.ccParticipantType==="VARIABLE"?l.row.ccParticipant:l.row.ccParticipantType==="GROUP"?W.get(parseInt(l.row.ccParticipant)):i.get(l.row.ccParticipant)})},{field:"action",headerName:"Action",sortable:!1,filterable:!1,width:"100",hide:!1,headerAlign:"center",align:"center",renderCell:l=>t(Se,{children:l.row.isRowEditable?n(b,{direction:"row",children:[t(Ge,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{Ye(l.row),ge(l.row.id),h.handleOpenPromptBox("SUBMIT_CHANGES",{title:"Confirm Submit",message:"Do you want to save this record?",severity:"success",cancelButton:!0,okButton:!0,okButtonText:"Submit"})},children:t(aa,{sx:{color:"green"}})}),t(Ge,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{ge(l.row.id),h.handleOpenPromptBox("DISCARD_CHANGES",{title:"Confirm Discard",message:"Are you sure you want to proceed with discard? The entered data will be lost",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Discard"})},children:t(oi,{sx:{color:"red"}})})]}):n(b,{direction:"row",children:[t(Ge,{size:"small","aria-label":"Edit",color:"error",onClick:()=>Oe(l.row,l.row.id),children:t(na,{sx:{color:"blue"}})}),t(Ge,{size:"small","aria-label":"Edit",color:"error",onClick:()=>{ge(l.row.id),h.handleOpenPromptBox("DELETE_MAPPING",{title:"Confirm Delete",message:"Do you want to delete this record?",severity:"warning",cancelButton:!0,okButton:!0,okButtonText:"Ok"})},children:t(ui,{color:"danger"})})]})})}];return n("div",{p:2,className:"cwntSetHeight100 cwntSetWidth100",children:[t(O,{container:!0,sx:{marginBottom:"1.5rem"},children:n(O,{item:!0,md:9,style:{display:"flex"},children:[t(O,{item:!0,sx:{maxWidth:"max-content"},children:t(Ge,{onClick:yt,color:"primary",component:"label",className:"iconButton-spacing-small",sx:{padding:"0.25rem",height:"max-content"},children:t(di,{sx:{fontSize:"25px",color:"#000000"}})})}),n(O,{item:!0,xs:!0,children:[t(T,{variant:"h3",children:t("strong",{children:"Associated Template"})}),t(T,{variant:"body2",color:"#777",children:"This view allows user to manage associated Email Templates"})]})]})}),t(Ot,{className:"backdrop",sx:{zIndex:"9"},open:V,children:t(Nt,{color:"primary"})}),t(kt,{type:fe.type,promptState:fe.open,setPromptState:h.handleClosePromptBox,onCloseAction:h.getCloseFunction(),promptMessage:fe.message,dialogSeverity:fe.severity,dialogTitleText:fe.title,handleCancelButtonAction:h.getCancelFunction(),cancelButtonText:fe.cancelText,showCancelButton:fe.cancelButton,handleSnackBarPromptClose:h.getCloseAndRedirectFunction(),handleOkButtonAction:h.getOkFunction(),okButtonText:fe.okButtonText,showOkButton:fe.okButton}),t(Va,{width:"100%",rows:E??[],columns:ut,hideFooter:!1,getRowIdValue:"id",disableSelectionOnClick:!0,isLoading:!1}),t(_t,{message:A,creationType:G,open:U,onClose:l=>$e(l)}),t(Re,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:"4"},elevation:2,children:t($t,{className:"container_BottomNav",children:t(b,{direction:"row",sx:{marginLeft:"auto"},spacing:2,alignItems:"center",children:t(de,{variant:"contained",sx:{minWidth:"max-content",padding:"6px 12px",texttransform:"capitalize",height:"2rem"},onClick:we,children:"Create New Mapping"})})})})]})};var Kt={},za=Vt;Object.defineProperty(Kt,"__esModule",{value:!0});var yi=Kt.default=void 0,ja=za(Ut()),$a=Gt;yi=Kt.default=(0,ja.default)((0,$a.jsx)("path",{d:"M3 10h11v2H3zm0-2h11V6H3zm0 8h7v-2H3zm15.01-3.13.71-.71c.39-.39 1.02-.39 1.41 0l.71.71c.39.39.39 1.02 0 1.41l-.71.71zm-.71.71-5.3 5.3V21h2.12l5.3-5.3z"}),"EditNote");const Ha=e=>t(Hi,{store:Dt,children:t(Ja,{...e})}),Ya=I.memo(Ha);function Ja({token:e,destinations:d,feature:S,environment:f,useWorkAccess:r,useConfigServerDestination:R,userId:i,applicationName:s,applicationId:W,needHeading:j,showManageGroups:V,useCrud:M,...c}){const v=lt(p=>p.userReducer),_=zt(),te=jt(),[H,Z]=I.useState(""),[F,u]=I.useState(1),[X,le]=I.useState(!1),[Q,ce]=I.useState([]),[ie,Ce]=I.useState([]),[oe,ae]=I.useState([]),[Te,ne]=I.useState([]),[Ne,E]=I.useState([]),[K,U]=I.useState([]),[se,A]=I.useState(!1),[w,G]=I.useState(null),[ye,$]=I.useState("new"),[ge,Ee]=I.useState("INITIAL"),[Ye,Ze]=I.useState([]),[st,Be]=I.useState(!1),[me,he]=I.useState(""),[xe,we]=I.useState("success"),[Qe,Oe]=I.useState(null),[je,fe]=I.useState(""),[Ue,Pe]=I.useState("Cancel"),[y,h]=m.useState(!1),[_e,Ie]=m.useState(!1),[Je,Ve]=m.useState(!1),[$e,yt]=m.useState([]),[ut,l]=m.useState([]),[C,x]=m.useState([]),[D,N]=m.useState([]),[Y,ee]=m.useState([]),[be,Le]=m.useState(!0),{data:ke,error:ct}=Xt();xi();const{t:Ke}=Yi(),Mt=()=>{Ve(!1),Le(!0)},Bt=()=>{Ie(!1),Le(!0)},mt=()=>{h(!0),Le(!1),$("new"),Ee("CREATE")},It=()=>{h(!1),Le(!0)};m.useEffect(()=>{Q.length&&$e.length&&bt()},[Q,$e]);function bt(){var p=Q,ue=$e;const a=[];p.forEach(g=>{const o=ue.find(z=>g.emailDefinitionId===z.templateId),J={...g,toList:(o==null?void 0:o.toList)??"",toParticipant:(o==null?void 0:o.toParticipant)??"",toParticipantType:(o==null?void 0:o.toParticipantType)??"",ccList:(o==null?void 0:o.ccList)??"",ccParticipant:(o==null?void 0:o.ccParticipant)??"",ccParticipantType:(o==null?void 0:o.ccParticipantType)??""};a.push(J)}),N(a),ae(a.filter(g=>g.status==="Active")),E(a.filter(g=>g.status==="Draft"))}const qe=()=>{if(A(!0),!d){A(!1);return}if(ke){const p=(ke==null?void 0:ke.data)||[],ue=p==null?void 0:p.filter(o=>(o==null?void 0:o.identifierDesc)==="MDG_BATCH");ce(ue),Ce(ue);const a=ue.filter(o=>o.status==="Active"),g=ue.filter(o=>o.status==="Draft");ae(a),ne(a),E(g),U(g),A(!1)}ct&&(Be(!0),we("error"),he(ct==null?void 0:ct.message),A(!1))};m.useEffect(()=>{ke&&qe()},[ke]),I.useEffect(()=>{_(Ea({useWorkAccess:r,useConfigServerDestination:R,userId:i,applicationName:s,applicationId:W,useCrud:M})),_(wa(S)),Promise.all([_(Ia(e)),_(ba({destinations:d,environment:f}))]).then(([])=>{qe()}).catch(p=>{})},[v==null?void 0:v.refreshTemplates]);const Fe=(p,ue)=>{u(ue)},ht=p=>{const ue=p.toLowerCase();let a=[];F===1?a=Q.filter(o=>o.status==="Active"):F===2?a=Q.filter(o=>o.status==="Draft"):a=[...Q];const g=a.filter(({name:o,entityDesc:J,processDesc:z,subject:B})=>(o==null?void 0:o.toLowerCase().includes(ue))||(J==null?void 0:J.toLowerCase().includes(ue))||(z==null?void 0:z.toLowerCase().includes(ue))||(B==null?void 0:B.toLowerCase().includes(ue)));F===1?ae(g):F===2?E(g):Ze(g)},ft=()=>{switch(ge){case"EDIT":return t(ii,{headers:c==null?void 0:c.headers,isRecepientData:c==null?void 0:c.isRecepientData,open:y,onClose:It,...c,getMailDefinition:qe,data:w,creationType:ye,setSelectedRow:G,setCreationType:$,setAlert:Be,setAlertMessage:he,setAlertSeverity:we,setIsEditing:Oe,userList:c==null?void 0:c.userList,groupList:c==null?void 0:c.groupList,contentHTML:c==null?void 0:c.contentHTML,setScenario:Ee});case"INITIAL":return n(b,{children:[t(b,{spacing:1,children:n(O,{container:!0,sx:{...Xi},children:[n(O,{item:!0,md:5,xs:12,sx:Zi,children:[t(T,{variant:"h3",children:t("strong",{children:Ke("Email Template Configurations")})}),t(T,{variant:"body2",color:"#777",children:Ke("This view allows the user to create and display Email Templates")})]}),t(O,{item:!0,md:7,xs:12,sx:{display:"flex",justifyContent:"flex-end",alignItems:"center"},children:t(k,{sx:{maxWidth:200,marginLeft:"auto"},className:"searchEmail",children:t(Ae,{fullWidth:!0,size:"small",placeholder:Ke("Search Templates"),variant:"outlined",value:H,onChange:p=>{Z(p.target.value),ht(p.target.value)},InputProps:{startAdornment:t(Qi,{sx:{color:"action.active"}})},sx:{"& .MuiOutlinedInput-root":{borderRadius:"10px",backgroundColor:"white","&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main"},"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main",borderWidth:"2px"}},"& .MuiOutlinedInput-input":{padding:"10px 14px"}}})})})]})}),t(Re,{sx:{position:"fixed",bottom:0,left:0,right:0,zIndex:2},elevation:2,children:t($t,{className:"container_BottomNav",children:S.EMAIL_CONFIG_CREATE==="True"&&t(de,{size:"small",variant:"contained",className:"btn-ml",onClick:mt,children:Ke("Create New Template")})})}),be&&n(Se,{children:[S.EMAIL_CONFIG_SUMMARY==="True"&&t(k,{borderBottom:1,sx:{borderColor:"divider",marginBottom:"1rem"},children:n(ri,{value:F,onChange:Fe,children:[S.EMAIL_CONFIG_SUMMARY_ACTIVE==="True"&&t(dt,{label:n(b,{direction:"row",sx:{alignItems:"center"},children:[t(gi,{sx:{fontSize:"15px"}}),t(T,{variant:"body1",ml:1,sx:{fontWeight:600,fontSize:"14px"},children:Ke("Active Template")})]}),value:1,sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}}),S.EMAIL_CONFIG_SUMMARY_DRAFT==="True"&&t(dt,{label:n(b,{direction:"row",sx:{alignItems:"center"},children:[t(yi,{sx:{fontSize:"15px"}}),t(T,{variant:"body1",ml:1,sx:{fontWeight:600,fontSize:"14px"},children:Ke("Draft")})]}),value:2,sx:{fontWeight:"700",fontSize:"14px",textTransform:"none"}})]})}),t(b,{children:F===1&&t(Ta,{setScenario:Ee,destinations:d,setCreationType:$,applicationName:s,setSelectedRow:G,setShowConfirmation:le,setIsEditing:Oe,isEditing:Qe,setButtonAction:Pe,setConfirmationMessage:fe,active:oe,mailDefination:Q,mailmappingData:$e,searchParam:H,setOpenCreateTemplate:h,ccToParticipant:ut,toparticipant:C,emailTemplateData:D,groupList:c==null?void 0:c.groupList,userList:c==null?void 0:c.userList,isLoading:se,allGroups:Y,headers:c==null?void 0:c.headers})}),t(b,{children:F===2&&t(ya,{destinations:d,setCreationType:$,applicationName:s,setSelectedRow:G,setShowConfirmation:le,draft:Ne,setIsEditing:Oe,isEditing:Qe,setButtonAction:Pe,setConfirmationMessage:fe,mailDefination:Q,searchParam:H,setOpenCreateTemplate:h,mailmappingData:$e,emailTemplateData:D,groupList:c==null?void 0:c.groupList,userList:c==null?void 0:c.userList,isLoading:se,allGroups:Y,headers:c==null?void 0:c.headers,setScenario:Ee})}),t(b,{children:F==="3"&&t(ga,{destinations:d,setCreationType:$,applicationName:s,setSelectedRow:G,setScenario:Ee,setShowConfirmation:le,setIsEditing:Oe,isEditing:Qe,setButtonAction:Pe,setConfirmationMessage:fe,filteredData:Ye,mailDefination:Q,searchParam:H,setOpenCreateTemplate:h,mailmappingData:$e,emailTemplateData:D,groupList:c==null?void 0:c.groupList,userList:c==null?void 0:c.userList,isLoading:se,allGroups:Y,headers:c==null?void 0:c.headers})})]})]});case"CREATE":return t(ii,{scenario:ge,headers:c==null?void 0:c.headers,isRecepientData:c==null?void 0:c.isRecepientData,open:y,onClose:It,...c,getMailDefinition:qe,data:w,creationType:ye,setSelectedRow:G,setCreationType:$,setAlert:Be,setAlertMessage:he,setAlertSeverity:we,setIsEditing:Oe,userList:c==null?void 0:c.userList,groupList:c==null?void 0:c.groupList,contentHTML:c==null?void 0:c.contentHTML,setScenario:Ee});case"MANAGE_GROUPS":return t(Fa,{headers:c==null?void 0:c.headers,open:_e,onClose:Bt,...c,getMailDefinition:qe,data:w,creationType:ye,setSelectedRow:G,setCreationType:$,setAlert:Be,setAlertMessage:he,setAlertSeverity:we,setIsEditing:Oe,setScenario:Ee,isAssociatedTemplate:c==null?void 0:c.isAssociatedTemplate});case"ASSOCIATED_TEMPLATE":return t(Ga,{headers:c==null?void 0:c.headers,open:Je,onClose:Mt,...c,getMailDefinition:qe,data:w,creationType:ye,setSelectedRow:G,setCreationType:$,setAlert:Be,setAlertMessage:he,setAlertSeverity:we,setIsEditing:Oe,userList:c==null?void 0:c.userList,groupList:c==null?void 0:c.groupList,setScenario:Ee,promptAction_Functions:ve})}},[De,q]=m.useState({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""}),[it,ot]=m.useState(""),ve={handleClosePromptBox:()=>{q(p=>({open:!1,type:"",redirectOnClose:!0,message:"",title:"",severity:""})),ot("")},handleOpenPromptBox:(p,ue={})=>{let a={open:!0,title:"",message:"",okButton:!0,cancelButton:!0,okText:"Ok",cancelText:"Cancel",type:"dialog"};p==="SUCCESS"&&(a.type="snackbar"),ot(p),q({...a,...ue})},handleCloseAndRedirect:()=>{ve.handleClosePromptBox(),te("/purchaseManagement/purchaseOrder")},getCancelFunction:()=>{switch(it){default:return()=>{ve.handleClosePromptBox()}}},getCloseFunction:()=>{switch(it){case"COMMENTERROR":default:return p=>{ve.handleClosePromptBox()}}},getOkFunction:()=>{switch(it){case"CONFIRMDELETE_PROCESS":return()=>deleteProcess();case"CONFIRMDELETE_METADATA":return()=>deleteMetaData();default:return()=>ve.handleClosePromptBox()}},getCloseAndRedirectFunction:()=>De.redirectOnClose?ve.handleCloseAndRedirect:ve.handleClosePromptBox};return d===null?t(Ot,{className:"backdrop",open:!0,children:t(Nt,{color:"primary"})}):n(Se,{children:[t(kt,{type:De.type,promptState:De.open,setPromptState:ve.handleClosePromptBox,onCloseAction:ve.getCloseFunction(),promptMessage:De.message,dialogSeverity:De.severity,dialogTitleText:De.title,handleCancelButtonAction:ve.getCancelFunction(),cancelButtonText:De.cancelText,showCancelButton:De.cancelButton,handleSnackBarPromptClose:ve.getCloseAndRedirectFunction(),handleOkButtonAction:ve.getOkFunction(),okButtonText:De.okButtonText,showOkButton:De.okButton}),t(b,{sx:{...Ji,minHeight:"100vh",height:"max-content",backgroundColor:p=>p.palette.background.default},children:ft()})]})}const rn=()=>{let e=lt(j=>j.userManagement.userData);const d=lt(j=>j.applicationConfig),[S,f]=m.useState([]),[r,R]=m.useState([]),i=d.environment==="localhost"?[{Description:"",Name:"CW_Worktext",URL:`${Pt}`},{Description:"",Name:"WorkUtilsServices",URL:`${Pt}`},{Description:"",Name:"WorkUtilsServicesHana",URL:`${Pt}`},{Description:"",Name:"CrudApiServices",URL:`${Ki}`}]:[];let s=d.environment==="localhost"?d.iwaToken:"Bearer ";const W={EMAIL_CONFIG_SUMMARY:"True",EMAIL_CONFIG_SUMMARY_ACTIVE:"True",EMAIL_CONFIG_SUMMARY_DRAFT:"True",EMAIL_CONFIG_SUMMARY_SORT:"True",EMAIL_CONFIG_SUMMARY_SEARCH:"True",EMAIL_CONFIG_CREATE:"True",EMAIL_CONFIG_SAVE:"True",EMAIL_CONFIG_DISCARD:"True",EMAIL_CONFIG_DELETE:"True",EMAIL_CONFIG_SUBMIT:"True",EMAIL_CONFIG_MANAGE_GROUPS:"True",EMAIL_CONFIG_MANAGE_GROUPS_CREATE:"True",EMAIL_CONFIG_MANAGE_GROUPS_ADD_USER:"True",EMAIL_CONFIG_MANAGE_GROUPS_MEMBER_INFO:"True",EMAIL_CONFIG_MANAGE_GROUPS_TEMPLATE_INFO:"false",EMAIL_CONFIG_GROUP_MAPPING:"True",EMAIL_CONFIG_GROUP_MAPPING_ADD:"True",EMAIL_CONFIG_GROUP_MAPPING_DELETE:"True",EMAIL_CONFIG_GROUP_MAPPING_FILTER:"True"};return t("div",{children:t(Ya,{applicationName:"ITM",token:s,destinations:i,headers:[],useCrud:!0,environment:"itm",useWorkAccess:!1,useConfigServerDestination:!1,userId:e==null?void 0:e.emailId,applicationId:"1",groupList:r,userList:S,contentHTML:"",needHeading:!1,isAssociatedTemplate:!0,isRecepientData:!1,showManageGroups:!0,pathName:"/configCockpit/userManagement?component=groups",feature:W})})};export{rn as default};
