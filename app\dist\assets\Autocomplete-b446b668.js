import{qm as n,q6 as a,qn as l,cb as t,qo as m,qp as u}from"./index-226a1e75.js";import{i as p}from"./Dropdown-6cea9e1f.js";const d=(e,i)=>({small:{"& .MuiInputBase-root":{minHeight:"1.5rem"},"& .MuiAutocomplete-input":{minHeight:"0.5rem",alignContent:"center"},...i&&{"& .MuiInputBase-input":{minHeight:"1.25rem"}}},medium:{"& .MuiInputBase-root":{minHeight:"2rem"},"& .MuiAutocomplete-input":{minHeight:"1rem",alignContent:"center"}},large:{"& .MuiInputBase-root":{minHeight:"2.25rem"},"& .MuiAutocomplete-input":{minHeight:"1.5rem",alignContent:"center"}}})[e],s=n({components:{MuiAutocomplete:{styleOverrides:{option:{overflow:"hidden",color:"var(--text-primary)",textOverflow:"ellipsis",fontSize:"0.875rem",fontWeight:400,lineHeight:"normal",fontFamily:"inherit",backgroundColor:"var(--background-default)","&:hover":{backgroundColor:"var(--background-read-only)",fontWeight:500},'&[aria-selected="true"]':{backgroundColor:"var(--primary-light) !important"},"&. Mui-selected":{backgroundColor:"var(--primary-light)"}},groupLabel:{color:"var(--text-primary)",textOverflow:"ellipsis",fontSize:"0.875rem",fontWeight:500,lineHeight:"normal",fontFamily:"inherit"},paper:{fontFamily:"inherit"},listbox:{padding:0}}}}}),c=a(l,{shouldForwardProp:e=>e!=="customSize"})(({customSize:e,multiple:i,readOnly:o,disabled:r})=>({"& .MuiOutlinedInput-root":{paddingRight:"1rem !important",...o===!0&&{backgroundColor:"var(--background-read-only)"},...r===!0&&{backgroundColor:"var(--background-disabled)",color:"var(--text-disabled)"}},"& .MuiAutocomplete-input":{paddingRight:"1.5rem !important"},"& .MuiAutocomplete-clearIcon":{padding:"0.5rem"},"& .MuiChip-root":{"& .MuiChip-label":{padding:"0rem "}},...d(e,i)}));function b({size:e="medium",...i}){return t.jsx(m,{theme:s,children:t.jsx(c,{...i,size:e==="large"?"medium":e,customSize:e,popupIcon:t.jsx(p,{size:"xsmall"}),clearIcon:t.jsx(u,{size:"xsmall"})})})}export{b as v};
