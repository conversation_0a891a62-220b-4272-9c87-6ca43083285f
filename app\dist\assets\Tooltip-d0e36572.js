import{q8 as r,cb as o,q9 as i}from"./index-226a1e75.js";const a=r(({className:e,...t})=>o.jsx(i,{...t,PopperProps:{className:e,modifiers:[{name:"offset",options:{offset:[0,-10]}}]}}))(({theme:e})=>({"& .MuiTooltip-tooltip":{display:"inline-flex",padding:"0.5rem",alignItems:"center",flexShrink:0,borderRadius:"0.25rem",background:"var(--text-primary)",maxWidth:"none",whiteSpace:"nowrap",[e.breakpoints.up("sm")]:{padding:"0.5rem"},[e.breakpoints.down("sm")]:{padding:"0.5rem"}},"& .MuiTooltip-arrow":{color:"var(--text-primary)"},"& .MuiTooltip-title":{color:"var(--background-default)",textAlign:"center",fontSize:"0.75rem",fontStyle:"normal",fontWeight:400,lineHeight:"1.25",whiteSpace:"normal",wordBreak:"break-word"}}));function s({title:e,...t}){return o.jsx(a,{title:o.jsx("span",{className:"MuiTooltip-title",children:e}),...t})}export{s as p};
